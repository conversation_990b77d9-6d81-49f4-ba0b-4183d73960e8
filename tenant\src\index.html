<!doctype html>
<html>

<head>
  <meta charset="utf-8">
  <title>测评调研云</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta http-equiv="Expires" content="0"> 
  <meta http-equiv="Pragma" content="no-cache"> 
  <meta http-equiv="Cache-control" content="no-cache"> 
  <meta http-equiv="Cache" content="no-cache">
  <link rel="icon" type="image/x-icon" href="./favicon.ico">
  <style type="text/css">
  /* ! scopecss-disable */
  micro-app[name=sag-main] img {
    display: unset;
  }

  micro-app[name=sag-main] micro-app-body {
    overflow-y: auto;
  }

  micro-app[name=sag-main] layout-header {
    display: none;
  }
  </style>
  <style type="text/css">
    button:focus{outline:0;}
    a{ outline:none; }
    .preloader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      background: #49a9ee;
      z-index: 9999;
      transition: opacity .65s
    }

    .preloader-hidden-add {
      opacity: 1;
      display: block
    }

    .preloader-hidden-add-active {
      opacity: 0
    }

    .preloader-hidden {
      display: none
    }

    .cs-loader {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%
    }

    .cs-loader-inner {
      transform: translateY(-50%);
      top: 50%;
      position: absolute;
      width: 100%;
      color: #fff;
      text-align: center
    }

    .cs-loader-inner label {
      font-size: 20px;
      opacity: 0;
      display: inline-block
    }

    @keyframes lol {
      0% {
        opacity: 0;
        transform: translateX(-300px)
      }

      33% {
        opacity: 1;
        transform: translateX(0)
      }

      66% {
        opacity: 1;
        transform: translateX(0)
      }

      100% {
        opacity: 0;
        transform: translateX(300px)
      }
    }

    .cs-loader-inner label:nth-child(6) {
      animation: lol 3s infinite ease-in-out
    }

    .cs-loader-inner label:nth-child(5) {
      animation: lol 3s .1s infinite ease-in-out
    }

    .cs-loader-inner label:nth-child(4) {
      animation: lol 3s .2s infinite ease-in-out
    }

    .cs-loader-inner label:nth-child(3) {
      animation: lol 3s .3s infinite ease-in-out
    }

    .cs-loader-inner label:nth-child(2) {
      animation: lol 3s .4s infinite ease-in-out
    }

    .cs-loader-inner label:nth-child(1) {
      animation: lol 3s .5s infinite ease-in-out
    }
  </style>

</head>

<body>
  <app-root></app-root>
  <div class="preloader">
    <div class="cs-loader">
      <div class="cs-loader-inner">
        <label> ●</label>
        <label> ●</label>
        <label> ●</label>
        <label> ●</label>
        <label> ●</label>
        <label> ●</label>
      </div>
    </div>
  </div>
</body>
<!-- <script src="assets/libs/jquery-1.11.0.min.js"></script>
<script src="assets/libs/super_slider.js"></script> -->
</html>
