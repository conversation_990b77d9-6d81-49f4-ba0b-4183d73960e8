import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import _ from 'lodash';



@Component({
    selector: 'app-break-crumb',
    templateUrl: './breakcrumb.component.html',
    styleUrls: ['./breakcrumb.component.less']
})

export class BreakCrumbComponent implements OnInit {
    @Input() Breadcrumbs: []
    @Input() queryParams:any ={};
    constructor(private router: Router) { }
    ngOnInit(): void { 
    }

    goingTo(path,name) {
        if(name == '新建活动') path = '/new-activity'
        const url = path.split('?')[0]
        let urlindex = path.indexOf('=')
        let urlId = null
        if(urlindex > 0){
            urlId = path.slice(urlindex + 1, path.length)
            this.queryParams = {
                id: urlId
            }
        }

        let newsqueryParams = JSON.parse(JSON.stringify(this.queryParams))
        if (newsqueryParams && newsqueryParams.type && !newsqueryParams.projectType) newsqueryParams.projectType = newsqueryParams.type
        this.router.navigate(
            [url],
            { queryParams: newsqueryParams ? newsqueryParams : {}}
        );
    }
}
