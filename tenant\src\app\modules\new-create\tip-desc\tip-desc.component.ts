import { Component, OnInit, Inject, Input, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { DomSanitizer } from "@angular/platform-browser";
import { NewCreateService } from "../new-create.service";
import _ from "lodash";
import { NzDrawerRef } from "ng-zorro-antd";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
@Component({
  selector: "app-tip-desc",
  templateUrl: "./tip-desc.component.html",
  styleUrls: ["./tip-desc.component.less"],
})
export class TipDescComponent implements OnInit, OnDestroy {
  @Input() description: any;
  @Input() reportType?: string;
  @Input() tinyconfig: any;
  @Input() isAtADAPTIVE?: boolean = false;
  @Input() standardQuestionnaireId: string;
  @Input() projectId: string;
  @Input() dimensionCodes?: string[];
  @Input() questionnaireId: string;

  zh_CN = "";
  en_US = "";
  atAdaptiveDesc = null;
  tenantApi: string = "/tenant-api";
  Isactivelan = true;

  desc = {};
  lan = "zh_CN";
  i18n = [
    { name: "中文", value: "zh_CN" },
    { name: "美国英语", value: "en_US" },
  ];
  private routerSubscription: Subscription;

  constructor(
    private drawerRef: NzDrawerRef,
    private HttpClient: HttpClient,
    private sanitizer: DomSanitizer,
    private http: NewCreateService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private router: Router
  ) {}

  ngOnInit() {
    this.zh_CN = this.description.zh_CN;
    // innerHTML渲染问题 需用this.sanitizer.bypassSecurityTrustHtml
    this.atAdaptiveDesc = this.sanitizer.bypassSecurityTrustHtml(
      this.description.zh_CN
    );
    this.en_US = this.description.en_US;
    this.desc = _.cloneDeep(this.description);
    const _this = this;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.tinyconfig = {
      min_height: 300,
      fontsize_formats:
        "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 32pt 36pt",
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      images_upload_url: `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`, // 配置你图片上传的url
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.HttpClient.post(
          `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`,
          formData,
          { headers: headers }
        ).subscribe(
          (response: any) => {
            if (response) {
              this.HttpClient.get(
                `${this.tenantApi}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                { headers: headers }
              ).subscribe((imgurl: any) => {
                let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                success(url);
              });
            } else {
              if (response && response.rtnMsg) {
                failure(response.rtnMsg);
              } else {
                failure("上传失败：未知错误");
              }
            }
          },
          (error1) => {
            failure("上传失败：未知错误");
          }
        );
      },
    };
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  okModalDesc() {
    let that = this;
    that.drawerRef.close({
      desc: that.desc,
      reportType: this.reportType,
    });
  }
  setDescDefault() {
    // this.http.getDimensions(this.standardQuestionnaireId).subscribe((res) => {
    //   this.zh_CN = res.data.answerDescription.zh_CN;
    //   this.en_US = res.data.answerDescription.en_US;
    //   this.desc = _.cloneDeep({
    //     zh_CN: this.zh_CN,
    //     en_US: this.en_US,
    //   });
    // });
    const params = {
      projectId: this.projectId,
      standardQuestionnaireId: this.standardQuestionnaireId,
      questionnaireId: this.questionnaireId,
      reportType: this.reportType,
      recoverFlag: true,
    };
    if (this.dimensionCodes && this.dimensionCodes.length > 0) {
      params["dimensionCodes"] = this.dimensionCodes;
    }
    this.http.getQuestionnaireAnswerDescription(params).subscribe((res) => {
      if (res.result.code === 0) {
        let tmpDesc = res.data;
        this.zh_CN = tmpDesc.zh_CN;
        this.en_US = tmpDesc.en_US;
        this.desc = _.cloneDeep({
          zh_CN: this.zh_CN,
          en_US: this.en_US,
        });
      }
    });
  }

  onSelectI18n(e) {
    this.lan = e;
  }
}
