.iconfont {
  display: flex;
  align-items: center;
  width: 16px;
  height: 16px;
  margin-left: 5px;
}

.hover-icon {
  color: #595959;
}

.hover-icon:hover {
  color: #409eff;
}

.index_xy {
  display: flex;
  justify-content: center;
  padding: 30px 0;
  background-color: #f5f6fa;
  height: 100%;
}

.big_contant {
  .create_p {
    display: flex;
    justify-content: space-between;

    .span_left {
      font-size: 24px;
    }

    .span_right {
      span {
        cursor: pointer;
      }

      > .span_blue {
        color: #53a8fe;
      }
    }
  }

  .create_name {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    font-size: 14px;
    font-weight: 400;
    color: #17314c;

    > li {
      > p {
        margin: 0px 0 30px 0;
        font-weight: bold;
      }

      .pri_name {
        width: 100%;
        height: 50px;
        border-radius: 5px;
        font-size: 16px;
      }
    }
  }

  .setmodal {
    margin-top: 30px;

    .setmodal_top {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;

      > .cur_span {
        cursor: pointer;
        color: #409eff;
      }
    }

    .setmodal_card {
      margin-top: 30px;
      width: 100%;
      min-height: 646px;
      background-color: #fff;
      border-radius: 8px;
      display: flex;

      .card_left {
        width: 146px;
        padding: 20px 0;
        min-height: 646px;
        border-right: 1px solid #e6e6e6;

        .left_div {
          width: 146px;
          height: 48px;
          line-height: 48px;
          text-align: center;
          font-size: 14px;
          font-weight: bold;
          cursor: pointer;

          > span {
            width: 100px;
            display: inline-block;
            overflow: hidden;
            /*超出部分隐藏*/
            white-space: nowrap;
            /*不换行*/
            text-overflow: ellipsis;
            /*超出部分文字以...显示*/
          }
        }

        .showClass {
          background-color: #f5faff;
          color: #409eff;
          border-left: 2px solid #409eff;
          width: 99.99%;
        }
      }

      .card_right {
        flex: 1;
        height: 100%;

        padding: 15px 30px;
      }
    }
  }
}

.submit_xy {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 70px;
  background: #fff;
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: center;

  .center_menu {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .menus_xy {
      display: flex;
      align-items: center;

      .menus_left {
        width: 98px;
        height: 38px;
        border-radius: 19px;
        border: 1px solid #409eff;
        text-align: center;
        line-height: 38px;
        cursor: pointer;
        color: #409eff;

        &:hover {
          box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.15);
        }
      }

      .menus_right {
        width: 98px;
        height: 38px;
        background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        text-align: center;
        line-height: 38px;
        cursor: pointer;
        color: #fff;
        margin-left: 20px;
        border: 0;

        &:hover {
          box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.8);
        }
      }

      .menus_right_new {
        width: 98px;
        height: 38px;
        background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
        box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
        border-radius: 19px;
        text-align: center;
        line-height: 38px;
        cursor: pointer;
        color: #fff;
        margin-left: 20px;
        border: 0;

        &:hover {
          box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.8);
        }
      }
    }
  }
}

.time_picker {
  height: 50px;
  width: 500px;
  border-radius: 5px;

  ::ng-deep input {
    font-size: 16px;
  }
}

:host ::ng-deep {
  .ant-calendar-range-picker-separator {
    line-height: 40px;
  }

  .ant-calendar-picker-input.ant-input {
    height: 50px;
    width: 500px;
    border-radius: 5px;
  }

  .ant-calendar-picker-icon {
    color: #409eff;
    font-size: 20px;
  }

  .ant-calendar-picker-clear,
  .ant-calendar-picker-icon {
    right: 14px;
    margin-top: -10px;
  }
}

.mock_div {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999999999;

  .bg_ul {
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.6;
  }

  .img_ul {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;

    > li {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .btn_div {
      width: 160px;
      line-height: 38px;
      text-align: center;
      color: #fff;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      cursor: pointer;
    }
  }
}

.label_title {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    color: #17314c;
    font-size: 14px;
    font-weight: bold;
  }

  .div_right {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .custom_add_xy {
    color: #495970;
    font-size: 12px;
    display: flex;
    align-items: center;

    &:hover {
      color: #409eff;
    }

    i {
      margin-right: 4px;
      margin-left: 0;
    }

    > span {
      cursor: pointer;
    }
  }

  .border_left_d {
    border-left: 1px solid #e1e1e1;
    margin: 0 10px;
    height: 14px;
  }
}

.top_div {
  margin-top: 8px;
  border-radius: 2px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  width: 100%;
  min-height: 48px;

  .div_left {
    display: flex;
    align-items: center;

    .li_title {
      font-size: 14px;
      color: #17314c;
      font-weight: bold;
      width: 90px;
    }

    .li_list {
      display: flex;
      align-items: center;
      margin-top: -2px;
      padding-bottom: 14px;

      .li_span {
        display: flex;
        align-items: center;
        margin-top: 16px;
        margin-left: 20px;
        margin-right: 12px;

        ::ng-deep .ant-checkbox {
          top: 1px;
        }

        // max-width: 140px;
        // span {
        //   display: inline-block;
        //   max-width: 70px;
        //   overflow: hidden;
        //   /*超出部分隐藏*/
        //   white-space: nowrap;
        //   /*不换行*/
        //   text-overflow: ellipsis;
        //   /*超出部分文字以...显示*/
        // }
      }
    }
  }
}

.nz_modal {
  position: relative;
}

.right_top {
  width: 90%;
}

.big_div {
  display: flex;
  margin-top: 30px;
  position: relative;

  .all_div {
    .top_tab {
      display: flex;
      border: 1px solid #e6e6e6;
      border-radius: 8px 8px 0 0;

      .top_ul {
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
        flex: 1;

        li {
          padding: 10px 0;
          flex: 1;
          cursor: pointer;
          text-align: center;
        }

        .bot_bod {
          border-bottom: 2px solid #409eff;
        }
      }

      .top_ul_subsrcript {
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
        flex: 1;

        li {
          padding: 10px 0;
          flex: 1;
          cursor: pointer;
        }
      }

      .clear_top {
        flex: 1;
        padding: 10px 20px;
        text-align: right;
      }
    }

    .no_top {
      border-top: none;
    }

    .radius_bod {
      border-radius: 8px !important;
    }

    .list_ul {
      border: 1px solid #e6e6e6;
      border-radius: 0 0 8px 8px;
      display: flex;

      li {
        > div {
          padding: 10px 0;
        }
      }

      .list_left {
        flex: 1;

        .left_div {
          display: flex;

          > span {
            min-width: 100px;
            max-width: 150px;
            text-align: center;
            color: #17314c;
            font-size: 14px;
            font-weight: bold;
          }

          .name_span {
            width: 100px;
            padding-left: 10px;
            display: inline-block;
            overflow: hidden;
            /*超出部分隐藏*/
            white-space: nowrap;
            /*不换行*/
            text-overflow: ellipsis;
            /*超出部分文字以...显示*/
          }

          .son_div {
            display: flex;
            flex: 1;
            padding-left: 10px;
            flex-wrap: wrap;

            .name_cursor {
              cursor: pointer;
            }

            // > div {
            //   margin: 0 10px;
            // }
          }
        }
      }
    }
  }

  .small_tips {
    margin-left: 30px;
    width: 190px;
    margin-top: -10px;

    .all_tips {
      width: 190px;
      min-height: 173px;
      padding-bottom: 20px;
      background: #f9f9f9;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .name_mod {
        width: 100%;
        padding: 15px 20px 5px 20px;
        font-size: 14px;
        color: #17314c;
        display: flex;
        justify-content: space-between;

        .diss_span {
          font-weight: bold;
        }

        .wei_span {
          cursor: pointer;
        }
      }

      .prev_pos {
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
      }

      .tips_mod {
        position: relative;
        width: 160px;
        height: 100px;
        background: #ffffff;
        border-radius: 8px;
        margin-top: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .btn_d {
          padding: 0 15px;
          background: #ffffff;
          border-radius: 15px;
          border: 1px solid #409eff;
          text-align: center;
          color: #409eff;
          cursor: pointer;
          margin-top: 10px;
        }
      }

      .choose_name_mod {
        width: 160px;
        height: 200px;
        background: #ffffff;
        border-radius: 8px;
        margin-top: 10px;
        overflow-y: auto;
        .vxscrollbar();

        .choose_name {
          width: 100%;
          min-height: 100px;
          // background-color: red;
          display: flex;
          flex-wrap: wrap;

          .names_div_li {
            padding: 0 8px;
            background-color: #ebf5ff;
            color: #409eff;
            height: 30px;
            border-radius: 15px;
            margin: 3px;
            font-size: 12px;
            font-weight: 400;
            color: #409eff;
            line-height: 30px;
            max-width: 140px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
    }
  }
}

.other_types {
  display: flex;

  .types_ul {
    display: flex;
    flex: 1;
    // min-height: 132px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #efefef;
    padding: 10px;

    .types_li_left {
      > div {
        cursor: pointer;
        padding: 10px 0;
      }
    }

    .types_li_right {
      flex: 1;
      // padding: 10px 0;
      margin-left: 30px;

      .right_div {
        display: flex;
        flex-wrap: wrap;

        > div {
          padding: 10px;
        }
      }
    }
  }

  .types_tips {
    margin-left: 30px;
    width: 190px;
    // height: 310px;
    padding-bottom: 20px;
    background: #f9f9f9;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .name_mod {
      width: 100%;
      padding: 10px;
      font-size: 14px;
      color: #17314c;
      font-weight: bold;
    }

    .tips_mod {
      width: 160px;
      height: 100px;
      background: #ffffff;
      border-radius: 8px;
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}

.title_right_1 {
  margin-left: -100px;
  display: flex;

  .linelin_left {
    width: 71px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 0px 2px 2px 0px;
    border: 1px solid #eee;
    font-weight: bold;
    cursor: pointer;
  }

  .linelin_right {
    width: 71px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 0px 2px 2px 0px;
    border: 1px solid #eee;
    font-weight: bold;
    cursor: pointer;
  }

  .linear {
    color: #fff;
    background: linear-gradient(90deg, #409eff 0%, #26d0f1 100%);
    border: none;
  }
}

.example {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  background: #f5f6fa;
  opacity: 0.5;
}

.Highlight {
  color: #049fff;
}

.footer_left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 128px;
  height: 38px;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  cursor: pointer;
}

.footer_right {
  margin-left: 30px;
  cursor: pointer;
  width: 128px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background: #fafafa;
  border-radius: 19px;
}

.upload_img {
  width: 129px;
  line-height: 38px;
  background-color: #ecf5ff;
  border: 1px dashed #419eff;
  text-align: center;
  border-radius: 4px;
  color: #419eff;
  cursor: pointer;
  user-select: none;
}

//滚动条
.vxscrollbar() {
  scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    outline: none;
  }
}

.news_windows {
  height: 300px;
  overflow-y: auto;
}

.modalTitle {
  display: flex;

  .titleName {
    font-size: 16px;
    font-weight: 600;
    color: #495970;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    span {
      font-size: 12px;
      font-weight: 400;
      color: #ff4f40;
      margin-left: 16px;
    }
  }

  .boxName {
    margin-left: 42px;
  }
}

.footer {
  width: 144px;
  text-align: center;
  line-height: 45px;
  color: #fff;
  font-size: 16px;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  border-radius: 35px;
  cursor: pointer;
}

.radioul {
  display: flex;
  flex-wrap: wrap;
}

.ca_tip {
  display: flex;
  justify-content: space-between;
  height: 45px;
  border-left: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
  border-top: 1px solid #e6e6e6;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  padding: 0 0 0 20px;

  .ca_disp {
    display: flex;
    align-items: center;
    height: 100%;

    .disp_t {
      color: #ffba3c;
      width: 100px;
      display: flex;
      align-items: center;

      .rantan {
        width: 11px;
        height: 11px;
        background-color: #ffba3c;
        margin-right: 5px;
      }
    }

    .disp_r {
      margin-left: 10px;
      color: #17314c;
      font-weight: 500;
      font-size: 14px;
    }

    .custom_disp {
      color: #409eff;
      font-size: 12px;
      cursor: pointer;
    }

    .line_disp {
      height: 12px;
      border-left: 1px solid #e1e1e1;
      margin: 0 12px;
    }

    .clear_disp {
      font-size: 12px;
      cursor: pointer;
    }
  }

  .disp_8 {
    flex: 8;
    background: linear-gradient(to right, #ffffff 98%, #f4f4f4);
  }

  .disp_2 {
    justify-content: center;
    margin-left: 12px;
    flex: 2;
  }
}

.exam {
  &_content {
    // height: 480px; // todo：自适应相关暂时去掉
    // overflow-y: scroll; // todo：自适应相关暂时去掉
    // border-bottom: 1px solid #E6E6E6; // todo：自适应相关暂时去掉
    // border-top: 1px solid #E6E6E6;

    scrollbar-color: auto;
    scrollbar-width: auto;
    overflow-y: overlay;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    // 滑块背景
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      box-shadow: none;
    }
    // 滑块
    &::-webkit-scrollbar-thumb {
      background-color: #c1c1c1;
      outline: none;
    }

    &_top {
      display: flex;
      justify-content: space-between;

      &_item {
        width: 452px;
        height: 100%;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #efefef;
      }

      .active {
        border-color: #409eff;
        transition: border-color 0.5s linear 0s;
      }

      .box {
        border-radius: 10px;
        border: 3px solid #fff;
      }

      .boxActive {
        border-radius: 10px;
        border: 3px solid #e9f4ff;
        transition: border-color 0.5s linear 0s;
      }

      .left {
        ::ng-deep {
          .ant-form-item {
            margin-bottom: 10px !important;
          }
        }
      }

      .right {
        ::ng-deep {
          .ant-form-item {
            margin-bottom: 0px !important;
          }
        }
      }
    }

    &_bottom {
      p {
        height: 20px;
        font-size: 14px;
        font-weight: bold;
        color: #262626;
        line-height: 20px;
        margin: 30px 0 20px 0;
      }

      &_item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        span {
          height: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #595959;
          line-height: 20px;
          margin: 0 10px;
        }
      }
    }
  }

  &_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 15px;

    h3 {
      font-size: 16px;
      font-weight: bold;
      color: #17314c;
      line-height: 22px;
      // border-left: 3px solid #409eff;
      padding-left: 16px;
      margin: 16px 0;
    }

    &_selected {
      font-size: 20px;
      color: #049fff;
      cursor: pointer;
    }

    &_unselected {
      width: 20px;
      height: 20px;
      border-radius: 10px;
      border: 1px solid #e9e9e9;
      cursor: pointer;
    }

    &_icon {
      font-size: 16px;
      margin-left: 10px;
      color: #bfbfbf;
      cursor: pointer;
    }

    &_tip {
      width: 470px;

      h3 {
        font-size: 14px;
        font-weight: bold;
        color: #262626;
        line-height: 20px;
      }

      p {
        font-size: 12px;
        font-weight: 400;
        color: #262626;
        line-height: 17px;
        margin: 10px 0 16px 0;
      }

      img {
        width: 100%;
      }
    }
  }

  &_form {
    margin: 0 16px;

    ::ng-deep {
      .ant-radio-group {
        display: flex;
        justify-content: flex-start;
      }

      .ant-radio-wrapper {
        width: 30%;
      }
    }

    &_label {
      font-size: 14px;
      font-weight: bold;
      color: #262626;
      line-height: 20px;
      > span {
        font-size: 12px;
        font-weight: 400;
        color: #bfbfbf;
        line-height: 20px;
        margin-left: 20px;
      }

      &_icon {
        font-size: 16px;
        margin-left: 10px;
        color: #bfbfbf;
        cursor: pointer;
        position: relative;
        top: 1px;
      }

      &_tip {
        width: 550px;

        img {
          width: 100%;
        }
      }
    }

    &_col {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      &_item {
        width: 50%;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        span {
          margin-left: 10px;
          // margin-top: 3px;
        }
      }
    }

    &_list {
      display: flex;
      justify-content: flex-start;
      width: 100%;
      margin-top: 20px;

      &_item {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        > span {
          margin: 0 15px 0 5px;
        }
      }
    }
  }

  &_adaptive {
    margin: 0 16px;

    &_label {
      &::before {
        display: inline-block;
        margin-right: 4px;
        color: #ff4f40;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
      }
    }

    &_checkboxs {
      background-color: #bfbfbf;
    }
  }
}

::ng-deep {
  .round-right-drawer3 {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 108px);
      overflow-y: auto;
      // padding-bottom: 66px;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer3-nofooter {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow-y: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
