import { HttpClient } from "@angular/common/http";
import {
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
  ViewChild,
  OnD<PERSON>roy,
} from "@angular/core";
import { SurveyApiService } from "../../../service/survey-api.service";
import {
  NzTreeComponent,
  NzMessageService,
  NzDrawerService,
} from "ng-zorro-antd";
import { ProjectManageService } from "../../../service/project-manage.service";
import { DownloadUtilService } from "../../../service/download-util.service";
import { AdvancedModelComponent } from "../../../report-manage/report-home/advancedFilter/advanced-model.component";
import { NzModalService } from "ng-zorro-antd/modal";
import { LoginService } from "@src/modules/login/login.service";
import { BatchExportModalComponent } from "../batchexport-modal/batchexport-modal.component";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { NzDrawerRef } from "ng-zorro-antd";
import { PermissionService } from "@src/modules/service/permission-service.service";
@Component({
  selector: "app-prisma-drawer",
  templateUrl: "./prisma-drawer.component.html",
  styleUrls: ["./prisma-drawer.component.less"],
})
export class PrismaDrawerComponent implements OnInit, OnDestroy {
  @ViewChild("advancedChild", { static: false })
  advancedChild: AdvancedModelComponent;

  @Input() projectId: string;
  @Input() orgId: string;
  @Input() orgName: string;

  tabIndex: number = 0; // tabIndex

  respondentList: any[] = []; // 填答人列表数据
  respondentCheckedIds: any[] = []; // 选中填答人员ids
  isAll: boolean = false; // 是否全选
  searchRespondentValue: string = ""; // 模糊搜索
  respondentParams: any = ""; // 填答人搜索参数

  dataSet = [];
  searchName: string = "";

  totalCount: number = 0;
  currentPage: number = 1;
  pageSize: number = 20;

  public chartOption: any;
  // containerWidth = '500'
  containerWidth = "465";
  containerHeight = "350";
  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;

  permission: boolean = false;

  checked: boolean = false;

  defaultExpandedKeys: string[] = [];

  page: any = {
    current: 1,
    size: 20,
    total: 1,
  };
  private routerSubscription: Subscription;

  constructor(
    private http: HttpClient,
    private surveySerivce: SurveyApiService,
    private cdf: ChangeDetectorRef,
    private msgServ: NzMessageService,
    private api: ProjectManageService,
    private downApi: DownloadUtilService,
    private modalService: NzModalService,
    private drawerService: NzDrawerService,
    private loginServ: LoginService,
    private customMsg: MessageService,
    private router: Router,
    private drawerRef: NzDrawerRef,
    public permissionService: PermissionService
  ) {}

  tabChange(event) {
    // 切换数据 重新渲染图表
    this.tabIndex = event.index;
    this.loadingData();
  }

  getQueryParam(): any {
    // 获取高级筛选参数
    this.respondentParams = {
      isAll: this.isAll,
      // "match": null,
      // "moreConditions": null,
      page: this.page,
      personIds: this.isAll ? [] : this.respondentCheckedIds,
      projectId: this.projectId,
      value: this.searchRespondentValue,
    };
    if (this.advancedChild.getSonMatch())
      this.respondentParams.match = this.advancedChild.getSonMatch();
    if (this.advancedChild.getSonParams())
      this.respondentParams.moreConditions = this.advancedChild.getSonParams();
  }

  async loadingData(isSearch?: boolean) {
    // 调用数据

    if (this.tabIndex === 0) {
      // 组织部门
    } else if (this.tabIndex === 1) {
      // 人口标签
    } else {
      // 填答人
      this.clearParams();
      this.respondentParams = {
        isAll: this.isAll,
        // "match": null,
        // "moreConditions": null,
        page: this.page,
        personIds: this.isAll ? [] : this.respondentCheckedIds,
        projectId: this.projectId,
        value: this.searchRespondentValue,
      };
      await this.api
        .getRespondentData(this.respondentParams)
        .subscribe((res) => {
          if (res.result.code === 0) {
            this.respondentCheckedIds = [];
            this.respondentList = res.data;
            this.page = res.page;
            this.totalCount = res.page.total; // 当前条件筛选出的总数
            this.respondentList.forEach((item) => {
              this.isAll ? (item.checked = true) : (item.checked = false);
            });
          }
        });
    }
  }

  async loadData() {
    // 高级筛选数据
    this.getQueryParam();
    await this.api.getRespondentData(this.respondentParams).subscribe((res) => {
      if (res.result.code === 0) {
        // this.searchRespondentValue = "";
        // this.respondentCheckedIds = []
        this.respondentList = res.data;
        this.page = res.page;
        this.totalCount = res.page.total; // 当前条件筛选出的总数
        this.respondentList.forEach((item) => {
          this.isAll ? (item.checked = true) : (item.checked = false);
          if (this.respondentCheckedIds.length !== 0 && !this.isAll) {
            this.respondentCheckedIds.map((itm) => {
              if (itm == item.id) item.checked = true;
            });
          }
        });
      }
    });
  }

  exportRespondentList() {
    // 导出填答人列表
    if (this.respondentCheckedIds.length === 0)
      // return this.msgServ.warning("请选择要导出的人员");
      return this.customMsg.open("warning", "请选择要导出的人员");
    this.respondentParams.isAll = this.isAll;
    this.respondentParams.personIds = this.isAll
      ? []
      : this.respondentCheckedIds;
    this.api.exportRespondentData(this.respondentParams).subscribe((res) => {
      this.downFile(res);
    });
  }

  downFile(data) {
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });

    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];

    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];

    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
      // .split('\'\'')[1]
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  showDeleteConfirm() {
    if (this.respondentCheckedIds.length === 0 && !this.isAll)
      // return this.msgServ.error("请选择要删除的数据");
      return this.customMsg.open("error", "请选择要删除的数据");
    this.modalService.confirm({
      nzTitle: `确定删除当前选中的 ${
        this.isAll ? this.totalCount : this.respondentCheckedIds.length
      } 条数据吗`,
      // nzContent: '<b style="color: red;">Some descriptions</b>',
      nzOkText: "确认",
      nzOkType: "danger",
      nzOnOk: () => {
        this.getQueryParam();
        this.api
          .batchDeletePersonInfo(this.respondentParams)
          .subscribe((res) => {
            if (res.result.code === 0) {
              this.msgServ.success("删除成功");
              this.loadingData();
            }
          });
      },
      nzCancelText: "取消",
      nzOnCancel: () => console.log("Cancel"),
    });
  }

  changeRespondentAllChecked() {
    this.isAll = !this.isAll;
    this.respondentCheckedIds = [];
    this.respondentList.map((item) => {
      item.checked = this.isAll;
      if (this.isAll) {
        this.respondentCheckedIds.push(item.id);
      }
    });
  }

  changeRespondentChecked(e, id) {
    // 选择列表人员

    let index = this.respondentCheckedIds.findIndex((item) => {
      return item === id;
    });
    if (index === -1) {
      if (e) {
        this.respondentCheckedIds.push(id);
      }
    } else {
      if (!e) {
        this.respondentCheckedIds.splice(index, 1);
      }
    }
  }

  search(isBtn?: boolean) {
    // isHigh
    if (this.tabIndex === 2) {
      // this.page = {
      //   current: 1,
      //   size: 20,
      //   total: 1,
      // };
      this.page.current = 1;
      if (isBtn) {
        this.respondentParams = {
          isAll: this.isAll,
          page: this.page,
          personIds: this.isAll ? [] : this.respondentCheckedIds,
          projectId: this.projectId,
          value: this.searchRespondentValue,
        };
      } else {
        this.getQueryParam();
      }

      this.api.getRespondentData(this.respondentParams).subscribe((res) => {
        if (res.result.code === 0) {
          this.respondentList = res.data;
          this.page = res.page;
          this.totalCount = res.page.total; // 当前条件筛选出的总数
          this.respondentList.forEach((item) => {
            this.isAll ? (item.checked = true) : (item.checked = false);
            if (this.respondentCheckedIds.length !== 0 && !this.isAll) {
              this.respondentCheckedIds.map((itm) => {
                if (itm == item.id) item.checked = true;
              });
            }
          });
        }
      });
    }
  }

  clearParams(isClear?: boolean) {
    // 清空参数
    // this.page = {
    //   current: 1,
    //   size: 20,
    //   total: 1,
    // };
    this.page.current = 1;
    this.respondentCheckedIds = [];
    this.isAll = false;
    this.searchRespondentValue = "";
    this.respondentParams = {
      isAll: this.isAll,
      // "match": null,
      // "moreConditions": null,
      page: this.page,
      personIds: this.respondentCheckedIds,
      projectId: this.projectId,
      value: this.searchRespondentValue,
    };
    if (isClear) this.loadData();
  }

  ngOnInit() {
    this.permission = this.permissionService.isPermission();

    this.loadingData(); // 加载数据
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  showClearConfirm() {
    // 清除填答
    if (this.respondentCheckedIds.length === 0 && !this.isAll)
      // return this.msgServ.error("请选择要清除的数据");
      return this.customMsg.open("error", "请选择要清除的数据");
    this.modalService.confirm({
      nzTitle: `确定清除当前选中的 ${
        this.isAll ? this.totalCount : this.respondentCheckedIds.length
      } 条数据吗`,
      // nzContent: '<b style="color: red;">Some descriptions</b>',
      nzOkText: "确认",
      nzOkType: "danger",
      nzOnOk: () => {
        this.getQueryParam();
        this.api
          .batchClearPersonInfo(this.respondentParams)
          .subscribe((res) => {
            if (res.result.code === 0) {
              this.msgServ.success("清除数据成功");
              this.loadingData();
            }
          });
      },
      nzCancelText: "取消",
      nzOnCancel: () => console.log("Cancel"),
    });
  }
  funbatchexport() {
    const modal = this.drawerService.create({
      nzContent: BatchExportModalComponent,
      nzTitle: "批量导出",
      nzWrapClassName: "round-right-drawer9",
      nzContentParams: {
        projectId: this.projectId,
        // orgId: orgId,
        // orgName: orgName
      },
      nzWidth: 600,
      nzMaskClosable: true,
      nzClosable: true,
    });

    this.loginServ.drawerRef = modal;
  }
}
