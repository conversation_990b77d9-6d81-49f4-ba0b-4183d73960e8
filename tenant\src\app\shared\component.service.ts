import { Injectable } from '@angular/core';
import { NzModalService } from "ng-zorro-antd";
import { MessageComponent } from './message/message.component';

@Injectable({
  providedIn: 'root'
})
export class ComponentService {

  constructor(private modalService: NzModalService) { }

  message(type, message?) {
    this.modalService.create({
      nzContent: MessageComponent,
      nzComponentParams:{
        type,
        message
      },
      nzWidth: 480,
      nzFooter: null
    })
  }

}
