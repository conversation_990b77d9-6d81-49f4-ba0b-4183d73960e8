import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TouristComponent } from './tourist.component';
import { TouristRoutingModule } from './tourist-routing.modules'
import { TipComponent } from '../tourist/tip/tip.component';
import { EpaComponent } from '../tourist/epa/epa.component';
import { AtComponent } from '../tourist/at/at.component';
import { AmaComponent } from '../tourist/ama/ama.component';
import { LayoutModule } from '../../layout/layout.module';
import { CsiComponent } from './csi/csi.component';
import { CaComponent } from './ca/ca.component';
import { HrComponent } from './hr/hr.component';
import { ArtistComponent } from './artist/artist.component';
import { ProgrammerComponent } from './programmer/programmer.component';
import { SalesComponent } from './sales/sales.component';
import { PmComponent } from './pm/pm.component';
import { PurchaseComponent } from './purchase/purchase.component';
import { MarketComponent } from './market/market.component';
import { FinanceComponent } from './finance/finance.component';
import { CsComponent } from './cs/cs.component';
import { LegalComponent } from './legal/legal.component';
import { RiskControlComponent } from './risk-control/risk-control.component';
import { PwvoComponent } from './pwvo/pwvo.component';
import { PcaComponent } from './pca/pca.component';
import { PtaComponent } from './pta/pta.component';
import { S360Component } from './s360/s360.component';
import { McaComponent } from './mca/mca.component';
import { PrismaComponent } from './prisma/prisma.component';
import { CultureComponent } from './vnew_culture/culture.component';
import { RenComponent } from './vnew_ren/culture.component';
import { SheComponent } from './vnew_she/culture.component';
import { XiaoComponent } from './vnew_xiao/culture.component';

import { TipnewComponent } from './v_Tip_new/tipnew.component'
import { CanewComponent } from './v_Ca_new/canew.component'

import { S360Componentcultrue } from './s360p/s360p.component'
import { S270Component } from './s270/s270.component'
import { NzCarouselModule } from 'ng-zorro-antd/carousel';
import { PdpComponent } from './pdp/pdp.component'
import { SjtpComponent } from './sjtp/sjtp.component'
import { VideoComponent } from './video/video.component'
import { MokaSuccessComponent } from './moka-success/oauthmoka-success.component'
import { MokaFailComponent } from './moka-fail/oauthmoka-fail.component'

import { PrismaCultureComponent } from './prismaCulture/prismaCulture.component'
import { MhsComponent } from "../tourist/mhs/mhs.component";
import { DoublePerspectiveComponent } from './doublePerspective/doublePerspective.component';
import { OrgSurveyComponent } from './orgSurvey/orgSurvey.component';

@NgModule({
  imports: [CommonModule, TouristRoutingModule, LayoutModule, NzCarouselModule],
  declarations: [
    TouristComponent,
    TipComponent,
    EpaComponent,
    AtComponent,
    AmaComponent,
    CsiComponent,
    CaComponent,
    HrComponent,
    ArtistComponent,
    ProgrammerComponent,
    SalesComponent,
    PmComponent,
    PurchaseComponent,
    MarketComponent,
    FinanceComponent,
    CsComponent,
    LegalComponent,
    RiskControlComponent,
    PwvoComponent,
    PcaComponent,
    PtaComponent,
    S360Component,
    McaComponent,
    PrismaComponent,
    CultureComponent,
    RenComponent,
    SheComponent,
    XiaoComponent,
    TipnewComponent,
    CanewComponent,
    S360Componentcultrue,
    S270Component,
    PdpComponent,
    SjtpComponent,
    VideoComponent,
    MokaSuccessComponent,
    MokaFailComponent,
    PrismaCultureComponent,
    MhsComponent,
    DoublePerspectiveComponent,
    OrgSurveyComponent,
  ],
  exports: [TouristComponent],
})
export class TouristModule {}
