import { Component,Input} from '@angular/core';
@Component({
  selector: 'index-tip',
  templateUrl: './index-tip.component.html',
  styleUrls: ['./index-tip.component.less'],
})
export class IndexTipComponent  {

  @Input() index: number = 0;
  @Input() x: 'HIGH' |'MID' |'LOW'; 
  @Input() y: 'HIGH' |'MID' |'LOW'; 

  row = ['HIGH', 'MID', 'LOW']
  col = ['LOW', 'MID', 'HIGH']
  constructor(
  ) {}
  
  ngOnInit() {
  }
}