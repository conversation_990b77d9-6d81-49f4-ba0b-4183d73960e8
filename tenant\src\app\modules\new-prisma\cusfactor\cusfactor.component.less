.custom_xy {
  position: absolute;
  // animation-name: example;
  animation-duration: 0.2s;
  min-height: 200px;
  z-index: 999999;
  left: 16px;
  top: 110px;
  overflow: hidden;

  .nz_menu_ul {
    padding: 20px 15px;
    min-height: 200px;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 8px;
    width: 289px;

    .nz_menu_li {
      display: flex;
      align-items: center;

      >span {
        display: inline-block;
        width: 30px;
      }

      >input {
        flex: 1;
        margin-left: 20px;
      }

      .input {
        flex: 1;
        margin-left: 20px;
      }
    }

    .list_word {
      margin-left: 50px;
      margin-top: 5px;
      font-size: 12px;

      .word_left {
        width: 68px;
        background: #409eff;
        border-radius: 20px;

        color: #fff;
        text-align: center;
        cursor: pointer;
        margin-top: 10px;
      }

      .word_right {
        margin-top: 10px;
        width: 58px;
        background: #fafafa;
        border-radius: 20px;
        text-align: center;
        margin-left: 20px;
        color: #aaaaaa;
        cursor: pointer;
      }
    }

    .list_button {
      margin-top: 25px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.disClick {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25);
  background-color: #fff;
}

.custom_xy_1 {
  position: absolute;
  // animation-name: example;
  animation-duration: 0.2s;
  min-height: 200px;
  z-index: 999999;
  left: 85px;
  overflow: hidden;

  .nz_menu_ul {
    padding: 20px 15px;
    min-height: 200px;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 8px;
    width: 289px;

    &.nz_menu_ul_1 {
      width: 230px;
      min-height: 160px;
    }

    .nz_menu_li {
      display: flex;
      align-items: center;

      >span {
        display: inline-block;
        width: 30px;
      }

      >input {
        flex: 1;
        margin-left: 20px;
      }

      .input {
        flex: 1;
        margin-left: 20px;
      }
    }

    .list_word {
      margin-left: 50px;
      margin-top: 5px;
      font-size: 12px;

      .word_left {
        width: 68px;
        background: #409eff;
        border-radius: 20px;

        color: #fff;
        text-align: center;
        cursor: pointer;
        margin-top: 10px;
      }

      .word_right {
        margin-top: 10px;
        width: 58px;
        background: #fafafa;
        border-radius: 20px;
        text-align: center;
        margin-left: 20px;
        color: #aaaaaa;
        cursor: pointer;
      }
    }

    .list_button {
      margin-top: 25px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.modal_div_bottom {
  .custom_xy_1 {
    top: 20%;
  }
}

.modal_div_top {
  .custom_xy_1 {
    bottom: 20%;
  }
}

.modal_show {
  display: flex;
  // border-bottom: 1px solid #e6e6e6;
  // height: calc(100% - 53px);
  height: 100%;

  .show_li_left {
    width: 200px;
    display: flex;
    flex-direction: column;

    .add-box {
      padding: 16px;
    }

    .modal-box-edit {
      height: calc(100% - 64px);
      overflow-y: auto;
    }

    .modal-box-view {
      flex: 1 0 0;
      overflow-y: auto;
    }

    .modal_div {
      width: 200px;
      height: 54px;
      line-height: 54px;
      // text-align: left;
      cursor: pointer;
      position: relative;
      display: flex;
      justify-content: flex-start;
      border-left: 4px solid transparent;

      .namesdiv {
        text-align: left;
        width: 160px;
        padding-left: 16px;
        overflow: hidden;
        font-weight: 500;
        /*超出部分隐藏*/
        white-space: nowrap;
        /*不换行*/
        text-overflow: ellipsis;
      }

      .point_xy {
        position: absolute;
        right: 10px;
        top: 19px;
        width: 16px;
        height: 16px;
        display: flex;
        flex-flow: column;
        justify-content: space-between;
        align-items: center;

        >li {
          width: 3px;
          height: 3px;
          border-radius: 1.5px;
          background-color: #409eff;
        }

        .coutons_xy {
          position: absolute;
          width: 112px;
          min-height: 144px;
          background: #ffffff;
          box-shadow: 0px 5px 12px 0px rgba(23, 49, 76, 0.12);
          border-radius: 8px;
          top: 20px;
          left: -115px;
          z-index: 999;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 10px 0;

          >li {
            width: 100%;
            height: 54px;
            line-height: 54px;
            color: #495970;
          }
        }
      }

      .Nz_menus {
        margin-right: 150px;
      }
    }

    .Modalcheckclass {
      background: #f5faff;
      color: #409eff;
      border-color: #409eff;
    }
  }

  .show_li_right {
    flex: 1;
    border-left: 1px solid #e6e6e6;

    .right-header {
      padding: 16px 16px 13px 16px;
      position: relative;

      .title-handle {
        position: absolute;
        right: 16px;
        top: 8px;
        display: flex;
        align-items: center;
      }
    }

    .right-box {
      height: calc(100% - 64px);
      overflow-y: auto;
    }

    .right_ul {
      padding: 0 0 10px 0;

      .righg_li {
        padding: 10px 10px 10px 16px;

        >div {
          display: flex;
          align-items: center;
          // justify-content: end;
          // padding-right: 63px;

          >.put_ui {
            max-width: 645px;
            height: 30px;
          }

          .li_span {
            width: 80px;
            text-align: left;
          }
        }

        .over_high {
          max-height: 300px;
          overflow-y: auto;
        }
      }
    }
  }
}

.custom_mock {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.modal_menus {
  display: flex;
  padding: 20px 20px;

  .li_menus_left {
    width: 128px;
    height: 38px;
    line-height: 38px;
    color: #fff;
    text-align: center;
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    cursor: pointer;
  }

  .li_menus_left_back {
    width: 128px;
    height: 38px;
    line-height: 38px;
    color: #aaaaaa;
    text-align: center;
    background: #fafafa;
    border-radius: 19px;
    cursor: pointer;
  }

  .li_menus_right {
    cursor: pointer;
    margin-left: 30px;
    width: 128px;
    height: 38px;
    color: #aaaaaa;
    background: #fafafa;
    border-radius: 19px;
    line-height: 38px;
    text-align: center;
  }
}


.scroll {
  // height: 500px;
  .vxscrollbar()
}

//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }

  &::-webkit-scrollbar-track {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

.add_child {
  margin-left: 80px;
  color: #419EFF;
  font-size: 12px;
  padding: 5px 0;
  justify-content: start !important;
  cursor: pointer;
}

.childclass {
  background-color: #F8F8F8;
}

.scroll-textarea::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.scroll-textarea::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}
.scroll-textarea::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.1);
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
.relation_label{
  position: relative;
  margin-right: 80px;
  color: #419EFF;
  font-size: 12px;
  padding: 5px 0;
  justify-content: start !important;
  cursor: pointer;
}
.custom_relation_label_1{
  position: absolute;
  // animation-name: example;
  animation-duration: 0.2s;
  min-height: 150px;
  z-index: 999999;
  right: 50px;
  overflow: hidden;

  .nz_menu_ul {
    padding: 20px 15px;
    min-height: 150px;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 8px;
    width: 289px;

    &.nz_menu_ul_1 {
      width: 230px;
      min-height: 160px;
    }

    .nz_menu_li {
      display: flex;
      justify-content: space-between;
  
      .relation_label_select {
       width:185px;
      }
    }

    .list_word {
      margin-left: 50px;
      margin-top: 5px;
      font-size: 12px;

      .word_left {
        width: 68px;
        background: #409eff;
        border-radius: 20px;

        color: #fff;
        text-align: center;
        cursor: pointer;
        margin-top: 10px;
      }

      .word_right {
        margin-top: 10px;
        width: 58px;
        background: #fafafa;
        border-radius: 20px;
        text-align: center;
        margin-left: 20px;
        color: #aaaaaa;
        cursor: pointer;
      }
    }

    .list_button {
      margin-top: 25px;
      display: flex;
      justify-content: flex-end;
    }
  }
  

}