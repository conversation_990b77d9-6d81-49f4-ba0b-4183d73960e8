<div class="title">
  批量修订
</div>

<div *ngFor="let item of modelList; let i = index">
  <app-subject-edit
    #editWidget
    (pre)="pre()"
    (next)="next()"
    *ngIf="i === currentIndex"
    [subjectModel]="item"
    [isBatchMode]="true"
  ></app-subject-edit>
</div>

<!-- <div style="display:flex; justify-content:flex-end; margin-bottom: 10px;">
    <button nz-button nzType="primary" (click)="pre()" [disabled]="currentIndex === 0" [nzLoading]="!isNext && isLoading" > 
        <i nz-icon nzType="left"></i>
    </button>
    <button nz-button nzType="primary" (click)="next()" [disabled]="currentIndex >= (modelList.length-1)" [nzLoading]="isNext && isLoading" >
        <i nz-icon nzType="right"></i>
    </button>
</div> -->

<div class="footer">
  <button nz-button class="iptBtn" (click)="ok()">
    <span>确认</span>
  </button>
</div>
