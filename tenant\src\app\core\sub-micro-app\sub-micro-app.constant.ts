import { createPairEventHook } from "@knx/micro-app";

export const VUE_MICRO_APP_HOST =
  process.env.NODE_ENV === "production"
    ? location.origin
    : "http://127.0.0.1:9000";

export const VUE_MICRO_APP_URL = new URL(
  "/child-sag-tenant",
  VUE_MICRO_APP_HOST
).href;
export const VUE_MICRO_APP_NAME = "sag-tenant";
export const VUE_MICRO_APP_BASEROUTE = "sag-tenant";

const [baseEvents, microEvents] = createPairEventHook(VUE_MICRO_APP_NAME);

export const VUE_MICRO_APP_BASE_EVENTS = baseEvents;
export const VUE_MICRO_APP_MICRO_EVENTS = microEvents;
