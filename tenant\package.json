{"name": "vx-admin", "version": "1.0.0", "description": "", "author": "", "license": "MIT", "keywords": ["vx", "angular"], "scripts": {"ng": "ng", "start": "ng serve --host 127.0.0.1 --port 4202 --disable-host-check --live-reload=false", "start-dev": "ng serve -o --port 4202 --proxy-config proxy.config.dev.js --host 0.0.0.0", "start-qa": "set NODE_OPTIONS=--openssl-legacy-provider && ng serve -o --port 4202 --proxy-config proxy.config.qa.js --host 0.0.0.0", "start-stage": "ng serve -o --port 4202 --proxy-config proxy.config.stage.js --host 0.0.0.0", "start-prod": "ng serve -o --port 4202 --proxy-config proxy.config.prod.js --host 0.0.0.0", "hmr": "npm run color-less && ng serve -c=hmr", "build-dev": "ng build --prod", "build": "npm run color-less && node --max_old_space_size=5120 ./node_modules/@angular/cli/bin/ng build --prod", "analyze": "node --max_old_space_size=5120 ./node_modules/@angular/cli/bin/ng build --prod --stats-json", "lint": "npm run lint:ts && npm run lint:style", "lint:ts": "tslint -p tsconfig.app.json -c tslint.json \"src/**/*.ts\" --fix", "lint:style": "stylelint \"src/**/*.less\" --syntax less --fix", "lint-staged": "lint-staged", "tslint-check": "tslint-config-prettier-check ./tslint.json", "e2e": "ng e2e", "test": "ng test --watch", "test-coverage": "ng test --code-coverage --watch=false", "color-less": "node scripts/color-less.js", "icon": "ng g ng-alain:plugin icon"}, "dependencies": {"@angular/animations": "~8.2.14", "@angular/common": "~8.2.14", "@angular/compiler": "~8.2.14", "@angular/core": "~8.2.14", "@angular/forms": "~8.2.14", "@angular/http": "7.2.15", "@angular/platform-browser": "~8.2.14", "@angular/platform-browser-dynamic": "~8.2.14", "@angular/router": "~8.2.14", "@antv/data-set": "^0.10.2", "@antv/g2": "^3.5.11", "@antv/g2-plugin-slider": "^2.1.1", "@balkangraph/orgchart.js": "^8.4.1", "@delon/auth": "^13.0.0", "@knx/knx-ngx": "^8.3.129", "@knx/micro-app-ng": "^1.0.0-beta.7", "@knx/shared": "^1.2.7", "@micro-zoe/micro-app": "^1.0.0-rc.3", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@types/echarts": "^4.1.3", "@types/xlsx": "0.0.36", "ajv": "^6.10.2", "angular-gridster2": "8.3.0", "angularx-qrcode": "^2.1.2", "clipboard": "^2.0.11", "dragula": "^3.7.3", "echarts": "^4.9.0", "echarts-wordcloud": "^1.1.3", "file-saver": "^2.0.2", "jszip": "^3.6.0", "lodash": "^4.17.21", "moment": "^2.24.0", "ng-zorro-antd": "^8.5.2", "ng2-dragula": "^2.1.1", "ngx-tinymce": "^7.0.0", "ngx-ueditor": "^2.1.3", "qrious": "^4.0.2", "rxjs": "~6.4.0", "rxjs-compat": "^6.1.0", "screenfull": "^5.0.0", "stream": "0.0.2", "tslib": "^1.10.0", "zone.js": "~0.9.1"}, "devDependencies": {"@angular-devkit/build-angular": "~0.803.14", "@angular/cli": "~8.3.24", "@angular/compiler-cli": "~8.2.14", "@angular/language-service": "~8.2.14", "@angularclass/hmr": "^2.1.3", "@knz/assembly": "2.1.0", "@knz/auth": "2.1.0", "@knz/cache": "2.1.0", "@knz/chart": "2.1.0", "@knz/form": "2.1.0", "@knz/mock": "2.1.0", "@knz/role": "2.1.0", "@knz/theme": "2.1.0", "@knz/util": "2.1.0", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "~2.0.3", "@types/jszip": "^3.1.6", "@types/mockjs": "^1.0.2", "@types/node": "~8.9.4", "antd-theme-generator": "^1.1.7", "codecov": "^3.6.1", "codelyzer": "^5.0.0", "gh-pages": "^2.1.1", "jasmine-core": "~3.4.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.1.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-jasmine": "~2.0.1", "karma-jasmine-html-reporter": "^1.4.0", "less-bundle-promise": "^1.0.7", "lint-staged": "^8.2.1", "mockjs": "^1.1.0", "ng-alain": "^8.8.0", "ng-alain-codelyzer": "^0.0.1", "prettier": "^1.18.2", "prettier-stylelint": "^0.4.2", "protractor": "~5.4.0", "stylelint": "^11.1.1", "stylelint-config-prettier": "^6.0.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^19.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.1.0", "stylelint-order": "^3.1.1", "ts-node": "~7.0.0", "tslint": "~5.15.0", "tslint-config-prettier": "^1.18.0", "typescript": "~3.5.3", "webpack-bundle-analyzer": "^3.6.0", "xlsx": "^0.15.6"}, "lint-staged": {"linters": {"src/**/*.ts": ["npm run lint:ts", "git add"], "src/**/*.less": ["npm run lint:style", "git add"]}, "ignore": ["src/assets/*"]}}