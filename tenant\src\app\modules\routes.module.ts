import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';

import { SharedModule } from '@shared';
import { RouteRoutingModule } from './routes-routing.module';

/*import { HomeComponent } from './home/<USER>';*/

import { LoginComponent } from './auth/login/index.component';
import { LoadingComponent } from './auth/loading/loading.component';

import { TouristModule } from './tourist/tourist.module';
import { StaticModule } from "@src/modules/static/static.module";
import { JumpComponent } from './auth/jump/jump.component';
import { SubMicroAppModule } from '@src/core/sub-micro-app';


const COMPONENTS = [
  // HomeComponent,
  LoginComponent,
  LoadingComponent,
  JumpComponent,
];
const COMPONENTS_NOROUNT = [];

@NgModule({
  imports: [ SharedModule, RouteRoutingModule, TouristModule, StaticModule, SubMicroAppModule ],
  declarations: [...COMPONENTS, ...COMPONENTS_NOROUNT, ],
  entryComponents: COMPONENTS_NOROUNT,
})
export class RoutesModule {}
