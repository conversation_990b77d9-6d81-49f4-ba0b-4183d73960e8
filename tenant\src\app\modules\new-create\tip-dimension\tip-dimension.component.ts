import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-tip-dimension',
  templateUrl: './tip-dimension.component.html',
  styleUrls: ['./tip-dimension.component.less']
})
export class TipDimensionComponent implements OnInit {

  @Input() dimensions: any[];
  @Input() reportType?: string;
  @Input() checked: string[] = [];
  @Input() radioCodelist?: any[] = [];
  @Input() radiolist?: any[] = [];
  @Input() isdisabled?: boolean = false;
  
  @Output() checkItem = new EventEmitter<any>();

  constructor(
  ) { }


  ngOnInit() {
    console.log(this.reportType, this.dimensions, this.checked)
  }
  wrapperChange(e) {
    this.checkItem.emit({reportType: this.reportType, checked: e})
  }//所有选中的维度code
}
