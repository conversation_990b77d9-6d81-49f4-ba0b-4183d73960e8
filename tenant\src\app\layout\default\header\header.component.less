.header {
  // width: 100%;
  // height: 64px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(230, 230, 230, 1);
  // align-items: center;
  // display: flex;
  &.add {
    background: none;
    border: none;
  }
  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // background-color: gold;
    padding: 14px 0;
    .new_add {
      font-size: 16px;
      color: #464646;
      // flex: 1;
      display: flex;
      justify-content: space-between;
      // padding: 0 200px;
      > div {
        width: 132px;
        line-height: 64px;
        text-align: center;
      }
      .bg_div {
        color: #fff;
        background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      }
      .product {
        position: absolute;
        z-index: 999;
        top: 70px;
        left: -450px;
        width: 929px;
        min-height: 255px;
        background: #ffffff;
        box-shadow: 0px 9px 21px 0px rgba(138, 138, 138, 0.11);
        border-radius: 4px;
        border: 1px solid #eef1f6;
        color: #17314c;
        display: flex;
        padding: 0 30px;
        .left_li {
          .title_d {
            display: flex;
            align-items: center;
            > span {
              margin-left: 15px;
              font-weight: bold;
            }
          }
          .point_name {
            display: flex;
            align-items: center;
            font-size: 16px;
            p {
              width: 4px;
              height: 4px;
              border-radius: 14px;
              background-color: #464646;
            }
            > span {
              margin-left: 10px;
            }
          }
        }
        .right_li {
          height: 384px;
          .title_d {
            display: flex;
            align-items: center;
            > span {
              margin-left: 15px;
              font-weight: bold;
            }
          }
          .ul_names {
            display: flex;
            flex-direction: column;
          }
          .point_name {
            min-width: 200px;
            width: 220px;
            display: flex;
            align-items: center;
            font-size: 16px;
            // padding: 0 10px;
            p {
              width: 4px;
              height: 4px;
              border-radius: 14px;
              background-color: #464646;
            }
            > span {
              margin-left: 10px;
            }
          }
        }
      }
    }
  }
  .logo {
    // width: 220px;
    // height: 64px;
    // width: 177px;
    // height: 54px;

    width: 118px;
    height: 36px;
  }
  .user {
    width: 15px;
    height: 16px;
    display: inline-block;
    background: url("../../../../assets/images/user.png");
    margin-right: 5px;
    position: relative;
    top: 2px;
  }
  .reg-btn {
    display: inline-block;
    width: 62px;
    height: 30px;
    line-height: 30px;
    margin-left: 30px;
    text-align: center;
    background: linear-gradient(
      90deg,
      rgba(38, 208, 241, 1) 0%,
      rgba(64, 158, 255, 1) 100%
    );
    border-radius: 15px;
    color: #ffffff;
  }
  .login {
    color: #464646;
    &.add {
      color: #17314c;
    }
  }
  .register {
    width: 62px;
    height: 30px;
    color: #fff;
    background: #409eff;
    border-radius: 15px;
    text-align: center;
    line-height: 30px;
    display: inline-block;
    margin-left: 33px;
    &.add {
      width: 62px;
      text-align: center;
      line-height: 30px;
      height: 30px;
      background: linear-gradient(
        90deg,
        rgba(38, 208, 241, 1) 0%,
        rgba(64, 158, 255, 1) 100%
      );
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 15px;
      color: #fff;
    }
  }
}

.indexHeader {
  width: 100%;
  height: 80px;
  color: #ffffff;
  align-items: center;
  display: flex;
  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .reg-btn {
    display: inline-block;
    width: 62px;
    height: 30px;
    line-height: 30px;
    margin-left: 30px;
    text-align: center;
    background: rgba(255, 255, 255, 1);
    border-radius: 15px;
    color: #409eff;
  }
}
