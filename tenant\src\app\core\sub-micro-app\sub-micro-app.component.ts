import { _HttpClient } from "@knz/theme";
import { Component, Inject, NgZ<PERSON>, On<PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { APP_BASE_HREF } from "@angular/common";
import { MicroAppEvent, AppState } from "@knx/micro-app";
import {
  VUE_MICRO_APP_URL,
  VUE_MICRO_APP_NAME,
  VUE_MICRO_APP_BASEROUTE,
  VUE_MICRO_APP_BASE_EVENTS,
  VUE_MICRO_APP_MICRO_EVENTS,
} from "./sub-micro-app.constant";
import { Router } from "@angular/router";
import { debounce } from "lodash";
import { MICRO_APP_OPTIONS, MicroAppOptions } from "@knx/micro-app-ng";

@Component({
  selector: "app-micro-app",
  templateUrl: "./sub-micro-app.component.html",
  styleUrls: ['./sub-micro-app.component.less']
})
export class SubMicroAppComponent implements  <PERSON><PERSON><PERSON><PERSON> {
  url = VUE_MICRO_APP_URL;
  name = VUE_MICRO_APP_NAME;
  baseroute = `${this.baseHref.replace(/\/$/, "")}/${VUE_MICRO_APP_BASEROUTE}`;
  data: Record<string, unknown> = {
    events: VUE_MICRO_APP_MICRO_EVENTS,
  };
  state: AppState | null = null;
  loading = false;

  cancel: VoidFunction;

  setLoading: (loading: boolean) => void;

  constructor(
    private router: Router,
    private ngZone: NgZone,
    @Inject(MICRO_APP_OPTIONS) private options: MicroAppOptions,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) {
    this.cancel = VUE_MICRO_APP_BASE_EVENTS.on((event) =>
      this.handleEvents(event)
    );
    this.setLoading = debounce((loading) => void (this.loading = loading), 50);
  }

  ngOnDestroy(): void {
    this.cancel();
  }

  private handleEvents(event: MicroAppEvent) {
    if (event.type === "navigate") {
      const { appName, path, routeKey, params = {} } = event.data;
      if (appName === this.options.appName) {
        if (path) {
          this.ngZone.run(() =>
            this.router.navigate([path], {
              queryParams: params,
            })
          );
        } else if (routeKey) {
          // TODO: 实现子应用根据 routeKey 控制基座跳转
        }
      }
    } else if (event.type === "appStateChange") {
      /**
       * TODO: 实现子应用跳转和加载时，基座的加载动画，或者一些异常处理
       *
       * - 'error' 子应用跳转或加载失败
       * - 'loading' 子应用初始化加载中
       * - 'loaded' 子应用加载完成
       * - 'unloaded' 子应用已卸载
       * - 'navigating' 子应用正在执行页面跳转操作
       * - 'page-not-found' 子应用跳转页面时，页面不存在
       * - 'permission-denied' 子应用跳转页面时，权限不足
       * - 'navigated' 子应用页面跳转成功
       *
       * 基本逻辑
       * 1. 应用第一次加载时，显示加载动画
       *    - create 之后，loading 状态置为 true
       *    - 任何页面加载完成事件触发后，loading 状态值为 false
       *    - 优化项，可以考虑设置一个 debounce
       * 2. 根据应用当前状态，在微应用上覆盖对应的遮罩
       */

      const state = event.data;
      if (this.loading && this.isNavigationEnd(state)) {
        this.setLoading(false);
      }
      this.state = state;
    }
  }

  /**
   * 判断状态是否表示应用的页面跳转行为跳转结束
   * @param state - 应用状态
   */
  private isNavigationEnd(state: AppState) {
    return (
      state === "navigated" ||
      state === "page-not-found" ||
      state === "permission-denied" ||
      state === "error"
    );
  }
}
