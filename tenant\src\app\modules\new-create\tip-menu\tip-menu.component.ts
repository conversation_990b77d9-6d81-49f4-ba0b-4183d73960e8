import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";

@Component({
  selector: "app-tip-menu",
  templateUrl: "./tip-menu.component.html",
  styleUrls: ["./tip-menu.component.less"],
})
export class TipMenuComponent implements OnInit {
  @Input() menus: any;
  @Input() reportTypes?: string[];
  @Input() checkReportType?: string;
  @Output() checkItem = new EventEmitter<any>();

  data: any[];
  selectReportType = "";
  isOpen: boolean = true;

  constructor() {}

  ngOnChanges() {
    setTimeout(() => {
      if (JSON.stringify(this.menus) != "{}") {
        this.data = this.formatData([this.menus]);
        this.selectReportType = this.checkReportType || this.data[0].reportType;
        let item = null;
        this.data.forEach((a) => {
          if (this.selectReportType == a.reportType) {
            item = a;
          } else {
            if (a.child) {
              a.child.forEach((b) => {
                if (this.selectReportType == b.reportType) {
                  item = b;
                }
              });
            }
          }
        });
        this.checkItem.emit(item);
      }
    });
  }

  ngOnInit() {
    if (JSON.stringify(this.menus) != "{}") {
      this.data = this.formatData([this.menus]);
      this.selectReportType = this.data[0].reportType;
      const item = this.data.find(
        (val) => val.reportType == this.selectReportType
      );
      this.checkItem.emit(item);
    }
  }
  formatData(data) {
    const newData = data.map((a) => ({
      reportType: a.surveyStandardQuestionnaire.reportType,
      name: a.surveyStandardQuestionnaire.name,
      val: a,
      child: a.questionnaireTipItems
        .map((b) => ({
          reportType: b.reportType,
          name: b.name,
          val: b,
        }))
        .filter((c) => this.reportTypes.includes(c.reportType)),
    }));
    return newData;
  }
  onCheck(e) {
    this.selectReportType = e.reportType;
    this.checkItem.emit(e);
  }
  onCollapsed() {
    this.isOpen = !this.isOpen;
  }
}
