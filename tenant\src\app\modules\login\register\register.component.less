.container {
  padding: 10px;
  margin-top: 15px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 38px;
}

.header_1 {
  width: 48px;
  height: 33px;
  font-size: 24px;
  font-weight: 300;
  color: rgba(23, 49, 76, 1);
  line-height: 33px;
}

.header_2 {
  width: 136px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(23, 49, 76, 1);
  line-height: 20px;
}

.content {
  width: 100%;
  padding: 30px 0;
}

.input_short {
  width: 200px;
  height: 40px;
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
  border: 1px solid rgba(230, 230, 230, 1);
}

.input_short2 {
  width: 200px;
  top: -4px;
  height: 40px;
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
}

.input_long {
  width: 416px;
  height: 40px;
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
  border: 1px solid rgba(230, 230, 230, 1);
}

.input_row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input_item {
  flex: 1;
}

.label_item {
  width: 412px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 117, 117, 1);
  line-height: 20px;
}

.submit {
  width: 416px;
  text-align: center;
  margin-top: 12px;
  cursor: pointer;
  user-select: none;
  border: none;
  height: 38px;
  line-height: 38px;
  font-size: 16px;
  color: #aaa;
  font-weight: 500;
  background: rgba(250, 250, 250, 1);
  border-radius: 19px;

  &.act {
    background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    color: #fff;
  }
}

nz-form-item {
  height: 62px;
}

:host ::ng-deep {

  .ant-select-selection--single .ant-select-selection__rendered {
    height: 40px;
  }

  .ant-form-item {
    margin-bottom: 0;
  }

  .ant-select-selection--single {
    height: 40px;
  }

  .ant-select-selection-selected-value {
    line-height: 40px;
  }

  .has-error .ant-form-explain,
  .has-error .ant-form-split {
    color: #FF7575;
  }

  .ant-btn-primary {
    width: 300px;
  }
}