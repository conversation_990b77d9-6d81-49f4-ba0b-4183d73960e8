<ng-container>
  <div [class]="type === 'phone' ? 'phone' : 'pc'">
    <!-- 中奖mask -->
    <div class="mask" *ngIf="!pageChange && !showWinningPage">
      <div>
        <i class="iconfont icon-icon-close"></i>
        <div class="mask-bg-img">
          <p
            class="mask-title"
            [innerHTML]="lotteryResultData.reminder.zh_CN | html"
          ></p>
          <div class="prize-picture">
            <img
              *ngIf="lotteryResultData.localPic"
              [attr.src]="lotteryResultData.localPic"
              width="auto"
              height="100%"
              alt="中奖图片"
            />
          </div>
          <p
            class="desc"
            [innerHTML]="lotteryResultData.prizeName.zh_CN | html"
          ></p>
          <div class="view-prize" (click)="toWinningPage()"></div>
        </div>
      </div>
    </div>
    <div
      class="bg-box"
      *ngIf="privewData"
      [ngStyle]="{
        background:
          type === 'phone'
            ? !privewData.bgPhoneImg
              ? 'linear-gradient(180deg, #6552E2 0%, #A17DE1 10%, #F8EEF6 28%, #BACBFF 100%)'
              : privewData.bgPhoneImg
            : !privewData.bgPCImg
            ? 'linear-gradient(180deg, #6552E2 0%, #A17DE1 10%, #F8EEF6 28%, #BACBFF 100%)'
            : privewData.bgPCImg
      }"
    >
      <div class="box">
        <img
          class="my-prize"
          *ngIf="!showWinningPage"
          (click)="toWinningPage()"
          src="assets/images/prisma/myprize.png"
          alt=""
        />
        <ng-container *ngIf="!showWinningPage">
          <div class="left-box">
            <div
              class="title"
              [ngStyle]="{ background: privewData.subjectPic }"
            >
              <p
                class="txt"
                [innerHTML]="privewData.subjectName.zh_CN | html"
              ></p>
            </div>
            <div
              class="show-box"
              [ngStyle]="{ background: privewData.boxBorderColor }"
            >
              <ul
                class="jiugongge"
                [ngStyle]="{ background: privewData.boxBackgroundColor }"
              >
                <li>
                  <div
                    class="prize-box"
                    *ngFor="
                      let item of privewData.prizeDisplayVoList;
                      let index = index
                    "
                    [ngStyle]="{
                      'border-color':
                        index === 0 ? privewData.boxBorderColor : none
                    }"
                  >
                    <div
                      class="prize-detail-box"
                      [ngStyle]="{
                        background:
                          index === 0
                            ? privewData.boxBorderColor
                            : !item.lotteryId
                            ? item.localIcon
                            : none
                      }"
                    >
                      <img
                        [attr.src]="item.localIcon"
                        *ngIf="item.lotteryId && item.prizeIcon !== 'empty'"
                        alt=""
                      />
                      <div
                        style="text-align: center; height: 22px; white-space:nowrap;"
                        *ngIf="!item.lotteryId || item.prizeIcon === 'empty'"
                        [innerHTML]="privewData.drawButtonName.zh_CN | html"
                      ></div>
                      <span
                        *ngIf="item.lotteryId"
                        [innerHTML]="item.categoryName.zh_CN | html"
                      ></span>
                      <span *ngIf="!item.lotteryId" style="color: #FFFFFF;"
                        >剩余{{ privewData.remainDrawNumber }}次</span
                      >
                    </div>
                  </div>
                </li>
              </ul>
              <div
                class="time-box"
                [ngStyle]="{ background: privewData.boxBackgroundColor }"
              >
                <div class="time-detail-box">
                  <span>距结束还剩</span>
                  <div class="time-block">
                    <span>{{ d }}</span>
                  </div>
                  <span>天</span>
                  <div class="time-block">
                    <span>{{ h }}</span>
                  </div>
                  <span>小时</span>
                  <div class="time-block">
                    <span>{{ m }}</span>
                  </div>
                  <span>分</span>
                </div>
              </div>
            </div>
          </div>
          <div class="rules-box">
            <div>
              <div class="rules-title">
                <img src="assets/images/prisma/mobile-title-rules.png" alt="" />
              </div>
              <div class="rules-list">
                <div
                  [innerHTML]="privewData.drawRuleDescription.zh_CN | html"
                ></div>
              </div>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="showWinningPage">
          <div class="left-box">
            <div class="title">
              <p
                class="txt"
                [innerHTML]="lotteryResultData.reminder.zh_CN | html"
              ></p>
            </div>
            <div
              class="show-box"
              [ngStyle]="{ background: privewData.boxBorderColor }"
            >
              <div
                class="jiugongge lottery-result-box"
                [ngStyle]="{ background: privewData.boxBackgroundColor }"
              >
                <img
                  *ngIf="lotteryResultData.localPic"
                  [attr.src]="lotteryResultData.localPic"
                  width="auto"
                  height="100%"
                  alt=""
                />
              </div>
              <div
                class="time-box"
                [ngStyle]="{ background: privewData.boxBackgroundColor }"
              >
                <div class="time-detail-box">
                  <span
                    style="color: #FFFFFF;"
                    [innerHTML]="lotteryResultData.prizeName.zh_CN | html"
                  ></span>
                </div>
              </div>
            </div>
          </div>
          <div class="rules-box">
            <div>
              <div class="rules-title">
                <img
                  src="assets/images/prisma/mobile-title-prize-rule.png"
                  alt=""
                />
              </div>
              <div class="rules-list">
                <div
                  [innerHTML]="privewData.cashRuleDescription.zh_CN | html"
                ></div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</ng-container>
