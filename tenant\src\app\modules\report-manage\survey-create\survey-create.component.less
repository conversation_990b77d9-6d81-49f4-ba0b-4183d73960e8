nz-input-group {
    padding: 10px 0;
}

.title {
    margin-top: 15px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
        font-size: 24px;
        font-family: PingFangSC-Light, PingFang SC;
        font-weight: 300;
        color: #17314C;
        line-height: 33px;
    }

    .lan {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #17314C;
        line-height: 20px;
        z-index: 10;
    }

}

.container {
    height: 500px;
    display: flex;
    justify-content: space-between;

    .left {
        border-right: solid 1px gray;
        width: 370px;
        padding-right: 10px;
        flex: 1;

        .tree {
            width: 100%;
            max-height: 450px;
        }

        .list {
            width: 100%;
            max-height: 490px;
        }
    }

    .right {
        width: 400px;
        height: 500px;
        margin-left: 20px;
        padding: 10px;
        background-color: #DCDCDC;

        .type {
            width: 99%;
            padding: 10px;
            background-color: white;
            border-radius: 8px;

            &:not(:last-of-type) {
                margin-bottom: 15px;
            }

            span {
                margin-bottom: 10px;
                font-size: 14px;
                display: block;
                font-weight: 500;
            }

            .items {
                display: flex;
                align-items: flex-start;
                flex-wrap: wrap;

                .label {
                    border-radius: 20px;
                    padding: 2px 12px;
                    margin-bottom: 10px;
                    margin-right: 8px;
                    white-space: pre-wrap;
                    color: #06f;
                    background: rgba(0, 102, 255, .1);
                }
            }
        }
    }

}

.footer {
    border-top: solid 1px #E6E6E6;
    padding-top: 20px;
    display: flex;
    justify-content: space-between;

    .iptBtn {
        width: 128px;
        height: 38px;
        background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
        box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        font-weight: 500;
        color: #FFFFFF;
    }

    .after {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .line1 {
            height: 17px;
            font-size: 13px;
            font-weight: 500;
            color: #495970;
            line-height: 17px;

            .total {
                margin-right: 10px;
                height: 28px;
                font-size: 20px;
                font-weight: 400;
                color: #E1251B;
                line-height: 28px;
            }
        }

        .line2 {
            height: 17px;
            font-size: 13px;
            font-weight: 500;
            color: #17314C;
            line-height: 17px;
            margin-top: 15px;
        }
    }
}

:host .container ::ng-deep .ant-checkbox-group-item {
    margin-bottom: 15px;
    display: block;
}