//主题色
@theme-color: #1890ff;

//字体超出
.vxfontEllipsis(@lineclamp:1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @lineclamp;
  -webkit-box-orient: vertical;
}

.scroll {
  .vxscrollbar();
  overflow-x: overlay;
}

//滚动条
.vxscrollbar() {
  scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
    // background-color: transparent;
    background-color: #f1f1f1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #c1c1c1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  box-shadow: none;
}
::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}


* {
  // 默认滚动调样式 ！！！
  scrollbar-color: auto;
  scrollbar-width: auto;
}
