
import { NgModule, LOCALE_ID, APP_INITIALIZER, CUSTOM_ELEMENTS_SCHEMA, forwardRef } from '@angular/core';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { default as ngLang } from '@angular/common/locales/zh';
import { NZ_I18N, zh_CN as zorroLang, NzConfig, NZ_CONFIG } from 'ng-zorro-antd';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { registerLocaleData } from '@angular/common';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

import { DELON_LOCALE, zh_CN as vxLang } from '@knz/theme';
import { KNZ_I18N_TOKEN } from '@knz/theme';
import { SimpleInterceptor } from '@knz/auth';
import { DefaultInterceptor,StartupService,I18NService } from '@core';
import { DragulaModule } from "ng2-dragula";
import { KnxJumpingModule } from '@knx/knx-ngx/jumping';

const LANG = {
  abbr: 'zh',
  ng: ngLang,
  zorro: zorroLang,
  vx: vxLang,
};

const ngZorroConfig: NzConfig = {
  // 注意组件名称没有 nz 前缀
  notification: { nzMaxStack: 1 }
};

registerLocaleData(LANG.ng, LANG.abbr);

const LANG_PROVIDES = [
  { provide: LOCALE_ID, useValue: LANG.abbr },
  { provide: NZ_I18N, useValue: LANG.zorro },
  { provide: DELON_LOCALE, useValue: LANG.vx },
];

// 加载i18n语言文件
export function I18nHttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, `${window.__MICRO_APP_PUBLIC_PATH__ || ''}assets/tmp/i18n/`, '.json');
}

const I18NSERVICE_MODULES = [
  TranslateModule.forRoot({
    loader: {
      provide: TranslateLoader,
      useFactory: I18nHttpLoaderFactory,
      deps: [HttpClient],
    },
  }),
];

const I18NSERVICE_PROVIDES = [{ provide: KNZ_I18N_TOKEN, useClass: I18NService, multi: false }];

const GLOBAL_THIRD_MODULES = [];

// #endregion
const FORM_MODULES = [];

const INTERCEPTOR_PROVIDES = [
  { provide: HTTP_INTERCEPTORS, useClass: DefaultInterceptor, multi: true },
  { provide: HTTP_INTERCEPTORS, useClass: SimpleInterceptor, multi: true },
];

export const routeIdMap = {
  'HOME': '/jump',
}

// #endregion
export function StartupServiceFactory(startupService: StartupService) {
  return () => startupService.load();
}

const APPINIT_PROVIDES = [
  StartupService,
  {
    provide: APP_INITIALIZER,
    useFactory: StartupServiceFactory,
    deps: [StartupService],
    multi: true,
  },
  { provide: NZ_CONFIG, useValue: ngZorroConfig }
];

import { VXModule } from './vx.module';
import { CoreModule } from './core/core.module';
import { SharedModule } from './shared/shared.module';
import { AppComponent } from './app.component';
import { RoutesModule } from './modules/routes.module';
import { LayoutModule } from './layout/layout.module';
import { MicroAppModule } from '@knx/micro-app-ng';

@NgModule({
  declarations: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    VXModule.forRoot(),
    CoreModule,
    SharedModule,
    LayoutModule,
    RoutesModule,
    ...I18NSERVICE_MODULES,
    ...GLOBAL_THIRD_MODULES,
    ...FORM_MODULES,
    DragulaModule.forRoot(),
    KnxJumpingModule.forRoot({ routeIdMap }),
    MicroAppModule.register({
      appName: "sag-main",
      routeMap: routeIdMap,
    }),
   
  ],
  providers: [...LANG_PROVIDES, ...INTERCEPTOR_PROVIDES, ...I18NSERVICE_PROVIDES, ...APPINIT_PROVIDES],
  bootstrap: [AppComponent],
})
export class AppModule {}
