import { Component, OnInit } from "@angular/core";
import { NzMessageService, NzModalService } from "ng-zorro-antd";
import { Router, ActivatedRoute } from "@angular/router";
import { ForgotPasswordComponent } from "../forgot-password/forgot-password.component";
import { SuccessComponent } from "../success/success.component";
import { LoginComponent } from "@src/modules/login/login/login.component";
import { HttpClient } from "@angular/common/http";

@Component({
  selector: "app-wrapper-forgot",
  templateUrl: "./wrapper-forgot.component.html",
  styleUrls: ["./wrapper-forgot.component.less"],
})
export class WrapperForgotComponent implements OnInit {
  constructor(
    private routeInfo: ActivatedRoute,
    private msg: NzMessageService,
    private modalService: NzModalService,
    private router: Router,
    private http: HttpClient,
    private message: NzMessageService
  ) {}
  tourist: boolean = true;
  isNeedLogin: boolean = false;
  ngOnInit(): void {
    this.showForgotPassword();
  }

  showForgotPassword() {
    const loginName = this.routeInfo.snapshot.queryParams["loginName"] || "";
    const modal = this.modalService.create({
      nzComponentParams: {
        loginName: loginName,
      },
      nzContent: ForgotPasswordComponent,
      nzWidth: 520,
      nzCancelText: null,
      nzOkText: null,
      nzFooter: null,
      nzCancelDisabled: true,
      nzKeyboard: false,
      nzMaskClosable: false,
      nzWrapClassName: "vertical-center-modal",
    });
    modal.afterClose.subscribe(() => {
      this.router.navigateByUrl("/user/login");
    });
  }
}
