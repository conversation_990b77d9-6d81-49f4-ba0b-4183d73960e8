.container {
  // background-color: cornflowerblue;
  // margin-top: 20px;
  width: 100%;
  height: auto;
  .icon-icon- {
    margin-left: 20px;
    cursor: pointer;
  }
  .icon-icon-:hover {
    color: orangered;
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .text {
      height: 33px;
      font-size: 24px;
      font-family: PingFangSC-Light, PingFang SC;
      font-weight: 300;
      color: #17314c;
      line-height: 33px;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .batchexport {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #409eff;
    line-height: 20px;
    text-align: end;
  }
  .tree {
    // width: 100%;
    width: 465px;
    max-height: 450px;
  }

  .body {
    min-height: 60px;
    margin-bottom: 5px;
    position: relative;

    .input {
      position: absolute;
      top: 11px;
      right: 10px;

      .search {
        width: 166px;
        height: 30px;
        background: rgba(255, 255, 255, 1);
        border-radius: 15px;
        .ant-input {
          border-radius: 15px;
          border: none;
          // border: solid 1px gainsboro;
        }
      }
    }

    .tit {
      padding-top: 15px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #17314c;
      margin-bottom: 15px;

      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.text-wrap {
  display: block;
  width: 78px;
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
}

.maxW {
  max-width: 350px;
}

.nowrap {
  white-space: nowrap;
}

button {
  padding: 0;
}

.inprogress {
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #45bfd9;
  line-height: 20px;
}

td {
  padding: 10px 16px !important;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #17314c;
}

tr {
  background-color: #fff;
}

.tab-box {
  box-sizing: border-box;
  // height: calc(100vh - 170px);
  height: calc(100vh - 150px);
  overflow-y: auto;
  .vxscrollbar();
}

.bg {
  background: #f5f6fa;
  // background: #fff;
}

.orgSearch {
  border-bottom: 1px solid #e6e6e6;
}

::ng-deep .ant-popover-buttons {
  text-align: center !important;
}

:host ::ng-deep {
  .ant-input {
    border-radius: 15px;
    border: none;
  }

  .ant-tree-title {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #17314c;
  }
}

// // 滚动条
// .treeScroll {
// overflow-y:auto;
// overflow-x:auto;
// white-space:nowrap;
// }

.top {
  display: flex;
  justify-content: space-between;
  justify-content: center;
  padding: 20px 10px 0 10px;
}
.respondent-btns {
  // padding: 5px 10px 10px 10px;
  padding: 10px;
  a {
    margin-left: 10px;
  }
  // margin-bottom: 10px;
}
.draw-button {
  display: inline-flex;
  align-items: center;
  text-align: center;
  padding: 3px 8px;
}

::ng-deep {
  .round-right-drawer9 {
    .ant-drawer-body {
      padding: 16px;
      // height: calc(100% - 108px);
      height: calc(100% - 106px);
      overflow: auto;
      // padding-bottom: 66px;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer9-nofooter {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow-y: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}
