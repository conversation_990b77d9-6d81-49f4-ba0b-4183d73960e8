import "./public-path";

import { preloaderFinished } from '@knz/theme';
import microAppRuntime from '@micro-zoe/micro-app';
import { enableProdMode, NgModuleRef, ViewEncapsulation } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { microApp } from '@knx/micro-app'
import { AppModule } from './app/app.module';
import { environment } from './environments/environment';
import { hmrBootstrap } from './hmr';
import { VUE_MICRO_APP_NAME, VUE_MICRO_APP_URL } from "@src/core/sub-micro-app";

preloaderFinished();

if (environment.production) {
  enableProdMode();
}

let app: void | NgModuleRef<AppModule>;

const mount = async () => {
  if (!microApp.isMicroAppEnv) {
    microAppRuntime.start({
      fiber: true,
      iframe: true,
      prefetchLevel: 2,
      'disable-patch-request': true,
      'disable-memory-router': true,
    });
    microAppRuntime.preFetch([
      {
        name: VUE_MICRO_APP_NAME,
        url: VUE_MICRO_APP_URL
      }
    ]);

    if (process.env.NODE_ENV === 'production') {
      let observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          mutation.addedNodes.forEach((addedNode) => {
            if (addedNode instanceof HTMLIFrameElement && addedNode.id === VUE_MICRO_APP_NAME) {
              addedNode.src = addedNode.src.replace(/\/$/, '') + '/main/'
              observer.disconnect();
              observer = null;
            }
          })
        }
      });
      observer.observe(document.body, { childList: true })
    }
  }

  const root = document.querySelector('app-root');
  if (!root) {
    const rootEl = document.createElement('app-root');
    document.body.insertBefore(rootEl, document.body.firstChild)
  }

  app = await platformBrowserDynamic().bootstrapModule(AppModule, {
    defaultEncapsulation: ViewEncapsulation.Emulated,
    preserveWhitespaces: false,
  })

  if ((window as any).appBootstrap) {
    (window as any).appBootstrap()
  }

  return app;
};

const unmount = () => {
  app && app.destroy();
  app = null;
  const root = document.querySelector('app-root');
  root && (root.innerHTML = '')
};

if (window["__MICRO_APP_ENVIRONMENT__"]) {
  window[`micro-app-${window["__MICRO_APP_NAME__"]}`] = { mount, unmount };
} else {
  if (environment.hmr) {
    // tslint:disable-next-line: no-string-literal
    if (module["hot"]) {
      hmrBootstrap(module, mount);
    } else {
      console.error("HMR is not enabled for webpack-dev-server!");
      console.log("Are you using the --hmr flag for ng serve?");
    }
  } else {
    mount();
  }
}
