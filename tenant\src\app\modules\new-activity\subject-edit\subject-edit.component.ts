import {
  Component,
  Input,
  OnInit,
  ViewChild,
  Output,
  EventEmitter,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { NzMessageService, NzModalRef, NzModalService } from "ng-zorro-antd";
import { NameDescEditComponent } from "../name-desc-edit/name-desc-edit.component";
import { NameEditComponent } from "../name-edit/name-edit.component";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-subject-edit",
  templateUrl: "./subject-edit.component.html",
  styleUrls: ["./subject-edit.component.less"],
})
export class SubjectEditComponent implements OnInit {
  @Output() private pre = new EventEmitter<string>();
  @Output() private next = new EventEmitter<string>();
  @Input() subjectModel: any;
  @Input() isBatchMode: boolean;
  name: any = {
    // zh_CN: `水1-sdfsd收费是 , 水2-dsds 收费是,dsfsfsf 11 dfs,last sss dfsdf`,
    // zh_CN: `水1-{{电费:水费,税费}}收费是 , 水2-{{水费:电费,水费,ttt}}收费是,dsfsfsf 11 {{ss:ss,dd,tt}},last sss {{test1:test1,test2,test3}}`,
    zh_CN: `<p><span style="color: #121212; font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', 'Source Han Sans SC', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif; font-size: medium;">输入的Observable可被同时订阅的最大数量，可以用它来控制网络请求的并发，如：控制同时上传的图片数量，就可以通过设置此参数来完美解决，</span><span style="color: rgba(0, 0, 0, 0.65); font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';">哈哈{{我:我,你,他}}，哈哈{{我:我,你,他组织}}哈看看</span></p>`,
    en_US: `he code {{you:they,you,me,others}} write isn immediately executable. For example, components have templates that contain custom elements, 
    attribute directives, Angular binding declarations, and {{dragon:some,any,all,every}} peculiar syntax that clearly is native HTML.{{oi:oi,fine,test,sksksl}}`,
  };

  checked: boolean = false;
  @ViewChild("zh_CN", { static: true }) zh_CN: NameEditComponent;
  @ViewChild("en_US", { static: true }) en_US: NameEditComponent;

  @ViewChild("desc", { static: true }) descCom: NameDescEditComponent;

  paramJson: any = {};

  demoTabs = [
    { id: 1, name: "称谓" },
    { id: 2, name: "描述" },
  ];
  indexNum: number = 0;
  position = "top";

  canEdit: boolean = false;
  lans: any[] = [];

  disabled: boolean = false;

  addOptionObj = {
    // 多选题选项
    id: 1,
    name: {
      zh_CN: "",
      en_US: "",
    },
  };

  constructor(
    private modalService: NzModalService,
    private routeInfo: ActivatedRoute,
    private modalRef: NzModalRef,
    private msg: NzMessageService,
    private projSerivce: ProjectManageService,
    private surveySerivce: SurveyApiService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    if (this.subjectModel && this.subjectModel.name) {
      this.name = this.subjectModel.name;
    }
    this.editData();
  }

  ok() {
    if (this.canEdit === false) {
      if (this.checkAndTrans()) {
        this.modalRef.triggerOk();
      }
    } else {
      if (this.checkByDesc()) {
        this.modalRef.triggerOk();
      }
    }
  }

  checkByDesc(): boolean {
    let lans: any[] = this.lans;
    let strCn: string;
    let strEn: string;
    if (this.canEdit === true) {
      strCn = lans[0].after;
      strEn = lans[1].after;
    } else {
      strCn = this.subjectModel.replaceName.zh_CN;
      strEn = this.subjectModel.replaceName.zh_CN;
    }

    if (strCn.trim() === "") {
      // this.msg.error("中文题干不能为空");
      this.customMsg.open("error", "中文题干不能为空");
      return false;
    }
    let surveyQuestion: any;
    if (this.subjectModel.type === "MULTIPLE_CHOICE_ESSAY_QUESTION") {
      surveyQuestion = {
        id: this.subjectModel.id,
        name: {
          zh_CN: strCn,
          en_US: strEn,
        },
        options: this.subjectModel.options,
        questionnaireId: this.subjectModel.questionnaireId,
      };
    } else {
      surveyQuestion = {
        id: this.subjectModel.id,
        name: {
          zh_CN: strCn,
          en_US: strEn,
        },
        questionnaireId: this.subjectModel.questionnaireId,
      };
    }

    this.paramJson = {
      type: 2,
      surveyQuestion,
      replace: [],
      isReplaceAll: false,
    };

    return true;
  }

  checkAndTrans(): boolean {
    if (this.canEdit === true) {
      return this.checkByDesc();
    }

    if (
      this.canNotSave(this.zh_CN.selectionList, "zh_CN") ||
      this.canNotSave(this.en_US.selectionList, "en_US")
    ) {
      return false;
    }

    // prepare param
    let surveyQuestion: any = {
      id: this.subjectModel.id,
      name: {
        zh_CN: this.zh_CN.getSaveName(),
        en_US: this.en_US.getSaveName(),
      },
      questionnaireId: this.subjectModel.questionnaireId,
    };

    let zhArr = this.transParams(this.zh_CN.selectionList, "zh_CN");
    let enArr = this.transParams(this.en_US.selectionList, "en_US");
    let replace = [...zhArr, ...enArr];

    let isReplaceAll = this.checked;

    this.paramJson = {
      surveyQuestion,
      replace,
      isReplaceAll,
      type: 1,
    };
    return true;
  }

  transParams(selectionList: any[], lan: string): any[] {
    let tmps: any[] = [];
    for (let index = 0; index < selectionList.length; index++) {
      const element = selectionList[index];
      let val = element.value;
      tmps.push({ [lan]: `{{${val.newValue}:${val.listString}}}` });
    }
    return tmps;
  }

  canNotSave(list: any[], lan?: string): boolean {
    for (let index = 0; index < list.length; index++) {
      const element = list[index];
      if (!element.value.newValue) {
        // this.msg.error(`调整后的值不能为空, ${lan}, 位置: ${element.key}`);
        // this.msg.error(`调整后的值不能为空。`);
        this.customMsg.open("error", "调整后的值不能为空。");
        return true;
      }
    }
    return false;
  }

  editData() {
    this.lans = [
      { lan: "zh_CN", name: "中文", origin: "", after: "" },
      { lan: "en_US", name: "ENG", origin: "", after: "" },
    ];

    for (let index = 0; index < this.lans.length; index++) {
      const element = this.lans[index];
      element.origin = this.subjectModel.replaceName[element.lan];
      element.after = this.subjectModel.replaceName[element.lan];
    }
  }

  edit() {
    this.zh_CN.changeDisabled();
    this.en_US.changeDisabled();
    this.disabled = true;
    this.checked = false;
    this.editData();
    this.canEdit = true;
  }

  childPre() {
    this.editData();
    this.canEdit = false;
    this.pre.emit();
  }
  childNext() {
    this.editData();
    this.canEdit = false;
    this.next.emit();
  }

  addOptions() {
    if (this.subjectModel.options.options.length >= 10) {
      // return this.msg.error("选项最多为10个");
      this.customMsg.open("error", "选项最多为10个");
      return;
    } else {
      let id = this.recursionId(1);
      this.addOptionObj.id = id;
      this.subjectModel.options.options.unshift(this.addOptionObj);
      // addOptionObj
    }
  }

  deleteOption(idx) {
    // 删除未保存的新增题目
    if (this.subjectModel.options.options.length <= 1) {
      // return this.msg.warning("多选开放题最少一个选项");
      return this.customMsg.open("warning", "多选开放题最少一个选项");
    }
    this.subjectModel.options.options.splice(idx, 1);
  }

  recursionId(id) {
    let idx = this.subjectModel.options.options.findIndex(
      (item) => item.id === id
    );
    if (idx !== -1) {
      id = id + 1;
      this.recursionId(id);
    } else {
      return id;
    }
  }
}
