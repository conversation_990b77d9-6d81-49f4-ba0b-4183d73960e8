import { Component, Input, OnInit } from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { NzModalRef, zh_CN, en_US } from "ng-zorro-antd";
import { notBlank } from "../validate-notblank";
import { OrgService } from "../org.service";
import { ActivatedRoute } from "@angular/router";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-org-edit",
  templateUrl: "./org-edit.component.html",
  styleUrls: ["./org-edit.component.less"],
})
export class OrgEditComponent implements OnInit {
  @Input() id;
  @Input() orgModel: any;
  @Input() parentList: any[];
  @Input() typeshow;
  @Input() fatherCode: string;
  @Input() i18n: any[];

  validateForm: FormGroup;
  isLoading: boolean = false;
  isUpdate: boolean = false;
  isVirtual: boolean = false;
  loading: boolean = false;
  permission: boolean = false;
  reportType = "";
  constructor(
    private fb: FormBuilder,
    private modalRef: NzModalRef,
    private orgSerivce: OrgService,
    private routeInfo: ActivatedRoute,
    public permissionService: PermissionService
  ) {}

  change(e) {}

  ngOnInit() {
    console.log("===============>", this.i18n);
    this.i18n.sort((a, b) => {
      if (a.value === "zh_CN") return -1;
      if (b.value === "zh_CN") return 1;
      if (a.value === "en_US") return -1;
      if (b.value === "en_US") return 1;
      if (a.value === "jp") return -1;
      if (b.value === "jp") return 1;
      if (a.value === "ko") return -1;
      if (b.value === "ko") return 1;
      if (a.value === "cs_1") return -1;
      if (b.value === "cs_1") return 1;
      if (a.value === "cs_2") return -1;
      if (b.value === "cs_2") return 1;
      if (a.value === "cs_3") return -1;
      if (b.value === "cs_3") return 1;
      return 0;
    });
    this.permission = this.permissionService.isPermission();
    this.reportType = this.routeInfo.snapshot.queryParams.reportType;
    console.log(this.routeInfo.snapshot.queryParams.reportType);

    const i18nFormObj = {};
    const i18nNameObj = {};
    this.i18n.forEach((e) => {
      if (e.value !== "zh_CN") {
        i18nFormObj[e.value] = [null, []];
      }
      i18nNameObj[e.value] = "";
    });
    this.validateForm = this.fb.group({
      zh_CN: [null, [Validators.required, notBlank]],
      // en_US: [null, []],
      ...i18nFormObj,
      code: [null, [Validators.required, notBlank]],
      description: [null, []],
      statusNew: [null, []],
    });
    console.log("this.validateForm", this.validateForm);
    if (!this.id || this.id === "0") {
      this.orgModel = {
        id: "0",
        name: {
          // zh_CN:"",
          // en_US:""
          ...i18nNameObj,
        },
        code: "",
        description: "",
        parentCode: this.fatherCode ? this.fatherCode : undefined,
        isUpdateParentOrganization: true,
        isVirtual: false,
        statusNew: true,
      };
      this.validateForm.addControl(
        "parentCode",
        new FormControl("", Validators.required)
      );
    } else {
      console.log(this.orgModel);

      this.isUpdate = true;
      this.validateForm.addControl("parentCode", new FormControl(""));
    }
  }

  ok() {
    this.modalRef.triggerOk();
  }

  submitForm(): void {
    for (const i in this.validateForm.controls) {
      this.validateForm.controls[i].markAsDirty();
      this.validateForm.controls[i].updateValueAndValidity();
    }
  }

  //禁用组织
  clickIsVisitAnswer(e, index) {
    //如果是false 则提示用户是否禁用
    if (!this.orgModel.statusNew) {
      e.stopPropagation();
      this.loading = true;
      this.orgSerivce.enableOrganization(this.id).subscribe((res) => {
        if (res.result.code === 0) {
          this.orgModel.statusNew = true;
        }
        this.loading = false;
      });
    } else {
      // this.validateForm.Parent[k].statusNew = false
    }
  }
  confirmSwitch(): void {
    this.loading = true;
    this.orgSerivce.disableOrganization(this.id).subscribe((res) => {
      if (res.result.code === 0) {
        this.orgModel.statusNew = false;
      }
      this.loading = false;
    });
  }
}
