import { HttpClient } from "@angular/common/http";
import {
  ChangeDetectorRef,
  Component,
  Inject,
  Input,
  OnInit,
  ViewChild,
  OnDestroy,
} from "@angular/core";
import { Observable } from "rxjs";
import _ from "lodash";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import {
  NzFormatEmitEvent,
  NzModalRef,
  NzTreeComponent,
  NzTreeNode,
  NzMessageService,
} from "ng-zorro-antd";
import { ProjectManageService } from "../../service/project-manage.service";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { NzDrawerRef } from "ng-zorro-antd";
import { PermissionService } from "@src/modules/service/permission-service.service";
@Component({
  selector: "app-person-list",
  templateUrl: "./person-list.component.html",
  styleUrls: ["./person-list.component.less"],
})
export class PersonListComponent implements OnInit, On<PERSON><PERSON>roy {
  dataSet = [];
  searchName: string = "";
  tenantUrl: string = "/tenant-api";

  @Input() projectId: string;
  @Input() orgId: string;
  @Input() orgName: string;

  totalCount: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  public chartOption: any;
  // containerWidth = '500'
  containerWidth = "465";
  containerHeight = "350";
  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;

  permission: boolean = false;

  checked: boolean = false;

  defaultExpandedKeys: string[] = [];
  private routerSubscription: Subscription;

  constructor(
    private http: HttpClient,
    private surveySerivce: SurveyApiService,
    private cdf: ChangeDetectorRef,
    private msgServ: NzMessageService,
    private api: ProjectManageService,
    private drawerRef: NzDrawerRef,
    private router: Router,
        public permissionService: PermissionService,
  ) {}

  ngOnInit() {
    this.permission = this.permissionService.isPermission();

    this.initChart();
    this.loadOrgs();
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  clearHistory(personId) {
    // 清除填答
    let params: any = {
      personId: personId,
      projectId: this.projectId,
    };
    this.api.clearAnswerData(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.msgServ.success("操作成功");
        this.loadOrgs();
      }
    });
  }

  getSource(): any[] {
    let tmp = [];
    for (let index = 0; index < this.orgPersonList.length; index++) {
      const element = this.orgPersonList[index];
      tmp.push([element.completedCount, element.name.zh_CN]);
    }
    return tmp;
  }

  initChart() {
    let showOrgNum: number = 10;
    let isShowZoom = Object.keys(this.selectedNodes).length > showOrgNum;

    let percent = 100;
    let orgs: number = Object.keys(this.selectedNodes).length;
    if (orgs <= 5) {
      percent = 100;
    } else if (orgs > 5 && orgs <= 10) {
      percent = 50;
    } else if (orgs > 10 && orgs <= 20) {
      percent = 30;
    } else {
      percent = 10;
    }

    this.chartOption = {
      title: {
        text: "完成进度",
        x: "center",
        textStyle: {
          color: "#17314C",
          fontStyle: "normal",
          fontSize: 16,
        },
      },
      tooltip: {
        show: true,
      },
      grid: {
        left: "40", // grid 组件离容器左侧的距离
      },
      dataset: {
        dimensions: ["persons", "org"],
        source: this.getSource(),
      },

      dataZoom: [
        {
          // rangeMode: 'value',
          type: "inside",
          start: 0,
          end: percent,
        },
        {
          start: 0,
          end: 10,
        },
      ],

      // dataZoom: [
      //   {
      //       type:'slider',
      //       show: isShowZoom,
      //       startValue: 0,
      //       endValue: showOrgNum - 1,
      //       orient: 'vertical',
      //       showDetail: false,
      //       showDataShadow: false,
      //       zoomLock: true,
      //       backgroundColor: '#EDF0F7',
      //       fillerColor: '#25a4fb',
      //       handleSize: '0',
      //       x2: 20,
      //       width: 6
      //   }
      // ],

      xAxis: {
        type: "category",
        axisLabel: {
          interval: 0,
          formatter: function(value) {
            let retVal: string = value;
            let label: string = value;
            if (label.length > 5) {
              retVal = label.slice(0, 5) + "...";
            }
            return retVal;
            // return value.split("").join("\n");
          },
        },
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          type: "bar",
          // barWidth: 16,
          encode: {
            // 将 "score" 列映射到 X 轴。
            // 将 "product" 列映射到 Y 轴。
            y: "persons",
            x: "org",
          },
          itemStyle: {
            normal: {
              color: function(params) {
                var colorList = ["#60CB7F"];
                return colorList[0];
              },
              label: {
                show: true, // 开启显示
                // position: 'insideRight',
                position: "top", // 在内部右方显示
                color: "black",
                fontSize: 12,
              },
            },
          },
        },
      ],
    };
  }

  nzEvent(event: NzFormatEmitEvent): void {
    if (event.node.isDisabled) {
      return;
    }

    let currentNode: any = event.node.origin;
    let isSelected: boolean = currentNode.checked;
    let id = currentNode.key;
    let name = currentNode.title;
    if (isSelected) {
      this.selectedNodes[id] = { id: id, name: name };
    } else {
      delete this.selectedNodes[id];
    }

    this.currentPage = 1;
    this.totalCount = 1;
    this.searchName = "";

    this.loadOrgPersons();
    this.loadPersonData();
  }

  // 全选
  selectAll(isSelected: boolean, isAutoSelect: boolean): void {
    let allNodes: NzTreeNode[] = this.nzTreeComponent.getTreeNodes();
    allNodes.forEach((n: NzTreeNode) => {
      this.setSelectState(n, isSelected, isAutoSelect);
    });

    // update displaying selected nodes
    let nodeList: NzTreeNode[] = this.nzTreeComponent.getCheckedNodeList();
    this.selectedNodes = {};
    if (isSelected) {
      for (let index = 0; index < nodeList.length; index++) {
        const element = nodeList[index].origin;
        this.selectedNodes[element.key] = {
          id: element.key,
          name: element.title,
        };
      }
    }

    this.currentPage = 1;
    this.totalCount = 1;
    this.searchName = "";

    this.loadOrgPersons();
    this.loadPersonData();
  }

  setSelectState(node: NzTreeNode, isSelected: boolean, isAutoSelect: boolean) {
    if (isSelected && isAutoSelect) {
      let tmpSelect = node.level === 1;
      node.setChecked(tmpSelect);
    } else {
      node.setChecked(isSelected);
    }

    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        this.setSelectState(child, isSelected, isAutoSelect);
      }
    }
  }

  orgList: any[] = [];
  searchValue = "";
  selectedNodes: any = {};

  chartLoaded = false;
  orgPersonList: any[] = [];

  loadOrgs() {
    this.getOrgsbyParentId().subscribe((res) => {
      if (res.result.code === 0) {
        this.orgList = res.data;
        if (this.orgList.length > 0) {
          this.defaultExpandedKeys.push(this.orgList[0].key);
        }

        // this.loadOrgPersons();

        let that = this;
        setTimeout(() => {
          // that.checked = true;
          that.selectAll(true, true);
        }, 500);
      }
    });
  }

  loadOrgPersons() {
    this.chartLoaded = false;
    this.getFinishedOrgCount().subscribe((res) => {
      this.chartLoaded = true;
      if (res.result.code === 0) {
        this.orgPersonList = res.data;
        this.initChart();
      }
    });
  }

  public getOrgsbyParentId(): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listChildOrganizationsByTree/${this.projectId}/${this.orgId}`;
    return this.http.get(url);
  }

  public getFinishedOrgCount(): Observable<any> {
    let param = {
      organizationIds: Object.keys(this.selectedNodes),
      projectId: this.projectId,
    };
    const url = `${this.tenantUrl}/survey/project/queryOrganizationCompleteInfo`;
    return this.http.post(url, param);
  }

  searchPerson() {
    this.currentPage = 1;
    this.totalCount = 1;
    this.loadPersonData();
  }

  loadPersonData() {
    // not admin, ignore query
    if (!this.permission) {
      return;
    }

    this.dataSet = [];
    let param = {
      organizationIds: Object.keys(this.selectedNodes),
      page: {
        current: this.currentPage,
        size: this.pageSize,
      },
      projectId: this.projectId,
      name: this.searchName,
    };
    // this.surveySerivce.getOrganizationPersonByPage(param).
    const url = `${this.tenantUrl}/survey/person/listPersonByOrgs`;
    this.http.post(url, param).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.totalCount = res.page.total;
        this.dataSet = res.data;
      }
    });
  }

  deletePerson(id: string) {
    this.surveySerivce.deletePerson(id).subscribe((res) => {
      if (res.result.code == 0) {
        this.loadOrgPersons();
        this.loadPersonData();
      }
    });
  }
}
