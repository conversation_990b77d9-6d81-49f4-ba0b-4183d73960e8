import { Component, OnInit, ChangeDetectorRef } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { NzMessageService, NzModalService } from "ng-zorro-antd";
import { Observable } from "rxjs";
import { ProjectManageService } from "../../service/project-manage.service";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { DatePipe } from "@angular/common";

interface ItemData {
  id: number;
}
@Component({
  selector: "app-sendmsg",
  templateUrl: "./sendmsg.component.html",
  styleUrls: ["./sendmsg.component.less"],
  providers: [DatePipe],
})
export class SendmsgComponent implements OnInit {
  checked = false;
  indeterminate = false;
  listOfCurrentPageData: readonly ItemData[] = [];
  listOfData: readonly ItemData[] = [];
  setOfCheckedId = new Set<number>();
  showlist = false;
  dateRange = []; // 活动周期
  senddata = {
    endTime: "",
    startTime: "",
  };
  personName: "";
  projectName: "";
  projectCode: "";
  messageType = [];
  messagePurpose: "";
  messageStatuss: any = [];
  PageIndex = 1;
  listTotal = 0;
  PageSize = 10;
  checkOptionsOne = [
    { label: "发送成功", value: "SENT", checked: false },
    { label: "发送中", value: "SENDING", checked: false },
    { label: "发送失败", value: "FAIL", checked: false },
  ];

  listOfOption = [
    {
      value: "SMS",
      label: "短信",
    },
    {
      value: "MAIL",
      label: "邮件",
    },
    {
      value: "ENTERPRISE_WECHAT",
      label: "企业微信",
    },
    {
      value: "DING_DING",
      label: "钉钉",
    },
    {
      value: "THIRD_PARTY_LARK_TEXT_CARD",
      label: "飞书",
    },
  ];
  tenantUrl: string = "/tenant-api";

  constructor(
    private http: HttpClient,
    private api: ProjectManageService,
    private cd: ChangeDetectorRef,
    private msg: NzMessageService,
    private modal: NzModalService,
    private customMsg: MessageService,
    private datePipe: DatePipe
  ) {}

  ngOnInit() {
    this.getlistAllPage();
  }

  getlistAllPage() {
    this.getList().subscribe((item: any) => {
      // console.log(item);
      if (item.result.code == 0) {
        this.listOfData = item.data;
        // console.log(this.listOfData);
        this.showlist = true;
        this.PageIndex = item.page.current;
        this.listTotal = item.page.total;
        this.cd.detectChanges();
      }
    });
  }
  nzPageIndexChange() {
    this.getlistAllPage();
  }
  nzPageSizeChange() {
    this.getlistAllPage();
  }

  public getList(): Observable<any> {
    let api = this.tenantUrl + "/survey/messageLog/listByPage";
    let param = {
      messagePurpose: this.messagePurpose, //消息类型
      messageStatuss: this.messageStatuss, //状态
      messageTypes: this.messageType, //发送方式
      page: {
        current: this.PageIndex,
        size: this.PageSize,
      },
      personName: this.personName,
      projectName: this.projectName,
      projectCode: this.projectCode,
      // sendDateBegin: this.senddata.startTime,
      // sendDateEnd: this.senddata.endTime,
      sendDateBeginFormat: this.senddata.startTime || null,
      sendDateEndFormat: this.senddata.endTime || null,
    };
    return this.http.post(api, param);
  }

  updateCheckedSet(id: number, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onItemChecked(id: number, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }
  public getALLlistid(): Observable<any> {
    let api = this.tenantUrl + "/survey/messageLog/selectIds";
    let param = {
      messagePurpose: this.messagePurpose, //消息类型
      messageStatuss: this.messageStatuss, //状态
      messageTypes: this.messageType, //发送方式
      personName: this.personName,
      projectName: this.projectName,
      projectCode: this.projectCode,
      // sendDateBegin: this.senddata.startTime,
      // sendDateEnd: this.senddata.endTime,
      sendDateBeginFormat: this.senddata.startTime || null,
      sendDateEndFormat: this.senddata.endTime || null,
    };
    return this.http.post(api, param);
  }

  onAllChecked(value: boolean): void {
    // console.log(this.listOfCurrentPageData);

    this.getALLlistid().subscribe((item: any) => {
      if (item.result.code == 0) {
        // console.log(item);
        let listids = [];
        item.data.forEach((val) => {
          listids.push({
            id: val,
          });
        });
        this.listOfCurrentPageData = listids;
        this.listOfCurrentPageData.forEach((item) =>
          this.updateCheckedSet(item.id, value)
        );
        this.refreshCheckedStatus();
      }
    });
  }

  onCurrentPageDataChange($event: readonly ItemData[]): void {
    this.listOfCurrentPageData = $event;
    // console.log($event)
    if (this.listOfCurrentPageData.length != 0) {
      this.refreshCheckedStatus();
    }
  }

  refreshCheckedStatus(): void {
    this.checked = this.listOfCurrentPageData.every((item) =>
      this.setOfCheckedId.has(item.id)
    );

    this.indeterminate =
      this.listOfCurrentPageData.some((item) =>
        this.setOfCheckedId.has(item.id)
      ) && !this.checked;
  }

  updateSingleChecked() {
    this.messageStatuss = this.checkOptionsOne
      .map((item) => {
        if (item.checked) {
          return item.value;
        }
      })
      .filter((res) => {
        return res;
      });
    this.getlistAllPage();
  }

  clearlistMap() {
    this.dateRange = []; // 活动周期
    this.senddata = {
      endTime: "",
      startTime: "",
    };
    this.personName = "";
    this.projectName = "";
    this.projectCode = "";
    this.messageType = [];
    this.messagePurpose = null;
    this.messageStatuss = [];
    this.PageIndex = 1;
    this.checkOptionsOne.forEach((item) => {
      item.checked = false;
    });
    this.getlistAllPage();
  }

  searchlistMap() {
    this.PageIndex = 1;
    this.getlistAllPage();
    // todo 当全选勾选时进行查询 更新当前选中
    this.onAllChecked(false);
    this.setOfCheckedId.clear();
  }

  onChange(result: Date): void {
    if (result[0]) {
      if (!this.senddata.startTime) {
        this.dateRange[0].setHours(0);
        this.dateRange[0].setMinutes(0);
        this.dateRange[0].setSeconds(0);
      } else {
        const startTime_ = new Date(this.senddata.startTime);
        if (
          startTime_.getHours() !== this.dateRange[0].getHours() &&
          startTime_.getMinutes() !== this.dateRange[0].getMinutes()
        ) {
          this.dateRange[0].setHours(startTime_.getHours());
          this.dateRange[0].setMinutes(startTime_.getMinutes());
          this.dateRange[0].setSeconds(startTime_.getSeconds());
        }
      }
      this.senddata.startTime = `${this.formatDate(
        this.dateRange[0],
        "date"
      )} ${this.formatDate(this.dateRange[0], "time")}`;
    } else {
      this.senddata.startTime = null;
    }

    if (result[1]) {
      if (!this.senddata.endTime) {
        this.dateRange[1].setHours(23);
        this.dateRange[1].setMinutes(59);
        this.dateRange[1].setSeconds(59);
      } else {
        const endTime_ = new Date(this.senddata.endTime);
        if (
          endTime_.getHours() !== this.dateRange[1].getHours() &&
          endTime_.getMinutes() !== this.dateRange[1].getMinutes()
        ) {
          this.dateRange[1].setHours(endTime_.getHours());
          this.dateRange[1].setMinutes(endTime_.getMinutes());
          this.dateRange[1].setSeconds(endTime_.getSeconds());
        }
      }
      this.senddata.endTime = `${this.formatDate(
        this.dateRange[1],
        "date"
      )} ${this.formatDate(this.dateRange[1], "time")}`;
    } else {
      this.senddata.endTime = null;
    }
  } //日期选中回调

  formatDate(shijianchuo, type) {
    const time = new Date(shijianchuo); // 需要使用Date格式进行日期转化，若是时间戳、字符串时间，需要通过new Date(..)转化

    const y = time.getFullYear();

    const m = time.getMonth() + 1;

    const d = time.getDate();

    const h = time.getHours();

    const mm = time.getMinutes();

    const s = time.getSeconds();
    if (type === "date") {
      return y + "-" + this.isZero(m) + "-" + this.isZero(d);
    } else {
      return this.isZero(h) + ":" + this.isZero(mm) + ":" + this.isZero(s);
    }
  } //日期格式化
  isZero(m) {
    return m < 10 ? "0" + m : m;
  } //时间处理

  exportList() {
    let ids = [];
    this.setOfCheckedId.forEach((item) => {
      ids.push(item);
    });
    let parmas = {
      ids: ids,
    };

    if (ids.length == 0) {
      // this.msg.error("请至少选择一条数据导出")
      this.customMsg.open("error", "请至少选择一条数据导出");
    } else {
      this.api.exportMessageLog(parmas).subscribe((res) => {
        const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
        let fileName = res.headers
          .get("Content-Disposition")
          .split(";")[1]
          .split("filename=")[1];
        const fileNameUnicode = res.headers
          .get("Content-Disposition")
          .split("filename*=")[1];
        // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
        if (fileName) {
          fileName = decodeURIComponent(fileName);
        }
        if (fileNameUnicode) {
          fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
        }
        const link = document.createElement("a");
        link.setAttribute("href", URL.createObjectURL(blob));
        link.setAttribute("download", fileName);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
    }
  }
  // 重新发送 二次确认
  handReturnSend() {
    if (this.setOfCheckedId.size === 0) {
      // this.msg.error("请至少选择一条数据重新发送")
      this.customMsg.open("error", "请至少选择一条数据重新发送");
      return;
    }
    this.modal.confirm({
      nzTitle: `选中记录${this.setOfCheckedId.size}条，确认重新发送吗？`,
      nzContent: "<b>请注意：重新发送将再次计费</b>",
      nzOkText: "确认",
      nzOnOk: () => this.ReturnSend(),
      nzCancelText: "取消",
    });
  }
  // 重新发送
  ReturnSend() {
    this.Sendlist().subscribe((item: any) => {
      if (item.result.code == 0) {
        this.msg.success("发送成功");
        this.setOfCheckedId.clear();
        this.checked = false;
        this.indeterminate = false;
      } else {
        // this.msg.error(item.result.message)
      }
    });
  }
  Sendlist() {
    let api = this.tenantUrl + "/survey/messageLog/sendAgain";
    let ids = [];
    this.setOfCheckedId.forEach((item) => {
      ids.push(item);
    });
    let param = {
      ids: ids,
    };
    return this.http.post(api, param);
  }
  // /survey/messageLog/export
}
