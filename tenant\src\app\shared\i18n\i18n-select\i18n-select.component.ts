/**
 *
 *  @author: <PERSON>
 *  @Date: 2023/09/18
 *  @content: i18n tab切换组件
 *
 */
import { 
  Component, 
  OnInit, 
  Input, 
  Output, 
  EventEmitter 
} from "@angular/core";
import { NewPrismaService } from "@src/modules/new-prisma/new-prisma.service";
import _ from "lodash";
interface I18nOptions {
  name: string;
  value: string;
  simplifyName?: string; // 简称
}

@Component({
  selector: "app-i18n-select",
  templateUrl: "./i18n-select.component.html",
  styleUrls: ["./i18n-select.component.less"],
})
export class I18nSelectComponent implements OnInit {
  @Input() active: string;
  @Input() isDefault?: boolean = false; // 使用默认中英文
  @Output() selectChange = new EventEmitter<any>();

  i18n: I18nOptions[] = [];

  constructor(private api: NewPrismaService) {}

  ngOnInit() {
    if (!this.isDefault) {
      this.getLanOptions();
    } else {
      this.i18n = [
        {
          name: "中文",
          value: "zh_CN",
        },
        {
          name: "美国英语",
          value: "en_US",
        },
      ];
    }
  }

  /**
   * getLanOptions 获取语言配置
   */
  async getLanOptions() {
    // 当前所有语言
    const currentLansRes = await this.api.getLanguages().toPromise();
    // 默认-语言
    const defaultCode = ["zh_CN", "en_US"];
    // 从sessionStorage中获取当前活动语言
    const projectLanguages =
      JSON.parse(sessionStorage.getItem("projectLanguages")) || defaultCode;
    // 语言过滤排序
    this.i18n = currentLansRes.data
      .filter((val) => projectLanguages.includes(val.value))
      .sort((a, b) => {
        if (a.value === "zh_CN") return -1;
        if (b.value === "zh_CN") return 1;
        if (a.value === "en_US") return -1;
        if (b.value === "en_US") return 1;
        if (a.value === "jp") return -1;
        if (b.value === "jp") return 1;
        if (a.value === "ko") return -1;
        if (b.value === "ko") return 1;
        if (a.value === "cs_1") return -1;
        if (b.value === "cs_1") return 1;
        if (a.value === "cs_2") return -1;
        if (b.value === "cs_2") return 1;
        if (a.value === "cs_3") return -1;
        if (b.value === "cs_3") return 1;
        return 0;
      });
  }

  /**
   * saveLan 语言切换
   * @param {string} val 选中的语言
   */
  handTab(val: string) {
    this.selectChange.emit(val);
  }
}
