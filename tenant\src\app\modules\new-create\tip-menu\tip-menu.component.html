<div class="tipMenu">
  <ng-container *ngFor="let first of data">
    <div
      class="tipMenu_item"
      [ngClass]="{ active: first.reportType == selectReportType }"
      (click)="onCheck(first)"
    >
      <div (click)="onCollapsed()">
        <span [nzTooltipTitle]="first.name.zh_CN" nz-tooltip>{{
          first.name.zh_CN
        }}</span>
        <div class="tipMenu_item_icon" *ngIf="!!first.child">
          <i nz-icon nzType="down" nzTheme="outline" *ngIf="isOpen"></i>
          <i nz-icon nzType="up" nzTheme="outline" *ngIf="!isOpen"></i>
        </div>
      </div>
    </div>
    <ng-container *ngIf="isOpen">
      <div
        class="tipMenu_item child"
        [ngClass]="{ active: second.reportType == selectReportType }"
        *ngFor="let second of first.child"
        (click)="onCheck(second)"
      >
        <span [nzTooltipTitle]="second.name.zh_CN" nz-tooltip>{{
          second.name.zh_CN
        }}</span>
      </div>
    </ng-container>
  </ng-container>
</div>
