<div class="container content client-width">
  <div
    style="display: flex;justify-content: space-between;align-items: center;"
  >
    <h1>
      活动管理<img
        style="cursor: pointer;margin-left: 8px;"
        src="assets/images/shownew.png"
        (click)="getnewlead()"
        alt=""
      />
    </h1>
    <div>
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </div>
  </div>
  <header>
    <p><a href="javascript:void(0)" (click)="clearParams()">清空条件</a></p>
    <ul class="search-box">
      <li class="new-event" (click)="newAddProject()">
        <img
          src="assets/images/event-management/home/<USER>"
          alt=""
          style="margin-right: 8px;"
        />新建活动
      </li>
      <li>
        <nz-input-group [nzSuffix]="suffixIconSearch">
          <input
            style="width: 157px; padding-left: 10px;"
            [(ngModel)]="name"
            (keydown.enter)="search()"
            type="text"
            nz-input
            placeholder="请输入关键词"
          />
        </nz-input-group>
        <ng-template #suffixIconSearch>
          <i nz-icon nzType="search"></i>
        </ng-template>
      </li>
      <li>
        <nz-select
          [(ngModel)]="status"
          nzAllowClear
          nzPlaceHolder="状态"
          (ngModelChange)="search()"
        >
          <nz-option nzValue="ANNOUNCED" nzLabel="未发布"></nz-option>
          <nz-option nzValue="PREVIEW" nzLabel="预发布"></nz-option>
          <nz-option nzValue="ANSWERING" nzLabel="进行中"></nz-option>
          <nz-option nzValue="OVER" nzLabel="已结束"></nz-option>
          <!-- <nz-option nzValue="WAITING_ANSWER" nzLabel="未开始"></nz-option> -->
        </nz-select>
      </li>
      <li>
        <nz-select
          [(ngModel)]="standardQuestionnaireId"
          nzShowSearch
          nzAllowClear
          nzPlaceHolder="工具"
          (ngModelChange)="search()"
        >
          <nz-option
            *ngFor="let tool of tools"
            [nzCustomContent]="true"
            [nzValue]="tool.id"
            [nzLabel]="tool.name"
          >
            <div
              [nzTooltipTitle]="tool.name"
              nzTooltipPlacement="right"
              nz-tooltip
            >
              {{ tool.name }}
            </div>
          </nz-option>
        </nz-select>
      </li>
      <li>
        <nz-select
          [(ngModel)]="surveyType"
          nzAllowClear
          nzPlaceHolder="分类"
          (ngModelChange)="search()"
        >
          <nz-option nzValue="ASSESSMENT" nzLabel="测评"></nz-option>
          <nz-option nzValue="EMPLOYEE_ENGAGEMENT" nzLabel="调研"></nz-option>
        </nz-select>
      </li>
      <!-- <li>
        <nz-select [(ngModel)]="founder" nzAllowClear nzPlaceHolder="创建人">
          <nz-option nzValue="lucy" nzLabel="Lucy"></nz-option>
          <nz-option nzValue="disabled" nzLabel="Disabled" nzDisabled></nz-option>
        </nz-select>
      </li> -->
      <li>
        <nz-range-picker
          [(ngModel)]="dateRange"
          (ngModelChange)="onChange($event)"
        ></nz-range-picker>
      </li>
      <li></li>
      <li>
        <button nz-button nzType="default" (click)="search()">查询</button>
      </li>
    </ul>
    <div class="title">
      <div class="left">
        <h2>
          活动列表
          <div
            class="img_up"
            [ngClass]="orderBy === 'ASC' ? 'img_up_hover' : ''"
            (click)="sort('ASC')"
          ></div>
          <div
            class="img_down"
            [ngClass]="orderBy === 'DESC' ? 'img_down_hover' : ''"
            (click)="sort('DESC')"
          ></div>
        </h2>
        <div>
          <label
            nz-checkbox
            (ngModelChange)="changeCheckbox($event)"
            [(ngModel)]="isClosingSoon"
            >7天内到期活动</label
          >
        </div>
      </div>
      <div class="right">
        <div class="tab-box">
          <div
            class="card"
            [ngClass]="tabNum == 2 ? 'blue-text' : ''"
            (click)="changeTab(2)"
          >
            <i class="iconfont icon-card-model"></i><span>卡片模式</span>
          </div>
          <nz-divider nzType="vertical"></nz-divider>
          <div
            class="list"
            [ngClass]="tabNum == 1 ? 'blue-text' : ''"
            (click)="changeTab(1)"
          >
            <i class="iconfont icon-list-model"></i><span>列表模式</span>
          </div>
        </div>
        <div
          class="pagination"
          *ngIf="listData.length !== 0 && projectCount !== 0"
        >
          <i
            class="iconfont icon-icon_arrow_left"
            [ngClass]="page.current == 1 ? 'page-no' : ''"
            (click)="pageIndexChange(page.current - 1)"
          ></i>
          <span class="page-num"
            ><span>{{ page.current }}</span
            >{{ "/" + page.pages }}</span
          >
          <i
            class="iconfont icon-icon_arrow_right"
            [ngClass]="page.current == page.pages ? 'page-no' : ''"
            (click)="pageIndexChange(page.current + 1)"
          ></i>
        </div>
      </div>
    </div>
  </header>
  <nz-spin [nzSpinning]="isSpinning">
    <div class="listbox">
      <!-- 活动空状态 -->
      <ng-container *ngIf="projectCount === 0">
        <div class="empty-box">
          <img
            src="assets/images/event-management/home/<USER>"
            alt=""
          />
          <p>活动列表空空如也，请先新建活动吧～</p>
          <div class="new-event" (click)="newAddProject()">
            <img
              src="assets/images/event-management/home/<USER>"
              alt=""
            />
            新建活动
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="projectCount !== 0">
        <!-- 搜索空状态 -->
        <ng-container *ngIf="listData.length === 0">
          <div class="empty-search-box">
            <img
              src="assets/images/event-management/home/<USER>"
              alt=""
            />
            <p>
              当前条件暂无搜索到活动工具，请刷新后重新选择，或点击右上角“清空条件”，进行选择。
            </p>
            <div class="new-event" (click)="clearParams()">刷新页面</div>
          </div>
        </ng-container>
        <!-- 搜索有数据 -->
        <ng-container *ngIf="listData.length !== 0">
          <!-- 标准列表 -->
          <ng-container *ngIf="tabNum === 1">
            <ul class="standard-list-box">
              <li>
                <div style="margin-bottom: 20px;" *ngFor="let data of listData">
                  <app-card
                    [isSystemInterface]="isSystemInterface"
                    type="list"
                    [data]="data"
                    [homePage]="this"
                    (loadListData)="loadListData()"
                  ></app-card>
                </div>
              </li>
            </ul>
          </ng-container>
          <!-- 卡片列表 -->
          <ng-container *ngIf="tabNum === 2">
            <ul class="card-list-box">
              <li>
                <div *ngFor="let data of listData">
                  <app-card
                    [isSystemInterface]="isSystemInterface"
                    type="card"
                    [data]="data"
                    [homePage]="this"
                    (loadListData)="loadListData()"
                  ></app-card>
                </div>
              </li>
            </ul>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>
  </nz-spin>
  <footer *ngIf="listData.length !== 0 && projectCount !== 0">
    <nz-pagination
      [nzPageIndex]="page.current"
      [nzTotal]="page.total"
      nzShowSizeChanger
      [nzPageSizeOptions]="[6, 12, 18, 24, 30]"
      [nzPageSize]="page.size"
      (nzPageIndexChange)="pageIndexChange($event)"
      (nzPageSizeChange)="pageSizeChange($event)"
    ></nz-pagination>
  </footer>
  <div class="mock_div" *ngIf="showmock">
    <div class="bg_ul" *ngIf="showmock"></div>
    <ul class="img_ul" *ngIf="setp1">
      <img src="./assets/images/project_1.png" />
      <div style="margin-left: 120px;">
        <p>点击这里，对本次活动进行操作</p>
        <div class="btn_div">
          <div class="left_d" (click)="jumprun()">跳过</div>
          <div class="right_d" (click)="next1()">我知道了</div>
        </div>
      </div>
    </ul>
  </div>
</div>
