.container {
  // border-right: 2px dashed #ccc;
  // background-color: burlywood;
  // display: flex;
  // flex-direction: column;
}

.lan {
  margin: 15px 0;
  font-size: 14px;
  font-weight: 700;
  color: #17314C;
  line-height: 20px;
}

.pre {
  margin-top: 40px;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: 700;
  color: #17314C;
  line-height: 20px;
}

.preview {
  margin-bottom: 15px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 500;
  color: #495970;
  line-height: 20px;
}

tr {
  height: 38px;
  padding: 5px;
}

.redBorder {
  border: solid red 1px;
}

.td_title {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #495970;
}

.arrow {
  display: flex;
  padding: 5px 0;
  width: 150px;

  div:first-child {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
  }
}

// input[disabled] {
//     background: #fff;
//     opacity: 1;
//     color: black;
// }