<div class="container">

    <!-- <div class="header">
        <div class="text">
            组织名称：{{orgName}}
        </div>
        
    </div> -->

    <!-- org list -->
    <div class="bg" style="min-height: 200px; width: 100%; ">

        <div class="orgSearch" style="display: flex; justify-content: space-between; justify-content: center; margin-bottom: 10px; padding: 10px;">
            <div style="margin-right: 10px; display: flex; align-items: center;">
                <label nz-checkbox [(ngModel)]="checked" (ngModelChange)="selectAll($event, false)">
                    {{checked ? '全不选' : '全选'}}
                </label>
            </div>
    
            <div style="flex: 1;">
                <nz-input-group [nzPrefix]="suffixIconSearch1">
                    <input type="text" nz-input placeholder="请输入关键词" [(ngModel)]="searchValue" />
                </nz-input-group>

                <ng-template #suffixIcon>
                    <i nz-icon nzType="search"></i>
                </ng-template>

                <ng-template #suffixIconSearch1>
                    <img src="./assets/images/icon_search.png" >
                  </ng-template>

            </div>
        </div>

        <div *ngIf="orgList.length > 0" style="width: 465px; max-height: 450px; display:flex; align-items: stretch;" class="treeScroll">
            <nz-tree #nzTreeComponent
                nzCheckable
                [nzCheckStrictly]= "true"
                [nzExpandAll]="false"
                [nzData]="orgList" 
                [nzExpandedKeys]="defaultExpandedKeys"
                [nzSearchValue]="searchValue" 
                (nzCheckBoxChange)="nzEvent($event)">
            </nz-tree>
        </div>
    </div>

    <!-- chart -->
    <div class="bg" style="width:100%; min-height: 360px; padding-top: 25px; display:flex; justify-content: flex-end; align-items: center; margin: 15px 0;">
        <echart *ngIf="chartLoaded || true" [option]="chartOption" [width]="containerWidth" [height]="containerHeight"></echart>    
    </div>

    <!-- 人员列表 -->
    <div *ngIf="permission" class="body bg">

        <div class="input">
            <nz-input-group [nzPrefix]="suffixIconSearch" class="search">
                <input type="text" nz-input placeholder="请输入关键词" [(ngModel)]="searchName" (keyup.enter)="searchPerson()" />
            </nz-input-group>
            <ng-template #suffixIconSearch>
                <img src="./assets/images/icon_search.png" (click)="searchPerson()">
            </ng-template>
        </div>

        <div class="tit">
            <div>作答人员</div>
        </div>

        <nz-table style="background-color: white;" #basicTable [nzData]="dataSet" [nzFrontPagination]="false">
            <thead>
                <tr>
                    <!-- <th class="nowrap">序号</th> -->
                    <th class="nowrap">测评者</th>
                    <!-- <th class="nowrap">邀请时间</th> -->
                    <th class="nowrap">组织</th>
                    
                    <th class="nowrap">完成时间</th>
                    <th class="nowrap">填答状态</th>
                    <!-- <th class="nowrap">操作</th> -->
                </tr>
            </thead>
    
            <tbody>
                <tr *ngFor="let data of basicTable.data; let ind = index;">
                    <!-- <td>{{(currentPage - 1) * pageSize + ind + 1 }}</td> -->
                    <td>{{data.firstName}}</td>
                    <!-- <td>{{data.createTime | date:'yyyy-MM-dd HH:mm' }}</td> -->
                    <td>{{data.organizationName?.zh_CN}}</td>
                    <td>{{data.endTime | date:'yyyy-MM-dd HH:mm' }}</td>
                    <td>
                        {{data.answerStatus === 'ANSWERED' ? '已完成' : '未完成'}}
                        <app-btn [text]="''" [image]="'./assets/images/org/del.png'" [hoverColor]="'#409EFF'"
                            [hoverImage]="'./assets/images/org/del_hover.png'" nz-popconfirm
                            [nzPopconfirmTitle]="'确定要删除人员?'" nzPopconfirmPlacement="bottom"
                            (nzOnConfirm)="deletePerson(data.id)">
                        </app-btn>
                        <ng-container *ngIf="permission">
                          <i *ngIf="data.answerStatus !== 'NOT_INVITED', else IconTem" class="iconfont icon-icon-" title="清除"
                          (click)="clearHistory(data.id)"></i>
                          <ng-template #IconTem>
                            <i class="iconfont icon-icon-" style="cursor:not-allowed;" title="清除"></i>
                          </ng-template>
                        </ng-container>
                    </td>
                   
                </tr>
            </tbody>
    
        </nz-table>
    
        <!-- 分页控件 -->
        <div style="display: flex; justify-content: flex-end; padding: 12px 0;">
            <nz-pagination [(nzPageIndex)]="currentPage" [(nzPageSize)]="pageSize" [nzTotal]="totalCount"
                [nzSize]="'small'" (nzPageIndexChange)="loadPersonData()" nzShowQuickJumper>
            </nz-pagination>
        </div>
        
    </div>

</div>