<ng-container *ngIf="!locwelcomepage && !locengpage">
  <div class="advanced">
    <nz-tabset
      [nzTabBarStyle]="{ padding: 0 }"
      [nzSelectedIndex]="tableIndex"
      (nzSelectedIndexChange)="changeSelectedIndex($event)"
    >
      <nz-tab nzTitle="活动设置">
        <!-- 活动名称 -->
        <!-- <div class="mb-16">
                    <p class="title mb-16">英文活动名称</p>
                    <input nz-input nzSize="large" placeholder="请输入活动名称(英)" [(ngModel)]="settingData.name.en_US" />
                </div> -->
        <div class="mb-24">
          <p class="title mb-16">活动名称</p>
          <div style="width: 99%;">
            <app-i18n-input
              [value]="settingData.name"
              (changeValue)="changeNameValue($event)"
            ></app-i18n-input>
          </div>
        </div>
        <!-- 提示说明 -->
        <div class="mb-24">
          <p class="title mb-16">提示说明</p>
          <div nz-row [nzGutter]="16" class="mb-16">
            <div nz-col [nzSpan]="12">
              <nz-switch
                [(ngModel)]="settingData.isCheckLicenseNotice"
                class="mr-8"
              ></nz-switch>
              <span class="text mr-16">许可声明</span>
              <a routerLink="/permission-statement" target="_blank">预览</a>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-switch
                [(ngModel)]="settingData.isEnableWelcomePage"
                class="mr-8"
              ></nz-switch>
              <span class="text mr-16">欢迎页</span>
              <a
                *ngIf="settingData.isEnableWelcomePage"
                (click)="editorwelcome()"
                >编辑</a
              >
            </div>
          </div>
          <div nz-row [nzGutter]="16" class="mb-16">
            <div nz-col [nzSpan]="12">
              <nz-switch
                [(ngModel)]="settingData.isEnableEndPage"
                class="mr-8"
              ></nz-switch>
              <span class="text mr-16">结束页</span>
              <a *ngIf="settingData.isEnableEndPage" (click)="editorend()"
                >编辑</a
              >
            </div>
          </div>
        </div>
        <!-- 企业LOGO -->
        <div class="mb-24">
          <p class="title mb-16">企业LOGO</p>
          <div class="mb-16">
            <label nz-checkbox [(ngModel)]="settingData.isShowKnxLogo"
              >显示测评调研云logo</label
            >
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-upload
                class="avatar-uploader"
                nzListType="picture-card"
                nzName="avatar"
                [nzShowUploadList]="false"
                [nzBeforeUpload]="beforeUpload1"
                [nzCustomRequest]="customReq"
                (nzChange)="handleChange($event)"
              >
                <img *ngIf="avatarUrl" [attr.src]="avatarUrl" class="avatar" />

                <ng-container>
                  <div class="loading">
                    <img src="./assets/images/upload.png" *ngIf="!avatarUrl" />
                    <div *ngIf="!avatarUrl" class="ant-upload-text">上传</div>
                  </div>
                </ng-container>
              </nz-upload>
              <span class="suggest"
                >建议：800px*800px以上的1:1尺寸，300K以内，PNG格式</span
              >
            </div>
          </div>
        </div>
        <!-- 填答背景 -->
        <div>
          <p class="title mb-16">填答背景</p>
          <div class="mb-16">
            <label nz-checkbox [(ngModel)]="settingData.isShowBackgroundPic"
              >显示填答背景</label
            >
          </div>
          <div nz-row [nzGutter]="40">
            <div nz-col [nzSpan]="10">
              <nz-upload
                class="avatar-uploader"
                nzListType="picture-card"
                nzName="avatar"
                [nzShowUploadList]="false"
                [nzBeforeUpload]="beforeUpload"
                [nzCustomRequest]="customReqPc"
                (nzChange)="handleChangePic($event, 'PC')"
              >
                <img
                  *ngIf="PcavatarUrl"
                  [attr.src]="PcavatarUrl"
                  class="avatar"
                />
                <ng-container>
                  <div class="loading">
                    <img
                      src="./assets/images/upload.png"
                      *ngIf="!PcavatarUrl"
                    />
                    <div *ngIf="!PcavatarUrl" class="ant-upload-text">
                      上传(PC端)
                    </div>
                  </div>
                </ng-container>
              </nz-upload>
              <span class="suggest">建议:1920px*1080px，500K以内，PNG格式</span>
            </div>
            <div nz-col [nzSpan]="10">
              <nz-upload
                class="avatar-uploader"
                nzListType="picture-card"
                nzName="avatar"
                [nzShowUploadList]="false"
                [nzBeforeUpload]="beforeUpload"
                [nzCustomRequest]="customReqMb"
                (nzChange)="handleChangePic($event, 'MB')"
              >
                <img
                  *ngIf="MbavatarUrl"
                  [attr.src]="MbavatarUrl"
                  class="avatar"
                />

                <ng-container>
                  <div class="loading">
                    <img
                      src="./assets/images/upload.png"
                      *ngIf="!MbavatarUrl"
                    />
                    <div *ngIf="!MbavatarUrl" class="ant-upload-text">
                      上传(移动端)
                    </div>
                  </div>
                </ng-container>
              </nz-upload>
              <span class="suggest">建议:160px*78px，500K以内，PNG格式</span>
            </div>
          </div>
        </div>
      </nz-tab>
      <nz-tab nzTitle="发布设置">
        <!-- 题数 -->
        <div class="mb-24">
          <p class="title mb-16">题数</p>
          <div class="mb-16">
            <nz-radio-group
              [(ngModel)]="settingData.isQuestionCustomSort"
              (ngModelChange)="changeQuestionCustomSort($event)"
            >
              <label nz-radio [nzValue]="false">每页题数</label>
              <nz-input-number
                class="mr-24"
                [(ngModel)]="settingData.questionNumInOnePage"
                [nzPrecision]="precision"
                nzPlaceHolder="题数"
                [nzDisabled]="settingData.isQuestionCustomSort"
              >
              </nz-input-number>
              <label
                nz-radio
                *ngIf="
                  reportType.indexOf('INVESTIGATION_RESEARCH_CUSTOM') !== -1
                "
                [nzValue]="true"
                >自定义</label
              >
            </nz-radio-group>
          </div>
          <div class="mb-16">
            <label
              nz-checkbox
              [nzValue]="true"
              [(ngModel)]="settingData.isAutoPage"
            >
              一页一题时，自动翻页
            </label>
          </div>
          <div class="mb-16" *ngIf="settingData.isQuestionCustomSort">
            <label
              nz-checkbox
              [nzValue]="true"
              [(ngModel)]="settingData.isAutoFillQuestionBook"
            >
              题本分发致页面题目有缺时，自动补位后续题本
            </label>
          </div>
        </div>
        <!-- 排序 -->
        <div class="mb-24">
          <p class="title mb-16">排序</p>
          <div class="mb-16">
            <nz-radio-group
              [(ngModel)]="settingData.sequence"
              (ngModelChange)="funsequence($event)"
              [nzDisabled]="settingData.isQuestionCustomSort"
            >
              <label nz-radio nzValue="QUESTION_TYPE" class="mr-24"
                >按题目类型排序</label
              >
              <label nz-radio nzValue="RANDOM">
                打乱排序
              </label>
            </nz-radio-group>
          </div>
        </div>
        <!-- 有效性 -->
        <div
          class="mb-24"
          *ngIf="
            this.permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PUBLISH_SETTING'
            )
          "
        >
          <p class="title mb-16">有效性</p>
          <div class="mb-16">
            <div class="mb-16">
              <span class="mr-8">填答有效时间 ≥</span>
              <nz-input-number
                [(ngModel)]="mm"
                [nzMin]="0"
                [nzPrecision]="precision_min"
                nzPrecisionMode="cut"
                (nzBlur)="getdefaultTime()"
                (ngModelChange)="mChange($event)"
                nzPlaceHolder="分"
              ></nz-input-number>
              <span class="mr-8 ml-8">分</span>
              <nz-input-number
                [(ngModel)]="ss"
                [nzMin]="0"
                [nzPrecision]="precision_min"
                nzPrecisionMode="cut"
                (nzBlur)="getdefaultTime()"
                (ngModelChange)="sChange($event)"
                nzPlaceHolder="秒"
              ></nz-input-number>
              <span class="ml-8">秒</span>
              <i
                nz-icon
                nzType="question-circle"
                nzTheme="fill"
                class="tip-iocn ml-8"
                nz-tooltip
                [nzTitle]="validTime"
                [nzOverlayStyle]="{ 'max-width': '340px' }"
              ></i>
              <ng-template #validTime>
                <p style="margin: 4px 0;font-weight: 600;">
                  有效时间说明
                </p>
                <p style="margin: 4px 0;">
                  有效时间计算精确到秒，毫秒直接舍弃。
                </p>
                <p style="margin: 4px 0;">
                  举例：
                </p>
                <div style="display: flex;">
                  <span style="margin-right: 4px;">●</span>
                  <p>
                    设置有效时间≥2分0秒，即2分钟以内为无效数据，用户填答1分59秒85毫秒记为无效；
                  </p>
                </div>
                <div style="display: flex;margin-bottom:4px;">
                  <span style="margin-right: 4px;">●</span>
                  <p>
                    设置有效时间≥2分1秒，即2分钟及以内为无效数据，用户填答2分0秒49毫秒记为无效。
                  </p>
                </div>
              </ng-template>
            </div>
            <div class="mb-16">
              <span class="mr-8">填答完成率 ≥</span>
              <nz-input-number
                [(ngModel)]="settingData.answerEffectiveRange"
                [nzMin]="1"
                [nzMax]="100"
                [nzStep]="1"
                [nzPrecision]="precision"
                nzPrecisionMode="cut"
                nzPlaceHolder="百分比"
              ></nz-input-number>
              <span class="ml-8">%</span>
              <i
                nz-icon
                nzType="question-circle"
                nzTheme="fill"
                class="tip-iocn ml-8"
                nz-tooltip
                [nzTitle]="effectiveRange"
                [nzOverlayStyle]="{ 'max-width': '340px' }"
              ></i>
              <ng-template #effectiveRange>
                <p style="margin: 4px 0;font-weight: 600;">
                  完成率说明
                </p>
                <p style="margin: 4px 0;">
                  填答人所有题目的作答比例。
                </p>
                <div style="display: flex;">
                  <span style="margin-right: 4px;">●</span>
                  <p>
                    含所有题型，含必填、选填，不含穿透的结果题；
                  </p>
                </div>
                <div style="display: flex;margin-bottom:4px;">
                  <span style="margin-right: 4px;">●</span>
                  <p>
                    不同填答人的计算分母因分发题可能存在差异。
                  </p>
                </div>
              </ng-template>
            </div>
            <div class="mb-16">
              <span class="mr-8">填答一致性 ≤</span>
              <nz-input-number
                [(ngModel)]="settingData.answerSameRate"
                [nzMin]="1"
                [nzMax]="100"
                [nzStep]="1"
                [nzPrecision]="precision"
                nzPrecisionMode="cut"
                nzPlaceHolder="百分比"
              ></nz-input-number>
              <span class="ml-8">%</span>
              <i
                nz-icon
                nzType="question-circle"
                nzTheme="fill"
                class="tip-iocn ml-8"
                nz-tooltip
                [nzTitle]="consistency"
                [nzOverlayStyle]="{ 'max-width': '370px' }"
              ></i>
              <ng-template #consistency>
                <p style="margin: 4px 0;font-weight: 600;">
                  一致性说明
                </p>
                <p style="margin: 4px 0;">
                  填答过程中，填答人在所有题目的选项中选择了相同选项的比例。
                </p>
                <p style="margin: 4px 0;">
                  所有题目范围：
                </p>
                <div style="display: flex;">
                  <span style="margin-right: 4px;">●</span>
                  <p>
                    包含穿透的条件题、穿透的结果题、分发题、必填题、选填题；
                  </p>
                </div>
                <div style="display: flex;margin-bottom:4px;">
                  <span style="margin-right: 4px;">●</span>
                  <p>
                    不含开放题、多选开放题。
                  </p>
                </div>
                <p style="margin: 4px 0;">
                  相同选项：
                </p>
                <div style="display: flex;">
                  <span style="margin-right: 4px;">●</span>
                  <p>
                    选项序号相同，记为相同选项，不依赖于选项的描述；
                  </p>
                </div>
                <div style="display: flex;margin-bottom:4px;">
                  <span style="margin-right: 4px;">●</span>
                  <p>
                    选项序号不同，即便选项描述一致，视为不相同选项。
                  </p>
                </div>
                <p style="margin: 4px 0;">
                  不同填答人的计算分母因分发题、穿透题可能存在差异。
                </p>
              </ng-template>
            </div>
                 <div class="mb-16">
               <nz-switch
                class="mr-8"
                [(ngModel)]="settingData.isCheckedAnswerValid"
                nzCheckedChildren="开启"
                nzUnCheckedChildren="关闭"
              ></nz-switch>
              <span >不满足填答有效性不可提交</span>
              </div>
        
            <div class="mb-16">
              <label
                nz-checkbox
                [nzDisabled]="voild"
                [(ngModel)]="settingData.isOpenValidQuestion"
                >填答有效题本</label
              >
            </div>
            <div class="mb-16" *ngIf="isShowIsNotCalculateLackAnswer">
              <nz-select
                style="width: 86px;"
                [(ngModel)]="settingData.isNotCalculateLackAnswer"
                nzPlaceHolder="计算/不计算"
              >
                <nz-option [nzValue]="false" nzLabel="计算"></nz-option>
                <nz-option [nzValue]="true" nzLabel="不计算"></nz-option>
              </nz-select>
              <span class="mr-8">
                <!-- 不计算 -->
                【因不计分/未填写题本等】致不足有效人数</span
              >
            </div>
          
          </div>
        </div>
        <!-- 语言 -->
        <div class="mb-24">
          <p class="title-between mb-16">
            <span>语言</span>
            <a *ngIf="permission && isShowLan()" (click)="showanSetting()"
              ><i nz-icon nzType="setting" nzTheme="outline" class="mr-8"></i
              >语言设置</a
            >
          </p>
          <div
            nz-row
            [nzGutter]="8"
            class="mb-16"
            *ngIf="permission && isShowLan()"
          >
            <div nz-col [nzSpan]="6">
              <div class="mt-6">请选择新增语言</div>
            </div>
            <div nz-col [nzSpan]="18">
              <nz-select
                style="width: 100%"
                nzMode="multiple"
                nzPlaceHolder="请选择"
                [(ngModel)]="addCodes"
                [nzDropdownRender]="render"
                [(nzOpen)]="allLansOpen"
                nzAllowClear
                nzPlaceHolder="请选择"
                (ngModelChange)="changeProjectLan($event)"
              >
                <!-- <nz-option *ngFor="let option of lansFilter_cnen" [nzLabel]="option.name" [nzValue]="option.value">
                </nz-option> -->
                <nz-option
                  *ngFor="let option of lansFilter_cnen"
                  [nzLabel]="option.name"
                  [nzValue]="option.value"
                ></nz-option>
              </nz-select>
              <!-- <ng-template #render>
                <ng-container *ngIf="lansFilter_cnen.length > 0">
                  <div
                    style="font-size: 12px;color: #FF4F40;line-height: 17px;padding: 8px 12px"
                  >
                    *最多可设置 3 种语言
                  </div> -->
                        <!-- <nz-divider style="margin: 2px 0;"></nz-divider>
                        <div style="display: flex;justify-content: flex-end;padding: 8px 12px">
                          <button nz-button nzType="primary" (click)="showanSetting()">设置</button>
                        </div> -->
                  <!-- </ng-container>
                </ng-template> -->
            </div>
          </div>
          <div nz-row [nzGutter]="8" class="mb-16">
            <div nz-col [nzSpan]="6">
              <span>填答时可选语言</span>
            </div>
            <div nz-col [nzSpan]="18">
              <nz-checkbox-wrapper (nzOnChange)="optionalLan($event)">
                <label
                  *ngFor="let item of projectLangOptions; let i = index"
                  nz-checkbox
                  [nzValue]="item.value"
                  [(ngModel)]="item.checked"
                >
                  {{ item.name }}
                </label>
              </nz-checkbox-wrapper>
            </div>
          </div>
          <div nz-row [nzGutter]="8" class="mb-16">
            <div nz-col [nzSpan]="6">
              <span>默认填答语言</span>
            </div>
            <div nz-col [nzSpan]="18">
              <nz-radio-group
                [(ngModel)]="settingData.language"
                (ngModelChange)="funsequence($event)"
              >
                <label
                  *ngFor="let item of projectLangOptions; let i = index"
                  nz-radio
                  [nzValue]="item.value"
                  >{{ item.name }}</label
                >
              </nz-radio-group>
            </div>
          </div>
        </div>

        <!-- 平均计算 -->
        <div
          *ngIf="
            this.permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PUBLISH_SETTING'
            )
          "
        >
          <p class="title mb-16">计算</p>
          <div nz-row [nzGutter]="8" class="mb-16">
            <div nz-col [nzSpan]="4">
              <span>计算方式</span>
            </div>
            <div nz-col [nzSpan]="20">
              <nz-radio-group
                [(ngModel)]="settingData.calculationMethod"
                class="flex_dir_1"
              >
                <label nz-radio nzValue="STANDARD_APPROVAL_DEGREE"
                  >赞成度（标准）<i
                        nz-icon
                        nzType="question-circle"
                        nzTheme="fill"
                        class="tip-iocn ml-8"
                        nz-tooltip
                        nzTitle="两种计算方式的总关联任务百分比均为100%"
                        [nzOverlayStyle]="{ 'max-width': '340px' }"
                ></i
                ></label
                >
                <label nz-radio nzValue="AVERAGE"
                  >平均分
                  <i
                    nz-icon
                    nzType="question-circle"
                    nzTheme="fill"
                    class="tip-iocn ml-8"
                    nz-tooltip
                    nzTitle="平均分目前仅支持乾象报告类型"
                    [nzOverlayStyle]="{ 'max-width': '340px' }"
                  ></i
                ></label>
              </nz-radio-group>
            </div>
          </div>
          <!-- 赞成/中立/反对 选项组，仅在选择赞成度（标准）时显示 -->
          <div nz-row [nzGutter]="8" class="mb-16" [hidden]="settingData.calculationMethod !== 'STANDARD_APPROVAL_DEGREE' || reportType.indexOf('DP_INVESTIGATION_RESEARCH_CUSTOM') < 0" style="background-color: #f3f4f6; padding: 8px;">
            <div nz-col [nzSpan]="20">
              <nz-radio-group [(ngModel)]="settingData.calculationApprovalType">
                <label nz-radio nzValue="APPROVAL_NEUTRAL_DISAPPROVAL">赞成/中立/反对</label>
                <label nz-radio nzValue="APPROVAL_NEUTRAL_DISAPPROVAL_IGNORE">赞成/中立/反对/不选择</label>
              </nz-radio-group>
            </div>
          </div>
          <div nz-row [nzGutter]="8" class="mb-16">
            <div nz-col [nzSpan]="4">
              <span>计算层级</span>
            </div>
            <div nz-col [nzSpan]="20">
              <nz-radio-group
                [(ngModel)]="settingData.isDimensionAlgorithmCustom"
                class="flex_dir_1"
              >
                <label nz-radio [nzValue]="false">按层级计算（标准）</label>
                <label
                  nz-radio
                  [nzValue]="true"
                  (click)="showCusBox()"
                  nzOverlayClassName="permission-tool"
                  [nzVisible]="showCus"
                  nz-tooltip
                  [nzTooltipTitle]="CusBox"
                  [nzTooltipTrigger]="'click'"
                  >自定义</label
                >
              </nz-radio-group>
              <ng-template #CusBox>
                <div class="permission-tool-body">
                  <ul>
                    <li>
                      指数：
                      <nz-select
                        style="width: 100px;"
                        [(ngModel)]="signAlgorithm"
                        nzPlaceHolder="请选择"
                      >
                        <nz-option
                          *ngFor="let option of cusOption; let index = index"
                          [nzValue]="option.value"
                          [nzLabel]="option.label"
                        ></nz-option>
                      </nz-select>
                    </li>
                    <li>
                      一级维度：
                      <nz-select
                        style="width: 100px;"
                        [(ngModel)]="oneRankDimensionAlgorithm"
                        nzPlaceHolder="请选择"
                      >
                        <nz-option
                          *ngFor="let option of cusOption; let index = index"
                          [nzDisabled]="index === 0"
                          [nzValue]="option.value"
                          [nzLabel]="option.label"
                        ></nz-option>
                      </nz-select>
                    </li>
                    <li>
                      二级维度：
                      <nz-select
                        style="width: 100px;"
                        [(ngModel)]="twoRankDimensionAlgorithm"
                        nzPlaceHolder="请选择"
                      >
                        <nz-option
                          *ngFor="let option of cusOption; let index = index"
                          [nzDisabled]="index === 0 || index === 1"
                          [nzValue]="option.value"
                          [nzLabel]="option.label"
                        ></nz-option>
                      </nz-select>
                    </li>
                    <li>
                      三级维度：
                      <nz-select
                        style="width: 100px;"
                        [(ngModel)]="threeRankDimensionAlgorithm"
                        nzPlaceHolder="请选择"
                      >
                        <nz-option
                          *ngFor="let option of cusOption; let index = index"
                          [nzDisabled]="
                            index === 0 || index === 1 || index === 2
                          "
                          [nzValue]="option.value"
                          [nzLabel]="option.label"
                        ></nz-option>
                      </nz-select>
                    </li>
                    <li>
                      <span class="tips"
                        >*如维度层级缺失，自动向下一级计算</span
                      >
                    </li>
                  </ul>

                  <footer class="permission-tool-footer">
                    <button
                      (click)="cusDefault()"
                      nzSize="small"
                      nzType="default"
                      nz-button
                    >
                      恢复默认
                    </button>
                    <button
                      (click)="cusOk()"
                      nzSize="small"
                      nzType="primary"
                      nz-button
                    >
                      确认
                    </button>
                  </footer>
                </div>
              </ng-template>
            </div>
          </div>
          <!-- 交叉计算 目前开放给调研-->
          <div *ngIf="reportType.indexOf('INVESTIGATION_RESEARCH') !== -1">
            <p class="title mb-16">交叉计算</p>
            <div class="mb-16">
              <nz-radio-group
                [(ngModel)]="settingData.coefficientAlgorithm"
                class="flex_dir_1"
              >
                <label nz-radio nzValue="REGRESSION" *ngIf=" reportType.indexOf('DP_INVESTIGATION_RESEARCH_CUSTOM') >= 0 || reportType.indexOf('OC_INVESTIGATION_RESEARCH') >= 0">回归系数</label>
                <!-- [nzDisabled]="
                  reportType === 'DP_INVESTIGATION_RESEARCH_CUSTOM' ||
                  reportType === 'OC_INVESTIGATION_RESEARCH'
                " -->
                <label nz-radio nzValue="CORRELATION">相关系数</label>
              </nz-radio-group>
            </div>
          </div>
        </div>

        <!-- 关联任务 -->
        <div
          class="mb-24"
          *ngIf="
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:LOTTERY'
            ) ||
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
            )
          "
        >
          <p class="title mb-16">关联任务</p>
          <div nz-row [nzGutter]="16" class="mb-16">
            <div
              nz-col
              [nzSpan]="12"
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:LOTTERY'
                )
              "
            >
              <nz-switch
                [(ngModel)]="isEnableAnswerLotteryDraw"
                class="nz-switch"
                (ngModelChange)="lotteryChange($event)"
              ></nz-switch>
              <span class=" ml-8">填答抽奖任务</span>
            </div>
            <div
              nz-col
              [nzSpan]="12"
              *ngIf="
                reportType !== 'CULTURE_INVESTIGATION_RESEARCH' &&
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:REPORT_CONGIF'
                )
              "
            >
              <nz-switch
                [(ngModel)]="isEnableReportSettings"
                class="nz-switch"
              ></nz-switch>
              <span class=" ml-8">报告设置</span>
            </div>
            <!-- <div nz-col [nzSpan]="12"
              *ngIf="reportType !== 'CULTURE_INVESTIGATION_RESEARCH' && reportType !== 'VIVO_INVESTIGATION_RESEARCH_CUSTOM'">
              <nz-switch [nzDisabled]="!type || type === 'ANNOUNCED' || type === 'PREVIEW'"
                [(ngModel)]="isEnableAnalysisCustomIndex" class="nz-switch"></nz-switch>
              <span class=" ml-8">分析指数任务</span>
            </div> -->
          </div>
          <div nz-row [nzGutter]="16" class="mb-16">
            <div
              nz-col
              [nzSpan]="12"
              *ngIf="
                reportType !== 'VIVO_INVESTIGATION_RESEARCH_CUSTOM' &&
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                )
              "
            >
              <nz-switch
                [(ngModel)]="isEnableQuestionBookDistribute"
                class="nz-switch"
              ></nz-switch>
              <span class=" ml-8">题本分发任务</span>
            </div>
            <!-- <div nz-col [nzSpan]="12" *ngIf="reportType != 'CULTURE_INVESTIGATION_RESEARCH'">
              <nz-switch [nzDisabled]="!type || type === 'ANNOUNCED' || type === 'PREVIEW'"
                [(ngModel)]="isEnableAnalysisTask" class="nz-switch"></nz-switch>
              <span class=" ml-8">相关分析任务</span>
            </div> -->
          </div>
        </div>
      </nz-tab>
    </nz-tabset>
  </div>
  <div class="footer" [hidden]="locwelcomepage || locengpage">
    <button nz-button nzType="default" (click)="getDefault()">恢复默认</button>
    <button nz-button nzType="primary" (click)="getSaveSet()">保存设置</button>
  </div>
</ng-container>
<!-- 开始页/结束页 -->
<ng-container *ngIf="locwelcomepage || locengpage">
  <div class="advanced">
    <p class="title mb-16">{{ pagename }}</p>
    <ng-container *ngIf="locengpage">
      <app-i18n-select
        [active]="loceng_locwelcome_lan"
        (selectChange)="onSelectI18n($event)"
      ></app-i18n-select>
      <div style="width: 99%; margin-top: 16px;">
        <tinymce
          [config]="tinyconfig"
          [(ngModel)]="settingData.endPage[loceng_locwelcome_lan]"
          delay="10"
        ></tinymce>
      </div>
    </ng-container>
    <ng-container *ngIf="locwelcomepage">
      <app-i18n-select
        [active]="loceng_locwelcome_lan"
        (selectChange)="onSelectI18n($event)"
      ></app-i18n-select>
      <div style="width: 99%; margin-top: 16px;">
        <tinymce
          [config]="tinyconfig"
          [(ngModel)]="settingData.welcomePage[loceng_locwelcome_lan]"
          delay="10"
        ></tinymce>
      </div>
    </ng-container>
    <div class="footer">
      <button nz-button nzType="default" (click)="wlcomeDefault()">
        返回上一步
      </button>
      <button nz-button nzType="primary" (click)="wlcomeSaveSet()">确认</button>
    </div>
  </div>
</ng-container>

<!-- 语言设置 -->
<app-i18n-setting
  [visible]="lanVisible"
  [selectOptions]="allLans"
  [projectId]="projectId"
  (onClose)="onLanSettingClose()"
  (onSave)="onLanSettingSave($event)"
></app-i18n-setting>
