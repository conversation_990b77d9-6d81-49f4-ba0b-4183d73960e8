import { Directive, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[appDisableTime]'
})
export class DisableTimeDirective {

  @Input('appDisableTime') disableTime: number;

  constructor() { }

  @HostListener('click', ['$event'])
  onClick(evt: MouseEvent) {
    if(!this.disableTime || this.disableTime < 1) {
      this.disableTime = 2;
    }
    let that = this;
    evt.target['disabled'] = true; 
    console.log(`${this.disableTime }秒后可用`)
    setTimeout(function () {
      evt.target['disabled'] = false; 
    }, that.disableTime * 1000);
  }

}
