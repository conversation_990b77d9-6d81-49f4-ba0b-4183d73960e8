user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

  #前端配置
    server {
      listen 80 default_server;
      listen [::]:80 default_server;

      root /usr/share/nginx/html/tenant;

      index index.html index.htm index.nginx-debian.html;

      server_name localhost;

      location = /index.html {
        add_header Cache-Control "no-cache, no-store";
      }

      location / {
        try_files $uri $uri/ /index.html;
      }
    }
    #gzip  on;

    include /etc/nginx/conf.d/*.conf;
}
