import { Component, OnInit, Input, ViewChild, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import {
  NzDrawerRef,
  NzFormatEmitEvent,
  NzMessageService,
  NzTreeComponent,
  NzTreeNode,
  NzTreeNodeOptions,
  UploadXHRArgs,
} from "ng-zorro-antd";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import _ from "lodash";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { HttpClient, HttpEvent } from "@angular/common/http";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { NzSpinModule } from "ng-zorro-antd/spin";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-sub-administrator-modal",
  templateUrl: "./sub-administrator-modal.component.html",
  styleUrls: ["./sub-administrator-modal.component.less"],
})
export class SubAdministratorModalComponent implements OnInit, OnDestroy {
  @Input() projectId;
  @Input() standardReportType;
  @Input() transType;
  @ViewChild("nzOrgTreeComponent", { static: false })
  nzOrgTreeComponent: NzTreeComponent;
  @ViewChild("nzOrgViewTreeComponent", { static: false })
  nzOrgViewTreeComponent: NzTreeComponent;
  @ViewChild("nzFactorsComponent", { static: false })
  nzFactorsComponent: NzTreeComponent;
  @ViewChild("nzAnswerFactorsComponent", { static: false })
  nzAnswerFactorsComponent: NzTreeComponent;
  @ViewChild("nzViewReportFactorsComponent", { static: false })
  nzViewReportFactorsComponent: NzTreeComponent;

  panels: any = [
    {
      active: false,
      disabled: false,
      isShow: true,
      title: "可查看填答进度的部门",
      id: "orgView",
      customStyle: {
        background: "#ffffff",
        borderBottom: "1px solid #E6E6E6",
      },
      list: [],
    },
    {
      active: false,
      disabled: false,
      isShow: true,
      title: "报告查看部门",
      id: "org",
      customStyle: {
        background: "#ffffff",
        borderBottom: "1px solid #E6E6E6",
      },
      list: [],
    },
    {
      active: false,
      disabled: false,
      isShow: true,
      title: "报告分析因子", // 报告分析因子 可生成团队报告的权限
      id: "demographics",
      customStyle: {
        background: "#ffffff",
        borderBottom: "1px solid #E6E6E6",
      },
      list: [],
    },
    {
      active: false,
      disabled: false,
      isShow: true,
      title: "可查看填答进度的分析因子",
      id: "demographicsView",
      customStyle: {
        background: "#ffffff",
        borderBottom: "1px solid #E6E6E6",
      },
      list: [],
    },
    {
      active: false,
      disabled: false,
      isShow: true,
      title: "报告内外部常模",
      id: "norm",
      customStyle: {
        background: "#ffffff",
        borderBottom: "1px solid #E6E6E6",
      },
      list: [],
    },
    {
      active: false,
      disabled: false,
      isShow: true,
      id: "history",
      title: "报告历史对比",
      customStyle: {
        background: "#ffffff",
        borderBottom: "1px solid #E6E6E6",
      },
      list: [],
    },
    {
      active: false,
      disabled: false,
      isShow: true,
      id: "viewReportFactors",
      title: "可查看/下载报告的权限",
      customStyle: {
        background: "#ffffff",
        borderBottom: "1px solid #E6E6E6",
      },
      list: [],
    },
  ];

  clickSunManagerId: string = null; // 当前 id
  currentSubIndex: string = null; // 当前索引

  checked = false;
  searchValue: any; // 搜索-子管理员
  selectedId: any = "orgView";
  selectedLevel: any;
  selectedViewLevel: any;
  showTips: boolean = false; // 是否展示右侧提示
  checkAlluser: boolean = false;
  userCheckedIndeterminate: boolean = false;

  searchOrgValue: any; // 搜索-报告查看部门
  searchOrgViewValue: any; // 搜索-可查看填答进度的部门
  searchDemographicValue: any; // 搜索-报告分析因子
  searchDemographicsViewValue: any; // 搜索-可查看填答进度的分析因子
  searchViewReportFactorsValue: any; // 搜索-可查看/下载报告

  subList: any[] = [];

  data: any;

  // org
  expandedNodes: string[] = [];
  checkedKeys: string[] = [];
  checkedKeysView: string[] = [];
  expandedNodesView: string[] = [];
  selectedNodes: any;

  expandendFactorsNodes: string[] = [];
  checkedFactorsKeys: string[] = [];
  expandendAnswerFactorsNodes: string[] = [];
  checkedAnswerFactorsKeys: string[] = [];

  // 360/测评
  expandendViewReportFactorsNodes: string[] = [];
  checkedViewReportFactorsKeys: string[] = [];

  // 折叠面板状态类
  expandMap: any = {};
  isLoading = false;
  isExporting = false;
  isImporting = false;
  // 管理权限
  permission: boolean;
  private routerSubscription: Subscription;
  constructor(
    private api: SurveyApiService,
    private drawerRef: NzDrawerRef,
    private msg: NzMessageService,
    private router: Router,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {}

  ngOnInit() {
    // transType  1：其它测评  2：360测评 3：调研
    if (this.transType === 3) {
      // 调研
      this.panels[4].isShow = !(
        this.standardReportType === "CULTURE_INVESTIGATION_RESEARCH"
      );
      this.panels[6].isShow = false; //调研可查看/下载报告
    } else {
      // 360测评&其它测评
      const evaluationIds = [
        "demographicsView",
        "demographics",
        "viewReportFactors",
      ];
      this.panels.forEach((e) => {
        e.isShow = evaluationIds.includes(e.id);
        if (e.id === "demographicsView") {
          e.title = "可查看/下载填答进度的权限";
        } else if (e.id === "demographics") {
          e.title = "可生成团队报告的权限";
        }
      });
    }
    this.permission = this.permissionService.isPermission();
    this.loadSubList();
    this.loadSomeData();
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  /**
   * 子管理员-搜索
   */
  searchUser() {
    this.subList.forEach((item) => {
      if (this.searchValue) {
        if (item.name.indexOf(this.searchValue) !== -1) {
          item.isShow = true;
        } else {
          item.isShow = false;
        }
      } else {
        item.isShow = true;
      }
      if (this.clickSunManagerId === item.userId && !item.isShow) {
        this.clickSunManagerId = "";
      }
    });
  }

  /**
   * 子管理员-获取数据
   */
  loadSubList() {
    this.api.getListSubManager(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        res.data.forEach((element) => {
          element.isShow = true;
          if (element.norms.length !== 0) {
            element.norms.forEach((ele) => {
              if (ele.normId) {
                ele.id = ele.normId;
                ele.type = "prismalNorms";
              } else if (ele.orgaztionSinkingAnalysis) {
                ele.id = ele.orgaztionSinkingAnalysis;
                ele.type = "orgaztionSinkingAnalysis";
              } else {
                ele.id = ele.internalNormType;
                ele.type = "internalNorms";
              }
            });
          }
        });
        this.subList = res.data;
      }
    });
  }

  /**
   * 获取组织，人口学数据
   */
  loadSomeData() {
    this.api.getSubPermissionContent(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        res.data.demographicTrees = _.cloneDeep(res.data.demographics);
        res.data.demographicTrees.forEach((element) => {
          element.isChecked = false;
        });
        this.formatter(res.data.demographicTrees);
        res.data.demographicsView = _.cloneDeep(res.data.demographicTrees);
        res.data.viewReportFactors = _.cloneDeep(res.data.demographicTrees);
        res.data.organizationTreesView = _.cloneDeep(
          res.data.organizationTrees
        );
        res.data.internalNorms.forEach((element) => {
          element.isChecked = false;
        });
        if (
          this.standardReportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM"
        ) {
          res.data.orgaztionSinkingAnalysiss.forEach((element) => {
            element.isChecked = false;
          });
        }

        res.data.prismaNorms.forEach((element) => {
          element.isChecked = false;
        });
        res.data.historyDataList.forEach((element) => {
          element.isChecked = false;
        });

        this.data = res.data;
        this.subUserIndeterminate();
      }
    });
  }

  /**
   * 格式化-人口学
   * @param data
   */
  formatter(data) {
    data.forEach((item) => {
      item.title = item.name.zh_CN;
      item.key = item.id;
      if (item.children && item.children.length !== 0) {
        item.isLeaf = false;
        this.formatter(item.children);
      } else {
        item.isLeaf = true;
      }
    });
  }

  /**
   * 点击子管理员
   * @param data
   * @param index
   */
  clickSubManager(data, index) {
    if (this.data) {
      this.clickSunManagerId = data.userId;
      this.currentSubIndex = index;
      this.subList[index].isSubManager = true;
      const initId = this.panels.filter((val) => val.isShow)[0].id;
      this.selectedId = initId;
      this.subUserIndeterminate();
      this.changeSubManager(data);
    }
  }

  /**
   * 右侧Empty显示
   */
  showRightEmpty() {
    let num = 0;

    this.panels.map((item) => {
      if (item.list.length > 0) num++;
    });
    if (num > 0) {
      this.showTips = true;
    } else {
      this.showTips = false;
    }
  }

  /**
   * 子管理员-全选
   * @param e
   */
  subUserAllChange(e) {
    //
    this.userCheckedIndeterminate = false;
    this.checkAlluser = e;
    this.subList.forEach((item) => {
      item.isSubManager = e;
    });
  }

  /**
   * 子管理员-多选
   * @param e
   * @param data
   * @param index
   */
  SubManagerChange(e, data, index) {
    if (this.data) {
      this.clickSunManagerId = data.userId;
      this.currentSubIndex = index;
      this.subList[index].isSubManager = e;
      const initId = this.panels.filter((val) => val.isShow)[0].id;
      this.selectedId = initId;
      this.subUserIndeterminate();
      this.changeSubManager(data);
    }
  }

  /**
   * 子管理员-全选状态
   */
  subUserIndeterminate() {
    let num = 0;
    this.subList.map((item) => {
      if (item.isSubManager) num++;
    });
    if (num === 0) {
      this.userCheckedIndeterminate = false;
      this.checkAlluser = false;
    }
    if (num > 0 && num < this.subList.length) {
      this.userCheckedIndeterminate = true;
      this.checkAlluser = false;
    }
    if (num === this.subList.length) {
      this.userCheckedIndeterminate = true;
      this.checkAlluser = true;
    }
  }

  /**
   * 关闭
   */
  close() {
    this.drawerRef.close();
  }

  /**
   * 修改子管理员 重置数据
   * @param data
   */
  changeSubManager(data) {
    let tree;
    let tree1;
    let factorsTree;
    let answerFactorsTree;
    let viewReportFactorsTree;
    setTimeout(() => {
      tree = this.nzOrgTreeComponent.getTreeNodes();
      tree1 = this.nzOrgViewTreeComponent.getTreeNodes();
      factorsTree = this.nzFactorsComponent.getTreeNodes();
      answerFactorsTree = this.nzAnswerFactorsComponent.getTreeNodes();
      viewReportFactorsTree = this.nzViewReportFactorsComponent.getTreeNodes();
      tree.forEach((element) => {
        this.nodeisVirtual(element);
      });
      tree1.forEach((element) => {
        this.nodeisVirtual(element);
      });
      // factorsTree.forEach(element => {
      //   this.nodeisVirtual(element)
      // });
      // answerFactorsTree.forEach(element => {
      //   this.nodeisVirtual(element)
      // });
      // 组织
      if (data.organizations.length !== 0) {
        // this.clearTree(tree)
        tree.forEach((element) => {
          this.selectTree(element, data.organizations);
        });
      } else {
        tree.forEach((node) => {
          this.clearTree(node);
        });
      }
      // 组织进度
      if (data.answerProgressOrganizations.length !== 0) {
        tree1.forEach((element) => {
          this.selectTree(element, data.answerProgressOrganizations);
        });
      } else {
        tree1.forEach((node) => {
          this.clearTree(node);
        });
      }
      // 人口
      if (data.factors.length !== 0) {
        factorsTree.forEach((element) => {
          this.selectDemoTree(element, data.factors);
        });
      } else {
        factorsTree.forEach((node) => {
          this.clearTree(node);
        });
      }
      // 人口view
      if (data.answerProgressFactors.length !== 0) {
        answerFactorsTree.forEach((element) => {
          this.selectDemoTree(element, data.answerProgressFactors);
        });
      } else {
        answerFactorsTree.forEach((node) => {
          this.clearTree(node);
        });
      }
      // 可查看/下载报告
      if (data.viewReportFactors.length !== 0) {
        viewReportFactorsTree.forEach((element) => {
          this.selectDemoTree(element, data.viewReportFactors);
        });
      } else {
        viewReportFactorsTree.forEach((node) => {
          this.clearTree(node);
        });
      }

      // 常模
      let normCount = 0;
      this.data.internalNorms.forEach((element) => {
        let index = _.findIndex(data.norms, function(o) {
          return element.id === o.internalNormType;
        });
        if (index !== -1) {
          element.isChecked = true;
          normCount++;
        } else {
          element.isChecked = false;
        }
      });
      if (this.standardReportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
        this.data.orgaztionSinkingAnalysiss.forEach((element) => {
          let index = _.findIndex(data.norms, function(o) {
            return element.id === o.orgaztionSinkingAnalysis;
          });
          if (index !== -1) {
            element.isChecked = true;
            normCount++;
          } else {
            element.isChecked = false;
          }
        });
      }
      this.data.prismaNorms.forEach((element) => {
        let length =
          this.data.prismaNorms.length + this.data.internalNorms.length;
        if (
          this.standardReportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM"
        ) {
          length = length + this.data.orgaztionSinkingAnalysiss.length || 0;
        }
        let index = _.findIndex(data.norms, function(o) {
          return element.id === o.normId;
        });
        if (index !== -1) {
          element.isChecked = true;
          normCount++;
        } else {
          element.isChecked = false;
        }

        if (normCount === length) {
          this.normCheckedAll = true;
          this.normCheckedIndeterminate = false;
        }
        if (normCount > 0 && normCount < length) {
          this.normCheckedAll = false;
          this.normCheckedIndeterminate = true;
        }
        if (normCount === 0) {
          this.normCheckedAll = false;
          this.normCheckedIndeterminate = false;
        }
      });
      let hisCount = 0;
      this.data.historyDataList.forEach((element) => {
        let length = this.data.historyDataList.length;
        let index = _.findIndex(data.historys, function(o) {
          return element.id === o.id;
        });
        if (index !== -1) {
          element.isChecked = true;
          hisCount++;
        } else {
          element.isChecked = false;
        }
        if (hisCount === length) {
          this.historyCheckedAll = true;
          this.historyCheckedIndeterminate = false;
        }
        if (hisCount > 0 && hisCount < length) {
          this.historyCheckedAll = false;
          this.historyCheckedIndeterminate = true;
        }
        if (hisCount === 0) {
          this.historyCheckedAll = false;
          this.historyCheckedIndeterminate = false;
        }
      });
      this.panels[0].list = this.viewData(data.answerProgressOrganizations); // 可查看填答进度的部门
      this.panels[1].list = this.viewData(data.organizations); // 报考查看部门
      this.panels[2].list = this.viewData(data.factors); // 报告分析因子
      this.panels[3].list = this.viewData(data.answerProgressFactors); // 可查看填答进度的分析因子
      this.panels[4].list = this.viewData(data.norms); // 报告内外部常模
      this.panels[5].list = this.viewData(data.historys); // 报告历史对比
      this.panels[6].list = this.viewData(data.viewReportFactors); // 报告历史对比
      this.showRightEmpty();
      // todo:bug-8241 全选清除
      this.demoCheckedAll = false;
      this.demoViewCheckedAll = false;
      this.changeCurrentSelectAll();
    });
  }

  /**
   * 右侧因子数据格式化
   * @param list
   * @returns
   */
  viewData(list: any[]) {
    let arr = [];
    list.map((item) => {
      arr.push({
        id: item.id || item.normId || item.internalNormType,
        name: item.name.zh_CN || item.name,
        type: item.type,
      });
    });
    return arr;
  }

  /**
   * 选中数据回显-组织/人员
   * @param node
   * @param selectList
   */
  selectTree(node: NzTreeNode, selectList: any[]) {
    let index = _.findIndex(selectList, function(o) {
      return node.key === o.id;
    });
    if (index !== -1) {
      if (!node.origin.isVirtual) {
        node.isChecked = true;
        node.isDisabled = false;
      }
      if (!node.isLeaf) {
        node.children.forEach((node1) => {
          this.nodeDisable(node1, true);
        });
      }
    } else {
      if (!node.origin.isVirtual) {
        node.isChecked = false;
        node.isDisabled = false;
      }
      if (!node.isLeaf) {
        node.children.forEach((node1) => {
          this.selectTree(node1, selectList);
        });
      }
    }
  }

  /**
   * 选中数据回显-人口学因子
   * @param node
   * @param selectList
   */
  selectDemoTree(node: NzTreeNode, selectList: any[]) {
    let index = _.findIndex(selectList, function(o) {
      return node.key === o.id;
    });
    if (index !== -1 && !node.origin.isVirtual) {
      node.isChecked = true;
    } else {
      node.isChecked = false;
    }
    if (!node.isLeaf) {
      let allChildrenChecked = true;
      let anyChildChecked = false;
      node.children.forEach((childNode) => {
        this.selectDemoTree(childNode, selectList);
        if (!childNode.isChecked) {
          allChildrenChecked = false;
        } else {
          anyChildChecked = true;
        }
      });
      if (allChildrenChecked) {
        node.isChecked = true;
        node.isHalfChecked = false; // 所有子节点都选中，父节点不显示为部分选中状态
      } else if (anyChildChecked) {
        node.isHalfChecked = true; // 部分子节点选中，父节点显示为部分选中状态
      } else {
        node.isChecked = false;
        node.isHalfChecked = false; // 没有子节点选中，父节点不显示为部分选中状态
      }
    }
  }

  /**
   * 组织架构禁用
   * @param node
   * @param isDis
   */
  nodeDisable(node: NzTreeNode, isDis: boolean) {
    if (!node.origin.isVirtual) {
      node.isChecked = false;
      node.isDisabled = isDis;
    }
    if (!node.isLeaf) {
      node.children.forEach((node1) => {
        this.nodeDisable(node1, isDis);
      });
    }
  }

  /**
   * 组织架构禁用
   * @param node
   * @returns
   */
  nodeisVirtual(node: NzTreeNode) {
    if (node.origin.isVirtual) {
      node.isChecked = false;
      node.isDisabled = true;
    }
    if (node.isLeaf) return;
    node.children.forEach((item) => {
      this.nodeisVirtual(item);
    });
  }

  /**
   * 清空组织架构
   * @param node
   */
  clearTree(node: NzTreeNode) {
    if (!node.origin.isVirtual) {
      node.isChecked = false;
      node.isHalfChecked = false; // 清空时需要将 isHalfChecked 属性设置为 false
      node.isDisabled = false;
    }
    if (node.children.length !== 0) {
      node.children.forEach((childNode) => {
        if (childNode.isHalfChecked) {
          childNode.isHalfChecked = false; // 如果子节点是部分选中状态，则将其设置为未选中状态
        } else if (childNode.isChecked || !childNode.isHalfChecked) {
          this.clearTree(childNode); // 如果子节点是选中状态或者未选中状态，则继续递归处理它的子节点
        }
      });
    }
  }

  /**
   * 组织 org
   * @param event
   * @returns
   */
  nzEvent(event: NzFormatEmitEvent): void {
    if (event.node.isDisabled) {
      return;
    }
    if (this.selectedId === "org") {
      this.selectedLevel = null;
    } else if (this.selectedId === "orgView") {
      this.selectedViewLevel = null;
    }
    // select children
    let currentNode: any = event.node.origin;
    let isSelected: boolean = currentNode.checked;
    let map = {};
    this.selectChild(currentNode, isSelected, map);
    let allNodes: NzTreeNode[];
    if (this.selectedId === "org") {
      allNodes = this.nzOrgTreeComponent.getTreeNodes();
    } else if (this.selectedId === "orgView") {
      allNodes = this.nzOrgViewTreeComponent.getTreeNodes();
    } else if (this.selectedId === "demographics") {
      allNodes = this.nzFactorsComponent.getTreeNodes();
    } else if (this.selectedId === "demographicsView") {
      allNodes = this.nzAnswerFactorsComponent.getTreeNodes();
    } else if (this.selectedId === "viewReportFactors") {
      allNodes = this.nzViewReportFactorsComponent.getTreeNodes();
    }
    allNodes.forEach((n: NzTreeNode) => {
      this.setSelectState(n, isSelected, map);
    });

    // update displaying selected nodes
    let nodeList: NzTreeNode[];
    if (this.selectedId === "org") {
      nodeList = this.nzOrgTreeComponent.getCheckedNodeList();
    } else if (this.selectedId === "orgView") {
      nodeList = this.nzOrgViewTreeComponent.getCheckedNodeList();
    } else if (this.selectedId === "demographics") {
      nodeList = this.nzFactorsComponent.getCheckedNodeList();
    } else if (this.selectedId === "demographicsView") {
      nodeList = this.nzAnswerFactorsComponent.getCheckedNodeList();
    } else if (this.selectedId === "viewReportFactors") {
      nodeList = this.nzViewReportFactorsComponent.getCheckedNodeList();
    }

    let tmp: any[] = [];
    for (let index = 0; index < nodeList.length; index++) {
      const element = nodeList[index].origin;
      tmp.push({ id: element.key, name: element.title });
    }
    this.selectedNodes = tmp;
    this.addColl();
  }

  /**
   * 报告分析因子change
   * @param event
   */
  nzEventDemographic(event: NzFormatEmitEvent): void {
    let nodeList: NzTreeNode[] = this.getNodeList();
    let isAllChecked = true;
    let tmp: any[] = [];
    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        node1.children.map((node2) => {
          if (node2.isChecked) {
            tmp.push({ id: node2.key, name: node2.title });
          } else {
            isAllChecked = false;
          }
        });
      }
    });

    if (this.selectedId === "demographics") {
      this.demoCheckedAll = isAllChecked;
      if (!isAllChecked && tmp.length !== 0) {
        this.demoCheckedIndeterminate = true;
      } else {
        this.demoCheckedIndeterminate = false;
      }
    } else if (this.selectedId === "demographicsView") {
      this.demoViewCheckedAll = isAllChecked;
      if (!isAllChecked && tmp.length !== 0) {
        this.demoViewCheckedIndeterminate = true;
      } else {
        this.demoViewCheckedIndeterminate = false;
      }
    } else if (this.selectedId === "viewReportFactors") {
      this.viewReportFactorsCheckedAll = isAllChecked;
      if (!isAllChecked && tmp.length !== 0) {
        this.viewReportFactorsCheckedIndeterminate = true;
      } else {
        this.viewReportFactorsCheckedIndeterminate = false;
      }
    }
    this.selectedNodes = tmp;
    this.addColl();
  }

  /**
   * 展开
   * @param event
   */
  expanded(event: NzFormatEmitEvent): void {
    if (event.node.isExpanded) {
      event.node.isExpanded = false;
    } else {
      event.node.isExpanded = true;
    }
  }

  /**
   * get should select map
   * @param node
   * @param isSelected
   * @param map
   */
  selectChild(node: any, isSelected: boolean, map: any) {
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        map[child.key] = 1;
        this.selectChild(child, isSelected, map);
      }
    }
  }

  /**
   * set select node by map
   * @param node
   * @param isSelected
   * @param map
   */
  setSelectState(node: NzTreeNode, isSelected: boolean, map: any) {
    if (map[node.key] === 1) {
      if (node.origin.isVirtual) {
        node.isDisabled = true;
        node.isChecked = false;
      } else {
        node.isDisabled = isSelected;
        // node.setSelected(isSelected);
        node.isChecked = false;
      }
    }
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        this.setSelectState(child, isSelected, map);
      }
    }
  }

  /**
   * 团队报告 选中不同级别后操作
   * @param num
   */
  checkDepartment(num) {
    let allNodes;
    if (this.selectedId === "org") {
      allNodes = this.nzOrgTreeComponent.getTreeNodes();
      this.checkedKeys = [];
      this.expandedNodes = [];
    } else {
      allNodes = this.nzOrgViewTreeComponent.getTreeNodes();
      this.checkedKeysView = [];
      this.expandedNodesView = [];
    }

    let timeHandle = setTimeout(() => {
      if (num !== 0) {
        allNodes.forEach((item) => {
          this.celDepartment(item);
          this.chooseDepartment(item, num);
        });
      } else {
        allNodes.forEach((item) => {
          this.celDepartment(item);
          this.chooseIsLeat(item);
        });
      }
      let nodeList: NzTreeNode[];
      if (this.selectedId === "org") {
        nodeList = this.nzOrgTreeComponent.getCheckedNodeList();
      } else {
        nodeList = this.nzOrgViewTreeComponent.getCheckedNodeList();
      }
      let tmp: any[] = [];
      for (let index = 0; index < nodeList.length; index++) {
        const element = nodeList[index].origin;
        tmp.push({ id: element.key, name: element.title });
      }
      this.selectedNodes = tmp;

      this.addColl();
      clearTimeout(timeHandle);
    });
  }

  /**
   * 节点禁用处理
   * @param node
   */
  celDepartment(node) {
    if (!node.origin.isVirtual) {
      node.isDisabled = false;
    }
    node.children.forEach((element) => {
      this.celDepartment(element);
    });
  }

  /**
   * 全选
   * @param node
   */
  chooseIsLeat(node) {
    if (node.isLeaf) {
      if (!node.origin.isVirtual) {
        if (this.selectedId === "org") {
          this.checkedKeys.push(node.key);
        } else {
          this.checkedKeysView.push(node.key);
        }
        node.isChecked = true;
      }
    }
    if (node.children.length !== 0) {
      node.children.forEach((element) => {
        this.chooseIsLeat(element);
      });
    }
  }

  /**
   * 节点选中
   * @param node
   * @param num
   */
  chooseDepartment(node, num) {
    if (num > 0) {
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.chooseDepartment(element, num - 1);
        });
      }
    } else if (num === 0) {
      if (!node.origin.isVirtual) {
        this.checkedKeys.push(node.key);
        node.isChecked = true;
      }
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.chooseDepartment(element, num - 1);
        });
      }
    } else if (num < 0) {
      if (!node.origin.isVirtual) {
        node.isDisabled = true;
      }
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.chooseDepartment(element, num - 1);
        });
      }
    }
  }

  // 人口标签 demo、
  // 报告分析因子
  demoCheckedAll: boolean = false;
  demoCheckedIndeterminate: boolean = false;
  // 可查看填答进度的分析因子
  demoViewCheckedAll: boolean = false;
  demoViewCheckedIndeterminate: boolean = false;
  // 可查看/下载报告
  viewReportFactorsCheckedAll: boolean = false;
  viewReportFactorsCheckedIndeterminate: boolean = false;

  /**
   * 全选-人口学相关
   * @param e
   */
  demoChangeAll(e) {
    let tmp: any[] = [];
    const nodeList: NzTreeNode[] = this.getNodeList();
    nodeList.forEach((node1) => {
      if (node1.children.length !== 0) {
        node1.children.forEach((node2) => {
          node2.isChecked = e;
          if (e) {
            tmp.push({ id: node2.key, name: node2.title });
          }
        });
        node1.isHalfChecked = false;
        node1.isChecked = e;
      }
    });
    if (this.selectedId === "demographics") {
      if (e) {
        this.demoCheckedIndeterminate = false;
        this.demoCheckedAll = true;
      } else {
        this.demoCheckedIndeterminate = false;
        this.demoCheckedAll = false;
      }
    } else if (this.selectedId === "demographicsView") {
      if (e) {
        this.demoViewCheckedIndeterminate = false;
        this.demoViewCheckedAll = true;
      } else {
        this.demoViewCheckedIndeterminate = false;
        this.demoViewCheckedAll = false;
      }
    } else if (this.selectedId === "viewReportFactors") {
      if (e) {
        this.viewReportFactorsCheckedIndeterminate = false;
        this.viewReportFactorsCheckedAll = true;
      } else {
        this.viewReportFactorsCheckedIndeterminate = false;
        this.viewReportFactorsCheckedAll = false;
      }
    }

    this.selectedNodes = tmp;

    this.addColl();
  }

  /**
   * 获取相应tree数据
   * @returns
   */
  getNodeList() {
    if (this.selectedId === "org") {
      return this.nzOrgTreeComponent.getCheckedNodeList();
    } else if (this.selectedId === "orgView") {
      return this.nzOrgViewTreeComponent.getCheckedNodeList();
    } else if (this.selectedId === "demographics") {
      return this.nzFactorsComponent.getTreeNodes();
    } else if (this.selectedId === "demographicsView") {
      return this.nzAnswerFactorsComponent.getTreeNodes();
    } else if (this.selectedId === "viewReportFactors") {
      return this.nzViewReportFactorsComponent.getTreeNodes();
    }
  }

  /**
   * 人口学数据改变
   * @param e
   */
  demoChange(e) {
    let tmp: any[] = [];
    let all;
    if (this.selectedId === "demographics") {
      this.demoCheckedIndeterminate = false;
      this.demoCheckedAll = false;
      this.data.demographics.forEach((element) => {
        if (element.isChecked) {
          this.demoCheckedIndeterminate = true;
          this.demoCheckedAll = false;
          tmp.push({ id: element.id, name: element.name.zh_CN });
        }
      });
      all = this.data.demographics.every((item) => item.isChecked);
      if (all) {
        this.demoCheckedIndeterminate = false;
        this.demoCheckedAll = true;
      }
    } else if (this.selectedId === "demographicsView") {
      this.demoViewCheckedIndeterminate = false;
      this.demoViewCheckedAll = false;
      this.data.demographicsView.forEach((element) => {
        if (element.isChecked) {
          this.demoViewCheckedIndeterminate = true;
          this.demoViewCheckedAll = false;
          tmp.push({ id: element.id, name: element.name.zh_CN });
        }
      });
      all = this.data.demographicsView.every((item) => item.isChecked);
      if (all) {
        this.demoViewCheckedIndeterminate = false;
        this.demoViewCheckedAll = true;
      }
    } else if (this.selectedId === "viewReportFactors") {
      this.viewReportFactorsCheckedIndeterminate = false;
      this.viewReportFactorsCheckedAll = false;
      this.data.viewReportFactors.forEach((element) => {
        if (element.isChecked) {
          this.viewReportFactorsCheckedIndeterminate = true;
          this.viewReportFactorsCheckedAll = false;
          tmp.push({ id: element.id, name: element.name.zh_CN });
        }
      });
      all = this.data.demographicsView.every((item) => item.isChecked);
      if (all) {
        this.viewReportFactorsCheckedIndeterminate = false;
        this.viewReportFactorsCheckedAll = true;
      }
    }

    this.selectedNodes = tmp;

    this.addColl();
  }

  // 常模 norm
  normCheckedAll: boolean = false;
  normCheckedIndeterminate: boolean = false;

  /**
   * 常模全选
   * @param e
   */
  normChangeAll(e) {
    let tmp: any[] = [];

    this.data.internalNorms.forEach((element) => {
      element.isChecked = e;
      if (e)
        tmp.push({
          id: element.id,
          name: element.name.zh_CN,
          type: "internalNorms",
        });
    });
    if (this.standardReportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
      this.data.orgaztionSinkingAnalysiss.forEach((element) => {
        element.isChecked = e;
        if (e)
          tmp.push({
            id: element.id,
            name: element.name.zh_CN,
            type: "orgaztionSinkingAnalysis",
          });
      });
    }
    this.data.prismaNorms.forEach((element) => {
      element.isChecked = e;
      if (e)
        tmp.push({
          id: element.id,
          name: element.name.zh_CN,
          type: "prismaNorms",
        });
    });

    if (e) {
      this.normCheckedAll = true;
      this.normCheckedIndeterminate = false;
    } else {
      this.normCheckedAll = false;
      this.normCheckedIndeterminate = false;
    }

    this.selectedNodes = tmp;

    this.addColl();
  }

  /**
   * 常模-选中改变
   * @param e
   */
  normChange(e) {
    let tmp: any[] = [];
    this.normCheckedIndeterminate = false;
    this.normCheckedAll = false;
    this.data.internalNorms.forEach((element) => {
      if (element.isChecked) {
        this.normCheckedIndeterminate = true;
        this.normCheckedAll = false;
        tmp.push({
          id: element.id,
          name: element.name.zh_CN,
          type: "internalNorms",
        });
      }
    });

    if (this.standardReportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
      this.data.orgaztionSinkingAnalysiss.forEach((element) => {
        if (element.isChecked) {
          this.normCheckedIndeterminate = true;
          this.normCheckedAll = false;
          tmp.push({
            id: element.id,
            name: element.name.zh_CN,
            type: "orgaztionSinkingAnalysis",
          });
        }
      });
    }
    this.data.prismaNorms.forEach((element) => {
      if (element.isChecked) {
        this.normCheckedIndeterminate = true;
        this.normCheckedAll = false;
        tmp.push({
          id: element.id,
          name: element.name.zh_CN,
          type: "prismaNorms",
        });
      }
    });

    let all1 = this.data.internalNorms.every((item) => item.isChecked);
    let all2 = this.data.prismaNorms.every((item) => item.isChecked);
    let all3 = true;
    if (this.standardReportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
      all3 = this.data.orgaztionSinkingAnalysiss.every(
        (item) => item.isChecked
      );
    }
    if (all1 && all2 && all3) {
      this.normCheckedIndeterminate = false;
      this.normCheckedAll = true;
    }

    this.selectedNodes = tmp;

    this.addColl();
  }

  // history
  historyCheckedAll: boolean = false;
  historyCheckedIndeterminate: boolean = false;
  /**
   * 历史对比全选
   * @param e
   */
  historyChangeAll(e) {
    let tmp: any[] = [];

    this.data.historyDataList.forEach((element) => {
      element.isChecked = e;
      if (e) tmp.push({ id: element.id, name: element.name.zh_CN });
    });

    if (e) {
      this.historyCheckedAll = true;
      this.historyCheckedIndeterminate = false;
    } else {
      this.historyCheckedAll = false;
      this.historyCheckedIndeterminate = false;
    }

    this.selectedNodes = tmp;

    this.addColl();
  }

  /**
   * 历史对比-选中改变
   * @param e
   */
  historyChange(e) {
    let tmp: any[] = [];
    this.historyCheckedIndeterminate = false;
    this.historyCheckedAll = false;
    this.data.historyDataList.forEach((element) => {
      if (element.isChecked) {
        this.historyCheckedIndeterminate = true;
        this.historyCheckedAll = false;
        tmp.push({ id: element.id, name: element.name.zh_CN });
      }
    });

    let all = this.data.historyDataList.every((item) => item.isChecked);
    if (all) {
      this.historyCheckedIndeterminate = false;
      this.historyCheckedAll = true;
    }

    this.selectedNodes = tmp;

    this.addColl();
  }

  /**
   * 添加至右侧树
   */
  addColl() {
    this.panels.forEach((element) => {
      if (this.selectedId === element.id) {
        element.list = this.selectedNodes;
        element.active = true;
      }
    });
    this.subList[this.currentSubIndex].isUpdate = true;
    this.panels.map((item) => {
      let arr = [];
      let arr1 = [];
      let arr2 = [];
      if (item.id !== "norm") {
        item.list.map((item) => {
          arr.push(item);
        });
      } else {
        item.list.map((item) => {
          if (item.type === "internalNorms") {
            arr.push(item);
          } else if (item.type === "orgaztionSinkingAnalysis") {
            arr2.push(item);
          } else {
            arr1.push(item);
          }
        });
      }
      if (item.id === "org") {
        this.subList[this.currentSubIndex].organizations = arr;
      }
      if (item.id === "orgView") {
        this.subList[this.currentSubIndex].answerProgressOrganizations = arr;
      }
      if (item.id === "demographics") {
        this.subList[this.currentSubIndex].factors = arr;
      }
      if (item.id === "demographicsView") {
        this.subList[this.currentSubIndex].answerProgressFactors = arr;
      }
      if (item.id === "norm") {
        this.subList[this.currentSubIndex].interNorms = arr;
        this.subList[this.currentSubIndex].prismaNorms = arr1;

        if (
          this.standardReportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM"
        ) {
          this.subList[this.currentSubIndex].orgaztionSinkingAnalysis = arr2;
        }
        this.subList[this.currentSubIndex].norms = [...arr, ...arr2, ...arr1];
      }
      if (item.id === "history") {
        this.subList[this.currentSubIndex].historys = arr;
      }
      if (item.id === "viewReportFactors") {
        this.subList[this.currentSubIndex].viewReportFactors = arr;
      }
    });
    this.showRightEmpty();
  }

  /**
   * 提交数据map处理
   * @param list
   * @returns
   */
  mapParams(list: any[]) {
    let ids = [];
    list.map((item) => {
      ids.push({ id: item.id });
    });
    return ids;
  }

  /**
   * 删除sub绑定数据
   * @param typeId
   * @param delId
   * @param panIndex
   * @param index
   */
  delete(typeId: string, delId: string, panIndex: number, index: number) {
    this.panels[panIndex].list.splice(index, 1); // 删除右侧
    if (typeId === "org") {
      let tree = this.nzOrgTreeComponent.getTreeNodes();
      this.subList[this.currentSubIndex].organizations = this.panels[
        panIndex
      ].list; // 删除sub绑定数据
      tree.forEach((node) => {
        this.delTree(node, delId);
        this.changeCurrentSelectAll();
      });
    }
    if (typeId === "orgView") {
      let tree = this.nzOrgViewTreeComponent.getTreeNodes();
      this.subList[
        this.currentSubIndex
      ].answerProgressOrganizations = this.panels[panIndex].list; // 删除sub绑定数据
      tree.forEach((node) => {
        this.delTree(node, delId);
        this.changeCurrentSelectAll();
      });
    }
    if (typeId === "demographics") {
      let tree = this.nzFactorsComponent.getTreeNodes();
      this.subList[this.currentSubIndex].factors = this.panels[panIndex].list; // 删除sub绑定数据
      tree.forEach((node) => {
        this.delTree(node, delId);
        this.changeCurrentSelectAll();
      });
    }
    if (typeId === "demographicsView") {
      let tree = this.nzAnswerFactorsComponent.getTreeNodes();
      this.subList[this.currentSubIndex].answerProgressFactors = this.panels[
        panIndex
      ].list; // 删除sub绑定数据
      tree.forEach((node) => {
        this.delTree(node, delId);
        this.changeCurrentSelectAll();
      });
    }
    if (typeId === "norm") {
      this.subList[this.currentSubIndex].norms = this.panels[panIndex].list; // 删除sub绑定数据
      this.subList[this.currentSubIndex].interNorms = this.mapNorm(
        this.panels[panIndex].list,
        "interNorms"
      ); // 删除sub绑定数据
      this.subList[this.currentSubIndex].prismaNorms = this.mapNorm(
        this.panels[panIndex].list,
        "prismaNorms"
      ); // 删除sub绑定数据

      if (this.standardReportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
        this.subList[
          this.currentSubIndex
        ].orgaztionSinkingAnalysis = this.mapNorm(
          this.panels[panIndex].list,
          "orgaztionSinkingAnalysis"
        ); // 删除sub绑定数据
      }
      // this.data.internalNorms.forEach(element => {
      //   if(element.id === delId) {
      //     element.isChecked = false
      //   }
      //   if(element.isChecked) length++
      // });

      // this.data.prismaNorms.forEach(element => {
      //   if(element.id === delId) {
      //     element.isChecked = false
      //   }
      //   if(element.isChecked) length++
      // });
      let length = 0;
      let length1 = 0;
      let length2 = 0;
      length = this.getlist(this.data.internalNorms, length);
      length1 = this.getlist(this.data.prismaNorms, length1);

      if (this.standardReportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
        length2 = this.getlist(this.data.orgaztionSinkingAnalysiss, length2);
      }
      let andLength = length + length1 + length2;
      if (andLength === 0) {
        this.normCheckedAll = false;
        this.normCheckedIndeterminate = false;
      } else {
        this.normCheckedAll = false;
        this.normCheckedIndeterminate = true;
      }
    }
    if (typeId === "history") {
      this.subList[this.currentSubIndex].historys = this.panels[panIndex].list; // 删除sub绑定数据
      let length = 0;
      // this.data.historys.forEach(element => {
      //   if(element.id === delId) {
      //     element.isChecked = false
      //   }
      //   if(element.isChecked) length++
      // });
      length = this.getlist(this.data.historys, length);

      if (length === 0) {
        this.historyCheckedAll = false;
        this.historyCheckedIndeterminate = false;
      } else {
        this.historyCheckedAll = false;
        this.historyCheckedIndeterminate = true;
      }
    }
    if (typeId === "viewReportFactors") {
      let tree = this.nzViewReportFactorsComponent.getTreeNodes();
      this.subList[this.currentSubIndex].viewReportFactors = this.panels[
        panIndex
      ].list; // 删除sub绑定数据
      tree.forEach((node) => {
        this.delTree(node, delId);
        this.changeCurrentSelectAll();
      });
    }
  }

  /**
   * 数据吃力
   * @param list
   * @param delId
   * @returns
   */
  getlist(list: any[], delId) {
    let length = 0;
    list.forEach((element) => {
      if (element.id === delId) {
        element.isChecked = false;
      }
      if (element.isChecked) length++;
    });
    return length;
  }

  /**
   * 常模map
   * @param list
   * @param type
   * @returns
   */
  mapNorm(list: any[], type: string) {
    let arr = [];
    list.map((item) => {
      if (item.type === type) arr.push({ id: item.id });
    });
    return arr;
  }

  /**
   * 删除tree
   * @param node
   * @param delId
   */
  delTree(node: NzTreeNode, delId) {
    if (!node.origin.isVirtual) {
      if (node.key === delId) {
        node.isChecked = false;
        if (!node.isLeaf) {
          node.children.forEach((node1) => {
            this.nodeDisable(node1, false);
          });
        }
        if (node.parentNode) {
          if (
            node.parentNode.origin.children.filter((val) => val.checked)
              .length > 0
          ) {
            node.parentNode.isChecked = false;
            node.parentNode.isHalfChecked = true;
          } else {
            node.parentNode.isChecked = false;
            node.parentNode.isHalfChecked = false;
          }
        }
      } else {
        node.children.forEach((node1) => {
          this.delTree(node1, delId);
        });
      }
    } else {
      if (!node.isLeaf) {
        node.children.forEach((node1) => {
          this.delTree(node1, delId);
        });
      }
    }
  }

  /**
   * 保存
   */
  save() {
    let params = [];

    this.subList.map((item) => {
      if (item.isSubManager) {
        // 勾选
        let normsss = [];
        item.norms.map((item_) => {
          if (item_.type === "internalNorms") {
            normsss.push({
              internalNormType: item_.id,
            });
          } else if (item_.type === "orgaztionSinkingAnalysis") {
            normsss.push({
              orgaztionSinkingAnalysis: item_.id,
            });
          } else {
            normsss.push({
              normId: item_.id,
            });
          }
        });
        params.push({
          userId: item.userId,
          name: item.name,
          projectId: this.projectId,
          isSubManager: item.isSubManager,
          isViewAnswerProgressOrganization:
            item.answerProgressOrganizations.length !== 0,
          isViewAnswerProgressFactor: item.answerProgressFactors.length !== 0,
          answerProgressOrganizations: this.mapParams(
            item.answerProgressOrganizations
          ),
          answerProgressFactors: this.mapParams(item.answerProgressFactors),
          isSelectOrganization: item.organizations.length !== 0,
          organizations: this.mapParams(item.organizations),
          isSelectFactor: item.factors.length !== 0,
          factors: this.mapParams(item.factors),
          isSelectNorm: normsss.length !== 0,
          norms: normsss,
          isSelectHistory: item.historys != 0,
          historys: this.mapParams(item.historys),
          isViewReportFactor: item.viewReportFactors.length !== 0,
          viewReportFactors: this.mapParams(item.viewReportFactors),
        });
      }
    });
    this.isLoading = true;
    this.api.creatSubManager(this.projectId, params).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("修改成功");
        // this.NzModalRef.triggerOk();
        this.drawerRef.close(true);
      }
      this.isLoading = false;
    });
  }

  /**
   * 当前全选的状态
   */
  changeCurrentSelectAll(): void {
    let nodeList: NzTreeNode[] = this.getNodeList();
    let isAllChecked = true;
    let tmp: any[] = [];
    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        node1.children.map((node2) => {
          if (node2.isChecked) {
            tmp.push({ id: node2.key, name: node2.title });
          } else {
            isAllChecked = false;
          }
        });
      } else {
        isAllChecked = false;
      }
    });
    if (this.selectedId === "demographics") {
      this.demoCheckedAll = isAllChecked;
      if (!isAllChecked && tmp.length !== 0) {
        this.demoCheckedIndeterminate = true;
      } else {
        this.demoCheckedIndeterminate = false;
      }
    } else if (this.selectedId === "demographicsView") {
      this.demoViewCheckedAll = isAllChecked;
      if (!isAllChecked && tmp.length !== 0) {
        this.demoViewCheckedIndeterminate = true;
      } else {
        this.demoViewCheckedIndeterminate = false;
      }
    } else if (this.selectedId === "viewReportFactors") {
      this.viewReportFactorsCheckedAll = isAllChecked;
      if (!isAllChecked && tmp.length !== 0) {
        this.viewReportFactorsCheckedIndeterminate = true;
      } else {
        this.viewReportFactorsCheckedIndeterminate = false;
      }
    }
  }

  /**
   * 活动详情（测评，360，调研） -> 子管理员 ->  //导入子管理员操作
   *
   * 下载中有页面遮罩，不支持用户重复操作
   *
   * 下载完成，导出单个Excel文件
   *
   * 下载失败，文字提示“下载失败”，不需要用户确认。
   *
   * @param item
   */
  importSubManager = (item: UploadXHRArgs) => {
    const isUpdata =
      item.file.type ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      item.file.type === "application/vnd.ms-excel";

    if (!isUpdata) {
      // return this.msg.error("上传文件格式应为excel");
      return this.customMsg.open("error", "上传文件格式应为excel");
    }

    const formData = new FormData();
    formData.append("excel", item.file as any);

    // 前面有操作在操作中，不能支持此操作，需等前面操作完成后。
    if (this.isImporting) {
      this.customMsg.open("error", "有文件正在导入中，请稍后再试");
      return false;
    }
    // 操作开始，设置loading遮罩状态。避免用户进行其他操作，显性提醒用户此操作在进行中，还未结束。
    this.isImporting = true;

    // 掉service进行导入
    return this.api.importSubManager(formData, this.projectId).subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          // 服务端操作完成，取消置loading遮罩状态。显性让用户知道此操完成了。
          this.isImporting = false;

          this.msg.success("导入子管理员成功");
          this.loadSubList();
          this.loadSomeData();
          this.routerSubscriptionFun();
        } else {
          // 服务端操作失败，取消置loading遮罩状态。
          this.isImporting = false;

          // 为什么大于10000的错误不显示？？
          if (res.result.code < 10000) {
            this.customMsg.open("error", res.result.message);
            this.isImporting = false;
          }
        }
      },
      (err) => {
        // 服务端操作失败，取消置loading遮罩状态。
        this.isImporting = false;

        item.onError!(err, item.file!);
      }
    );
  };

  exportSubManager() {
    // 前面有操作在操作中，不能支持此操作，需等前面操作完成后。
    if (this.isExporting) {
      this.customMsg.open("error", "有文件正在导出中，请稍后再试");
      return false;
    }

    // 操作开始，设置loading遮罩状态。避免用户进行其他操作，显性提醒用户此操作在进行中，还未结束。
    this.isExporting = true;

    // 定义接口地址

    // 传参
    let param: any = { responseType: "blob", observe: "response" };
    this.api.exportSubManager(this.projectId).subscribe(
      (data) => {
        // 调取后端服务接口
        this.downFile(data);

        // 服务端操作完成，取消置loading遮罩状态。显性让用户明白此操完成了。
        this.isExporting = false;
      },
      (error1) => {
        // 服务端操作失败，取消置loading遮罩状态。
        this.isExporting = false;

        // 提示用户操完失败
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  }

  /**
   * 通用下载函数
   *
   * @param data
   */
  downFile(data) {
    this.showBlobErrorMessage(data);

    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];
    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  /**
   * 文件报错提示
   * @param data
   */
  showBlobErrorMessage(data: any) {
    let body = data.body;
    if (body.type === "application/json") {
      let that = this;
      const reader = new FileReader();
      reader.readAsText(body, "utf-8");
      reader.onload = () => {
        // 处理报错信息
        // JSON.parse(reader.result) 拿到报错信息
        let resp: any = JSON.parse(reader.result + "");
        let code: number = resp.result.code;
        let errMsg: string = resp.result.message;

        if (code !== 0) {
          this.isExporting = false;
          // that.msg.error(`${errMsg}，请联系管理员。`);
          this.customMsg.open("error", `${errMsg}，请联系管理员。`);
        }
      };
    }
  }
}
