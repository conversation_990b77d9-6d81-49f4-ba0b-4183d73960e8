<div class="lottery-box">
  <div class="client-width container-box">
    <div class="title">
      <span class="span_left">抽奖福利</span>
      <span class="span_right">
        <span (click)="getHome()">活动设置</span> >
        <span class="span_blue">抽奖福利</span>
      </span>
    </div>

    <div class="content-box">
      <div class="content_left">
        <div class="tab">
          <div>
            <button [class]="type ? 'active' : ''" (click)="channgeType(true)">
              电脑
            </button>
            <button [class]="type ? '' : 'active'" (click)="channgeType(false)">
              手机
            </button>
          </div>
        </div>
        <div class="tab-preview">
          <ng-container *ngIf="showPreview">
            <!-- 电脑 -->
            <ng-container *ngIf="type">
              <div class="pc">
                <div>
                  <app-draw
                    *ngIf="privewData && lotteryResultData"
                    type="pc"
                    [pageChange]="showPreview"
                    [lotteryResultData]="lotteryResultData"
                    [privewData]="privewData"
                  ></app-draw>
                </div>
              </div>
            </ng-container>
            <!-- 手机 -->
            <ng-container *ngIf="!type">
              <div class="phone">
                <div>
                  <app-draw
                    *ngIf="privewData && lotteryResultData"
                    type="phone"
                    [pageChange]="showPreview"
                    [lotteryResultData]="lotteryResultData"
                    [privewData]="privewData"
                  ></app-draw>
                </div>
              </div>
            </ng-container>
          </ng-container>
          <ng-container *ngIf="!showPreview">
            <!-- 电脑 -->
            <ng-container *ngIf="type">
              <div class="pc">
                <div>
                  <app-draw
                    *ngIf="privewData && lotteryResultData"
                    type="pc"
                    [pageChange]="showPreview"
                    [lotteryResultData]="lotteryResultData"
                    [privewData]="privewData"
                  ></app-draw>
                </div>
              </div>
            </ng-container>
            <!-- 手机 -->
            <ng-container *ngIf="!type">
              <div class="phone">
                <div>
                  <app-draw
                    *ngIf="privewData && lotteryResultData"
                    type="phone"
                    [pageChange]="showPreview"
                    [lotteryResultData]="lotteryResultData"
                    [privewData]="privewData"
                  ></app-draw>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <ul class="features">
          <li (click)="privewRefresh()">
            <i class="iconfont icon-icon_refresh"></i>刷新
          </li>
          <nz-divider nzType="vertical"></nz-divider>
          <li
            [ngClass]="showPreview ? 'active' : ''"
            (click)="togglePreview(true)"
          >
            抽奖页
          </li>
          <nz-divider nzType="vertical"></nz-divider>
          <li
            [ngClass]="!showPreview ? 'active' : ''"
            (click)="togglePreview(false)"
          >
            中奖页
          </li>
        </ul>
      </div>
      <div class="content_right">
        <div class="card-container">
          <nz-tabset
            [nzTabPosition]="'top'"
            [nzType]="'card'"
            (nzSelectChange)="tabChange($event)"
          >
            <nz-tab nzTitle="基础设置">
              <div class="basic-settings">
                <div class="basic">
                  <div class="set-box">
                    <div class="set-name">
                      <span class="name"
                        >参与按钮
                        <!-- <nz-radio-group [(ngModel)]="partakeBtnLan" [nzButtonStyle]="'solid'" [nzSize]="'small'">
                                                <label nz-radio-button nzValue="zh_CN">中文</label>
                                                <label nz-radio-button nzValue="en_US">ENG</label>
                                            </nz-radio-group> -->
                      </span>
                    </div>
                    <!-- 国际化切换 -->
                    <div style="margin: 10px 0;">
                      <app-i18n-select
                        [active]="partakeBtnLan"
                        (selectChange)="onSelectI18nPartakeBtn($event)"
                      ></app-i18n-select>
                    </div>
                    <nz-input-group [nzSuffix]="suffixTemplateInfo">
                      <input
                        type="text"
                        [(ngModel)]="
                          settingData.partakeButtonName[partakeBtnLan]
                        "
                        nzSize="large"
                        nz-input
                        placeholder="参与抽奖"
                      />
                    </nz-input-group>
                    <ng-template #suffixTemplateInfo>
                      <span class="suffix"
                        >{{
                          settingData.partakeButtonName[partakeBtnLan]
                            ? settingData.partakeButtonName[partakeBtnLan]
                                .length
                            : 0
                        }}
                        / 50</span
                      >
                    </ng-template>
                  </div>
                  <div class="set-box">
                    <div class="set-name">
                      <span class="name"
                        >主题名称
                        <!-- <nz-radio-group [(ngModel)]="subjectNameLan" [nzButtonStyle]="'solid'" [nzSize]="'small'">
                                                <label nz-radio-button nzValue="zh_CN">中文</label>
                                                <label nz-radio-button nzValue="en_US">ENG</label>
                                            </nz-radio-group> -->
                      </span>
                      <div class="set-name-right">
                        <span class="tip-name">标题图片</span>
                        <nz-upload
                          [nzCustomRequest]="customReqSubjectPic"
                          [nzShowUploadList]="false"
                        >
                          <button nz-button class="tip-btn">本地上传</button>
                        </nz-upload>
                        <button
                          nz-button
                          style="margin-left: 4px;"
                          class="tip-btn"
                          (click)="clearPic()"
                        >
                          清除
                        </button>
                      </div>
                    </div>
                    <!-- 国际化切换 -->
                    <div style="margin: 10px 0;">
                      <app-i18n-select
                        [active]="subjectNameLan"
                        (selectChange)="onSelectI18nSubjectName($event)"
                      ></app-i18n-select>
                    </div>
                    <tinymce
                      *ngIf="tabIndex === 0"
                      [config]="tinyconfig"
                      id="formula-textareanew"
                      [(ngModel)]="settingData.subjectName[subjectNameLan]"
                      delay="100"
                    >
                    </tinymce>
                  </div>
                  <!-- 主题背景 -->
                  <div class="set-box">
                    <div class="set-name">
                      <span class="name">主题背景</span>
                    </div>
                    <div>
                      <nz-radio-group
                        [(ngModel)]="settingData.subjectBackgroundType"
                        (ngModelChange)="changeThemeType($event)"
                      >
                        <label
                          nz-radio
                          nzValue="CUSTOM"
                          style="mask-repeat: 64px;"
                          >自定义背景</label
                        >
                        <label nz-radio nzValue="COLOR">纯色背景</label>
                      </nz-radio-group>
                    </div>
                    <!-- 自定义背景 -->
                    <div
                      class="img-box"
                      *ngIf="settingData.subjectBackgroundType === 'CUSTOM'"
                    >
                      <div class="img-group">
                        <div class="top">
                          <p>PC端</p>
                          <nz-upload
                            [nzCustomRequest]="customReqSubjectBackgroundPcPic"
                            [nzShowUploadList]="false"
                          >
                            <div class="upload-box">
                              <ng-container
                                *ngIf="!settingData.subjectBackgroundPcPic"
                              >
                                <img src="assets/images/upload.png" alt="" />
                                <p>上传</p>
                              </ng-container>
                              <img
                                class="upload"
                                *ngIf="settingData.subjectBackgroundPcPic"
                                [attr.src]="
                                  imgUrl + settingData.subjectBackgroundPcPic
                                "
                                alt=""
                              />
                            </div>
                          </nz-upload>
                        </div>
                        <p>建议：1920*1080px，<br />500K以内，PNG格式</p>
                      </div>
                      <div class="img-group">
                        <div class="top">
                          <p>移动端</p>
                          <nz-upload
                            [nzCustomRequest]="
                              customReqSubjectBackgroundMobilePic
                            "
                            [nzShowUploadList]="false"
                          >
                            <div class="upload-box">
                              <ng-container
                                *ngIf="!settingData.subjectBackgroundMobilePic"
                              >
                                <img src="assets/images/upload.png" alt="" />
                                <p>上传</p>
                              </ng-container>
                              <img
                                class="upload"
                                *ngIf="settingData.subjectBackgroundMobilePic"
                                [attr.src]="
                                  imgUrl +
                                  settingData.subjectBackgroundMobilePic
                                "
                                alt=""
                              />
                            </div>
                          </nz-upload>
                        </div>
                        <p>
                          建议：宽750px ，长≥1334px，<br />
                          500K以内，PNG格式
                        </p>
                      </div>
                    </div>
                    <!-- 纯色背景 -->
                    <ul
                      class="solid-color-bg"
                      *ngIf="settingData.subjectBackgroundType === 'COLOR'"
                    >
                      <li *ngFor="let bgcolors of solidColorBg">
                        <div
                          *ngFor="let bgcolor of bgcolors"
                          class="bg-color-box"
                          [ngClass]="
                            settingData.subjectBackgroundColor === bgcolor
                              ? 'bg-color-box-active'
                              : ''
                          "
                          (click)="chooseBg(bgcolor)"
                        >
                          <div [ngStyle]="{ background: bgcolor }"></div>
                        </div>
                      </li>
                    </ul>
                  </div>
                  <!-- 九宫格背景 -->
                  <div class="set-box">
                    <div class="set-name">
                      <span class="name">九宫格颜色设置</span>
                    </div>
                    <!-- 纯色背景 -->
                    <div style="display: flex; align-items: center;">
                      <span>边框颜色</span>
                      <div
                        class="bg-color-box bg-color-box-active"
                        style="margin: 0 10px;"
                        [ngStyle]="{ background: settingData.boxBorderColor }"
                        nz-dropdown
                        nzTrigger="click"
                        [nzDropdownMenu]="borderColorTemplate"
                        [(nzVisible)]="showBorderColorPopo"
                      ></div>
                      <span>背景颜色</span>
                      <div
                        class="bg-color-box bg-color-box-active"
                        style="margin: 0 10px;"
                        [ngStyle]="{
                          background: settingData.boxBackgroundColor
                        }"
                        nz-dropdown
                        nzTrigger="click"
                        [nzDropdownMenu]="bgColorTemplate"
                        [(nzVisible)]="showBgColorPopo"
                      ></div>
                    </div>
                    <nz-dropdown-menu #borderColorTemplate="nzDropdownMenu">
                      <ul class="solid-color-bg">
                        <li *ngFor="let bgcolors of solidColorBg">
                          <div
                            *ngFor="let bgcolor of bgcolors"
                            class="bg-color-box"
                            [ngClass]="
                              settingData.boxBackgroundColor === bgcolor
                                ? 'bg-color-box-active'
                                : ''
                            "
                            (click)="chooseNineBorderBg(bgcolor)"
                          >
                            <div [ngStyle]="{ background: bgcolor }"></div>
                          </div>
                        </li>
                      </ul>
                    </nz-dropdown-menu>
                    <nz-dropdown-menu #bgColorTemplate="nzDropdownMenu">
                      <ul class="solid-color-bg">
                        <li *ngFor="let bgcolors of solidColorBg">
                          <div
                            *ngFor="let bgcolor of bgcolors"
                            class="bg-color-box"
                            [ngClass]="
                              settingData.boxBackgroundColor === bgcolor
                                ? 'bg-color-box-active'
                                : ''
                            "
                            (click)="chooseNineBg(bgcolor)"
                          >
                            <div [ngStyle]="{ background: bgcolor }"></div>
                          </div>
                        </li>
                      </ul>
                    </nz-dropdown-menu>
                  </div>

                  <!-- 抽奖按钮 -->
                  <div class="set-box">
                    <div class="set-name">
                      <span class="name"
                        >抽奖按钮
                        <!-- <nz-radio-group [(ngModel)]="drawBtnNameLan" [nzButtonStyle]="'solid'" [nzSize]="'small'">
                                                <label nz-radio-button nzValue="zh_CN">中文</label>
                                                <label nz-radio-button nzValue="en_US">ENG</label>
                                            </nz-radio-group> -->
                      </span>
                      <div class="set-name-right">
                        <span class="tip-name">按钮图片</span>
                        <nz-upload
                          [nzCustomRequest]="customReqDrawBtn"
                          [nzShowUploadList]="false"
                        >
                          <button nz-button class="tip-btn">本地上传</button>
                        </nz-upload>
                      </div>
                    </div>
                    <!-- 国际化切换 -->
                    <div style="margin: 10px 0;">
                      <app-i18n-select
                        [active]="drawBtnNameLan"
                        (selectChange)="onSelectI18nBtnName($event)"
                      ></app-i18n-select>
                    </div>
                    <tinymce
                      *ngIf="tabIndex === 0"
                      [config]="tinyconfig"
                      id="formula-textareanew"
                      [(ngModel)]="settingData.drawButtonName[drawBtnNameLan]"
                      delay="100"
                    >
                    </tinymce>
                  </div>
                  <!-- 活动周期 -->
                  <div class="set-box">
                    <div class="set-name">
                      <span class="name">活动周期</span>
                    </div>
                    <nz-range-picker
                      [nzShowTime]="{ nzFormat: 'HH:mm' }"
                      nzFormat="YYYY/MM/DD HH:mm"
                      [nzPlaceHolder]="['开始时间', '结束时间']"
                      [(ngModel)]="timeRange"
                      (nzOnOk)="onOk($event)"
                    ></nz-range-picker>
                  </div>
                  <!-- 活动规则 -->
                  <div class="set-box">
                    <div class="set-name">
                      <span class="name"
                        >活动规则
                        <!-- <nz-radio-group [(ngModel)]="drawRuleDescLan" [nzButtonStyle]="'solid'" [nzSize]="'small'">
                                                <label nz-radio-button nzValue="zh_CN">中文</label>
                                                <label nz-radio-button nzValue="en_US">ENG</label>
                                            </nz-radio-group> -->
                      </span>
                    </div>
                    <!-- 国际化切换 -->
                    <div style="margin: 10px 0;">
                      <app-i18n-select
                        [active]="drawRuleDescLan"
                        (selectChange)="onSelectI18nRuleDesc($event)"
                      ></app-i18n-select>
                    </div>
                    <tinymce
                      *ngIf="tabIndex === 0"
                      [config]="tinyconfig"
                      id="formula-textareanew"
                      [(ngModel)]="
                        settingData.drawRuleDescription[drawRuleDescLan]
                      "
                      delay="100"
                    >
                    </tinymce>
                  </div>
                </div>
              </div>
            </nz-tab>
            <nz-tab nzTitle="奖品设置">
              <div class="basic-settings">
                <div class="basic">
                  <!-- 抽奖次数 -->
                  <div class="set-box">
                    <div class="set-name">
                      <span class="name">抽奖次数</span>
                    </div>
                    <div class="prize-settings">
                      <span>每个账号抽取：</span>
                      <div
                        class="icon-btn-minus"
                        *ngIf="projectType === 'ANNOUNCED' || projectType === 'PREVIEW'"
                        (click)="minusNumDraws()"
                      ></div>
                      <div
                        class="icon-btn-minus"
                        style="cursor: not-allowed;"
                        *ngIf="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                      ></div>
                      <input
                        #inputNumberDraws
                        [disabled]="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                        style="width: 65px; height: 30px; margin-right: 12px;"
                        nz-input
                        [ngModel]="settingData.drawNumber"
                        (ngModelChange)="numberDrawsChange($event)"
                        (blur)="numberDrawsOnBlur()"
                      />
                      <div
                        class="icon-btn-add"
                        *ngIf="projectType === 'ANNOUNCED' || projectType === 'PREVIEW'"
                        (click)="addNumDraws()"
                      ></div>
                      <div
                        class="icon-btn-add"
                        style="cursor: not-allowed;"
                        *ngIf="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                      ></div>
                      <span class="unit">次</span>
                    </div>
                  </div>
                  <div class="set-box">
                    <div class="set-name">
                      <span class="name">参与人数</span>
                    </div>
                    <div class="prize-settings">
                      <span>参与人数仅限：</span>
                      <div
                        class="icon-btn-minus"
                        *ngIf="projectType === 'ANNOUNCED' || projectType === 'PREVIEW'"
                        (click)="minusNumParts()"
                      ></div>
                      <div
                        class="icon-btn-minus"
                        style="cursor: not-allowed;"
                        *ngIf="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                      ></div>
                      <input
                        #inputNumberParticipants
                        [disabled]="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                        style="width: 65px; height: 30px; margin-right: 12px;"
                        nz-input
                        [ngModel]="settingData.partakeNumber"
                        (ngModelChange)="numberPartsChange($event)"
                        (blur)="numberPartsOnBlur()"
                      />
                      <div
                        class="icon-btn-add"
                        *ngIf="projectType === 'ANNOUNCED' || projectType === 'PREVIEW'"
                        (click)="addNumParts()"
                      ></div>
                      <div
                        class="icon-btn-add"
                        style="cursor: not-allowed;"
                        *ngIf="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                      ></div>
                      <span class="unit">人</span>
                    </div>
                  </div>
                  <div class="set-box">
                    <div class="set-name mb-8">
                      <span class="name">奖品详情</span>
                      <a
                        (click)="addPrize(tplContent)"
                        *ngIf="projectType === 'ANNOUNCED' || projectType === 'PREVIEW'"
                        ><img
                          style="margin-right: 8px;"
                          src="assets/images/add-condition.png"
                          alt=""
                        />添加</a
                      >
                    </div>
                    <div class="prize-table">
                      <nz-table
                        #columnTable
                        [nzData]="prizeList"
                        [nzFrontPagination]="false"
                        [nzShowPagination]="false"
                      >
                        <thead>
                          <tr>
                            <th nzWidth="100px">奖品分类</th>
                            <th nzWidth="100px">奖品名称</th>
                            <th nzWidth="80px">中奖率</th>
                            <th nzWidth="60px">数量</th>
                            <th nzWidth="100px">奖品图片</th>
                            <!-- <th nzWidth="100px">提示语</th> -->
                            <th nzWidth="100px" nzRight="0px">操作</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            *ngFor="
                              let data of columnTable.data;
                              let index = index
                            "
                          >
                            <td>
                              <img
                                *ngIf="data.localIcon !== 'empty'"
                                [attr.src]="data.localIcon"
                                height="22px"
                                alt=""
                              /><span>{{ data.categoryName["zh_CN"] }}</span
                              ><br /><span>{{
                                data.categoryName["en_Us"]
                              }}</span>
                            </td>
                            <td>
                              <span>{{ data.prizeName["zh_CN"] }}</span
                              ><br /><span>{{ data.prizeName["en_Us"] }}</span>
                            </td>
                            <td>{{ data.prizeRate }}</td>
                            <td>{{ data.totalNumber }}</td>
                            <td>
                              <div
                                *ngIf="data.prizePic"
                                class="img-little"
                                nz-popover
                                nzTooltipOverlayClassName="popover-img-body"
                                [nzPopoverContent]="contentTemplate"
                                nzPopoverPlacement="left"
                                nzPopoverTrigger="click"
                              >
                                <img
                                  [attr.src]="imgUrl + data.prizePic"
                                  alt=""
                                />
                              </div>
                              <ng-template #contentTemplate>
                                <div class="popover-img-box">
                                  <img
                                    *ngIf="data.prizePic"
                                    [attr.src]="imgUrl + data.prizePic"
                                    alt=""
                                  />
                                </div>
                              </ng-template>
                            </td>
                            <!-- <td><span *ngIf="data.reminder" [innerHTML]="data.reminder.zh_CN"></span><br><span *ngIf="data.reminder" [innerHTML]="data.reminder.en_US"></span></td> -->
                            <td nzRight="0px">
                              <a
                                (click)="addPrize(tplContent, data)"
                                style="margin-right: 8px;"
                                >设置</a
                              >
                              <!-- <br> -->
                              <a
                                *ngIf="
                                  !data.isPartakePrize && ( projectType === 'ANNOUNCED' || projectType === 'PREVIEW')
                                "
                                nz-popconfirm
                                nzPopconfirmTitle="确定删除当前奖品吗？"
                                nzPopconfirmPlacement="bottom"
                                (nzOnConfirm)="deletePrize(data.id)"
                                (nzOnCancel)="cancel()"
                                >删除</a
                              >
                            </td>
                          </tr>
                        </tbody>
                      </nz-table>
                      <div class="tips">
                        <img
                          src="assets/images/prisma/icon-warning.png"
                          alt=""
                        />
                        <p>
                          有效奖品的分类≤7个，九宫格剩余部分或有效奖品抽完后，将自动填充"谢谢参与"分类。
                        </p>
                      </div>
                    </div>
                  </div>
                  <!-- 兑奖规则 -->
                  <div class="set-box" *ngIf="tabIndex == 1">
                    <div class="set-name">
                      <span class="name"
                        >兑奖规则
                        <!-- <nz-radio-group [(ngModel)]="drawRuleDescLan" [nzButtonStyle]="'solid'" [nzSize]="'small'">
                                                <label nz-radio-button nzValue="zh_CN">中文</label>
                                                <label nz-radio-button nzValue="en_US">ENG</label>
                                            </nz-radio-group> -->
                      </span>
                    </div>
                    <!-- 国际化切换 -->
                    <div style="margin: 10px 0;">
                      <app-i18n-select
                        [active]="cashRuleDescLan"
                        (selectChange)="onSelectI18nCashRule($event)"
                      ></app-i18n-select>
                    </div>
                    <tinymce
                      [config]="tinyconfig"
                      id="formula-textareanew"
                      [(ngModel)]="
                        settingData.cashRuleDescription[cashRuleDescLan]
                      "
                      delay="100"
                    >
                    </tinymce>
                  </div>
                </div>
              </div>
            </nz-tab>
          </nz-tabset>
        </div>
      </div>
    </div>
  </div>

  <div class="submit_xy">
    <div class="client-width center_menu">
      <div></div>
      <ul class="menus_xy">
        <li class="menus_left" (click)="default()" nz-button>恢复默认</li>
        <li class="menus_right_new" (click)="saveData()">保存设置</li>
        <!--  -->
      </ul>
    </div>
  </div>
  <!-- 奖品设置弹窗 -->
  <ng-template #tplContent>
    <ng-container *ngIf="!showSetCategory">
      <header>
        <p style="text-align: right;">
          <i (click)="closeTplModal()" class="iconfont icon-penetra-close"></i>
        </p>
        <div class="prize-title">奖品设置</div>
      </header>
      <div class="prize-body">
        <div class="prize-box">
          <div>
            <div class="prize-name">
              <p>奖品分类</p>
              <a (click)="setCategory()" style="margin-right: 10px;">设置</a>
            </div>
            <div class="prize-content">
              <nz-select
                nzShowSearch
                nzAllowClear
                nzPlaceHolder="请选择"
                nzSize="large"
                [(ngModel)]="prizeSettingData.categoryId"
                style="width: 420px;"
              >
                <nz-option
                  *ngFor="let category of categoryList"
                  [nzLabel]="category.name.zh_CN"
                  [nzValue]="category.id"
                ></nz-option>
              </nz-select>
            </div>
          </div>
          <div style="width: 452px;">
            <div class="prize-name">
              <p>
                奖品名称
                <!-- <nz-radio-group [(ngModel)]="setPrizeNameLan" [nzButtonStyle]="'solid'" [nzSize]="'small'">
                                    <label nz-radio-button nzValue="zh_CN">中文</label>
                                    <label nz-radio-button nzValue="en_US">ENG</label>
                                </nz-radio-group> -->
              </p>
              <nz-upload
                [nzCustomRequest]="customReqPrizePic"
                [nzShowUploadList]="false"
              >
                <div class="prize-updata-btn">上传图片</div>
              </nz-upload>
            </div>
            <!-- 国际化切换 -->
            <div style="margin: 10px 0;">
              <app-i18n-select
                [active]="setPrizeNameLan"
                (selectChange)="onSelectI18nPrizeName($event)"
              ></app-i18n-select>
            </div>
            <div class="prize-content">
              <input
                nz-input
                placeholder="请输入"
                [(ngModel)]="prizeSettingData.prizeName[setPrizeNameLan]"
                nzSize="large"
                style="width: 452px;"
              />
            </div>
          </div>
        </div>
        <div class="prize-box">
          <div>
            <div class="prize-name">
              <p>奖品图标</p>
            </div>
            <div class="prize-icon">
              <ul>
                <li *ngFor="let icons of iconList">
                  <ng-container *ngFor="let icon of icons; let index = index">
                    <div
                      *ngIf="icon.name === 'empty'"
                      [ngClass]="
                        prizeSettingData.prizeIcon === icon.name
                          ? 'icon-box-active'
                          : ''
                      "
                      (click)="choosePrizeIcon(icon.name)"
                      class="icon-box"
                    >
                      空
                    </div>
                    <div
                      *ngIf="icon.name !== 'empty' && icon.name !== 'upload'"
                      [ngClass]="
                        prizeSettingData.prizeIcon === icon.name
                          ? 'icon-box-active'
                          : ''
                      "
                      (click)="choosePrizeIcon(icon.name)"
                      class="icon-box"
                    >
                      <img
                        [attr.src]="icon.localIcon"
                        class="icon-img"
                        alt=""
                      />
                    </div>
                    <nz-upload
                      *ngIf="icon.name === 'upload'"
                      [nzCustomRequest]="customReqPrizeIconPic"
                      [nzShowUploadList]="false"
                    >
                      <div class="upload-box">
                        <img src="assets/images/upload.png" alt="" />
                        <p>上传</p>
                      </div>
                    </nz-upload>
                  </ng-container>
                </li>
              </ul>
            </div>
          </div>
          <div style="width: 452px;">
            <div class="prize-name mt-8">
              <p>
                提示语
                <!-- <nz-radio-group [(ngModel)]="setReminderLan" [nzButtonStyle]="'solid'" [nzSize]="'small'">
                                    <label nz-radio-button nzValue="zh_CN">中文</label>
                                    <label nz-radio-button nzValue="en_US">ENG</label>
                                </nz-radio-group> -->
              </p>
            </div>
            <!-- 国际化切换 -->
            <div style="margin: 10px 0;">
              <app-i18n-select
                [active]="setReminderLan"
                (selectChange)="onSelectI18nReminder($event)"
              ></app-i18n-select>
            </div>
            <div class="prize-content">
              <tinymce
                [config]="tinyconfig"
                id="formula-textareanew"
                [(ngModel)]="prizeSettingData.reminder[setReminderLan]"
                delay="100"
              >
              </tinymce>
            </div>
          </div>
        </div>
        <div class="prize-box" *ngIf="!prizeSettingData.isPartakePrize">
          <div>
            <div class="prize-name">
              <p>奖品数量</p>
            </div>
            <div class="prize-content">
              <div class="prize-settings">
                <div
                  class="icon-btn-minus"
                  *ngIf="projectType === 'ANNOUNCED' || projectType === 'PREVIEW'"
                  (click)="minusTotalNumber()"
                ></div>
                <div
                  class="icon-btn-minus"
                  style="cursor: not-allowed;"
                  *ngIf="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                ></div>
                <input
                  #PrizeSettingData
                  [disabled]="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                  style="width: 65px; height: 30px; margin-right: 12px;"
                  nz-input
                  [ngModel]="prizeSettingData.totalNumber"
                  (ngModelChange)="SettingTotalNumber($event)"
                  (blur)="settingTotalNumberOnBlur()"
                />
                <div
                  class="icon-btn-add"
                  *ngIf="projectType === 'ANNOUNCED' || projectType === 'PREVIEW'"
                  (click)="addTotalNumber()"
                ></div>
                <div
                  class="icon-btn-add"
                  style="cursor: not-allowed;"
                  *ngIf="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                ></div>
                <span class="unit">次</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="showSetCategory">
      <header>
        <p style="text-align: right;">
          <i (click)="closeTplModal()" class="iconfont icon-penetra-close"></i>
        </p>
        <div class="prize-title">
          <div class="prize-title-name">
            <div class="prize-title-icon" (click)="goBackPage()">
              <i
                nz-icon
                nzType="left"
                style="font-size: 30px; margin-right: 10px;"
                nzTheme="outline"
              ></i>
            </div>
            奖品设置/
            <span class="little-title">分类设置</span>
          </div>
          <div class="right-btn" (click)="addCategorys()">添加分类</div>
        </div>
      </header>
      <div class="prize-body">
        <div>
          <nz-radio-group
            [(ngModel)]="categoryListLan"
            [nzButtonStyle]="'solid'"
          >
            <label nz-radio-button nzValue="zh_CN">中文</label>
            <label nz-radio-button nzValue="en_US">ENG</label>
          </nz-radio-group>
        </div>
        <ul>
          <li *ngFor="let catory of categoryList; let index = index">
            <input
              type="text"
              style="width: 420px;"
              [(ngModel)]="catory.name[categoryListLan]"
              nzSize="large"
              nz-input
              placeholder="请输入"
            />
            <div
              class="delt-icon"
              *ngIf="index !== 0"
              (click)="deleteCategory(index, catory.id)"
            ></div>
            <div class="delt-icon-empty" *ngIf="index === 0"></div>
          </li>
        </ul>
      </div>
    </ng-container>
    <footer>
      <ng-container *ngIf="!showSetCategory">
        <button
          nz-button
          (click)="submitAddPrize()"
          nzType="primary"
          nzShape="round"
          class="modal_foot_left"
        >
          确认
        </button>
        <button
          nz-button
          (click)="closeTplModal()"
          nzType="primary"
          nzShape="round"
          class="modal_foot_right"
        >
          取消
        </button>
      </ng-container>
      <button
        nz-button
        *ngIf="showSetCategory"
        (click)="goBackPage()"
        nzType="primary"
        nzShape="round"
        class="modal_foot_right"
      >
        返回上一页
      </button>
    </footer>
  </ng-template>
  <nz-drawer
    [nzBodyStyle]="{
      height: 'calc(100% - 55px)',
      overflow: 'auto',
      'padding-bottom': '53px'
    }"
    [nzMaskClosable]="false"
    [nzWidth]="550"
    [nzVisible]="visible"
    nzTitle="奖品设置"
    (nzOnClose)="closeTplModal()"
    nzWrapClassName="prize-right-drawer"
  >
    <div style="padding-bottom: 50px;">
      <ng-container *ngIf="!showSetCategory">
        <!-- <header>
                    <p style="text-align: right;"><i (click)="closeTplModal()" class="iconfont icon-penetra-close"></i></p>
                    <div class="prize-title">奖品设置</div>
                </header> -->
        <div class="prize-body">
          <div class="prize-box">
            <div class="prize-name">
              <p>奖品分类</p>
              <a (click)="setCategory()">设置</a>
            </div>
            <div class="prize-content">
              <nz-select
                nzShowSearch
                nzAllowClear
                nzPlaceHolder="请选择"
                nzSize="large"
                [(ngModel)]="prizeSettingData.categoryId"
                [nzDisabled]="prizeSettingData.isPartakePrize"
                style="width: 100%;"
              >
                <nz-option
                  *ngFor="let category of categoryList"
                  [nzLabel]="category.name.zh_CN"
                  [nzValue]="category.id"
                ></nz-option>
              </nz-select>
            </div>
          </div>
          <div class="prize-box mt-8">
            <div class="prize-name">
              <p>
                奖品名称
                <!-- <nz-radio-group [(ngModel)]="setPrizeNameLan" [nzButtonStyle]="'solid'" [nzSize]="'small'">
                                    <label nz-radio-button nzValue="zh_CN">中文</label>
                                    <label nz-radio-button nzValue="en_US">ENG</label>
                                </nz-radio-group> -->
              </p>
              <nz-upload
                [nzCustomRequest]="customReqPrizePic"
                [nzShowUploadList]="false"
              >
                <div class="prize-updata-btn">上传图片</div>
              </nz-upload>
            </div>
            <!-- 国际化切换 -->
            <div style="margin: 10px 0;">
              <app-i18n-select
                [active]="setPrizeNameLan"
                (selectChange)="onSelectI18nPrizeName($event)"
              ></app-i18n-select>
            </div>
            <div class="prize-content">
              <input
                nz-input
                placeholder="请输入"
                [(ngModel)]="prizeSettingData.prizeName[setPrizeNameLan]"
                nzSize="large"
                style="width: 452px;"
              />
            </div>
          </div>
          <div class="prize-box  mt-8">
            <div class="prize-name">
              <p>奖品图标</p>
            </div>
            <div class="prize-icon">
              <ul>
                <li *ngFor="let icons of iconList">
                  <ng-container *ngFor="let icon of icons; let index = index">
                    <div
                      *ngIf="icon.name === 'empty'"
                      [ngClass]="
                        prizeSettingData.prizeIcon === icon.name
                          ? 'icon-box-active'
                          : ''
                      "
                      (click)="choosePrizeIcon(icon.name)"
                      class="icon-box"
                    >
                      空
                    </div>
                    <div
                      *ngIf="icon.name !== 'empty' && icon.name !== 'upload'"
                      [ngClass]="
                        prizeSettingData.prizeIcon === icon.name
                          ? 'icon-box-active'
                          : ''
                      "
                      (click)="choosePrizeIcon(icon.name)"
                      class="icon-box"
                    >
                      <img
                        [attr.src]="icon.localIcon"
                        class="icon-img"
                        alt=""
                      />
                    </div>
                    <nz-upload
                      *ngIf="icon.name === 'upload'"
                      [nzCustomRequest]="customReqPrizeIconPic"
                      [nzShowUploadList]="false"
                    >
                      <div class="upload-box">
                        <img src="assets/images/upload.png" alt="" />
                        <p>上传</p>
                      </div>
                    </nz-upload>
                  </ng-container>
                </li>
              </ul>
            </div>
          </div>
          <div class="prize-box">
            <div class="prize-name">
              <p>
                提示语
                <!-- <nz-radio-group [(ngModel)]="setReminderLan" [nzButtonStyle]="'solid'" [nzSize]="'small'">
                                    <label nz-radio-button nzValue="zh_CN">中文</label>
                                    <label nz-radio-button nzValue="en_US">ENG</label>
                                </nz-radio-group> -->
              </p>
            </div>
            <!-- 国际化切换 -->
            <div style="margin: 10px 0;">
              <app-i18n-select
                [active]="setReminderLan"
                (selectChange)="onSelectI18nReminder($event)"
              ></app-i18n-select>
            </div>
            <div class="prize-content">
              <tinymce
                [config]="tinyconfig"
                id="formula-textareanew"
                [(ngModel)]="prizeSettingData.reminder[setReminderLan]"
                delay="100"
              >
              </tinymce>
            </div>
          </div>
          <div class="prize-box mt-8" *ngIf="!prizeSettingData.isPartakePrize">
            <div class="prize-name">
              <p>奖品数量</p>
            </div>
            <div class="prize-content">
              <div class="prize-settings">
                <div
                  class="icon-btn-minus"
                  *ngIf="projectType === 'ANNOUNCED' || projectType === 'PREVIEW'"
                  (click)="minusTotalNumber()"
                ></div>
                <div
                  class="icon-btn-minus"
                  style="cursor: not-allowed;"
                  *ngIf="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                ></div>
                <input
                  #PrizeSettingData
                  [disabled]="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                  style="width: 65px; height: 30px; margin-right: 12px;"
                  nz-input
                  [ngModel]="prizeSettingData.totalNumber"
                  (ngModelChange)="SettingTotalNumber($event)"
                  (blur)="settingTotalNumberOnBlur()"
                />
                <div
                  class="icon-btn-add"
                  *ngIf="projectType === 'ANNOUNCED' || projectType === 'PREVIEW'"
                  (click)="addTotalNumber()"
                ></div>
                <div
                  class="icon-btn-add"
                  style="cursor: not-allowed;"
                  *ngIf="projectType !== 'ANNOUNCED' && projectType !== 'PREVIEW'"
                ></div>
                <span class="unit">次</span>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="showSetCategory">
        <!-- <header>
                    <p style="text-align: right;"><i (click)="closeTplModal()" class="iconfont icon-penetra-close"></i></p>
                    <div class="prize-title">
                        <div class="prize-title-name">
                            <div class="prize-title-icon" (click)="goBackPage()">
                                <i nz-icon nzType="left" style="font-size: 30px; margin-right: 10px;" nzTheme="outline"></i>
                            </div>
                            奖品设置/
                            <span class="little-title">分类设置</span>
                        </div>
                        <div class="right-btn" (click)="addCategorys()">添加分类</div>
                    </div>
                </header> -->
        <div class="prize-body">
          <div class="prize-header">
            <span>分类设置</span>
            <a (click)="addCategorys()">添加分类</a>
          </div>
          <!-- 国际化切换 -->
          <div style="margin: 10px 0;">
            <app-i18n-select
              [active]="categoryListLan"
              (selectChange)="onSelectI18nCategoryList($event)"
            ></app-i18n-select>
          </div>
          <!-- <div>
                        <nz-radio-group [(ngModel)]="categoryListLan" [nzButtonStyle]="'solid'">
                            <label nz-radio-button nzValue="zh_CN">中文</label>
                            <label nz-radio-button nzValue="en_US">ENG</label>
                        </nz-radio-group>
                    </div> -->
          <ul style="padding: 0">
            <li
              *ngFor="let catory of categoryList; let index = index"
              style="margin-bottom: 8px;"
            >
              <input
                type="text"
                style="width: 420px;"
                [(ngModel)]="catory.name[categoryListLan]"
                nzSize="large"
                nz-input
                placeholder="请输入"
              />
              <div
                class="delt-icon"
                *ngIf="index !== 0"
                (click)="deleteCategory(index, catory.id)"
              ></div>
              <div class="delt-icon-empty" *ngIf="index === 0"></div>
            </li>
          </ul>
        </div>
      </ng-container>
      <div class="footer">
        <ng-container *ngIf="!showSetCategory">
          <button nz-button nzType="primary" (click)="submitAddPrize()">
            确认
          </button>
          <button nz-button nzType="default" (click)="closeTplModal()">
            取消
          </button>
        </ng-container>
        <button
          nz-button
          nzType="primary"
          *ngIf="showSetCategory"
          (click)="goBackPage()"
        >
          返回上一页
        </button>
      </div>
    </div>
  </nz-drawer>
</div>
