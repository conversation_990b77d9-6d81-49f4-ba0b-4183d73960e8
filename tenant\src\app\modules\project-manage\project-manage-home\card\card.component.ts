import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";

import { NzMessageService } from "ng-zorro-antd";

import { ProjectManageService } from "../../../service/project-manage.service";
import {
  KnxCoreService,
  KnxFunctionPermissionService,
} from "@knx/knx-ngx/core";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-card",
  templateUrl: "./card.component.html",
  styleUrls: ["./card.component.less"],
})
export class CardComponent implements OnInit {
  @Input() type: any;
  @Input() data: any;
  @Input() homePage: any;
  @Input() isSystemInterface: boolean;
  @Output() loadListData = new EventEmitter<any>();

  permission = sessionStorage.getItem("permission");
  projectId: string = "";
  questionnaireIds: any[] = [];

  statusClass: string = ""; // 活动状态 样式
  statusName: string = ""; // 活动状态中文
  moreTip: boolean = false;

  is360Invite: boolean = false;

  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/project-manage/home",
      name: "活动管理",
      Highlight: false,
    },
  ];
  constructor(
    private msg: NzMessageService,
    private router: Router,
    private projectManageService: ProjectManageService,
    public knxFunctionPermissionService: KnxFunctionPermissionService,
    private knxCoreService: KnxCoreService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    if (this.data.is360Project && this.data.isInviteAnswer)
      this.is360Invite = true;

    this.dataTransformation();
  }

  dataTransformation() {
    // 数据处理
    if (this.data) {
      this.projectId = this.data.id;
      this.statusClass = this.data.status.toLowerCase();
      switch (this.data.status) {
        case "ANNOUNCED":
          this.statusName = "未发布";
          break;
        case "WAITING_ANSWER":
          this.statusName = "未开始";
          break;
        case "OVER":
          this.statusName = "已结束";
          break;
        case "ANSWERING":
          this.statusName = "进行中";
          break;
        case "SUSPEND":
          this.statusName = "已结束";
          break;
        case "PREVIEW":
          this.statusName = "预发布";
          break;

        default:
          break;
      }
    }
  }

  inviteEval() {
    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    if (this.data.status == "ANNOUNCED") {
      // this.msg.error('活动还未发布，暂不能邀请测评者');
      this.customMsg.open("error", "活动还未发布，暂不能邀请测评者");
      return;
    }
    this.router.navigate(["project-manage/invite360-eval"], {
      queryParams: {
        projectId: this.projectId,
        projectCode: this.data.code,
        step: 2,
        name: this.data.name,
      },
    });
  }

  invite() {
    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    if (this.data.status == "ANNOUNCED") {
      // this.msg.error('活动还未发布，暂不能邀请测评者');
      this.customMsg.open("error", "活动还未发布，暂不能邀请测评者");
      return;
    }
    this.projectManageService
      .listByProjectId(this.projectId)
      .subscribe((res) => {
        res.data.map((item) => this.questionnaireIds.push(item.id));
      });
    if (this.data.surveyType === "ASSESSMENT") {
      if (!this.data.is360Project) {
        // 普通邀请
        this.router.navigate(["project-manage/invite"], {
          queryParams: {
            projectId: this.projectId,
            projectCode: this.data.code,
            questionnaireIds: this.questionnaireIds,
            step: 2,
            name: this.data.name,
          },
        });
      } else {
        // 360
        this.router.navigate(["project-manage/invite360"], {
          queryParams: {
            projectId: this.projectId,
            projectCode: this.data.code,
            step: 2,
            name: this.data.name,
          },
        });
      }
    } else {
      if (this.data.questionnaires[0].isShowOrganization) {
        // 是否显示组织架构  调研邀请
        this.router.navigate(["project-manage/inviteprisma"], {
          queryParams: {
            projectId: this.projectId,
            projectCode: this.data.code,
            questionnaireIds: this.questionnaireIds,
            step: 2,
            name: this.data.name,
          },
        });
      }
    }

    this.historydata(); // 页码定位
  }

  edit() {
    // 编辑

    if (this.data.surveyType === "EMPLOYEE_ENGAGEMENT") {
      localStorage.setItem("backurl", this.router.routerState.snapshot.url);
      console.log("edit:data.code", this.data.code);
      this.router.navigate(["new-prisma"], {
        queryParams: { projectId: this.data.id, type: this.data.status, projectCode: this.data.code },
      });
    } else {
      if (!this.data.is360Project) {
        this.data.standardReportType = this.data.standardReportType
          ? this.data.standardReportType
          : "other";
      }
      localStorage.setItem("backurl", this.router.routerState.snapshot.url);
      this.router.navigate(["new-create"], {
        queryParams: {
          projectId: this.data.id,
          projectCode: this.data.code,
          projectType: this.data.status,
          standardQuestionnaireId: this.data.questionnaires[0]
            .standardQuestionnaireId,
          questionnaireId: this.data.questionnaires[0].id,
          standardReportType: this.data.standardReportType,
        },
      });
      if (!this.data.is360Project) {
        sessionStorage.setItem(
          "standardQuestionnaireIds",
          JSON.stringify(this.data.questionnaires)
        );
      }
      localStorage.setItem("noprismadata", null);
    }
    this.historydata();
  }

  copyActive() {
    // 复制
    this.projectManageService.copyproject(this.data.id).subscribe((res) => {
      if (res.result.code == 0) {
        this.msg.success("复制成功！");
        this.loadListData.emit(); // 刷新页面
      }
    });
  }

  getProjectList() {
    this.moreTip = false;
    this.loadListData.emit();
  }

  historydata() {
    let acitvedata = this.homePage.getParams();
    sessionStorage.setItem("activepage", JSON.stringify(acitvedata));
  } //记录当前页面的定位操作

  goDetail() {
    // 跳转详情页
    this.router.navigate(["/project-manage/home-detail"], {
      queryParams: {
        id: this.data.id,
      },
    });
    this.historydata();
    this.projectManageService
      .syncMessageStatus(this.data.id)
      .subscribe((res) => {});
  }

  isPermissionOrSag(sag) {
    return (
      this.permission === "true" || this.knxFunctionPermissionService.has(sag)
    );
  }
}
