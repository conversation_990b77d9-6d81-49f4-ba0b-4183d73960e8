<!-- 评价设置 -->
<div>
  <div class="act_tit" style="margin-top: 0;">评价设置</div>
  <div style="display: flex;align-items: center; width: 100%;">
    <nz-radio-group [(ngModel)]="nairInfo.answerMode" class="info_radio">
      <label
        [nzDisabled]="disanswerMode || nairInfo.hasPierceThroughQuestion"
        nz-radio
        nzValue="MULTI"
      >
        多人矩阵评价
      </label>
      <ng-container>
        <i
          nz-popover
          [nzPopoverTitle]="tip"
          nzPopoverTrigger="click"
          nzPlacement="bottomRight"
          nzTooltipPlacement="bottomRight"
          class="iconfont icon-help-circle tipIcon"
        ></i>
        <ng-template #tip>
          <div class="tooltip_div_tip">
            <img src="assets/images/question_type_multi.png" alt="" srcset="" />
          </div>
        </ng-template>
      </ng-container>

      <label nz-radio nzValue="ONE_MULTI">
        一题多人评价
      </label>
      <ng-container>
        <i
          nz-popover
          [nzPopoverTitle]="tip1"
          nzPopoverTrigger="click"
          nzPlacement="bottomRight"
          nzTooltipPlacement="bottomRight"
          class="iconfont icon-help-circle tipIcon"
        ></i>
        <ng-template #tip1>
          <div class="tooltip_div_tip">
            <img
              src="assets/images/question_type_one_multi.png"
              alt=""
              srcset=""
            />
          </div>
        </ng-template>
      </ng-container>
      <label nz-radio nzValue="SINGLE">
        逐一评价
      </label>
      <ng-container>
        <i
          nz-popover
          [nzPopoverTitle]="tip2"
          nzPopoverTrigger="click"
          nzPlacement="bottomRight"
          nzTooltipPlacement="bottomRight"
          class="iconfont icon-help-circle tipIcon"
        ></i>
        <ng-template #tip2>
          <div class="tooltip_div_tip">
            <img
              src="assets/images/question_type_single.png"
              alt=""
              srcset=""
            />
          </div>
        </ng-template>
      </ng-container>
    </nz-radio-group>

    <div
      *ngIf="
        nairInfo.answerMode === 'SINGLE' || nairInfo.answerMode === 'ONE_MULTI'
      "
    >
      <label nz-checkbox [(ngModel)]="nairInfo.isHideRole">隐藏角色</label>
    </div>
  </div>
</div>
<!-- 显示设置 -->
<div style="margin-top: 20px;">
  <div class="act_tit">显示设置
    <i nz-popover [nzPopoverTitle]="showSettingView" nzPopoverTrigger="click" nzPlacement="bottomRight" nzTooltipPlacement="bottomRight"
      class="iconfont icon-help-circle showSettingView"></i>
    <ng-template #showSettingView>
      <div class="tooltip_div_view" style="margin:0; border-radius: 8px; font-size: 15px; max-width: 970px; max-height: 350px; display: flex; flex-direction: column; align-items: center;">
        <p style="font-size: 20px;color: #262626;line-height: 28px;text-align: left;font-style: normal; padding-bottom: 25px; padding-top: 10px;">
          显示设置
        </p>
        <img src="assets/images/answer_setting_show_view.png" style="width: 100%; height: 300px;" alt="" srcset="">
      </div>
    </ng-template>
  </div>

  <div class="show-setting-tabs">
    <div class="tab-header">
      <div class="tab-item" *ngFor="let tab of showTabs" [class.active]="activeShowTab === tab.key"
        (click)="checkShowTab(tab.key, null)">
        <label nz-checkbox  [nzDisabled]="tab.model === 'isShowRank'&&nairInfo.isEnableScoreDistribution === true" [(ngModel)]="nairInfo[tab.model]" (ngModelChange)="checkShowTab(tab.key, $event)">
        </label>
          {{ tab.label }}
      </div>
    </div>
    <div class="tab-content"  [ngClass]="(activeShowTab === 'person' && nairInfo.isShowPerson === true) || (activeShowTab === 'rank' && nairInfo.isShowRank === true)?'active':'active-none'">
      <!-- 显示人员内容 -->
      <ng-container *ngIf="activeShowTab === 'person' && nairInfo.isShowPerson">
        <ng-container *ngFor="let item of personPropertyList">
          <label nz-checkbox [nzDisabled]="item.checked && item.name === '姓名'" [(ngModel)]="item.checked"
            (ngModelChange)="checkPersonProperty(item.code, $event)">
            {{item.name}}
          </label>
          &nbsp;&nbsp;
        </ng-container>
      </ng-container>
      <!-- 显示排名内容 -->
      <ng-container *ngIf="activeShowTab === 'rank' && nairInfo.isShowRank">
        <nz-radio-group [(ngModel)]="nairInfo.rankSort" class="info_radio">
          <label nz-radio nzValue="ASC">按得分大到小正序</label>
          <label nz-radio nzValue="DESC">按得分大到小倒序</label>
        </nz-radio-group>
      </ng-container>
    </div>
  </div>
</div>
<ng-template #contentTemplateCol>
  <div
    style="margin:0; padding: 15px; border-radius: 8px; font-size: 15px; max-width: 480px;"
  >
    <div>
      条件：单人总得分=100分，单次评价对象=10人
    </div>
    <div style="margin: 8px 0;">
      设置：S占总分90%,占评价对象10%
    </div>
    <div>
      结果：S等级得分为90分（100*90%），10人中仅1人（10*10%）总得分可超过90分
    </div>
  </div>
</ng-template>

<div
  class="act_tit"
  style="margin-top: 30px;"
  *ngIf="nairInfo && permission && !disanswerMode"
>
  得分设置
  <img
    style="cursor: pointer; margin-left: 8px; position: relative; bottom: 3px;"
    src="assets/images/shownew.png"
    nz-popover
    nzPopoverPlacement="right"
    nzPopoverTitle=""
    [nzPopoverContent]="contentTemplateCol"
  />
</div>

<!-- <ul *ngIf="nairInfo && permission==='true' && !disanswerMode" style="display: flex;">
  <div class="content" [ngClass]="{'disable': disableSave}">

    <div style="margin: 20px 0 10px 0;">
      <label nz-checkbox [(ngModel)]="nairInfo.isEnableScoreDistribution">开启得分分布</label>

      <app-btn *ngIf="nairInfo.isEnableScoreDistribution" style="margin-left: 15px;" [text]="'添加规则'"
        [image]="'./assets/images/org/add.png'" [hoverColor]="'#409EFF'"
        [hoverImage]="'./assets/images/org/add_hover.png'" (btnclick)="add()">
      </app-btn>
    </div>

    <div *ngIf="nairInfo.isEnableScoreDistribution" class="details" nz-row>

      <div *ngFor="let detail of nairInfo.scoreDistribution; let i = index;">
        <span>类型：</span>
        <nz-select [(ngModel)]="detail.level">
          <ng-container *ngFor="let item of typeList">
            <nz-option [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
          </ng-container>
        </nz-select>

        <span class="label">占总分：</span>
        <nz-input-number [(ngModel)]="detail.lowScorePercent" [nzMin]="1" [nzMax]="100" [nzStep]="1">
        </nz-input-number>
        %
        -
        <nz-input-number [(ngModel)]="detail.highScorePercent" [nzMin]="1" [nzMax]="100" [nzStep]="1">
        </nz-input-number>
        %

        <span class="label">占评价对象：</span>
        <nz-input-number [(ngModel)]="detail.personPercent" [nzMin]="1" [nzMax]="100" [nzStep]="1">
        </nz-input-number>
        %

        <span class="label">保底值：</span>
        <label nz-checkbox [(ngModel)]="detail.isGuaranteed" (ngModelChange)="guChanged($event, detail)"></label>

        <i nz-icon style="margin-left: 15px;" nzType="delete" nzTheme="twotone" (click)="delete(i)"></i>

      </div>

    </div>

  </div>
  <div class="newcontent" *ngIf="nairInfo.isShowRank" [ngClass]="{'disable': disableSave}">
    <div style="margin: 20px 0 10px 20px;">
      <div style="display: flex;align-items: center;">
        <label nz-checkbox [(ngModel)]="nairInfo.isTotalScoreComment">开启总分评语</label>

        <app-btn *ngIf="nairInfo.isTotalScoreComment" style="width: 100px;" [text]="'添加规则'"
          [image]="'./assets/images/org/add.png'" [hoverColor]="'#409EFF'"
          [hoverImage]="'./assets/images/org/add_hover.png'" (btnclick)="newadd()">
        </app-btn>
      </div>
      <div *ngIf="nairInfo.isTotalScoreComment">
        <div style="margin-top: 20px;">
          满足下列
          <nz-select [(ngModel)]="nairInfo.conditionRelation" nzBorderless>
            <nz-option nzValue="AND" nzLabel="所有"></nz-option>
            <nz-option nzValue="OR" nzLabel="任意"></nz-option>
          </nz-select>
          条件：
        </div>
      </div>
    </div>
    <div *ngIf="nairInfo.isTotalScoreComment" style="margin: 10px 0 0px 20px;">
      <div *ngFor="let item of nairInfo.conditions.totalScoreCommentConditionList; let i = index;"
        style="margin: 8px 0;">
        <span>总分</span>
        <nz-select [(ngModel)]="item.symbol" nzBorderless style="margin: 0 8px;">
          <nz-option nzValue="EQUAL" nzLabel="等于"></nz-option>
          <nz-option nzValue="GREATER_THAN" nzLabel="大于"></nz-option>
          <nz-option nzValue="LESS_THAN" nzLabel="小于"></nz-option>
        </nz-select>
        <nz-input-number [(ngModel)]="item.value" [nzStep]="1"></nz-input-number>

        <i nz-icon nzType="delete" nzTheme="twotone" style="margin-left: 15px;" (click)="newdelete(i)"></i>

      </div>
    </div>
  </div>
</ul> -->
<div *ngIf="nairInfo && permission && !disanswerMode">
  <div class="score-setting-tabs" [ngClass]="{ disable: disableSave }">
    <div class="tab-header">
      <div class="tab-item" *ngFor="let tab of scoreTabs" [class.active]="activeScoreTab === tab.key"
        (click)="checkScoreTab(tab.key, null)">
        <label nz-checkbox [(ngModel)]="nairInfo[tab.model]" (ngModelChange)="checkScoreTab(tab.key, $event)">
        </label>
        {{ tab.label }}
      </div>
    </div>
    <div class="tab-content" [ngClass]="(activeScoreTab === 'mandatoryRank' && nairInfo.isEnableMandatoryRank === true) || (activeScoreTab === 'scoreDistribution' && nairInfo.isEnableScoreDistribution === true)?'active':'active-none'">
      <!-- 强制排名内容 -->
      <ng-container *ngIf="activeScoreTab === 'mandatoryRank' && nairInfo.isEnableMandatoryRank">
        <div class="details" nz-row>
          <label nz-checkbox [ngModel]="nairInfo.mandatoryRankType?.includes('TOTAL_SCORE')"
            (ngModelChange)="checkMandatoryRank('TOTAL_SCORE')">总分排名(被评估人在所有题目得分排名不可相同)</label>
          &nbsp;&nbsp;
          <label nz-checkbox [ngModel]="nairInfo.mandatoryRankType?.includes('QUESTION')"
            (ngModelChange)="checkMandatoryRank('QUESTION')">题目排名(被评估人在单个题目得分不可相同)</label>
          &nbsp;&nbsp;
        </div>
      </ng-container>
      <!-- 得分分布内容 -->
      <ng-container *ngIf="activeScoreTab === 'scoreDistribution' && nairInfo.isEnableScoreDistribution">
        <div class="details" nz-row>
          <app-btn [text]="'添加规则'" [class]="add-btn" [image]="'./assets/images/org/add.png'" [hoverColor]="'#409EFF'"
            [hoverImage]="'./assets/images/org/add_hover.png'" (btnclick)="add()">
          </app-btn>
          <div *ngFor="let detail of nairInfo.scoreDistribution; let i = index">
            <span>类型：</span>
            <nz-select [(ngModel)]="detail.level">
              <ng-container *ngFor="let item of typeList">
                <nz-option [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
              </ng-container>
            </nz-select>
            <span class="label">占总分：</span>
            <nz-input-number [(ngModel)]="detail.lowScorePercent" [nzMin]="1" [nzMax]="100"
              [nzStep]="1"></nz-input-number>
            % -
            <nz-input-number [(ngModel)]="detail.highScorePercent" [nzMin]="1" [nzMax]="100"
              [nzStep]="1"></nz-input-number>
            %
            <span class="label">占评价对象：</span>
            <nz-input-number [(ngModel)]="detail.personPercent" [nzMin]="1" [nzMax]="100" [nzStep]="1"></nz-input-number>
            %
            <span class="label">保底值：</span>
            <label nz-checkbox [(ngModel)]="detail.isGuaranteed" (ngModelChange)="guChanged($event, detail)"></label>
            <i nz-icon style="margin-left: 15px;" nzType="delete" nzTheme="twotone" (click)="delete(i)"></i>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>
<div class="act_tit" style="margin-top: 30px;">
  评语设置
</div>
<div *ngIf="nairInfo && permission && !disanswerMode">
  <div class="comment-setting-tabs" [ngClass]="{ disable: disableSave }">
    <div class="tab-header">
      <div *ngFor="let tab of commentTabs">
        <div class="tab-item" *ngIf="(nairInfo.isShowRank && tab.key === 'totalScore') || tab.key !== 'totalScore'" [class.active]="activeCommentTab === tab.key" (click)="checkCommentTab(tab.key,null)">
          <label nz-checkbox [(ngModel)]="nairInfo[tab.model]" (ngModelChange)="checkCommentTab(tab.key,$event)">
          </label>
          {{ tab.label }}
        </div>
      </div>
    </div>
    <div class="tab-content"  [ngClass]="(activeCommentTab === 'totalScore' && nairInfo.isShowRank && nairInfo.isTotalScoreComment === true)?'active':'active-none'">
      <!-- 总分评语内容 -->
      <ng-container *ngIf="activeCommentTab === 'totalScore' && nairInfo.isShowRank && nairInfo.isTotalScoreComment">
        <app-btn *ngIf="nairInfo.isTotalScoreComment" style="width: 100px;" [text]="'添加规则'"
          [image]="'./assets/images/org/add.png'" [hoverColor]="'#409EFF'"
          [hoverImage]="'./assets/images/org/add_hover.png'" (btnclick)="newadd()">
        </app-btn>
        <div style="margin-top: 20px;">
          满足下列
          <nz-select [(ngModel)]="nairInfo.conditionRelation" nzBorderless>
            <nz-option nzValue="AND" nzLabel="所有"></nz-option>
            <nz-option nzValue="OR" nzLabel="任意"></nz-option>
          </nz-select>
          条件：
        </div>
        <div style="margin: 10px 0 0px 0;" class="details">
          <div *ngFor="let item of nairInfo.conditions.totalScoreCommentConditionList; let i = index"
            style="margin: 8px 0;">
            <span>总分</span>
            <nz-select [(ngModel)]="item.symbol" nzBorderless style="margin: 0 8px;">
              <nz-option nzValue="EQUAL" nzLabel="等于"></nz-option>
              <nz-option nzValue="GREATER_THAN" nzLabel="大于"></nz-option>
              <nz-option nzValue="LESS_THAN" nzLabel="小于"></nz-option>
            </nz-select>
            <nz-input-number [(ngModel)]="item.value" [nzStep]="1"></nz-input-number>
            <i nz-icon nzType="delete" nzTheme="twotone" style="margin-left: 15px;" (click)="newdelete(i)"></i>
          </div>
        </div>
      </ng-container>
  
    </div>
  </div>
</div>

<div class="drawer-footer">
  <button nz-button nzType="primary" [nzLoading]="isLoading" (click)="ok()">
    <span>确认</span>
  </button>
</div>

<!-- <div>{{nairInfo.id | json}}</div> -->
