import {
  Component,
  OnInit,
  Input,
  TemplateRef,
  Output,
  EventEmitter,
} from "@angular/core";
import { ProjectManageService } from "../../../service/project-manage.service";
import {
  NzMessageService,
  NzModalService,
  NzModalRef,
  UploadXHRArgs,
  log,
} from "ng-zorro-antd";
import { HttpEvent } from "@angular/common/http";
import { ActivatedRoute } from "@angular/router";
import { DragulaService } from "ng2-dragula";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";
@Component({
  selector: "multi-view",
  templateUrl: "./multi-view.component.html",
  styleUrls: ["./multi-view.component.less"],
})
export class MultiView implements OnInit {
  @Output() loadData = new EventEmitter<string>();
  @Input() questionnaireId: string; //问卷id
  @Input() lan: string; //中英文

  tplModal: NzModalRef;
  questionType: string = null; // 题型
  questionStr: string = ""; // 模糊搜索 val
  resultType: string = null; // 结果 题型
  resultStr: string = ""; // 结果 模糊搜索 val
  questionList: any[] = [];
  resultList: any[] = [];
  parentQuestionId: ""; // 条件题id
  questionId: ""; // 结果题id
  tplModalButtonLoading: boolean = false; // 结果题id
  surveyStandardOptions: any[] = []; // 条件提选项id
  popoverVisible: boolean = false; // 条件提选项id
  penetrationList: any[] = []; // 关联列表
  answerType: string = "FINAL";
  reportType: string = null; //判断类型
  resultTypeList = [
    { label: "开放题", value: "ESSAY_QUESTION" },
    { label: "多选式开放题", value: "MULTIPLE_CHOICE_ESSAY_QUESTION" },
  ];
  searchValue = "";
  dimensionList = [];
  step = 1;
  customStyle = {
    "margin-top": "16px",
    border: "none",
  };
  problemData = [];
  visible: boolean = false;
  activeIndex = 0;
  // 下一步按钮节流
  nextStepLoading: boolean = false;
  optionsMap = {
    allRank: [], // 所有维度
    oneRank: [], // 一级维度
    twoRank: [], // 二级维度
    threeRank: [], // 三级维度
    prismaLabel: [], // 指数（标签）
  };
  projectId;
  constructor(
    private api: ProjectManageService,
    private msg: NzMessageService,
    private modalService: NzModalService,
    private routeInfo: ActivatedRoute,
    private dragulaService: DragulaService,
    private customMsg: MessageService
  ) {
    dragulaService.createGroup("MULTIVIEW", {
      moves: (el, container, handle) => {
        // 保证只有图标才能拖动
        return (
          handle.className ===
          "right-topic-content-list-item-drag-icon iconfont icon-caidan"
        );
      },
      accepts: (el, target: any, source: any, sibling: any) => {
        //拖动结束
        // console.log(target, source);
        //防止拖动区域串联
        return target.id == source.id;
      },
    });
  }

  ngOnInit() {
    this.projectId = this.routeInfo.snapshot.queryParams.projectId;
    // this.reportType = this.routeInfo.snapshot.queryParams.reportType;
    // this.getLListQuestionDimensionByRank()
  }
  ngOnDestroy() {
    // 销毁事件
    this.dragulaService.destroy("MULTIVIEW");
  }
  /**
   * 一键清除多视角
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  confirm() {
    this.api
      .batchDelQuestionDimension(this.questionnaireId)
      .subscribe((res) => {
        // 删除所有关联
        if (res.result.code === 0) {
          this.getAssociationList();
          //如果是第二步 需要重新请求一下题目数据
          if (this.step == 2) {
            this.nextStep(false);
          }
          this.msg.success("删除成功");
        }
      });
  }

  delOne(id: string) {
    this.api.delQuestionDimension(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.getAssociationList();
        //如果是第二步 需要重新请求一下题目数据
        if (this.step == 2) {
          this.nextStep(false);
        }
        this.msg.success("删除成功");
      }
    });
  }
  replaceHtmlEntities(text) {
    const entities = {
      "&amp;": "&",
      "&lt;": "<",
      "&gt;": ">",
      "&quot;": '"',
      "&apos;": "'",
      "&#39;": "'",
      "&nbsp;": " ",
      "&ldquo;": "“",
      "&rdquo;": "”",
    };
    for (const entity in entities) {
      if (entities.hasOwnProperty(entity)) {
        const regex = new RegExp(entity, "g");
        text = text.replace(regex, entities[entity]);
      }
    }

    return text;
  }
  // 使用正则表达式匹配并替换标签
  removeTags(htmlString) {
    // 匹配并替换特殊符号
    const result = htmlString.replace(/<\/?[^>]+(>|$)/g, " ");
    return this.replaceHtmlEntities(result.trim()); // 去除首尾空格
  }
  /**
   * 获取多视角列表数据
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  getLListQuestionDimensionByRank() {
    this.api
      .listQuestionDimensionByRank(this.questionnaireId)
      .subscribe((res) => {
        if (res.result.code === 0) {
          res.data.forEach((element) => {
            element.allChecked = false;
            element.indeterminate = false;
            element.checkedKeys = [];
            element.questionList.forEach((item) => {
              item.isLeaf = true;
              item.key = item.id;
              item.checked = item.checked ? item.checked : false;
              item.title = this.removeTags(item.name[this.lan]);
            });
          });
          this.dimensionList = res.data;
        }
      });
  }
  /**
   *点击题目时触发
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  nzEvent(e) {
    // 判断当前题本是否已经全选 如果全选了
    let num = 0;
    this.dimensionList[this.activeIndex].checkedKeys = [];
    this.dimensionList[this.activeIndex].questionList.forEach((item) => {
      if (item.checked) {
        num++;
        this.dimensionList[this.activeIndex].checkedKeys.push(item.key);
      }
    });
    if (num == this.dimensionList[this.activeIndex].questionList.length) {
      this.dimensionList[this.activeIndex].allChecked = true;
      this.dimensionList[this.activeIndex].indeterminate = false;
    } else {
      if (num !== 0) {
        this.dimensionList[this.activeIndex].allChecked = false;
        this.dimensionList[this.activeIndex].indeterminate = true;
      } else {
        this.dimensionList[this.activeIndex].allChecked = false;
        this.dimensionList[this.activeIndex].indeterminate = false;
      }
    }
  }
  /**
   *点击维度名称切换数据
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  updateActive(i) {
    this.activeIndex = i;
  }
  /**
   * 实时获取已经选择了几个维度
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  getDimensionNum() {
    let num = 0;
    this.dimensionList.forEach((data) => {
      if (data.allChecked || data.indeterminate) {
        num++;
      }
    });
    return num;
  }
  /**
   * 点击全选时的操作
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  updateAllChecked(e) {
    this.dimensionList[this.activeIndex].indeterminate = false;
    this.dimensionList[this.activeIndex].checkedKeys = [];
    this.dimensionList[this.activeIndex].questionList.forEach((item) => {
      item.checked = e;
      if (e) {
        this.dimensionList[this.activeIndex].checkedKeys.push(item.key);
      }
    });
  }
  /**
   *点击下一步 请求数据
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  nextStep(flag = true) {
    let questionIds = [];
    this.dimensionList.forEach((data) => {
      data.questionList.forEach((item) => {
        if (item.checked) {
          questionIds.push(item.key);
        }
      });
    });
    if (questionIds.length <= 0) {
      // this.msg.warning("请先选择需要关联多视角的题目");
      this.customMsg.open("warning", "请先选择需要关联多视角的题目");
      return;
    }
    this.nextStepLoading = true;
    let data = {
      questionnaireId: this.questionnaireId,
      questionIds,
    };
    this.api.listQuestionDimensionByQuestion(data).subscribe((res) => {
      if (res.result.code === 0) {
        res.data.forEach((item) => {
          item.active = true;
          item.disabled = false;
          item.name = item.questionName[this.lan];
          item.listFirst = item.dimensionList[0];
          if (item.dimensionList.length > 1) {
            item.listResidue = item.dimensionList.slice(1);
          } else {
            item.listResidue = [];
          }
        });
        this.problemData = res.data;
        if (flag) {
          this.queryListPrisma();
          this.queryCustomDimension();
        }
        this.step = 2;
      }

      this.nextStepLoading = false;
    });
  }
  /**
   *获取所有维度
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  queryCustomDimension() {
    this.api
      .getlistSurveyCustomDimension(this.questionnaireId)
      .subscribe((res) => {
        if (res.result.code === 0) {
          let data = res.data;
          data.forEach((res) => {
            if (!res.description) {
              res.description = { zh_CN: "", en_US: "" };
            }
          });
          this.optionsMap.allRank = data;
          this.optionsMap.oneRank = data.filter((item) => {
            return item.type === "ONE_RANK";
          });
          this.optionsMap.twoRank = data.filter((item) => {
            return item.type === "TWO_RANK";
          });
          this.optionsMap.threeRank = data.filter((item) => {
            return item.type === "THREE_RANK";
          });
        }
      });
  }
  /**
   * 获取指数
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  queryListPrisma() {
    this.api.getlistPrismaLabel(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        let data = res.data;
        this.optionsMap.prismaLabel = data.map((val) => ({
          ...val,
          zh_CN: val.name.zh_CN,
          en_US: val.name.en_US,
        }));
      }
    });
  }
  /**
   * 判断是否有重复的值
   *@author:wangxiangxin
   *@Date:2023/11/28
   */

  weightVerification(data) {
    //判断是否有重复的数据
    const arr = [];
    let tip = false;
    try {
      //找到重复编码跳出整个循环
      data.forEach((item) => {
        arr.forEach((eln) => {
          if (
            item.label === eln.label &&
            item.oneRankDimensionCode === eln.oneRankDimensionCode &&
            item.twoRankDimensionCode === eln.twoRankDimensionCode &&
            item.threeRankDimensionCode === eln.threeRankDimensionCode
          ) {
            throw new Error("finish");
          }
        });
        arr.push(item);
      });
    } catch (e) {
      tip = true;
    }
    return tip;
  }
  /**
   *关联多视角
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  association() {
    let surveyQuestionCustomSortMappingVoList = _.cloneDeep(this.problemData);
    surveyQuestionCustomSortMappingVoList.forEach((item) => {
      item.surveyQuestionDimensionMappingVoList = [
        item.listFirst,
        ...item.listResidue,
      ];
    });

    let flag1 = false;
    try {
      surveyQuestionCustomSortMappingVoList.forEach((element) => {
        element.surveyQuestionDimensionMappingVoList.forEach((item) => {
          if (!item.label) {
            // this.msg.error("指数不能为空");
            this.customMsg.open("error", "指数不能为空");
            throw new Error("finish");
          }
          if (!item.twoRankDimensionCode && !item.threeRankDimensionCode) {
            // this.msg.error("二级维度与三级维度最少填写一个！");
            this.customMsg.open("error", "二级维度与三级维度最少填写一个！");
            throw new Error("finish");
          }
        });
      });
    } catch (e) {
      flag1 = true;
    }
    if (flag1) {
      return;
    }
    // 多视角是否有重复值
    let flag = false;
    surveyQuestionCustomSortMappingVoList.forEach((element) => {
      if (
        this.weightVerification(element.surveyQuestionDimensionMappingVoList)
      ) {
        flag = true;
      }
    });
    if (flag) {
      // this.msg.error("多视角不能添加重复数据");
      this.customMsg.open("error", "多视角不能添加重复数据");
      return;
    }
    let formData = {
      projectId: this.projectId,
      questionnaireId: this.questionnaireId,
      surveyQuestionCustomSortMappingVoList,
    };

    // this.getData();
    // this.step = 1;
    // 关联多视角
    this.api.saveQuestionDimension(formData).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("关联成功");
        this.getData();
      }
    });
  }
  /**
   * 获取已关联的数据
   *@author:wangxiangxin
   *@Date:2023/11/28
   */
  getAssociationList() {
    let data = {
      questionnaireId: this.questionnaireId,
    };
    this.api.listQuestionDimensionByQuestion(data).subscribe((res) => {
      if (res.result.code === 0) {
        this.penetrationList = res.data;
      }
    });
  }
  /**
   *点击多视角打开弹框时
   *@author:wangxiangxin
   *@Date:2023/11/29
   */
  getData() {
    this.activeIndex = 0;
    this.step = 1;
    // 获取维度相关题目列表
    this.getLListQuestionDimensionByRank();
    // 获取已关联的数据
    this.getAssociationList();
  }

  /**
   * 恢复默认
   *@author:wangxiangxin
   *@Date:2023/11/29
   */
  reset() {
    this.activeIndex = 0;
    this.step = 1;
    this.dimensionList.forEach((element) => {
      element.allChecked = false;
      element.indeterminate = false;
      element.checkedKeys = [];
      element.questionList.forEach((item) => {
        item.isLeaf = true;
        item.checked = false;
      });
    });
  }

  /**
   * 多视角关联弹框
   * 
    tplTitle: TemplateRef<{}>,
    tplContent: TemplateRef<{}>,
    tplFooter: TemplateRef<{}>
   */
  createPenetrationQuestions(): void {
    this.getData();
    this.visible = true;
  }
  /**
   * 点击关闭弹框
   *@author:wangxiangxin
   *@Date:2023/11/29
   */
  closePenetraModal(): void {
    this.activeIndex = 0;
    this.step = 1;
    this.dimensionList = [];
    this.problemData = [];
    this.penetrationList = [];
    this.visible = false;
    this.loadData.next();
  }

  /**
   * customReq 导入多视角
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.api
      .questionDimensionImport(formData, this.questionnaireId)
      .subscribe(
        (event: HttpEvent<any>) => {
          item.onSuccess!();
          let res: any = event;
          if (res.result.code === 0) {
            this.msg.success("导入文件成功");
            this.getData();
            // this.loadData.next();
          }
        },
        (err) => {
          item.onError!(err, item.file!);
        }
      );
  }
  /**
   * 导出多视角
   *@author:wangxiangxin
   *@Date:2023/11/29
   */
  exportR() {
    this.api.questionDimensionExport(this.questionnaireId).subscribe((res) => {
      // 文件错误提示
      if (res.body.type === 'application/json') {
        const reader = new FileReader();
        reader.onload = (e) => {
          console.log('e.target', e.target)
          const result = JSON.parse((e.target as FileReader).result as string);
          this.customMsg.open('error', result.result.message);
        };
        reader.readAsText(res.body);
        return;
      }
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }

  /**
   *添加视角
   *@author:wangxiangxin
   *@Date:2023/11/24
   */
  add(e, i) {
    e.stopPropagation();
    if (!this.problemData[i].active) {
      this.problemData[i].active = true;
    }
    if (this.problemData[i].listFirst) {
      if (this.problemData[i].listResidue.length < 6) {
        this.problemData[i].listResidue.push({
          label: null,
          oneRankDimensionCode: null,
          twoRankDimensionCode: null,
          threeRankDimensionCode: null,
          questionId: this.problemData[i].questionId,
        });
      }
    } else {
      this.problemData[i].listFirst = {
        label: null,
        oneRankDimensionCode: null,
        twoRankDimensionCode: null,
        threeRankDimensionCode: null,
        questionId: this.problemData[i].questionId,
      };
    }
  }
  /**
   * 面板折叠同步active数值
   *@author:wangxiangxin
   *@Date:2023/11/24
   */
  activeChange(i, e) {
    this.problemData[i].active = e;
  }
  /**
   * 删除当前题目的多视角数据
   *@author:wangxiangxin
   *@Date:2023/11/24
   */
  delMulti(i, j) {
    this.problemData[i].listResidue.splice(j, 1);
  }
  /**
   * 上一步
   *@author:wangxiangxin
   *@Date:2023/11/29
   */
  createQues() {
    this.step = 1;
  }
}
