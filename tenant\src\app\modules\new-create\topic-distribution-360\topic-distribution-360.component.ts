/*
 * @Author: <PERSON> si<PERSON>@knx.com.cn
 * @Date: 2025-06-03 17:20:41
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2025-06-17 13:43:51
 * @FilePath: \tenant\src\app\modules\new-create\topic-distribution-360\topic-distribution-360.component.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {
  Component,
  OnInit,
  Input,
  OnDestroy,
  Output,
  EventEmitter,
} from "@angular/core";
import _ from "lodash";
import { Subscription } from "rxjs";
import { NewCreateService } from "../new-create.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { NzMessageService, UploadXHRArgs } from "ng-zorro-antd";
import { DownloadUtilService } from "@src/modules/service/download-util.service";
@Component({
  selector: "app-topic-distribution-360",
  templateUrl: "./topic-distribution-360.component.html",
  styleUrls: ["./topic-distribution-360.component.less"],
})
export class TopicDistribution360Component implements OnInit, OnDestroy {
  @Input() projectId: string;
  @Input() questionnaireId: string;
  @Output() onConfirm = new EventEmitter<any>();
  constructor(
    private until: DownloadUtilService,
    private http: NewCreateService,
    private message: NzMessageService,
    private customMsg: MessageService
  ) {}
  isSpinning = false;
  isVisible = false;
  lan = "zh_CN";
  // 被评估人-人口标签-全选
  allChecked = false;
  indeterminate = false;
  // 被评估人
  personType = "demographic";
  personDemographicActive = "";
  personDemographicList = [];
  personDemographicListChildren = [];
  personDemographicCheckeds = []; // 当前选中的人口标签
  personDemographicBinds = []; // 当前绑定的人口标签
  personDemographicCheckAll = false; // 是否全选
  personDemographicCheckAllId = ""; // 是否全选
  // 评估人
  investigatorType = "role";
  investigatorsurveyRoles = [];
  investigatorsurveyRolesCheckeds = []; // 当前选中的角色
  investigatorsurveyRolesBinds = []; // 当前绑定的角色
  // 维度
  type = "dimensions";
  dimensions = [];
  dimensionsCheckeds = []; // 当前选中的角色
  dimensionsBinds = []; // 当前绑定的角色
  isShowImport = false;

  // 结果--------------------------
  bindList = [];
  rightResult = [];
  surveyDemRoleSelectDimensionInfoList = [];
  // 题本分发导出loading
  isDispenseDownLoadSpinning: boolean = false;
  isShowDownload: boolean = false;
  fileName: string = "";
  ngOnInit() {}
  /**
   * 打开侧边栏
   */
  openModal(): void {
    this.isVisible = true;
    this.getListSelectDimension();
  }
  getListSelectDimension(): void {
    this.isSpinning = true;
    this.http.listSelectDimension(this.projectId).subscribe((res) => {
      // 人口标签
      this.personDemographicList = res.data.surveyDemographicList.filter(
        (val) => val.children.length
      );

      if (typeof res.data.parentDemId !== "undefined") {
        this.personType = res.data.parentDemId ? "demographic" : "specific";
      }
      if (typeof res.data.isRole !== "undefined") {
        this.investigatorType = res.data.isRole ? "role" : "specific";
      } else {
        this.investigatorType = "role";
      }
      if (typeof res.data.isDimension !== "undefined") {
        this.type = res.data.isDimension ? "dimensions" : "questions";
      } else {
        this.type = "dimensions";
      }

      if (this.personDemographicList.length === 0) {
        this.personType = "specific";
        this.isShowImport = true;
      } else {
        this.personDemographicActive =
          res.data.parentDemId || this.personDemographicList[0].id;
        this.changeDemographics(this.personDemographicActive);
      }
      // 角色
      this.investigatorsurveyRoles = res.data.surveyRoles;
      // 维度
      this.dimensions = res.data.dimensions;
      // 文件名称
      if (res.data.fileName) {
        this.fileName = res.data.fileName;
      }
      // 回填
      if (
        res.data.surveyDemRoleSelectDimensionInfoList &&
        res.data.surveyDemRoleSelectDimensionInfoList.length
      ) {
        this.surveyDemRoleSelectDimensionInfoList =
          res.data.surveyDemRoleSelectDimensionInfoList;
        this.changeLeft();
        const bindList = [];
        res.data.surveyDemRoleSelectDimensionInfoList.forEach((role) => {
          role.dimensionVoList.forEach((dimension) => {
            role.demographicList.forEach((dem) => {
              bindList.push({
                roleId: role.roleId,
                roleName: role.roleName.zh_CN,
                dimensionCode: dimension.dimensionCode,
                dimensionName: dimension.dimensionName,
                investigatorParentDemId: this.personDemographicActive,
                investigatorChildDemIds: dem.id,
                investigatorChildDemIdsName: dem.name.zh_CN,
              });
            });
          });
        });
        const uniqByList = _.uniqBy(
          bindList,
          (val) =>
            `${val.roleId}-${val.dimensionCode}-${val.investigatorChildDemIds}`
        );
        this.bindList = uniqByList;
        this.personDemographicBinds = _.uniq(
          uniqByList.map((val) => val.investigatorChildDemIds)
        );
        this.investigatorsurveyRolesBinds = _.uniq(
          uniqByList.map((val) => val.roleId)
        );
        this.dimensionsBinds = _.uniq(
          uniqByList.map((val) => val.dimensionCode)
        );
        const groupedBy = _.groupBy(
          this.bindList,
          (val) => `${val.investigatorChildDemIds}-${val.roleId}`
        );
        // 将分组后的对象转换为数组对象
        const groupedArray = Object.entries(groupedBy).map(([key, group]) => ({
          key, // 分组的键
          group, // 分组后的数组
        }));
        this.rightResult = groupedArray;
      }
      if (typeof res.data.isDimension === "boolean") {
        if (res.data.isDimension) {
          if (res.data.surveyDemRoleSelectDimensionInfoList.length) {
            this.isShowDownload = true;
          } else {
            this.isShowDownload = false;
          }
        } else {
          this.isShowDownload = true;
        }
      }
      this.isSpinning = false;
    });
  }
  /**
   * 侧边栏-关闭
   */
  handleCancel(): void {
    this.bindList = [];
    this.rightResult = [];
    this.personDemographicBinds = [];
    this.investigatorsurveyRolesBinds = [];
    this.dimensionsBinds = [];
    this.personDemographicCheckeds = [];
    this.investigatorsurveyRolesCheckeds = [];
    this.dimensionsCheckeds = [];
    this.personDemographicListChildren = [];
    this.investigatorsurveyRoles = [];
    this.dimensions = [];
    this.personDemographicList = [];
    this.personDemographicActive = "";
    this.personType = "demographic";
    this.investigatorType = "role";
    this.type = "dimensions";
    this.isShowImport = false;
    this.isVisible = false;
    this.allChecked = false;
    this.indeterminate = false;
  }
  ngOnDestroy(): void {}

  // 被评估人-变更-类型
  changePersonType(e) {
    this.changeLeft();
  }
  // 被评估人-变更-人口标签
  changeDemographics(e) {
    let target = null;
    target = this.personDemographicList.find(
      (val) => val.id === this.personDemographicActive
    );
    this.personDemographicListChildren = target.children;
    this.allChecked = false;
    this.indeterminate = false;
    this.changeLeft();
  }
  // 选中子人口标签
  onCheckDemographics(e) {
    // console.log("选中子人口标签", e, this.personDemographicCheckAll);
    this.personDemographicCheckeds = e;

    if (this.personDemographicListChildren.every((item) => !item.checked)) {
      this.allChecked = false;
      this.indeterminate = false;
    } else if (
      this.personDemographicListChildren.every((item) => item.checked)
    ) {
      this.allChecked = true;
      this.indeterminate = false;
    } else {
      this.indeterminate = true;
    }
  }
  // 评估人-变更
  changeInvType(e) {
    this.changeLeft();
  }
  // 选中角色
  onCheckRoles(e) {
    this.investigatorsurveyRolesCheckeds = e;
  }
  // 维度/题目-变更
  changeDimOrQues(e) {
    this.changeLeft();
  }
  // 选中维度
  onCheckDimensions(e) {
    this.dimensionsCheckeds = e;
  }
  // 左侧变更 条件的三个分类中，只要有一个选中人员/题目，右侧结果就是上传页面。
  changeLeft() {
    let isShowImport = false;
    if (
      this.personType === "specific" ||
      this.investigatorType === "specific" ||
      this.type === "questions"
    ) {
      isShowImport = true;
    } else {
      // 人口标签-子类超过10个
      if (this.personDemographicListChildren.length > 10) {
        isShowImport = true;
      }
    }
    this.isShowImport = isShowImport;
  }
  // 关联
  onBind() {
    // 选中的人口标签
    let checkedDemographics = null;
    let checkedDemographicsChildren = null;
    let checkedDemographicsChildrenSelect = null;
    checkedDemographics = this.personDemographicList.find(
      (val) => val.id === this.personDemographicActive
    );
    checkedDemographicsChildren = checkedDemographics.children;
    checkedDemographicsChildrenSelect = checkedDemographicsChildren.filter(
      (val) => this.personDemographicCheckeds.includes(val.id)
    );
    if (!this.personDemographicCheckeds.length) {
      this.customMsg.open("warning", "请选择被评估人人口标签");
      return;
    }
    // 选中的角色
    const checkedRoles = this.investigatorsurveyRoles.filter((val) =>
      this.investigatorsurveyRolesCheckeds.includes(val.id)
    );
    if (!checkedRoles.length) {
      this.customMsg.open("warning", "请选择评估人角色");
      return;
    }
    // 选中的维度
    const checkedDimensions = this.dimensions.filter((val) =>
      this.dimensionsCheckeds.includes(val.dimensionCode)
    );
    if (!checkedDimensions.length) {
      this.customMsg.open("warning", "请选择二级维度");
      return;
    }
    // console.log("全部or人口标签", this.personDemographicActive);
    // console.log("选中的人口标签", checkedDemographics);
    // console.log("选中的人口标签子类", checkedDemographicsChildren);
    // console.log("选中的人口标签子类选中", checkedDemographicsChildrenSelect);
    // console.log("选中的角色", checkedRoles);
    // console.log("选中的维度", checkedDimensions);
    const bindList = [];
    // 人口标签
    checkedRoles.forEach((role) => {
      checkedDimensions.forEach((dimension) => {
        checkedDemographicsChildrenSelect.forEach((dem) => {
          bindList.push({
            roleId: role.id,
            roleName: role.nameZh,
            dimensionCode: dimension.dimensionCode,
            dimensionName: dimension.dimensionName,
            investigatorParentDemId: this.personDemographicActive,
            investigatorChildDemIds: dem.id,
            investigatorChildDemIdsName: dem.name.zh_CN,
          });
        });
      });
    });
    // }
    const allBindList = _.sortBy(this.bindList.concat(bindList));
    const uniqByList = _.uniqBy(
      allBindList,
      (val) =>
        `${val.roleId}-${val.dimensionCode}-${val.investigatorChildDemIds}`
    );
    this.bindList = uniqByList;
    const groupedBy = _.groupBy(
      this.bindList,
      (val) => `${val.investigatorChildDemIds}-${val.roleId}`
    );
    // 将分组后的对象转换为数组对象
    const groupedArray = Object.entries(groupedBy).map(([key, group]) => ({
      key, // 分组的键
      group, // 分组后的数组
    }));
    this.rightResult = groupedArray;

    // 数据
    this.personDemographicBinds = _.uniq(
      this.personDemographicBinds.concat(this.personDemographicCheckeds)
    );
    this.investigatorsurveyRolesBinds = _.uniq(
      this.investigatorsurveyRolesBinds.concat(
        this.investigatorsurveyRolesCheckeds
      )
    );
    this.dimensionsBinds = _.uniq(
      this.dimensionsBinds.concat(this.dimensionsCheckeds)
    );

    this.personDemographicCheckeds = [];
    this.investigatorsurveyRolesCheckeds = [];
    this.dimensionsCheckeds = [];
    this.personDemographicListChildren.forEach((val) => {
      val.checked = false;
    });
    this.investigatorsurveyRoles.forEach((val) => {
      val.checked = false;
    });
    this.dimensions.forEach((val) => {
      val.checked = false;
    });
    this.allChecked = false;
    this.indeterminate = false;
  }

  async onClear() {
    this.isSpinning = true;
    const res = await this.http
      .removeAllSelected360(this.projectId)
      .toPromise();
    if (res.result.code === 0) {
      this.message.success("题本分发清除成功");
      this.isShowDownload = false;
      this.getListSelectDimension();
      this.bindList = [];
      this.rightResult = [];
      this.personDemographicBinds = [];
      this.investigatorsurveyRolesBinds = [];
      this.dimensionsBinds = [];
      this.personDemographicCheckeds = [];
      this.investigatorsurveyRolesCheckeds = [];
      this.allChecked = false;
      this.indeterminate = false;
      this.isSpinning = false;
    } else {
      this.isSpinning = false;
    }
  }

  async onSave() {
    if (!this.isShowImport) {
      // 人口标签
      const unCheckedDemographicsChildren = this.personDemographicListChildren.filter(
        (val) => !this.personDemographicBinds.includes(val.id)
      );
      if (unCheckedDemographicsChildren.length) {
        this.customMsg.open(
          "error",
          `被评估人人口标签【${unCheckedDemographicsChildren
            .map((val) => val.name.zh_CN)
            .join(",")}】存在未交叉`
        );
        return;
      }
      // 评估人角色
      const unCheckedRoles = this.investigatorsurveyRoles.filter(
        (val) => !this.investigatorsurveyRolesBinds.includes(val.id)
      );
      if (unCheckedRoles.length) {
        this.customMsg.open(
          "error",
          `评估人角色【${unCheckedRoles
            .map((val) => val.nameZh)
            .join(",")}】未交叉`
        );
        return;
      }
      // 维度
      const unCheckedDimensions = this.dimensions.filter(
        (val) => !this.dimensionsBinds.includes(val.dimensionCode)
      );
      if (unCheckedDimensions.length) {
        this.customMsg.open(
          "error",
          `维度【${unCheckedDimensions
            .map((val) => val.dimensionName)
            .join(",")}】未交叉`
        );
        return;
      }
      const groupedBy = _.groupBy(
        this.bindList,
        (val) => `${val.roleId}-${val.dimensionCode}`
      );
      const dataList = [];
      _.values(groupedBy).forEach((value) => {
        const obj = {
          projectId: this.projectId,
          questionnaireId: this.questionnaireId,
          dimensionCode: value[0].dimensionCode,
          roleId: value[0].roleId,
          investigatorParentDemId: value[0].investigatorParentDemId,
          investigatorChildDemIds: value[0].investigatorParentDemId
            ? value.map((val) => val.investigatorChildDemIds)
            : [],
        };
        dataList.push(obj);
      });
      this.isSpinning = true;
      const res = await this.http
        .saveSelectDimension360(
          this.projectId,
          this.personDemographicActive,
          this.investigatorType === "role",
          dataList
        )
        .toPromise();
      this.isSpinning = false;
      if (res.result.code === 0) {
        this.message.success("题本分发保存成功");
        // this.getListSelectDimension();
        this.handleCancel();
        this.onConfirm.emit(true);
      }
    } else {
      if (this.surveyDemRoleSelectDimensionInfoList.length || this.isShowDownload) {
        this.handleCancel();
      } else {
        this.customMsg.open("warning", "请导入题本分发配置");
      }
    }
  }

  /**
   * 题本分发-维度-导入
   * @param item
   */
  onDispenseImport = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    if (this.type === "dimensions") {
      this.onDispenseUploadExcel(formData, item);
    } else {
      this.onDispenseQuestionUploadExcel(formData, item);
    }
  };
  /**
   * 题本分发-维度-导入-上传配置
   */
  onDispenseUploadExcel(formData, item) {
    return this.http
      .importSelectDimension360(
        formData,
        this.projectId,
        this.personDemographicActive,
        this.investigatorType === "role"
      )
      .subscribe(
        (res) => {
          if (res.result.code === 0) {
            item.onSuccess!();
            this.message.success("题本分发导入成功");
            this.onConfirm.emit(true);
            // this.handleCancel();
            this.getListSelectDimension();
          }
        },
        (err) => {
          item.onError!(err, item.file!);
        }
      );
  }
  /**
   * 题本分发-维度-导入-上传配置
   */
  onDispenseQuestionUploadExcel(formData, item) {
    return this.http
      .importPersonQuestionMapping360(
        formData,
        this.projectId,
        this.personDemographicActive,
        this.investigatorType === "role"
      )
      .subscribe(
        (res) => {
          if (res.result.code === 0) {
            item.onSuccess!();
            this.message.success("题本分发-题目导入成功");
            // this.handleCancel();
            this.getListSelectDimension();
            this.onConfirm.emit(true);
          }
        },
        (err) => {
          item.onError!(err, item.file!);
        }
      );
  }

  /**
   * 题本分发-维度-导出
   */
  onDispenseDownLoad() {
    this.isDispenseDownLoadSpinning = true;
    const groupedBy = _.groupBy(
      this.bindList,
      (val) => `${val.roleId}-${val.dimensionCode}`
    );
    const dataList = [];
    _.values(groupedBy).forEach((value) => {
      const obj = {
        projectId: this.projectId,
        questionnaireId: this.questionnaireId,
        dimensionCode: value[0].dimensionCode,
        roleId: value[0].roleId,
        investigatorParentDemId: value[0].investigatorParentDemId,
        investigatorChildDemIds: value[0].investigatorParentDemId
          ? value.map((val) => val.investigatorChildDemIds)
          : [],
      };
      dataList.push(obj);
    });
    // console.log("按角色和维度分组", dataList);
    this.http
      .exportSelectDimension360(
        this.projectId,
        this.personDemographicActive,
        this.investigatorType === "role",
        dataList
      )
      .subscribe((res) => {
        this.until.downFile(res);
        this.isDispenseDownLoadSpinning = false;
      });
  }
  /**
   * 题本分发-题目-导出
   */
  onDispenseQuestionDownLoad() {
    this.isDispenseDownLoadSpinning = true;
    this.http
      .exportPersonQuestionMapping360(
        this.projectId,
        this.personDemographicActive,
        this.investigatorType === "role"
      )
      .subscribe((res) => {
        this.until.downFile(res);
        this.isDispenseDownLoadSpinning = false;
      });
  }

  updateAllChecked() {
    // console.log(this.allChecked, '全选')
    this.indeterminate = false;
    if (this.allChecked) {
      this.personDemographicListChildren = this.personDemographicListChildren.map(
        (item) => {
          return {
            ...item,
            checked: true,
          };
        }
      );
      this.personDemographicCheckeds = this.personDemographicListChildren.map(
        (item) => item.id
      );
    } else {
      this.personDemographicListChildren = this.personDemographicListChildren.map(
        (item) => {
          return {
            ...item,
            checked: false,
          };
        }
      );
      this.personDemographicCheckeds = [];
    }
  }
}
