<div class="index_xy" style="position: relative;">
  <div class="big_contant client-width">
    <div class="create_p">
      <span class="span_left">
        活动设置
        <img
          style="cursor: pointer;"
          src="assets/images/shownew.png"
          (click)="getGuideContent()"
          alt=""
        />
      </span>
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </div>
    <ul class="create_name">
      <li style="margin-right: 10px;max-width: 500px;width: 500px;">
        <p>活动名称</p>
        <input
          nz-input
          nzSize="large"
          class="pri_name"
          placeholder="请输入活动名称"
          [(ngModel)]="prismaData.name.zh_CN"
          [disabled]="
            !permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:NAME_EDIT'
            )
          "
        />
      </li>
      <li>
        <p>活动周期</p>
        <div class="time-picker-con">
          <nz-range-picker
            class="time_picker"
            [(ngModel)]="dateRange"
            [nzDisabledDate]="disabledDate"
            [nzShowTime]="{ nzMinuteStep: '30', nzFormat: 'HH:mm' }"
            [nzFormat]="'YYYY-MM-DD HH:mm'"
            [nzPlaceHolder]="['请选择开始日期', '请选择结束日期']"
            (ngModelChange)="onChangeActivityDateRange($event)"
            [nzDisabled]="
              !permissionService.isPermissionOrSag(
                'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:PERIOD_EDIT'
              )
            "
          ></nz-range-picker>
          <ng-template #suffixIconSearch>
            <i class="icon-time"></i>
          </ng-template>
        </div>
      </li>
    </ul>
    <div class="label_title">
      <p class="title">人口标签</p>
      <div
        class="div_right"
        *ngIf="
          permissionService.isPermissionOrSag(
            'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DEMOGRAPHIC_EDIT'
          )
        "
      >
        <div
          class="custom_add_xy"
          (click)="showAllCheck()"
          *ngIf="!isUpdateing && isShowAll"
        >
          <i class="iconfont icon-xianshi" style="font-size: 14px;"></i>
          <span>开启</span>
        </div>
        <div
          class="custom_add_xy"
          (click)="hiddenAllcheck()"
          *ngIf="!isUpdateing && !isShowAll"
        >
          <i class="iconfont icon-yingcang" style="font-size: 14px;"></i>
          <span>隐藏</span>
        </div>
        <div class="border_left_d" *ngIf="!isUpdateing"></div>
        <div class="custom_add_xy" (click)="checkAll()" *ngIf="!isUpdateing">
          <i nz-icon nzType="check-square" nzTheme="outline"></i>
          <span>全选</span>
        </div>
        <div class="border_left_d" *ngIf="!isUpdateing"></div>
        <div class="custom_add_xy" (click)="clearCheck()" *ngIf="!isUpdateing">
          <i class="iconfont icon-icon-"></i> <span>清空</span>
        </div>
        <div class="custom_add_xy" *ngIf="isUpdateing">
          <span (click)="addFactor(true)">查看</span>
        </div>
        <div class="border_left_d" *ngIf="!isUpdateing"></div>
        <div class="custom_add_xy" *ngIf="!isUpdateing">
          <span (click)="addFactor(false)">+自定义</span>
        </div>
      </div>
    </div>
    <div class="top_div">
      <ul class="div_left">
        <li class="li_list">
          <nz-checkbox-wrapper (nzOnChange)="ngModelChange($event)">
            <div
              style="display: flex; justify-content: flex-start;flex-wrap: wrap;"
            >
              <div
                class="wrapper"
                *ngFor="
                  let item of factorTable?.standardAnalysisFactorVOS;
                  let i = index
                "
              >
                <div
                  class="li_span"
                  nz-checkbox
                  [nzDisabled]="isUpdateing || item.isRequire"
                  [(ngModel)]="item.isChecked"
                  [(nzValue)]="item.standardDemographicId"
                >
                  <div style="display: flex;align-items: center;">
                    <!-- <span nz-tooltip [(nzTooltipTitle)]="item.name.zh_CN"
                      *ngIf="item.name.zh_CN.length >= 4">{{item.name.zh_CN}}</span> -->
                    <span>
                      {{ item.name.zh_CN }}
                    </span>

                    <ng-container
                      *ngIf="
                        permissionService.isPermissionOrSag(
                          'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DEMOGRAPHIC_EDIT'
                        )
                      "
                    >
                      <i
                        style="margin-left: 5px;cursor: pointer;"
                        *ngIf="
                          (item.isEdit && !item.isRequire && !isUpdateing) ||
                          item.type !== 'PULL_DOWN_BOX'
                        "
                        class="iconfont icon-bianji hover-icon"
                        (click)="spaneditor($event, item, i)"
                      ></i>
                      <ng-container *ngIf="isUpdateing; else tmp">
                        <i
                          *ngIf="item.isChecked && !item.isHidden"
                          class="iconfont icon-yanjing"
                        ></i>
                        <i
                          *ngIf="item.isChecked && item.isHidden"
                          class="iconfont icon-yanjing1"
                        ></i>
                      </ng-container>
                      <ng-template #tmp>
                        <i
                          *ngIf="item.isChecked && !item.isHidden"
                          style="cursor: pointer !important;"
                          class="iconfont icon-yanjing hover-icon"
                          (click)="changeHidden($event, item, i)"
                        ></i>
                        <i
                          *ngIf="item.isChecked && item.isHidden"
                          style="cursor: pointer !important;"
                          class="iconfont icon-yanjing1 hover-icon"
                          (click)="changeHidden($event, item, i)"
                        ></i>
                      </ng-template>
                      <div
                        *ngIf="!item.isEdit || item.isRequire || isUpdateing"
                        style="width: 17px;height: 17px;"
                      ></div>
                    </ng-container>
                  </div>
                </div>
              </div>
            </div>
          </nz-checkbox-wrapper>
        </li>
        <!-- 修改因子名称 -->
        <nz-drawer
          [(nzVisible)]="factorNameShow"
          nzTitle="修改因子名称"
          (nzOnClose)="spanCancel()"
          nzWrapClassName="round-right-drawer-new"
          [nzWidth]="400"
        >
          <div style="width: 360px;argin: 0 auto;">
            <app-i18n-input
              *ngIf="factorNameShow"
              [value]="factorName"
              (changeValue)="changeNameValue($event)"
            ></app-i18n-input>
          </div>
          <!-- <ul>
            <li>
              <p>因子名称(中)</p>
              <input
                style="margin-top: 10px;"
                nz-input
                placeholder="请输入中文名称"
                nzSize="default"
                [(ngModel)]="factorName.zh_CN"
              />
            </li>
            <li style="margin-top: 20px;">
              <p>因子名称(英)</p>
              <input
                style="margin-top: 10px;"
                nz-input
                placeholder="请输入英文名称"
                nzSize="default"
                [(ngModel)]="factorName.en_US"
              />
            </li>
          </ul> -->
          <div class="drawer-footer">
            <button nz-button nzType="primary" (click)="spanOk()">
              确认
            </button>
          </div>
        </nz-drawer>
      </ul>
    </div>

    <div class="setmodal">
      <div class="setmodal_top">
        <span style="color: #17314C;font-weight: bold;">模型设置</span>
        <span class="cur_span" (click)="showRoleModal()">高级设置</span>
      </div>
      <ul class="setmodal_card">
        <li class="card_left">
          <div
            class="left_div"
            *ngFor="let item of toolsName; let i = index"
            [ngClass]="showIndex == i ? 'showClass' : ''"
          >
            <span
              nz-tooltip
              [nzTooltipTitle]="item.title.zh_CN"
              style="width: 100px;"
              >{{ item.title.zh_CN }}</span
            >
          </div>
        </li>
        <li class="card_right">
          <div *ngIf="toolsName.length != 0">
            <div style="display: flex;justify-content: space-between;">
              <div style="display: flex;align-items: center;">
                <p class="P_1">{{ toolsName[showIndex].title.zh_CN }}</p>

                <ng-container
                  *ngIf="
                    permissionService.isPermissionOrSag(
                      'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:TOOL_NAME_EDIT'
                    )
                  "
                >
                  <i
                    class="iconfont icon-bianji"
                    style="margin-left: 10px;cursor: pointer;"
                    (click)="onEditName()"
                  ></i>
                </ng-container>
              </div>

              <ng-container
                *ngIf="
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:UPLOAD_MODULE_IMAGE'
                  )
                "
              >
                <div
                  style="color: #409EFF;cursor: pointer;"
                  (click)="getModalPie()"
                >
                  查看模型
                </div>
              </ng-container>
            </div>
            <div
              style="margin-top: 10px;"
              [innerHtml]="toolsName[showIndex].description"
            ></div>
          </div>

          <div style="flex: 1;" class="new_right">
            <div class="big_div">
              <div
                class="all_div"
                style="flex: 1;"
                *ngIf="factorTable?.detailedScoreConfigs.length != 0"
              >
                <ul
                  class="ca_tip"
                  *ngIf="standardReportType?.indexOf('CUSTOMIZE') < 0"
                >
                  <li class="ca_disp disp_8">
                    <div class="disp_t">
                      <div class="rantan"></div>
                      <div>推荐指标</div>
                    </div>
                    <div class="disp_r">
                      已选：<span *ngIf="radiolist.length != 0 && jobcodeshow"
                        >{{ radiolist[0]?.name.zh_CN }}岗位；</span
                      >
                      <span *ngIf="radioCodelist.length != 0 && jobcodeshow">{{
                        radioCodelist[0]?.name
                      }}</span>
                    </div>
                  </li>
                  <li
                    class="ca_disp disp_2"
                    *ngIf="!isUpdateing && PretestValueShow"
                  >
                    <div class="clear_disp" (click)="clearCode()">清空条件</div>
                  </li>
                </ul>
                <nz-checkbox-wrapper
                  (nzOnChange)="onChangeAllDimensions($event)"
                  class="list_ul"
                  style="flex: 1;"
                >
                  <ul class="radius_bod">
                    <li class="list_left">
                      <div
                        *ngFor="
                          let item of factorTable?.detailedScoreConfigs;
                          let i = index
                        "
                        class="left_div"
                      >
                        <div
                          *ngIf="item.name?.zh_CN"
                          style="font-weight: bold;"
                        >
                          <span
                            class="name_span"
                            nz-tooltip
                            [(nzTooltipTitle)]="item.name.zh_CN"
                            *ngIf="item.name.zh_CN.length > 5"
                            >{{ item.name.zh_CN }}</span
                          >
                          <span
                            class="name_span"
                            *ngIf="item.name.zh_CN.length <= 5"
                            >{{ item.name.zh_CN }}</span
                          >
                        </div>
                        <div class="son_div">
                          <div
                            *ngFor="
                              let val of item.detailedScoreChildDimensions;
                              let j = index
                            "
                          >
                            <div>
                              <label
                                *ngIf="item.isSelect"
                                [nzDisabled]="
                                  isdisabled ||
                                  (TipJobtip.length != 0 &&
                                    !TipJobtip[0].isConfirmed) ||
                                  val.related
                                "
                                nz-checkbox
                                [nzValue]="val.code"
                                style="display: flex;align-items: center;"
                                [(ngModel)]="val.isSelected"
                                (ngModelChange)="
                                  onChangeChildDimensions(
                                    $event,
                                    val.isSelected,
                                    item,
                                    val.code
                                  )
                                "
                              >
                                <ng-container
                                  *ngIf="val.description; else elseTemp"
                                >
                                  <span
                                    class="name_span"
                                    nz-tooltip
                                    [ngStyle]="{ color: val.color }"
                                    [nzTooltipTitle]="
                                      val.name.zh_CN +
                                      '：' +
                                      val.description.zh_CN
                                    "
                                    >{{ val.name.zh_CN }}</span
                                  >
                                </ng-container>
                                <ng-template #elseTemp>
                                  <span
                                    class="name_span"
                                    nz-tooltip
                                    [ngStyle]="{ color: val.color }"
                                    [(nzTooltipTitle)]="val.name.zh_CN"
                                    *ngIf="val.name.zh_CN.length > 5"
                                    >{{ val.name.zh_CN }}</span
                                  >
                                  <span
                                    class="name_span"
                                    [ngStyle]="{ color: val.color }"
                                    *ngIf="val.name.zh_CN.length <= 5"
                                    >{{ val.name.zh_CN }}</span
                                  >
                                </ng-template>
                              </label>
                              <span
                                class="name_span"
                                nz-tooltip
                                [(nzTooltipTitle)]="val.name.zh_CN"
                                *ngIf="
                                  !item.isSelect && val.name.zh_CN.length > 5
                                "
                                >{{ val.name.zh_CN }}</span
                              >
                              <span
                                class="name_span"
                                *ngIf="
                                  !item.isSelect && val.name.zh_CN.length <= 5
                                "
                                >{{ val.name.zh_CN }}</span
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  </ul>
                </nz-checkbox-wrapper>
              </div>
              <div
                class="all_div"
                style="flex: 1;"
                *ngIf="factorTable?.detailedScoreConfigs.length == 0"
              >
                <nz-empty
                  nzNotFoundImage="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
                  [nzNotFoundContent]="contentTpl"
                  [nzNotFoundFooter]="footerTpl"
                >
                  <ng-template #contentTpl>
                    <span> 暂无模型!请先导入题本 </span>
                  </ng-template>
                  <ng-template #footerTpl>
                    <button nz-button nzType="primary" (click)="onClickBook()">
                      定制题本
                    </button>
                  </ng-template>
                </nz-empty>
              </div>
              <div class="small_tips">
                <div class="all_tips" style="margin-top: 10px;">
                  <div class="name_mod">
                    <span class="diss_span">关联任务</span>
                  </div>
                  <!-- 对标岗位/层级 -->
                  <app-task-card
                    *ngIf="TipJobtip?.length != 0 && permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:BENCHMARKING_POSIT'
                      )"
                    text="创建岗位/层级模型"
                    btnText="对标岗位/层级"
                    [isConfirmed]="TipJobtip[0].isConfirmed"
                    (onClick)="getModalJob()"
                  >
                  </app-task-card>
                  <!-- 定制题本 -->
                  <app-task-card
                    *ngIf="
                      question_book?.length != 0 &&
                      permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:QUESTION'
                      )
                    "
                    text="定制您的专属题本"
                    btnText="定制题本"
                    [isConfirmed]="question_book[0].isConfirmed"
                    (onClick)="onClickBook()"
                  >
                  </app-task-card>
                  <!-- 评价关系 -->
                  <app-task-card
                    *ngIf="relationship?.length != 0 && permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:ASSESS_RELATIONSHIP'
                      )"
                    text="开始构建评价关系"
                    btnText="评价关系"
                    [isConfirmed]="relationship[0].isConfirmed"
                    (onClick)="onClickRelationEvaluation()"
                  >
                  </app-task-card>
                  <!-- 题本分发 -->
                  <app-task-card
                    *ngIf="
                      relationrole?.length != 0 &&
                      prismaData.isEnableRoleDimension &&
                      permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                      )
                    "
                    text="设置题本分发关系"
                    btnText="题本分发"
                    [isConfirmed]="relationrole[0].isConfirmed"
                    (onClick)="onAssociation()"
                  >
                  </app-task-card>
                  <!-- 作答说明 -->
                  <app-task-card
                    *ngIf="
                      descTip?.length != 0 &&
                      permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:ANSWER_DESCRIPTION'
                      )
                    "
                    text="自定义作答说明"
                    btnText="作答说明"
                    [isConfirmed]="descTip[0].isConfirmed"
                    (onClick)="showModalDesc()"
                  >
                  </app-task-card>
                </div>
                <div
                  class="all_tips"
                  style="margin-top: 10px;"
                  *ngIf="prismaData.projectReportDimensions.length != 0"
                >
                  <div class="name_mod">
                    <span class="diss_span">关键指标</span>
                  </div>
                  <div class="choose_name_mod" style="padding: 10px;">
                    <ul class="choose_name scroll">
                      <li
                        *ngFor="
                          let item of prismaData.projectReportDimensions;
                          let i = index
                        "
                        nz-tooltip
                        [nzTooltipTitle]="item.name"
                      >
                        {{ item.name }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <!-- 产品模型 -->
              <nz-drawer
                [(nzVisible)]="isVisiblemodal"
                nzWrapClassName="round-right-drawer-new-nofooter"
                nzTitle="产品模型"
                (nzOnClose)="modalCancel()"
                [nzWidth]="1000"
              >
                <div
                  style="display:flex;justify-content:center;align-items:center;"
                >
                  <div
                    style="display: flex;justify-content: center;"
                    *ngIf="
                      factorTable?.surveyStandardSagReportTemplate.modelType ==
                      'DYNAMIC'
                    "
                  >
                    <app-echarts-pie
                      *ngIf="echartData?.length != 0"
                      [data]="echartData"
                      [containerWidth]="'950'"
                      [containerHeight]="'750'"
                      [sunecharts]="false"
                    >
                    </app-echarts-pie>
                    <div *ngIf="echartData?.length == 0">
                      <nz-empty></nz-empty>
                    </div>
                  </div>
                  <div
                    *ngIf="
                      factorTable?.surveyStandardSagReportTemplate.modelType ==
                      'STATIC'
                    "
                    style="display: flex;justify-content: center;"
                  >
                    <div
                      *ngIf="
                        factorTable?.surveyStandardSagReportTemplate
                          .modelImageUrl != ''
                      "
                    >
                      <img [attr.src]="modelImageUrl" alt="" />
                    </div>
                    <div
                      *ngIf="
                        factorTable?.surveyStandardSagReportTemplate
                          .modelImageUrl == ''
                      "
                      style="display: flex;justify-content: center;"
                    >
                      <app-echarts-pie
                        *ngIf="echartData.length != 0"
                        [data]="echartData"
                        [containerWidth]="'950'"
                        [containerHeight]="'720'"
                        [sunecharts]="false"
                      >
                      </app-echarts-pie>
                      <div *ngIf="echartData.length == 0">
                        <nz-empty></nz-empty>
                      </div>
                    </div>
                  </div>
                </div>
              </nz-drawer>
              <!-- 维度选择提示 -->
              <nz-modal
                [(nzVisible)]="factorshow"
                [nzFooter]="null"
                [nzClosable]="false"
                nzTitle="维度选择提示"
                nzCancelDisabled="true"
                (nzOnCancel)="factorOk()"
              >
                <div>
                  子维度最多选择{{
                    factorTable?.surveyStandardSagReportTemplate
                      .maxDimensionNum
                  }}个！
                </div>
                <div style="display: flex;justify-content: flex-end;">
                  <button nz-button nzType="primary" (click)="factorOk()">
                    确认
                  </button>
                </div>
              </nz-modal>
              <!-- 对标岗位 -->
              <nz-drawer
                nzWrapClassName="round-right-drawer-new"
                [(nzVisible)]="visibleJob"
                [nzTitle]="modalTitle"
                (nzOnClose)="handleJobClose()"
                [nzWidth]="800"
              >
                <ng-template #modalTitle>
                  <div class="modalTitle">
                    <div class="titleName">
                      对标岗位
                    </div>
                  </div>
                </ng-template>
                <ul *ngIf="standardReportType == '_360_TRAIN'">
                  <li class="Pretest_dc">请选择前测活动，一键引用前测模型：</li>
                  <li class="Pretest_li">
                    <nz-select
                      class="Pretest_se"
                      nzShowSearch
                      nzAllowClear
                      [nzDisabled]="
                        projectType == 'ANSWERING' || projectType == 'OVER'
                      "
                      (ngModelChange)="pretestLog($event)"
                      nzPlaceHolder="请选择前测活动"
                      [(ngModel)]="PretestValue"
                    >
                      <nz-option
                        *ngFor="let item of PretestList"
                        [nzValue]="item.projectId"
                        [nzLabel]="item.projectName"
                      >
                      </nz-option>
                    </nz-select>
                    <div
                      *ngIf="
                        projectType != 'ANSWERING' && projectType != 'OVER'
                      "
                      class="Pretest_div"
                      (click)="getValueShow()"
                    >
                      无前测模型，立即建模
                    </div>
                  </li>
                  <li class="select_code" *ngIf="PretestValue">
                    <div class="code_ds">已选：</div>
                    <div class="codelist">
                      <div
                        *ngFor="let item of selecteddimensionList"
                        class="dimension"
                      >
                        {{ item?.name?.zh_CN }}
                      </div>
                    </div>
                  </li>
                </ul>

                <div *ngIf="PretestValueShow">
                  <div class="boxName">
                    <label
                      [nzDisabled]="
                        projectType == 'ANSWERING' || projectType == 'OVER'
                      "
                      nz-checkbox
                      [(ngModel)]="checkedvalue"
                      (ngModelChange)="ngModelSelect($event)"
                      >自选模型（以下都不匹配，您可自选或联系管理员定制）</label
                    >
                  </div>
                  <p style="padding: 10px 0;font-size: 20px;margin-top: 20px;">
                    岗位
                  </p>
                  <ul>
                    <nz-radio-group
                      [(ngModel)]="radioValue"
                      [nzDisabled]="
                        projectType == 'ANSWERING' || projectType == 'OVER'
                      "
                      (ngModelChange)="nzOnJobsType()"
                      class="radioul"
                    >
                      <li
                        *ngFor="let item of Tipjobslist; let i = index"
                        style="width: 120px;"
                      >
                        <label [nzValue]="item.id" nz-radio>{{
                          item.name.zh_CN
                        }}</label>
                      </li>
                    </nz-radio-group>
                  </ul>

                  <p style="padding: 10px 0;font-size: 20px;margin-top: 20px;">
                    层级
                  </p>
                  <ul>
                    <nz-radio-group
                      [(ngModel)]="radioCode"
                      [nzDisabled]="
                        projectType == 'ANSWERING' || projectType == 'OVER'
                      "
                      (ngModelChange)="nzOnCodesType()"
                      class="radioul"
                    >
                      <li
                        *ngFor="
                          let item of factorTable?.dimensionLevelEnumsZHnew;
                          let i = index
                        "
                        style="width: 120px;"
                      >
                        <label [nzValue]="item.code" nz-radio>{{
                          item.name
                        }}</label>
                      </li>
                    </nz-radio-group>
                  </ul>
                </div>
                <div class="drawer-footer">
                  <button
                    nz-button
                    nzType="primary"
                    (click)="handleJobOk()"
                    [nzLoading]="isSpinning"
                  >
                    确认
                  </button>
                </div>
              </nz-drawer>
              <!-- 关联设置 -->
              <!-- 题本分发 -->
              <nz-drawer
                [(nzVisible)]="Association"
                nzTitle="题本分发关联设置"
                nzWrapClassName="round-right-drawer-new"
                (nzOnClose)="AssociationCancel()"
                [nzWidth]="960"
              >
                <!-- 题本分发导入导出 -->
                <div class="association_head">
                  <nz-upload
                    [nzCustomRequest]="onDispenseImport"
                    [nzShowUploadList]="false"
                  >
                    <button nz-button nzType="link" [disabled]="isdisabled">
                      <i class="iconfont icon-import"></i> <span>导入</span>
                    </button>
                  </nz-upload>
                  <button
                    nz-button
                    nzType="link"
                    [nzLoading]="isDispenseDownLoadSpinning"
                    (click)="onDispenseDownLoad()"
                  >
                    <i class="iconfont icon-export_ic"></i> 导出
                  </button>
                </div>
                <!-- 内容 -->
                <div class="association_content">
                  <div class="association_content_left">
                    <!-- 评价角色&二级维度 -->
                    <div class="association_content_left_factors scroll">
                      <div>
                        <p>评价角色</p>
                        <div class="factors">
                          <nz-checkbox-wrapper>
                            <div class="labelBox">
                              <ng-container
                                *ngFor="
                                  let item of Associationlist?.surveyRoles
                                "
                              >
                                <label
                                  [nzDisabled]="isdisabled"
                                  nz-checkbox
                                  [nzValue]="item.id"
                                  [(ngModel)]="item.checked"
                                >
                                  <span
                                    nz-tooltip
                                    nzPlacement="topLeft"
                                    [nzTooltipTitle]="item.name.zh_CN"
                                    >{{ item.name.zh_CN }}</span
                                  >
                                </label>
                              </ng-container>
                            </div>
                          </nz-checkbox-wrapper>
                        </div>
                      </div>
                      <div class="mt-16">
                        <p>二级维度</p>
                        <div class="factors">
                          <nz-checkbox-wrapper>
                            <div class="labelBox">
                              <ng-container
                                *ngFor="
                                  let item of Associationlist?.NewcustomDimensions
                                "
                              >
                                <label
                                  [nzDisabled]="isdisabled"
                                  nz-checkbox
                                  [nzValue]="item.name"
                                  [(ngModel)]="item.checked"
                                >
                                  <span
                                    nz-tooltip
                                    nzPlacement="topLeft"
                                    [(nzTooltipTitle)]="item.name"
                                    >{{ item.name }}</span
                                  >
                                </label>
                              </ng-container>
                            </div>
                          </nz-checkbox-wrapper>
                        </div>
                      </div>
                    </div>
                    <!-- 操作 -->
                    <div class="association_content_left_operate">
                      <button
                        nz-button
                        nzType="link"
                        nzSize="small"
                        (click)="associationClear()"
                        [disabled]="isdisabled"
                      >
                        清空选项
                      </button>
                      <button
                        nz-button
                        nzType="primary"
                        nzGhost
                        (click)="associationName()"
                        nzShape="round"
                        [disabled]="isdisabled"
                      >
                        关联
                      </button>
                    </div>
                  </div>
                  <!-- 角色维度关联 -->
                  <div class="association_content_right">
                    <div
                      class="association_content_right_result"
                      *ngFor="
                        let item of Associationlist?.surveyRoles;
                        let i = index
                      "
                      [ngStyle]="{
                        marginBottom:
                          i === Associationlist?.surveyRoles.length - 1
                            ? '0'
                            : '16px'
                      }"
                    >
                      <!-- 角色 -->
                      <div class="role">
                        <span>{{ item.name?.zh_CN }}</span>
                        <button
                          nz-button
                          nzType="link"
                          nzSize="small"
                          (click)="onCloseCardAll(i)"
                          [disabled]="isdisabled"
                        >
                          清空选项
                        </button>
                      </div>
                      <div class="items" *ngIf="!isdisabled">
                        <nz-tag
                          *ngFor="let val of item.Dimensions; let j = index"
                          nzMode="closeable"
                          (nzOnClose)="onCloseCard(i, j)"
                          >{{ val.name }}</nz-tag
                        >
                      </div>
                      <!-- 关联维度 -->
                      <div class="items" *ngIf="isdisabled">
                        <nz-tag
                          *ngFor="let val of item.Dimensions; let j = index"
                          nzMode="default"
                          (nzOnClose)="onCloseCard(i, j)"
                          >{{ val.name }}</nz-tag
                        >
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 分发确认 -->
                <div class="drawer-footer">
                  <button nz-button nzType="primary" (click)="AssociationOk()">
                    确认
                  </button>
                </div>
              </nz-drawer>
              <!-- 作答说明 -->
              <nz-drawer
                nzTitle="作答说明"
                [(nzVisible)]="visibleDesc"
                nzWrapClassName="round-right-drawer-new"
                [nzWidth]="600"
                (nzOnClose)="cancelModalDesc()"
              >
                <ng-container *ngIf="visibleDesc">
                  <div class="example" *ngIf="nzSimple">
                    <nz-spin nzSimple></nz-spin>
                  </div>
                  <app-i18n-select
                    [active]="lan"
                    (selectChange)="onSelectI18n($event)"
                  ></app-i18n-select>
                  <div style="min-height: 500px; margin-top: 16px;">
                    <ng-container *ngFor="let item of i18n">
                      <div [hidden]="lan !== item.value">
                        <tinymce
                          [config]="tinyConfig"
                          id="formula-textareanew"
                          *ngIf="visibleDesc"
                          [(ngModel)]="prismaData.answerDescription[item.value]"
                          delay="10"
                        >
                        </tinymce>
                      </div>
                    </ng-container>
                  </div>
                  <div class="drawer-footer">
                    <button
                      nz-button
                      style="margin-right: 8px;"
                      (click)="setDescDefault(true)"
                    >
                      恢复默认
                    </button>
                    <button nz-button nzType="primary" (click)="okModalDesc()">
                      确认
                    </button>
                  </div>
                </ng-container>
              </nz-drawer>
              <!-- 修改工具名称-侧边栏 -->
              <nz-drawer
                [(nzVisible)]="showname"
                [nzWidth]="440"
                nzWrapClassName="round-right-drawer-new"
                nzTitle="修改工具名称"
                (nzOnClose)="namehandleCancel()"
              >
                <div>
                  <p>活动工具名称(中)</p>
                  <input
                    nz-input
                    placeholder="活动工具名称"
                    [(ngModel)]="changeeditorName.zh_CN"
                  />
                </div>
                <div style="margin-top: 20px;">
                  <p>活动工具名称(eng)</p>
                  <input
                    nz-input
                    placeholder="Activity tool name"
                    [(ngModel)]="changeeditorName.en_US"
                  />
                </div>
                <div class="drawer-footer">
                  <button nz-button nzType="primary" (click)="namehandleOk()">
                    确认
                  </button>
                </div>
              </nz-drawer>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
  <div class="submit_xy">
    <div class="client-width center_menu">
      <div>
        <span style="color: #B2B8C2;"
          >K米将在报告生成时，自动从您的账户中扣除</span
        >
      </div>
      <ul class="menus_xy">
        <li
          class="menus_left"
          (click)="submitSave()"
          nz-button
          [nzLoading]="isNzOkLoading"
          *ngIf="
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:SAVE'
            )
          "
        >
          保存
        </li>
        <li
          class="menus_right_new"
          (click)="submitPreviewSave()"
          *ngIf="
            (projectType == 'ANNOUNCED' || !projectType) &&
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:RELEASE'
            )
          "
          nz-button
          [nzLoading]="isNzPreLoading"
        >
          预发布
        </li>
        <li
          class="menus_right"
          *ngIf="
            !isUpdateing &&
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:RELEASE'
            )
          "
          (click)="submitRelase()"
          nz-button
          [nzLoading]="isNzRELoading"
        >
          发布
        </li>
      </ul>
    </div>
  </div>

  <div
    style="position: fixed;
    top: 0;bottom: 0;left: 0;right: 0;display: flex;justify-content: center;align-items: center;background-color: #eee;opacity: 0.4;z-index: 999999;"
    *ngIf="isSpinning"
  >
    <nz-spin nzSimple [nzSpinning]="isSpinning" [nzSize]="'large'"></nz-spin>
  </div>
</div>

<div class="mock_div " *ngIf="showmock">
  <ul class="bg_ul" *ngIf="showmock"></ul>
  <ul class="img_ul" *ngIf="noviceGuidance">
    <li>
      <div style="position: relative;">
        <img src="assets/images/create_2.png" alt="" />
      </div>
      <div style="margin-top: 20px;cursor: pointer;" (click)="closed()">
        <img src="assets/images/dele_new.png" alt="" />
      </div>
    </li>
  </ul>
</div>

<!-- 人口标签 -->
<nz-drawer
  class="nz_modal"
  [(nzVisible)]="addfactorshow"
  [nzWidth]="960"
  [(nzBodyStyle)]="nzBodyStyle"
  [nzWrapClassName]="'round-right-drawer-new'"
  [nzTitle]="'人口标签-' + analysisFactorTitle"
  (nzOnClose)="handleCancel()"
>
  <app-factors
    *ngIf="addfactorshow"
    [factorlist]="factorTable"
    [reportType]="reportType"
    [showAnalysisFactor]="showAnalysisFactor"
    [surveyType]="prismaData.surveyType"
    (loadDataMap)="loadDataMap($event)"
    [shownumber]="shownumber"
    [addfactorshow]="addfactorshow"
    [projectId]="projectId"
    (closemodal)="closeModal($event)"
    (cancelmodal)="handleCancel()"
    (getdefaultlist)="getdefaultlist($event)"
    [changeNumber]="changeNumber"
  >
  </app-factors>
  <div class="factorSpinning" *ngIf="factorisSpinning">
    <nz-spin
      nzSimple
      [nzSpinning]="factorisSpinning"
      [nzSize]="'small'"
    ></nz-spin>
  </div>
</nz-drawer>

<!-- 360 关联设置 -->
<app-topic-distribution-360  [projectId]="projectId" 
[questionnaireId]="questionnaireId"
(onConfirm)="onConfirm360Distribution($event)"
></app-topic-distribution-360>
