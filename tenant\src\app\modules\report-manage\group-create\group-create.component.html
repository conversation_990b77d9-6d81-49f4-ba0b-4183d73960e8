<!-- <div style="font-size: 20px;">
    当前租户余额 : <em>{{tenantBalance}}</em>
</div> -->

<div class="container">
    <div class="left scroll">
        <div class="nair" [ngClass]="{'currentNair': item.isCurrent}" *ngFor="let item of createList" (click)="clickNair(item.nairId)">
            <div class="line1">
                <span class="text0">{{item.nairName.zh_CN}}</span> <label nz-checkbox [(ngModel)]="item.isSelect"></label>
            </div>
            <div class="line2">
                <span class="text1 autoTxt" *ngIf="!!item.groupStyleType">机出报告</span>
                <span class="text1 handTxt" *ngIf="!item.groupStyleType">手出报告</span>
                <span class="text2">{{item.detailList.length}}人</span>
            </div>
            <div class="line3">
                <span class="text3">售价：{{item.cost}}K米/份</span>
            </div>
        </div>
    </div>

    <div class="right">
        <div class="title"> 
            <span>团队报告人数建议≥5人</span>
            <div class="input">
                <nz-input-group [nzPrefix]="suffixIconSearch" class="search">
                    <input type="text" nz-input placeholder="请输入姓名查找" [(ngModel)]="searchText" (keyup.enter)="searchData()" />
                  </nz-input-group>
                  <ng-template #suffixIconSearch>
                    <i nz-icon nzType="search" style="color: #409EFF" (click)="searchData()"></i>
                  </ng-template>
            </div>
        </div>

        <div class="table scroll">
            <nz-table [nzSize]="'small'" #tmpTable [nzData]="getDataList()" [nzNoResult]="'暂无数据'" [nzFrontPagination]="false">
                <thead class="thead">
                  <tr>
                    <th class="th">姓名</th>
                    <th class="th">活动名称</th>
                    <th class="th">填答日期</th>
                    <th class="th">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of tmpTable.data">
                    <td width="20%" class="td col1">{{data.name}}</td>
                    <td width="40%" class="td col2">{{data.projectName}}</td>
                    <td width="30%" class="td">{{data.time | date:'yyyy/MM/dd HH:mm'}}</td>
                    <td width="10%">
                        <img (click)="delPerson(data)" src="./assets/images/delete-ico-hover.png" alt="">
                    </td>
                  </tr>
                </tbody>
              </nz-table>
        </div>
        
    </div>

</div>

<div class="linesplit"></div>

<div class="bottom">
    <div class="pre">
        <button nz-button [disabled]="!isAgree" [nzLoading]="taskRunning" (click)="doCreateJob()"><span>确 定</span></button>
        <!-- <label nz-checkbox [(ngModel)]="isAgree"></label>
        <span>我已阅读并同意“<a href="/home" target="_blank">用户协议和隐私条款</a>”</span> -->
    </div>
    <div class="after">
        <span class="line1">共计：<span class="total">{{getTotal()}}</span>K米</span>
        <span class="line2">K米将自动从您的账户中扣除</span>
    </div>
</div>