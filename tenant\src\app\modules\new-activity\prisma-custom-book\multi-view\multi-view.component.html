<div class="penetration-questions" (click)="createPenetrationQuestions()">
  <i class="iconfont icon-TBDSJ"></i>
  <span class="penetration-span">题本多视角</span>
  <nz-divider nzType="vertical"></nz-divider>
</div>

<!-- 题本穿透弹窗 -->
<nz-drawer
  [nzBodyStyle]="{
    height: 'calc(100% - 55px)',
    overflow: 'hidden',
    'padding-bottom': '53px'
  }"
  [nzMaskClosable]="false"
  [nzWidth]="1000"
  [nzVisible]="visible"
  nzTitle="题本多视角"
  (nzOnClose)="closePenetraModal()"
  nzWrapClassName="multi-view-round-right-drawer"
>
  <div class="multi-view">
    <div class="multi-view-body">
      <div class="left" *ngIf="step == 1 && dimensionList.length > 0">
        <ul>
          <li
            *ngFor="let item of dimensionList; let i = index"
            [ngClass]="{ active: activeIndex == i }"
            (click)="updateActive(i)"
          >
            {{ item.dimensionName[lan] }}
          </li>
        </ul>
      </div>
      <div class="right">
        <div class="header">
          <div class="header-left">
            <div
              class="associate-btn"
              nz-popover
              [nzPopoverTitle]="titleTemplate"
              nzPopoverTrigger="click"
              [(nzVisible)]="popoverVisible"
              [nzPopoverContent]="contentTemplate"
              nzPopoverPlacement="bottomRight"
            >
              已选关联({{ penetrationList.length }})
            </div>
            <ng-template #titleTemplate>
              <div class="popover-header">
                <span>关联详情</span>
                <a
                  *ngIf="penetrationList.length > 0"
                  nz-popconfirm
                  nzPopconfirmTitle="是否清空所有关联？"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="confirm()"
                  (nzOnCancel)="cancel()"
                  >清空所有关联</a
                >
              </div>
            </ng-template>
            <ng-template #contentTemplate>
              <div class="multi-popover-body">
                <ul>
                  <li
                    class="multi-popover-body-item"
                    *ngFor="let ques of penetrationList"
                  >
                    <div class="multi-popover-body-item-header">
                      <span
                        class="multi-popover-body-item-header-identifying"
                      ></span>
                      <p
                        nz-tooltip
                        nzTooltipPlacement="topLeft"
                        [nzTooltipTitle]="titleTemplate"
                        class="multi-popover-body-item-header-topic"
                        [innerHTML]="ques.questionName[lan] | html"
                      ></p>
                      <ng-template #titleTemplate>
                        <div [innerHTML]="ques.questionName[lan] | html"></div>
                      </ng-template>
                      <span
                        class="multi-popover-body-item-header-del"
                        nz-popconfirm
                        nzPopconfirmTitle="是否删除当前关联？"
                        nzPopconfirmPlacement="bottom"
                        (nzOnConfirm)="delOne(ques.questionId)"
                        >删除</span
                      >
                    </div>
                    <ul class="multi-popover-body-item-main">
                      <li *ngFor="let item of ques.dimensionList">
                        <span>{{ item.labelName.zh_CN }}</span>
                        <span *ngIf="item.oneRankDimensionCode"
                          >/{{ item.oneRankDimensionName.zh_CN }}</span
                        >
                        <span *ngIf="item.twoRankDimensionCode"
                          >/{{ item.twoRankDimensionName.zh_CN }}</span
                        >
                        <span *ngIf="item.threeRankDimensionCode"
                          >/{{ item.threeRankDimensionName.zh_CN }}</span
                        >
                      </li>
                    </ul>
                  </li>
                </ul>
              </div>
            </ng-template>

            <button
              nz-button
              nzType="primary"
              nzGhost
              (click)="association()"
              style="height: 28px;"
              [disabled]="step == 1"
            >
              关联
            </button>
          </div>
          <div class="header-right">
            <nz-upload [nzCustomRequest]="customReq" [nzShowUploadList]="false">
              <button nz-button nzType="link" class="btn3">
                <i class="iconfont icon-import"></i> 导入
              </button>
            </nz-upload>
            <button nz-button nzType="link" class="btn3" (click)="exportR()">
              <i class="iconfont icon-export_ic"></i> 导出
            </button>
          </div>
        </div>
        <div
          class="right-content"
          *ngIf="step == 1 && dimensionList.length > 0"
        >
          <!-- (ngModelChange)="updateAllChecked($event)" -->

          <div class="right-content-header">
            <label
              nz-checkbox
              [(ngModel)]="dimensionList[activeIndex].allChecked"
              (ngModelChange)="updateAllChecked($event)"
              [nzIndeterminate]="dimensionList[activeIndex].indeterminate"
            >
              全选
            </label>
            <div class="search">
              <nz-input-group [nzPrefix]="suffixIcon">
                <input
                  style="border-radius:8px;"
                  type="text"
                  nz-input
                  placeholder="请输入关键词"
                  [(ngModel)]="searchValue"
                />
              </nz-input-group>

              <ng-template #suffixIcon>
                <i nz-icon nzType="search"></i>
              </ng-template>
            </div>
          </div>
          <div class="right-content-main">
            <nz-tree
              style="width: 100%;"
              nzCheckable
              [nzData]="dimensionList[activeIndex].questionList"
              [nzSearchValue]="searchValue"
              [nzCheckedKeys]="dimensionList[activeIndex].checkedKeys"
              (nzCheckBoxChange)="nzEvent($event)"
              (nzClick)="nzEvent($event)"
              (nzSearchValueChange)="nzEvent($event)"
            >
            </nz-tree>
          </div>
        </div>
        <div class="right-topic-content" *ngIf="step == 2">
          <nz-collapse [nzBordered]="false">
            <nz-collapse-panel
              #html
              *ngFor="let panel of problemData; let i = index"
              [nzHeader]="header"
              [nzActive]="panel.active"
              [ngStyle]="customStyle"
              [nzExtra]="extraTpl"
              (nzActiveChange)="activeChange(i, $event)"
            >
              <div class="right-topic-content-list">
                <div class="right-topic-content-list-item disable">
                  <i
                    class="right-topic-content-list-item-drag-icon iconfont icon-caidan"
                  ></i>
                  <span class="right-topic-content-list-item-sort">1</span>
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="请选择指数"
                    style="width:216px; margin: 0 8px 0 0;"
                    [(ngModel)]="panel.listFirst.label"
                  >
                    <nz-option
                      *ngFor="let item of optionsMap.prismaLabel"
                      [nzLabel]="item.labelName"
                      [nzValue]="item.parentDimensionCode"
                      nzCustomContent
                      ><span nz-tooltip [nzTooltipTitle]="item.labelName">
                        {{ item.labelName }}
                      </span></nz-option
                    >
                  </nz-select>
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="请选择一级维度"
                    style="width:216px; margin: 0 8px 0 0;"
                    [(ngModel)]="panel.listFirst.oneRankDimensionCode"
                  >
                    <nz-option
                      *ngFor="let item of optionsMap.oneRank"
                      [nzLabel]="item.nameCn + '-' + item.code"
                      [nzValue]="item.code"
                      nzCustomContent
                      ><span
                        nz-tooltip
                        [nzTooltipTitle]="item.nameCn + '-' + item.code"
                      >
                        {{ item.nameCn + "-" + item.code }}
                      </span></nz-option
                    >
                  </nz-select>
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="请选择二级维度"
                    style="width:216px; margin: 0 8px 0 0;"
                    [(ngModel)]="panel.listFirst.twoRankDimensionCode"
                  >
                    <nz-option
                      *ngFor="let item of optionsMap.twoRank"
                      [nzLabel]="item.nameCn + '-' + item.code"
                      [nzValue]="item.code"
                      nzCustomContent
                      ><span
                        nz-tooltip
                        [nzTooltipTitle]="item.nameCn + '-' + item.code"
                      >
                        {{ item.nameCn + "-" + item.code }}
                      </span></nz-option
                    >
                  </nz-select>
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="请选择三级维度"
                    style="width:216px; margin: 0 8px 0 0;"
                    [(ngModel)]="panel.listFirst.threeRankDimensionCode"
                  >
                    <nz-option
                      *ngFor="let item of optionsMap.threeRank"
                      [nzLabel]="item.nameCn + '-' + item.code"
                      [nzValue]="item.code"
                      nzCustomContent
                      ><span
                        nz-tooltip
                        [nzTooltipTitle]="item.nameCn + '-' + item.code"
                      >
                        {{ item.nameCn + "-" + item.code }}
                      </span></nz-option
                    >
                  </nz-select>
                  <i class="right-topic-content-list-item-delete"></i>
                </div>
                <div
                  dragula="MULTIVIEW"
                  [(dragulaModel)]="panel.listResidue"
                  [id]="i"
                >
                  <div
                    class="right-topic-content-list-item"
                    *ngFor="let data of panel.listResidue; let j = index"
                    style="display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 100% ;
                    height: 44px;"
                  >
                    <i
                      style="font-size: 20px;color: #C4C4C4;"
                      class="right-topic-content-list-item-drag-icon iconfont icon-caidan"
                    ></i>
                    <span
                      class="right-topic-content-list-item-sort"
                      style=" font-size: 14px;
                      font-family: PingFangSC, PingFang SC;
                      font-weight: 400;
                      color: #262626;
                      line-height: 20px;
                      margin: 0 16px;"
                      >{{ 2 + j }}</span
                    >
                    <nz-select
                      nzShowSearch
                      nzAllowClear
                      nzPlaceHolder="请选择指数"
                      style="width:216px; margin: 0 8px 0 0;"
                      [(ngModel)]="data.label"
                    >
                      <nz-option
                        *ngFor="let item of optionsMap.prismaLabel"
                        [nzLabel]="item.labelName"
                        [nzValue]="item.parentDimensionCode"
                        nzCustomContent
                        ><span nz-tooltip [nzTooltipTitle]="item.labelName">
                          {{ item.labelName }}
                        </span></nz-option
                      >
                    </nz-select>
                    <nz-select
                      nzShowSearch
                      nzAllowClear
                      nzPlaceHolder="请选择一级维度"
                      style="width:216px; margin: 0 8px 0 0;"
                      [(ngModel)]="data.oneRankDimensionCode"
                    >
                      <nz-option
                        *ngFor="let item of optionsMap.oneRank"
                        [nzLabel]="item.nameCn + '-' + item.code"
                        [nzValue]="item.code"
                        nzCustomContent
                        ><span
                          nz-tooltip
                          [nzTooltipTitle]="item.nameCn + '-' + item.code"
                        >
                          {{ item.nameCn + "-" + item.code }}
                        </span></nz-option
                      >
                    </nz-select>
                    <nz-select
                      nzShowSearch
                      nzAllowClear
                      nzPlaceHolder="请选择二级维度"
                      style="width:216px; margin: 0 8px 0 0;"
                      [(ngModel)]="data.twoRankDimensionCode"
                    >
                      <nz-option
                        nzCustomContent
                        *ngFor="let item of optionsMap.twoRank"
                        [nzLabel]="item.nameCn + '-' + item.code"
                        [nzValue]="item.code"
                        ><span
                          nz-tooltip
                          [nzTooltipTitle]="item.nameCn + '-' + item.code"
                        >
                          {{ item.nameCn + "-" + item.code }}
                        </span></nz-option
                      >
                    </nz-select>
                    <nz-select
                      nzShowSearch
                      nzAllowClear
                      nzPlaceHolder="请选择三级维度"
                      style="width:216px; margin: 0 8px 0 0;"
                      [(ngModel)]="data.threeRankDimensionCode"
                    >
                      <nz-option
                        *ngFor="let item of optionsMap.threeRank"
                        [nzLabel]="item.nameCn + '-' + item.code"
                        [nzValue]="item.code"
                        nzCustomContent
                        ><span
                          nz-tooltip
                          [nzTooltipTitle]="item.nameCn + '-' + item.code"
                        >
                          {{ item.nameCn + "-" + item.code }}
                        </span></nz-option
                      >
                    </nz-select>
                    <i
                      nz-popconfirm
                      nzPopconfirmTitle="是否删除当前视角？"
                      nzPopconfirmPlacement="bottom"
                      (nzOnConfirm)="delMulti(i, j)"
                      style="color: #262626; font-size: 14px;  width: 14px;"
                      class="right-topic-content-list-item-delete iconfont  icon-icon_delete"
                    ></i>
                  </div>
                </div>
              </div>
              <ng-template #extraTpl>
                <!-- You can use stopPropagation if you don't want the panel to toggle -->
                <button
                  nz-button
                  nzType="link"
                  class="btn3"
                  [disabled]="panel.listResidue.length > 5"
                  (click)="add($event, i)"
                >
                  <i class="iconfont icon-plus-circle"></i> 添加视角
                </button>
              </ng-template>

              <ng-template #header>
                <!-- You can use stopPropagation if you don't want the panel to toggle -->
                <p [innerHTML]="panel.name | html"></p>
              </ng-template>
            </nz-collapse-panel>
          </nz-collapse>
        </div>
      </div>
    </div>
  </div>
  <div class="drawer-footer">
    <div class="drawer-footer-left" *ngIf="step == 1">
      已选：<em>{{ getDimensionNum() }}</em
      >个维度
    </div>
    <div *ngIf="step == 2"></div>
    <div>
      <button *ngIf="step == 1" class="cancel-btn" nz-button (click)="reset()">
        恢复默认
      </button>
      <button
        *ngIf="step == 1"
        nz-button
        nzType="primary"
        (click)="nextStep()"
        [nzLoading]="nextStepLoading"
        [nzLoading]="tplModalButtonLoading"
      >
        下一步
      </button>

      <button
        *ngIf="step == 2"
        nz-button
        nzType="primary"
        (click)="createQues()"
        [nzLoading]="tplModalButtonLoading"
      >
        上一步
      </button>
    </div>
  </div>
</nz-drawer>
<!-- 题本穿透弹窗 end-->
