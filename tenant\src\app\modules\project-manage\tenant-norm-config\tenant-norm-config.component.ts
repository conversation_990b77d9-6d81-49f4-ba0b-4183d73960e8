import { Component, OnInit } from "@angular/core";
import { ProjectManageService } from "../../service/project-manage.service";
import { ActivatedRoute, Router } from "@angular/router";
import { NzMessageService, UploadXHRArgs } from "ng-zorro-antd";
import { isNumber, isArray } from "util";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { HttpClient, HttpEvent } from "@angular/common/http";
import { Observable } from "rxjs";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-tenant-norm-config",
  templateUrl: "./tenant-norm-config.component.html",
  styleUrls: ["./tenant-norm-config.component.less"],
})
export class TenantNormConfigComponent implements OnInit {
  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/project-manage",
      name: "活动管理",
      Highlight: false,
    },
    {
      path: "",
      name: "租户常模配置",
      Highlight: true,
    },
  ];
  permission; //更新租户配置权限
  keyValue: string = "";
  selectedValue: string = "";
  norms = []; //常模
  projectId;
  tools = [];
  tenantUrl: string = "/tenant-api";
  listOfData = [];
  constructor(
    private projectManageService: ProjectManageService,
    private router: Router,
    private msgServ: NzMessageService,
    private routerInfo: ActivatedRoute,
    private surveySerivce: SurveyApiService,
    private http: HttpClient,
    private customMsg: MessageService,
        public permissionService: PermissionService,
  ) {}

  ngOnInit() {
    this.projectId = this.routerInfo.snapshot.queryParams.projectId;
    this.init();

    this.permission = this.permissionService.isPermission();
  }

  init() {
    let params = {
      name: this.keyValue,
      projectId: this.projectId
    };
    this.projectManageService.getTenantNormConfigList(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.listOfData = res.data;
      }
    });
  }



  submit() {
    let prismaNormList = JSON.parse(JSON.stringify(this.listOfData));
    // params.forEach((element) => {
    //   if (element.cm) {
    //     element.algorithm.assessmentNorms = this.textareaToModel(element.cm);
    //   }
    // });
   let params= {
    projectId: this.projectId,
    prismaNormVOList: prismaNormList
  }
    this.projectManageService
      .saveTenantNormConfigList(params)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.msgServ.success("保存成功");
          this.init();
        }
      });
  }

  /**
   * refresh
   */
  refresh() {
    this.projectManageService
          .updateProjectNorm(this.projectId)
          .toPromise()
          .then((res) => {
            if (res.result.code === 0) {
              this.msgServ.success("更新成功");
              this.init();
            } else {
              this.customMsg.open("error", res.result.message);
            }
          })
          .catch((err) => {})
          .finally(() => {});
      
      }
}
