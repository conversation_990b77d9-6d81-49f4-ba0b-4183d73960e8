@import "./variable.less";
* {
  margin: 0;
  padding: 0;
}
s {
  text-decoration: none;
}
.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.content {
  margin: 0 auto;
}
.app-layout {
  background: #fff;
}
p {
  margin: 0;
}
li {
  list-style: none;
}
.flex {
  display: flex;
}
.space-between {
  justify-content: space-between;
}
layout-default {
  position: initial !important;
  display: flex !important;
  overflow: hidden;
  .default {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 1px;
    //overflow-y: overlay !important;
    left: 0;
    flex: 1;
    padding: 15px 0 0 0;
    overflow: hidden;
    transition: margin-left 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
}

page-header {
  .page-header {
    margin: 0 0 15px 0 !important;
    padding: 10px 15px 0 15px !important;
  }
}

.page-buttons {
  .buttons {
    padding: 0 0 15px 0;
  }

  .pagination {
    text-align: right;
  }
}

.page-body {
  .vxscrollbar();
  flex: 1;
  width: 100%;
  height: 100%;
}
.client-width {
  width: 1200px;
}
// @media (min-width: 1024px){
//   .client-width{
//     width: 980px;
//   }
// }

// @media (min-width: 1280px) {
//   .client-width{
//     width: 1200px;
//   }
// }

// @media (min-width: 1366px) {
//   .client-width{
//     width: 1000px;
//   }
// }

// @media (min-width: 1440px) {
//   .client-width{
//     width: 1200px;
//   }
// }
