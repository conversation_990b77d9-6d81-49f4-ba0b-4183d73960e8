import { Component, OnInit, Input, OnD<PERSON>roy } from "@angular/core";
import _ from "lodash";

import { NzDrawerRef } from "ng-zorro-antd/drawer";
import { NewPrismaService } from "../new-prisma.service";
import { UploadXHRArgs, UploadFile } from "ng-zorro-antd/upload";
import { NzModalService, NzMessageService } from "ng-zorro-antd";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { DragulaService } from "ng2-dragula";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";

@Component({
  selector: "app-report-display",
  templateUrl: "./report-display.component.html",
  styleUrls: ["./report-display.component.less"],
})
export class ReportDisplay implements OnInit, OnDestroy {
  @Input() projectId: string;
  @Input() reportType: string;
  @Input() standardAnalysisFactorVOS: any;
  constructor(
    private drawerRef: NzDrawerRef,
    private api: NewPrismaService,
    private nzModalService: NzModalService,
    private msg: NzMessageService,
    private http: HttpClient,
    private dragulaService: DragulaService,
    private router: Router
  ) {}
  private routerSubscription: Subscription;
  loading = false;
  // 是否全部选中
  isSelectAll = true;
  listOfData = [];

  optionReport = [
    {
      name: "RESEARCHERS_DISTRIBUTION",
      type: "调研人员分布(人数/占比)",
      checked: true,
    },
    {
      name: "POP_LABELS_CROSS_ANALYSIS",
      type: "人口标签交叉分析(四窗)",
      checked: true,
    },
    {
      name: "GROUP_ANALYSIS",
      type: "群体分析(详细列表)",
      checked: true,
    },
    {
      name: "KEY_POPULATION_ANALYSIS",
      type: "重点人群分析(定制报告专用)",
      checked: true,
    },
    {
      name: "POP_LABELS_CROSS_ANALYSIS_OVERVIEW",
      type: "双低人群概览(定制报告专用)",
      checked: true,
    },
    {
      name: "APPENDIX",
      type: "附录",
      checked: true,
    },
  ];
  closeModal() {
    this.drawerRef.close();
  }

  ngOnInit(): void {
    this.listOfData = [];
    let type = [
      "EPSON_INVESTIGATION_RESEARCH_CUSTOM",
      "NETEASE_INVESTIGATION_RESEARCH_CUSTOM",
      "TENCENT_INVESTIGATION_RESEARCH_CUSTOM",
      "VIVO_INVESTIGATION_RESEARCH_CUSTOM",
    ];
    if (!type.includes(this.reportType)) {
      this.optionReport.splice(3, 2);
    }
    // console.log(this.optionReport, "[[]]");

    this.standardAnalysisFactorVOS.forEach((item) => {
      let obj = {};
      if (!item.labelShowReportPage) {
        item.labelShowReportPage = [];
        this.optionReport.forEach((data) => {
          obj[data.name] = {
            name: data.name,
            checked: true,
          };
          item.labelShowReportPage.push(data.name);
        });
      } else {
        this.optionReport.forEach((data) => {
          if (item.labelShowReportPage.includes(data.name)) {
            obj[data.name] = {
              name: data.name,
              checked: true,
            };
          } else {
            obj[data.name] = {
              name: data.name,
              checked: false,
            };
          }
        });
      }
      // 判断是否已经全部选中
      if (this.isSelectAll) {
        if (!item.labelShowReportPage || item.labelShowReportPage.length < 6) {
          this.isSelectAll = false;
        }
      }
      this.listOfData.push({
        ...item,
        labelShowReportPageList: _.cloneDeep(obj),
      });
    });
    this.determineIsSelectAll();
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
  /**
   *判断是否全部选中
   *@author:wangxiangxin
   *@Date:2023/09/14
   */
  determineIsSelectAll() {
    let flag = true;
    this.listOfData.forEach((item) => {
      item.labelShowReportPage = [];
      Object.values(item.labelShowReportPageList).forEach((element: any) => {
        if (element.checked) {
          item.labelShowReportPage.push(element.name);
        }
      });
      // 判断是否已经全部选中

      if (flag) {
        if (!item.labelShowReportPage || item.labelShowReportPage.length < 6) {
          flag = false;
        }
      }
    });
    this.isSelectAll = flag;
  }
  handClick() {
    this.loading = true;
    this.listOfData.forEach((item) => {
      item.labelShowReportPage = [];
      Object.values(item.labelShowReportPageList).forEach((element: any) => {
        if (element.checked) {
          item.labelShowReportPage.push(element.name);
        }
      });
    });
    this.api
      .saveOrUpdate({
        projectId: this.projectId,
        analysisFactorDto: this.listOfData,
      })
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.msg.success("保存成功");
          this.drawerRef.close(this.listOfData);
        }
        this.loading = false;
      });
  }
  invert() {
    this.listOfData.forEach((item) => {
      item.labelShowReportPage = [];
      for (let k in item.labelShowReportPageList) {
        item.labelShowReportPageList[k].checked = !this.isSelectAll;
      }
    });
    this.determineIsSelectAll();
  }
  /**
   * 点击单个复选框的时候 重新判断是否全部选中
   *@author:wangxiangxin
   *@Date:2023/09/14
   */
  checkChange() {
    this.determineIsSelectAll();
  }
}
