import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { WrapperRegisterComponent } from "./wrapper-register/wrapper-register.component";
import { WrapperLoginComponent } from "./wrapper-login/wrapper-login.component";
import { WrapperForgotComponent } from "./wrapper-forgot/wrapper-forgot.component";
import { LoginMobileComponent } from "./login-mobile/login-mobile.component";
import { SuccessMobileComponent } from "./login-mobile/success/success.component";

import { AgreeComponent } from "./login/Userknx/agreement.component";
import { PolicyComponent } from "./login/Privacy/policy.component";
import { ResetComponent } from "./reset/reset.component";

const routes: Routes = [
  {
    path: "",
    component: null,
    children: [
      { path: "", redirectTo: "login" },
      { path: "reset", component: ResetComponent },
      { path: "login", component: WrapperLoginComponent },
      { path: "register", component: WrapperRegisterComponent },
      { path: "forgot", component: WrapperForgotComponent },
      { path: "mobile", component: LoginMobileComponent },
      { path: "reg-success", component: SuccessMobileComponent },
      {
        path: "userAgree",
        component: AgreeComponent,
        data: { title: "KNX隐私政策", titleI18n: "KNX隐私政策" },
      },
      {
        path: "userPolicy",
        component: PolicyComponent,
        data: { title: "KNX用户协议", titleI18n: "KNX用户协议" },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LoginRoutingModule {}
