* {
  margin: 0;
  padding: 0;
}
img{display:inline-block;}

nz-select {
  width: 130px !important;
  // ::ng-deep .ant-select-selection--single, .ant-select-selection--single {
  //   height: 36px !important;
  // }
}

nz-range-picker {
  width: 262px;
  height: 36px;
}

::ng-deep {
  .simple-page-class {
    .ant-pagination-item-link{
      background-color: #F3F7FB !important;
    }
  }
}

.container {  // 容器
  margin: 0px auto;
  // width: 1202px;
  h1 { // 标题
    font-size: 24px;
    font-family: PingFangSC-Thin, PingFang SC;
    font-weight: 100;
    color: #17314C;
    line-height: 33px;
    margin-top: 25px;
  }
  // header str
  header {
    p {
      width: 100%;
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #419EFF;
      line-height: 17px;
      text-align: right;
    }
    .search-box{ // 搜索条件 box
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      margin-bottom: 30px;
      .new-event { // 新建活动
        width: 125px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 2px;
        cursor: pointer;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
      }
      li:not(:last-child) {
        margin-right: 10px;
      }
      li:last-child {
        button {
          width: 68px;
          height: 32px;
          background: #FFFFFF;
          border-radius: 2px;
          border: 1px solid #409EFF;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #409EFF;
          line-height: 20px;
          cursor: pointer;
        }
      }
    }

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 1202px;
      height: 49px;
      background: #F3F7FB;
      border-radius: 4px 4px 0px 0px;
      .left {
        display: flex;
        align-items: center;
        h2 {
          width: 64px;
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #495970;
          line-height: 22px;
          margin-left: 29px;
          position: relative;
          margin-right: 71px;
          .img_up {
            position: absolute;
            top: 6px;
            right: -16px;
            width: 7px;
            height: 4px;
            transform: rotate(180deg);
            cursor: pointer;
            background: url(../../../../assets/images/event-management/home/<USER>
          }
          .img_down {
            position: absolute;
            bottom: 6px;
            right: -16px;
            width: 7px;
            height: 4px;
            cursor: pointer;
            background: url(../../../../assets/images/event-management/home/<USER>
          }
          .img_up_hover {
            transform: rotate(0deg);
            background: url(../../../../assets/images/event-management/home/<USER>
          }
          .img_down_hover {
            transform: rotate(180deg);
            background: url(../../../../assets/images/event-management/home/<USER>
          }
        }
      }

      .right {
        display: flex;
        align-items: center;
        .tab-box {
          display: flex;
          .card, .list {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #495970;
            line-height: 20px;
            cursor: pointer;
            span {
              margin-left: 7px;
              margin-right: 26px;
            }
          }
          .card {
            border-right: 1px solid #CBCBCB;
          }
          .list {
            margin-left: 26px;
          }
          
        }

        .pagination {
          display: flex;
          align-items: center;
          margin-right: 21px;
          .iconfont:hover {
            color: #409EFF;
            cursor: pointer;
          }
          .page-num {
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            line-height: 22px;
            padding: 0 14px;
            >span {
              color: #409EFF;
            }
          }
          .page-no {
            color: #C0C0C0 !important;
            cursor: not-allowed !important;
          }
        }

      }
    }
  }
  // header end
  .blue-text {
    color: #409EFF !important;
  }
  // list 列表
  .listbox {
    padding-top: 20px;
    .empty-box { // 空状态
      display: flex;
      flex-direction: column;
      padding-top: 92px;
      align-items: center;
      p {
        text-align: center;
        padding: 45px 0 38px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #495970;
        line-height: 22px;
      }
      .new-event {
        width: 182px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 2px;
        cursor: pointer;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
      }
    }
    .empty-search-box {
      display: flex;
      flex-direction: column;
      padding-top: 92px;
      align-items: center;
      p {
        text-align: center;
        padding: 45px 0 38px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #495970;
        line-height: 22px;
      }
      .new-event {
        width: 125px;
        height: 36px;
        background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 2px;
        line-height: 36px;
        text-align: center;
        cursor: pointer;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
      }
    }
    .card-list-box { // 卡片列表
      display: flex;
      // justify-content: space-between;
      margin-bottom: 30px;
      li {
        width: 100%;
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        div {
          margin: 0 10px 20px 10px;
        }
      //   div:last-child {
      //     margin: 0 0 20px 0;
      //   }
      }
    }
    .standard-list-box { // 标准列表
      margin-bottom: 20px;
      li {
        // display: flex;
        // justify-content: space-between;
        // flex-wrap: wrap;
      }
    }
  }

  footer {
    margin-bottom: 40px;
    text-align: right;
  }
}

.example {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  background: rgba(0, 0, 0, 0.05);
  
}

.mock_div {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 9999;
  .bg_ul {
    width: 100%;
    height: 100%;
    background-color: #000000;
    opacity: 0.6;
  }
  .img_ul{
    position: absolute;
    top: 300px;
    left: 200px;
    color: #fff;
    
  }
  .btn_div {
    display: flex;
    margin-top: 20px;
    
    >div{
      cursor: pointer;
    }
    .left_d{
      width: 104px;
      line-height: 36px;
      border-radius: 19px;
      text-align: center;
      border: 1px solid #ffffff;
    }
    .right_d{
      margin-left: 20px;
      width: 104px;
      line-height: 36px;
      border-radius: 19px;
      text-align: center;
      background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
    }
  }
}