<nz-modal
  [(nzVisible)]="visible"
  [nzWrapClassName]="'vertical-center-modal'"
  (nzOnCancel)="handleCancel()"
  nzWidth="480"
  (nzOnOk)="handleOk()"
  [nzFooter]="modalFooter"
  class="updatePassword"
>
  <p class="title">修改密码</p>
  <form
    nz-form
    [formGroup]="validateForm"
    class="login-form"
    (ngSubmit)="submitForm()"
  >
    <nz-form-item *ngIf="this.type !== 'email'">
      <nz-form-control nzErrorTip="请输入旧密码">
        <input
          nz-input
          type="password"
          formControlName="oldPassword"
          (ngModelChange)="updateConfirmValidator()"
          placeholder="请输入旧密码"
        />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-control nzErrorTip="请输入新的密码">
        <input
          nz-input
          type="password"
          id="password"
          formControlName="newPassword"
          (ngModelChange)="updateConfirmValidator()"
          placeholder="请输入新密码"
        />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-control [nzErrorTip]="errorTpl">
        <input
          nz-input
          type="password"
          formControlName="checkPassword"
          id="checkPassword"
          placeholder="请再次输入"
        />
        <ng-template #errorTpl let-control>
          <ng-container *ngIf="control.hasError('required')">
            请确认新密码!
          </ng-container>
          <ng-container *ngIf="control.hasError('confirm')">
            两次密码不一致!
          </ng-container>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </form>
  <ng-template #modalFooter>
    <button
      nz-button
      [ngClass]="{ submit: true, act: true }"
      (click)="handleOk()"
    >
      提交
    </button>
  </ng-template>
</nz-modal>
