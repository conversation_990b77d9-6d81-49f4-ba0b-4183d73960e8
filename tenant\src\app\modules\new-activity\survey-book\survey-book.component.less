@bgColor: #40a9ff;
@fgColor: white;

.content {
  background-color: white;
  height: 100%;
  display: flex;
  justify-content: flex-start;

  .body {
    flex: 1;
    margin-left: 15px;
    margin-right: 15px;
    padding: 8px;
  }

  .title {
    margin: 20px 30px 20px 0;
    font-size: 24px;
    font-family: PingFangSC-Thin, PingFang SC;
    font-weight: 200;
    color: #17314C;
  }

  .searchDiv {
    margin-left: 50px;
  }

  .input-search {
    border-radius: 20px;
    width: 200px;
  }

  .icon-search {
    color: #409EFF;
  }

  .tab {
    font-size: 24px;
    font-weight: 500;
    color: #17314C;
    line-height: 33px;
  }

  .action {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 252px;
  }

  .projName {
    max-width: 400px;
    white-space: pre-wrap;
    text-align: center;
    font-size: 20px;
    font-weight: 600;
    color: #17314C;
    line-height: 28px;
  }

  .topiclist {
    // height: 600px;
    // height: 100%;

    .topic {
      padding: 5px 2px;
      min-height: 100px;
      border-top: solid 1px #E6E6E6;
      max-width: 1150px;

      .ques {
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
        font-size: 16px;
        font-weight: 400;
        color: #17314C;
        line-height: 28px;

        .quesName {
          display: flex;
        }

        span {
          max-width: 1015px;
        }
      }

      .optionlist {
        display: flex;
        flex-wrap: wrap;
        max-width: 100%;

        div {
          padding-bottom: 10px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #17314C;
          line-height: 22px;
        }

        .marg {
          margin-right: 60px;
        }
      }

    }

    .double {
      // background-color: #CEE7FF;
      background-color: #F5FAFF;
    }

  }

}

.last_topic {
  border-bottom: solid 1px #E6E6E6;
}

.iptBtn {
  width: 128px;
  height: 38px;
  background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  font-weight: 500;
  color: #FFFFFF;
  // font-size: 14px;
}