import {
  Component,
  OnInit,
  Input,
  ViewChild,
  ChangeDetectorRef,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { ProjectManageService } from "../../../service/project-manage.service";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ValidationErrors,
  Validators,
} from "@angular/forms";
import {
  NzFormatEmitEvent,
  NzMessageService,
  NzModalRef,
  NzTreeComponent,
  NzTreeNode,
  NzTreeNodeOptions,
} from "ng-zorro-antd";
import { isNull } from "util";
import { MessageService } from "@src/shared/custom-message/message-service.service";

import _ from "lodash";

interface Question {
  id: string;
  name: any; //题干
  nameEn: any;
  type: string; //题型
  code: string; //题目编码
  customDimension: null;
  label: string; // 标签
  scope: string; // 计算
  parentCustomDimension: null; //二级维度
  childCustomDimension: null;
  proportion: number; //比重
  isRequire: Boolean; //选填必填
  markcode: null;
}

interface Option {
  id: string | number;
  name: any;
  value: number; //分值
  isScoring: boolean; //是否计分
  scope;
  inputshow: boolean;
  stepScore;
  isValid;
  lowestScore;
  highestScore;
  isEnableOpenAnswer: boolean;
  isRequireOpenAnswer: boolean;
}

@Component({
  selector: "app-add-prisma-custom-book",
  templateUrl: "./add-prisma-custom-book.component.html",
  styleUrls: ["./add-prisma-custom-book.component.less"],
})
export class AddPrismaCustomBookComponent implements OnInit {
  @Input() id: string; //题目id
  @Input() questionnaireId: string; //问卷id
  @Input() isPierceThrough: boolean; //问卷id
  @Input() projectId: string;
  @Input() isedited: Boolean;
  @Input() nametip: string;
  @Input() status: string;
  @Input() reportType: string;

  validateForm: FormGroup;

  optionList: Option[] = []; //新增、编辑选项列表

  factorlist: any = []; //添加维度
  ClassAfactor: any = []; //一级维度
  ClassBfactor: any = []; //二级维度

  ClassCfactor: any = []; //三级维度
  oldDemoshow: number = 1; // 1 新增 2 维度 3 题本修订
  savemodal = false;
  selectedValue = null;
  // firstDimensionDis: boolean = false; // 一级维度禁用
  calculateShow: boolean = false; // 计算隐藏
  loading: boolean = false;

  questionTypeList: any[] = [
    // { id: 'SCALE', name: { zh_CN: '量表题' }, value: '0' },
    // { id: 'ESSAY_QUESTION', name: { zh_CN: '开放题' }, value: '0' },
    // { id: 'MULTIPLE_CHOICE_ESSAY_QUESTION', name: { zh_CN: '多选式开放题' }, value: '0' },
    // { id: 'SINGLE', name: { zh_CN: '单选题' }, value: '0' },
  ]; //题型
  questionTypeList2: any[] = [
    { id: "SCALE", name: { zh_CN: "量表" }, value: "0" },
    // { id: 'ESSAY_QUESTION', name: { zh_CN: '开放题' }, value: '0' },
    // { id: 'MULTIPLE_CHOICE_ESSAY_QUESTION', name: { zh_CN: '多选式开放题' }, value: '0' },
    { id: "SINGLE", name: { zh_CN: "单选" }, value: "0" },
  ]; //题型
  labelList: any[] = [
    // 标签列表
    // {id: 'ESI', name: '满意度指数',group:'指数',changename_zh:'',changename_en:''},
    // {id: 'EEI', name: '敬业度指数',group:'指数',changename_zh:'',changename_en:''},
    // {id: 'OCI', name: '组织能力指数',group:'指数',changename_zh:'',changename_en:''},
    // {id: 'OTHER', name: '开放题',group:'其它',changename_zh:'',changename_en:''},
    // {id: 'RELIABILITY', name: '调研可信度',group:'其它',changename_zh:'',changename_en:''},
  ];
  scopeList: any[] = [
    // 计算列表
    { id: "APPROVAL_RATE", name: "赞成百分比" },
    // {id: '', name: '平均分'},
  ];
  optionScopeList: any[] = [
    // 选项
    { id: "APPROVAL", name: "赞成", disabled: false },
    { id: "NEUTRAL", name: "中立", disabled: false },
    { id: "DISAPPROVAL", name: "不赞成", disabled: false },
    { id: "IGNORE", name: "不计分", disabled: false },
  ];
  vivoScopeList: any[] = [
    // 选项
    { id: "APPROVAL", name: "赞成", disabled: false },
    { id: "NEUTRAL", name: "中立", disabled: false },
    { id: "DISAPPROVAL", name: "不赞成", disabled: false },
  ];
  questionData: Question = {
    id: "",
    name: "",
    nameEn: "",
    code: "",
    type: "SCALE",
    label: "ESI", // 标签
    scope: "APPROVAL_RATE", // 计算
    customDimension: null,
    parentCustomDimension: null,
    childCustomDimension: null,
    proportion: 0,
    isRequire: true,
    markcode: null,
  };
  batchquestionData = [
    {
      id: "",
      name: "",
      nameEn: "",
      type: "SCALE",
      code: "",
      IGNOREselect: false,
      multiSelectMin: null,
      multiSelectMax: null,
      optionList: [],
      label: "ESI",
      scope: "APPROVAL_RATE",
      customDimension: null,
      parentCustomDimension: null,
      childCustomDimension: null,
      markcode: null,
      proportion: 0,
      isRequire: true,
      totalScoreStatus: true,
      validateForm: this.fb.group({
        id: [""],
        name: ["", [Validators.required]],
        nameEn: [""],
        code: ["", [Validators.required]],
        label: ["ESI", [Validators.required]], // 标签
        scope: ["APPROVAL_RATE"], // 计算
        type: ["SCALE", [Validators.required]],
        customDimension: [""],
        parentCustomDimension: [""],
        childCustomDimension: [""],
        markcode: [null],
        proportion: [1],
        isRequire: [true, [Validators.required]],
      }),
    },
  ];

  batchshow = false;
  optionId = 0;
  projectType: string;
  codeDis: boolean = false;
  markshow = false;
  constructor(
    private api: ProjectManageService,
    private msg: NzMessageService,
    private fb: FormBuilder,
    private modalRef: NzModalRef,
    private routeInfo: ActivatedRoute,
    private customMsg: MessageService // private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit() {
    this.questionTypeList = this.api.questionTypeList;
    this.projectType = this.routeInfo.snapshot.queryParams.projectType;

    if (!this.id) {
      this.codeDis = false;
    } else {
      if (!this.status || this.status == "ANNOUNCED") {
        this.codeDis = false;
      } else {
        this.codeDis = true;
      }
    }
    if (!this.isedited) {
      this.questionTypeList = this.questionTypeList.filter((res) => {
        return res.id == "ESSAY_QUESTION";
      });
    }

    // this.validateForm =
    if (this.id) this.initData();
    this.getlistfactor();
    this.getlabelList();
  }

  initData() {
    this.api.getQuestion(this.id).subscribe((res) => {
      if (res.result.code === 0) {
        const {
          id: questionId,
          name: questionName,
          type,
          code,
          IGNOREselect,
          customDimension = "",
          label,
          parentCustomDimension = "",
          childCustomDimension = "",
          proportion = 1,
          isRequire,
          markcode = null,
        } = res.data;
        const options = res.data.options.options;
        this.batchquestionData[0] = {
          id: questionId,
          name: questionName.zh_CN,
          nameEn:
            typeof questionName.en_US === "undefined" ||
            isNull(questionName.en_US)
              ? ""
              : questionName.en_US,
          type,
          code,
          label,
          IGNOREselect,
          scope: res.data.options.prismaCalType,
          multiSelectMin: res.data.options.multiSelectMin,
          multiSelectMax: res.data.options.multiSelectMax,
          customDimension,
          parentCustomDimension,
          childCustomDimension,
          markcode,
          proportion,
          isRequire,
          optionList: [],
          totalScoreStatus: res.data.options.totalScoreStatus === "ENABLE",
          validateForm: this.fb.group({
            id: [""],
            nameEn: [""],
            name: ["", [Validators.required]],
            code: ["", [Validators.required]],
            type: ["SCALE", [Validators.required]],
            label:
              this.reportType == "CULTURE_INVESTIGATION_RESEARCH"
                ? [""]
                : ["ESI", [Validators.required]],
            scope: ["APPROVAL_RATE"],
            customDimension: [""],
            parentCustomDimension: [""],
            childCustomDimension: [""],
            markcode: [null],
            proportion: [1, [Validators.required]],
            isRequire: [true, [Validators.required]],
          }),
        };
        this.questionData = {
          id: questionId,
          name: questionName.zh_CN,
          nameEn:
            typeof questionName.en_US === "undefined" ||
            isNull(questionName.en_US)
              ? ""
              : questionName.en_US,
          type,
          code,
          label: "",
          scope: "",
          customDimension,
          parentCustomDimension,
          childCustomDimension,
          markcode,
          proportion,
          isRequire,
        };

        this.batchquestionData[0].validateForm.setValue(this.questionData);
        options.forEach((item) => {
          item.inputshow = item.scope == "IGNORE" ? true : false;
        });
        for (const option of options) {
          const {
            id: optionId,
            name: optionName,
            value,
            scope,
            isScoring,
            stepScore,
            questionId,
            answerType,
            lowestScore,
            highestScore,
            isEnableOpenAnswer,
            isRequireOpenAnswer,
            isValid,
            inputshow,
          } = option;
          this.onAddOption(0, {
            id: optionId,
            name: optionName,
            scope,
            inputshow,
            value,
            isScoring,
            stepScore,
            questionId,
            answerType,
            lowestScore,
            isValid,
            highestScore,
            isEnableOpenAnswer,
            isRequireOpenAnswer,
          });
        }
        let IGNOREid = null;
        console.log(this.batchquestionData);
        this.batchquestionData.forEach((item) => {
          item.optionList.forEach((val) => {
            if (val.scope == "IGNORE") {
              IGNOREid = val.id;
            }
            if (IGNOREid != val.id) {
              val.optionScopeList.forEach((res) => {
                if (res.id == "IGNORE") res.disabled = true;
              });
            }
          });
        });
        this.domapignore();
      }
    });
  }

  // labelChange(e) { // 标签修改
  //   if(e === 'EEI' || e === 'OCI') {
  //     // 一级维度重置  禁用
  //     this.batchquestionData[0].parentCustomDimension = null
  //     this.firstDimensionDis = true
  //   } else {
  //     this.firstDimensionDis = false
  //   }
  // }

  typeChange(e, i) {
    this.questionData.type = e;

    if (e === "ESSAY_QUESTION" || e === "MULTIPLE_CHOICE_ESSAY_QUESTION") {
      // 计算隐藏 calculateShow
      this.batchquestionData[i].scope = null;
      this.batchquestionData[i].label = "OTHER";
      this.calculateShow = false;
    } else {
      this.calculateShow = true;
    }
  }

  ManageOption() {
    this.getlistfactor("add");
  }

  changeNumber() {}

  getSaveSet() {
    let nosave = false;
    let nosaveEn = false;
    let description = false;
    let descriptionEn = false;

    this.factorlist.forEach((res) => {
      if (res.name.zh_CN.length > 8) {
        nosave = true;
      }
      if (res.name.en_US && res.name.en_US.length > 60) {
        nosaveEn = true;
      }
      if (res.description.zh_CN && res.description.zh_CN.length > 50) {
        description = true;
      }

      if (res.description.en_US && res.description.en_US.length > 500) {
        // this.msg.error("英文维度解释不可以超过100个字符");
        this.customMsg.open("error", "英文维度解释不可以超过100个字符");
        descriptionEn = true;
        return;
      }
    });
    if (nosave) {
      // this.msg.error("中文维度名称超过字数限制长度！长度为8");
      this.customMsg.open("error", "中文维度名称超过字数限制长度！长度为8");
      return;
    }

    if (nosaveEn) {
      // this.msg.error("英文维度名称超过字数限制长度！长度为60");
      this.customMsg.open("error", "英文维度名称超过字数限制长度！长度为60");
      return;
    }

    if (description) {
      // this.msg.error("中文维度说明超过字数限制长度！长度为50");
      this.customMsg.open("error", "中文维度说明超过字数限制长度！长度为50");
      return;
    }

    if (descriptionEn) {
      // this.msg.error("英文维度说明超过字数限制长度！长度为100");
      this.customMsg.open("error", "英文维度说明超过字数限制长度！长度为100");
      return;
    }

    let factornames = [];
    factornames = this.factorlist.filter(function(value) {
      return !value.id && value.name.zh_CN === "" && value.name.zh_CN !== 0;
    });

    if (factornames.length > 0) {
      // this.msg.error("请将维度名称填写完整");
      this.customMsg.open("error", "请将维度名称填写完整");
      return;
    }

    // let customTexts = []

    // customTexts = this.factorlist.filter(function (value) {
    //   return value.type ==='TWO_RANK' && value.description.zh_CN === '' && value.description.zh_CN !== 0;
    // });

    // if (customTexts.length > 0) {
    //   this.msg.error('请将维度说明填写完整')
    //   return
    // }

    let parmas = [];
    this.factorlist.forEach((res) => {
      parmas.push({
        name: { zh_CN: res.name.zh_CN, en_US: res.name.en_US },
        description: {
          zh_CN: res.description.zh_CN,
          en_US: res.description.en_US,
        },
        questionnaireId: res.questionnaireId,
        projectId: res.projectId,
        type: res.type,
        proportion: res.proportion,
      });
    });

    this.api.batchSaveSurveyCustomDimension(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.oldDemoshow = 1;
        this.getlistfactor();
      }
    });
  }

  changeStatus(e, i) {
    if (e === false) {
      this.batchquestionData[i].multiSelectMin = 0;
    }
  }

  getlistfactor(type?) {
    this.api
      .listSurveyCustomDimension(this.questionnaireId)
      .subscribe((item) => {
        this.ClassAfactor = [];
        if (item.result.code == 0) {
          this.factorlist = item.data;
          this.factorlist.forEach((res) => {
            if (!res.description) {
              res.description = { zh_CN: "", en_US: "" };
            }
          });
          this.ClassAfactor = item.data.filter((item) => {
            return item.type == "ONE_RANK";
          });
          this.ClassBfactor = item.data.filter((item) => {
            return item.type == "TWO_RANK";
          });

          this.ClassCfactor = item.data.filter((item) => {
            return item.type == "THREE_RANK";
          });
          // this.ClassAfactor.unshift({
          //   name: '无'
          // })
          this.oldDemoshow = 1;
          if (type == "add") {
            this.oldDemoshow = 2;
          }
        }
      });
  }
  getDefault() {
    this.factorlist = [];
    this.oldDemoshow = 1;
  }
  addfactor() {
    this.factorlist.push({
      name: { zh_CN: "", en_US: "" },
      description: { zh_CN: "", en_US: "" },
      questionnaireId: this.questionnaireId,
      projectId: this.projectId,
      type: "ONE_RANK",
      proportion: 1,
    });
  } //添加维度

  onDeletefactorlist(index, id) {
    this.api.listdeletefactor(id).subscribe((res) => {
      if (!res.data) {
        this.factorlist.splice(index, 1);
      } else {
        // this.msg.error("该维度已绑定题目，不可删除！");
        this.customMsg.open("error", "该维度已绑定题目，不可删除！");
      }
    });
    //
  }

  /**
   * 提交校验
   */
  valid() {
    let checked = true;
    this.batchquestionData.forEach((res) => {
      for (const key in res.validateForm.controls) {
        res.validateForm.controls[key].markAsDirty();
        res.validateForm.controls[key].updateValueAndValidity();
      }
      if (!res.validateForm.valid) {
        checked = false;
      }
    });

    return checked;

    // if (this.validateForm.controls.type.value !== 'ESSAY_QUESTION') {
    //   this.msg.error('该题型选项不能为空');
    //   return false;
    // }

    // for (let item of this.optionList) {
    //   if (!item.name.zh_CN) {
    //     this.msg.error('选项不能为空');
    //     return false;
    //   }
    // }
  }

  /**
   * onAddOption添加选项
   */
  onAddOption(
    index?,
    optionData = {
      id: this.batchquestionData[index].optionList.length + 1,
      name: {},
      questionId: "",
      value: 1,
      scope: "",
      isScoring: true,
      lowestScore: null,
      highestScore: null,
      stepScore: 0.1,
      isEnableOpenAnswer: true,
      isRequireOpenAnswer: null,
      isValid: false,
      answerType: "FINAL",
      inputshow: false,
    }
  ) {
    this.batchquestionData[index].optionList.push({
      id: optionData.id || this.batchquestionData[index].optionList.length + 1,
      name: optionData.name,
      value: optionData.value,
      inputshow: optionData.inputshow,
      scope: optionData.scope,
      isScoring: optionData.isScoring,
      questionId: optionData.questionId,
      answerType: optionData.answerType,
      lowestScore: optionData.lowestScore,
      highestScore: optionData.highestScore,
      stepScore: optionData.stepScore,
      totalScoreStatus: true,
      isValid: optionData.isValid,
      isEnableOpenAnswer: optionData.isEnableOpenAnswer,
      isRequireOpenAnswer: optionData.isRequireOpenAnswer,
      optionScopeList:
        this.reportType == "INVESTIGATION_RESEARCH_CUSTOM"
          ? this.optionScopeList
          : this.vivoScopeList,
      // vivoScopeList
    });
    let IGNOREmap = this.batchquestionData[index].optionList.some((item) => {
      return item.scope == "IGNORE";
    });

    this.batchquestionData[index].optionList.forEach((val) => {
      let optionScopeList = JSON.parse(JSON.stringify(val.optionScopeList));
      optionScopeList.forEach((res) => {
        if (IGNOREmap && res.id == "IGNORE") {
          res.disabled = true;
        }
      });
      val.optionScopeList = optionScopeList;
    });
    // this.optionList.push({

    // })
  }

  /**
   * onDeleteOption 删除选项
   * @param id
   */
  onDeleteOption(i, j) {
    this.batchquestionData[i].optionList.splice(j, 1);
    this.domapignore();
  }

  /**
   * 新增、编辑
   */
  getcommit() {
    let messageshow = false;
    let stepScoreshow = false;
    let optionDistribution = false;
    let messagelowScoreshow = false;
    let messagehighScoreshow = false;
    let showoptions = false;
    let nameshow = false;
    let valueshow = false;
    let codeshow = false;
    let validOpt = false; // 是否有有效选项
    let requireOpenAnswerShow = false;

    this.batchquestionData.forEach((item) => {
      if (this.questionData.label === "VALID") {
        validOpt = item.optionList.some((opt) => {
          return opt.isValid;
        });
      }

      if (!item.customDimension && !item.childCustomDimension) {
        codeshow = true;
      }
      if (item.type == "PROPORTION" || item.type == "PROPORTION_MULTIPLE") {
        item.optionList.forEach((res) => {
          if (!res.name.zh_CN || res.name.zh_CN == "") {
            nameshow = true;
          }
          if (res.lowestScore === "") {
            messagelowScoreshow = true;
          }
          if (res.lowestScore === "") {
            messagehighScoreshow = true;
          }
          if (res.lowestScore >= res.highestScore) {
            messageshow = true;
          }
        });

        item.optionList.forEach((res) => {
          if (Number(res.stepScore) < 0.1) {
            stepScoreshow = true;
          }
        });
      } else {
        if (item.type === "SCALE") {
          item.optionList.forEach((res) => {
            if (!res.name.zh_CN || res.name.zh_CN == "") {
              nameshow = true;
            }

            if (res.scope == "") {
              optionDistribution = true;
            }

            if (res.value === "") {
              valueshow = true;
            }
          });
        }

        if (item.type === "MULTIPLE_CHOICE_ESSAY_QUESTION") {
          item.optionList.forEach((res) => {
            if (!res.name.zh_CN || res.name.zh_CN == "") {
              nameshow = true;
            }

            //开放题校验追问是否必填
            if (item.type === "MULTIPLE_CHOICE_ESSAY_QUESTION") {
              if (
                res.isEnableOpenAnswer &&
                (res.isRequireOpenAnswer === "" ||
                  res.isRequireOpenAnswer == null)
              ) {
                requireOpenAnswerShow = true;
              }
            }
          });
        }

        if (
          item.type === "ESSAY_QUESTION" ||
          item.type === "MULTIPLE_CHOICE_ESSAY_QUESTION"
        ) {
          item.proportion = 0;
        }
      }
      if (item.optionList.length == 0 && item.type != "ESSAY_QUESTION") {
        showoptions = true;
      }
    });

    if (messageshow) {
      // this.msg.error("比重题最小值不可以大于或者等于最大值");
      this.customMsg.open("error", "比重题最小值不可以大于或者等于最大值");
      return;
    }
    if (this.questionData.label === "VALID" && !validOpt) {
      // this.msg.error("有效题最少要有一个有效选项");
      this.customMsg.open("error", "有效题最少要有一个有效选项");
      return;
    }
    if (stepScoreshow) {
      // this.msg.error("刻度值最小为0.1");
      this.customMsg.open("error", "刻度值最小为0.1");
      return;
    }
    if (messagehighScoreshow) {
      // this.msg.error("选项分数最大值不能为空值！");
      this.customMsg.open("error", "选项分数最大值不能为空值！");
      return;
    }
    if (messagelowScoreshow) {
      // this.msg.error("选项分数最小值不能为空值！");
      this.customMsg.open("error", "选项分数最小值不能为空值！");
      return;
    }
    if (codeshow) {
      // this.msg.error("二级维度与三级维度最少填写一个！");
      this.customMsg.open("error", "二级维度与三级维度最少填写一个！");
      return;
    }
    if (this.calculateShow === true && this.batchquestionData[0].scope === "") {
      // this.msg.error("请选择计算方式！");
      this.customMsg.open("error", "请选择计算方式！");
      return;
    }
    if (showoptions) {
      // this.msg.error("除开放题外,选项不能为空！");
      this.customMsg.open("error", "除开放题外,选项不能为空！");
      return;
    }
    if (nameshow) {
      // this.msg.error("选项必须要有名称！");
      this.customMsg.open("error", "选项必须要有名称！");
      return;
    }
    if (requireOpenAnswerShow) {
      // this.msg.error("请确认选项开放式回答是否必填！");
      this.customMsg.open("error", "请确认选项开放式回答是否必填！");
      return;
    }
    if (valueshow) {
      // this.msg.error("选项分数不能为空值！");
      this.customMsg.open("error", "选项分数不能为空值！");
      return;
    }
    if (this.questionData.label !== "VALID") {
      if (optionDistribution) {
        // this.msg.error("选项分布不能为空值！");
        this.customMsg.open("error", "选项分布不能为空值！");
        return;
      }
    } else {
    }

    if (!showoptions && !nameshow && !valueshow) {
      if (this.valid()) {
        this.savemodal = true;
        // this.modalRef.destroy()
        const params = {
          questionnaireId: this.questionnaireId,
        };

        if (this.onSubmit(params) != null) {
          let data = this.onSubmit(params);
          this.loading = true;
          if (data.type == "create") {
            this.api.createQuestion(data.params).subscribe((res) => {
              if (res.result.code === 0) {
                this.msg.success("保存成功");
                this.savemodal = false;
                this.modalRef.destroy();
              }
            });
          } else {
            let newparmas = { questionnaireId: "" };
            newparmas = data.params.questions[0];
            newparmas.questionnaireId = data.params.questionnaireId;
            this.api.updateQuestion(newparmas).subscribe((res) => {
              if (res.result.code === 0) {
                this.msg.success("编辑成功");
                this.savemodal = false;
                this.modalRef.destroy();
              }
            });
          }
        }
      }
    }
  }
  getrelease() {
    this.savemodal = false;
    this.modalRef.destroy();
  }

  onSubmit(params) {
    if (this.savemodal) {
      params.questions = [];
      this.batchquestionData.forEach((res) => {
        params.questions.push({
          id: res.id,
          name: { zh_CN: res.name, en_US: res.nameEn },
          type: res.type,
          code: res.code,
          label:
            this.reportType == "CULTURE_INVESTIGATION_RESEARCH"
              ? ""
              : res.label,
          scope: res.scope,
          customDimension: res.customDimension,
          parentCustomDimension: res.parentCustomDimension,
          childCustomDimension: res.childCustomDimension,
          markcode: res.markcode,
          proportion: res.proportion,
          isRequire: res.isRequire,
          optionList: res.optionList,
          totalScoreStatus: res.totalScoreStatus,
        });
      });

      params.questions.forEach((item) => {
        item.options = {
          prismaCalType: "",
          multiSelectMin: null,
          multiSelectMax: null,
          options: [],
        };
        item.options.options = item.optionList;
        item.options.prismaCalType = this.batchquestionData[0].scope;
        item.options.multiSelectMin = this.batchquestionData[0].multiSelectMin;
        item.options.multiSelectMax = this.batchquestionData[0].multiSelectMax;
        if (item.type == "PROPORTION_MULTIPLE") {
          item.options.totalScoreStatus = item.totalScoreStatus
            ? "ENABLE"
            : "DISABLE";
        }
      });
      let data = {
        params: params,
        type: "",
      };
      if (this.valid()) {
        if (this.id) {
          data.type = "update";
          return data;
        } else {
          data.type = "create";
          return data;
        }
      } else {
        return null;
      }
    }
  }
  addNewlist() {
    this.batchquestionData.push({
      id: "",
      name: "",
      nameEn: "",
      type: "SCALE",
      label: "ESI",
      code: "",
      multiSelectMin: null,
      multiSelectMax: null,
      scope: "APPROVAL_RATE",
      optionList: [],
      IGNOREselect: false,
      customDimension: null,
      parentCustomDimension: null,
      childCustomDimension: null,
      markcode: null,
      proportion: 0,
      isRequire: true,
      totalScoreStatus: true,
      validateForm: this.fb.group({
        id: [""],
        name: ["", [Validators.required]],
        code: ["", [Validators.required]],
        type: ["SCALE", [Validators.required]],
        label:
          this.reportType == "CULTURE_INVESTIGATION_RESEARCH"
            ? [""]
            : ["ESI", [Validators.required]],
        scope: ["APPROVAL_RATE", [Validators.required]],
        customDimension: [""],
        parentCustomDimension: [""],
        childCustomDimension: [""],
        markcode: [null],
        proportion: [1, [Validators.required]],
        isRequire: [true, [Validators.required]],
      }),
    });
    // setTimeout(()=>{

    // },1000)
  }
  deletelist(i) {
    if (this.batchquestionData.length > 1) {
      this.batchquestionData.splice(i, 1);
    }
  }
  ManageLabel() {
    this.markshow = true;
  }
  markClear() {
    this.api.getIrestorePrismaLabel(this.projectId).subscribe((item) => {
      if (item.result.code == 0) {
        this.getlabelList();
      }
    });
  }
  markCommit() {
    let parmas = this.labelList;
    this.api.getIupdatePrismaLabel(parmas).subscribe((item) => {
      if (item.result.code == 0) {
        this.markshow = false;
        this.getlabelList();
      }
    });
  }

  getlabelList() {
    this.api.getIlistPrismaLabel(this.projectId).subscribe((item) => {
      if (item.result.code == 0) {
        this.labelList = item.data;
      }
    });
  }

  // 题本修订
  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;
  @ViewChild("nzTreeComponentRenkou", { static: false })
  nzTreeComponentRenkou: NzTreeComponent;
  @ViewChild("nzTreeComponentQues", { static: false })
  nzTreeComponentQues: NzTreeComponent;

  revisionConditionObj = [
    {
      value: 1,
      label: "组织架构",
    },
    {
      value: 2,
      label: "人口标签",
    },
    {
      value: 3,
      label: "对象",
    },
  ];

  selectConditionVal: number = 1;
  orgList: any[] = [];
  demographics: any[] = [];
  revisionTypes: any[] = [];

  expandedNodes: any[] = [];
  expandedRenNodes: any[] = [];
  expandedRevisionTypes: any[] = [];

  selectedNodes: any[] = [];
  selectedRenkouNodes: any[] = [];
  selectedQuesNodes: any[] = [];
  // selectedKeys : any[] = [];
  // selectedRenkouKeys : any[] = [];
  // selectedQuesKeys : any[] = [];

  resultList: any[] = [];

  clearTree(node) {
    node.isHalfChecked = false;
    if (node.isLeaf) {
      if (node.isChecked) {
        node.isChecked = false;
      }
    } else {
      if (node.isChecked) {
        node.isChecked = false;
      }
      node.children.forEach((element) => {
        this.clearTree(element);
      });
    }
  }

  toRevision() {
    // 切换至题本修订 获取修订数据
    this.api.getCreateRevisionInfo(this.id).subscribe((res) => {
      if (res.result.code === 0) {
        this.oldDemoshow = 3;
        if (
          res.data.organizationTrees &&
          res.data.organizationTrees.length > 0
        ) {
          this.expandedNodes.push(res.data.organizationTrees[0].key);
        }
        if (res.data.demographics && res.data.demographics.length > 0) {
          res.data.demographics.map((item) => {
            this.expandedRenNodes.push(item.id);
          });
        }

        if (res.data.revisionTypes && res.data.revisionTypes.length > 0) {
          res.data.revisionTypes.map((item) => {
            this.expandedRevisionTypes.push(item.id);
          });
        }

        this.orgList = res.data.organizationTrees;
        // 过滤虚拟组织
        this.orgList.forEach((iie) => {
          iie.disabled = true; // 根组织不可选
          this.nodeDisable(iie);
        });

        this.demographics = res.data.demographics;
        this.formatter(this.demographics);
        this.revisionTypes = res.data.revisionTypes;
        this.formatter(this.revisionTypes);
      }
    });
    this.getDateRes({ questionId: this.id });
  }

  // 过滤虚拟组织
  nodeDisable(node) {
    if (node.isVirtual) {
      node.disabled = true;
    }
    if (node.children.length !== 0) {
      node.children.forEach((element) => {
        this.nodeDisable(element);
      });
    }
  }

  getDateRes(params) {
    this.api.getOrCreateRevisionResult(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.resultList = res.data;
      }
    });
  }

  createRevision() {
    // 生成修订数据

    let demographicContents = this.getfactorListParam(); // 人口标签参数

    if (demographicContents.length === 0 && this.selectedNodes.length === 0) {
      // this.msg.error("请勾选人口标签或组织架构");
      this.customMsg.open("error", "请勾选人口标签或组织架构");
      return;
    }
    if (this.selectedQuesNodes.length === 0) {
      // this.msg.error("对象未勾选");
      this.customMsg.open("error", "对象未勾选");
      return;
    }
    let params = {
      demographicContents: demographicContents,
      organizationIds: this.selectedNodes,
      questionId: this.id,
      revisionTypes: this.selectedQuesNodes,
    };

    this.getDateRes(params);
  }

  getSaveRevision() {
    // 保存修订数据
    const param = {
      questionId: this.id,
      list: this.resultList,
    };
    if (param.list.length === 0) {
      // this.msg.error("请先创建内容");
      this.customMsg.open("error", "请先创建内容");
      return;
    }
    this.api.saveRevisionResult(param).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("保存成功");
        this.oldDemoshow = 1;
        this.selectConditionVal = 1;
        this.orgList = [];
        this.demographics = [];
        this.revisionTypes = [];

        this.expandedNodes = [];
        this.expandedRenNodes = [];
        this.expandedRevisionTypes = [];

        this.selectedNodes = [];
        this.selectedRenkouNodes = [];
        this.selectedQuesNodes = [];

        this.resultList = [];
      }
    });
  }

  deleteOne(resInd, queInd) {
    if (this.resultList[resInd].revisionInfoVos.length === 1) {
      this.resultList.splice(resInd, 1);
    } else {
      this.resultList[resInd].revisionInfoVos.splice(queInd, 1);
    }
  }

  formatter(data) {
    data.forEach((item) => {
      item.title = item.name.zh_CN;
      item.key = item.id;
      if (item.children.length !== 0) {
        item.isLeaf = false;
        this.formatter(item.children);
      } else {
        item.isLeaf = true;
      }
    });
  }

  clear() {
    let orgTree = this.nzTreeComponent.getTreeNodes();
    let demoTree = this.nzTreeComponentRenkou.getTreeNodes();
    let queTree = this.nzTreeComponentQues.getTreeNodes();
    this.selectedNodes = [];
    orgTree.forEach((item) => {
      this.clearTree(item);
    });
    demoTree.forEach((item) => {
      this.clearTree(item);
    });
    queTree.forEach((item) => {
      this.clearTree(item);
    });
    this.api.clearRevisionResult(this.id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("数据已清空");
        this.getDateRes({ questionId: this.id });
      }
    });
  }

  nzEvent(event: NzFormatEmitEvent): void {
    if (event.node.isDisabled) {
      return;
    }
    // this.chooseType = null
    // select children
    let currentNode: any = event.node.origin;
    let isSelected: boolean = currentNode.checked;
    let map = {};
    this.selectChild(currentNode, isSelected, map);

    let allNodes: NzTreeNode[] = this.nzTreeComponent.getTreeNodes();
    allNodes.forEach((n: NzTreeNode) => {
      this.setSelectState(n, isSelected, map);
    });

    // update displaying selected nodes
    let nodeList: NzTreeNode[] = this.nzTreeComponent.getCheckedNodeList();
    let tmp: any[] = [];
    for (let index = 0; index < nodeList.length; index++) {
      const element = nodeList[index].origin;
      tmp.push(element.key);
    }
    this.selectedNodes = tmp;

    // this.getShowList();
  }

  nzEventRen(event: NzFormatEmitEvent, data): void {
    // this.chooseType = null
    // update displaying selected nodes
    const nodeList: NzTreeNode[] = this.nzTreeComponentRenkou.getTreeNodes();
    let all = true; // 是否全选
    let tmp: any[] = [];
    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        node1.children.map((node2) => {
          if (node2.children.length !== 0) {
            node2.children.map((node3) => {
              if (node3.isChecked) {
                tmp.push({ id: node3.key, name: node3.title });
              } else {
                all = false;
              }
            });
          } else {
            if (node2.isChecked) {
              tmp.push({ id: node2.key, name: node2.title });
            } else {
              all = false;
            }
          }
        });
      }
    });

    this.selectedRenkouNodes = tmp;
  }

  nzEventQues(event: NzFormatEmitEvent): void {
    // this.chooseType = null
    // update displaying selected nodes
    let tmp: any[] = [];
    const nodeList: NzTreeNode[] = this.nzTreeComponentQues.getTreeNodes();

    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        node1.children.map((node2) => {
          if (node2.isChecked) {
            tmp.push(node2.origin);
          }
        });
      } else {
        if (node1.isChecked) {
          tmp.push(node1.origin);
        }
      }
    });

    this.selectedQuesNodes = tmp;
  }

  // get should select map
  selectChild(node: any, isSelected: boolean, map: any) {
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        map[child.key] = 1;
        this.selectChild(child, isSelected, map);
      }
    }
  }

  // set select node by map
  setSelectState(node: NzTreeNode, isSelected: boolean, map: any) {
    if (map[node.key] === 1) {
      if (node.origin.isVirtual) {
        node.isDisabled = true;
        node.isChecked = false;
      } else {
        node.isDisabled = isSelected;
        // node.setSelected(isSelected);
        node.isChecked = false;
      }
    }
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        this.setSelectState(child, isSelected, map);
      }
    }
  }

  getfactorListParam() {
    let param = {};
    let factorList = [];
    const demographics = this.nzTreeComponentRenkou.getTreeNodes();

    demographics.map((children1) => {
      let demographicRootId;
      let demographicIds = [];
      if (children1.children.length !== 0) {
        children1.children.map((children2) => {
          if (children2.children.length !== 0) {
            children2.children.map((children3) => {
              if (
                _.find(children3, function() {
                  return children3.isChecked;
                })
              ) {
                demographicRootId = children1.key;
                demographicIds.push(children3.key);
              }
            });
          } else {
            if (
              _.find(children2, function() {
                return children2.isChecked;
              })
            ) {
              demographicRootId = children1.key;
              demographicIds.push(children2.key);
            }
          }
        });
      }
      if (demographicRootId) {
        factorList.push({
          demographicRootId: demographicRootId,
          demographicIds: demographicIds,
        });
      }
    });

    return factorList;
  }
  // 有效性题本
  typeLabelChange(e) {
    this.questionData.label = e;
    if (e === "VALID") {
      this.questionData.type = "SINGLE";
    }
  }

  ngModelChangeitem(itemdata, questionData) {
    if (itemdata.scope == "IGNORE") {
      itemdata.value = null;
      itemdata.inputshow = true;
      questionData.IGNOREselect = true;
      questionData.optionList.forEach((val) => {
        let optionScopeList = JSON.parse(JSON.stringify(val.optionScopeList));
        optionScopeList.forEach((res) => {
          if (res.id == "IGNORE") {
            res.disabled = true;
          }
        });
        val.optionScopeList = optionScopeList;
      });
    } else {
      itemdata.value = 1;
      itemdata.inputshow = false;
    }
    this.domapignore();
  }

  domapignore() {
    this.batchquestionData.forEach((item) => {
      item.IGNOREselect = item.optionList.some((res) => {
        return res.scope == "IGNORE";
      });
      if (!item.IGNOREselect) {
        item.optionList.forEach((val) => {
          let optionScopeList = JSON.parse(JSON.stringify(val.optionScopeList));
          optionScopeList.forEach((res) => {
            if (res.id == "IGNORE") {
              res.disabled = false;
            }
          });
          val.optionScopeList = optionScopeList;
        });
      }
    });
  }
}
