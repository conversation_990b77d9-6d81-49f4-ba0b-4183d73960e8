import { Injectable } from "@angular/core";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";

@Injectable({
  providedIn: "root",
})
export class PermissionService {
  permission = sessionStorage.getItem("permission");
  constructor(
    public knxFunctionPermissionService: KnxFunctionPermissionService
  ) {}
  isPermissionOrSag(sag) {
    return (
      this.permission === "true" || this.knxFunctionPermissionService.has(sag)
    );
  }
  isPermissionAndSag(sag) {
    return (
      this.permission === "true" && this.knxFunctionPermissionService.has(sag)
    );
  }

  isPermission() {
    return this.permission === "true";
  }

  isSag(sag) {
    return this.knxFunctionPermissionService.has(sag);
  }
}
