<div class="title">
  <span class="span1">新增题目</span>
  <span class="span2">已选：{{ dimensionCount() }}个维度</span>
</div>

<div class="body">
  <!-- {{dimensionList | json}} -->
  <div class="left scroll">
    <div
      *ngFor="let dim of dimensionList"
      class="dim"
      [ngClass]="{ dimsel: dim.isSelect, dimDefault: !dim.isSelect }"
      (click)="selectDim(dim)"
    >
      <span [ngClass]="{ dimsel: dim.isSelect }">
        {{ dim.dimensionName.zh_CN + " (" + dim.surveyQuestions.length + ")" }}
      </span>
    </div>
  </div>

  <div class="right scroll">
    <!-- <div>
            {{currentDim | json}}
        </div> -->
    <div class="right_top">
      <!-- <label nz-checkbox [(ngModel)]="allChecked" (ngModelChange)="selectAll()">全选</label> -->
      <button nz-button nzType="text" nzSize="small" (click)="selectAll()">
        &nbsp;全选&nbsp;
      </button>

      <app-btn
        *ngIf="currentDim && currentDim.isOpen"
        [text]="'新增'"
        [image]="'./assets/images/org/add.png'"
        [hoverColor]="'#409EFF'"
        [hoverImage]="'./assets/images/org/add_hover.png'"
        (btnclick)="addOpenSubject()"
      >
      </app-btn>

      <div class="searchDiv">
        <nz-input-group [nzPrefix]="IconSearch">
          <input
            class="input-search"
            type="text"
            nz-input
            placeholder="请输入关键词"
            [(ngModel)]="keyWord"
            (keydown.enter)="search()"
          />
        </nz-input-group>
        <ng-template #IconSearch>
          <i nz-icon nzType="search" class="icon-search" (click)="search()"></i>
        </ng-template>
      </div>
    </div>

    <div class="right_bottom ">
      <div class="topic" *ngFor="let topic of topicList">
        <div class="topic_index">
          <label nz-checkbox [(ngModel)]="topic.isSelect"></label>
        </div>
        <div class="topic_title">
          <!-- {{topic | json}} -->
          <div class="preview" [innerHTML]="topic.name.zh_CN | html"></div>
          <div style="height:5px;"></div>
          <div class="preview" [innerHTML]="topic.name.en_US | html"></div>
        </div>
      </div>
    </div>
  </div>
</div>
