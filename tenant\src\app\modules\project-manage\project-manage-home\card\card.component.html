<div class="card-body" *ngIf="type === 'card'">
    <div *ngIf="data.surveyType == 'ASSESSMENT'" class="border-top"></div>
    <div *ngIf="data.surveyType == 'EMPLOYEE_ENGAGEMENT'" class="border-top border-top-research"></div>
    <div class="content">
        <header>
            <h1 (click)="goDetail()" [title]="data.name">{{data.name}}</h1>
            <div [class]="'process process-'+statusClass">{{statusName}}</div>
        </header>
        <div class="project-id">ID：{{data.code}}</div>
        <ul class="statistics">
            <li>
                <p>{{data.completedCount}}</p>
                <span>已完成</span>
            </li>
            <li>
                <p>{{data.onGoingCount}}</p>
                <span class="dots-blue">进行中</span>
            </li>
            <li>
                <p>{{data.inCompletedCount}}</p>
                <span class="dots-red">未开始</span>
            </li>
        </ul>
        <div class="detail-box">
            <div *ngIf="data.surveyType == 'ASSESSMENT'" class="type-evaluation">测评</div>
            <div *ngIf="data.surveyType == 'EMPLOYEE_ENGAGEMENT'" class="type-evaluation type-research">调研</div>
            <p class="active" nz-tooltip [nzTooltipTitle]="titleTemplate1">活动工具：
                <ng-container *ngFor="let item of data.questionnaires;let i = index">
                    {{item.name.zh_CN}}
                    <ng-container *ngIf="i!=data.questionnaires.length-1">、</ng-container>
                </ng-container>
            </p>
            <ng-template #titleTemplate1>
                <ng-container *ngFor="let item of data.questionnaires;let i = index">
                    {{item.name.zh_CN}}
                    <ng-container *ngIf="i!=data.questionnaires.length-1">、</ng-container>
                </ng-container>
            </ng-template>
            <p>活动周期：{{data.startTime | date: 'yyyy-MM-dd HH:mm'}}至{{data.endTime | date: 'yyyy-MM-dd HH:mm'}}</p>
            <div *ngIf="data.isClosingSoon" class="tips-box">7天内到期活动</div>
        </div>
    </div>

    <!-- 底部操作栏 --> 
    <footer>
        <div class="action-bar">
            <!-- 邀请 -->
            <img (click)="invite()" *ngIf="!is360Invite && isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:INVITE')" nz-tooltip nzTooltipPlacement="bottom" nzTooltipTitle="邀请" class="icon-img" src="assets/images/event-management/home/<USER>"
                alt="">
            <img *ngIf="is360Invite && isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:INVITE')" nz-dropdown [nzDropdownMenu]="menuInvite" class="icon-img" src="assets/images/event-management/home/<USER>" alt="">
            <nz-dropdown-menu #menuInvite="nzDropdownMenu">
                <ul nz-menu nzSelectable class="down-menu">
                    <li nz-menu-item (click)="inviteEval()">邀请被评估人完善评价关系<span class="iconfont icon-icon_arrow_right"></span></li>
                    <li nz-menu-item (click)="invite()">邀请所有人进行评价作答<span class="iconfont icon-icon_arrow_right"></span></li>
                </ul>
            </nz-dropdown-menu>
            <!-- 编辑 -->
            <img (click)="edit()" nz-tooltip nzTooltipPlacement="bottom" nzTooltipTitle="编辑" class="icon-img" src="assets/images/event-management/home/<USER>" alt="" *ngIf="isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:UPDATE')">
            <!-- 复制 -->
            <img (click)="copyActive()" nz-tooltip nzTooltipPlacement="bottom" nzTooltipTitle="复制" class="icon-img" src="assets/images/event-management/home/<USER>" alt="" *ngIf="isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:COPY_PROJECT')">
            <!-- 更多 -->   
            <app-project [isSystemInterface]="isSystemInterface" [projectModel]="data" (getProjectList)="getProjectList()" *ngIf="isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:HIDE')"></app-project>
        </div>
        <p>{{ data.creator}}</p>
    </footer>
</div>


<div class="standard-card-box" *ngIf="type === 'list'">
    <div *ngIf="data.surveyType == 'ASSESSMENT'" class="line-color"></div>
    <div *ngIf="data.surveyType == 'EMPLOYEE_ENGAGEMENT'" class="line-color line-color-research"></div>
    <div class="detail-box">
        <header>
            <div class="title">
                <div class="title-text">

                    <h1 (click)="goDetail()" [title]="data.name">{{data.name}}</h1>
                    <div [class]="'process process-'+statusClass">{{statusName}}</div>
                </div>
                <div class="title-id">ID：{{data.code}}</div>
            </div>

            <div class="tool-box">
                <p>创建人：{{ data.creator }}</p>
                <div class="action-bar">
                    <!-- 邀请 -->
                    <img (click)="invite()" *ngIf="!is360Invite && isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:INVITE')" nz-tooltip nzTooltipPlacement="bottom" nzTooltipTitle="邀请" class="icon-img" src="assets/images/event-management/home/<USER>"
                        alt="">
                    <img (click)="invite()" *ngIf="is360Invite && isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:INVITE')" nz-dropdown [nzDropdownMenu]="menuInvite" class="icon-img" src="assets/images/event-management/home/<USER>" alt="">
                    <nz-dropdown-menu #menuInvite="nzDropdownMenu">
                        <ul nz-menu nzSelectable class="down-menu">
                            <li nz-menu-item (click)="inviteEval()">邀请被评估人完善评价关系<span class="iconfont icon-icon_arrow_right"></span>
                            </li>
                            <li nz-menu-item (click)="invite()">邀请所有人进行评价作答<span class="iconfont icon-icon_arrow_right"></span></li>
                        </ul>
                    </nz-dropdown-menu>
                    <!-- 编辑 -->
                    <img (click)="edit()" nz-tooltip nzTooltipPlacement="bottom" nzTooltipTitle="编辑" class="icon-img" src="assets/images/event-management/home/<USER>" alt="" *ngIf="isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:UPDATE')">
                    <!-- 复制 -->
                    <img (click)="copyActive()" nz-tooltip nzTooltipPlacement="bottom" nzTooltipTitle="复制" class="icon-img" src="assets/images/event-management/home/<USER>" alt="" *ngIf="isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:COPY_PROJECT')">
                    <!-- 更多 -->
                    <app-project [projectModel]="data" (getProjectList)="getProjectList()" *ngIf="isPermissionOrSag('SAG:TENANT:PROJECT_MGT:LIST:HIDE')"></app-project>
                </div>
            </div>
        </header>
        <footer>
            <div class="info">
                <ul class="statistics">
                    <li>

                        <p><span>已完成</span>{{data.completedCount}}</p>
                    </li>
                    <li>

                        <p><span class="dots-blue">进行中</span>{{data.onGoingCount}}</p>
                    </li>
                    <li>

                        <p><span class="dots-red">未开始</span>{{data.inCompletedCount}}</p>
                    </li>
                </ul>
                <div class="activity-tool">
                    <p nz-tooltip [nzTooltipTitle]="titleTemplate">活动工具：
                        <ng-container *ngFor="let item of data.questionnaires;let i = index">
                            {{item.name.zh_CN}}
                            <ng-container *ngIf="i!=data.questionnaires.length-1">、</ng-container>
                        </ng-container>
                    </p>
                    <div *ngIf="data.surveyType == 'ASSESSMENT'" class="type-evaluation">测评</div>
                    <div *ngIf="data.surveyType == 'EMPLOYEE_ENGAGEMENT'" class="type-evaluation type-research">调研</div>
                    <ng-template #titleTemplate>
                        <ng-container *ngFor="let item of data.questionnaires;let i = index">
                            {{item.name.zh_CN}}
                            <ng-container *ngIf="i!=data.questionnaires.length-1">、</ng-container>
                        </ng-container>

                    </ng-template>
                </div>

            </div>
            <div class="activity-cycle">
                <p>活动周期：{{data.startTime | date: 'yyyy-MM-dd HH:mm'}} 至 {{data.endTime | date: 'yyyy-MM-dd HH:mm'}}</p>
                <div *ngIf="data.isClosingSoon" class="tips-box">7天内到期活动</div>
            </div>
        </footer>
    </div>
</div>