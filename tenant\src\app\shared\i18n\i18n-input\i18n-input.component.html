<!--
    *
    *@author: <PERSON>
    *@Date: 2023/09/18
    *@content: i18n input组件
    *
-->
<div class="i18nInputs">
  <ng-container *ngIf="i18n.length > 0">
    <ng-container *ngIf="i18n.length === 1">
      <input
        type="text"
        nz-input
        [placeholder]="placeholder"
        [(ngModel)]="i18n[0].value"
        (change)="changeVal()"
      />
    </ng-container>
    <ng-container *ngIf="i18n.length > 1">
      <nz-input-group [nzSuffix]="suffixTemplate">
        <input
          type="text"
          nz-input
          [placeholder]="placeholder"
          [(ngModel)]="i18n[0].value"
          (change)="changeVal()"
        />
      </nz-input-group>
      <ng-template #suffixTemplate>
        <i
          class="iconfont icon-duoyuyan i18n"
          nz-popover
          [nzContent]="contentTemplate"
          nzPlacement="bottomRight"
          nzTrigger="click"
          [(nzVisible)]="visible"
          title="多语言"
          (click)="handI18n()"
        ></i>
        <ng-template #contentTemplate>
          <div style="width: 370px;">
            <div *ngFor="let item of i18n" style="margin-bottom: 6px">
              <ng-container>
                <p style="margin-bottom: 6px ">{{ item.label }}</p>
                <input
                  type="text"
                  nz-input
                  [(ngModel)]="item.value"
                  [placeholder]="placeholder"
                />
              </ng-container>
            </div>
          </div>
          <div style="display: flex;justify-content: flex-end;">
            <button nz-button nzType="primary" (click)="handOk()">确认</button>
          </div>
        </ng-template>
      </ng-template>
    </ng-container>
  </ng-container>
</div>
