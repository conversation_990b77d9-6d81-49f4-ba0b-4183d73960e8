<!--
    *@author: <PERSON>
    *@Date: 2023/09/18
    *@content: 带icon的btn，支持nz7.5.x版本以及iconfont
-->
<a
  class="btn-font"
  [ngStyle]="{ 'margin-left': !!text ? '50px' : '10px' }"
  (click)="clickLink()"
  (mouseenter)="enter()"
  (mouseleave)="leave()"
>
  <!-- 使用nz-icon -->
  <ng-container *ngIf="type === 'default'">
    <ng-container *ngIf="!filled">
      <i
        nz-icon
        [nzType]="iconType"
        [nzTheme]="theme"
        [ngStyle]="{ color: isHover ? hoverColor : color }"
      ></i>
    </ng-container>
    <ng-container *ngIf="filled">
      <i
        nz-icon
        [nzType]="iconType"
        class="filled"
        [nzTheme]="theme"
        [ngStyle]="{ 'background-color': isHover ? hoverColor : color }"
      ></i>
    </ng-container>
  </ng-container>

  <!-- 使用iconfont -->
  <ng-container *ngIf="type === 'iconfont'">
    <ng-container *ngIf="!filled">
      <i
        nz-icon
        [nzIconfont]="iconFont"
        [ngStyle]="{ color: isHover ? hoverColor : color }"
      ></i>
    </ng-container>
    <ng-container *ngIf="filled">
      <i
        nz-icon
        [nzIconfont]="iconFont"
        class="filled"
        [ngStyle]="{ 'background-color': isHover ? hoverColor : color }"
      ></i>
    </ng-container>
  </ng-container>
  <span [ngStyle]="{ color: isHover ? hoverColor : color }">{{ text }}</span>
</a>
