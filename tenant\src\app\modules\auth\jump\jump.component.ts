import { Component, OnInit, Inject } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { LoginService } from "../../login/login.service";
import { NzMessageService } from "ng-zorro-antd";
import { cookie } from "@knx/shared";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-jump",
  templateUrl: "./jump.component.html",
  styleUrls: ["./jump.component.less"],
})
export class JumpComponent implements OnInit {
  constructor(
    private routeInfo: ActivatedRoute,
    private router: Router,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private login: LoginService,
    private msg: NzMessageService,
    private customMsg: MessageService
  ) {}

  isSuccess = false;
  isError = false;
  errorMsg = "";
  percent = 0;
  url = "/home";

  ngOnInit() {
    const local_token: string = localStorage.getItem("token");
    const cookie_token: string = cookie.read("token");
    // const testStr = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MzE2NTg3NTksInVzZXJJZCI6IjE2NjYyNjA2NDE4NzYyNTA2MjUifQ.Jd6A9cOWMEFUXd2TEkzF1mgIWNyYU1bBdng8MKSRCeo";
    // const testStr = "";
    // let token = cookie_token || local_token || testStr;

    let token = cookie_token || local_token;
    console.log("local.token：", cookie.read("token"));
    console.log("cookie.token：", cookie.read("token"));
    if (!token) {
      this.customMsg.open("error", "用户认证失败");
      this.errorMsg = "用户认证失败，未携带token";
      this.isError = true;
      return; // 提前结束
    }

    while (token.indexOf('"') !== -1) {
      token = token.replace('"', "");
    }

    let tokenObj: any = {};
    tokenObj.token = token;
    tokenObj.time = new Date();
    // tokenObj.refreshToken = token;

    this.tokenService.set(tokenObj);

    /**
     * 获取用户信息
     */
    // this.login.login().subscribe(
    //   (res) => {
    //     if (res.result.code === 0) {
    //       this.login.emitUser(res);
    //       setTimeout(() => {
    //         this.isSuccess = true;
    //       }, 1000);
    //     }
    //   },
    // (err) => {
    //   this.customMsg.open("error", "用户认证失败");
    //   this.errorMsg = '用户认证失败';
    //   this.isError = true;
    // }
    // );
    // 租户中台权限
    this.getPermisionCode();
    // // sso登录
    // this.getSSoLogin();
  }
  async getPermisionCode() {
    let params = { productCode: "SAG" };
    const defaultPermissions = [];
    try {
      const res = await this.login
        .getPermisionCode(params.productCode)
        .toPromise();
      if (res.result.code === 0) {
        localStorage.setItem(
          params.productCode + "_permissioncode",
          JSON.stringify(
            !res.data || res.data.length === 0
              ? defaultPermissions
              : res.data
          )
        );
        // sso登录
        this.getSSoLogin();
      }
    } catch (err) {
      console.log("err", err);
      this.showError(err.error.result.message);
    }
  }

  // sso登录
  async getSSoLogin(){
    try {
      const res = await this.login.login().toPromise();
      if (res.result.code === 0) {
        setTimeout(() => {
          this.isSuccess = true;
          // 此处需要区分,进入在线看板还是租户端（待处理）
          setInterval(() => {
            this.percent = this.percent + 5;
            if (this.percent > 110) {
              this.router.navigateByUrl(this.url + "?_allow_anonymous=true");
            }
          }, 20);
        }, 500);
      }
    } catch (err) {
      this.showError(err.error.result.message);
    }
  }
  // 授权错误提示
  showError(msg) {
    this.customMsg.open("error", msg);
    this.errorMsg = msg;
    this.isError = true;
  }
  // 跳转至登录
  gotoLogin() {
    this.router.navigateByUrl("/");
    // this.router.navigateByUrl("/user/login");
  }
}
