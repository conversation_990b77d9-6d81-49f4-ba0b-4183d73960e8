nz-input-group {
  padding: 10px 0;
}

.file-name {
  // margin: 0 0 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 6px;
}

.title {
  margin-top: 15px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-size: 24px;
    font-family: PingFangSC-Light, PingFang SC;
    font-weight: 300;
    color: #17314c;
    line-height: 33px;
  }

  .lan {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #17314c;
    line-height: 20px;
    z-index: 10;
  }
}

.check-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.container {
  height: calc(100vh - 123px);
  display: flex;
  justify-content: space-between;

  .left {
    border-right: solid 1px #e6e6e6;
    width: 360px;
    padding: 0 16px 16px 16px;
    .setting {
      margin: 0;
      height: calc(100vh - 184px);
      ::ng-deep {
        .ant-tabs-tab {
          padding-left: 8px;
          padding-right: 8px;
          margin: 0 48px 0 0;
          &:nth-last-child(1) {
            margin-right: 0 !important;
          }
        }
      }
      .tab-content {
        border-radius: 8px;
        border: 1px solid #ececec;
        .tab-search1 {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 16px;
          border-bottom: solid 1px #ececec;
        }
        .tab-search2 {
          padding: 0 16px 10px 16px;
          border-bottom: solid 1px #ececec;
          .bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
        }
      }
    }
    .tree1 {
      width: 100%;
      height: calc(100vh - 305px);
      display: flex;
      padding: 8px;
    }
    .tree2 {
      width: 100%;
      height: calc(100vh - 345px);
      display: flex;
      padding: 8px;
    }
    .tree3 {
      width: 100%;
      height: calc(100vh - 250px);
      border: 1px solid #ececec;
      border-radius: 8px;
      padding: 8px;
      ::ng-deep {
        .ant-tree-treenode-content {
          background-color: gold;
          // width: 100%;
        }
      }
    }
    .list {
      width: 100%;
      height: 325px;
      max-height: 325px;
    }
  }

  .right {
    width: 360px;
    height: calc(100vh - 123px);
    // padding: 10px;
    display: flex;
    align-items: flex-start;
    justify-content: stretch;

    .type {
      width: 99%;
      padding: 10px;
      background-color: white;
      border-radius: 8px;

      &:not(:last-of-type) {
        margin-bottom: 15px;
      }

      span {
        margin-bottom: 10px;
        font-size: 14px;
        display: block;
        font-weight: 500;
      }

      .items {
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;

        .label {
          border-radius: 20px;
          padding: 2px 12px;
          margin-bottom: 10px;
          margin-right: 8px;
          white-space: pre-wrap;
          color: #06f;
          background: rgba(0, 102, 255, 0.1);
        }
      }
    }
  }
}

.orderedDiv {
  margin-top: 16px;
  padding: 8px;
  border: dashed 1px #e6e6e6;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  &:nth-child(1) {
    margin-top: 10px;
  }
}

.treeScroll {
  overflow-y: auto;
  //   overflow-x: auto;
}

:host .container ::ng-deep .ant-checkbox-group-item {
  margin-bottom: 16px;
  display: block;
}

:host .container ::ng-deep {
  nz-tag {
    margin-top: 10px;
    white-space: normal;
  }

  .ant-collapse-content-box {
    padding-top: 6px;
    background: #fbfbfb;
  }

  .ant-collapse {
    min-width: 285px;
    border-radius: 0;
    border-color: #ececec;
    border-left: none;
    border-right: none;
  }

  .ant-checkbox-wrapper {
    display: block;
    margin-left: 0;
    margin-top: 10px;
  }
}

:host .right ::ng-deep {
  .ant-collapse-header {
    padding-left: 16px;
    background: #fff;
  }
}

:host .list ::ng-deep {
  .ant-collapse-header {
    padding-top: 4px;
    padding-bottom: 4px;
  }
}

::ng-deep {
  .ant-modal-close-x {
    width: 36px !important;
    height: 36px !important ;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  box-shadow: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}
::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}
.empty-box {
  height: calc(100vh - 123px);
  overflow-y: auto;
}
.collapse-box {
  width: 100%;
}
.tag-default {
  background: #ececec;
  border-radius: 6px;
  color: #262626;
  border-color: #ececec;
  &:hover{
    background: #EBF5FF;
    border-color: #EBF5FF;
    ::ng-deep {
      .anticon-close {
        color: #409EFF;
      }
    }
  }
}
.tag-red {
  border-radius: 6px;
  color: #ff4f40;
  border-color: #ff4f40;
  background-color: #fff;
}
.is-show-rule-tip {
  font-weight: 400;
  font-size: 12px;
  color: #bfbfbf;
  line-height: 17px;
  text-align: left;
  font-style: normal;
}
