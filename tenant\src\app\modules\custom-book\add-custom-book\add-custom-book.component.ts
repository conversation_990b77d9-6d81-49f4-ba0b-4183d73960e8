import {
  Component,
  OnInit,
  Input,
  ViewChild,
  ChangeDetectorRef,
} from "@angular/core";
import { ProjectManageService } from "../../service/project-manage.service";
import {
  NzFormatEmitEvent,
  NzMessageService,
  NzModalRef,
  NzTreeComponent,
  NzTreeNode,
} from "ng-zorro-antd";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ValidationErrors,
  Validators,
} from "@angular/forms";
import { isNull } from "util";
import _ from "lodash";
import { ActivatedRoute } from "@angular/router";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

interface Question {
  id: string;
  name: any; //题干
  nameEn: any;
  type: string; //题型
  code: "";
  customDimension: null;
  // customDimensionDescription: string;//维度解释
  parentCustomDimension: null; //二级维度
  childCustomDimension: null; //三级维度
  proportion: number; //比重
  isRequire: Boolean; //选填必填
}

interface Option {
  id: string | number;
  name: any;
  value: number; //分值
  isScoring: boolean; //是否计分
  stepScore;
  lowestScore;
  highestScore;
  isEnableOpenAnswer: boolean;
  isRequireOpenAnswer: boolean;
}

@Component({
  selector: "app-add-custom-book",
  templateUrl: "./add-custom-book.component.html",
  styleUrls: ["./add-custom-book.component.less"],
})
export class AddCustomBookComponent implements OnInit {
  @Input() id: string; //题目id
  @Input() questionnaireId: string; //问卷id
  @Input() projectId: string;
  @Input() isedited: Boolean;
  @Input() nametip: string;
  @Input() isVisible;
  @Input() standardReportType: string; // 问卷类型
  @Input() currentReportType: string; // 当前问卷类型-多问卷用

  // 题本修订
  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;
  @ViewChild("nzTreeComponentRenkou", { static: false })
  nzTreeComponentRenkou: NzTreeComponent;
  @ViewChild("nzTreeComponentQues", { static: false })
  nzTreeComponentQues: NzTreeComponent;
  permission: boolean = this.permissionService.isPermission();
  validateForm: FormGroup;
  standard_360: boolean = false;
  optionList: Option[] = []; //新增、编辑选项列表
  // expandedNodes=[];
  expandedRenNodes = [];
  expandedroles = [];
  roleList = [];
  expandedRevisionTypes = [];
  revisionTypes = [];
  demographics = [];
  selectConditionVal: number = 1;
  resultList = [];
  revisionConditionObj = [
    {
      value: 1,
      label: "角色",
    },
    {
      value: 2,
      label: "人口标签",
    },
    {
      value: 3,
      label: "对象",
    },
  ];
  factorlist: any = []; //添加维度
  ClassAfactor: any = []; //一级维度
  ClassBfactor: any = []; //二级维度
  ClassCfactor: any = []; //三级维度
  oldDemoshow: number = 1; // 1 新增 2 维度 3 题本修订
  savemodal = false;
  selectedNodes = [];
  selectedRenkouNodes = [];
  selectedQuesNodes = [];
  questionTypeList; //题型
  questionData: Question = {
    id: "",
    name: "",
    nameEn: "",
    code: "",
    type: "SINGLE",
    customDimension: null,
    parentCustomDimension: null,
    childCustomDimension: null,
    proportion: 0,
    isRequire: true,
  };
  batchquestionData = [
    {
      id: "",
      name: "",
      nameEn: "",
      code: "",
      type: "SINGLE",
      optionList: [],
      customDimension: null,
      parentCustomDimension: null,
      childCustomDimension: null,
      proportion: 0,
      isRequire: true,
      totalScoreStatus: true,
      multiSelectMin: null,
      multiSelectMax: null,
      validateForm: this.fb.group({
        id: [""],
        name: ["", [Validators.required]],
        nameEn: [""],
        code: ["", [Validators.required]],
        type: ["SINGLE", [Validators.required]],
        customDimension: [null, [Validators.required]],
        parentCustomDimension: [null],
        childCustomDimension: [null],
        proportion: [1, [Validators.required]],
        isRequire: [true, [Validators.required]],
      }),
    },
  ];

  batchshow = false;
  optionId = 0;

  constructor(
    private api: ProjectManageService,
    private msg: NzMessageService,
    private fb: FormBuilder,
    private modalRef: NzModalRef,
    private cdr: ChangeDetectorRef,
    private routeInfo: ActivatedRoute,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {}

  ngOnInit() {
    this.questionTypeList = this.api.questionTypeList;
    this.standard_360 =
      this.routeInfo.snapshot.queryParams.edittype &&
      this.routeInfo.snapshot.queryParams.edittype.includes("STAND");
    if (!this.isedited) {
      this.questionTypeList = this.questionTypeList.filter((res) => {
        return (
          res.id == "ESSAY_QUESTION" ||
          res.id == "MULTIPLE_CHOICE_ESSAY_QUESTION"
        );
      });
    }
    // 自定义问卷过滤比重题 题本年修订-角色
    if (
      this.standardReportType == "BLANK_CUSTOM" ||
      this.currentReportType == "BLANK_CUSTOM"
    ) {
      const filterQuestionTypes = ["PROPORTION", "PROPORTION_MULTIPLE"];
      this.questionTypeList = this.questionTypeList.filter((res) => {
        return !filterQuestionTypes.includes(res.id);
      });
      this.revisionConditionObj = this.revisionConditionObj.filter(
        (res) => res.value != 1
      );
      this.selectConditionVal = 2;
    }
    // this.validateForm =
    if (this.id) this.initData();
    this.getlistfactor();
  }

  initData() {
    this.api.getQuestion(this.id).subscribe((res) => {
      // console.log(444, res)
      if (res.result.code === 0) {
        const {
          id: questionId,
          name: questionName,
          code,
          type,
          customDimension = null,
          parentCustomDimension = null,
          childCustomDimension = null,
          proportion = 1,
          isRequire,
        } = res.data;

        const { multiSelectMin, multiSelectMax } = res.data.options;
        const options = res.data.options.options;
        // this.batchquestionData[0].multiSelectMin = res.data.options.multiSelectMin;
        // this.batchquestionData[0].multiSelectMax = res.data.options.multiSelectMax;
        this.batchquestionData[0] = {
          id: questionId,
          name: questionName.zh_CN,
          code,
          nameEn:
            typeof questionName.en_US === "undefined" ||
            isNull(questionName.en_US)
              ? ""
              : questionName.en_US,
          type,
          customDimension: customDimension || null,
          parentCustomDimension: parentCustomDimension || null,
          childCustomDimension: childCustomDimension || null,
          proportion,
          isRequire,
          optionList: [],
          totalScoreStatus: res.data.options.totalScoreStatus === "ENABLE",
          multiSelectMin,
          multiSelectMax,
          validateForm: this.fb.group({
            id: [""],
            nameEn: [""],
            code: ["", [Validators.required]],
            name: ["", [Validators.required]],
            type: ["SINGLE", [Validators.required]],
            customDimension: [null, [Validators.required]],
            parentCustomDimension: [null],
            childCustomDimension: [null],
            proportion: [1, [Validators.required]],
            isRequire: [true, [Validators.required]],
          }),
        };
        this.questionData = {
          id: questionId,
          name: questionName.zh_CN,
          nameEn:
            typeof questionName.en_US === "undefined" ||
            isNull(questionName.en_US)
              ? ""
              : questionName.en_US,
          type,
          code,
          customDimension: customDimension || null,
          parentCustomDimension: parentCustomDimension || null,
          childCustomDimension: childCustomDimension || null,
          proportion,
          isRequire,
        };
        this.batchquestionData[0].validateForm.setValue(this.questionData);
        for (const option of options) {
          const {
            id: optionId,
            name: optionName,
            value,
            isScoring,
            stepScore,
            lowestScore,
            highestScore,
            isEnableOpenAnswer,
            isRequireOpenAnswer,
            questionId: questionId,
            answerType,
          } = option;
          // console.log(666, answerType)
          this.onAddOption(0, {
            id: optionId,
            name: optionName,
            value,
            isScoring,
            stepScore,
            lowestScore,
            highestScore,
            isEnableOpenAnswer,
            isRequireOpenAnswer,
            questionId,
            answerType,
          });
        }
      }
    });
  }
  formatter(data) {
    data.forEach((item) => {
      item.title = item.name.zh_CN;
      item.key = item.id;
      if (item.children && item.children.length !== 0) {
        item.isLeaf = false;
        this.formatter(item.children);
      } else {
        item.isLeaf = true;
      }
    });
  }
  getDateRes(params) {
    this.api.getOrCreateRevisionResult(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.resultList = res.data;
      }
    });
  }
  toRevision() {
    // 切换至题本修订 获取修订数据
    this.api.getCreateRevisionInfo(this.id).subscribe((res) => {
      if (res.result.code === 0) {
        this.oldDemoshow = 3;
        if (res.data.demographics && res.data.demographics.length > 0) {
          res.data.demographics.map((item) => {
            this.expandedRenNodes.push(item.id);
          });
        }

        if (res.data.roles && res.data.roles.length > 0) {
          res.data.roles.map((item) => {
            this.expandedroles.push(item.id);
          });
        }
        if (res.data.revisionTypes && res.data.revisionTypes.length > 0) {
          res.data.revisionTypes.map((item) => {
            this.expandedRevisionTypes.push(item.id);
          });
        }
        this.demographics = res.data.demographics || [];
        this.formatter(this.demographics);
        this.roleList = res.data.roles || [];
        this.formatter(this.roleList);
        this.revisionTypes = res.data.revisionTypes || [];
        this.formatter(this.revisionTypes);
      }
    });
    this.getDateRes({ questionId: this.id });
  }
  getfactorListParam() {
    let param = {};
    let factorList = [];
    const demographics = this.nzTreeComponentRenkou.getTreeNodes();

    demographics.map((children1) => {
      let demographicRootId;
      let demographicIds = [];
      if (children1.children.length !== 0) {
        children1.children.map((children2) => {
          if (children2.children.length !== 0) {
            children2.children.map((children3) => {
              if (
                _.find(children3, function() {
                  return children3.isChecked;
                })
              ) {
                demographicRootId = children1.key;
                demographicIds.push(children3.key);
              }
            });
          } else {
            if (
              _.find(children2, function() {
                return children2.isChecked;
              })
            ) {
              demographicRootId = children1.key;
              demographicIds.push(children2.key);
            }
          }
        });
      }
      if (demographicRootId) {
        factorList.push({
          demographicRootId: demographicRootId,
          demographicIds: demographicIds,
        });
      }
    });

    return factorList;
  }
  createRevision() {
    // 生成修订数据

    let demographicContents = this.getfactorListParam(); // 人口标签参数
    if (
      this.standardReportType == "BLANK_CUSTOM" ||
      this.currentReportType == "BLANK_CUSTOM"
    ) {
      if (demographicContents.length === 0) {
        // this.msg.error("请勾选人口标签");
        this.customMsg.open("error", "请勾选人口标签");
        return;
      }
    } else {
      if (demographicContents.length === 0 && this.selectedNodes.length === 0) {
        // this.msg.error("请勾选角色或人口标签");
        this.customMsg.open("error", "请勾选角色或人口标签");
        return;
      }
    }
    if (this.selectedQuesNodes.length === 0) {
      // this.msg.error("对象未勾选");
      this.customMsg.open("error", "对象未勾选");
      return;
    }
    let params = {
      demographicContents: demographicContents,
      roleIds: this.selectedNodes,
      questionId: this.id,
      revisionTypes: this.selectedQuesNodes,
    };

    this.getDateRes(params);
  }
  // get should select map
  selectChild(node: any, isSelected: boolean, map: any) {
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        map[child.key] = 1;
        this.selectChild(child, isSelected, map);
      }
    }
  }

  // set select node by map
  setSelectState(node: NzTreeNode, isSelected: boolean, map: any) {
    if (map[node.key] === 1) {
      if (node.origin.isVirtual) {
        node.isDisabled = true;
        node.isChecked = false;
      } else {
        node.isDisabled = isSelected;
        // node.setSelected(isSelected);
        node.isChecked = false;
      }
    }
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        this.setSelectState(child, isSelected, map);
      }
    }
  }
  nzEvent(event: NzFormatEmitEvent): void {
    // if (event.node.isDisabled) {
    //   return;
    // }
    // this.chooseType = null
    // select children
    let currentNode: any = event.node.origin;
    let isSelected: boolean = currentNode.checked;
    let map = {};
    this.selectChild(currentNode, isSelected, map);

    let allNodes: NzTreeNode[] = this.nzTreeComponent.getTreeNodes();
    allNodes.forEach((n: NzTreeNode) => {
      this.setSelectState(n, isSelected, map);
    });

    // update displaying selected nodes
    let nodeList: NzTreeNode[] = this.nzTreeComponent.getCheckedNodeList();
    let tmp: any[] = [];
    for (let index = 0; index < nodeList.length; index++) {
      const element = nodeList[index].origin;
      tmp.push(element.key);
    }
    this.selectedNodes = tmp;

    // this.getShowList();
  }
  nzEventRen(event: NzFormatEmitEvent, data): void {
    // this.chooseType = null
    // update displaying selected nodes
    const nodeList: NzTreeNode[] = this.nzTreeComponentRenkou.getTreeNodes();
    let all = true; // 是否全选
    let tmp: any[] = [];
    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        node1.children.map((node2) => {
          if (node2.children.length !== 0) {
            node2.children.map((node3) => {
              if (node3.isChecked) {
                tmp.push({ id: node3.key, name: node3.title });
              } else {
                all = false;
              }
            });
          } else {
            if (node2.isChecked) {
              tmp.push({ id: node2.key, name: node2.title });
            } else {
              all = false;
            }
          }
        });
      }
    });

    this.selectedRenkouNodes = tmp;
  }

  nzEventQues(event: NzFormatEmitEvent): void {
    // this.chooseType = null
    // update displaying selected nodes
    let tmp: any[] = [];
    const nodeList: NzTreeNode[] = this.nzTreeComponentQues.getTreeNodes();

    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        node1.children.map((node2) => {
          if (node2.isChecked) {
            tmp.push(node2.origin);
          }
        });
      } else {
        if (node1.isChecked) {
          tmp.push(node1.origin);
        }
      }
    });

    this.selectedQuesNodes = tmp;
  }
  getSaveRevision() {
    // 保存修订数据
    const param = {
      questionId: this.id,
      list: this.resultList,
    };
    if (param.list.length === 0) {
      // return this.msg.error("请先创建内容");
      this.customMsg.open("error", "请先创建内容");
      return;
    }
    this.api.saveRevisionResult(param).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("保存成功");
        this.oldDemoshow = 1;
        this.selectConditionVal =
          this.standardReportType == "BLANK_CUSTOM" ||
          this.currentReportType == "BLANK_CUSTOM"
            ? 2
            : 1;
        this.roleList = [];
        this.demographics = [];
        this.revisionTypes = [];

        this.expandedroles = [];
        this.expandedRenNodes = [];
        this.expandedRevisionTypes = [];

        this.selectedNodes = [];
        this.selectedRenkouNodes = [];
        this.selectedQuesNodes = [];

        this.resultList = [];
      }
    });
  }
  deleteOne(resInd, queInd) {
    if (this.resultList[resInd].revisionInfoVos.length === 1) {
      this.resultList.splice(resInd, 1);
    } else {
      this.resultList[resInd].revisionInfoVos.splice(queInd, 1);
    }
  }

  clear() {
    let orgTree = this.nzTreeComponent.getTreeNodes();
    let demoTree = this.nzTreeComponentRenkou.getTreeNodes();
    let queTree = this.nzTreeComponentQues.getTreeNodes();
    this.selectedNodes = [];
    orgTree.forEach((item) => {
      this.clearTree(item);
    });
    demoTree.forEach((item) => {
      this.clearTree(item);
    });
    queTree.forEach((item) => {
      this.clearTree(item);
    });
    this.api.clearRevisionResult(this.id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("数据已清空");
        this.getDateRes({ questionId: this.id });
      }
    });
  }
  clearTree(node) {
    node.isHalfChecked = false;
    if (node.isLeaf) {
      if (node.isChecked) {
        node.isChecked = false;
      }
    } else {
      if (node.isChecked) {
        node.isChecked = false;
      }
      node.children.forEach((element) => {
        this.clearTree(element);
      });
    }
  }
  ManageOption() {
    this.getlistfactor("add");
  }
  ngModelChange(e) {
    this.questionData.type = e;
  }

  getSaveSet() {
    let nosave = false;
    let nosaveEn = false;
    let description = false;
    let descriptionEn = false;
    // 维度code必填
    let codeRequired = false;
    let codeError = "";

    this.factorlist.forEach((res) => {
      if (res.name.zh_CN.length > 20) {
        nosave = true;
      }
      if (res.name.en_US && res.name.en_US.length > 50) {
        nosaveEn = true;
      }
      if (res.description.zh_CN && res.description.zh_CN.length > 150) {
        description = true;
      }

      if (res.description.en_US && res.description.en_US.length > 500) {
        descriptionEn = true;
      }

      // 维度code
      if (!res.code) {
        codeRequired = true;
        const typeMap = {
          ONE_RANK: "一级维度",
          TWO_RANK: "二级维度",
          THREE_RANK: "三级维度",
        };
        codeError = `请输入${typeMap[res.type]}【${res.name.zh_CN}】的编码`;
      }
    });
    // 检查维度code重复
    const duplicates = this.hasDuplicate(this.factorlist, "code");
    if (nosave) {
      // this.msg.error("中文维度名称超过字数限制长度！长度为20");
      this.customMsg.open("error", "中文维度名称超过字数限制长度！长度为20");
      return;
    }

    if (nosaveEn) {
      // this.msg.error("英文维度名称超过字数限制长度！长度为50");
      this.customMsg.open("error", "英文维度名称超过字数限制长度！长度为50");
      return;
    }

    if (description) {
      // this.msg.error("中文维度说明超过字数限制长度！长度为150");
      this.customMsg.open("error", "中文维度说明超过字数限制长度！长度为150");
      return;
    }

    if (descriptionEn) {
      // this.msg.error("英文维度说明超过字数限制长度！长度为500");
      this.customMsg.open("error", "中文维度说明超过字数限制长度！长度为150");
      return;
    }
    let factornames = [];
    /*factornames.push(_.filter(this.factorlist, {
      name: {zh_CN:""}
    }).length)

    if (factornames.filter(item => item !== 0).length > 0) {
      this.msg.error('请将维度名称填写完整')
      return
    }*/
    // tslint:disable-next-line:only-arrow-functions
    factornames = this.factorlist.filter(function(value) {
      return !value.id && value.name.zh_CN === "" && value.name.zh_CN !== 0;
    });

    if (factornames.length > 0) {
      // this.msg.error("请将维度名称填写完整");
      this.customMsg.open("error", "请将维度名称填写完整");
      return;
    }

    // 维度code
    if (codeRequired) {
      // this.msg.error(codeError);
      this.customMsg.open("error", codeError);
      return;
    }
    const duplicatesArr = [];
    if (duplicates.ONE_RANK.length > 0) {
      const duplicateCodes = _.uniq(
        duplicates.ONE_RANK.map((res) => {
          return res.code;
        })
      );
      duplicatesArr.push(`一级维度编码${duplicateCodes.join()}`);
    }
    if (duplicates.TWO_RANK.length > 0) {
      const duplicateCodes = _.uniq(
        duplicates.TWO_RANK.map((res) => {
          return res.code;
        })
      );
      duplicatesArr.push(`二级维度编码${duplicateCodes.join()}`);
    }
    if (duplicates.THREE_RANK.length > 0) {
      const duplicateCodes = _.uniq(
        duplicates.THREE_RANK.map((res) => {
          return res.code;
        })
      );
      duplicatesArr.push(`三级维度编码${duplicateCodes.join()}`);
    }
    if (duplicatesArr.length > 0) {
      // this.msg.error(`${duplicatesArr.join(";")}重复`);
      this.customMsg.open("error", `${duplicatesArr.join(";")}重复`);
      return;
    }

    let dimensions = [];
    this.factorlist.forEach((res, i) => {
      dimensions.push({
        name: { zh_CN: res.name.zh_CN, en_US: res.name.en_US },
        description: {
          zh_CN: res.description.zh_CN,
          en_US: res.description.en_US,
        },
        questionnaireId: res.questionnaireId,
        projectId: res.projectId,
        type: res.type,
        proportion: res.proportion,
        algorithmType: res.algorithmType,
        algorithmData: res.algorithmData,
        id: res.id,
        sort: i,
        code: res.code,
      });
    });
    const parmas = {
      questionnaireId: this.questionnaireId,
      dimensions,
    };
    this.api.SaveSurveyCustomDimension(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.oldDemoshow = 1;
        this.getlistfactor();
      }
    });
  }
  getlistfactor(type?) {
    this.api
      .listSurveyCustomDimension(this.questionnaireId)
      .subscribe((item) => {
        this.ClassAfactor = [];
        if (item.result.code == 0) {
          this.factorlist = item.data;
          this.factorlist.forEach((res) => {
            if (!res.description) {
              res.description = { zh_CN: "", en_US: "" };
            }
          });
          this.ClassAfactor = item.data.filter((item) => {
            return item.type == "ONE_RANK";
          });
          this.ClassBfactor = item.data.filter((item) => {
            return item.type == "TWO_RANK";
          });
          this.ClassCfactor = item.data.filter((item) => {
            return item.type == "THREE_RANK";
          });
          // this.ClassAfactor.unshift({
          //   name: '无'
          // })
          this.oldDemoshow = 1;
          if (type == "add") {
            this.oldDemoshow = 2;
          }
        }
      });
  }
  getDefault() {
    this.factorlist = [];
    this.oldDemoshow = 1;
  }
  addfactor() {
    this.factorlist.push({
      name: { zh_CN: "", en_US: "" },
      description: { zh_CN: "", en_US: "" },
      questionnaireId: this.questionnaireId,
      projectId: this.projectId,
      type: "ONE_RANK",
      proportion: 1,
      algorithmData: {},
      algorithmType: null,
    });
  } //添加维度

  onDeletefactorlist(index, id) {
    this.api.listdeletefactor(id).subscribe((res) => {
      if (!res.data) {
        this.factorlist.splice(index, 1);
      } else {
        // this.msg.error("该维度已绑定题目，不可删除！");
        this.customMsg.open("error", "该维度已绑定题目，不可删除！");
      }
    });
    //
  }

  /**
   * 提交校验
   */
  valid() {
    let checked = true;
    this.batchquestionData.forEach((res) => {
      for (const key in res.validateForm.controls) {
        res.validateForm.controls[key].markAsDirty();
        res.validateForm.controls[key].updateValueAndValidity();
      }
      if (!res.validateForm.valid) {
        checked = false;
      }
    });

    return checked;

    // if (this.validateForm.controls.type.value !== 'ESSAY_QUESTION') {
    //   this.msg.error('该题型选项不能为空');
    //   return false;
    // }

    // for (let item of this.optionList) {
    //   if (!item.name.zh_CN) {
    //     this.msg.error('选项不能为空');
    //     return false;
    //   }
    // }
  }

  /**
   * onAddOption添加选项
   */
  onAddOption(
    index?,
    optionData = {
      id: ++this.optionId,
      name: {},
      value: 1,
      isScoring: true,
      lowestScore: null,
      highestScore: null,
      stepScore: 0.1,
      isEnableOpenAnswer: true,
      questionId: "",
      isRequireOpenAnswer: null,
      answerType: null,
    }
  ) {
    this.batchquestionData[index].optionList.push({
      id: optionData.id || ++this.optionId,
      name: optionData.name,
      value: optionData.value,
      isScoring: optionData.isScoring,
      lowestScore: optionData.lowestScore,
      highestScore: optionData.highestScore,
      stepScore: optionData.stepScore,
      totalScoreStatus: true,
      isEnableOpenAnswer: optionData.isEnableOpenAnswer,
      isRequireOpenAnswer: optionData.isRequireOpenAnswer,
      questionId: optionData.questionId,
      answerType: optionData.answerType,
    });
  }

  /**
   * onDeleteOption 删除选项
   * @param id
   */
  onDeleteOption(i, j) {
    this.batchquestionData[i].optionList.splice(j, 1);
    // this.optionList.splice(index, 1);
  }

  /**
   * 新增、编辑
   */
  getcommit() {
    let messageshow = false;
    let stepScoreshow = false;
    let showoptions = false;
    let nameshow = false;
    let valueshow = false;
    let messagelowScoreshow = false;
    let messagehighScoreshow = false;
    let messageSelectMinshow = false;
    let messageSelectMaxshow = false;
    let messagenumbershow = false;
    let codeshow = false;
    let requireOpenAnswerShow = false;
    // console.log(555,  this.batchquestionData)
    this.batchquestionData.forEach((item) => {
      if (!item.customDimension) {
        codeshow = true;
      }
      if (item.type == "PROPORTION" || item.type == "PROPORTION_MULTIPLE") {
        item.optionList.forEach((res) => {
          if (!res.name.zh_CN || res.name.zh_CN == "") {
            nameshow = true;
          }
          if (res.lowestScore === "") {
            messagelowScoreshow = true;
          }
          if (res.lowestScore === "") {
            messagehighScoreshow = true;
          }
          if (res.lowestScore >= res.highestScore) {
            messageshow = true;
          }
        });

        item.optionList.forEach((res) => {
          if (Number(res.stepScore) < 0.1) {
            stepScoreshow = true;
          }
        });
      } else {
        if (item.type != "ESSAY_QUESTION") {
          item.optionList.forEach((res) => {
            if (!res.name.zh_CN || res.name.zh_CN == "") {
              nameshow = true;
            }

            if (res.value === "") {
              valueshow = true;
            }

            //开放题校验追问是否必填
            if (item.type === "MULTIPLE_CHOICE_ESSAY_QUESTION") {
              if (
                res.isEnableOpenAnswer &&
                (res.isRequireOpenAnswer === "" ||
                  res.isRequireOpenAnswer == null)
              ) {
                requireOpenAnswerShow = true;
              }
            }
          });
        }

        if (item.type === "ESSAY_QUESTION") {
          item.proportion = 0;
        }
        if (item.type == "MULTIPLE_CHOICE_ESSAY_QUESTION") {
          item.proportion = 0;
          if (item.multiSelectMin === "" || item.multiSelectMin == null) {
            messageSelectMinshow = true;
          }
          if (item.multiSelectMax === "" || item.multiSelectMax == null) {
            messageSelectMaxshow = true;
          }
          if (item.multiSelectMin > item.multiSelectMax) {
            messagenumbershow = true;
          }
        }
      }
      if (item.optionList.length == 0 && item.type != "ESSAY_QUESTION") {
        showoptions = true;
      }
    });
    if (messageshow) {
      // this.msg.error("比重题最小值不可以大于或者等于最大值");
      this.customMsg.open("error", "比重题最小值不可以大于或者等于最大值");
      return;
    }
    if (stepScoreshow) {
      // this.msg.error("刻度值最小为0.1");
      this.customMsg.open("error", "刻度值最小为0.1");
      return;
    }
    if (showoptions) {
      // this.msg.error("除开放题外,选项不能为空！");
      this.customMsg.open("error", "除开放题外,选项不能为空！");
      return;
    }
    if (nameshow) {
      // this.msg.error("选项必须要有名称！");
      this.customMsg.open("error", "选项必须要有名称！");
      return;
    }
    if (requireOpenAnswerShow) {
      // this.msg.error("请确认选项开放式回答是否必填！");
      this.customMsg.open("error", "选项必须要有名称！");
      return;
    }
    if (valueshow) {
      // this.msg.error("选项分数不能为空值！");
      this.customMsg.open("error", "选项分数不能为空值！");
      return;
    }
    if (messagehighScoreshow) {
      // this.msg.error("选项分数最大值不能为空值！");
      this.customMsg.open("error", "选项分数最大值不能为空值！");
      return;
    }
    if (messagelowScoreshow) {
      // this.msg.error("选项分数最小值不能为空值！");
      this.customMsg.open("error", "选项分数最小值不能为空值！");
      return;
    }
    if (codeshow) {
      // this.msg.error("二级维度不能为空！");
      this.customMsg.open("error", "二级维度不能为空！");
      return;
    }
    if (messageSelectMinshow) {
      // this.msg.error("选项个数最小值不能为空值！");
      this.customMsg.open("error", "选项个数最小值不能为空值！");
      return;
    }
    if (messageSelectMaxshow) {
      // this.msg.error("选项个数最大值不能为空值！");
      this.customMsg.open("error", "选项个数最大值不能为空值！");
      return;
    }
    if (messagenumbershow) {
      // this.msg.error("选项个数最小值不可以大于最大值");
      this.customMsg.open("error", "选项个数最小值不可以大于最大值");
      return;
    }

    if (
      !messageshow &&
      !stepScoreshow &&
      !showoptions &&
      !nameshow &&
      !valueshow &&
      !messagehighScoreshow &&
      !messagelowScoreshow
    ) {
      if (this.valid()) {
        this.savemodal = true;
        // this.modalRef.destroy()
      }
    }
    let data = {
      questionnaireId: this.questionnaireId,
    };
    this.onSubmit(data);
  }
  getrelease() {
    this.savemodal = false;
    this.modalRef.destroy();
  }

  onSubmit(params) {
    if (this.savemodal) {
      params.questions = [];
      this.batchquestionData.forEach((res) => {
        // console.log(333, res)
        params.questions.push({
          id: res.id,
          name: { zh_CN: res.name, en_US: res.nameEn },
          code: res.code,
          type: res.type,
          customDimension: res.customDimension,
          parentCustomDimension: res.parentCustomDimension,
          childCustomDimension: res.childCustomDimension,
          proportion: res.proportion,
          isRequire: res.isRequire,
          optionList: res.optionList,
          multiSelectMin: res.multiSelectMin,
          multiSelectMax: res.multiSelectMax,
          totalScoreStatus: res.totalScoreStatus,
          // questionId
        });
      });

      params.questions.forEach((item) => {
        item.options = {
          multiSelectMin: item.multiSelectMin,
          multiSelectMax: item.multiSelectMax,
          options: [],
        };
        item.options.options = item.optionList;
        // console.log(222, item.optionList)
        if (item.type == "PROPORTION_MULTIPLE") {
          item.options.totalScoreStatus = item.totalScoreStatus
            ? "ENABLE"
            : "DISABLE";
        }
      });

      let data = {
        params: params,
        type: "",
      };
      if (this.valid()) {
        if (this.id) {
          // data.type = 'update'
          // return data
          let newparmas = { questionnaireId: "" };
          newparmas = data.params.questions[0];
          newparmas.questionnaireId = data.params.questionnaireId;
          // console.log(111,  newparmas)
          this.api.updateQuestion(newparmas).subscribe((res) => {
            if (res.result.code === 0) {
              this.msg.success("编辑成功");
              this.modalRef.destroy();
            } else {
              // this.modalRef.destroy()
            }
          });
        } else {
          // data.type = 'create'
          // return data
          this.api.createQuestion(data.params).subscribe((res) => {
            if (res.result.code === 0) {
              this.msg.success("保存成功");
              this.modalRef.destroy();
            } else {
              // this.modalRef.destroy()
            }
          });
        }
      } else {
        // return null
      }
    } else {
      // return null
    }
  }
  addNewlist() {
    this.batchquestionData.push({
      id: "",
      name: "",
      code: "",
      nameEn: "",
      type: "SINGLE",
      optionList: [],
      customDimension: null,
      parentCustomDimension: null,
      childCustomDimension: null,
      proportion: 0,
      isRequire: true,
      totalScoreStatus: true,
      multiSelectMin: null,
      multiSelectMax: null,
      validateForm: this.fb.group({
        id: [""],
        name: ["", [Validators.required]],
        code: ["", [Validators.required]],
        nameEn: "",
        type: ["SINGLE", [Validators.required]],
        customDimension: [null, [Validators.required]],
        parentCustomDimension: [null],
        childCustomDimension: [null],
        proportion: [1, [Validators.required]],
        isRequire: [true, [Validators.required]],
      }),
    });
    // setTimeout(()=>{

    // },1000)
  }
  deletelist(i) {
    if (this.batchquestionData.length > 1) {
      this.batchquestionData.splice(i, 1);
    }
  }
  ngquireChange(item) {
    if (!item.isRequire) {
      item.multiSelectMin = 0;
    } else {
      // item.multiSelectMin = null
    }
  }

  algorithmChange(val, item) {
    const { algorithmType, algorithmData } = val;
    item.algorithmType = algorithmType;
    item.algorithmData = algorithmData;
  }

  /**
   * 检查重复字段
   * @param array
   * @param field
   * @returns
   */
  hasDuplicate(array, field) {
    const one = array.filter((val) => val.type === "ONE_RANK");
    const two = array.filter((val) => val.type === "TWO_RANK");
    const three = array.filter((val) => val.type === "THREE_RANK");
    const grouped_one = _.groupBy(one, field);
    const grouped_two = _.groupBy(two, field);
    const grouped_three = _.groupBy(three, field);
    const map = {
      ONE_RANK: _.flatMap(grouped_one, (items) =>
        items.length > 1 ? items : []
      ),
      TWO_RANK: _.flatMap(grouped_two, (items) =>
        items.length > 1 ? items : []
      ),
      THREE_RANK: _.flatMap(grouped_three, (items) =>
        items.length > 1 ? items : []
      ),
    };
    return map;
  }

  /**
   * 根据选项的值或标签进行检索
   * @param inputValue
   * @param option
   * @returns
   */
  filterOption(inputValue: string, option: any): boolean {
    return (
      option.nzValue.indexOf(inputValue) > -1 ||
      option.nzLabel.indexOf(inputValue) > -1
    );
  }
}
