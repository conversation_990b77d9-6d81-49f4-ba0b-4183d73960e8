@text-color: #17314C;

.content {
    width: 100%;
}

.s1 {
    width: 100%;
    background-color: #ffffff;

    &>div {
        margin: 0 auto;
        padding: 60px 0;
        display: flex;
    }

    .s1-l {
        flex: 2;
        padding-right: 150px;
    }

    .s1-r {
        flex: 1;
    }

    h5 {
        font-size: 30px;
        line-height: 42px;
        margin-bottom: 25px;

        span {
            font-size: 12px;
            color: #409EFF;
            border-radius: 14px;
            padding: 2px 10px;
            margin-left: 30px;
            background-color: rgba(64, 158, 255, 0.1);
        }
    }

    p {
        font-size: 14px;
        line-height: 24px;
        margin-bottom: 80px;
    }

    .btn {
        width: 160px;
        height: 38px;
        line-height: 38px;
        background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        font-size: 16px;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
    }
}

.s2 {
    width: 100%;
    background-color: #F5F6FA;

    &>div {
        width: 1000px;
        margin: 0 auto;
        padding: 60px 0;
    }

    h5 {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        margin: 40px 0 50px;
    }

    p {
        text-align: center;
        margin-bottom: 77px;
    }

    ul {
        display: flex;
        justify-content: space-between;

        img {
            display: block;
            margin-bottom: 42px;
        }

        li {
            border-radius: 8px;
            background-color: #ffffff;
            width: 185px;
            padding: 25px 25px 33px;
        }

        h3 {
            font-size: 18px;
            font-weight: bold;
        }
    }
}

.s3 {
    width: 100%;
    text-align: center;

    &>div {
        margin: 0 auto;
        padding: 60px 0;
    }

    p {
        margin-bottom: 60px;
    }

    h5 {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        margin: 40px 0 50px;
    }
}

.s4 {
    width: 100%;
    background-color: #F5F6FA;
    padding-top: 40px;
    padding-bottom: 100px;

    h5 {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 76px 0 36px;
    }

    .s4-main {
        margin: 0 auto;
        padding: 30px 0 60px;
        display: flex;

        li {
            flex: 1;
            text-align: center;
            padding-right: 80px;

            &:last-of-type {
                padding-right: 0;
            }

            h3 {
                font-size: 20px;
                margin: 10px 0 30px;
            }

            p {
                font-size: 16px;
                text-align: left;
                padding: 0 30px;
            }
        }
    }

    .btn {
        text-align: center;

        button {
            width: 160px;
            height: 38px;
            line-height: 38px;
            background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 19px;
            font-size: 16px;
            font-weight: bold;
            color: #ffffff;
        }
    }
}

.s5 {
    width: 100%;
    background-color: #ffffff;

    .s5-main {
        margin: 0 auto;
        padding: 60px 0;
        display: flex;
    }

    h5 {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 76px 0 36px;
    }

    h3 {
        font-size: 20px;
        margin-bottom: 20px;
        padding-left: 20px;
    }

    .s5-r {
        flex: 1;
        font-size: 16px;
        padding-top: 30px;

        ul {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        p {
            position: relative;
            font-size: 16px;
            padding-left: 20px;
        }
    }

    .s5-l {
        width: 60%;
        text-align: center;
    }

    .btn {
        margin: 30px 0;
        padding-left: 40px;

        button {
            width: 160px;
            height: 38px;
            line-height: 38px;
            background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 19px;
            font-size: 16px;
            font-weight: bold;
            color: #ffffff;
        }
    }
}