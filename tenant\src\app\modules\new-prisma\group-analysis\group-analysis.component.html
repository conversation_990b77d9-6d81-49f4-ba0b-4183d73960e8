<!--
    *@author: <PERSON>
    *@Date: 2023/09/12
    *@content: 多群体分析
-->
<div class="box">
  <div nz-row class="row">
    <div nz-col nzSpan="12">
      <!-- 标题 -->
      <div class="title">
        <div>
          <span>人口标签</span>
          <span>按题目分析"同时满足不同标签的群体”的数据</span>
        </div>
        <button
          nz-button
          nzType="link"
          (click)="handClearLeft()"
        >
          清空
        </button>
      </div>
      <!-- 内容区域-滑动 -->
      <div class="scroll">
        <div class="scroll-inside hasBtns">
          <!-- 对比数据 -->
          <nz-spin [nzSpinning]="factorDataLoading">
            <nz-tree
              #nzTreeComponent
              [nzData]="factorData"
              nzCheckable
              [nzExpandAll]="false"
              [nzBlockNode]="true"
              [nzCheckedKeys]="defaultCheckedKeys"
              (nzClick)="expandedLeft($event)"
              (nzCheckBoxChange)="changeLeft($event)"
            ></nz-tree>
          </nz-spin>
        </div>
      </div>
      <div class="btns">
        <button
          nz-button
          nzShape="round"
          [nzLoading]="factorAddLoading"
          (click)="handOK()"
        >
          确定
        </button>
      </div>
    </div>
    <div nz-col nzSpan="12">
      <!-- 标题 -->
      <div class="title">
        <div>
          <span>结果</span>
        </div>
        <button
          nz-button
          nzType="link"
          (click)="handClearRight()"
          [nzLoading]="resultClearLoading"
        >
          清空
        </button>
      </div>
      <!-- 内容区域-滑动 -->
      <div class="scroll">
        <nz-spin [nzSpinning]="resultDataLoading">
          <div class="scroll-inside">
            <!-- 结果 -->
            <ng-container *ngIf="resultData.length > 0">
              <!-- 大类 -->
              <div
                class="right"
                *ngFor="let parent of resultData; let parentIndex = index"
              >
                <div class="right-title">
                  <div class="right-title-text">
                    <b>结果{{ parentIndex + 1 }}</b>
                    <span>{{ parent.firstName }}</span>
                    <span class="cross">交叉</span>
                    <span>{{ parent.secondName }}</span>
                  </div>
                  <!-- <i nz-icon nzType="delete" nzTheme="fill"></i> -->
                </div>
                <!-- 小类 -->
                <div class="right-card" *ngFor="let child of parent.items">
                  <div class="right-card-info">
                    <span class="tag">{{
                      child.firstParentDemographicName.zh_CN
                    }}</span>
                    <span class="val">{{
                      child.firstDemographicName.zh_CN
                    }}</span>
                    <span class="also">且</span>
                    <span class="tag">{{
                      child.secondParentDemographicName.zh_CN
                    }}</span>
                    <span class="val">{{
                      child.secondDemographicName.zh_CN
                    }}</span>
                  </div>
                  <i
                    nz-icon
                    nzType="delete"
                    nzTheme="fill"
                    (click)="handDelDemographic(child)"
                  ></i>
                </div>
              </div>
            </ng-container>
            <!-- 无数据 -->
            <ng-container *ngIf="resultData.length === 0">
              <app-empty></app-empty>
            </ng-container>
          </div>
        </nz-spin>
      </div>
    </div>
  </div>
</div>
<div class="footer">
  <button nz-button nzType="default" (click)="handClose()">关闭</button>
</div>
