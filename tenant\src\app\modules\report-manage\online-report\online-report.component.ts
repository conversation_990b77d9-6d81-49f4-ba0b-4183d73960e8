import { Component, OnInit } from "@angular/core";
@Component({
  selector: "app-online-report",
  templateUrl: "./online-report.component.html",
  styleUrls: ["./online-report.component.less"],
})
export class OnlineReportComponent implements OnInit {
  /**
   * 面包屑
   */
  breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/report-manage",
      name: "报告管理",
      Highlight: false,
    },
    {
      path: "",
      name: "我的定制报告",
      Highlight: true,
    },
  ];
  size: string = "default";
  /**
   * 搜索-关键字
   */
  searchValue: string = "";
  /**
   * 搜索-状态
   */
  searchStatus = null;
  /**
   * 搜索-工具
   */
  searchReportType = null;
  /**
   * 搜索-开始结束日期
   */
  searchDateRange = [];
  /**
   * 7天内到期活动
   */
  isSevenDays = false;

  /**
   * 排序方式
   * true:正序
   * false:倒序
   */
  orderType: boolean = true;
  /**
   * 显示模式
   * true:卡片
   * false:列表
   */
  modelType: boolean = false;
  /**
   * 翻页-当前页
   */
  pageCurrent: number = 1;
  /**
   * 翻页-总页数
   */
  pageTotal: number = 4;
  /**
   * 数据
   */
  data = [];
  /**
   * isLoading
   */
  isLoading = false;
  /**
   * 侧边栏显示隐藏
   */
  addVisible = false;
  /**
   * 在线看板模-全部
   */
  reportTemplateAll = [
    {
      name: "【个人报告】360行为反馈-1",
      img: "assets/images/report-home/online-report-1.png",
      type: "1",
      class: "0",
    },
    {
      name: "【个人报告】职业性格测评-1",
      img: "assets/images/report-home/online-report-2.png",
      type: "2",
      class: "1",
    },
    {
      name: "【个人报告】360行为反馈-2",
      img: "assets/images/report-home/online-report-1.png",
      type: "3",
      class: "0",
    },
    {
      name: "【个人报告】职业性格测评-2",
      img: "assets/images/report-home/online-report-2.png",
      type: "4",
      class: "1",
    },
    {
      name: "【个人报告】360行为反馈-3",
      img: "assets/images/report-home/online-report-1.png",
      type: "5",
      class: "0",
    },
    {
      name: "【个人报告】职业性格测评-3",
      img: "assets/images/report-home/online-report-2.png",
      type: "6",
      class: "1",
    },
  ];
  /**
   * 在线看板模-过滤
   */
  reportTemplateFilter = [];
  /**
   * params-选中的-在线看板模板
   */
  checkedReportTemplate = null;
  /**
   * 模板过滤-关键字
   */
  searchReportTemplateKeyword = null;
  /**
   * 模板过滤-分类
   */
  searchReportTemplateClassify = null;
  /**
   * params-报告名称
   */
  onlineReportName = null;
  /**
   * params-报告关联活动
   */
  onlineReportBindProject = null;

  /**
   * 快捷绑定活动
   */
  shortcutList = [
    {
      name: "最新的活动1",
      type: "AT",
      id: 1,
    },
    {
      name: "最新的活动2",
      type: "Tip2.0",
      id: 2,
    },
    {
      name: "最新的活动3",
      type: "MCA",
      id: 3,
    },
  ];
  /**
   * 选中的-快捷绑定活动
   */
  checkedShortcut = null;
  /**
   * 翻页
   * @param type
   * true: 上一页
   * false: 下一页
   */
  onChangePage(type) {
    if (type) {
      if (this.pageCurrent === 1) return;
      this.pageCurrent = this.pageCurrent - 1;
    } else {
      if (this.pageCurrent === this.pageTotal) return;
      this.pageCurrent = this.pageCurrent + 1;
    }
  }

  ngOnInit() {
    this.initData();
  }
  /**
   * 查询数据
   */
  initData() {
    const data = [
      {
        id: "1",
        name: "自定义的报告名称1",
        isOpen: true,
        isInFormation: false,
        number: 6,
        reportType: "敬业度调研",
        createTime: "2023-11-12 13:30",
        creator: "Pavith Nadal",
        isDue: false,
      },
      {
        id: "2",
        name: "自定义的报告名称2",
        isOpen: true,
        isInFormation: false,
        number: 6,
        reportType: "敬业度调研",
        createTime: "2023-11-12 13:30",
        creator: "Pavith Nadal",
        isDue: true,
      },
      {
        id: "3",
        name: "自定义的报告名称3",
        isOpen: true,
        isInFormation: true,
        number: 6,
        reportType: "敬业度调研",
        createTime: "2023-11-12 13:30",
        creator: "Pavith Nadal",
        isDue: false,
      },
      {
        id: "4",
        name: "自定义的报告名称4",
        isOpen: true,
        isInFormation: false,
        number: 6,
        reportType: "敬业度调研",
        createTime: "2023-11-12 13:30",
        creator: "Pavith Nadal",
        isDue: false,
      },
    ];
    this.isLoading = true;

    setTimeout(() => {
      // this.data = [];
      // this.data = [...data, ...data, ...data];
      this.data = data;
      this.isLoading = false;
    }, 1000);
  }
  /**
   * 搜索
   */
  onSrearch(): void {
    console.log("搜索: ");
    this.initData();
  }
  // 清空条件
  onClear(): void {
    console.log("清空条件: ");
    this.initData();
  }
  /**
   * 筛选-正序倒序
   * @param type
   */
  onChangeOrderType() {
    this.orderType = !this.orderType;
    this.initData();
  }

  /**
   * 筛选-模式切换
   * @param type
   */
  onChangeModelType(type) {
    this.isLoading = true;
    setTimeout(() => {
      this.modelType = type;
      this.isLoading = false;
    }, 1000);
  }
  /**
   * 筛选-7日内到期活动
   * @param e
   */
  onChangeIsSevenDays(e) {
    console.log(e);
    this.initData();
  }
  /**
   * 新建报告-侧边栏-开启
   */
  onOpenDrawer() {
    this.addVisible = true;
    this.reportTemplateFilter = [].concat(this.reportTemplateAll);
    // this.checkedReportTemplate = "3";
    // setTimeout(() => {
    //   this.scrollToSelectedElement();
    // });
  }
  /**
   * 新建报告-侧边栏-关闭
   */
  onCloseDrawer() {
    this.addVisible = false;
    // 模板过滤清空
    this.searchReportTemplateKeyword = null;
    this.searchReportTemplateClassify = null;
    this.checkedShortcut = null;
    // params清空
    this.checkedReportTemplate = null;
    this.onlineReportName = null;
    this.onlineReportBindProject = null;
  }
  /**
   * 新建报告-侧边栏-确定
   */
  onConfirm() {
    this.onCloseDrawer();
  }
  /**
   * 新建报告-选择模板-筛选
   */
  onFilterReportTemplate() {
    console.log(
      `关键字:${this.searchReportTemplateKeyword}`,
      `分类:${this.searchReportTemplateClassify}`
    );
    this.reportTemplateFilter = this.reportTemplateAll.filter((val) => {
      let isKeyword = true;
      let isClassify = true;
      if (this.searchReportTemplateKeyword) {
        isKeyword = val.name.includes(this.searchReportTemplateKeyword);
      }
      if (this.searchReportTemplateClassify) {
        isClassify = val.name.includes(this.searchReportTemplateClassify);
      }
      return isKeyword && isClassify;
    });
    // 若过滤后的模板中不存在当前选中模板，选中重置
    const hasCheckedIndex = this.reportTemplateFilter.findIndex(
      (val) => val.type === this.checkedReportTemplate
    );
    if (hasCheckedIndex < 0) {
      this.checkedReportTemplate = null;
    }
    this.scrollToSelectedElement();
  }
  /**
   * 新建报告-选择模板
   */
  onSelectReportTemplate(item) {
    this.checkedReportTemplate = item.type;
    this.scrollToSelectedElement();
  }
  /**
   * 滑块滚动
   */
  scrollToSelectedElement() {
    if (this.reportTemplateFilter.length < 3) return;
    var sliderContainer = document.getElementById("sliderContainer");
    // 容器宽度
    // const containerWidth = 518;
    // 元素的宽度
    const elementWidth = 197;
    const index = this.reportTemplateFilter.findIndex(
      (val) => val.type === this.checkedReportTemplate
    );
    // 选中元素相对于滑块容器左侧的距离
    const elementLeft = index * (elementWidth + 16) - 44;
    sliderContainer.scrollTo({
      left: elementLeft,
      behavior: "smooth",
    });
  }
  /**
   * 新建报告-快捷绑定活动
   */
  onSelectShortcut(item) {
    this.checkedShortcut = item.id;
  }
}
