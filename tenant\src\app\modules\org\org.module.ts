import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { OrgListComponent } from "./org-list/org-list.component";
import { RouterModule, Routes } from "@angular/router";
import { SharedModule } from "@src/shared";
import { OrgEditComponent } from "./org-edit/org-edit.component";
import { OrgChartsComponent } from "./org-charts/org-charts.component";

const routes: Routes = [{ path: "", component: OrgListComponent }];

@NgModule({
  declarations: [OrgListComponent, OrgEditComponent, OrgChartsComponent],
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
  exports: [RouterModule],
  entryComponents: [OrgEditComponent, OrgChartsComponent],
})
export class OrgModule {}
