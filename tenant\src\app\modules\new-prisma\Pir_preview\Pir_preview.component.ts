import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { NzMessageService, NzModalService } from "ng-zorro-antd";

import { SubjectEditComponent } from "../../new-activity/subject-edit/subject-edit.component";
import _ from "lodash";
import { SubjectAddComponent } from "../../new-activity/subject-add/subject-add.component";
import { SubjectBatchEditComponent } from "../../new-activity/subject-batch-edit/subject-batch-edit.component";
import { BookSettingComponent } from "../../new-activity/book-setting/book-setting.component";
import { ThrowStmt } from "@angular/compiler";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-preview",
  templateUrl: "./Pir_preview.component.html",
  styleUrls: ["./Pir_preview.component.less"],
})
export class PirpreviewComponent implements OnInit {
  sourceList: any[] = [];

  topicList: any[] = [];

  projectInfo: any;

  // 活动id
  projectId: string;
  //活动状态
  projectType: string;
  // 搜索关键字
  keyWord: string = "";
  // 分页控制
  totalCount: number = 3;
  currentPage: number = 1;
  pageSize: number = 2;

  // 语言控制
  lanIndex: number = 0;
  lanIndexuser: number = 0;
  lans: any[] = [
    { key: "zh_CN", value: "中文" },
    { key: "en_US", value: "ENG" },
  ];
  lan: string = "zh_CN";
  tabSize: string = "small";
  checked = true;

  constructor(
    private modalService: NzModalService,
    private routeInfo: ActivatedRoute,
    private msg: NzMessageService,
    private router: Router,
    private projSerivce: ProjectManageService,
    private surveySerivce: SurveyApiService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    this.projectId = this.routeInfo.snapshot.queryParams.projectId;
    this.projectType = this.routeInfo.snapshot.queryParams.projectType;

    this.getProject();
    this.loadData();
  }

  getProject() {
    this.surveySerivce.getProjectById(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.projectInfo = res.data;
      }
    });
  }

  loadData() {
    this.surveySerivce.getQuestionsByProjId(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        // 排序
        res.data.surveyQuestions.sort(function(a, b) {
          return a.sort - b.sort;
        });
        //  this.topicList = res.data.surveyQuestions;
        this.topicList = _.filter(res.data.surveyQuestions, function(q) {
          return q.type !== "PAGE_SPLIT";
        });

        this.sourceList = this.topicList;

        this.topicList.forEach((item) => {
          if (
            item.name.zh_CN !== item.replaceName.zh_CN ||
            item.name.en_US !== item.replaceName.en_US
          ) {
            item.canEdit = true;
          }
        });
        this.questionnaireId = this.topicList[0].questionnaireId;
      }
    });
  }

  search() {
    let content: string = this.keyWord;
    this.topicList = _.filter(this.sourceList, function(q) {
      let tmpCN: string = q.replaceName.zh_CN;
      let tmpEN: string = q.replaceName.en_US;
      return tmpCN.indexOf(content) >= 0 || tmpEN.indexOf(content) >= 0;
    });
  }

  canEdit() {
    return true;
  }

  mouseState(item, flag: boolean) {
    item.active = flag;
  }
  changeLanuser(): void {
    this.lanIndexuser;
  }
  changeLan(): void {
    let cur = this.lans[this.lanIndex];
    this.lan = cur.key;
  }

  questionnaireId: string;

  add(id: string) {
    const modal = this.modalService.create({
      // nzTitle: `${tmpTitle}组织`,
      nzTitle: null,
      nzFooter: null,
      nzWidth: 880,
      nzContent: SubjectAddComponent,
      nzComponentParams: {
        projectId: this.projectId,
        questionnaireId: this.questionnaireId,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child = modal.getContentComponent();
        let paramList = [];
        let selList: any[] = child.getSelectedQuestions();
        _.forEach(selList, function(value) {
          let id = value.id;
          paramList.push({ id });
        });

        this.surveySerivce.addQuestions(paramList).subscribe((res) => {
          if (res.result.code === 0) {
            this.msg.success("添加题目成功。");
            this.loadData();
            modal.close();
          }
        });

        return false;
      },
    });
  }

  edit(questionModel: any) {
    const modal = this.modalService.create({
      // nzTitle: `题目修订`,
      nzTitle: null,
      nzFooter: null,
      nzWidth: 800,
      nzContent: SubjectEditComponent,
      nzComponentParams: {
        subjectModel: questionModel,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child: SubjectEditComponent = modal.getContentComponent();
        let param = child.paramJson;
        this.surveySerivce.replaceQuestion(param).subscribe((res) => {
          if (res.result.code === 0) {
            // update local
            let tmp = _.find(this.topicList, { id: questionModel.id });
            tmp.name = param.surveyQuestion.name;
            this.msg.success("修订成功。");
            modal.close();
          }
        });
        return false;
      },
    });

    modal.afterClose.subscribe((result) => {
      this.loadData();
    });
  }

  batchEdit() {
    let tmpList = _.filter(this.topicList, function(q) {
      return q.canEdit;
    });

    const modal = this.modalService.create({
      nzTitle: null,
      nzFooter: null,
      nzWidth: 800,
      nzContent: SubjectBatchEditComponent,
      nzComponentParams: {
        modelList: tmpList,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child: SubjectBatchEditComponent = modal.getContentComponent();
        let param = child.editWidget.paramJson;
        this.surveySerivce.replaceQuestion(param).subscribe((res) => {
          if (res.result.code === 0) {
            this.msg.success("批量修订成功。");
            modal.close();
          }
        });
        return false;
      },
    });

    modal.afterClose.subscribe((result) => {
      this.loadData();
    });
  }

  delete(id: string) {
    this.surveySerivce.deleteQuestionById(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("删除成功");
        this.loadData();
      }
    });
  }

  // bookConfirm() {
  //   this.surveySerivce.confirmQuestionBook(this.projectId).subscribe(res => {
  //     if (res.result.code === 0) {
  //       this.router.navigate(
  //         ['/new-prisma'],
  //         { queryParams: {projectId:this.projectId,type:this.projectType} }
  //       );
  //     }
  //   });
  // }

  config() {
    const modal = this.modalService.create({
      nzTitle: `题本设置`,
      nzFooter: null,
      nzWidth: 500,
      nzContent: BookSettingComponent,
      nzComponentParams: {
        questionnaireId: this.questionnaireId,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzMaskClosable: false,
      nzOnOk: () => {
        let child: BookSettingComponent = modal.getContentComponent();
        if (child.defaultList.length === 0) {
          // this.msg.warning("没有选择填答语言。");
          this.customMsg.open("warning", "没有选择填答语言");
          return false;
        }
        if (!child.radioValue) {
          // this.msg.warning("没有选择默认语言。");
          this.customMsg.open("warning", "没有选择填答语言");
          return false;
        }
        return true;
      },
    });
  }
}
