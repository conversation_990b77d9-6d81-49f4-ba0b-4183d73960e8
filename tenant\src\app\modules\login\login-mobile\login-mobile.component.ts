import { Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ValidationErrors,
  Validators,
} from "@angular/forms";
import { from } from "rxjs";
import { LoginService } from "../login.service";
import { NzMessageService, NzModalService } from "ng-zorro-antd";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-login-mobile",
  templateUrl: "./login-mobile.component.html",
  styleUrls: ["./login-mobile.component.less"],
})
export class LoginMobileComponent implements OnInit {
  constructor(
    private fb: FormBuilder,
    private loginService: LoginService,
    private msg: NzMessageService,
    private modalService: NzModalService,
    private router: Router,
    private customMsg: MessageService
  ) {
    this.validateForm = this.fb.group({
      name: ["", [Validators.required]],
      email: ["", [Validators.email, Validators.required]],
      password: ["", [Validators.required]],
      mobile: ["", [Validators.required]],
      position: ["", [Validators.required]],
      productCode: ["SAG", [Validators.required]],
      realName: ["", [Validators.required]],
    });
  }
  validateForm: FormGroup;
  cardPasswords = [];
  errTipInfo = [];
  success = false;
  disabled = false;
  @ViewChild("tplContent", { static: true }) tplContent;
  ngOnInit() {}

  checkPassword(value, index) {
    this.loginService.checkoutCardPassword(value).subscribe((res) => {
      if (res.result.code === 0) {
        this.errTipInfo[index] = res.data;
        if (!res.data) {
          this.cardPasswords[index] = value;
        }
      }
    });
  }

  onAdd() {
    this.cardPasswords.push("");
    this.errTipInfo.push("");
  }

  onDelete(index) {
    this.cardPasswords.splice(index, 1);
    this.errTipInfo.splice(index, 1);
  }
  changePsd(index) {
    this.errTipInfo[index] = "";
  }

  onSubmit() {
    setTimeout(() => {
      for (const i in this.validateForm.controls) {
        this.validateForm.controls[i].markAsDirty();
        this.validateForm.controls[i].updateValueAndValidity();
      }

      if (this.validateForm.valid) {
        let commitJson = this.validateForm.value;
      }
      if (
        this.cardPasswords.length == 0 ||
        (this.cardPasswords.length == 1 && this.cardPasswords[0] == "")
      ) {
        // this.msg.error("请添加卡片密码");
        this.customMsg.open("error", "请添加卡片密码");
        return;
      }

      if (this.errTipInfo.find((el) => !!el)) {
        // this.msg.error("请修改卡片密码");
        this.customMsg.open("error", "请修改卡片密码");
        return;
      }

      let params: any = Object.assign(this.validateForm.value, {
        cardPasswords: this.cardPasswords,
      });

      this.loginService.createTenantRegister(params).subscribe((res) => {
        if (res.data.id) {
          this.success = true;
          this.disabled = true;
          this.router.navigateByUrl("/user/reg-success");
        } else {
          // this.msg.error("创建失败");
          this.customMsg.open("error", "创建失败");
        }
      });
    }, 1000);
  }
}
