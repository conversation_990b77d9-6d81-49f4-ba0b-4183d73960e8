/*
 *
 *  @Description: 指数分析
 *  @author: <PERSON>
 *  @Date: 2023/09/26
 *
 */
import { Component, OnInit, Input, OnDestroy } from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ValidationErrors,
  Validators,
} from "@angular/forms";
import { Observable, Observer } from "rxjs";
import {
  NzModalService,
  NzMessageService,
  NzTreeComponent,
  NzFormatEmitEvent,
  NzTreeNode,
  NzModalRef,
  UploadXHRArgs,
} from "ng-zorro-antd";
import { HttpClient } from "@angular/common/http";
import _ from "lodash";

import { NewPrismaService } from "../new-prisma.service";
import { NzDrawerRef } from "ng-zorro-antd/drawer";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { Router, NavigationEnd } from "@angular/router";
import { Subscription } from "rxjs";

@Component({
  selector: "app-index-analysis",
  templateUrl: "./index-analysis.component.html",
  styleUrls: ["./index-analysis.component.less"],
})
export class IndexAnalysisComponent implements OnInit, OnDestroy {
  @Input() projectId: string;
  // @Input() type: number; // 指数-0 系数-1
  validateForm: FormGroup;
  language: string = "zh_CN";
  selectedValue: 123;

  // 指数/维度管理
  showcard: boolean = false;
  groupHidden: boolean = true;
  namelist: any[] = [];
  oldlist: any[] = [];
  groupList: any[] = []; // 已选关联
  typelist = [
    {
      value: "INDEX",
      label: "指数",
    },
    {
      value: "ONE_RANK",
      label: "一级维度",
    },
    {
      value: "TWO_RANK",
      label: "二级维度",
    },
    {
      value: "THREE_RANK",
      label: "三级维度",
    },
  ];
  // 指数维度数值
  provinceDatacus: any[] = [];
  provinceDataone: any[] = [];
  provinceDatatwo: any[] = [];
  provinceDatathree: any[] = [];
  //  指数维度option
  selectedProvincecus = null;
  selectedProvinceone = null;
  selectedProvincetwo = null;
  selectedProvincethree = null;
  selectedProvince: any = null;
  valuewords: any = null;

  // mark 相关
  catList = [];
  // catOtherList = [];

  catMap = {};
  // catOtherMap = {};

  dimQuesList: any[] = [];
  oldPrismaReportData = [];
  twoDash: string = "__";

  updateId: string;
  tenantUrl: string = "/tenant-api";

  // 导出loading
  isDownLoadSpinning: boolean = false;
  private routerSubscription: Subscription;

  constructor(
    private fb: FormBuilder,
    private api: NewPrismaService,
    private nzModalService: NzModalService,
    private msgServ: NzMessageService,
    private http: HttpClient,
    private drawerRef: NzDrawerRef,
    private customMsg: MessageService,
    private router: Router
  ) {
    this.validateForm = this.fb.group({
      userName: ["", Validators.required],
    });
  }
  submitForm(form: any) {}
  // 指数/维度管理
  getTips() {
    this.showcard = true;
    this.getcustomlist();
  }
  getcustomlist() {
    this.api.dimensionListByProjectId(this.projectId).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.namelist = res.data;
        this.namelist.forEach((item) => {
          item.checked = true;
        });
        this.oldlist = _.cloneDeep(res.data);

        this.provinceDatacus = this.namelist.filter((item) => {
          return item.type == "INDEX";
        });
        this.provinceDataone = this.namelist.filter((item) => {
          return item.type == "ONE_RANK";
        });
        this.provinceDatatwo = this.namelist.filter((item) => {
          return item.type == "TWO_RANK";
        });
        this.provinceDatathree = this.namelist.filter((item) => {
          return item.type == "THREE_RANK";
        });
      }
    });
  }
  test(e) {
    if (this.valuewords === null) return;
    this.namelist.forEach((item) => {
      if (item.name.zh_CN.indexOf(this.valuewords) > -1) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
    this.namelist.sort(function(a, b) {
      return b.checked - a.checked;
    });
  }
  addlist() {
    this.namelist.unshift({
      type: null,
      name: {
        en_US: "",
        zh_CN: "",
      },
      id: null,
      checked: true,
    });
    this.namelist.sort(function(a, b) {
      return b.checked - a.checked;
    });
  }
  cannelcard() {
    this.showcard = false;
    this.valuewords = "";
  }
  commitcard() {
    let list = this.namelist.every((item) => {
      return item.type;
    });

    if (list) {
      let param = {
        list: this.namelist,
        projectId: this.projectId,
      };
      this.api.batchCreateOrUpdate(param).subscribe((res: any) => {
        if (res.result.code == 0) {
          this.showcard = false;
          this.valuewords = "";
          this.getcustomlist();
        }
      });
    } else {
      // this.msgServ.warning("指数类别必选！");
      this.customMsg.open("warning", "指数类别必选");
      return;
    }
  }

  deleteTips(e, id, i) {
    e.stopPropagation();
    this.namelist.splice(i, 1);
    this.namelist.sort(function(a, b) {
      return b.checked - a.checked;
    });
  }

  // 指数/维度管理 End

  getQuestionDimensionList() {
    // 获取题本
    this.api
      .listQuestionDimensionByProjectId(this.projectId)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.dimQuesList = res.data;
          this.intData();
        }
      });
  }

  getOnceDimension() {}

  loadData() {
    this.getQuestionDimensionList();
    this.getcustomlist();
    this.bgGetGroupList();
  }

  closeModal() {
    // this.nzModalService.closeAll()
    this.drawerRef.close();
  }

  ngOnInit() {
    this.loadData();
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  /**
   * 题本相关操作 - copy mark
   */

  clearGroup() {
    this.bgEmptyGroup().subscribe((res: any) => {
      if (res.result.code === 0) {
        this.msgServ.success("清空所有组合成功");
        this.updateId = undefined;
        this.bgGetGroupList();
      }
    });
  }

  // 清除选项
  clearOption(clearName?: boolean) {
    if (clearName) {
      this.selectedProvincecus = null;
      this.selectedProvinceone = null;
      this.selectedProvincetwo = null;
      this.selectedProvincethree = null;
      this.selectedProvince = null;
    }
    this.updateId = undefined;
    this.validateForm.reset();
    // clear select
    for (let index = 0; index < this.catList.length; index++) {
      const cat = this.catList[index];
      cat.searchText = "";
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        item.checked = false;
        item.isShow = cat.type === 1;
      }
      cat.allChecked = false;
      cat.indeterminate = false;
    }
  }

  ok() {
    this.closeModal();
  }

  intData() {
    // 大维度
    let tmp = [];
    let list: any[] = this.getSelection1();
    let obj = {
      id: "dimension",
      name: "大维度",
      allChecked: false,
      indeterminate: false,
      items: list[0],
      type: 1,
      checkedValues: [],
      searchText: "",
    };
    this.catList.push(obj);
    this.catMap[obj.type] = obj;

    // 子维度
    tmp = [];
    obj = {
      id: "subDimension",
      name: "子维度",
      allChecked: false,
      indeterminate: false,
      items: list[1],
      type: 2,
      checkedValues: [],
      searchText: "",
    };
    this.catList.push(obj);
    this.catMap[obj.type] = obj;

    // 题本
    tmp = [];
    obj = {
      id: "question",
      name: "题本",
      allChecked: false,
      indeterminate: false,
      items: list[2],
      type: 3,
      checkedValues: [],
      searchText: "",
    };
    this.catList.push(obj);
    this.catMap[obj.type] = obj;
  }

  getSelection1(): any[] {
    let tmp1: any[] = [];
    let tmp2: any[] = [];
    let tmp3: any[] = [];

    for (let index = 0; index < this.dimQuesList.length; index++) {
      const element = this.dimQuesList[index];
      let val = element.type;
      tmp1.push({
        label: element.name.zh_CN,
        value: val,
        checked: false,
        isShow: true,
      });
      this.getSelection2(element.child, tmp2, tmp3, val);
    }
    return [tmp1, tmp2, tmp3];
  }

  getSelection2(
    dataList: any[],
    itemList: any[],
    subItemList: any[],
    parentId: string
  ) {
    for (let index = 0; index < dataList.length; index++) {
      const element = dataList[index];
      let val = element.type + this.twoDash + element.dimensionCode;
      itemList.push({
        label: element.name.zh_CN,
        value: val,
        checked: false,
        isShow: false,
        pId: parentId,
      });
      this.getSelection3(
        element.child,
        subItemList,
        val,
        element.dimensionCode
      );
    }
  }

  getSelection3(
    dataList: any[],
    itemList: any[],
    parentId: string,
    dimCode: string
  ) {
    for (let index = 0; index < dataList.length; index++) {
      const element = dataList[index];
      let val =
        element.type +
        this.twoDash +
        dimCode +
        this.twoDash +
        element.questionId;
      itemList.push({
        label: this.removeTags(element.name.zh_CN) || element.name.zh_CN,
        value: val,
        checked: false,
        isShow: false,
        pId: parentId,
      });
    }
  }

  updateAllChecked(tabData, e) {
    tabData.indeterminate = false;
    tabData.items.forEach((item) => {
      if (item.isShow) {
        item.checked = e;
      }
    });

    this.changeCheckModel(tabData);
  }

  showGroup() {
    this.groupHidden = !this.groupHidden;
  }

  delete(e, id) {
    e.stopPropagation();
    this.bgDelete(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msgServ.success("删除成功");
        if (id === this.updateId) {
          this.updateId = undefined;
        }
        this.bgGetGroupList();
      }
    });
  }

  edit(e, groupData: any) {
    e.stopPropagation();
    this.clearOption(true);
    let arr1: string[] = [];
    let arr2: string[] = [];
    let arr3: string[] = [];

    let selList = groupData.selectedList;
    for (let index = 0; index < selList.length; index++) {
      const selItem = selList[index];
      let type: string = selItem.type;
      let dimensionCode: string = selItem.dimensionCode;
      let questionId: string = selItem.questionId;
      let val: string = "";
      if (questionId && dimensionCode && type) {
        val = type + this.twoDash + dimensionCode + this.twoDash + questionId;
        arr3.push(val);
      } else if (dimensionCode && type) {
        val = type + this.twoDash + dimensionCode;
        arr2.push(val);
      } else {
        val = type;
        arr1.push(val);
      }
    }

    this.restoreCheckState(1, arr1, []);
    this.restoreCheckState(2, arr2, arr1);
    this.restoreCheckState(3, arr3, arr2);
    this.showGroup();

    this.selectedProvincecus = groupData.indexId;
    this.validateForm = this.fb.group({
      userName: [groupData.indexId, Validators.required],
    });
    this.selectedProvinceone = groupData.parentDimensionId;
    this.selectedProvincetwo = groupData.dimensionId;
    this.selectedProvincethree = groupData.threeDimensionId;
    let selectedProvince = [];
    groupData.demographicContents.forEach((item) => {
      selectedProvince.push(...item.demographicIds);
    });
    this.selectedProvince = selectedProvince;
    this.updateId = groupData.id;
  }

  restoreCheckState(
    type: number,
    checkedValues: string[],
    checkParentValues: string[]
  ) {
    let items: any[];
    items = this.catMap[type].items;
    if (items && items.length > 0) {
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        // set show state
        if (type === 1 || _.includes(checkParentValues, item.pId)) {
          item.isShow = true;
        } else {
          item.isShow = false;
        }
        // set check state
        item.checked = _.includes(checkedValues, item.value);
      }
    }
  }

  updateSingleChecked(tabData, itemData, e) {
    if (tabData.items.every((item) => !item.checked)) {
      tabData.allChecked = false;
      tabData.indeterminate = false;
    } else if (tabData.items.every((item) => item.checked)) {
      tabData.allChecked = true;
      tabData.indeterminate = false;
    } else {
      tabData.indeterminate = true;
    }

    this.changeCheckModel(tabData);
  }

  changeCheckModel(tabData) {
    let type = tabData.type;
    if (type === 3) {
      return;
    }

    let checkArr: string[] = [];
    let items: any[] = tabData.items;
    for (let index = 0; index < items.length; index++) {
      const element = items[index];
      if (element.checked && element.isShow) {
        checkArr.push(element.value);
      }
    }
    // 下一级是否显示
    let children: any[];
    children = this.catMap[type + 1].items;

    if (children && children.length > 0) {
      for (let j = 0; j < children.length; j++) {
        const ch = children[j];
        if (_.includes(checkArr, ch.pId)) {
          ch.isShow = true;
        } else {
          ch.isShow = false;
        }
      }
    }

    // 如果是大维度，检查问题是否显示
    if (type === 1) {
      this.checkQuestion();
    }
  }

  checkQuestion() {
    // 子维度选择
    let checkArr: string[] = [];
    let items: any[];
    items = this.catMap[2].items;

    for (let index = 0; index < items.length; index++) {
      const element = items[index];
      if (element.checked && element.isShow) {
        checkArr.push(element.value);
      }
    }

    // 问题是否显示
    let children: any[];
    children = this.catMap[3].items;

    if (children && children.length > 0) {
      for (let j = 0; j < children.length; j++) {
        const ch = children[j];
        if (_.includes(checkArr, ch.pId)) {
          ch.isShow = true;
        } else {
          ch.isShow = false;
        }
      }
    }
  }
  // 关联
  makeGroup() {
    if (!this.selectedProvincecus) {
      // this.msgServ.error("指数必选");
      this.customMsg.open("error", "指数必选");
      return;
    }

    let tmpList: any[] = [];

    for (let index = 0; index < this.catList.length; index++) {
      let cat: any = this.catList[index];
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const it = items[j];
        if (it.isShow && it.checked) {
          tmpList.push(this.getSingleParam(it.value));
        }
      }
    }

    if (tmpList.length == 0) {
      // this.msgServ.error("组合维度必选");
      this.customMsg.open("error", "组合维度必选");
      return;
    }

    let demographicContents = this.oldPrismaReportData.filter((item) => {
      item.children = item.children.filter((res) => {
        return res.checked;
      });
      return item.checked;
    });
    let newdemographicContents = [];
    demographicContents.forEach((item) => {
      item.demographicIds = [];
      item.children.forEach((val) => {
        item.demographicIds.push(val.id);
      });
      newdemographicContents.push({
        demographicRootId: item.id,
        demographicIds: item.demographicIds,
      });
    });

    let param: any = {
      projectId: this.projectId,
      indexId: this.selectedProvincecus,
      detailList: tmpList,

      //可选可不选
      demographicContents: newdemographicContents,
      parentDimensionId: this.selectedProvinceone,
      dimensionId: this.selectedProvincetwo,
      threeDimensionId: this.selectedProvincethree,
    };

    let sub = null;
    if (this.updateId) {
      param.id = this.updateId;
      sub = this.bgUpdate(param);
    } else {
      sub = this.bgCreate(param);
    }

    sub.subscribe((res: any) => {
      if (res.result.code === 0) {
        this.msgServ.success("保存成功");
        this.clearOption(true);
        this.bgGetGroupList();
      }
    });
  }
  getSingleParam(str: string): any {
    let arr: string[] = _.split(str, this.twoDash);
    let tmp: any = {};
    if (arr.length > 0) {
      tmp.type = arr[0];
    }
    if (arr.length > 1) {
      tmp.dimensionCode = arr[1];
    }
    if (arr.length > 2) {
      tmp.questionId = arr[2];
    }
    return tmp;
  }

  bgGetGroupList() {
    this.api.listByProjectIdGroup(this.projectId).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.groupList = res.data;
        for (let index = 0; index < this.groupList.length; index++) {
          const element = this.groupList[index];
          element.active = true;
          element.factor = "";
        }
        this.groupList.forEach((item) => {
          item.groupname = [];
          item.demographics.forEach((val) => {
            val.factorname = "";
            val.children.forEach((res, index) => {
              val.factorname +=
                res.name.zh_CN + (index == val.children.length - 1 ? "" : "、");
            });
            val.factor = val.name.zh_CN + "：" + val.factorname;
            item.factor += val.factor;
          });
          if (item.indexName && item.indexName.zh_CN) {
            item.indextype = item.indexName.zh_CN;
          } else {
            item.indextype = "";
          }
          if (item.parentDimensionName && item.parentDimensionName.zh_CN) {
            item.parenttype = item.parentDimensionName.zh_CN;
          } else {
            item.parenttype = "";
          }
          if (item.dimensionName && item.dimensionName.zh_CN) {
            item.dimensiontype = item.dimensionName.zh_CN;
          } else {
            item.dimensiontype = "";
          }
          if (item.threeDimensionName && item.threeDimensionName.zh_CN) {
            item.childDimensiontype = item.threeDimensionName.zh_CN;
          } else {
            item.childDimensiontype = "";
          }
          item.groupname.push(
            item.indextype,
            item.parenttype,
            item.dimensiontype,
            item.childDimensiontype,
            item.factor
          );
          item.groupnames = item.groupname
            .filter((item) => {
              return item;
            })
            .join("/");
        });
      }
    });
  }

  bgCreate(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/combination/create`;
    return this.http.post(api, param);
  }

  bgUpdate(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/combination/update`;
    return this.http.post(api, param);
  }

  bgDelete(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/combination/delete/${id}`;
    return this.http.post(api, {});
  }

  bgEmptyGroup(): Observable<any> {
    let api = `${this.tenantUrl}/survey/prisma/custom/index/combination/deleteAllByProjectId/${this.projectId}`;
    return this.http.post(api, {});
  }

  /**
   * preview 预览上传的文件
   * @param file
   */
  preview(file) {
    window.open(window.URL.createObjectURL(file.originFileObj));
  }
  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let params = {
      fileType: "." + item.file.name.split(".")[1],
    };
    this.uploadExcel(formData, params, item);
  };
  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, params, item) {
    // return this.api.readLanguageConfigExcel(formData, this.active).subscribe(
    //   (res) => {
    //     if (res.result.code === 0) {
    //       item.onSuccess!();
    //       this.languageConfig = res.data;
    //       this.saveLan(null, '导入成功！');
    //     }
    //   },
    //   err => {
    //     item.onError!(err, item.file!);
    //   }
    // )
  }

  // 导出
  downLoad() {
    // this.isDownLoadSpinning = true
    // this.api.exportLanguageConfigExcel(this.active).subscribe(res => {
    //   const blob = new Blob([res.body], { type: 'application/vnd.ms-excel' });
    //   let fileName = res.headers.get('Content-Disposition').split(';')[1].split('filename=')[1];
    //   const fileNameUnicode = res.headers.get('Content-Disposition').split('filename*=')[1];
    //   // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    //   if (fileName) {
    //       fileName = decodeURIComponent(fileName);
    //   }
    //   if (fileNameUnicode) {
    //       fileName = decodeURIComponent(fileNameUnicode.split('\'\'')[1]);
    //   }
    //   const link = document.createElement('a');
    //   link.setAttribute('href', URL.createObjectURL(blob));
    //   link.setAttribute('download', fileName);
    //   link.style.visibility = 'hidden';
    //   document.body.appendChild(link);
    //   link.click();
    //   document.body.removeChild(link);
    //   this.isDownLoadSpinning = false;
    // })
  }

  // 匹配并替换特殊符号
  replaceHtmlEntities = (text) => {
    const entities = {
      "&amp;": "&",
      "&lt;": "<",
      "&gt;": ">",
      "&quot;": '"',
      "&apos;": "'",
      "&#39;": "'",
      "&nbsp;": " ",
      "&ldquo;": "“",
      "&rdquo;": "”",
    };

    for (const entity in entities) {
      if (entities.hasOwnProperty(entity)) {
        const regex = new RegExp(entity, "g");
        text = text.replace(regex, entities[entity]);
      }
    }

    return text;
  };
  // 使用正则表达式匹配并替换标签
  removeTags = (htmlString) => {
    const result = htmlString.replace(/<\/?[^>]+(>|$)/g, " ");
    return this.replaceHtmlEntities(result.trim()); // 去除首尾空格
  };
}
