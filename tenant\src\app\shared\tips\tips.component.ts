import {Component, OnInit, Input, Output, EventEmitter} from '@angular/core';
import {OutletContext} from '@angular/router';

@Component({
  selector: 'app-tips',
  templateUrl: './tips.component.html',
  styleUrls: ['./tips.component.less']
})
export class TipsComponent implements OnInit {
  isVisible = true;
  @Input() title: any;
  @Input() content: any;
  @Input() visible: boolean;
  @Input() cancelTxt: null | '取消';
  @Input() confirmTxt: null | '确定';
  @Output('checked') checkedBack = new EventEmitter<any>();
  id: any = 'now';

  constructor() {
  }

  ngOnInit(): void {
  }

  showModal(): void {
    this.visible = true;
  }

  handleOk(): void {
    this.visible = false;
    this.checkedCallback();
  }

  handleCancel(): void {
    this.visible = false;
    this.checkedBack.emit('cancel');
  }
  checkedCallback() {
    this.checkedBack.emit(this.id);
  }
}
