<div>
  <header class="text-left">
    <button class="add-btn" (click)="addRole()">
      <i class="iconfont icon-plus-circle"></i> 添加角色
    </button>
  </header>
  <nz-table
    [nzData]="rolelist"
    [nzFrontPagination]="false"
    [nzShowPagination]="false"
    [nzScroll]="{ y: 'calc(100vh - 216px)' }"
  >
    <thead>
      <tr>
        <th nzWidth="330px">角色名称</th>
        <th nzWidth="330px">对被评估人的称呼</th>
        <th nzWidth="80px" *ngIf="isCustomRoleWeight == 'false'">角色比重</th>
        <th nzWidth="50px" nzAlign="center">操作</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of rolelist; let i = index">
        <td nzWidth="330px">
          <div style="width: 100%;">
            <ng-container  *ngIf="this.i18nData.length" >
              <app-i18n-input
              [value]="item.name"
              (changeValue)="changeNameValue($event, i, 'name')"
              [i18nData]="getLanOptionsValue(item.name)"
            ></app-i18n-input>
            <!-- [i18nData]="getLanOptionsValue(item.name)" -->
            </ng-container>
          </div>
        </td>
        <td nzWidth="330px">
          <div style="width: 100%;">
            <ng-container *ngIf="this.i18nData.length">
              <app-i18n-input
              [value]="item.investigatorTitle"
              (changeValue)="changeNameValue($event, i, 'investigatorTitle')"
              [i18nData]="getLanOptionsValue(item.investigatorTitle)"
            ></app-i18n-input>
            <!-- [i18nData]="getLanOptionsValue(item.investigatorTitle)" -->
            </ng-container>
          </div>
        </td>
        <td nzWidth="80px" *ngIf="isCustomRoleWeight == 'false'">
          <nz-input-number
            [(ngModel)]="item.percentage"
            [nzPrecision]="precision"
            nzPlaceHolder="toFixed"
            [nzMin]="0"
            [nzMax]="100"
            [nzStep]="1"
            style="width: 100%;"
            [nzDisabled]="Projectstatus == 'checked'"
          ></nz-input-number>
        </td>
        <td nzWidth="50px" nzAlign="center">
          <ng-container *ngIf="item.type === 'DEFAULT'; else block">
            <a style="color: #262626;" class="link-del">-</a>
          </ng-container>
          <ng-template #block>
            <a class="link-del" (click)="deleteRole(i)">删除</a>
          </ng-template>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div class="footer">
    <button nz-button nzType="primary" (click)="submit()">确认</button>
  </div>
</div>
