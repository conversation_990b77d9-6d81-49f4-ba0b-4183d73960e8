<div>
    <header class="text-right">
        <button *ngIf="showtype !='small'" class="add-btn" (click)="addRole()">添加角色</button>
    </header>
    <div>
        <div class="flex">
            <div class="flex-1 title">角色名称</div>
            <div class="flex-1 title">角色名称(英)</div>
            <div class="flex-1 title" *ngIf="showtype !='small'">对被评估人的称呼</div>
            <div class="flex-1 title" *ngIf="showtype !='small'">对被评估人的称呼(英)</div>
            <div class="flex-1 title" *ngIf="showtype =='small' || isCustomRoleWeight == 'false'">角色比重</div>
        </div>
        <div *ngFor="let item of rolelist; let i = index">
            <div class="flex item" *ngIf="!standardType || item.type != 'DEFAULT'">
                <input nz-input type="text" [(ngModel)]="item.name.zh_CN" [disabled]="showtype =='small'" class="flex-1 mr20" placeholder="请输入">
                <input nz-input type="text" [(ngModel)]="item.name.en_US" [disabled]="showtype =='small'" class="flex-1 mr20" placeholder="请输入">

                <input nz-input type="text" *ngIf="showtype !='small'" [(ngModel)]="item.investigatorTitle.zh_CN" class="flex-1 mr20" placeholder="请输入">
                <input nz-input type="text" *ngIf="showtype !='small'" [(ngModel)]="item.investigatorTitle.en_US" class="flex-1 mr20" placeholder="请输入">

                <nz-input-number *ngIf="showtype =='small' || isCustomRoleWeight == 'false'" class="flex-1 " [(ngModel)]="item.percentage" [nzPrecision]="precision" nzPlaceHolder="toFixed" [nzMin]="0" [nzMax]="100" [nzStep]="1" [nzDisabled]="Projectstatus == 'checked'"></nz-input-number>
                <ng-container *ngIf="item.type === 'DEFAULT'; else block">
                    <a class="link-del">&nbsp;&nbsp;</a>
                </ng-container>
                <ng-template #block>
                    <a class="link-del" *ngIf="showtype !='small'" (click)="deleteRole(i)">删除</a>
                    <a class="link-del" *ngIf="showtype =='small'"></a>
                </ng-template>
            </div>
        </div>
    </div>
</div>