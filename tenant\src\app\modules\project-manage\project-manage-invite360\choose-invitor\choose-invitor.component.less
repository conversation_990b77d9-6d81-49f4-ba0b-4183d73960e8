.con {
    display: flex;
    align-items:flex-start;
    justify-content: space-around;

    .tb {
        width: 345px;
        height: 460px;
    }

    .middle {
        width: 30px;
        height: 200px;
        padding: 50px 0;
        align-self: center;
        // background-color: aqua;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
    }
}

.ell {
    max-width: 290px;
    overflow: hidden;
    padding: 8px 0;
    text-overflow: ellipsis;
    white-space: nowrap;
}

:host ::ng-deep {
    .ant-table-content {
        height: 380px;
    }
}