<div class="batchSendBox">
  <div class="container content client-width">
    <div class="headContent">
      <h1>批量发送</h1>
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </div>
    <div class="main">
      <div class="main_L">
        <div class="radio_type">
          <h5>发送对象</h5>
          <div class="invite-user">
            <nz-radio-group [(ngModel)]="formData.isSelf">
              <!-- <label nz-radio [nzDisabled]="type!=='individual'" [nzValue]="true">本人</label> -->
              <label nz-radio [nzValue]="false">指定</label>
            </nz-radio-group>
          </div>
        </div>
        <div class="send_contet">
          <div class="title">
            <h5>发送内容</h5>
            <div *ngIf="!formData.isSelf">
              <nz-upload
                [nzCustomRequest]="customReq"
                [nzDisabled]="isUploading"
                [nzShowUploadList]="false"
              >
                <button nz-button [nzLoading]="isUploading" nzType="primary">
                  导入
                </button>
              </nz-upload>
              <button
                nz-button
                (click)="exportR()"
                [nzLoading]="isLoading"
                nzType="primary"
              >
                导出
              </button>
            </div>
          </div>
          <ul class="content" id="scrollContent">
            <li *ngFor="let item of list">
              【{{ item.isGroup ? "团队报告" : "个人报告" }}】{{
                item.projectName
              }}-{{ item.name }}-{{ item.productType }}，{{
                formData.isSelf ? "本人" : "指定"
              }}
            </li>
            <li
              class="example"
              *ngIf="!isSpinning"
              style="text-align: center; padding-top: 10px;"
            >
              <nz-spin nzSimple> </nz-spin>
            </li>
          </ul>
          <p style="color: #E1251B;margin-top: 20px;">
            0.05K米/1M/封
          </p>
        </div>
      </div>
      <div class="main_R">
        <div class="email_subject">
          <h5>邮件主题</h5>
          <div class="input-user">
            <input
              class="title_email"
              nz-input
              name="title"
              placeholder="邮件主题"
              [(ngModel)]="formData.subject"
            />
          </div>
        </div>
        <div class="email_text">
          <h5>邮件正文</h5>
          <div>
            <tinymce
              [config]="tinyconfig"
              id="formula-textarea"
              [(ngModel)]="formData.content"
              delay="100"
            >
            </tinymce>
          </div>
          <!-- 因为暂时没有 上传附件的需要 暂时隐藏掉 -->
          <!-- <nz-upload [nzPreview]="preview" [nzCustomRequest]="uploadAttachment" [nzBeforeUpload]="beforeUpload"
            (nzChange)="fileChange($event)" [nzFileList]="fileListAttachment">
            <div class="upload">上传附件 <span>(支持的文件类型：txt,.docx.doc,xlsx,xls,.html)</span></div>
          </nz-upload> -->
        </div>
      </div>
    </div>
  </div>
  <footer class="invite-footer">
    <div class="container client-width">
      <button
        nz-button
        nzType="primary"
        (click)="send()"
        [nzLoading]="isLoadingOne"
        nzShape="round"
      >
        发送
      </button>
    </div>
  </footer>
</div>
