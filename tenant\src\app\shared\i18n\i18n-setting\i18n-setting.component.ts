/**
 *
 *  @author: <PERSON>
 *  @Date: 2023/09/18
 *  @content: 调研-高级设置-语言设置组件
 *
 */
import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  OnChanges,
} from "@angular/core";
import { NewPrismaService } from "@src/modules/new-prisma/new-prisma.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import _ from "lodash";
import { NzMessageService, UploadXHRArgs } from "ng-zorro-antd";
@Component({
  selector: "app-i18n-setting",
  templateUrl: "./i18n-setting.component.html",
  styleUrls: ["./i18n-setting.component.less"],
})
export class I18nSettingComponent implements OnInit, OnChanges {
  @Input() selectOptions: any[];
  @Input() visible: boolean;
  @Input() projectId: string;
  @Output() onClose = new EventEmitter<any>();
  @Output() onSave = new EventEmitter<any>();

  active = "zh_CN";
  activeNames = "中文";
  languageSimplifyName = "";

  languageConfig = [];
  languageConfig_CN = [];
  languageTitleMap = {};

  disabled: boolean = true;
  isDownLoadSpinning: boolean = false;

  isSpinning: boolean = false;
  // 默认的简写 中文 - 中， 英文 - EN， 日文 - 日，韩文 - 한
  defaultAbbr = {
    zh_CN: "中",
    en_US: "EN",
    jp: "日",
    ko: "한",
  };

  constructor(
    private api: NewPrismaService,
    private message: NzMessageService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {}

  ngOnChanges() {
    if (this.selectOptions.length > 0) {
      this.getLanguageConfigFun(this.active, true);
    }
  }

  /**
   * handClose 清空
   */
  handClose() {
    this.disabled = true;
    this.active = "zh_CN";
    this.activeNames = "中文";
    this.onClose.emit();
  }

  /**
   * handTab 语言切换
   */
  handTab(e) {
    this.active = e.value;
    this.activeNames = e.name;
    this.getLanguageConfigFun(e.value, false);
  }

  /**
   * getLanguageConfigFun 获取所选语言的配置信息
   * @param {string} code 语言code
   * @param {boolean} isFirst 是否首次
   */
  getLanguageConfigFun(code: string, isFirst: boolean) {
    this.isSpinning = true;
    this.api.getLanguageConfig(code).subscribe((res) => {
      if (res.result.code === 0) {
        // 首次加载需copy一份中文，作为显示文本以及配置模板
        if (isFirst) {
          const {
            languageCode,
            languageConfigs,
            languageSimplifyName,
          } = res.data;
          this.languageConfig = languageConfigs.sort((a, b) =>
            a.key.localeCompare(b.key)
          );
          const abbr = this.defaultAbbr[languageCode] || ""; // 简写
          this.languageSimplifyName = languageSimplifyName || abbr;
          this.languageConfig_CN = _.cloneDeep(languageConfigs);
          const initTitleMap = {};
          this.languageConfig_CN.forEach((element) => {
            initTitleMap[element.key] = element.content;
          });
          this.languageTitleMap = initTitleMap;
        } else {
          const {
            languageCode,
            languageConfigs,
            languageSimplifyName,
          } = res.data;

          const abbr = this.defaultAbbr[languageCode] || ""; // 简写
          this.languageSimplifyName = languageSimplifyName || abbr;
          if (languageConfigs.length > 0) {
            this.languageConfig = languageConfigs.sort((a, b) =>
              a.key.localeCompare(b.key)
            );
          } else {
            const initConfig = [];
            this.languageConfig_CN.forEach((element) => {
              initConfig.push({
                ...element,
                content: "",
                languageCode: code,
              });
            });
            this.languageConfig = initConfig;
          }
        }
      }
      this.isSpinning = false;
    });
  }

  /**
   * clear 清空
   */
  clear() {
    const initConfig = [];
    this.languageConfig.forEach((element) => {
      initConfig.push({
        ...element,
        content: "",
      });
    });
    this.languageConfig = initConfig;
  }

  /**
   * saveLan 保存语言
   * @param {any} nextLan 保存成功后要切换语言
   * @param {string} msg 操作成功的提示
   */
  saveLan(nextLan?, msg = "保存成功！") {
    // 中英文必填校验
    this.isSpinning = true;
    // if (this.active === 'zh_CN' || this.active === 'en_US'){
    if (!this.languageSimplifyName.trim()) {
      // this.message.error("请输入简称");
      this.customMsg.open("error", "请输入简称");
      this.isSpinning = false;
      return;
    }
    if (this.languageSimplifyName.trim().length > 2) {
      // this.message.error("简称仅2个字符");
      this.customMsg.open("error", "简称仅2个字符");
      this.isSpinning = false;
      return;
    }
    for (let index = 0; index < this.languageConfig.length; index++) {
      const element = this.languageConfig[index];
      if (!element.content) {
        // this.message.error(
        //   `${index + 1}. ${this.languageConfig_CN[index].content}不能为空`
        // );
        this.customMsg.open(
          "error",
          `${index + 1}. ${this.languageConfig_CN[index].content}不能为空`
        );
        this.isSpinning = false;
        return;
      }
    }
    // }
    const params = {
      languageCode: this.active,
      languageName: this.activeNames,
      languageConfigs: this.languageConfig,
      languageSimplifyName: this.languageSimplifyName,
    };
    // todo: 还需要加一个是否调整过得前端校验
    this.api.saveLanguageConfig(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.disabled = true;
        this.message.success(msg);
        this.onSave.emit({ name: this.activeNames, value: this.active });
        // 如果是更新中文，更新当前缓存
        if (params.languageCode === "zh_CN") {
          this.languageConfig_CN = _.cloneDeep(
            params.languageConfigs.sort((a, b) => a.key.localeCompare(b.key))
          );
          const initTitleMap = {};
          this.languageConfig_CN.forEach((element) => {
            initTitleMap[element.key] = element.content;
          });
          this.languageTitleMap = initTitleMap;
        }
        if (nextLan) {
          this.handTab(nextLan);
        } else {
          this.getLanguageConfigFun(this.active, false);
        }
      }
      this.isSpinning = false;
    });
  }

  /**
   * cancelLan 不保存语言切换
   */
  cancelLan(e) {
    this.disabled = true;
    this.handTab(e);
  }

  /**
   * preview 预览上传的文件
   * @param file
   */
  preview(file) {
    window.open(window.URL.createObjectURL(file.originFileObj));
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let params = {
      fileType: "." + item.file.name.split(".")[1],
    };
    this.uploadExcel(formData, params, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, params, item) {
    return this.api.readLanguageConfigExcel(formData, this.active).subscribe(
      (res) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.languageConfig = res.data;
          this.saveLan(null, "导入成功！");
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  /**
   * downLoad 导出
   */
  downLoad() {
    this.isDownLoadSpinning = true;
    this.api.exportLanguageConfigExcel(this.active,this.projectId).subscribe((res) => {
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.isDownLoadSpinning = false;
    });
  }
}
