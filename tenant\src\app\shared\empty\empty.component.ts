
 /**
  *
  *  @author: <PERSON>
  *  @Date: 2023/09/20
  *  @content: 无状态组件
  *
 */
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-empty',
  templateUrl: './empty.component.html',
  styleUrls: ['./empty.component.less']
})
export class EmptyComponent implements OnInit {

  /*
   *@text：文本
   *@btnText：按钮文本
   *
   *@btnClick：点击按钮
   *
  */
  @Input() text: string= '暂无数据';
  @Input() btnText?: string = '';
  @Input() imgUrl?: string = 'assets/images/org/group_empty.png';
  
  
  @Output() btnClick = new EventEmitter<any>();

  constructor() { }
  
  ngOnInit() {}

  handBtn() {
    this.btnClick.emit();
  }

}
