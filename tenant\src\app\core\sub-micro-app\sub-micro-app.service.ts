import runtime from '@micro-zoe/micro-app';
import { microApp, NavigateOptions, MicroApp } from "@knx/micro-app";
import {
  VUE_MICRO_APP_NAME,
  VUE_MICRO_APP_BASE_EVENTS,
  VUE_MICRO_APP_BASEROUTE,
} from "./sub-micro-app.constant";
import { Router } from '@angular/router';
import { Injectable } from '@angular/core';

@Injectable()
export class MicroAppService extends MicroApp {
  constructor(private router: Router) {
    super()
  }

  /**
   * 独立运行时，直接控制子应用跳转，作为子应用运行时，通知基座跳转
   */
  navigateByUrl(path: string, options: NavigateOptions = {}): void {
    return microApp.runIfInMicroApp(
      () => super.navigateByUrl(path, options),
      () => {
        const { appName, ...restOptions } = options;
        if (appName === VUE_MICRO_APP_NAME) {
          if (this.isActivedApp(appName)) {
            VUE_MICRO_APP_BASE_EVENTS.emit({
              type: "routeChange",
              timestamp: Date.now(),
              data: path,
              options: restOptions,
            });
          } else {
            this.router.navigate([`/${VUE_MICRO_APP_BASEROUTE}/${path.replace(/^\//, '')}`], {
              queryParams: restOptions.params || {}
            })
          }
        }
      }
    );
  }

  private isActivedApp(appName: string) {
    const apps = runtime.getActiveApps();
    return apps.includes(appName);
  }
}
