<div *ngIf="!isBatchMode" class="title">
  题目修订
</div>

<div
  [hidden]="indexNum === 1"
  style="display: flex; justify-content: space-between;"
>
  <app-name-edit
    #zh_CN
    style="width: 48%;"
    lan="zh_CN"
    [name]="name.zh_CN"
  ></app-name-edit>
  <app-name-edit
    #en_US
    style="width: 48%;"
    lan="en_US"
    [name]="name.en_US"
  ></app-name-edit>
</div>
<div class="preview">
  <header>
    <p>预览</p>
    <div>
      <app-btn
        *ngIf="!canEdit"
        [text]="'编辑'"
        [image]="'./assets/images/org/edit.png'"
        [hoverColor]="'#409EFF'"
        [hoverImage]="'./assets/images/org/edit_hover.png'"
        (btnclick)="edit()"
      >
      </app-btn>
      <app-btn
        *ngIf="canEdit"
        [text]="'编辑'"
        [image]="'./assets/images/org/edit_hover.png'"
        [color]="'#409EFF'"
        [hoverColor]="'#409EFF'"
        [hoverImage]="'./assets/images/org/edit.png'"
        (btnclick)="edit()"
      >
      </app-btn>
      <nz-divider
        *ngIf="zh_CN.selectionList.length"
        nzType="vertical"
        class="divider"
      ></nz-divider>
      <div
        *ngIf="zh_CN.selectionList.length"
        style="display:flex; justify-content:flex-end; padding-right: 15px;"
      >
        <label nz-checkbox [(ngModel)]="checked" [disabled]="disabled">
          <span class="tip">应用于所有题本</span>
        </label>
      </div>
    </div>
  </header>
  <div class="preview-content">
    <ul>
      <li>
        <div>中文</div>
        <p *ngIf="!canEdit" [innerHTML]="zh_CN.getShowName() | html"></p>
        <textarea
          id="text-zh"
          *ngIf="canEdit"
          rows="3"
          nz-input
          [(ngModel)]="lans[0].after"
        ></textarea>
      </li>
      <li>
        <div>ENG</div>
        <p *ngIf="!canEdit" [innerHTML]="en_US.getShowName() | html"></p>
        <textarea
          id="text-en"
          *ngIf="canEdit"
          rows="3"
          nz-input
          [(ngModel)]="lans[1].after"
        ></textarea>
      </li>
    </ul>
    <!-- <ol *ngIf="subjectModel.type === 'MULTIPLE_CHOICE_ESSAY_QUESTION'">
      <li class="open-que-title">
        <span>选项（≤10）</span>
        <a *ngIf="canEdit" (click)="addOptions()">添加选项</a>
      </li>
      <li *ngFor="let data of subjectModel.options.options let idx = index">
        <div>
          <p *ngIf="!canEdit" [innerHTML]="data.name.zh_CN | html"></p>
          <input *ngIf="canEdit" style="margin-bottom: 10px;" nz-input placeholder="请输入(中文)" [(ngModel)]="data.name.zh_CN" />
          <p *ngIf="!canEdit" [innerHTML]="data.name.en_US | html"></p>
          <input *ngIf="canEdit" nz-input placeholder="请输入(英文)" [(ngModel)]="data.name.en_US" />
        </div>
        <div *ngIf="canEdit">
          <app-btn nz-popconfirm nzPopconfirmTitle="是否删除当前的选项吗？"
              (nzOnConfirm)="deleteOption(idx)" [text]="''" [image]="'./assets/images/org/del.png'" [hoverColor]="'#409EFF'" 
                [hoverImage]="'./assets/images/org/del_hover.png'"  >
              </app-btn>
        </div>
      </li>
    </ol> -->
    <div class="page-arrow" *ngIf="isBatchMode">
      <div>
        <img (click)="childPre()" src="./assets/images/pre_arrow.png" alt="" />
      </div>
      <div>
        <img
          (click)="childNext()"
          src="./assets/images/next_arrow.png"
          alt=""
        />
      </div>
    </div>
  </div>
</div>

<!-- <div [hidden]="indexNum === 0">
    <app-name-desc-edit #desc style="width: 90%;" [replaceName]="subjectModel.replaceName" [name]="name"></app-name-desc-edit>
</div> -->

<div *ngIf="!isBatchMode" class="footer">
  <button nz-button class="iptBtn" (click)="ok()">
    <span>确认</span>
  </button>
</div>
