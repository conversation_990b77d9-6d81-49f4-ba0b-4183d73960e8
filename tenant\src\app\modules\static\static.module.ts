import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StaticRoutingModule } from './static-routing.module';
import { PermissionStatementComponent } from './permission-statement/permission-statement.component';
import { RespondStatementComponent } from "@src/modules/static/respond-statement/respond-statement.component";


@NgModule({
  declarations: [PermissionStatementComponent, RespondStatementComponent],
  imports: [
    CommonModule,
    StaticRoutingModule
  ]
})
export class StaticModule { }
