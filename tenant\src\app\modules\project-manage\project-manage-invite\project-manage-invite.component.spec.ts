import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ProjectManageInviteComponent } from './project-manage-invite.component';

describe('ProjectManageInviteComponent', () => {
  let component: ProjectManageInviteComponent;
  let fixture: ComponentFixture<ProjectManageInviteComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ProjectManageInviteComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProjectManageInviteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
