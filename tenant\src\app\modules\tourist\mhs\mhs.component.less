@text-color: #17314C;

.content {
  width: 100%;
}

.s1 {
  width: 100%;
  background-color: #ffffff;

  &>div {
    margin: 0 auto;
    padding: 62px 60px 91px;
    display: flex;
  }

  .s1-l {
    flex: 2;
    padding-right: 115px;
  }

  .s1-r {
    flex: 1;
  }

  h5 {
    font-size: 30px;
    line-height: 42px;
    margin-bottom: 25px;

    span {
      font-size: 12px;
      color: #409EFF;
      border-radius: 14px;
      padding: 2px 10px;
      margin-left: 30px;
      background-color: rgba(64, 158, 255, 0.1);
    }
  }

  p {
    font-size: 14px;
    line-height: 24px;
    margin-bottom: 32px;
  }

  .btn {
    width: 160px;
    height: 38px;
    line-height: 38px;
    background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
  }
}

.s2 {
  width: 100%;
  background-color: #F5F6FA;
  padding: 69px 0 46px;

  div {
    width: 800px;
    margin: 0 auto;

    h5 {
      font-size: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: bold;
      color: #17314C;
      line-height: 33px;
      text-align: center;
      margin: 0;
    }

    p {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 300;
      color: #17314C;
      line-height: 24px;
      text-align: center;
      margin: 16px 0 18px;
    }

    img {
      display: block;
      margin: 0 auto;
    }
  }
}

.s3 {
  width: 100%;
  background-color: #ffffff;
  padding-top: 40px;

  .s3-main {
    margin: 0 auto;
    padding: 60px 0;
    display: flex;
  }

  h5 {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 60px 0 60px;
    background: url(../../../../assets/images/q.png) no-repeat center;
  }

  .s3-l {
    flex: 1;
    font-size: 16px;

    li {
      position: relative;
      line-height: 96px;

      div {
        width: 180px;
        display: inline-block;
      }

      span {
        font-size: 16px;
        color: #409EFF;
        background-color: #F1F8FF;
        border-radius: 46px;
        padding: 10px 25px;
      }

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        height: 1px;
        width: 60%;
        background: linear-gradient(to right, #ffffff, #ECECEC, #ffffff)
      }
    }
  }

  .s3-r {
    flex: 1;

    img {
      margin-top: 30px;
    }
  }
}

.s4 {
  width: 100%;
  background-color: #F5F6FA;
  padding: 60px 0 90px;

  &-content {
    width: 1020px;
    margin: 0 auto;

    >h5 {
      height: 33px;
      font-size: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: bold;
      color: #17314C;
      line-height: 33px;
      text-align: center;
      margin-bottom: 70px;
    }

    &-bottom {
      display: flex;
      align-items: center;
      height: 248px;
      justify-content: space-between;

      >div {
        background-color: #fff;
        box-sizing: border-box;
        padding: 27px 22px 26px 35px;
        border-radius: 13px;

        img {
          display: block;
        }

        h5 {
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #17314C;
          line-height: 28px;
          margin: 31px 0 11px;
        }

        p {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 300;
          color: #17314C;
          line-height: 24px;
        }
      }

      &-left {
        width: 690px;
      }

      &-right {
        width: 306px;
      }
    }

  }
}

.s5 {
  width: 100%;
  padding: 80px 0 100px;
  background-color: #fff;

  &-content {
    width: 1020px;
    margin: 0 auto;

    &-title {
      font-size: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: bold;
      color: #17314C;
      line-height: 33px;
      text-align: center;
      margin-bottom: 16px;
    }

    &-info {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 300;
      color: #17314C;
      line-height: 24px;
      text-align: center;

      span {
        color: #F99039;
      }
    }

    &-grade {
      width: 100%;
      height: 168px;
      background: #FFFFFF;
      box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.08);
      border-radius: 13px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 17px 40px 20px 0;
      margin-top: 40px;

      &-left {
        padding-top: 3px;

        &-title {
          width: 510px;
          height: 40px;
          box-sizing: border-box;
          display: flex;
          padding-left: 20px;
          align-items: center;
          background: linear-gradient(270deg, rgba(116, 205, 255, 0) 0%, rgba(64, 158, 255, .07) 100%);
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: bold;
          color: #409EFF;
          line-height: 1;
          margin-bottom: 16px;
        }

        p {
          width: 680px;
          box-sizing: border-box;
          font-size: 14px;
          padding-left: 20px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 300;
          color: #17314C;
          line-height: 24px;

          span {
            color: #F99039;
          }
        }
      }
    }

    &-performance {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 300;
      color: #17314C;
      line-height: 24px;
      text-align: center;
      margin: 80px 0 40px;
    }

    &-performanceImg {
      display: block;
      margin: 0 auto 24px;
    }

    &-data-list {
      width: 100%;
      height: 264px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 80px;

      &-left,
      &-right {
        width: 500px;
        height: 100%;
        background: #FFFFFF;
        box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.08);
        border-radius: 13px;
        padding: 18px 13px 20px 0;

        &-head {
          display: flex;
          justify-content: space-between;
          margin-bottom: 7px;

          &-title {
            flex: 1;
            height: 40px;
            background: linear-gradient(270deg, rgba(116, 205, 255, 0) 0%, rgba(64, 158, 255, .07) 100%);
            padding-left: 25px;
            font-size: 16px;
            display: flex;
            align-items: center;
            font-family: PingFangSC, PingFang SC;
            font-weight: bold;
            color: #409EFF;
            line-height: 24px;
            margin-top: 2px;
          }
        }
      }

      &-left {

        &-main {
          display: flex;
          justify-content: space-between;
          align-items: end;
          padding-left: 20px;
          padding-right: 7px;

          &-left {
            margin-right: 18px;

            &-title,
            p {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 600;
              color: #17314C;
              line-height: 24px;
            }

            p {
              font-weight: 400;
            }
          }

          &:last-of-type {
            margin-top: 24px;
          }
        }

      }

      &-right {
        &-head {
          margin-bottom: 13px;
        }

        &-main {
          padding-left: 20px;

          p {
            width: 460px;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #17314C;
            line-height: 24px;
          }

          span {
            font-weight: 600;
          }
        }
      }
    }

    &-download {
      width: 160px;
      height: 38px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: bold;
      color: #FFFFFF;
      line-height: 1;
    }
  }
}