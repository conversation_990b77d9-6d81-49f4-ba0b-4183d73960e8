import { Component, OnInit, Input } from '@angular/core';
import {DatePipe} from "@angular/common";

// @ts-ignore
@Component({
  selector: 'app-project-reopen',
  templateUrl: './project-reopen.component.html',
  styleUrls: ['./project-reopen.component.less'],
  providers: [DatePipe]
})
export class ProjectReopenComponent implements OnInit {

  @Input() public id : string;
  // @Input() public startDate : Date;
  // @Input() public endDate : Date;
  @Input() public dates : string[];
  startTime = null;
  endTime = null;

  constructor(private datePipe: DatePipe) { }

  ngOnInit(): void {
    if(this.dates[0]){
      this.startTime = new Date(this.dates[0]);
    }

    if(this.dates[1]){
      this.endTime = new Date(this.dates[1]);
    }


  }

  onOk(e) {
   
  }

  onChange(e){
    if(e[0]){
      if(!this.startTime){
        e[0].setHours(0);
        e[0].setMinutes(0);
      } else {
        const startTime_ = this.startTime;
        if(startTime_.getHours() !== e[0].getHours() && startTime_.getMinutes() !== e[0].getMinutes()){
          e[0].setHours(startTime_.getHours());
          e[0].setMinutes(startTime_.getMinutes());
        }
      }

      this.dates[0] = this.datePipe.transform(e[0], 'yyyy-MM-dd HH:mm');

    }

    if(e[1]){
      if(!this.endTime){
        e[1].setHours(23);
        e[1].setMinutes(30);
      } else {
        const endTime_ = new Date(this.endTime);
        if(endTime_.getHours() !== e[1].getHours() && endTime_.getMinutes() !== e[1].getMinutes()){
          e[1].setHours(endTime_.getHours());
          e[1].setMinutes(endTime_.getMinutes());
        }
      }

      this.dates[1] = this.datePipe.transform(e[1], 'yyyy-MM-dd HH:mm');
    }

    this.startTime = e[0];
    this.endTime = e[1];
  }

}
