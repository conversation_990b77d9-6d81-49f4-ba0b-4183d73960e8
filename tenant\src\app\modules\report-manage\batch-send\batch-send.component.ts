import { Component, OnInit, ElementRef, Inject } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import _ from "lodash";
import { ActivatedRoute, Router } from "@angular/router";
import { ReportService } from "../report.service";
import { NzMessageService, UploadXHRArgs, UploadFile } from "ng-zorro-antd";
import { HttpClient, HttpHeaders, HttpEvent } from "@angular/common/http";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { fromEvent } from "rxjs";
import { MessageService } from "@src/shared/custom-message/message-service.service";
@Component({
  selector: "app-batch-send",
  templateUrl: "./batch-send.component.html",
  styleUrls: ["./batch-send.component.less"],
  // providers: [DatePipe]
})
export class BatchSendComponent implements OnInit {
  Breadcrumbs = [];
  formData = {
    isSelf: false, //指定false 全部true
    subject: "",
    content: "<p>您好：</p>\n<p>报告在附件中请查收，谢谢！</p>",
  };
  type = "";
  tenantUrl: string = "/tenant-api";
  tinyconfig = {};
  fileIds: any[] = []; // 上传附件id
  fileListAttachment: UploadFile[] = []; // 批量导入
  fileType = [".txt", ".docx,", ".doc", ".xlsx", ".xls", ".html"];
  isLoading: boolean;
  isUploading: boolean;
  isLoadingOne = false;
  subscribeScoll: any;
  scrollContent: any;
  scrollLoding: boolean; //当前请求数据是否加载完成
  isSpinning: boolean; //是否显示加载样式
  reportMailContentId: string;
  page: any = {
    // 分页条件
    current: 1,
    pages: 10,
    size: 20,
  };
  list = [];
  constructor(
    private reportService: ReportService,
    private route: ActivatedRoute,
    private router: Router,
    private el: ElementRef,
    private fb: FormBuilder,
    private msg: NzMessageService,
    private http: HttpClient,
    private customMsg: MessageService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService
  ) {}
  ngOnInit() {
    this.Breadcrumbs = JSON.parse(localStorage.getItem("break")) || [];
    this.Breadcrumbs.forEach((item) => {
      if (item.Highlight) {
        if (item.name == "首页") {
          item.path = "/home";
        }
        if (item.name == "报告管理") {
          item.path = "/report-manage";
        }
        item.Highlight = false;
      }
    });
    this.Breadcrumbs.push({
      path: "",
      name: "批量发送",
      Highlight: true,
    });
    const _this = this;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.tinyconfig = {
      // resize: false, // 禁用编辑器缩放
      // elementpath: false, // 禁用元素路径显示
      statusbar: false, // 禁用底部状态栏
      // toolbar_mode: 'floating', // 工具栏收起模式，需要版本5.2以上
      images_upload_url: `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`, // 配置你图片上传的url
      fontsize_formats:
        "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 32pt 36pt",
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.http
          .post(
            `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`,
            formData,
            { headers: headers }
          )
          .subscribe(
            (response: any) => {
              if (response) {
                this.http
                  .get(
                    `${this.tenantUrl}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                    { headers: headers }
                  )
                  .subscribe((imgurl: any) => {
                    // let baseUrl: string = window.location.origin + "/";
                    let baseUrl: string = window.location.origin + "/";
                    if (baseUrl.indexOf("http://localhost") !== -1) {
                      baseUrl = "http://sag-qa.knxdevelop.com/";
                    }
                    let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                    // if ( environment.dev ) {
                    //    url = environment.SERVER_URL.substr(0, environment.SERVER_URL.length - 1)  + imgurl.data.url; // 这里是你获取图片url
                    // } else {
                    //   url = 'api' + imgurl.data.url; // 这里是你获取图片url
                    // }
                    // 把图片链接，img src标签显示图片的有效链接放到下面回调函数里

                    success(url);

                    // this.getBase64(url,success)//图片转base64
                  });
              } else {
                if (response && response.rtnMsg) {
                  failure(response.rtnMsg);
                } else {
                  failure("上传失败：未知错误");
                }
              }
            },
            (error1) => {
              failure("上传失败：未知错误");
            }
          );
      },
      height: 307,
    };
    this.type = this.route.snapshot.queryParams.type;
    this.reportMailContentId = this.route.snapshot.queryParams.reportMailContentId;
    // this.formData.isSelf = this.type === 'individual'
    this.scrollContent = this.el.nativeElement.querySelector("#scrollContent");
    this.getList();
    this.subscribeScoll = fromEvent(this.scrollContent, "scroll").subscribe(
      (event) => {
        this.onWindowScroll(); //调用
      }
    );
  }
  onWindowScroll() {
    this.scollPostion();
  }
  scollPostion() {
    var t, c, h;
    if (this.scrollContent && this.scrollContent.scrollTop >= 0) {
      //滚动条滚动距离
      t = this.scrollContent.scrollTop;
      //文档内容实际高度（包括超出视窗的溢出部分）
      h = this.scrollContent.scrollHeight;
      //窗口可视范围高度
      c = this.scrollContent.clientHeight;
      if (h - c - t <= 30 && !this.scrollLoding) {
        if (this.page.current < this.page.pages) {
          this.page.current++;
          this.getList();
        }
      }
    } else {
      console.log("滚动出错了");
    }
    return {
      top: t,
      clientHeight: c,
      height: h,
    };
  }
  // 获取报告列表
  getList() {
    if (this.scrollLoding) {
      return;
    }
    this.scrollLoding = true;
    this.reportService
      .pageByReportMailId(this.page, this.reportMailContentId)
      .subscribe((res) => {
        this.list = this.list.concat(res.data);
        this.page.pages = res.page.pages;
        if (this.page.current >= this.page.pages) {
          this.isSpinning = true;
        }
        this.scrollLoding = false;
      });
  }
  /**
   * customReq 导入
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    if (this.isUploading) {
      return false;
    }
    this.isUploading = true;
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };
  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.reportService
      .readReportSendExcel(
        formData,
        this.reportMailContentId,
        this.type == "team"
      )
      .subscribe((event: HttpEvent<any>) => {
        this.isUploading = false;
        item.onSuccess!();
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入文件成功");
        }
      });
  }
  exportR() {
    // 导出
    if (this.isLoading) {
      return false;
    }
    this.isLoading = true;
    this.reportService
      .exportReportSendExcel(this.reportMailContentId)
      .subscribe((res) => {
        this.isLoading = false;
        const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
        let fileName = res.headers
          .get("Content-Disposition")
          .split(";")[1]
          .split("filename=")[1];
        const fileNameUnicode = res.headers
          .get("Content-Disposition")
          .split("filename*=")[1];
        // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）

        if (fileName) {
          fileName = decodeURIComponent(fileName);
        }
        if (fileNameUnicode) {
          fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
        }
        const link = document.createElement("a");
        link.setAttribute("href", URL.createObjectURL(blob));
        link.setAttribute("download", fileName);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
  }
  /**
   * preview 预览上传的文件
   * @param file
   */
  preview(file) {
    window.open(window.URL.createObjectURL(file.originFileObj));
  }
  /**
   * uploadAttachment 上传附件
   * @param item
   */
  uploadAttachment = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("file", item.file as any);
    formData.append("isPublic", "false");
    formData.append("effectiveFileTypes", this.fileType as any);
    formData.append("businessType", "DEFAULT");
    this.uploadAttachmentUrl(formData, item);
  };
  beforeUpload = (file: File) => {
    //限制文件格式
    let name = file.name;
    var suffix = name.substring(name.lastIndexOf(".")); //.txt
    const index = this.fileType.indexOf(suffix);
    if (index === -1) {
      // this.msg.error(`文件格式不正确`);
      this.customMsg.open("error", "文件格式不正确");
      return false;
    }
  };
  /**
   * uploadAttachmentUrl 上传pdf报告文件
   */
  uploadAttachmentUrl(formData, item) {
    const url = `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`;
    return this.http.post(url, formData).subscribe(
      (res: any) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.msg.success("上传文件成功");
          this.fileIds.push({
            id: res.data.id,
            uid: item.file.uid,
          });
        } else {
          item.onError!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }
  fileChange(e) {
    const arr = e.fileList;
    this.fileListAttachment = [...arr];
  }
  // 发送
  send() {
    if (!this.formData.subject) {
      // this.msg.error("邮件主题不能为空");
      this.customMsg.open("error", "邮件主题不能为空");
      return false;
    }

    if (!this.formData.content) {
      // this.msg.error("邮件正文不能为空");
      this.customMsg.open("error", "邮件正文不能为空");
      return false;
    }
    this.isLoadingOne = true;
    this.reportService
      .sendReportEmail({
        ...this.formData,
        reportMailContentId: this.reportMailContentId,
      })
      .subscribe((res) => {
        this.isLoadingOne = false;
        if (res.result.code === 0) {
          this.msg.success("邮件发送成功");
          this.router.navigate(["report-manage"]);
        }
      });
  }

  // 国际化
  lan = "zh_CN";
  onChangeLan(e) {
    this.lan = e;
  }
}
