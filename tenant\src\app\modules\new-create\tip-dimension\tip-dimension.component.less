
.tipDimension{
    width: 100%;
    min-height: 500px;
    ::ng-deep{
      .ant-card{
        border-radius: 8px;
        min-height: 500px;
        margin-bottom: 0px;
      }
      .ant-card-head{
        width: 100%;
        padding: 0 20px;
        height: 40px !important;
        min-height: 40px !important;
        font-size: 14px;
      }
      .ant-card-head-wrapper{
        height: 40px !important;
      }
      .ant-card-body{
        padding: 20px;
      }
      .ant-card-head-title{
        padding: 0;
      }
    }
    .row{
      margin-bottom: 20px;
      .sup{
        font-weight: 600;
        >span{
          width: 100%;
          overflow: hidden; /*超出部分隐藏*/
          white-space: nowrap; /*不换行*/
          text-overflow: ellipsis; /*超出部分文字以...显示*/
        }
        .son{
          .sonItem{
            background-color: gold;
            margin-bottom: 5px;
          }
        }
      }
    }
    .title{
      display: flex;
      align-items: center;
      .recommend{
        display: flex;
        align-items: center;
        margin-right: 55px;
        div{
          width: 10px;
          height: 10px;
          background-color: #ffba3c;
          margin-right: 5px;
        }
        span{
          color: #ffba3c;
        }
      }
    }
    .empty{
      cursor: pointer;
    }
  }