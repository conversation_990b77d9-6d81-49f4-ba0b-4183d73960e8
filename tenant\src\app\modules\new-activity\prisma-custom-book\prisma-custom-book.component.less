@bgColor: #40a9ff;
@fgColor: white;

.btn-parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.block {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #fbb853;
  margin-left: 5px;
  cursor: pointer;
}

::ng-deep .rev-mod .ant-modal-body {
  padding-bottom: 0;
}

.tip-box {
  display: inline-block;
  background: rgba(51, 114, 255, 0.08);
  border-radius: 2px;
  padding: 2px 5px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 17px;
  // text-align: center;
  word-wrap: break-word;
}

.ques-box {
  display: flex;

  p {
    flex: 1;
  }
}

::ng-deep {
  .survey-book-add-modal .ant-modal-body {
    padding: 0;
  }
}

::ng-deep {
  .through-tr {
    border-bottom: none;

    td {
      border-bottom: none !important;
    }
  }
}

.content {
  background-color: #f5f6fa;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  min-width: 1200px;

  .body {
    flex: 1;
    // margin-left: 15px;
    // margin-right: 15px;
    // padding: 30px 8px 0 8px;
    padding-top: 30px;
  }

  .title {
    display: flex;
    align-items: center;
    // margin: 20px 30px 20px 0;
    // margin-top: 20px;
    font-size: 24px;
    font-family: PingFangSC-Thin, PingFang SC;
    font-weight: 200;
    color: #17314c;

    >span {
      margin-right: 10px;
    }
  }

  .searchDiv {
    margin-left: 50px;
  }

  .input-search {
    border-radius: 20px;
    width: 400px;
  }

  .icon-search {
    color: #409eff;
  }

  .tab {
    font-size: 24px;
    font-weight: 500;
    color: #17314c;
    line-height: 33px;
  }

  .action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    margin-bottom: 16px;

    // min-height: 56px;
    .right-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .penetration-questions {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #495970;
        line-height: 20px;
        cursor: pointer;

        span {
          margin-left: 6px;
        }

        .penetration-span {
          padding-right: 18px;
        }
      }

      .penetration-questions:hover {
        color: #409eff;
      }
    }
  }

  .projName {
    max-width: 400px;
    white-space: pre-wrap;
    text-align: center;
    font-size: 20px;
    font-weight: 600;
    color: #17314c;
    line-height: 28px;
  }

  .topiclist {
    .word-wrap {
      word-wrap: break-word;
      white-space: pre-wrap;
      margin: 0;
    }

    .topic {
      padding: 5px 2px;
      min-height: 100px;
      border-top: solid 1px #e6e6e6;
      max-width: 1150px;

      .ques {
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
        font-size: 16px;
        font-weight: 400;
        color: #17314c;
        line-height: 28px;

        .quesName {
          display: flex;
        }

        span {
          max-width: 1015px;
        }
      }

      .optionlist {
        display: flex;
        flex-wrap: wrap;
        max-width: 100%;

        div {
          padding-bottom: 10px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #17314c;
          line-height: 22px;
        }

        .marg {
          margin-right: 60px;
        }
      }
    }

    .double {
      // background-color: #CEE7FF;
      background-color: #f5faff;
    }
  }
}

.last_topic {
  border-bottom: solid 1px #e6e6e6;
}

.iptBtn {
  width: 128px;
  height: 38px;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  font-weight: 500;
  color: #ffffff;
  font-size: 16px;
}

.download_template {
  display: flex;
  justify-content: center;
  align-items: center;
}

// .revision-btn:hover {
//   color: white;
//   background-color: @bgColor;
// }

.to-delete {
  color: #f19672;
}

.svg-ico-delete {
  background-color: #f19672;
}

.delete-box {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-left: 18px;
}

.delete-txt {
  color: #f19672;
  margin-left: 6px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  box-shadow: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

.primary-btn {
  height: 38px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  padding: 0 16px 0 12px;
  color: #fff;

  >i {
    font-size: 24px;
    margin-right: 9px;
    color: #fff;
  }

  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  line-height: 22px;
  cursor: pointer;
}

.default-btn {
  height: 36px;
  background: transparent;
  border: 1px solid #409eff;
  color: #409eff;
  border-radius: 24px;
  padding: 0 16px 0 12px;
  display: flex;
  justify-content: center;
  align-items: center;

  >i {
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    font-size: 12px;
    margin-right: 9px;
    color: #fff;
    border-radius: 50%;
    background-color: #409eff;
  }

  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  cursor: pointer;
}

.mb-12 {
  margin-bottom: 12px;
}

.mt-12 {
  margin-top: 12px;
}

.mr-16 {
  margin-right: 16px;
}

.fixd-footer {
  width: 100%;
  position: fixed;
  bottom: 0px;
  background-color: white;
  height: 70px;
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);

  >div {
    max-width: 1200px;
    display: flex;
    justify-content: flex-end;
    margin: auto;
    height: 100%;
    align-items: center;
  }
}
