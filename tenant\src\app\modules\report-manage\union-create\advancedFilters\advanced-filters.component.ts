import { map } from "rxjs/operators";
import {
  Component,
  OnInit,
  Input,
  TemplateRef,
  Output,
  EventEmitter,
} from "@angular/core";
import { NzModalRef, NzModalService } from "ng-zorro-antd/modal";
import { ReportService } from "../../report.service";
import { UploadFile, UploadXHRArgs, UploadFilter } from "ng-zorro-antd/upload";
import _ from "lodash";
import { NzMessageService } from "ng-zorro-antd/message";
import { Observable, Observer } from "rxjs";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-advanced-filters",
  templateUrl: "./advanced-filters.component.html",
  styleUrls: ["./advanced-filters.component.less"],
})
export class AdvancedFiltersComponent implements OnInit {
  @Input() father: any;
  @Input() isMin: boolean;
  @Input() organizationDistanceList = [];
  errorFlag: boolean = false;
  uploading: boolean = false;
  downloading: boolean = false;

  FilterBoxVisiable: boolean = false;
  // 是否点击了确认按钮
  isClickSubmitBtn: boolean = false;
  list: any[] = []; // 参数

  tplModal: NzModalRef;
  tplModalButtonLoading: boolean = false;

  uploadName: string = "上传文件";
  // 选项值
  match: string = "MATCH_ALL";

  params: any[] = [];

  advancedFilterObject = {
    // 高级筛选 对象
    // 选项名称
    optionName: [
      {
        value: "name",
        name: "组织架构",
      },
    ],
    condition: [
      {
        value: "INCLUDE",
        name: "按组织层级",
      },
    ],
    product: [],

    // 活动名称
  };

  fileType = ".xlsx,.xls";

  constructor(
    private modalService: NzModalService,
    private message: NzMessageService,
    private rptService: ReportService,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {}

  ngOnInit() {
    if (this.isMin) {
      this.advancedFilterObject.optionName = [
        {
          value: "demographic",
          name: "人口信息",
        },
      ];
    }
    const permission = this.permissionService.isPermission();
    if (permission) {
      this.advancedFilterObject.condition.push({
        value: "NOT_INCLUDE",
        name: "自定义",
      });
    }
    this.rptService
      .gettoollist({ questionnaireSceneTypeEnum: "QUESTIONNAIRE_TYPE" })
      .subscribe((res) => {
        this.advancedFilterObject.product = res.data.sceneTypes;
      });
  }

  beforeUploadProduct = (
    file: UploadFile // 限制文件类型
  ) =>
    new Observable((observer: Observer<boolean>) => {
      const isType = [""];
      file.name.lastIndexOf(".");
      const fileType = file.name.substring(
        file.name.lastIndexOf("."),
        file.name.length
      ); //从后往前截取文类型
      if (fileType == ".xlsx" || fileType == ".xls") {
        // fileType==".doc"||fileType==".docx"||fileType==".pdf"||fileType==".rar"||
        observer.next(true);
        observer.complete();
      } else {
        // this.message.warning('只支持excel类型!');
        this.customMsg.open("warning", "只支持excel类型");
        observer.complete();
        return;
      }
    });

  addField(e?: MouseEvent): void {
    if (e) {
      e.preventDefault();
    }
    if (this.list.length >= 1) {
      this.message.create("error", "筛选条件最多只能有一项！");
      return;
    }
    this.errorFlag = false;

    this.list.push({
      eventName: null, // 第一选项
      judge: null, // 第二选项
      name: null, // 第三选项
      chooseType: null, // 第三选项
    });
  }
  /**
   * 监听弹框的显示和隐藏
   *@author:wangxiangxin
   *@Date:2023/09/25
   */
  popoverVisibleChange(e) {
    if (e) {
      this.isClickSubmitBtn = false;
    }
    this.FilterBoxVisiable = e;
    // 当点击了确定按钮以后 更新选中的内容
    if (this.isClickSubmitBtn) {
      this.father.updateList(this.list, this.isMin);
    }
  }
  downloadTemplate() {
    // 下载模板
    this.downloading = true;
    if (this.isMin) {
      this.rptService
        .downloadDemographicSimpleTemplate(this.father.projectId)
        .subscribe((res) => {
          this.downFile(res);
          this.downloading = false;
        });
    } else {
      this.rptService
        .downloadSimpleTemplate(this.father.projectId)
        .subscribe((res) => {
          this.downFile(res);
          this.downloading = false;
        });
    }
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let params = {
      fileType: "." + item.file.name.split(".")[1],
    };
    this.upload(formData, item);
  };

  /**
   * uploadExcel 上传配置
   */
  upload(formData, item) {
    this.uploading = true;
    const uploadApi = this.isMin
      ? "uploadSimpleTemplateDemographic"
      : "uploadSimpleTemplate";
    return this.rptService[uploadApi](
      formData,
      this.father.projectId
    ).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.message.success("文件上传成功");
          if (item.file.name.length > 20) {
            let arr = item.file.name.split(".");
            let str = arr[0]
              .split("")
              .splice(0, 16)
              .join("");
            let arrNew = [];
            arrNew.push(str);
            arrNew.concat(arr[1]).join(".");
            this.uploadName = arrNew.concat(arr[1]).join(".");
          } else {
            this.uploadName = item.file.name;
          }
          this.list.forEach((item) => {
            item.name = res.data.map((list) => {
              return list.id;
            });
          });
        } else {
          this.message.success("文件上传失败" + res.result.message);
        }
        this.uploading = false;
      },
      (err) => {
        this.uploading = false;
        item.onError!(err, item.file!);
      }
    );
  }

  downFile(data) {
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });

    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];

    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];

    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
      // .split('\'\'')[1]
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  optionChange(data: any, index: number): void {
    if (data === "name") {
      this.list.map((item, idx) => {
        if (item.eventName === "name" && index !== idx) {
          this.message.create("error", "人员姓名最多只能有一项！");
          this.list[index] = {
            eventName: null,
            judge: null,
            name: null,
          };
        }
      });
    }
    if (data === "questionnaireType") {
      this.list.map((item, idx) => {
        if (item.eventName === "questionnaireType" && index !== idx) {
          this.message.create("error", "产品类型最多只能有一项！");
          this.list[index] = {
            eventName: null,
            judge: null,
            name: null,
          };
        }
      });
    }
    if (data !== "questionnaireType" && data !== "name") {
      this.list[index] = {
        eventName: data,
        judge: null,
        name: null,
      };
    }
  }

  removeField(i, e: MouseEvent): void {
    // 移除选项
    e.preventDefault();
    this.uploadName = "上传文件";
    this.list.splice(i, 1);
  }

  createTplModal(
    tplTitle: TemplateRef<{}>,
    tplContent: TemplateRef<{}>,
    tplFooter: TemplateRef<{}>
  ): void {
    this.tplModal = this.modalService.create({
      nzTitle: tplTitle,
      nzContent: tplContent,
      nzFooter: tplFooter,
    });
  }

  submitForm(): void {
    // 提交表单
    // 表单验证
    this.verifyParams();
    if (this.errorFlag) return;
    // this.father.loadData()
    this.FilterBoxVisiable = false;
    this.isClickSubmitBtn = true;
  }

  verifyParams() {
    // 验证
    if (this.list.length !== 0) {
      for (let item of this.list) {
        if (item.eventName !== "demographic") {
          if (!item.eventName || !item.judge) {
            // this.message.warning('选项不能为空')
            this.customMsg.open("warning", "选项不能为空");
          } else if (item.judge === "INCLUDE") {
            if (item.chooseType === null) {
              // this.message.warning('选项不能为空')
              this.customMsg.open("warning", "选项不能为空");
              this.errorFlag = true;
              return;
            } else {
              this.errorFlag = false;
            }
          } else {
            if (!item.name) {
              // this.message.warning('请上传文件')
              this.customMsg.open("warning", "请上传文件");
              this.errorFlag = true;
              return;
            } else {
              this.errorFlag = false;
            }
          }
        } else {
          if (!item.name) {
            // this.message.warning('请上传文件')
            this.customMsg.open("warning", "请上传文件");
            this.errorFlag = true;
            return;
          } else {
            this.errorFlag = false;
          }
        }
      }
    }
  }

  getSonParams() {
    this.params = [];
    // this.verifyParams()
    if (this.errorFlag) return;
    this.list.map((item) => {
      this.params.push({
        condition: item.eventName,
        judge: item.judge,
        value: item.name,
      });
    });
    return this.params;
  }

  getSonMatch() {
    return this.match;
  }

  clear() {
    this.list = [];
    this.uploadName = "上传文件";
    this.errorFlag = false;
    // this.father.updateList(this.list)
  }

  // box
  showFilterBox() {
    this.verifyParams();
    if (this.errorFlag) return;
    this.FilterBoxVisiable = !this.FilterBoxVisiable;
  }

  onChange(value: string, index): void {
    setTimeout(() => {
      this.list[index].name = this.updateValue(value);
    }, 0);
  }

  updateValue(value: string) {
    const reg = /^-?(0|[1-9][0-9]*)(\.[0-9]*)?$/;
    if ((!isNaN(+value) && reg.test(value)) || value === "") {
      return value;
    }
  }
}
