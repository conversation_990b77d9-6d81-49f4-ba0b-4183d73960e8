import { Component, OnInit, Inject } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import {
  SocialService,
  SocialOpenType,
  ITokenService,
  DA_SERVICE_TOKEN,
} from "@knz/auth";
import { LoginService } from "../../login/login.service";
import { NzMessageService } from "ng-zorro-antd";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-loading",
  template: "",
})
export class LoadingComponent implements OnInit {
  constructor(
    private routeInfo: ActivatedRoute,
    private router: Router,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private login: LoginService,
    private msg: NzMessageService,
    private customMsg: MessageService
  ) {
    let token = this.routeInfo.snapshot.queryParams["token"];
    let refreshToken = this.routeInfo.snapshot.queryParams["refreshToken"];
    let url = this.routeInfo.snapshot.queryParams["url"];
    let params = {
      productCode: "SAG",
    };
    const defaultPermissions = [
      "SAG:TENANT:HOME",
      "SAG:TENANT:PROJECT_MGT:CREATE_SELECT",
      "SAG:TENANT:PROJECT_MGT:LIST",
      "SAG:TENANT:PROJECT_MGT:DETAIL",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:COMPARE",
      "SAG:TENANT:USER_CENTER",
      "SAG:TENANT:DOC_MGT",
      "SAG:TENANT:PROJECT_MGT:CREATE_SELECT_CONFIRM",
      "SAG:TENANT:PROJECT_MGT:LIST:UPDATE",
      "SAG:TENANT:PROJECT_MGT:LIST:COPY_PROJECT",
      "SAG:TENANT:PROJECT_MGT:LIST:HIDE",
      "SAG:TENANT:PROJECT_MGT:LIST:INVITE",
      "SAG:TENANT:PROJECT_MGT:DETAIL:EDIT",
      "SAG:TENANT:PROJECT_MGT:DETAIL:DELETE",
      "SAG:TENANT:PROJECT_MGT:DETAIL:INVITE_ANSWER",
      "SAG:TENANT:PROJECT_MGT:DETAIL:EXPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:CREATE_GROUP_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:CREATE_COMPARE_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_DIMENSION_SCORE",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:VIDEO",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:EXPERT_INTERPRETATION",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_DIMENSION_SCORE",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:HIDE",
      "SAG:TENANT:PROJECT_MGT:DETAIL:DELAY",
      "SAG:TENANT:PROJECT_MGT:DETAIL:STOP",
      "SAG:TENANT:PROJECT_MGT:DETAIL:ANSWER_RATE",
      "SAG:TENANT:PROJECT_MGT:DETAIL:BATCH_EXPORT_ANSWER_RATE",
      "SAG:TENANT:MSG_SEND_LOG",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:BATCH_EMAIL_SEND",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:BATCH_EMAIL_SEND",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:ONLINE_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:ONLINE_REPORT:CREATE",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:CREATE_GROUP_REPORT_CN",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:CREATE_GROUP_REPORT_EN",
    ];

    let tokenObj: any = this.tokenService.get();
    if (!tokenObj) {
      tokenObj = {};
    }

    tokenObj.token = token;
    tokenObj.refreshToken = refreshToken;
    this.tokenService.set(tokenObj);
    sessionStorage.setItem("permission", "true");
    localStorage.setItem("permission", "true");

    this.login.getPermisionCode(params.productCode).subscribe((res2) => {
      if (res2.result.code === 0) {
        if (!res2.data || res2.data.length === 0) {
          localStorage.setItem(
            params.productCode + "_permissioncode",
            JSON.stringify(defaultPermissions)
          );
        } else {
          localStorage.setItem(
            params.productCode + "_permissioncode",
            JSON.stringify(res2.data)
          );
        }
      } else {
        // this.msg.error(res2.result.message);
        this.customMsg.open("error", res2.result.message);
      }

      this.login.login().subscribe((res) => {
        if (res.result.code === 0) {
          this.login.emitUser(res);
          this.router.navigateByUrl(url + "?_allow_anonymous=true");
        }
      });
    });
  }

  ngOnInit() {}
}
