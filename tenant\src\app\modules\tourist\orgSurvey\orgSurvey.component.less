@text-color: #17314c;

.content {
    width: 100%;
}

.s1 {
    width: 100%;
    background-color: #ffffff;

    &>div {
        margin: 0 auto;
        padding: 60px 0;       
    }

    .s1-l {
       
        // padding-right: 100px;
        .blue_color_text {
          color: rgba(65, 158, 255, 1);;
        }
    }

    h5 {
        font-size: 30px;
        line-height: 42px;
        margin-bottom: 25px;

        span {
            font-size: 12px;
            color: #FF17314C;
            border-radius: 14px;
            padding: 2px 10px;
            margin-left: 30px;
            background-color: rgba(64, 158, 255, 0.1);
        }     
    }

    p {
        font-size: 14px;
        line-height: 24px;
        margin-bottom: 80px;
    }

    .btn {
        width: 160px;
        height: 38px;
        line-height: 38px;
        background: linear-gradient(90deg,
                rgba(38, 208, 241, 1) 0%,
                rgba(64, 158, 255, 1) 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        font-size: 16px;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
    }
}

.s2 {
    width: 100%;
    background-color: #f5f6fa;
    text-align: center;

    &>div {
        margin: 0 auto;
        padding: 60px 0;
    }

    h5 {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        margin: 40px 0 50px;
    }
}

.s3 {
    width: 100%;
    background-color: #ffffff;

    h5 {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 60px 0 60px;
        // background: url(../../../../assets/images/q.png) no-repeat center;
    }

    .s3_content{
        display: flex;
        width: 100%;
        .s3_l {
            width: 50%;
            margin-left: 250px;
            img {
                margin-top: 30px;
            }
        }
    
        .s3_r {
            margin-right: 300px;
            img {
                margin-top: 30px;
            }
        }
    }
 
}

.s4 {
    width: 100%;
    background-color: #f5f6fa;
    h5 {
        font-size: 24px;
        font-weight: bold;
        padding: 50px 0 40px;
        text-align: center;
    }
    .s4_content{
        display: flex;
        width: 100%;
        .s4_l {
            margin-left: 400px;    
            img {

                margin-top: 30px;
            }
    
            text-align: center;
        }
    
        .s4_r {
            font-size: 16px;
            margin-left: 300px;    

            li{
                line-height: 50px;
                img {
                    width: 20px;
                    height: 20px;
                    margin-right: 10px;
                }
            }
           
        }
    }
    

    
    .btn {
        margin: 20px 0;

        button {
            width: 160px;
            height: 38px;
            line-height: 38px;
            background: linear-gradient(90deg,
                    rgba(38, 208, 241, 1) 0%,
                    rgba(64, 158, 255, 1) 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 19px;
            font-size: 16px;
            font-weight: bold;
            color: #ffffff;
            cursor: pointer;
        }
    }
}


.tip_name {
    width: 310px;
    height: 38px;
    line-height: 38px;
    color: #fff;
    text-align: center;
    font-size: 16px;
    background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
    border-radius: 24px 24px 24px 0px;
}

.tip_ul {
    display: flex;
    margin-top: 15px;

    li {
        display: flex;
        align-items: center;

        em {
            width: 14px;
            height: 14px;
            background: linear-gradient(180deg, #0E65FE 0%, #28CAFD 100%);
            border-radius: 9px;
        }

        span {
            color: #17314c;
            font-size: 16px;
            margin-left: 5px;
        }
    }
}

.ground_f {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 10px 20px 0px rgba(161, 180, 199, 0.3);
    border-radius: 10px;
    padding-bottom: 60px;
}