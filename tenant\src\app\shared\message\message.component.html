<!-- 图片采用样式背景图的形式而不是img标签,不然可能会导致发版后图片不显示 -->
<div class="msg-warp">
  <ng-container *ngIf="type === 'deleted'">
    <div class="msg-img-delete msg-img"></div>
    <p>已删除</p>
  </ng-container>
  
  <ng-container *ngIf="type === 'unInviteType'">
    <div class="msg-img-type msg-img"></div>
    <p>请选择邀请方式</p>
  </ng-container>
  
  <ng-container *ngIf="type === 'invited'">
    <div class="msg-img-invited msg-img"></div>
    <p>邀请已发送</p>
  </ng-container>

  <ng-container *ngIf="type === 'success'">
    <div class="msg-img-success msg-img"></div>
    <p>保存成功</p>
  </ng-container>
  
  <ng-container *ngIf="type === 'untopic'">
    <div class="msg-img-untopic msg-img"></div>
    <p>请先完善题本管理</p>
  </ng-container>

  <ng-container *ngIf="type === 'error'">
    <div class="msg-img-tips msg-img"></div>
    <p>{{message}}</p>
  </ng-container>
  
</div>

<!-- <div class="msg-warp">
  <ng-container *ngIf="type === 'deleted'">
    <img class="msg-img" src="../../../assets/images/msg_deleted.png" alt="">
    <p>已删除</p>
  </ng-container>

  <ng-container *ngIf="type === 'unInviteType'">
    <img class="msg-img" src="../../assets/images/mag_invite_type.png" alt="">
    <p>请选择邀请方式</p>
  </ng-container>

  <ng-container *ngIf="type === 'invited'">
    <img class="msg-img-invited" src="../../assets/images/msg_invited.png" alt="">
    <p>邀请已发送</p>
  </ng-container>

  <ng-container *ngIf="type === 'success'">
    <img class="msg-success" src="../../assets/images/msg_success.png" alt="">
    <p>保存成功</p>
  </ng-container>

  <ng-container *ngIf="type === 'untopic'">
    <img class="msg-img" src="../../assets/images/msg_topic.png" alt="">
    <p>请先完善题本管理</p>
  </ng-container>

  <ng-container *ngIf="type === 'error'">
    <img class="msg-img" src="../../assets/images/tips_err.png" alt="">
    <p>{{message}}</p>
  </ng-container>
  
</div> -->