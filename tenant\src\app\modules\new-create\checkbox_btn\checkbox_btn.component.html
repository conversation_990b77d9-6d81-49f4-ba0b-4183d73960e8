<div class="checkbox_btn">
  <span
    *ngFor="let e of options"
    [ngClass]="
      this.model.length && this.model.indexOf(e.value) > -1 ? 'checked' : ''
    "
    (click)="setValue(e)"
  >
    <!-- <section> -->
    <!-- <mat-checkbox [disabled]="disabled"
                        [checked]="this.model.length && this.model.indexOf(e.id) > -1"
                        [value]="e[value]"
                        (change)="setValue(e)"> -->
    {{ e[display] }}
    <!-- </mat-checkbox> -->
    <!-- </section> -->
  </span>
</div>
