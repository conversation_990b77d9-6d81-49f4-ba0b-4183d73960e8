<div class="title">
    报告下载  <span>请选择需要下载的类别</span>
</div>

<div class="cat" *ngFor="let cat of catList">
    <span *ngIf="isShowCat(cat)">
        {{cat.name}}
    </span>
    <div class="items">
        <ng-container *ngFor="let item of cat.items">
            <label *ngIf="(!item.needAdmin || isAdmin) && item.isHasPermission" nz-checkbox [(ngModel)]="item.isSelected" (ngModelChange)="getSelectActions(true)">
                <span *ngIf="loading[item.id] === true && item.isSelected === true">
                    <i nz-icon nzType="loading" nzTheme="outline"></i>
                    <span style="font-size: 12px; color: #f56a00; margin: 0 5px;">&#8595;</span>
                </span>
                {{item.name}}
            </label>
        </ng-container>
    </div>
</div>

<div style="margin-left: 15px; margin-bottom: 15px;" *ngIf="this.loading.totol > 1">
    标准报告将分成&nbsp;{{this.loading.totol}}&nbsp;个文件下载，已完成&nbsp;：{{this.loading.done}}
</div>

<div class="footer">

    <ng-template #pop>
        <div style="display: flex;">
            <i style="color: coral; font-size: 18px; position: relative; top: 3px;" nz-icon nzType="exclamation-circle" nzTheme="outline"></i>
            &nbsp;&nbsp;
            <div>
                <div>
                    有 {{inProgressCount}} 份文件正在生成。
               </div>
               <div>
                   确定不等待，继续下载吗？
               </div>
            </div>
        </div>
    </ng-template>

    <button *ngIf="reportChecked && inProgressCount > 0; else btn1" nz-button class="iptBtn" 
            nz-popconfirm
            [nzPopconfirmTitle]="pop"
            nzPopconfirmPlacement="right"
            (nzOnConfirm)="ok()"
            [nzLoading]="loading.dimScore || loading.answerData || loading.zh_CN">
        <span>确认</span>
    </button>

    <ng-template #btn1>
        <button nz-button class="iptBtn" (click)="ok()"  [nzLoading]="loading.dimScore || loading.answerData || loading.zh_CN">
            <span>确认</span>
        </button>
    </ng-template>

</div>
