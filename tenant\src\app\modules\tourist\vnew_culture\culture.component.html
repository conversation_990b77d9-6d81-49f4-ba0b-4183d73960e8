<div class="content client-width">
    <layout-header class="vx-default__header" [showtitle]="showtitle" [tourist]="tourist" [isNeedLogin]="isNeedLogin"></layout-header>

    <section class="s1">
        <div class="client-width">
            <div class="s1-l">
                <h5>培养 1v1 </h5>
                <p>借助科学建构的测评模型，帮助企业系统化诊断员工能力现状，精准把握培养重点。培养结束后搭配训后测评，以量化数据展现能力提升的程度，简明直接地评估培训效果，进一步提示下个培养周期的方向。 </p>
                <button *ngIf="!tourist" class="btn" (click)="downloadReport()">下载报告样例</button>
                <button *ngIf="tourist" class="btn" routerLink="/new-activity">即刻体验</button>
            </div>
            <div class="s1-r">
                <img src="assets/images/v_culture.png" alt="">
            </div>
        </div>
    </section>

    <section class="s2">
        <div class="client-width">
            <h5>在岗员工这么多，如何定向培养高潜人才？</h5>
            <img src="assets/images/v_culture_1.png" alt="">
        </div>
    </section>

    <section class="s3" style="display: flex;justify-content: center">
        <div class="client-width">

            <img src="assets/images/v_culture_2.png" alt="">
        </div>
    </section>

    <section class="s2">
        <div class="client-width" style="display: flex;justify-content: center;">
            <div style="width: 160px;
            line-height: 38px;color: #fff;
            background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 19px;cursor: pointer;" (click)="gotoHome('create')">
                即刻体验
            </div>
        </div>
    </section>

    <app-footer></app-footer>
</div>