<div class="con">

    <div [hidden]="page !== 1">
        <ul class="news_ul">
            <div class="name">
                <div class="new_title">
                    <div class="label"><span style="color: red;">*</span>指数/维度</div>
                    <div class="second_lab">
                        <span (click)="getTips()">指数/维度管理</span>
                        <div class="tipscard" *ngIf="showcard">
                            <div class="card-title">指数/维度管理</div>
                            <ul class="list_ul">
                                <li style="flex: 1; margin-right: 10px;">
                                    <input nz-input placeholder="请输入文字" [ngModelOptions]="{standalone: true}" [(ngModel)]="valuewords" (blur)="test()"
                                        (keydown.enter)="test()" />
                                </li>
                                <li (click)="addlist()" style="color: #419EFF;">
                                  <img alt="" src="../../../../assets/images/add-condition.png" style="margin-right: 10px;"/> 添加
                                </li>
                            </ul>
                            
                            <ul class="table_ul">
                                <li>
                                    <div>类别</div>
                                    <div>名称</div>
                                </li>
                                <div class="line_div">
                                    <li class="line_list" *ngFor="let item of namelist;let i = index">
                                        <ng-container *ngIf="item.checked" >
                                            <div>
                                                <span>{{i+1}}.</span>
                                                <nz-select style="margin-left: 4px;width: 96px;" [ngModelOptions]="{standalone: true}" [nzDisabled]="item.id" nzPlaceHolder="请选择"
                                                    [(ngModel)]="item.type">
                                                    <nz-option *ngFor="let p of typelist" [nzValue]="p.value"
                                                        [nzLabel]="p.label" ></nz-option>
                                                </nz-select>
                                            </div>
                                            <div style="display: flex;align-items: center;">
                                                <input nz-input placeholder="请输入名称(中)" [ngModelOptions]="{standalone: true}" [(ngModel)]="item.name.zh_CN" />
                                                <input style="margin-left: 6px;" [ngModelOptions]="{standalone: true}" nz-input placeholder="请输入名称(英)"
                                                    [(ngModel)]="item.name.en_US" />
                                            </div>
                                            <div>
                                                <i nz-icon nzType="delete" nzTheme="twotone"
                                                    (click)="deleteTips($event,item.id,i)"></i>
                                            </div>
                                        </ng-container>
                                    </li>
                                </div>
                                <li class="li_footer" *ngIf="namelist.length != 0">
                                    <button class="btn_cancel" nz-button nzType="default" (click)="cannelcard()">取消</button>
                                    <button class="btn_confirm" nz-button nzType="primary" (click)="commitcard()">确认</button>
                                </li>
                            </ul>
                        </div>
                      </div>
                </div>
                <div class="input">
                    <nz-select class="select" [(ngModel)]="selectedProvincecus" nzAllowClear nzPlaceHolder="请选择指数" nzShowSearch>
                        <nz-option *ngFor="let p of provinceDatacus" [nzCustomContent]="true" [nzValue]="p.id" [nzLabel]="p.name.zh_CN">
                            <div nz-tooltip [nzTooltipTitle]="p.name.zh_CN">{{p.name.zh_CN}}</div>
                        </nz-option>
                    </nz-select>

                    <nz-select class="select" [(ngModel)]="selectedProvinceone" nzAllowClear nzPlaceHolder="请选择一级维度" nzShowSearch>
                        <nz-option *ngFor="let p of provinceDataone" [nzCustomContent]="true" [nzValue]="p.id" [nzLabel]="p.name.zh_CN">
                            <div nz-tooltip [nzTooltipTitle]="p.name.zh_CN">{{p.name.zh_CN}}</div>
                        </nz-option>
                    </nz-select>

                    <nz-select class="select" [(ngModel)]="selectedProvincetwo" nzAllowClear nzPlaceHolder="请选择二级维度" nzShowSearch>
                        <nz-option *ngFor="let p of provinceDatatwo" [nzCustomContent]="true" [nzValue]="p.id" [nzLabel]="p.name.zh_CN">
                            <div nz-tooltip [nzTooltipTitle]="p.name.zh_CN">{{p.name.zh_CN}}</div>
                        </nz-option>
                    </nz-select>
                    <nz-select class="select" [(ngModel)]="selectedProvincethree" nzAllowClear nzPlaceHolder="请选择三级维度" nzShowSearch>
                        <nz-option *ngFor="let p of provinceDatathree" [nzCustomContent]="true" [nzValue]="p.id" [nzLabel]="p.name.zh_CN">
                            <div nz-tooltip [nzTooltipTitle]="p.name.zh_CN">{{p.name.zh_CN}}</div>
                        </nz-option>
                    </nz-select>
                </div>
            </div>
            <!-- <div class="name_2">
                <div class="label">人口标签</div>
                <div class="input">
                    <nz-select class="select" nzMode="tags" nzPlaceHolder="请选择标签" [(ngModel)]="selectedProvince"
                        (ngModelChange)="provincechoose($event)">
                        <nz-option-group *ngFor="let item of PrismaReportData" [nzLabel]="item.name.zh_CN">
                            <nz-option *ngFor="let p of item.children" [nzValue]="p.id" [nzLabel]="p.name.zh_CN">
                            </nz-option>
                        </nz-option-group>
                    </nz-select>
                </div>
            </div> -->
        </ul>

      <div class="title">
        <div class="label">
            <span style="color: red;">*</span>分析对象
        </div>
        <!-- <button nz-button [nzSize]="'small'" nzType="link" (click)="clearOptionTree()">
            <span>清空选项</span>
        </button> -->
      </div>
      <div class="vi-tab">
        <ul>
          <li [ngClass]="{ active: tabIndex === 1 }" (click)="tabIndex = 1">组织架构</li>
          <li [ngClass]="{ active: tabIndex === 2 }" (click)="tabIndex = 2">人口信息</li>
        </ul>
        <div class="vi-tab-content">
          <div [hidden]="tabIndex !== 1">
            <div class="vi-tab-content_top">
              <div class="vi-tab-content_title">
                <div>
                  <label style="width: 80px;" nz-checkbox [(ngModel)]="treeOrgAll"  (ngModelChange)="treeOrgChangeAll($event)">全选</label>
                </div>
                <nz-input-group nzCompact style="width: 240px;">
                    <nz-select [(ngModel)]="language" style="width: 80px;">
                        <nz-option [nzLabel]="'中文'" [nzValue]="'zh_CN'"></nz-option>
                        <nz-option [nzLabel]="'英文'" [nzValue]="'en_US'"></nz-option>
                    </nz-select>
                    <input type="text" nz-input [(ngModel)]="organizationName[language]" placeholder="请输入名称" style="width: 160px;" />
                </nz-input-group>
              </div>
              <nz-input-group [nzPrefix]="suffixIconSearch1" style="width: 200px;">
                <input style="border-radius:15px;" type="text" nz-input placeholder="请输入"
                [(ngModel)]="searchNode2" />
              </nz-input-group>

              <ng-template #suffixIcon>
                  <i nz-icon nzType="search"></i>
              </ng-template>

              <ng-template #suffixIconSearch1>
                  <img src="./assets/images/icon_search.png">
              </ng-template>
            </div>
            <div class="vi-tab-content_bottom">
              <nz-tree
                #nzTreeOrgComponent
                [nzData]="treeOrg"
                nzCheckable
                [nzSearchValue]="searchNode2"
                [nzCheckedKeys]="defaultCheckedKeys"
                [nzExpandedKeys]="expandedOrgNodes"
                (nzClick)="nzEvent($event)"
                (nzExpandChange)="nzEvent($event)"
                (nzCheckBoxChange)="nzCheck($event)"
              >
              </nz-tree>
            </div>
          </div>
          <div [hidden]="tabIndex !== 2">
            <div class="vi-tab-content_top">
              <label nz-checkbox [(ngModel)]="renkouCheckAll" (ngModelChange)="CheckRenKouAll($event)">全选</label>
              <nz-input-group [nzPrefix]="suffixIconSearch1" style="width: 200px;">
                <input style="border-radius:15px;" type="text" nz-input placeholder="请输入"
                [(ngModel)]="searchNode1" />
              </nz-input-group>

              <ng-template #suffixIcon>
                  <i nz-icon nzType="search"></i>
              </ng-template>

              <ng-template #suffixIconSearch1>
                  <img src="./assets/images/icon_search.png">
              </ng-template>
            </div>
            <div class="vi-tab-content_bottom">
              <nz-tree
                #nzTreeComponent
                [nzData]="treeNodes"
                nzCheckable
                [nzSearchValue]="searchNode1"
                [nzCheckedKeys]="defaultCheckedRenkouKeys"
                [nzExpandedKeys]="expandedRenNodes"
                (nzClick)="nzEvent($event)"
                (nzExpandChange)="nzEvent($event)"
                (nzCheckBoxChange)="CheckRenKou($event)"
              >
              </nz-tree>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 第二页 -->
    <div [hidden]="page !== 2">
      <div class="title">
        <div class="label">
            <span style="color: red;">*</span>组合维度
        </div>
        <button nz-button [nzSize]="'small'" nzType="link" (click)="clearOption(false)">
            <span>清空选项</span>
        </button>
      </div>

      <div class="content">

          <ng-container *ngFor="let tab of catList">

              <div class="list " [ngClass]="{takeRemain: tab.id === 'question'}">

                  <div class="listSearch"
                      style="display: flex; justify-content: space-between; align-items: center; padding: 0 10px; border-bottom: solid 1px #ccc; margin-bottom: 10px; min-height: 53px;">
                      <div style="margin-right: 10px; display: flex; align-items: center;">
                          <label nz-checkbox [(ngModel)]="tab.allChecked"
                              (ngModelChange)="updateAllChecked(tab, $event)" [nzIndeterminate]="tab.indeterminate">
                              {{tab.name}}全选
                          </label>
                      </div>
                      <div style="flex: 1;">
                          <nz-input-group [nzPrefix]="suffixIconSearch1">
                              <input style="border-radius:15px;" type="text" nz-input placeholder="请输入"
                                  [(ngModel)]="tab.searchText" />
                          </nz-input-group>

                          <ng-template #suffixIcon>
                              <i nz-icon nzType="search"></i>
                          </ng-template>

                          <ng-template #suffixIconSearch1>
                              <img src="./assets/images/icon_search.png">
                          </ng-template>
                      </div>
                  </div>

                  <div style="padding: 0 10px;" class="listItem treeScroll">
                      <ng-container *ngFor="let itemData of tab.items">
                          <label
                              *ngIf="itemData.isShow && (!tab.searchText || itemData.label.indexOf(tab.searchText)!== -1 )"
                              style="margin-top: 10px;" nz-checkbox [(ngModel)]="itemData.checked"
                              (ngModelChange)="updateSingleChecked(tab, itemData, $event)">
                              {{itemData.label}}
                          </label>
                      </ng-container>
                  </div>

              </div>

          </ng-container>
      </div>
    </div>

    <div class="action">
        <button class="button2" nz-button [nzSize]="'small'" nzType="primary" (click)="showGroup()">
            <span>已选组合 {{groupList.length}}</span>
        </button>
        <ng-container *ngIf="page === 1">
            <button class="button3" nz-button [nzSize]="'small'" nzType="primary" (click)="next()">
              <span>下一页</span>
            </button>
        </ng-container>
        <ng-container *ngIf="page === 2">
          <button class="button1" nz-button [nzSize]="'small'" nzType="primary" (click)="makeGroup()">
              <span>组合</span>
          </button>
          <button class="button3" nz-button [nzSize]="'small'" nzType="primary" (click)="page = 1">
              <span>上一页</span>
          </button>
        </ng-container>
    </div>

    <div [hidden]="groupHidden" class="group">
        <div class="gHeader">
            <span class="gTitle">
                组合详情
            </span>
            <button nz-button [nzSize]="'small'" nzType="link" (click)="clearGroup()">
                <span>清空所有组合</span>
            </button>
        </div>

        <div class="gContent treeScroll">
            <nz-collapse>
                <nz-collapse-panel *ngFor="let group of groupList" [nzHeader]="extraTplname" [nzActive]="group.active"
                    [nzExtra]="extraTpl">
                    <div *ngFor="let det of group.detailList">
                        {{det.desc}}
                    </div>

                    <ng-template #extraTplname style="width:250px">
                        <span class="header_tit" nz-tooltip
                            [nzTooltipTitle]="group.groupnames">
                            {{group.groupnames}}
                        </span>
                    </ng-template>
                    <ng-template #extraTpl>
                        <div style="position: relative; top: -5px;">
                            <button nz-button nzType="default" nzShape="circle" (click)="edit($event, group)">
                                <i nz-icon nzType="edit" nzTheme="outline"></i>
                            </button>
                            <button nz-button nzType="default" nzShape="circle" (click)="delete($event, group.id)">
                                <i nz-icon nzType="delete" nzTheme="outline"></i>
                            </button>
                        </div>
                    </ng-template>
                </nz-collapse-panel>
            </nz-collapse>
        </div>

        <div class="gAction">
            <button nz-button [nzSize]="'small'" nzType="link" (click)="showGroup()">
                <span>收起已选组合</span>
            </button>
        </div>

    </div>


</div>