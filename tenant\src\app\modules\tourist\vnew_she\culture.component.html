<div class="content client-width">
    <layout-header class="vx-default__header" [showtitle]="showtitle" [tourist]="tourist" [isNeedLogin]="isNeedLogin"></layout-header>

    <section class="s1">
        <div class="client-width">
            <div class="s1-l" >
                <h5>社招1X  </h5>
                <p>基于岗位的深度分析掌握“核心素质要求”，描绘能够充分胜任目标岗位的优秀人才画像。将候选人的测评结果与优秀人才画像进行对比，理解候选人与岗位的匹配情况，快速筛选适合的人才。 </p>
                <button *ngIf="!tourist" class="btn"  (click)="gotoHome('download')">下载报告样例</button>
                <button *ngIf="tourist" class="btn" routerLink="/new-activity">即刻体验</button>
            </div>
            <div class="s1-r">
                <img src="assets/images/v_she_0.png" alt="">
            </div>
        </div>
    </section>

    <section class="s2">
        <div class="client-width">
            <h5>关键核心岗位，人才如同公司中流砥柱。</h5>
            <img src="assets/images/v_she_1.png" alt="">
        </div>
    </section>

    <section class="s3" style="display: flex;justify-content: center">
        <div class="client-width">
            <h5>1份整合报告</h5>
            <p style="padding: 10px  0;text-align: center;">多个测评整合一份报告，总览结果直击适配程度。</p>
            <img src="assets/images/v_she_2.png" alt="">
        </div>
    </section>

    <section class="s4" style="display: flex;justify-content: center">
        <div class="client-width" style="display: flex;flex-direction: column;align-items: center;">
            <h5>测评价值</h5>
            <img src="assets/images/v_she_3.png" alt="">
        </div>
    </section>

    <section class="s2">
        <div class="client-width" style="display: flex;justify-content: center;">
            <div style="width: 160px;
            line-height: 38px;color: #fff;
            background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 19px;cursor: pointer;" (click)="gotoHome('create')">
            即刻体验
            </div>
        </div>
    </section>

    <app-footer></app-footer>
  </div>
  