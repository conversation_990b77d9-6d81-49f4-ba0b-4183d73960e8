import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ReportService } from '../../report.service';
import { UploadXHRArgs } from 'ng-zorro-antd/upload';
import { NzMessageService } from 'ng-zorro-antd';
@Component({
  selector: 'object-setting',
  templateUrl: './object-setting.component.html',
  styleUrls: ['./object-setting.component.less'],
})
export class ObjectSettingComponent  {

  @Input() itemId: string = '' // 九宫格id

  @Output() closePopover = new EventEmitter<void>();


  // 分页控制
  totalCount: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;
  pages: number = 0;
  name: string = '';
  listData: any[] = [];
  isDownLoadSpinning: boolean = false;
  tableLoading: boolean = false;
  fileList: any[] = [] // 批量上传的文件列表
  unRefineCount: number = 0;

  constructor(
    private rptService: ReportService,
    private msgServ: NzMessageService, 
  ) {}
  ngOnInit() {
    this.loadData();
  }
  pageChange(page?: any){
    this.currentPage = page;
    this.loadData();
  }
  loadData(){
    this.tableLoading = true;
    const params = {
        itemId: this.itemId,
        name: this.name,
        page: {
          current: this.currentPage,
          pages: 0,
          searchCount: true,
          size: this.pageSize
        }
    }
    this.rptService.getMappingReportSudokuObjectListByPage(params).subscribe(res =>{
      this.tableLoading = false;
      const {data, page} = res;
      this.totalCount = page.total;
      this.pages =  page.pages;
      this.listData = data;
    })
    this.rptService.getMappingReportSudokuItemObjectUnRefineCount(this.itemId).subscribe(res =>{
      this.unRefineCount = res.data || 0;
    })
    
  }
  // 导出
  downLoad() {
    this.isDownLoadSpinning = true
    this.rptService.getObjectDownLoad(this.itemId).subscribe(res => {
      const blob = new Blob([res.body], { type: 'application/vnd.ms-excel' });
      let fileName = res.headers.get('Content-Disposition').split(';')[1].split('filename=')[1];
      const fileNameUnicode = res.headers.get('Content-Disposition').split('filename*=')[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
          fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
          fileName = decodeURIComponent(fileNameUnicode.split('\'\'')[1]);
      }
      const link = document.createElement('a');
      link.setAttribute('href', URL.createObjectURL(blob));
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.isDownLoadSpinning = false;
    })
  }
  /**
   * preview 预览上传的文件
   * @param file
   */
  preview(file) {
    window.open(window.URL.createObjectURL(file.originFileObj));
  }
  /**
  * customReq 上传
  * @param item
  */
  customReq = (item: UploadXHRArgs) => {
    this.fileList = [{
      uid: item.file.uid,
      name: item.file.name,
      status: 'uploading',
      originFileObj: item.file
    }]
    const formData = new FormData();
    formData.append('excel', item.file as any);
    let params = {
      fileType: "." + item.file.name.split(".")[1],
    }
    console.log(item,this.fileList)
    this.uploadExcel(formData, params, item);
  };
  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, params, item) {
    return this.rptService.postObjectUpload(formData, this.itemId).subscribe(
      (res) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.loadData()
          this.msgServ.success('导入成功！')
          this.fileList[0].status = 'done'
        } else {
          this.fileList = [{
            uid: item.file.uid,
            name: item.file.name,
            status: 'error',
            originFileObj: item.file
          }];
        }
      },
      err => {
        item.onError!(err, item.file!);
      }
    )
  }
  // 搜索
  search(){
    this.currentPage = 1;
    this.loadData()
  }
  // 清空
  clear(){
    this.name = '';
    this.currentPage = 1;
    this.loadData()
  }
  // 确认
  confirm(){
    this.rptService.postMappingReportSudokuItemsObjectConfirm(this.itemId).subscribe(res =>{
      if (res.data) {
        this.msgServ.success('对象设置成功！')
        this.closePopover.emit();
      }
    })
  }
}