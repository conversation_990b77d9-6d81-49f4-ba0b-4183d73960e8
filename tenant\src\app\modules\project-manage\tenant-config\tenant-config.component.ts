import { Component, OnInit } from "@angular/core";
import { ProjectManageService } from "../../service/project-manage.service";
import { ActivatedRoute, Router } from "@angular/router";
import { NzMessageService, UploadXHRArgs } from "ng-zorro-antd";
import { isNumber, isArray } from "util";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { HttpClient, HttpEvent } from "@angular/common/http";
import { Observable } from "rxjs";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-tenant-config",
  templateUrl: "./tenant-config.component.html",
  styleUrls: ["./tenant-config.component.less"],
})
export class TenantConfigComponent implements OnInit {
  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/project-manage",
      name: "活动管理",
      Highlight: false,
    },
    {
      path: "",
      name: "租户配置",
      Highlight: true,
    },
  ];
  permission; //更新租户配置权限
  keyValue: string = "";
  selectedValue: string = "";
  norms = []; //常模
  projectId;
  tools = [];
  tenantUrl: string = "/tenant-api";
  listOfData = [
    {
      key: "1",
      name: "<PERSON>",
      age: 32,
      address: "New York No. 1 Lake Park",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      aa: "aa",
      bb: "bb",
    },
  ];
  constructor(
    private projectManageService: ProjectManageService,
    private router: Router,
    private msgServ: NzMessageService,
    private routerInfo: ActivatedRoute,
    private surveySerivce: SurveyApiService,
    private http: HttpClient,
    private customMsg: MessageService,
        public permissionService: PermissionService,
  ) {}

  ngOnInit() {
    this.projectId = this.routerInfo.snapshot.queryParams.projectId;
    this.getReportStyle();
    this.init();

    this.permission = this.permissionService.isPermission();
  }

  init() {
    let params = {
      dimensionName: this.keyValue,
      projectId: this.projectId,
      questionnaireId: this.selectedValue,
    };
    this.projectManageService.getTenantConfigList(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.listOfData = res.data;
        this.listOfData.forEach((item) => {
          if (
            (item as any).algorithm.assessmentNorms &&
            (item as any).algorithm.assessmentNorms.length > 0
          ) {
            (item as any).cm = "";
            (item as any).algorithm.assessmentNorms.forEach((element) => {
              (item as any).cm =
                (item as any).cm + `${element.count}:${element.score};`;
            });
          }

          if ((item as any).algorithmType == "NORM_INTERFACE") {
            (item as any).algorithm.normType =
              (item as any).algorithm.normType || 0;
          }
        });
      }
    });
  }

  textareaToModel(norm) {
    let objs: any[] = [];
    let arr1: string[] = norm.split(";");
    for (let index = 0; index < arr1.length; index++) {
      const element = arr1[index];
      if (element.trim() != "") {
        if (element.indexOf(":") == -1) {
          let msg = `常模的第${index + 1}行数据输入有误,必须含英文分号。`;
          // this.msgServ.create("error", `${msg}`);
          this.customMsg.open("error", msg);
        }
        let arr2: string[] = element.split(":");
        let para1: any = arr2[0].trim();
        let para2: any = arr2[1].trim();
        const valid1 = isNaN(para1);
        const valid2 = isNaN(para2);
        // console.info(`para1= ${para1}, para2= ${para2},valid1= ${valid1},valid2= ${valid2},`);
        if (valid1) {
          let msg = `常模的第${index + 1}行数据输入有误,  ${para1}  不是数字。`;
          // this.msgServ.create("error", `${msg}`);
          this.customMsg.open("error", msg);
        }
        if (valid2) {
          let msg = `常模的第${index + 1}行数据输入有误,  ${para2}  不是数字。`;
          // this.msgServ.create("error", `${msg}`);
          this.customMsg.open("error", msg);
        }
        objs.push({ count: para1, score: para2 });
      }
    }
    return objs;
  }

  getReportStyle() {
    this.projectManageService
      .listByProjectId(this.projectId)
      .subscribe((res) => {
        if (res.result.code === 0) {
          res.data.forEach((element) => {
            this.tools.push({ id: element.id, name: element.name.zh_CN });
          });
        }
      });
  }

  submit() {
    let params = JSON.parse(JSON.stringify(this.listOfData));
    params.forEach((element) => {
      if (element.cm) {
        element.algorithm.assessmentNorms = this.textareaToModel(element.cm);
      }
    });
    this.projectManageService
      .updateTenantConfigList(params)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.router.navigateByUrl("/project-manage/home");
        }
      });
  }

  /** importTenantCongif excel批量更改题目包
   * @param item
   */
  importTenantCongif = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.importTenantCongifReq(formData, item);
  };

  /**
   * importTenantCongifReq
   */
  importTenantCongifReq(formData, item) {
    let api: string = `${this.tenantUrl}/sagittarius/report/package/readExcelUpdateBatch?projectId=${this.projectId}`;
    let sub: Observable<any>;
    sub = this.http.post(api, formData);

    return sub.subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          this.msgServ.success("导入成功");
          // TODO 成功后要刷新页面
          this.init();
        } else {
          if (res.result.code < 10000) {
            // this.msgServ.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }
  /* exportTenantCongif 导出算法 */
  exportTenantCongif = () => {
    let api: string = `${this.tenantUrl}/sagittarius/report/package/exportExcel`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any>;

    let json = {
      projectId: this.projectId,
    };

    sub = this.http.post(api, json, param);

    sub.subscribe(
      (data) => {
        this.downFile(data);
      },
      (error1) => {
        // this.msgServ.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  };

  downFile(data) {
    this.showBlobErrorMessage(data);
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];
    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  showBlobErrorMessage(data: any) {
    let body = data.body;
    if (body.type === "application/json") {
      let that = this;
      const reader = new FileReader();
      reader.readAsText(body, "utf-8");
      reader.onload = () => {
        // 处理报错信息
        // JSON.parse(reader.result) 拿到报错信息
        let resp: any = JSON.parse(reader.result + "");
        let code: number = resp.result.code;
        let errMsg: string = resp.result.message;

        if (code !== 0) {
          // that.msgServ.error(`${errMsg}，请联系管理员。`);
          this.customMsg.open("error", `${errMsg}，请联系管理员。`);
        }
      };
    }
  }
}
