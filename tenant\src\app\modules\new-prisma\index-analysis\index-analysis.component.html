<!--
    *@author: <PERSON>
    *@Date: 2023/09/11
    *@content: 自定义指数分析
-->
<div class="box">
  <header>
    <div>
      <button class="btn3" nz-button nzType="primary" (click)="showGroup()">
        已选关联 ( {{ groupList.length }} )
      </button>
      <button class="btn4" nz-button nzType="default" (click)="makeGroup()">
        关联
      </button>
    </div>
    <!-- <div>
      <nz-upload
        [nzCustomRequest]="customReq"
        [nzShowUploadList]="false"
        style="display: flex;align-items: flex-start;"
      >
        <button nz-button nzType="link">
          <i class="iconfont icon-icon_import"></i> 导入
        </button>
      </nz-upload>
      <button
        nz-button
        nzType="link"
        [nzLoading]="isDownLoadSpinning"
        (click)="downLoad()"
      >
        <i class="iconfont icon-icon_export"></i> 导出
      </button>
    </div> -->
  </header>
  <div [hidden]="groupHidden" class="group">
    <div class="gHeader">
      <span class="gTitle">
        关联详情 <a (click)="bgGetGroupList()">更新数据</a>
      </span>
      <button nz-button [nzSize]="'small'" nzType="link" (click)="clearGroup()">
        <span>清空所有关联</span>
      </button>
    </div>

    <div class="gContent treeScroll">
      <nz-collapse>
        <nz-collapse-panel
          *ngFor="let group of groupList"
          [nzHeader]="extraTplname"
          [nzActive]="group.active"
          [nzExtra]="extraTpl"
        >
          <div *ngFor="let det of group.detailList">
            {{ det.desc }}
          </div>

          <ng-template #extraTplname style="width:250px">
            <span
              class="header_tit"
              nz-tooltip
              [nzTooltipTitle]="group.groupnames"
            >
              {{ group.groupnames }}
            </span>
          </ng-template>
          <ng-template #extraTpl>
            <div style="display: flex;">
              <div (click)="edit($event, group)" class="edit_icon_close"></div>
              <div (click)="delete($event, group.id)" class="del_icon"></div>
            </div>
          </ng-template>
        </nz-collapse-panel>
      </nz-collapse>
    </div>

    <div class="gAction">
      <button nz-button [nzSize]="'small'" nzType="link" (click)="showGroup()">
        <span>收起已选关联</span>
      </button>
    </div>
  </div>
  <div class="form-top">
    <form
      nz-form
      [nzLayout]="'vertical'"
      [formGroup]="validateForm"
      (ngSubmit)="submitForm(validateForm.value)"
    >
      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzRequired>
          <span style="font-weight: 600;">指数/维度</span>
        </nz-form-label>
        <div nz-col [nzSpan]="18" style="text-align: right;" class="form-title">
          <div class="second_lab">
            <span (click)="getTips()"
              >指数/维度管理
              <span nz-icon nzType="down" nzTheme="outline"></span
            ></span>
            <div class="tipscard" *ngIf="showcard">
              <div class="card-title">指数/维度管理</div>
              <ul class="list_ul">
                <li style="flex: 1; margin-right: 10px;">
                  <input
                    nz-input
                    placeholder="请输入文字"
                    [ngModelOptions]="{ standalone: true }"
                    [(ngModel)]="valuewords"
                    (blur)="test($event)"
                    (keydown.enter)="test($event)"
                  />
                </li>
                <li (click)="addlist()" style="color: #419EFF;">
                  <img
                    alt=""
                    src="./assets/images/add-condition.png"
                    style="margin-right: 10px;"
                  />
                  添加
                </li>
              </ul>

              <ul class="table_ul">
                <li>
                  <div>类别</div>
                  <div>名称</div>
                </li>
                <div class="line_div">
                  <li
                    class="line_list"
                    *ngFor="let item of namelist; let i = index"
                  >
                    <ng-container *ngIf="item.checked">
                      <div>
                        <nz-select
                          style="margin-left: 4px;width: 96px;"
                          [ngModelOptions]="{ standalone: true }"
                          [nzDisabled]="item.id"
                          nzPlaceHolder="请选择"
                          [(ngModel)]="item.type"
                        >
                          <nz-option
                            *ngFor="let p of typelist"
                            [nzValue]="p.value"
                            [nzLabel]="p.label"
                          ></nz-option>
                        </nz-select>
                      </div>
                      <div style="display: flex;align-items: center;">
                        <input
                          nz-input
                          placeholder="请输入名称(中)"
                          [ngModelOptions]="{ standalone: true }"
                          [(ngModel)]="item.name.zh_CN"
                        />
                        <input
                          style="margin-left: 6px;"
                          [ngModelOptions]="{ standalone: true }"
                          nz-input
                          placeholder="请输入名称(英)"
                          [(ngModel)]="item.name.en_US"
                        />
                      </div>
                      <div
                        class="del-icon"
                        (click)="deleteTips($event, item.id, i)"
                      ></div>
                    </ng-container>
                  </li>
                </div>
                <li class="li_footer" *ngIf="namelist.length != 0">
                  <button
                    class="btn_cancel"
                    nz-button
                    nzType="default"
                    (click)="cannelcard()"
                  >
                    取消
                  </button>
                  <button
                    class="btn_confirm"
                    nz-button
                    nzType="primary"
                    (click)="commitcard()"
                  >
                    确认
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div nz-row [nzGutter]="16" style="padding-left: 8px;">
          <nz-form-control [nzSpan]="6">
            <nz-select
              style="width: 100%;"
              formControlName="userName"
              [(ngModel)]="selectedProvincecus"
              nzAllowClear
              nzShowSearch
              nzPlaceHolder="请选择指数"
            >
              <nz-option
                *ngFor="let p of provinceDatacus"
                [nzCustomContent]="true"
                [nzValue]="p.id"
                [nzLabel]="p.name.zh_CN"
              >
                <div nz-tooltip [nzTooltipTitle]="p.name.zh_CN">
                  {{ p.name.zh_CN }}
                </div>
              </nz-option>
            </nz-select>
          </nz-form-control>
          <div nz-col [nzSpan]="6">
            <nz-select
              style="width: 100%;"
              [(ngModel)]="selectedProvinceone"
              [ngModelOptions]="{ standalone: true }"
              nzAllowClear
              nzShowSearch
              nzPlaceHolder="一级维度名称"
            >
              <nz-option
                *ngFor="let p of provinceDataone"
                [nzCustomContent]="true"
                [nzValue]="p.id"
                [nzLabel]="p.name.zh_CN"
              >
                <div nz-tooltip [nzTooltipTitle]="p.name.zh_CN">
                  {{ p.name.zh_CN }}
                </div>
              </nz-option>
            </nz-select>
          </div>
          <div nz-col [nzSpan]="6">
            <nz-select
              style="width: 100%;"
              [(ngModel)]="selectedProvincetwo"
              [ngModelOptions]="{ standalone: true }"
              nzAllowClear
              nzShowSearch
              nzPlaceHolder="请选择二级维度"
            >
              <nz-option
                *ngFor="let p of provinceDatatwo"
                [nzCustomContent]="true"
                [nzValue]="p.id"
                [nzLabel]="p.name.zh_CN"
              >
                <div nz-tooltip [nzTooltipTitle]="p.name.zh_CN">
                  {{ p.name.zh_CN }}
                </div>
              </nz-option>
            </nz-select>
          </div>
          <div nz-col [nzSpan]="6">
            <nz-select
              style="width: 100%;"
              [(ngModel)]="selectedProvincethree"
              [ngModelOptions]="{ standalone: true }"
              nzAllowClear
              nzShowSearch
              nzPlaceHolder="请选择三级维度"
            >
              <nz-option
                *ngFor="let p of provinceDatathree"
                [nzCustomContent]="true"
                [nzValue]="p.id"
                [nzLabel]="p.name.zh_CN"
              >
                <div nz-tooltip [nzTooltipTitle]="p.name.zh_CN">
                  {{ p.name.zh_CN }}
                </div>
              </nz-option>
            </nz-select>
          </div>
        </div>
      </nz-form-item>
    </form>
  </div>
  <div class="title">
    <div class="label"><span style="color: red;">*</span>组合维度</div>
    <button
      nz-button
      [nzSize]="'small'"
      nzType="link"
      (click)="clearOption(false)"
    >
      <span>清空选项</span>
    </button>
  </div>

  <div class="content">
    <ng-container *ngFor="let tab of catList">
      <div class="list " [ngClass]="{ takeRemain: tab.id === 'question' }">
        <div
          class="listSearch"
          style="display: flex; justify-content: space-between; align-items: center; padding: 0 10px; border-bottom: solid 1px #ccc; margin-bottom: 10px; min-height: 53px;"
        >
          <div style="margin-right: 10px; display: flex; align-items: center;">
            <label
              nz-checkbox
              [(ngModel)]="tab.allChecked"
              (ngModelChange)="updateAllChecked(tab, $event)"
              [nzIndeterminate]="tab.indeterminate"
            >
              {{ tab.name }}全选
            </label>
          </div>
          <div style="flex: 1;">
            <nz-input-group [nzPrefix]="suffixIconSearch1">
              <input
                style="border-radius:15px;"
                type="text"
                nz-input
                placeholder="请输入"
                [(ngModel)]="tab.searchText"
              />
            </nz-input-group>

            <ng-template #suffixIcon>
              <i nz-icon nzType="search"></i>
            </ng-template>

            <ng-template #suffixIconSearch1>
              <img src="./assets/images/icon_search.png" />
            </ng-template>
          </div>
        </div>

        <div style="padding: 0 10px;" class="listItem treeScroll">
          <ng-container *ngFor="let itemData of tab.items">
            <label
              *ngIf="
                itemData.isShow &&
                (!tab.searchText ||
                  itemData.label.indexOf(tab.searchText) !== -1)
              "
              style="margin-top: 10px; width: 100%; margin-left: 0;"
              nz-checkbox
              [(ngModel)]="itemData.checked"
              (ngModelChange)="updateSingleChecked(tab, itemData, $event)"
            >
              {{ itemData.label }}
            </label>
          </ng-container>
        </div>
      </div>
    </ng-container>
  </div>
  <!-- <footer>
    <button class="btn_cancel" nz-button nzType="default" (click)="ok()">确认</button>
    <button class="btn_confirm" nz-button nzType="default" (click)="ok()">关闭</button>
  </footer> -->
</div>
<div class="footer">
  <button nz-button nzType="default" (click)="ok()">关闭</button>
</div>
