<div class="tipDimension">
  <!-- <nz-card [nzTitle]="reportType =='CA'?nzTitle:null" [nzExtra]="extraTemplate"> -->
  <nz-card [nzTitle]="reportType == 'CA' ? nzTitle : null">
    <ng-template #nzTitle>
      <ng-container>
        <div class="title">
          <div class="recommend">
            <div></div>
            <span>推荐指标</span>
          </div>
          <div>
            已选：
            <span *ngIf="radiolist.length != 0"
              >{{ radiolist[0]?.name.zh_CN }}岗位；</span
            >
            <span *ngIf="radioCodelist.length != 0">{{
              radioCodelist[0]?.name
            }}</span>
          </div>
        </div>
      </ng-container>
    </ng-template>
    <!-- <ng-template #extraTemplate>
            <span class="empty">清空条件</span>
        </ng-template> -->
    <nz-checkbox-wrapper
      style="width: 100%;"
      (nzOnChange)="wrapperChange($event)"
    >
      <div nz-row *ngFor="let sup of dimensions" class="row">
        <div nz-col nzSpan="4" class="sup">
          <ng-container
            *ngIf="sup.name.zh_CN && sup.name.zh_CN.length > 5; else noSupTip"
          >
            <span nz-tooltip [nzTooltipTitle]="sup.name.zh_CN">{{
              sup.name.zh_CN.substr(0, 5) + "..." || "--"
            }}</span>
          </ng-container>
          <ng-template #noSupTip
            ><span>{{ sup.name.zh_CN || "--" }} </span></ng-template
          >
        </div>
        <div nz-col nzSpan="20" class="son">
          <div nz-row>
            <div
              *ngFor="let son of sup.detailedScoreChildDimensions"
              nz-col
              nzSpan="6"
              class="sonItem"
            >
              <label *ngIf="!sup.isSelect">{{ son.name.zh_CN }}</label>
              <label
                *ngIf="sup.isSelect"
                [nzDisabled]="isdisabled"
                nz-checkbox
                [nzValue]="son.code"
                [ngModel]="son.isSelected"
              >
                <ng-container *ngIf="son.description; else noDescription">
                  <span
                    nz-tooltip
                    [ngStyle]="{ color: son.color }"
                    [nzTooltipTitle]="
                      son.name.zh_CN + '：' + son.description.zh_CN
                    "
                    >{{ son.name.zh_CN }}</span
                  >
                </ng-container>
                <ng-template #noDescription>
                  <span>{{ son.name.zh_CN }}</span>
                </ng-template>
              </label>
            </div>
          </div>
        </div>
      </div>
    </nz-checkbox-wrapper>
  </nz-card>
</div>
