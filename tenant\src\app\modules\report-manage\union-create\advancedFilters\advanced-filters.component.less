.control {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.condition-box {
  margin-bottom: 16px;
}

.condition {
  // 条件下拉菜单样式
  width: 70px;
}

.upload_button {
  ::ng-deep {
    .ant-upload {
      width: 100%;
    }
    .ant-btn {
      width: 100%;
      border-style: dashed;
      span {
        width: 100%;
        line-height: 30px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
.ant-input {
  border: 1px solid #d9d9d9 !important;
}

.border-error {
  ::ng-deep {
    .ant-select-selection {
      border-color: #f5222d;
    }
  }
}

::ng-deep .filter-error {
  border: 1px #f5222d solid;

  .ant-select-selection--multiple {
    border-color: #f5222d;
  }
}

.text-blue {
  color: #409eff;
}

.box {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  width: 138px;
  height: 32px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e4e4e4;
  margin: 0 0 0 8px;
  padding: 3px 11px;
}

::ng-deep .ant-popover-content:has(.filter_boxs) {
  border-radius: 4px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .ant-popover-inner {
    .ant-popover-title {
      padding: 0 !important;
      border: none !important;
    }

    .ant-popover-inner-content {
      padding: 0 !important;
    }
  }
}

.filter-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  cursor: pointer;

  .arrow {
    font-size: 12px;
    color: #bfbfbf;
    transition: transform 0.3s;
  }

  .arrow-open {
    transform: rotate(180deg);
    color: #409eff;
  }
}

.filter_boxs {
  // width: 755px;
  // height: 521px;
  background: #ffffff;

  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 16px;
    margin-bottom: 10px;
    span:first-child {
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #262626;
      line-height: 28px;
    }

    span:last-child {
      font-size: 12px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #595959;
      line-height: 17px;

      i {
        font-style: normal;
        color: #409eff;
        margin: 0 4px;
      }
    }
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0 16px;
    margin-bottom: 16px;
    div:first-child {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .condition {
        margin: 0 8px;
      }
    }
    div:last-child {
      cursor: pointer;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #409eff;
      line-height: 20px;
      span {
        margin-left: 6px;
      }
    }
  }
  .content {
    padding: 0 16px;
    .empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 260px;

      p {
        padding-top: 26px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #495970;
        line-height: 20px;
        margin-bottom: 34px;
      }
    }

    .option-box {
      // 选项
      height: 260px;

      .option-scroll {
        overflow-y: auto;
        overflow-x: hidden;
        height: calc(260px - 70px);
      }

      .option-scroll::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      .option-scroll::-webkit-scrollbar-track {
        background-color: #F1F1F1;
        box-shadow: none;
        border-radius: 2px;
      }

      .option-scroll::-webkit-scrollbar-thumb {
        // background-color: #e9e9e9;
        background-color: #C1C1C1;
      }

      .option-scroll::-webkit-scrollbar-thumb:hover {
        background: #909090;
      }

      .option-scroll::-webkit-scrollbar-corner {
        background: #179a16;
      }

      // 选项
      .option_empty {
        width: 100%;
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        border: 1px dashed #d9d9d9;
        color: #d9d9d9;
        text-align: center;
        cursor: default;
        font-size: 14px;
        > i {
          font-size: 12px;
          margin-right: 4px;
        }
      }
      .option_del {
        cursor: pointer;
        font-size: 14px;
        color: #262626;
        line-height: 32px;
        &:hover {
          color: #f5222d;
        }
      }
    }
  }

  footer {
    width: 100%;
    border-top: 1px solid #ececec;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px 16px;
    .button {
      border-radius: 15px;
      width: 69px;
      height: 30px;
      border-color: #409eff;
    }
  }
}

.min {
  width: 500px;
}
