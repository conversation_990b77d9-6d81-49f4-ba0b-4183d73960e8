import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-tip-type',
  templateUrl: './tip-type.component.html',
  styleUrls: ['./tip-type.component.less']
})
export class TipTypeComponent implements OnInit {

  @Input() items: any[];
  @Input() checked?: string[];
  @Input() isdisabled: boolean;

  @Output() checkItem = new EventEmitter<any>();
  @Output() onCheckType = new EventEmitter<any>();
  constructor(
  ) { }


  ngOnInit() {
    // console.log(11111, this.items)
  }
  changeTypes(value: string[]): void {
      this.checkItem.emit(value)
  }
  clickType(val:any){
    this.onCheckType.emit(val)
  }
}
