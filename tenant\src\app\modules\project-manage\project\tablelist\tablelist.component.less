// .flex_li {
// width: 95%;
// display: flex;
// align-items: center;
// justify-content: space-between;
// padding: 20px 0;
// }
.table-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 16px 16px 16px;
  > div {
    display: flex;
    align-items: center;
  }
}
.table_action {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #40a9ff;
  > li {
    cursor: pointer;
  }
}

.right {
  display: flex;
  align-items: center;

  li {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #495970;
    line-height: 20px;

    .iconfont {
      font-size: 18px;
      margin-right: 6px;
    }
    .icon-icon_export {
      font-size: 17px;
    }

    .ant-divider {
      background: #cbcbcb;
    }
  }

  .divider {
    margin: 0 10px;
  }

  .btn {
    cursor: pointer;
  }

  .btn:hover {
    color: #409eff;
    i,
    span {
      color: #409eff;
    }
  }
}

::ng-deep .ant-table-body.ng-star-inserted {
  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }
}
::ng-deep .ant-table-header {
  overflow: hidden !important;
  padding-right: 6px;
  margin-bottom: 0 !important;
}
::ng-deep .ant-table-th-right-sticky.ng-star-inserted {
  // right: -11px !important;
  // padding-right: 22px;
  // right: -6px !important;
  &::after {
    content: "";
    display: block;
    background-color: #fafafa;
    position: absolute;
    width: 58px;
    height: 8px;
    right: -31px;
    bottom: 24px;
    transform: rotate(90deg);
    // border-left: 1px solid #e8e8e8;
    border-right: 1px solid #e8e8e8;
  }
}
// ::ng-deep .ant-table-th-td-sticky.ng-star-inserted {
//   right: -6px !important;
// }
::ng-deep textarea.ant-input {
  scrollbar-color: #c1c1c1 transparent;
  scrollbar-width: thin;
}

::ng-deep .ant-table-pagination.ant-pagination {
  margin: 16px;
}
.bl {
  border-left: 1px solid #e8e8e8;
}
