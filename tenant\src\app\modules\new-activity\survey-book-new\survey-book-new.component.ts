import { Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { NzMessageService, NzModalService, UploadXHRArgs } from "ng-zorro-antd";
import { SubjectEditComponent } from "../subject-edit/subject-edit.component";
import _ from "lodash";
import { SubjectAddComponent } from "../subject-add/subject-add.component";
import { SubjectBatchEditComponent } from "../subject-batch-edit/subject-batch-edit.component";
import { BookSettingComponent } from "../book-setting/book-setting.component";
import { ThrowStmt } from "@angular/compiler";
import { HttpEvent } from "@angular/common/http";

import { ElementRef } from "@angular/core";
import { Subscription, fromEvent } from "rxjs";
import { debounceTime } from "rxjs/operators";

//////////////////////////////
import { SurveyBookModalComponent } from "./modal-content/survey-book-modal.component";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-survey-book-new",
  templateUrl: "./survey-book-new.component.html",
  styleUrls: ["./survey-book-new.component.less"],
})
export class SurveyBookNewComponent implements OnInit {
  @ViewChild("deliveryTable", { static: false }) deliveryView: ElementRef;
  private subscription: Subscription;
  sourceList: any[] = [];

  topicList: any[] = [];

  projectInfo: any;

  // 活动id
  projectId: string;
  //活动状态
  projectType: string;
  // 搜索关键字
  keyWord: string = "";
  // 分页控制
  totalCount: number = 3;
  currentPage: number = 1;
  pageSize: number = 2;

  questionnaireId: string;

  // 语言控制
  lanIndex: number = 0;
  lanKey: string = "zh_CN";
  lans: any[] = [
    { key: "zh_CN", value: "中文" },
    { key: "en_US", value: "ENG" },
  ];
  lan: string = "zh_CN";
  tabSize: string = "small";
  checked = true;
  typeshow = false;
  exportshow = false;
  uploadshow = false;

  tips: any = {
    SINGLE: {
      name: {
        zh_CN: "单选",
        en_US: "Single Choice",
      },
      color: "#9833FF",
      bg: "rgba(152, 51, 255, 0.08)",
    },
    SCALE: {
      name: {
        zh_CN: "量表",
        en_US: "Scale",
      },
      color: "#3372FF",
      bg: "rgba(51, 114, 255, 0.08)",
    },
    MULTIPLE_CHOICE: {
      name: {
        zh_CN: "多选",
        en_US: "Multiple Choice",
      },
      color: "#3372FF",
    },
    ESSAY_QUESTION: {
      name: {
        zh_CN: "开放",
        en_US: "Open Question",
      },
      color: "#24CC9E",
      bg: "rgba(36, 204, 158, 0.08)",
    },
    MULTIPLE_CHOICE_ESSAY_QUESTION: {
      name: {
        zh_CN: "多选开放",
        en_US: "Multiple Choice Open Question",
      },
      color: "#FF8D33",
      bg: "rgba(255, 141, 51, 0.08)",
    },
    isPierceThrough: {
      name: {
        zh_CN: "穿透",
        en_US: "Penetration Question",
      },
      color: "#FF58A6",
      bg: "rgba(255, 88, 166, 0.08)",
    },
  };

  // 批量/删除 -ldx
  deleteIds: any[] = [];
  // 全选 -ldx
  isAllChecked: boolean = false;
  // 全选 -ldx
  disabledAllChecked: boolean = false;
  tableTop: string = "";
  backtype: string;
  permission: boolean;
  permissionShow: boolean;
  reportType: string = "";
  constructor(
    private modalService: NzModalService,
    private routeInfo: ActivatedRoute,
    private msg: NzMessageService,
    private router: Router,
    private projSerivce: ProjectManageService,
    private surveySerivce: SurveyApiService,
    private customMsg: MessageService,
        public permissionService: PermissionService,
  ) {}

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    if (this.permission) {
      this.permissionShow = true;
    } else {
      this.permissionShow = false;
    }

    this.projectId = this.routeInfo.snapshot.queryParams.projectId;
    this.projectType = this.routeInfo.snapshot.queryParams.projectType;
    this.questionnaireId = this.routeInfo.snapshot.queryParams.questionnaireId;
    this.backtype = this.routeInfo.snapshot.queryParams.backtype;
    this.reportType = this.routeInfo.snapshot.queryParams.reportType;

    if (
      this.projectType == "OVER" ||
      this.projectType == "SUSPEND" ||
      this.projectType == "ANSWERING"
    ) {
      this.typeshow = true;
    }
    if (this.projectType == "OVER" || this.projectType == "SUSPEND") {
      this.exportshow = true;
    }
    if (!this.projectType || this.projectType == "ANNOUNCED") {
      this.uploadshow = true;
    }

    if (this.permissionShow && this.backtype === "home") {
      this.typeshow = false;
      this.exportshow = false;
      this.uploadshow = true;
    }

    this.getProject();
    this.loadData();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.tableTop = `${this.deliveryView.nativeElement.clientHeight - 200}px`;
    });
    this.subscription = fromEvent(window, "resize")
      .pipe(debounceTime(100)) // 以免频繁处理
      .subscribe((event) => {
        // 这里处理页面变化时的操作
        this.tableTop = `${this.deliveryView.nativeElement.clientHeight -
          200}px`;
        // console.log(this.tableTop);
      });
  }

  ngOnDestroy() {
    // 销毁事件
    this.subscription.unsubscribe();
  }

  dragnumber(e) {
    let ids = [];
    e.map((item) => {
      ids.push(item.id);
    });

    this.surveySerivce.reSort(ids, this.questionnaireId).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("排序保存成功");
      }
    });
  }

  getProject() {
    this.surveySerivce.getProjectById(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.projectInfo = res.data;
        if (this.projectInfo.language === "en_US") {
          this.lan = "en_US";
          this.lanIndex = 1;
        }
      }
    });
  }

  loadData() {
    this.surveySerivce.getQuestionsByProjId(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        // 排序
        // res.data.surveyQuestions.sort(function (a, b) { return a.sort - b.sort });

        this.topicList = _.filter(res.data.surveyQuestions, function(q) {
          return q.type !== "PAGE_SPLIT";
        });

        this.sourceList = this.topicList;

        this.topicList.forEach((item) => {
          if (
            item.name.zh_CN !== item.replaceName.zh_CN ||
            item.name.en_US !== item.replaceName.en_US
          ) {
            item.canEdit = true;
          }
        });
        this.questionnaireId = this.topicList[0].questionnaireId;
        let flag = this.topicList.findIndex((item) => {
          return item.isForceSelect === false;
        });
        if (flag === -1) {
          this.disabledAllChecked = true;
          this.isAllChecked = false;
        } else {
          this.disabledAllChecked = false;
          this.isAllChecked = false;
        }
      }
    });
  }

  search() {
    let content: string = this.keyWord;
    this.topicList = _.filter(this.sourceList, function(q) {
      let tmpCN: string = q.replaceName.zh_CN;
      let tmpEN: string = q.replaceName.en_US;
      if (!!tmpEN) {
        return tmpCN.indexOf(content) >= 0 || tmpEN.indexOf(content) >= 0;
      } else {
        return tmpCN.indexOf(content) >= 0;
      }
    });
  }

  canEdit() {
    return true;
  }

  // 中英文切换 -ldx
  changeLan(e): void {
    let cur = this.lans[this.lanIndex];
    this.lan = cur.key;
  }

  add() {
    let self = this;
    const modal = this.modalService.create({
      // nzTitle: `${tmpTitle}组织`,
      nzTitle: null,
      nzFooter: null,
      nzWidth: 1200,
      nzWrapClassName: "survey-book-add-modal",
      nzContent: SurveyBookModalComponent,
      nzComponentParams: {
        projectId: this.projectId,
        questionnaireId: this.questionnaireId,
      },
      nzOnOk: () => {
        const child = modal.getContentComponent();
        let questionsIds = [];
        questionsIds = child.getSelectedQuestions();
        this.surveySerivce
          .addQuestions({
            projectId: this.projectId,
            surveyQuestions: questionsIds,
          })
          .subscribe((res) => {
            if (res.result.code === 0) {
              this.msg.success("添加题目成功。");
              modal.close();
              this.loadData();
            } else {
              child.isLoading = false;
            }
          });

        return false;
      },
    });
    // this.loadData()
  }
  // 删除问题 -ldx
  onDeleteQuestion() {
    if (this.deleteIds.length === 0)
      // return this.msg.warning("请选择想要删除的题目");
      return this.customMsg.open("warning", "请选择想要删除的题目");
    this.surveySerivce.batchLogicDelete(this.deleteIds).subscribe((res) => {
      this.msg.success("删除数据成功！");
      this.loadData();
    });
    this.deleteIds = [];
  }

  delete(id: string) {
    this.surveySerivce.deleteQuestionById(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("删除成功");
        this.loadData();
      }
    });
  }
  // 全选
  checkAll(e) {
    this.topicList.map((item) => {
      if (!item.isForceSelect) {
        item.isDeleted = e;
      }
    });
    this.topicList.map((item) => {
      if (!item.isForceSelect) {
        if (item.isDeleted === true) {
          this.deleteIds.push(item.id);
        }
      }
    });
    if (e === false) this.deleteIds = [];
  }

  checkOne() {
    this.deleteIds = [];
    this.topicList.map((item) => {
      if (item.isDeleted === true) {
        this.deleteIds.push(item.id);
      }
    });
    let flag = this.topicList.every((item) => {
      return item.isForceSelect === true || item.isDeleted === true;
    });
    this.isAllChecked = flag;
  }

  edit(questionModel: any) {
    const modal = this.modalService.create({
      // nzTitle: `题目修订`,
      nzTitle: null,
      nzFooter: null,
      nzWidth: 800,
      nzContent: SubjectEditComponent,
      nzComponentParams: {
        subjectModel: questionModel,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child: SubjectEditComponent = modal.getContentComponent();
        let param = child.paramJson;
        this.surveySerivce.replaceQuestion(param).subscribe((res) => {
          if (res.result.code === 0) {
            // update local
            let tmp = _.find(this.topicList, { id: questionModel.id });
            if (tmp) tmp.name = param.surveyQuestion.name;
            this.msg.success("修订成功。");
            modal.close();
          }
        });
        return false;
      },
    });

    modal.afterClose.subscribe((result) => {
      this.loadData();
    });
  }

  batchEdit() {
    let tmpList = _.filter(this.topicList, function(q) {
      return q.canEdit;
    });

    const modal = this.modalService.create({
      nzTitle: null,
      nzFooter: null,
      nzWidth: 800,
      nzContent: SubjectBatchEditComponent,
      nzComponentParams: {
        modelList: tmpList,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child: SubjectBatchEditComponent = modal.getContentComponent();
        let param = child.editWidget.paramJson;
        this.surveySerivce.replaceQuestion(param).subscribe((res) => {
          if (res.result.code === 0) {
            this.msg.success("批量修订成功。");
            modal.close();
          }
        });
        return false;
      },
    });

    modal.afterClose.subscribe((result) => {
      this.loadData();
    });
  }

  bookConfirm() {
    let data = {
      projectId: this.projectId,
      questionnaireId: this.questionnaireId,
      type: "QUESTION_BOOK",
      isConfirmed: true,
    };
    this.surveySerivce.confirmRelation(data).subscribe((res) => {
      if (res.result.code === 0) {
        if (this.backtype == "home") {
          this.router.navigateByUrl("/project-manage/home");
        } else {
          localStorage.setItem("backurl", this.router.routerState.snapshot.url);
          this.router.navigate(["/new-prisma"], {
            queryParams: {
              projectId: this.projectId,
              type: this.projectType,
              questionnaireId: this.questionnaireId,
            },
          });
        }
      }
    });
  }

  config() {
    const modal = this.modalService.create({
      nzTitle: `题本设置`,
      nzFooter: null,
      nzWidth: 500,
      nzContent: BookSettingComponent,
      nzComponentParams: {
        questionnaireId: this.questionnaireId,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzMaskClosable: false,
      nzOnOk: () => {
        let child: BookSettingComponent = modal.getContentComponent();
        if (child.defaultList.length === 0) {
          // this.msg.warning("没有选择填答语言。");
          this.customMsg.open("warning", "没有选择默认语言");
          return false;
        }
        if (!child.radioValue) {
          // this.msg.warning("没有选择默认语言。");
          this.customMsg.open("warning", "没有选择默认语言");
          return false;
        }
        return true;
      },
    });
  }

  exportQusBook() {
    this.surveySerivce
      .exportPrismaQuestionBook(this.questionnaireId)
      .subscribe((res) => {
        const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
        let fileName = res.headers
          .get("Content-Disposition")
          .split(";")[1]
          .split("filename=")[1];
        const fileNameUnicode = res.headers
          .get("Content-Disposition")
          .split("filename*=")[1];
        // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
        if (fileName) {
          fileName = decodeURIComponent(fileName);
        }
        if (fileNameUnicode) {
          fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
        }
        const link = document.createElement("a");
        link.setAttribute("href", URL.createObjectURL(blob));
        link.setAttribute("download", fileName);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.surveySerivce
      .uploadPrismaQuestionBook(formData, this.questionnaireId)
      .subscribe(
        (event: HttpEvent<any>) => {
          item.onSuccess!();
          let res: any = event;
          if (res.result.code === 0) {
            this.msg.success("导入文件成功");
            this.getProject();
            this.loadData();
          }
        },
        (err) => {
          item.onError!(err, item.file!);
        }
      );
  }
}
