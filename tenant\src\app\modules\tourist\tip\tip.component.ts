import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Router } from "@angular/router";
import { NzMessageService } from "ng-zorro-antd";
import { TouristService } from '../tourist.service';

@Component({
  selector: 'app-tip',
  templateUrl: './tip.component.html',
  styleUrls: ['./tip.component.less']
})
export class TipComponent implements OnInit {
  type=1;
  constructor(private http: HttpClient,private router: Router,private message: NzMessageService,private TouristService:TouristService) {

  }
  tourist:boolean=true;
  isNeedLogin:boolean=false;
  token:any;
  _token:any;
  /*
  * 切换模块：type:number
  */
  toggle(type){
    this.type=type;
  }

  ngOnInit() {
    // this.token = JSON.parse(localStorage.getItem("token"));
    this._token = JSON.parse(localStorage.getItem("_token"));
    if (this._token) {
        this.tourist=false;
        this.isNeedLogin=true;
    }
  }
  downloadReport(){
    this.TouristService.downLoad('tip');
  }
}
