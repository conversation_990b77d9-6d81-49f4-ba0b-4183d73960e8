import {
  Component,
  Input,
  OnInit,
  QueryList,
  ViewChildren,
} from "@angular/core";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { NzMessageService } from "ng-zorro-antd";
import { SingleSurveyComponent } from "../single-survey/single-survey.component";
import _ from "lodash";
@Component({
  selector: "app-union-survey",
  templateUrl: "./union-survey.component.html",
  styleUrls: ["./union-survey.component.less"],
})
export class UnionSurveyComponent implements OnInit {
  @Input() createList: any[] = [];
  currentProjId: string = "";

  lans: any[] = [
    { label: "中文", value: "zh_CN", checked: true },
    { label: "ENG", value: "en_US", checked: false },
  ];

  @ViewChildren(SingleSurveyComponent) sons: QueryList<SingleSurveyComponent>;

  constructor(
    private projSerivce: ProjectManageService,
    private surveySerivce: SurveyApiService,
    private msg: NzMessageService
  ) {}

  ngOnInit() {
    this.initData();
  }

  initData() {
    let firstProjId = "";
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      let proj = element.project;
      element.extInfo = {
        cost: 0,
        info: {},
      };
      element.isSelect = false;
      element.isCurrent = false;
      if (index === 0) {
        firstProjId = proj.id;
      }
    }
    this.clickProject(firstProjId);
  }

  clickProject(projId) {
    this.currentProjId = projId;
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      let proj = element.project;
      if (projId === proj.id) {
        element.isCurrent = true;
      } else {
        element.isCurrent = false;
      }
    }
  }

  getTotal(): number {
    let total: number = 0;
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      if (element.isSelect) {
        total = total + element.extInfo.cost;
      }
    }
    return total;
  }

  buildParam(): any[] {
    let param: any[] = [];
    let nairCount: number = 0;

    let sonMap: any = {};
    this.sons.forEach((item) => {
      sonMap[item.projectId] = item.buildSingleParam();
    });

    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      let proj = element.project;
      if (element.isSelect) {
        // param.push(proj.projectName.zh_CN);
        param.push(sonMap[proj.id]);
        nairCount++;
      }
    }

    return param;
  }
}
