<div class="tipDesc">
  <ng-container *ngIf="isAtADAPTIVE">
    <nz-card style="height: 500px;">
      <div [innerHTML]="desc.zh_CN"></div>
    </nz-card>
  </ng-container>
  <ng-container *ngIf="!isAtADAPTIVE">
    <app-i18n-select
      [active]="lan"
      (selectChange)="onSelectI18n($event)"
    ></app-i18n-select>
    <div style="min-height: 500px; ; margin-top: 16px;">
      <ng-container *ngFor="let item of i18n">
        <div [hidden]="lan !== item.value">
          <tinymce
            [config]="tinyconfig"
            id="formula-textareanew"
            [(ngModel)]="desc[item.value]"
            delay="10"
          >
          </tinymce>
        </div>
      </ng-container>
    </div>
  </ng-container>
  <div class="drawer-footer">
    <button
      *ngIf="!isAtADAPTIVE"
      nz-button
      style="margin-right: 8px;"
      (click)="setDescDefault()"
    >
      恢复默认
    </button>
    <button nz-button nzType="primary" (click)="okModalDesc()">
      确认
    </button>
  </div>
</div>
