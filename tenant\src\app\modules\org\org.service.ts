import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class OrgService {
  apiBase: string = "/tenant-api";

  constructor(private http: HttpClient) {}

  getOrgList(
    projectId: string,
    pageIndex: number,
    pageSize: number,
    searchField?: string
  ): Observable<any> {
    const url = `${this.apiBase}/survey/project/pageOrganization`;
    return this.http.post(url, {
      projectId: projectId,
      searchContent: searchField,
      pageRequest: {
        current: pageIndex,
        size: pageSize,
      },
    });
  }

  getParentList(projectId: string): Observable<any> {
    const url = `${this.apiBase}/survey/project/listOrganization?projectId=${projectId}`;
    return this.http.get(url);
  }

  public createOrg(param: any): Observable<any> {
    let api = `${this.apiBase}/survey/project/createOrganization`;
    return this.http.post(api, param);
  }

  public updateOrg(param: any): Observable<any> {
    let api = `${this.apiBase}/survey/project/updateOrganization`;
    return this.http.post(api, param);
  }

  public deleteOrg(id: string): Observable<any> {
    let api = `${this.apiBase}/survey/project/removeOrganization?id=${id}`;
    return this.http.get(api);
  }

  public exportExcel(projectId?: string): Observable<any> {
    let httpOptions: any = { responseType: "Blob" };
    let api = `${this.apiBase}/survey/project/exportOrganizationExcel`;
    if (projectId) {
      api = api + `?projectId=${projectId}`;
    }
    return this.http.post(api, {}, httpOptions);
  }

  public uploadFile(formData, projectId?: string): Observable<any> {
    let api = `${this.apiBase}/survey/project/readOrganizationExcel`;
    if (projectId) {
      api = api + `?projectId=${projectId}`;
    }
    return this.http.post(api, formData);
  }

  public exportList(projId: string): Observable<any> {
    let httpOptions: any = { responseType: "Blob" };
    let api =
      `${this.apiBase}/survey/project/exportOrganizationList?projectId=` +
      projId;
    return this.http.get(api, httpOptions);
  }

  public clearOrganization(projectId: string): Observable<any> {
    let api = `${this.apiBase}/survey/project/clearOrganization?projectId=${projectId}`;
    return this.http.post(api, {});
  }

  // 导入 词云
  importWordCloud(formData, prismaReportDataId): Observable<any> {
    let api = `${this.apiBase}/sagittarius/report/keyword/import?prismaReportDataId=${prismaReportDataId}`;
    return this.http.post(api, formData);
  }
  // 导入chu 词云
  exportWordCloud(prismaReportDataId): Observable<any> {
    let httpOptions: any = { responseType: "Blob" };
    let api = `${this.apiBase}/sagittarius/report/keyword/export?prismaReportDataId=${prismaReportDataId}`;
    return this.http.get(api, httpOptions);
  }
  // 禁用组织
  disableOrganization(id): Observable<any> {
    let api = `${this.apiBase}/survey/project/disableOrganization/${id}`;
    return this.http.post(api, {});
  } // 启用组织
  enableOrganization(id): Observable<any> {
    let api = `${this.apiBase}/survey/project/enableOrganization/${id}`;
    return this.http.post(api, {});
  }
}
