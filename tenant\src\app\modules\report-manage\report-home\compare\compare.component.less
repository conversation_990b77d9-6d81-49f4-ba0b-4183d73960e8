.compare {
  display: flex;
  height: 100%;
  .left {
    width: 230px;
    height: 100%;
    &_title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        width: 160px;
        display: inline-block;
        font-size: 24px;
        color: #495970;
        cursor: default;
      }
      .type {
        display: inline-block;
        text-align: center;
        line-height: 20px;
        padding: 2px 6px;
        min-width: 60px;
        font-size: 12px;
        color: #409eff;
        background-color: #eff4ff;
        cursor: default;
      }
      > span {
        overflow: hidden; /*超出部分隐藏*/
        white-space: nowrap; /*不换行*/
        text-overflow: ellipsis; /*超出部分文字以...显示*/
      }
    }
    .name_table {
      display: flex;
      border-bottom: 1px solid #eee;
      margin-top: 30px;
      > span {
        flex: 1;
        display: inline-block;
        // width: 115px;
        height: 45px;
        text-align: center;
        line-height: 45px;
        font-size: 16px;
        cursor: pointer;
      }
      .table_left {
        background: #409eff;
        color: #fff;
      }
    }
    &_checkbox {
      div {
        width: 230px;
        line-height: 40px;
        padding: 0 20px;
      }
      .box_div_1 {
        background: #f9f9f9;
      }
      .box_div_2 {
        background: #fff;
      }
    }
    .scroll_com {
      overflow-y: auto;
      overflow-x: hidden;
      height: calc(100vh - 200px);
      .vxscrollbar();
    }
  }
  .right {
    flex: 1;
    height: calc(100vh - 88px);
    .vxscrollbar();
    &_title {
      display: flex;
      justify-content: center;
      > span {
        cursor: pointer;
        display: inline-block;
        line-height: 40px;
        width: 56px;
        text-align: center;
      }
      .title_left {
        color: #409eff;
        border-bottom: 2px solid #409eff;
      }
    }
    &_checkbox {
      margin-top: 20px;
      padding: 0 90px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;

      div {
        width: 20%;
        line-height: 40px;
        background: #ffffff;
        margin-top: 5px;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
      }
      &:after {
        content: "";
        width: 20%;
        height: 0;
      }
      .cicle {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
      }
      .color0 {
        background-color: #409eff;
      }
      .color1 {
        background-color: #ffba3b;
      }
      .color2 {
        background-color: #ff7676;
      }
      .color3 {
        background-color: #4fcf83;
      }
      .color4 {
        background-color: #a06bff;
      }

      .name_span {
        display: inline-block;
        width: 100%;
        overflow: hidden; /*超出部分隐藏*/
        white-space: nowrap; /*不换行*/
        text-overflow: ellipsis; /*超出部分文字以...显示*/
      }
    }
    .lineChart {
      height: calc(100vh - 195px);
      min-height: 500px;
      display: flex;
      align-items: center;
    }
  }
}

//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}
::ng-deep {
  .round-right-drawer-nofooter {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow-y: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
