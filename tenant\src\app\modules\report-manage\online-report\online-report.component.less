.online-report {
  background: #ffffff;
  min-height: 100%;
  //   .content {
  //
  //   }
  //   头部
  &-head {
    display: flex;
    justify-content: space-between;
    // 标题
    .title {
      font-size: 24px;
      font-weight: 100;
      color: #17314c;
      line-height: 33px;
      padding: 25px 0;
    }
    // 面包屑
    .break-crumb {
      padding-top: 20px;
    }
  }
  //   搜索
  &-search {
    position: relative;
    // 新建按钮
    .add-btn {
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border: none;
      &:hover {
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.8);
      }
    }
    // 清空按钮
    .clear {
      font-size: 12px;
      font-weight: 400;
      color: #495970;
      line-height: 17px;
      position: absolute;
      right: 6px;
      top: -26px;
      cursor: pointer;
      &:hover {
        color: #049fff;
      }
    }
  }
  //   筛选
  &-filter {
    margin-top: 16px;
    background: #f5faff;
    border-radius: 8px;
    height: 50px;
    padding: 0 24px;
    > div {
      height: 100%;
      display: flex;
      align-items: center;
    }
    // 标题
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      line-height: 22px;
      margin-right: 8px;
    }
    // 排序-正序倒序
    .order {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 22px;
      cursor: pointer;
      &-top {
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 6px solid #409eff;
        margin-bottom: 2px;
      }
      &-bottom {
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 6px solid #aaaaaa;
      }
    }
    .spin {
      transform: rotate(180deg);
    }
    // 翻页
    .pages {
      display: flex;
      justify-content: space-between;
      button {
        color: #8c8c8c;
        width: 18px;
        &:hover {
          color: #409eff;
        }
      }
      &-text {
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        line-height: 22px;
        color: #595959;
        cursor: default;
        span {
          &:first-child {
            color: #409eff;
          }
        }
      }
    }
    // 模式切换-卡片/列表
    .models {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 24px;
      div {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        line-height: 20px;
        i {
          margin-right: 8px;
          font-size: 14px;
        }
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
      }
      .divider {
        width: 1px;
        height: 14px;
        background: #bfbfbf;
      }
      .active {
        color: #409eff;
      }
    }
  }
  //   数据呈现
  &-list {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin: 0 -15px;
    .data {
      // 卡片模式
      &-card {
        position: relative;
        margin: 15px 15px 0 15px;
        width: 380px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #e2e2e2;
        overflow: hidden;
        // 内容
        &-top {
          border-top: 5px solid #409eff;
          margin-bottom: 24px;
          div {
            margin: 0 24px;
            display: flex;
            justify-content: space-between;
            // 报告名&开关
            &:nth-child(1) {
              margin-top: 20px;
              span {
                font-size: 20px;
                font-weight: 600;
                color: #262626;
                line-height: 28px;
                cursor: default;
                width: 80%;
              }
            }
            // 有效期&id
            &:nth-child(2) {
              margin-top: 9px;
              position: relative;
              span {
                font-size: 12px;
                font-weight: 400;
                color: #262626;
                line-height: 17px;
                cursor: default;
                &:last-child {
                  color: #595959;
                }
              }
              //   是否七日内
              .danger {
                position: absolute;
                top: 17px;
                left: 0;
                color: #f84444;
              }
            }
            // 使用人数&重新生成
            &:nth-child(3) {
              margin-top: 16px;
              align-items: flex-end;
              span {
                font-size: 12px;
                font-weight: 400;
                color: #262626;
                line-height: 17px;
                cursor: default;
                &:first-child {
                  color: #262626;
                  b {
                    font-size: 24px;
                    font-weight: 500;
                    color: #262626;
                    line-height: 33px;
                  }
                }
                &:last-child {
                  font-size: 14px;
                  font-weight: 400;
                  color: #409eff;
                  line-height: 20px;
                  cursor: pointer;
                  &:hover {
                    opacity: 0.75;
                  }
                }
              }
            }
            // 工具&创建时间
            &:nth-child(4) {
              margin-top: 14px;
              span {
                font-size: 12px;
                font-weight: 400;
                line-height: 17px;
                color: #262626;
                cursor: default;
                &:first-child {
                  width: 50%;
                }
                &:last-child {
                  color: #595959;
                }
              }
            }
          }
        }
        // 操作&创建人
        &-bottom {
          margin: 0 24px;
          border-top: 1px solid #e2e2e2;
          padding: 10px 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          //   操作
          &-left {
            display: flex;
            justify-content: flex-start;
            .item {
              cursor: pointer;
              margin-right: 16px;
              width: 32px;
              height: 32px;
              display: flex;
              justify-content: center;
              align-items: center;
              background-color: #f8f8f8;
              border-radius: 50%;
              i {
                font-size: 20px;
                color: #8c8c8c;
              }
              &:hover {
                background-color: #f5faff;
                i {
                  color: #409eff;
                }
              }
            }
          }
          //   创建人
          &-right {
            font-size: 12px;
            font-weight: 400;
            color: #595959;
            line-height: 17px;
          }
        }
        // 蒙版
        .shade {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: #000000;
          border-radius: 8px;
          opacity: 0.5;
          cursor: no-drop;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          &-spin {
            display: flex;
            flex-wrap: wrap;
            width: 44px;
            div {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              background: #ffffff;
              margin: 6px;
              &:nth-child(2) {
                opacity: 0.75;
              }
              &:nth-child(3) {
                opacity: 0.5;
              }
              &:nth-child(4) {
                opacity: 0.25;
              }
            }
          }
          &-text {
            margin-top: 8px;
            font-size: 20px;
            font-weight: 400;
            color: #ffffff;
            line-height: 28px;
          }
          .rotate {
            animation: rotateAnimation 3s linear infinite;
          }

          @keyframes rotateAnimation {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        }
        // 超出部分省略
        .container {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      // 列表模式
      &-list {
        position: relative;
        margin: 15px 15px 0 15px;
        width: 100%;
        height: 115px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #e2e2e2;
        overflow: hidden;
        // 内容
        &-content {
          height: 100%;
          padding: 16px 24px;
          border-left: 5px solid #409eff;
          .col {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            //   名称
            &-title {
              font-size: 20px;
              font-weight: 600;
              color: #17314c;
              line-height: 28px;
              width: 100%;
              cursor: default;
            }
            //   使用人数/报告类型
            &-leftBottom {
              display: flex;
              justify-content: space-between;
              align-items: flex-end;
              > div {
                cursor: default;
                width: 60%;
                text-align: right;
              }
              > span {
                cursor: default;
                font-size: 12px;
                font-weight: 400;
                color: #262626;
                line-height: 17px;
                b {
                  font-size: 24px;
                  font-weight: 500;
                  color: #262626;
                  line-height: 33px;
                  margin-right: 4px;
                }
              }
            }
            //   id/创建人/操作/开关
            &-rightTop {
              > div {
                span {
                  width: 100%;
                  font-size: 12px;
                  font-weight: 400;
                  color: #595959;
                  line-height: 17px;
                  cursor: default;
                }
                height: 100%;
                display: flex;
                align-items: center;
              }
              //   操作
              .operation {
                display: flex;
                justify-content: space-between;
                &-item {
                  cursor: pointer;
                  width: 32px;
                  height: 32px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  background-color: #f8f8f8;
                  border-radius: 50%;
                  i {
                    font-size: 20px;
                    color: #8c8c8c;
                  }
                  &:hover {
                    background-color: #f5faff;
                    i {
                      color: #409eff;
                    }
                  }
                }
              }
              //   开关
              .switch {
                display: flex;
                justify-content: flex-end;
              }
            }
            // 有效期/到期提示/重新生成
            &-rightbottom {
              display: flex;
              justify-content: space-between;
              span {
                cursor: default;
                font-size: 12px;
                font-weight: 400;
                color: #262626;
                line-height: 17px;
                // 重新生成
                &:last-child {
                  cursor: pointer;
                  font-size: 14px;
                  font-weight: 400;
                  color: #409eff;
                  line-height: 20px;
                  &:hover {
                    opacity: 0.75;
                  }
                }
              }
              //   到期提示
              .danger {
                font-size: 11px;
                font-weight: 400;
                color: #f84444;
                line-height: 16px;
                padding-right: 50px;
              }
            }
          }
        }
        // 超出部分省略
        .container {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        // 蒙版
        .shade {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: #000000;
          border-radius: 8px;
          opacity: 0.5;
          cursor: no-drop;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          &-spin {
            display: flex;
            flex-wrap: wrap;
            width: 36px;
            div {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              background: #ffffff;
              margin: 4px;
              &:nth-child(2) {
                opacity: 0.75;
              }
              &:nth-child(3) {
                opacity: 0.5;
              }
              &:nth-child(4) {
                opacity: 0.25;
              }
            }
          }
          &-text {
            margin-top: 8px;
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
            line-height: 22px;
          }
          .rotate {
            animation: rotateAnimation 3s linear infinite;
          }

          @keyframes rotateAnimation {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        }
      }
    }
    .empty {
      width: 100%;
      min-height: calc(100vh - 330px);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      img {
        width: 240px;
        margin-bottom: 30px;
        cursor: pointer;
      }
      p {
        font-size: 16px;
        font-weight: 400;
        color: #262626;
        line-height: 22px;
      }
    }
  }
  .minheight {
    min-height: calc(100vh - 330px);
    padding-bottom: 16px;
  }
}
//滚动条
.vxscrollbar() {
  scrollbar-color: transparent;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px transparent;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    background-color: #999;
    border-radius: 6px;
    outline: none;
  }
}
::ng-deep {
  .online-report {
    .ant-calendar-picker {
      width: 100% !important;
    }
  }
  .round-right-drawer11 {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 123px);
      overflow: auto;
      .vxscrollbar();
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-title {
      font-weight: bold;
    }

    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
.drawer {
  &-content {
    &-head {
      > div {
        height: 32px;
        display: flex;
        align-items: center;
        b {
          font-size: 16px;
          font-weight: 500;
          color: #262626;
          line-height: 22px;
        }
      }
      margin-bottom: 16px;
      &-search {
        color: #c4c4c4;
        font-size: 16px;
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
      }
    }
    &-types {
      width: 100%;
      height: 358px;
      background: #ffffff;
      border-radius: 18px;
      border: 1px solid #ececec;
      margin-bottom: 24px;
      &-box {
        margin: 16px;
        display: flex;
        overflow-x: auto;
        > div {
          margin: 8px;
          width: 197px;
          cursor: pointer;
          img {
            width: 197px;
            height: 270px;
            background: #ffffff;
            box-shadow: 0px 8px 12px 0px rgba(0, 0, 0, 0.1);
            border-radius: 2px;
            border: 10px solid #fff;
            transition: transform 0.3s ease;
          }
          p {
            margin-top: 20px;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            line-height: 20px;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
          }
          &:hover {
            img {
              box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
              transform: scale(1.025);
            }
            p {
              color: #409eff;
            }
          }
        }
        .checked {
          img {
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            transform: scale(1.025);
          }
          p {
            color: #409eff;
          }
        }
      }
    }
    &-form {
      margin-bottom: 24px;
      p {
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #262626;
        line-height: 22px;
        margin-bottom: 16px;
      }
    }
    &-shortcut {
      p {
        font-size: 12px;
        font-weight: 400;
        color: #595959;
        line-height: 17px;
        margin-bottom: 12px;
      }
      div {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;
        span {
          font-size: 14px;
          font-weight: 400;
          color: #bfbfbf;
          line-height: 20px;
          background: #f8f8f8;
          border-radius: 14px;
          padding: 4px 16px;
          margin-right: 8px;
          margin-bottom: 8px;
          cursor: pointer;
          &:hover {
            color: #409eff;
            background: #f0f8ff;
          }
        }
        .checked {
          color: #409eff;
          background: #f0f8ff;
        }
      }
    }
  }
  &-footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 10px 16px;
    text-align: right;
    left: 0px;
    background: #fff;
  }
}
