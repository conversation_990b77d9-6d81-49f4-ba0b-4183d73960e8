<div class="box">
  <div class="content">
    <div class="content-head flex items-center justify-between">
      <p>全不选时PPT报告不显示，仅excel显示</p>
      <span (click)="invert()">{{ isSelectAll ? "全不选" : "全选" }}</span>
    </div>
    <nz-table
      #headerTable
      [nzData]="listOfData"
      [nzScroll]="{ y: 'calc( 100vh - 250px )' }"
      nzScroll
      [nzPageSize]="listOfData.length"
      [nzShowPagination]="false"
    >
      <thead>
        <tr class="head_tr tr_list">
          <th style="padding: 16px;" nzWidth="130px">人口学标签</th>
          <th nzWidth="120px">
            <p>调研人员分布</p>
            <p>(人数/占比)</p>
          </th>
          <th nzWidth="120px">
            <p>人口标签交叉分析</p>
            <p>(四窗)</p>
          </th>
          <th nzWidth="120px">
            <p>群体分析</p>
            <p>(详细列表)</p>
          </th>
          <th nzWidth="120px" *ngIf="reportType==='EPSON_INVESTIGATION_RESEARCH_CUSTOM' || reportType==='NETEASE_INVESTIGATION_RESEARCH_CUSTOM' 
          || reportType==='TENCENT_INVESTIGATION_RESEARCH_CUSTOM' || reportType==='VIVO_INVESTIGATION_RESEARCH_CUSTOM'">
            <p>重点人群分析</p>
            <p>(定制报告专用)</p>
          </th>
          <th nzWidth="120px" *ngIf="reportType==='EPSON_INVESTIGATION_RESEARCH_CUSTOM' || reportType==='NETEASE_INVESTIGATION_RESEARCH_CUSTOM' 
          || reportType==='TENCENT_INVESTIGATION_RESEARCH_CUSTOM' || reportType==='VIVO_INVESTIGATION_RESEARCH_CUSTOM'">
            <p>双低人群概览</p>
            <p>(定制报告专用)</p>
          </th>
          <th nzWidth="120px">附录</th>
        </tr>
      </thead>
      <tbody>
        <tr class="tr_list" *ngFor="let data of headerTable.data">
          <td style="padding: 16px;">{{ data.name.zh_CN }}</td>
          <td *ngFor="let item of optionReport">
            <label
              nz-checkbox
              [(ngModel)]="data.labelShowReportPageList[item.name].checked"
              (ngModelChange)="checkChange()"
            ></label>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
</div>
<div class="footer">
  <button
    nz-button
    nzType="primary"
    (click)="handClick()"
    [nzLoading]="loading"
    nzShape="round"
  >
    确定
  </button>
</div>
