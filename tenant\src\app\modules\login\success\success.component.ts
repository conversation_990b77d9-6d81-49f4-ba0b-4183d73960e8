import { Component, OnInit, Input } from "@angular/core";
import { NzModalRef } from "ng-zorro-antd";

@Component({
  selector: "app-success",
  templateUrl: "./success.component.html",
  styleUrls: ["./success.component.less"],
})
export class SuccessComponent implements OnInit {
  @Input() successModel: any;

  constructor(private ref: NzModalRef) {}

  ngOnInit(): void {
    if (!this.successModel) {
      this.successModel = {
        text1: "邮箱验证通过",
        text2: "新密码已经发送到您的邮箱，请注意查收。",
        text3: "（如您没有收到邮件，请尝试在垃圾邮箱中查找）",
      };
    }
  }

  onSubmit() {
    this.ref.triggerOk();
  }
}
