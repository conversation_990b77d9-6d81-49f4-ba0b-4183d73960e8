import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { NzModalRef, NzModalService } from "ng-zorro-antd";
import { Router } from "@angular/router";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { HttpClient } from "@angular/common/http";
import { SuccessComponent } from "@src/modules/login/success/success.component";

@Component({
  selector: "app-register",
  templateUrl: "./register.component.html",
  styleUrls: ["./register.component.less"],
})
export class RegisterComponent implements OnInit {
  @Input() isLoadingOne = false;
  @Output() submit = new EventEmitter<any>();

  validateForm: FormGroup;

  act = false;

  constructor(
    private modalService: NzModalService,
    private router: Router,
    private fb: FormBuilder,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.validateForm = this.fb.group({
      realName: [null, [Validators.required]],
      mobile: [
        null,
        [Validators.pattern("^1[3-9]\\d{9}$"), Validators.required],
      ],
      position: [null, [Validators.required]],
      email: [null, [Validators.email, Validators.required]],
      name: [null, [Validators.required]],
      password: [
        null,
        [
          Validators.required,
          Validators.pattern("^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$"),
        ],
      ],
    });
    this.validateForm.statusChanges.subscribe((ast) => {
      this.act = true;
    });
  }

  submitForm() {
    for (const i in this.validateForm.controls) {
      this.validateForm.controls[i].markAsDirty();
      this.validateForm.controls[i].updateValueAndValidity();
    }

    if (this.validateForm.valid) {
      let commitJson = this.validateForm.value;
      commitJson.productCode = "SAG";
      this.submit.emit(commitJson);
    }
  }

  toLogin() {
    this.router.navigateByUrl("/user/login");
  }
}
