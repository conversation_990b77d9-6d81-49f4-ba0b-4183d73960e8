<div class="setting">
  <div class="content client-width" style="padding-bottom:30px ;">
    <p class="index-title">个人中心</p>
    <form nz-form [formGroup]="validateForm" class="login-form">
      <div class="bg-color">
        <!-- 企业账户信息 -->
        <div class="act-tit-box flex">
          <div class="act-tit">企业账户信息</div>
        </div>
        <ul class="flex set_con">
          <li style="flex: 1;">
            <p class="title">公司名称</p>
            <nz-form-item>
              <input
                nz-input
                class="input_item  not-allowed"
                type="text"
                disabled
                value="公司"
                formControlName="name"
              />
            </nz-form-item>
          </li>
          <li style="flex: 1; margin-left: 20px;">
            <div style="display: flex;justify-content: space-between;">
              <span class="title">账户余额</span>
              <span style="margin-left: 50px; color: red;"
                >如需充值，请联系销售顾问</span
              >
            </div>
            <nz-form-item>
              <input
                nz-input
                class="input_item "
                type="text"
                placeholder="0"
                disabled
                formControlName="balance"
              />
            </nz-form-item>
          </li>
        </ul>
      </div>
      <!-- 个人账户信息 -->
      <div class="bg-color">
        <div class="act-tit-box flex">
          <div class="act-tit">个人账户信息</div>
        </div>
        <div class="set_con">
          <div nz-row nzGutter="16">
            <div nz-col nzSpan="6">
              <p class="title">姓名</p>
              <nz-form-item>
                <nz-form-control nzErrorTip="姓名不能为空">
                  <input
                    nz-input
                    class="input_item input_width"
                    type="text"
                    placeholder="请输入"
                    formControlName="realName"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6">
              <p class="title">电话</p>
              <nz-form-item>
                <nz-form-control nzErrorTip="请输入电话">
                  <input
                    nz-input
                    class="input_item input_short input_width"
                    type="text"
                    placeholder="请输入"
                    formControlName="mobile"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
            <!-- <div nz-col nzSpan="6">
              <p class="title">职位</p>
              <nz-form-item>
                <nz-form-control nzErrorTip="请输入职位">
                  <input
                    nz-input
                    class="input_item input_width"
                    type="text"
                    placeholder="请输入"
                    formControlName="position"
                  />
                </nz-form-control>
              </nz-form-item>
            </div> -->
            <div nz-col nzSpan="6">
              <p class="title">邮箱</p>
              <nz-form-item>
                <nz-form-control nzErrorTip="请输入邮箱">
                  <input
                    nz-input
                    class="input_item input_short input_width"
                    type="text"
                    placeholder="请输入"
                    formControlName="email"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6">
              <p class="title">账户登录名</p>
              <nz-form-item>
                <input
                  nz-input
                  class="input_item input_short input_width"
                  type="text"
                  placeholder="请输入"
                  disabled
                  formControlName="username"
                />
              </nz-form-item>
            </div>
          </div>
        </div>
        <router-outlet></router-outlet>
      </div>

      <!--<app-update-password [visible]="hiddenPsd" [password]="password" (callBackPassword)="callBackPassword($event)"></app-update-password>-->
    </form>

    <!-- 子账户信息 -->
    <form nz-form>
      <div class="bg-color tableForm" *ngIf="isPermission">
        <div class="act-tit-box flex">
          <div class="act-tit">子账户信息</div>
          <!-- 设置权限 -->
          <app-set-permission-modal></app-set-permission-modal>
        </div>
        <nz-table
          #basicTable
          [nzData]="userList"
          [nzShowPagination]="true"
          [nzPageIndex]="pageIndex"
          [nzPageSize]="pageSize"
        >
          <thead>
            <tr>
              <th nzWidth="22%">账户登录名</th>
              <th nzWidth="22%">姓名</th>
              <th nzWidth="22%">电话</th>
              <th nzWidth="22%">邮箱</th>
              <th nzWidth="12%">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of basicTable.data; let index = index">
              <td>
                <span class="line-32">{{ data.username }}</span>
              </td>
              <td>
                <span *ngIf="!data.isEdit" class="line-32">{{
                  data.realName
                }}</span>
                <nz-form-item *ngIf="data.isEdit">
                  <nz-form-control [nzSpan]="24" nzErrorTip="请输入姓名！">
                    <input
                      style="width: 100%;"
                      [(ngModel)]="data.realName"
                      name="required"
                      required
                      nz-input
                      placeholder="请输入"
                    />
                  </nz-form-control>
                </nz-form-item>
              </td>
              <td>
                <span *ngIf="!data.isEdit" class="line-32">{{
                  data.mobile
                }}</span>
                <nz-form-item *ngIf="data.isEdit">
                  <nz-form-control
                    [nzSpan]="24"
                    nzErrorTip="请输入正确的手机号！"
                  >
                    <input
                      style="width: 100%;"
                      [(ngModel)]="data.mobile"
                      name="pattern"
                      pattern="^1[3456789][0-9]{9}$"
                      nz-input
                      placeholder="请输入"
                    />
                  </nz-form-control>
                </nz-form-item>
              </td>
              <td>
                <span *ngIf="!data.isEdit" class="line-32">{{
                  data.email
                }}</span>
                <nz-form-item *ngIf="data.isEdit">
                  <nz-form-control
                    [nzSpan]="24"
                    nzErrorTip="请输入正确的邮箱！"
                  >
                    <input
                      style="width: 100%;"
                      [(ngModel)]="data.email"
                      name="email"
                      email
                      nz-input
                      placeholder="请输入"
                    />
                  </nz-form-control>
                </nz-form-item>
              </td>
              <td style="text-align: center;">
                <a
                  *ngIf="!data.isEdit"
                  (click)="editSub(data, index)"
                  class="line-32"
                  >编辑</a
                >
                <a
                  *ngIf="data.isEdit"
                  (click)="saveSub(data, index)"
                  class="line-32"
                  >保存</a
                >
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </form>

    <!-- 账户信息有效期 -->
    <div class="bg-color" style="padding-bottom: 24px;">
      <div class="act-tit-box flex">
        <div class="act-tit">账户信息有效期</div>
      </div>
      <div *ngIf="peroidModel" class="peroid">
        <div class="head">
          <div class="remain">
            <span>
              {{ peroidModel.remainingTime }}
            </span>
          </div>
          <div class="tip1">剩余天数</div>
          <div class="tip2">如需续期，请联系销售顾问</div>
        </div>

        <div style="display: flex; flex-direction: column; ">
          <div
            style="height: 26px; width: 100%; position: relative; left: -19px;"
          >
            <div class="tipText" [ngStyle]="{ left: percent + '%' }">
              <span>
                {{ peroidModel.usedTime }}
              </span>
            </div>
          </div>
          <div>
            <nz-progress
              [nzPercent]="percent"
              [nzShowInfo]="false"
            ></nz-progress>
          </div>
          <div
            style="display: flex; justify-content: space-between;color: #AAAAAA;"
          >
            <span>{{ peroidModel.startDate }}</span>
            <span>{{ peroidModel.endDate }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 权限管理 -->
    <ng-container *ngIf="array.length != 0">
      <div class="act-tit-box flex">
        <div class="act-tit" style="margin-top: 30px;">系统消息</div>
      </div>
      <div style="display: flex;justify-content: flex-end;">
        <a
          *ngIf="newsshow == 'false'"
          style="cursor: pointer;"
          (click)="openNews()"
          >开启提醒</a
        >
      </div>
      <div
        style="background-color: #fff;padding: 0 20px;color: #BFC4CD;border-radius: 4px;"
      >
        <div
          *ngFor="let item of array; let i = index"
          style="display: flex;align-items: center; padding: 10px 0;border-bottom: 1px solid #E6E6E6;"
        >
          <img src="assets/images/carousel.png" alt="" />
          <div style="width: 90%; margin-left: 7px;">
            {{ item }}
          </div>
        </div>
      </div>
    </ng-container>
  </div>

  <footer>
    <div class="content client-width">
      <button
        nz-button
        nzType="default"
        nzSize="large"
        nzShape="round"
        (click)="cancel()"
      >
        取消
      </button>
      <button
        nzType="primary"
        nz-button
        nzSize="large"
        nzShape="round"
        (click)="submitForm()"
      >
        确定
      </button>
    </div>
  </footer>
</div>
