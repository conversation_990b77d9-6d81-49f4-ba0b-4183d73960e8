import { HttpClient } from "@angular/common/http";
import { Component, Inject, OnInit } from "@angular/core";
import { DA_SERVICE_TOKEN, ITokenService } from "@knz/auth";
import { NzMessageService, UploadXHRArgs } from "ng-zorro-antd";
import { Observable } from "rxjs";
import _ from "lodash";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { first } from "rxjs/operators";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-survey-history",
  templateUrl: "./survey-history.component.html",
  styleUrls: ["./survey-history.component.less"],
})
export class SurveyHistoryComponent implements OnInit {
  reportStyleMap = {
    _360_DEGREES_GROUP: "360团队报告",
    PTA_STANDARD_GROUP: "PTA团队报告",
    EMPTY: "手出报告",
  };

  dataSet = [];

  searchName: string = "";

  currentGroupId: string;

  currentGroupData: any;

  currentLanguage: any;

  permission: boolean;

  // 分页控制
  totalCount: number = 1;
  currentPage: number = 1;
  pageSize: number = 5;

  constructor(
    private http: HttpClient,
    private msgServ: NzMessageService,
    private surveySerivce: SurveyApiService,
    private customMsg: MessageService,
    public permissionService: PermissionService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService
  ) {}

  token: string;
  tenantUrl: string = "/tenant-api";

  getToken(): string {
    return this.tokenService.get().token;
  }

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    this.token = this.tokenService.get().token;
    this.loadData();
  }

  loadData() {
    this.getGroupReportList().subscribe((res) => {
      if (res.result.code === 0) {
        this.totalCount = res.page.total;
        this.dataSet = res.data;
        for (let index = 0; index < this.dataSet.length; index++) {
          const element = this.dataSet[index];
          let tmp: any = {};
          let lanMap = {
            zh_CN: [],
            en_US: [],
          };
          element.reportFiles.forEach((item) => {
            if (item.fileUrl) {
              let fileIds: string[] = lanMap[item.language];
              if (fileIds) {
                fileIds.push(item.fileUrl);
              }
            }
            tmp[item.language] = item;
          });
          let keys: string[] = Object.keys(tmp);
          for (let i = 0; i < keys.length; i++) {
            const lan = keys[i];
            let fileObj: any = tmp;
            let fIds: any = lanMap;
            if (fileObj && fIds && fIds.length > 0) {
              fileObj[lan].fileUrl = fIds[lan];
            }
          }
          element.fileMap = tmp;
        }
      }
    });
  }

  startDownlod(serverFiles: any[], lineData: any, lan: string) {
    const api = `${this.tenantUrl}/file/downloadMultiFile`;
    lineData.isDownloading = true;
    let fileIds = [];

    if (serverFiles && serverFiles.length > 0) {
      serverFiles.forEach((f) => {
        if (f.language === lan) {
          fileIds.push(f.fileUrl);
        }
      });
    }

    this.http
      .post(api, fileIds, { responseType: "blob", observe: "response" })
      .subscribe(
        (data) => {
          lineData.isDownloading = false;
          this.downFileRAR(data);
        },
        (error) => {
          lineData.isDownloading = false;
        }
      );
  }

  getGroupReportList(): Observable<any> {
    let param: any = {
      page: {
        current: this.currentPage,
        size: this.pageSize,
      },
      searchField: this.searchName,
    };
    const api = `${this.tenantUrl}/sagittarius/report/content/listPrismaReportByPage`;
    return this.http.post(api, param);
  }

  downFileRAR(data) {
    this.showBlobErrorMessage(data);

    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];

    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
      // .split('\'\'')[1]
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  downFile(data) {
    this.showBlobErrorMessage(data);
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/pdf" });
    let content_desposition: string = data.headers.get("content-disposition");
    let name = content_desposition.split(";")[1];

    let fileName: string;
    let fileNameUnicode: string;
    let type: number = 0;

    if (name.indexOf("fileName=") >= 0) {
      fileName = name.replace("fileName=", "");
      type = 1;
    } else if (name.indexOf("fileName*=") >= 0) {
      fileNameUnicode = name.replace("fileName*=", "");
      type = 2;
    }

    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (type === 1) {
      fileName = decodeURIComponent(fileName);
    } else if (type === 2) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    } else {
      fileName = new Date().toLocaleDateString();
    }

    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  downExcelFile(data) {
    this.showBlobErrorMessage(data);

    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];

    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
      // .split('\'\'')[1]
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  showBlobErrorMessage(data: any) {
    let body = data.body;
    if (body.type === "application/json") {
      let that = this;
      const reader = new FileReader();
      reader.readAsText(body, "utf-8");
      reader.onload = () => {
        // 处理报错信息
        // JSON.parse(reader.result) 拿到报错信息
        let resp: any = JSON.parse(reader.result + "");
        let code: number = resp.result.code;
        let errMsg: string = resp.result.message;

        if (code !== 0) {
          // that.msgServ.error(`${errMsg}，请联系管理员。`);
          that.customMsg.open("error", `${errMsg}，请联系管理员。`);
        }
      };
    }
  }

  tdClick(gId: string, data: any, lan?: string) {
    this.currentGroupId = gId;
    this.currentLanguage = lan;
    this.currentGroupData = data;
  }

  redoPdf(gId: string, data) {
    this.currentGroupId = gId;
    this.currentGroupData = data;

    data.isCreating = true;
    const api = `${this.tenantUrl}/sagittarius/report/content/reCreatePrismaReport`;
    let param: any = {
      isReCalculate: true,
      prismaReportDataId: gId,
    };
    this.http.post(api, param).subscribe(
      (res: any) => {
        data.isCreating = false;
        if (res.result.code === 0) {
          this.msgServ.success("重新生成已提交");
          this.loadData();
        }
      },
      (error1) => {
        data.isCreating = false;
      }
    );
  }

  /**
   * customReq上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = ".pdf";
    formData.append("file", item.file as any);
    formData.append("isPublic", "false");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.uploadPdf(formData);
  };

  /**
   * uploadPdf 上传pdf报告文件
   */
  uploadPdf(formData) {
    this.currentGroupData.isUploading = true;
    const url = `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`;
    return this.http.post(url, formData).subscribe(
      (res: any) => {
        if (res.result.code === 0) {
          const retData = res.data;

          this.updateGroupFile(retData.id);
        }
      },
      (err) => {
        this.currentGroupData.isUploading = false;
      }
    );
  }

  /**
   * 更新group的fileId
   */
  updateGroupFile(fileId: string) {
    const url = `${this.tenantUrl}/sagittarius/report/content/updatePrismaReportFile`;
    let param = {
      fileId: fileId,
      language: this.currentLanguage,
      prismaReportDataId: this.currentGroupId,
    };
    return this.http.post(url, param).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.currentGroupData.isUploading = false;
        this.loadData();
        this.msgServ.success("上传成功");
      }
    });
  }

  copy(id) {
    const copyEl = document.querySelector("#content" + id);
    const range = document.createRange();
    range.selectNode(copyEl);
    window.getSelection().removeAllRanges();
    window.getSelection().addRange(range);
    document.execCommand("copy");
    this.msgServ.success("复制成功");
  }

  exportPrismaDimensions(data) {
    data.dimDownloading = true;
    return this.surveySerivce.exportPrismaDimensions(data.id).subscribe(
      (resdata: any) => {
        data.dimDownloading = false;
        this.downExcelFile(resdata);
      },
      (err) => {
        data.dimDownloading = false;
      }
    );
  }
}
