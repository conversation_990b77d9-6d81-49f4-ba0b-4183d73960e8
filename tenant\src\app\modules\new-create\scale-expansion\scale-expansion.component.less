.scaleExpansion {
  .borderCard {
    border-radius: 8px;
    border: 1px solid #ececec;
    overflow: hidden;
  }
  &_content {
    height: calc(100vh - 140px);
    &_left {
      height: 100%;
      background-color: #fff;
      padding: 20px 4px 20px 16px;
      .select {
        margin-top: 26px;
        > div {
          > span {
            font-size: 14px;
            font-weight: 500;
            color: #495970;
            line-height: 20px;
            margin-right: 16px;
          }
          margin-bottom: 10px;
        }
        > p {
          font-size: 12px;
          font-weight: 400;
          color: #aaaaaa;
          line-height: 17px;
        }
      }
      .slectlist {
        margin-top: 10px;
        overflow-y: auto;
        height: calc(100vh - 300px);
        &_item {
          background: #fbfbfb;
          padding: 10px 10px;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          font-size: 13px;
          font-weight: 400;
          color: #17314c;
          line-height: 20px;
          margin-bottom: 10px;
          margin-right: 12px;
          &:nth-last-child(1) {
            margin-bottom: 0;
          }
          span {
            white-space: nowrap;
            height: 30px;
            line-height: 30px;
            margin: 5px 0;
          }
          .bgBox {
            width: 35px;
            height: 30px;
            background: #ffffff;
            border-radius: 2px;
            border: 1px solid #d3d3d3;
            text-align: center;
            line-height: 30px;
            margin: 5px;
          }
          .na {
            border: 1px solid #f84444;
            color: #f84444;
          }
        }
        // .borderRed{
        //   border: 1px solid #F84444;
        // }
      }
    }
    &_right {
      height: 100%;
      background-color: #fbfbfb;
      padding: 20px 16px;
      .autoBox {
        height: calc(100vh - 235px);
        overflow-y: auto;
        padding-right: 2px;
      }
      .rowlist {
        display: flex;
        justify-content: space-between;
        margin-top: 8px;
        // margin-left: 14px;
        // margin-right: 10px;
        .th {
          font-weight: 500;
          color: #495970;
          padding-left: 8px;
        }
        &_left {
          width: 212px;
          display: flex;
          justify-content: space-between;
        }
        &_right {
          width: 160px;
        }
        .index {
          width: 36px;
          height: 32px;
          background: #ffffff;
          border-radius: 2px;
          text-align: center;
          line-height: 32px;
        }
        .option {
          width: 160px;
          height: 32px;
          background: #ffffff;
          border-radius: 2px;
          border: 1px solid #e6e6e6;
          padding-left: 11px;
          line-height: 32px;
        }
        .optionInput {
          width: 160px;
          border-radius: 2px;
          border-color: #e6e6e6;
        }
        .disabled {
          background: #fafafc;
          color: #aaaaaa;
        }
        ::ng-deep {
          .ant-select-selection {
            border-color: #e6e6e6;
            border-radius: 2px;
          }
        }
      }
      .mt-35 {
        margin-top: 35px;
      }
      .warning {
        // width: 404px;
        // margin-left: 14px;
        // margin-right: 10px;
        background: #fffbf0;
        border-radius: 2px;
        border: 1px solid #ffeec1;
        padding: 10px 16px;
        margin-top: 18px;
        p {
          font-size: 12px;
          font-weight: 400;
          color: #f09600;
          line-height: 17px;
          margin-bottom: 8px;
          &:nth-last-child(1) {
            margin-bottom: 0;
          }
        }
      }
    }
    &_top {
      font-size: 18px;
      font-weight: 400;
      color: #17314c;
      line-height: 25px;
      padding: 8px 8px 24px 8px;
    }
    &_bottom {
      padding: 0 8px;
      display: flex;
      flex-wrap: wrap;
      overflow-y: auto;
      max-height: calc(100vh - 200px);
      .listItem {
        width: 120px;
        height: 210px;
        margin-right: 16px;
        margin-bottom: 24px;
        &:nth-child(7n) {
          margin-right: 0;
        }
        p {
          font-size: 14px;
          font-weight: 400;
          color: #495970;
          margin-bottom: 12px;
        }
        span {
          padding: 2px 8px;
          color: #3372ff;
          background: #eff4ff;
          font-size: 12px;
          font-weight: 400;
          border-radius: 11px;
          line-height: 18px;
        }
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        // 滑块背景
        &::-webkit-scrollbar-track {
          // background-color: transparent;
          background-color: #F1F1F1;
          box-shadow: none;
        }
        // 滑块
        &::-webkit-scrollbar-thumb {
          // background-color: #e9e9e9;
          background-color: #C1C1C1;
          outline: none;
          -webkit-border-radius: 2px;
          -moz-border-radius: 2px;
          border-radius: 2px;
        }
      }
    }
    .titleBox {
      display: flex;
      justify-content: space-between;
      h3 {
        font-size: 16px;
        // font-weight: 500;
        font-weight: bold;
        color: #17314c;
        // padding-left: 8px;
        // border-left: 5px solid #409eff;
        height: 22px;
        line-height: 22px;
      }
    }
  }
  &_footer {
    display: flex;
    padding: 14px 18px;
    position: relative;
    &_shade {
      background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.05) 0%,
        rgba(255, 255, 255, 0) 100%
      );
      width: 960px;
      height: 38px;
      position: absolute;
      top: -48px;
      left: -16px;
    }
    span {
      width: 128px;
      height: 38px;
      text-align: center;
      line-height: 38px;
      border-radius: 19px;
      margin-right: 25px;
      cursor: pointer;
    }
    .primary {
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      color: #fff;
    }
    .default {
      background-color: #fafafa;
      color: #aaaaaa;
      font-weight: 500;
    }
  }
  .suspendedBox {
    position: relative;
  }
  .suspended {
    position: absolute;
    top: 15px;
    right: 172px;
    &_connect {
      width: 45px;
      height: 176px;
      border-radius: 0 4px 4px 0;
      border: 1px solid #409eff;
      border-left: none;
      position: relative;
      &_dot {
        width: 6px;
        height: 6px;
        background: #ffffff;
        border: 1px solid #409eff;
        border-radius: 50%;
        &:nth-child(1) {
          position: absolute;
          top: -3px;
          left: -3px;
        }
        &:nth-child(2) {
          position: absolute;
          bottom: -3px;
          left: -3px;
        }
      }
      &_box {
        white-space: nowrap;
        position: absolute;
        top: 64px;
        left: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #17314c;
        background: #ffffff;
        border-radius: 5px;
        border: 1px solid #e6e6e6;
        padding: 5px 10px;
      }
    }
  }
  .radius-8 {
    border-radius: 8px;
  }
}
// 查看量表
.viewScale {
  width: 220px;
  .overflowY {
    max-height: 377px;
    overflow-y: auto;
  }
  > h3 {
    font-size: 18px;
    // font-weight: 500;
    font-weight: bold;
    color: #262626;
    line-height: 28px;
    margin-bottom: 16px;
  }
  .table {
    margin-right: 2px;
    margin-bottom: 16px;
    width: calc(100% - 2px);
    border: 1px solid #c4c4c4;
    border-radius: 8px;
    overflow: hidden;
    .tr {
      width: 100%;
      display: flex;
      justify-content: space-between;
      border-top: 1px solid #c4c4c4;
      .td {
        width: 50%;
        border-left: 1px solid #c4c4c4;
        text-align: center;
        padding: 5px 0;
        font-size: 14px;
        color: #262626;
        line-height: 25px;
        &:nth-child(1) {
          border-left: none;
        }
      }
      &:nth-child(1) {
        border-top: none;
      }
    }
  }
}
// 查看案例
.viewCase {
  width: 220px;
  > h3 {
    font-size: 18px;
    // font-weight: 500;
    font-weight: bold;
    color: #262626;
    line-height: 28px;
    margin-bottom: 8px;
  }
  > p {
    font-size: 14px;
    font-weight: 400;
    color: #262626;
    line-height: 20px;
    margin-bottom: 16px;
  }
  > div {
    background: #f8f8f8;
    border-radius: 2px;
    padding: 16px 16px 16px 0;
    > div {
      margin-bottom: 16px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
      > p {
        font-size: 14px;
        font-weight: 400;
        color: #262626;
        line-height: 20px;
        &:nth-child(1):before {
          display: inline-block;
          width: 3px;
          height: 10px;
          background: #409eff;
          content: "";
          margin-right: 13px;
        }
        &:nth-child(2) {
          padding-left: 16px;
        }
      }
    }
  }
}

::ng-deep {
  .round-right-drawer4 {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 108px);
      // height: calc(100% - 106px);
      overflow-y: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer4-nofooter {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow-y: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
.drawer-footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}
