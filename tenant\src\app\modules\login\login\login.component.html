<div class="container">
  <div class="header">
    <span class="header_1">登录</span>
    <span class="header_2"
      >没有账号？<a
        class="header_3"
        href="javascript:void(0)"
        (click)="toRegister()"
        >立即注册</a
      ></span
    >
  </div>

  <form nz-form [formGroup]="validateForm">
    <div class="content client-width">
      <div class="input_row">
        <nz-form-item>
          <nz-form-control nzErrorTip="企业全称不能为空">
            <input
              nz-input
              [(ngModel)]="loginModel.tenantShortName"
              class="input_item input_long"
              type="text"
              placeholder="请输入企业简称"
              (keydown.enter)="onSubmit()"
              formControlName="tenantShortName"
            />
          </nz-form-control>
        </nz-form-item>
      </div>

      <div class="input_row">
        <nz-form-item>
          <nz-form-control nzErrorTip="用户名不能为空">
            <input
              nz-input
              [(ngModel)]="loginModel.username"
              class="input_item input_long"
              type="text"
              placeholder="请输入用户名"
              (keydown.enter)="onSubmit()"
              formControlName="username"
            />
          </nz-form-control>
        </nz-form-item>
      </div>

      <div class="input_row">
        <nz-form-item>
          <nz-form-control nzErrorTip="登录密码不能为空">
            <input
              nz-input
              [(ngModel)]="loginModel.password"
              class="input_item input_long"
              type="password"
              placeholder="请输入登录密码"
              (keydown.enter)="onSubmit()"
              formControlName="password"
            />
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </form>

  <!--<label class="footer_1" nz-checkbox [(ngModel)]="rememberMe">记住我</label>-->
  <div class="footer_1">
    <label
      nz-checkbox
      [(ngModel)]="Agreechecked"
      (ngModelChange)="labelChange()"
    ></label>
    <span style="margin-left: 15px;">
      阅读并同意
      <a class="footer_3" href="user/userPolicy" target="_blank"
        >《KNX用户协议》、</a
      >
      <a class="footer_3" href="user/userAgree" target="_blank"
        >《KNX隐私政策》</a
      >
      。
    </span>
  </div>

  <div class="footer_2">
    <a class="footer_3" href="javascript:void(0)" (click)="toForgot()"
      >忘记密码</a
    >
  </div>

  <div class="footer">
    <!--<button class="submit" (click)="onSubmit()">提交</button>-->
    <button
      nz-button
      [nzLoading]="isLoadingOne"
      [ngClass]="{ submit: true, act: act }"
      (click)="onSubmit()"
    >
      提交
    </button>
  </div>
</div>
