<!--
    *@author: <PERSON>
    *@Date: 2023/11/03
    *@content: 自定义问卷-题本分发
-->
<nz-drawer
  class="topicDistribution"
  nzWrapClassName="round-right-drawer5"
  [nzWidth]="960"
  [(nzVisible)]="isVisible"
  nzTitle="题本分发"
  (nzOnClose)="handleCancel()"
>
  <!-- 题本分发导入导出 -->
  <div class="topicDistribution_head">
    <div></div>
    <div>
      <nz-upload
        [nzCustomRequest]="onDispenseImport"
        [nzShowUploadList]="false"
      >
        <button nz-button nzType="link" class="btn3" [disabled]="isdisabled">
          <i class="iconfont icon-import"></i> 导入
        </button>
      </nz-upload>
      <button
        nz-button
        nzType="link"
        class="btn3"
        [nzLoading]="isDispenseDownLoadSpinning"
        (click)="onDispenseDownLoad()"
      >
        <i class="iconfont icon-export_ic"></i> 导出
      </button>
    </div>
  </div>
  <!-- 关联 -->
  <div class="topicDistribution_content">
    <div class="topicDistribution_content_left">
      <div class="topicDistribution_content_left_list scroll">
        <!-- 人口标签 -->
        <div class="populationLabels">
          <h4 class="title">人口标签</h4>
          <ng-container *ngIf="demographicIdsList.length > 0">
            <ul class="list">
              <li *ngFor="let item of demographicIdsList">
                <nz-select
                  style="width: 100%;"
                  [(ngModel)]="item.selectedValue"
                  nzAllowClear
                  nzMode="multiple"
                  [nzMaxTagCount]="1"
                  [nzMaxMultipleCount]="1"
                  [nzPlaceHolder]="item.name.zh_CN"
                  [nzDisabled]="isdisabled"
                >
                  <nz-option
                    nzCustomContent
                    [nzValue]="element"
                    [nzLabel]="element.name.zh_CN"
                    *ngFor="let element of item.allChildren"
                  >
                    <span
                      nz-tooltip
                      nzPlacement="topLeft"
                      [nzTitle]="element.name.zh_CN"
                    >
                      {{ element.name.zh_CN }}
                    </span>
                  </nz-option>
                </nz-select>
              </li>
            </ul>
          </ng-container>
          <ng-container *ngIf="demographicIdsList.length === 0">
            <div style="margin-top: 16px;">
              <app-empty text="暂无人口标签"></app-empty>
            </div>
          </ng-container>
        </div>
        <!-- 二级维度 -->
        <div class="dimension">
          <h4 class="title">二级维度</h4>

          <ng-container *ngIf="dimensionCodesList.length > 0">
            <nz-checkbox-wrapper>
              <div class="dimension_label">
                <div class="label_div" *ngFor="let item of dimensionCodesList">
                  <label
                    [nzDisabled]="isdisabled"
                    nz-checkbox
                    [nzValue]="item"
                    style="display: flex;align-items: start;"
                    [(ngModel)]="item.checked"
                  >
                    <span nz-tooltip [(nzTooltipTitle)]="item.name.zh_CN">{{
                      item.name.zh_CN
                    }}</span>
                  </label>
                </div>
              </div>
            </nz-checkbox-wrapper>
          </ng-container>
          <ng-container *ngIf="dimensionCodesList.length === 0">
            <app-empty text="暂无二级维度"></app-empty>
          </ng-container>
        </div>
      </div>
      <!-- 关联相关操作 -->
      <div class="topicDistribution_content_left_btn">
        <button
          nz-button
          nzType="link"
          nzSize="small"
          [disabled]="isdisabled"
          (click)="clearSelected()"
        >
          清空已选
        </button>
        <button
          nz-button
          nzType="primary"
          nzGhost
          (click)="association()"
          [disabled]="isdisabled"
          nzShape="round"
        >
          关联
        </button>
      </div>
    </div>
    <ng-container *ngIf="mappings && mappings.length > 0">
      <!-- 关联结果 -->
      <ul class="topicDistribution_content_right scroll">
        <li *ngFor="let item of mappings; let i = index">
          <div class="title">
            <h5>关联{{ i + 1 }}</h5>
            <button
              nz-button
              nzType="link"
              nzSize="small"
              (click)="empty(i)"
              [disabled]="isdisabled"
            >
              清空关联
            </button>
          </div>
          <!-- 人口标签项 -->
          <div class="item">
            <ng-container *ngIf="isdisabled">
              <nz-tag *ngFor="let item1 of item.demographics; let j = index">{{
                item1.name.zh_CN
              }}</nz-tag></ng-container
            >
            <ng-container *ngIf="!isdisabled">
              <nz-tag
                nzMode="closeable"
                *ngFor="let item1 of item.demographics; let j = index"
                (nzOnClose)="onCloseCard(i, j, 1)"
                >{{ item1.name.zh_CN }}</nz-tag
              >
            </ng-container>
          </div>
          <!-- 二级维度项 -->
          <div class="item">
            <ng-container *ngIf="isdisabled">
              <nz-tag *ngFor="let item1 of item.dimensions; let j = index">{{
                item1.name.zh_CN
              }}</nz-tag>
            </ng-container>
            <ng-container *ngIf="!isdisabled">
              <nz-tag
                nzMode="closeable"
                *ngFor="let item1 of item.dimensions; let j = index"
                (nzOnClose)="onCloseCard(i, j, 2)"
                >{{ item1.name.zh_CN }}</nz-tag
              >
            </ng-container>
          </div>
        </li>
      </ul>
    </ng-container>
    <!-- 暂无关联 -->
    <ng-container *ngIf="mappings && mappings.length === 0">
      <div class="topicDistribution_content_right card">
        <app-empty text="暂无关联"></app-empty>
      </div>
    </ng-container>
  </div>
  <!-- 题本分发操作项 -->
  <div class="footer">
    <button
      nz-button
      nzType="default"
      (click)="default()"
      [disabled]="isdisabled"
    >
      恢复默认
    </button>
    <button nz-button nzType="primary" (click)="primary()">确认</button>
  </div>
</nz-drawer>
