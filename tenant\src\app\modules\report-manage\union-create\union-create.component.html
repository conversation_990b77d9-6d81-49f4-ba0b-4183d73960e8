<!-- 报告中英文选择 -->
<div *ngIf="step === 2 && isHaveLanPermission()" class="lan">
  <nz-checkbox-group [(ngModel)]="lans"></nz-checkbox-group>
</div>
<!-- loading -->
<div
  style="text-align: center; height: 100vh; display: flex; align-items: center; justify-content: center;"
  *ngIf="isLoading"
>
  <ng-template #indicatorTemplate>
    <i nz-icon nzType="loading" style="font-size: 24px;"></i>
  </ng-template>
  <nz-spin nzSimple [nzIndicator]="indicatorTemplate"> </nz-spin>
</div>
<!-- 测评or调研团队报告 -->
<div class="container" *ngIf="data">
  <div *ngIf="data.assessmentInfos" [hidden]="step === 2">
    <app-union-assessment
      #app1
      [createList]="data.assessmentInfos"
    ></app-union-assessment>
  </div>
  <div *ngIf="data.employeeInfos" [hidden]="step === 1">
    <app-union-survey
      #app2
      [createList]="data.employeeInfos"
    ></app-union-survey>
  </div>
</div>
<!-- footer -->
<div class="footer-box">
  <div [hidden]="isLoading" class="after">
    <span>共计:</span>
    <span class="total">{{ getTotal() }}</span>
    <span>K米, K米将自动从您的账户中扣除</span>
  </div>

  <div>
    <!-- 测评&调研切换 -->
    <ng-container *ngIf="data?.assessmentInfos && data?.employeeInfos">
      <button *ngIf="step === 1" nz-button nzType="default" (click)="next()">
        下一步
      </button>

      <button *ngIf="step === 2" nz-button nzType="default" (click)="pre()">
        上一步
      </button>
    </ng-container>
    <!-- 确认 -->
    <button
      *ngIf="pageCount === 1 || (pageCount === 2 && step === 2)"
      nz-button
      nzType="primary"
      (click)="ok()"
      [nzLoading]="taskRunning"
    >
      <span>确认</span>
    </button>
  </div>
</div>
