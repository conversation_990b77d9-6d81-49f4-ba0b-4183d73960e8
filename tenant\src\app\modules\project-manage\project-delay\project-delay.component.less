nz-date-picker,
nz-month-picker,
nz-range-picker,
nz-week-picker {
  margin: 0 8px 12px 0;
}

.container {
  margin: 0;
  padding: 0;
}

.title {
  height: 33px;
  font-size: 24px;
  font-weight: 300;
  color: rgba(23, 49, 76, 1);
  line-height: 33px;
  text-align: center;
  margin-top: 6px;
  margin-bottom: 48px;
}

.content {
  height: 50px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(23, 49, 76, 1);
  line-height: 20px;
  display: flex;
  justify-content: left;
  align-items: stretch;
}

.label {
  vertical-align: middle;
  padding-top: 5px;
  padding-right: 5px;
  margin-left: 32px;
  height: auto;
}

.picker {
  height: auto;
}

.btn {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.btn>span {
  width: 30px;
}

.button_cancel {
  width: 128px;
  height: 38px;
  background: rgba(250, 250, 250, 1);
  border-radius: 19px;
  outline: none;
  border: none;
}

.button_ok {
  width: 128px;
  height: 38px;
  background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
  box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  outline: none;
  border: none;
}

.button_cancel>span {
  width: 32px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(170, 170, 170, 1);
  line-height: 22px;
}

.button_ok>span {
  width: 32px;
  height: 22px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  line-height: 22px;
}
