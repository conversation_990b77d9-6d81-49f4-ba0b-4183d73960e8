.container {
    display: flex;
    justify-content: flex-start;
    align-items: stretch;
    margin-right: 20px;

    .left {
        width: 220px;
        max-height: 700px;
        border-right: #E6E6E6 solid 1px;
        display: flex;
        flex-direction: column;
        padding-right: 20px;

        .nair {
            width: 208px;
            height: 110px;
            border-radius: 5px;
            padding: 20px 10px;
            border-bottom: #E6E6E6 solid 1px;

            div {
                margin-bottom: 5px;
            }

            .line1 {
                height: 21px;
                display: flex;
                justify-content: space-between;

                .text0 {
                    height: 20px;
                    font-size: 14px;
                    font-weight: 600;
                    color: #17314C;
                    line-height: 20px;
                }
            }

            .line2 {
                height: 21px;

                .text1 {
                    height: 21px;
                    border-radius: 4px;
                    font-weight: 500;
                    padding: 2px 5px;
                }

                .text2 {
                    padding: 0 5px;
                    margin-left: 5px;
                    font-size: 13px;
                    font-weight: 500;
                    color: #419EFF;
                    line-height: 17px;
                    height: 21px;
                    border-radius: 4px;
                    border: 1px solid #419EFF;
                }
            }

            .line3 {
                height: 21px;

                .text3 {
                    height: 20px;
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 500;
                    color: #AAAAAA;
                }
            }

        }

    }

    .right {
        width: 680px;

        .title {

            margin: 0 0 20px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            span {
                margin-left: 10px;
                height: 20px;
                font-size: 15px;
                font-weight: 500;
                color: #17314C;
                line-height: 20px;
            }

            .input {
                .search {
                    width: 186px;
                    height: 30px;
                    background: rgba(255, 255, 255, 1);
                    border-radius: 15px;

                    .ant-input {
                        border-radius: 15px;
                        border: none;
                        border: solid 1px gainsboro;
                    }
                }
            }

        }

        .table {
            margin-left: 10px;
            max-height: 450px;
        }
    }
}

.linesplit {
    width: 100%;
    height: 10px;
    background: -webkit-linear-gradient(top, #fff, #ddd);
    // background: -webkit-linear-gradient(top, #fff, #0000ff); 
}

.bottom {
    margin-top: 20px;
    padding: 0 20px;
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .pre {
        button {
            width: 128px;
            height: 38px;
            background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 19px;
            text-align: center;

            span {
                width: 32px;
                height: 22px;
                font-size: 16px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 600;
                color: #FFFFFF;
                line-height: 22px;
            }
        }

        label {
            margin-left: 50px;
        }

        span {
            margin-left: 10px;
        }
    }

    .after {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .line1 {
            height: 17px;
            font-size: 13px;
            font-weight: 500;
            color: #495970;
            line-height: 17px;

            .total {
                margin-right: 10px;
                height: 28px;
                font-size: 20px;
                font-weight: 400;
                color: #E1251B;
                line-height: 28px;
            }
        }

        .line2 {
            height: 17px;
            font-size: 13px;
            font-weight: 500;
            color: #17314C;
            line-height: 17px;
            margin-top: 15px;
        }
    }
}

.currentNair {
    background-color: #F0F0F0;
    // border:dashed #E0E0E0 1px !important;
    // background: -webkit-linear-gradient(top, #fff, #0000ff);
}

.autoTxt {
    font-weight: 500;
    color: #FFFFFF;
    background-color: #419EFF;
}

.handTxt {
    font-weight: 500;
    color: #FFFFFF;
    background-color: #45BFD9;
}

.thead {
    background-color: #F3F7FB !important;
}

.th {
    width: 24px;
    height: 17px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 600;
    color: #AAAAAA;
    line-height: 17px;
    background-color: #F3F7FB !important;
}

.td {
    height: 20px;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 500;
    color: #17314C;
    line-height: 20px;
}

.col1 {
    max-width: 130px;
}

.col2 {
    max-width: 260px;
}

img {
    width: 22px;
    height: 22px;
    border: 1px solid white;
    left: 0px;
    top: 0px;

    &:hover {
        width: 22px;
        height: 22px;
        border-radius: 100%;
        border-right: 2px solid white;
        border-bottom: 2px solid white;
        border-left: 2px solid white;
        border-top: 2px solid white;
        position: relative;
        left: 1px;
        top: 1px;
    }
}

.scroll {
    height: 500px;
    .vxscrollbar()
}

//滚动条
.vxscrollbar() {
      scrollbar-color: auto;
  scrollbar-width: auto;
    overflow-y: overlay;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    // 滑块背景
    &::-webkit-scrollbar-track {
      // background-color: transparent;
      background-color: #F1F1F1;
      box-shadow: none;
    }
    // 滑块
    &::-webkit-scrollbar-thumb {
      // background-color: #e9e9e9;
      background-color: #C1C1C1;
      outline: none;
      -webkit-border-radius: 2px;
      -moz-border-radius: 2px;
      border-radius: 2px;
    }
}

::ng-deep {
    .ant-modal-title {
        height: 33px;
        font-size: 24px;
        font-family: PingFangSC-Thin, PingFang SC;
        font-weight: 100;
        color: #17314C;
        line-height: 33px;
    }

    .ant-table-small {
        border: 0px;
    }

}