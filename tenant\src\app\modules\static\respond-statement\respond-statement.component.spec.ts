import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RespondStatementComponent } from './respond-statement.component';

describe('RespondStatementComponent', () => {
  let component: RespondStatementComponent;
  let fixture: ComponentFixture<RespondStatementComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RespondStatementComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RespondStatementComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
