import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { SharedModule } from "@shared";
import { Routes, RouterModule } from "@angular/router";
import { NewPrismaComponent } from "./new-prisma.component";
import { PirpreviewComponent } from "./Pir_preview/Pir_preview.component";
import { AdvancedSetting } from "./adv_set/advsetting.component";
import { LotteryComponent } from "./lottery/lottery.component";
import { DrawComponent } from "./draw/draw.component";
// 自定义指数分析
import { IndexAnalysisComponent } from "./index-analysis/index-analysis.component";
// 交叉分析
import { CrossAnalysisComponent } from "./cross-analysis/cross-analysis.component";
// 多群体分析
import { GroupAnalysisComponent } from "./group-analysis/group-analysis.component";
// 数据呈现/计算规则
import { DataShowCalRuleComponent } from "./data-show-cal-rule/data-show-cal-rule";
import { ReportSettings } from "./report-settings/report-settings.component";
import { ReportDisplay } from "./report-display/report-display.component";
import { CorrelationCoefficientComponent } from "./correlation-coefficient/correlation-coefficient.component";
import { ReportEscription } from "./report-escription/report-escription.component";
// import { MarkComponent } from './custommark/mark.component'

const routes: Routes = [
  { path: "", component: NewPrismaComponent },
  {
    path: "pri_preview",
    component: PirpreviewComponent,
    data: { title: "预览题本", titleI18n: "预览题本" },
  },
  {
    path: "lottery",
    component: LotteryComponent,
    data: { title: "抽奖福利", titleI18n: "lottery benefits" },
  },
];

@NgModule({
  declarations: [
    NewPrismaComponent,
    AdvancedSetting,
    PirpreviewComponent,
    LotteryComponent,
    DrawComponent,
    IndexAnalysisComponent,
    CorrelationCoefficientComponent,
    ReportSettings,
    ReportEscription,
    CrossAnalysisComponent,
    GroupAnalysisComponent,
    DataShowCalRuleComponent,
    ReportDisplay,
  ],
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
  //组件声明
  entryComponents: [
    AdvancedSetting,
    DrawComponent,
    IndexAnalysisComponent,
    CorrelationCoefficientComponent,
    ReportSettings,
    ReportEscription,
    CrossAnalysisComponent,
    GroupAnalysisComponent,
    DataShowCalRuleComponent,
    ReportDisplay,
  ],
  exports: [RouterModule],
})
export class NewPrismaModule {}
