import { Component, OnInit } from '@angular/core';

import _ from 'lodash'

import { Router } from '@angular/router'

import { ProjectManageService } from '../../service/project-manage.service';
@Component({
  selector: 'app-project-manage-home',
  templateUrl: './project-manage-home.component.html',
  styleUrls: ['./project-manage-home.component.less'],
  // providers: [DatePipe]
})
export class ProjectManageHomeComponent implements OnInit {
  Breadcrumbs = [
    {
      path: '/home',
      name:'首页',
      Highlight:false
    },
    {
      path: '',
      name:'活动管理',
      Highlight:true
    },
  ]
  isSpinning: boolean = false

  tabNum: number | string = 2 // 标准  2 卡片列表

  listType: boolean = true // card / list
  isSystemInterface: boolean = false // card / list

  listData: any[] = []; // 列表数据

  name: string = ''; // 搜索 活动名称
  status: string = null; // 搜索 状态
  surveyType: string = null; // 搜索 活动分类
  standardQuestionnaireId: string = null; // 搜索 活动工具
  isClosingSoon: boolean = null; // 是否5个工作日内到期
  founder: string = null; // 创建人
  startTime: string = null; // 开始
  endTime: string = null; // 结束
  
  isSearch:boolean = false
  orderBy: string = ''
  page: any = { // 分页条件
    current: 1,
    pages: 1,
    searchCount: true,
    size: 6,
    total: 1
  }

  projectCount: number = 0

  tools: any[] = []

  dateRange: any[] = [] // 日期
  showmock = false
  setp1 = false
  constructor(
    private projectManageService: ProjectManageService,
    private router: Router,
  ) { }

  ngOnInit() {
    this.showmock = JSON.parse(sessionStorage.getItem('noviceGuidance'))
    if (this.showmock) {
      this.setp1 = true
      this.tabNum = 2
    }
    this.projectManageService.projectSummary().subscribe(res => {
      this.projectCount = res.data.projectCount
    })
    let memberlist = JSON.parse(sessionStorage.getItem('activepage'))
    if (memberlist != null) {
      this.page = memberlist.page
      this.isClosingSoon = memberlist.isClosingSoon
      this.name = memberlist.name
      this.status = memberlist.status
      this.tabNum = memberlist.tabNum
      this.dateRange = memberlist.dateRange // 日期
      this.startTime = memberlist.startTime
      this.endTime = memberlist.endTime
      this.standardQuestionnaireId = memberlist.standardQuestionnaireId
      this.surveyType = memberlist.surveyType
      this.founder = memberlist.founder
      this.orderBy = memberlist.orderBy
    }
    this.projectManageService.listAllUsedQuestionnaireNames().subscribe(res => {
      this.tools = res.data
    })
    this.loadListData()
  }

  getParams() { // 离开页面保存参数
    let params = {
      name: this.name,
      status: this.status,
      standardQuestionnaireId: this.standardQuestionnaireId,
      founder: this.founder,
      surveyType: this.surveyType,
      page: this.page,
      isClosingSoon: this.isClosingSoon ,
      tabNum: this.tabNum,
      dateRange: this.dateRange, // 日期
      startTime: this.startTime,
      endTime: this.endTime,
      orderBy: this.orderBy
    }
    return params
  }

  loadListData(isSearch?: boolean) {
    if(isSearch) {
      this.isSearch = isSearch
      this.page = { // 分页条件
        current: 1,
        pages: 1,
        searchCount: true,
        size: 6,
        total: 1
      }
    } else {
      this.isSearch = false
    }
    let params: any = {
      page: this.page,
    }
    this.isClosingSoon ? params.isClosingSoon = this.isClosingSoon : ''
    this.name ? params.name = this.name : ''
    this.status ? params.status = this.status : ''
    this.standardQuestionnaireId ? params.questionnaireName = this.standardQuestionnaireId : ''
    this.surveyType ? params.surveyType = this.surveyType : ''
    this.startTime ? params.startTime = this.startTime : ''
    this.endTime ? params.endTime = this.endTime : ''
    this.isSpinning = true
    params.orderBy = this.orderBy
    this.projectManageService.getIsSystemInterface().subscribe((res) => {
      if(res.result.code === 0) {
        this.isSystemInterface = res.data
      }
    })
    this.projectManageService.getPagedProjectList1(params).subscribe(res => {
      if(this.listType) {
        this.page = res.page
        this.page.searchCount = true
        // this.listData = _.chunk(res.data, 3)
        this.listData = res.data
      }
      this.isSpinning = false
    })
  }

  sort(e) {
    if(this.orderBy === '') {
      this.orderBy = e
    } else {
      this.orderBy = ''
    }
    this.loadListData()
  }

  search() {
    this.loadListData(true)
  }

  clearParams() { // 清空参数
    sessionStorage.setItem('activepage', null)
    this.name= ''; // 搜索 活动名称
    this.status= null; // 搜索 状态
    this.surveyType= null; // 搜索 活动分类
    this.standardQuestionnaireId= null; // 搜索 活动工具
    this.isClosingSoon= false; // 是否5个工作日内到期
    this.founder= null; // 创建人
    this.dateRange = [] // 日期
    this.startTime = null // 日期
    this.endTime = null // 日期
    this.page = { // 分页条件
      current: 1,
      pages: 1,
      searchCount: true,
      size: 6,
      total: 1
    }

    this.loadListData(true)
  }

  changeCheckbox(e) {
    e ? this.isClosingSoon = e : this.isClosingSoon = null
    
    this.loadListData()
  }

  newAddProject() { // 新建活动
    this.router.navigateByUrl(`/new-activity`);
    sessionStorage.setItem('activepage', null)
  }

  onChange(e) { // 时间回调
    let timeRange = this.dateFormatter(e)
    this.startTime = timeRange.startTime
    this.endTime = timeRange.endTime
    this.search()
  }

  dateFormatter(range: any[]) {
    let timeRange = {
      startTime: '',
      endTime: ''
    }
    range.forEach((time, index) => {
      const date = new Date(time)
      let newdate = date.toISOString()
      let arr  = newdate.split('T')
      if(index == 0) timeRange.startTime = arr[0] + 'T00:00:00'
      timeRange.endTime = arr[0] + 'T23:59:59'
    })
    return timeRange
  }

  changeTab(type) { // 切换 列表模式
    // type === 1 ? this.page.size = 5 : this.page.size = 6
    // this.loadListData()
    this.tabNum = type
  }

  // 分页相关
  pageIndexChange(e) { // 页码改变
    if(e === 0 || e > this.page.pages) return
    console.log(e)
    this.page.current = e
    this.loadListData()
  }

  pageSizeChange(e) { // 每页条数改变
    this.page.size = e
    this.loadListData()
  }
  jumprun() {
    this.setp1 = false
    this.showmock = false
  }
  next1() {
    this.setp1 = false
    this.showmock = false
  }
  getnewlead() {
    this.showmock = true
    this.setp1 = true
    this.tabNum = 2
  }
}
