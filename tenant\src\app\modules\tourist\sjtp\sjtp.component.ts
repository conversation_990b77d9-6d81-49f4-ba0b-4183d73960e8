import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Router } from "@angular/router";
import { NzMessageService } from "ng-zorro-antd";
import { TouristService } from '../tourist.service';
declare var $:any;

@Component({
  selector: 'app-sjtp',
  templateUrl: './sjtp.component.html',
  styleUrls: ['./sjtp.component.less']
})
export class SjtpComponent implements OnInit {
  array = [1, 2, 3, 4];
  effect = 'scrollx';
  carouselList = [{
    src: 'assets/images/t_epa_1.png',
    title:'人才画像',
    des:'根据员工的性格倾向，精确匹配岗位适配度，使其发挥所长。'
  },
  {
    src: 'assets/images/t_epa_2.png',
    title:'企业招聘',
    des:'面试阶段使用测评，减轻校招决策负担，增加社招精准度，甄选真正需要的人。'
  },
  {
    src: 'assets/images/t_epa_3.png',
    title:'团队建设',
    des:'全面了解团队成员工作风格，调整领导方式，择其优势安排任务；促进团队成员相互理解，提升合作效率。'
  },
  {
    src: 'assets/images/t_epa_4.png',
    title:'员工培养',
    des:'精确定位待发展项，从内在出发，进行有效地职业生涯规划和自我提升。'
  },
  {
    src: 'assets/images/t_epa_5.png',
    title:'人才盘点',
    des:'结合能力测评，从显性到隐形特质，多方位了解人才质量和现状，挖掘高潜人才。'
  }]
  tourist:boolean=true;
  isNeedLogin:boolean=false;
  token:any;
  _token:any;
  constructor(private http: HttpClient,private router: Router,private message: NzMessageService,private TouristService:TouristService) { }

  ngOnInit() {
    // console.log($)
    // this.token = JSON.parse(localStorage.getItem("token"));
    this._token = JSON.parse(localStorage.getItem("_token"));
    if (this._token) {
      // let nowDate = new Date().getTime() - new Date(this.token.time).getTime();
      // let minutes = (nowDate % (1000 * 60 * 60)) / (1000 * 60);
      // if (minutes < 10) {
        this.tourist=false;
        this.isNeedLogin=true;
      // }
    }
  }
  ngAfterViewInit(){
    $(".carousel").superSlider({
      prevBtn: 	 ".prev",//左按钮
      nextBtn: 	 ".next",//右按钮
      listCont:    "#carousel",//滚动列表外层
      scrollWhere: "prev",//自动滚动方向next
      delayTime: 	 1000,//自动轮播时间间隔
      speed: 		 300,//滚动速度
      amount: 	 1,//单次滚动数量
      showNum: 	 3,//显示数量
      autoPlay: 	 true//自动播放
    });
  }
  downloadReport(){
    this.TouristService.downLoad('sjtp');
  }
}
