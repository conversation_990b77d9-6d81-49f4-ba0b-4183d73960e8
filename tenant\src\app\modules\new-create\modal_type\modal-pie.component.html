<div style="display: flex;justify-content: center;">
  <echart
    [option]="chartOption"
    [width]="containerWidth"
    [height]="containerHeight"
  ></echart>
</div>
<ul style="text-align: center;font-size: 16px;" *ngIf="!sunecharts">
  <li *ngFor="let item of data; let i = index" [style.color]="colors[i % 4]">
    <em
      *ngIf="item.name != 'null' && item.name != null && item.name != ''"
      [style.background]="colors[i % 4]"
    ></em>
    <span *ngIf="item.name != 'null' && item.name != null && item.name != ''">{{
      item.name
    }}</span>
  </li>
</ul>

<ul style="text-align: center;font-size: 16px;" *ngIf="sunecharts">
  <li
    *ngFor="let item of data; let i = index"
    [style.color]="colortwo[item.id]"
  >
    <em
      *ngIf="item.name != 'null' && item.name != null && item.name != ''"
      [style.background]="colortwo[item.id]"
    ></em>
    <span *ngIf="item.name != 'null' && item.name != null && item.name != ''">{{
      item.name
    }}</span>
  </li>
</ul>
