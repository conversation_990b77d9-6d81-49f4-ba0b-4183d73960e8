@text-color: #17314C;

.history-box {
  padding: 10px 20px;
  td {
    a {
      margin: 0 5px;
    }
  }
  .disable {
    color: #F0F0F0;
    cursor: not-allowed;
  }
  .enable {
    cursor: pointer;
  }
}

.container {
  margin:0px auto;
  height: auto;
}
.index-title{ font-size:30px; color: @text-color; margin: 25px 0;font-weight:300; }

.report-header{ 
  align-items: center;
}

.tbody{
  height: 600px;
  // .vxscrollbar();
  th{ background-color: #F3F7FB;text-align: center;}
  td{ background: #ffffff;span{width: 45px;padding:0 10px;display: inline-block;}}
  .M-input,.SD-input{width: 90px;height: 24px;}
  .CM-input{width: 226px;height: 24px;}
}

footer{
  text-align: right;
  height: 60px;
  margin-top: 30px;
  .btn{
    width: 100px;
    height: 34px;
    line-height: 34px;
    background: -webkit-gradient(linear, left top, right top, from(#26d0f1), to(#409eff));
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    cursor: pointer;
    box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
    border-radius: 27px;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
  }
}
// 滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}