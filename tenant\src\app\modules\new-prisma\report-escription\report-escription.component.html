<!-- <div class="close-box">
  <i class="iconfont icon-penetra-close" (click)="closeModal()"></i>
</div> -->
<div class="box">
  <div class="content">
    <div class="content-left">
      <div class="title">
        <h3>报告说明</h3>
        <p>可上传(多张)图片覆盖说明内容</p>
      </div>
      <div class="list-box">
        <ng-container *ngFor="let item of listdata; let i = index">
          <div
            class="item"
            [ngClass]="{ active: activeIndex == i }"
            (click)="handClickActive(item, i)"
          >
            <h5>{{ item.moduleName }}</h5>
            <div
              class="item-btn"
              [ngClass]="{ showTip: item.showTip }"
              (click)="doSomething($event)"
            >
              <span
                nz-popconfirm
                nzPopconfirmTitle="确定恢复成默认吗?"
                (nzVisibleChange)="changePopconfirm($event, i)"
                (nzOnConfirm)="default(i)"
                nzPopconfirmPlacement="bottomRight"
                >恢复默认</span
              >
              <span
              (click)="handleStaticPreview(item.module)"
              >预览</span
            >
            </div>
          </div>
        </ng-container>
      </div>
    </div>
    <div class="content-right">
      <div class="title">
        <h3>上传/{{ titleName }}</h3>
        <!-- <div class="btn-upload">上传</div> -->
        <button
          nz-button
          (click)="uploadFileList()"
          [nzLoading]="isUploadLoading"
          nzType="primary"
        >
          保存
        </button>
      </div>
      <div class="file-list">
        <nz-tabset [(nzSelectedIndex)]="selectedIndex">
          <nz-tab *ngFor="let tab of tabList" [nzTitle]="tab.name">
            <div
              *ngIf="fileList[tab.type].length < 1; else fil"
              style="margin-top: 20px;"
            >
              <nz-upload
                nzType="drag"
                [nzBeforeUpload]="beforeUpload"
                [nzCustomRequest]="customReq"
                [nzShowUploadList]="false"
              >
                <div class="upload-box">
                  <img
                    src="assets/images/report-escription-upload.png"
                    alt=""
                  />
                  <p>点击添加图片或直接拖拽图片至这里</p>
                </div>
              </nz-upload>
              <p class="tip">
                建议格式: 965px*540px，500K以内，PNG格式 最多上传10张
              </p>
            </div>
            <ng-template #fil>
              <div class="list-box">
                <div class="list-head">
                  <div class="list-head-l">
                    <span>已选文件 ({{ fileList[tab.type].length }})</span>
                    <span>最多上传10张</span>
                  </div>
                  <nz-upload
                    [nzCustomRequest]="customReq"
                    [nzBeforeUpload]="beforeUpload"
                    [nzDisabled]="fileList[tab.type].length > 9"
                    [nzShowUploadList]="false"
                  >
                    <div
                      class="list-head-r"
                      [ngClass]="{ dis: fileList[tab.type].length > 9 }"
                    >
                      <img
                        src="assets/images/report-escription-add.png"
                        alt=""
                      />
                      <span>添加</span>
                    </div>
                  </nz-upload>
                </div>
                <div
                  dragula="ESCRIPTION"
                  [(dragulaModel)]="fileList[tab.type]"
                  (dragulaModelChange)="dragnumber($event)"
                >
                  <ng-container
                    *ngFor="let item of fileList[tab.type]; let i = index"
                  >
                    <div
                      class="item-file"
                      style="display: flex;align-items: center;height: 50px;box-sizing: border-box;justify-content: space-between;"
                    >
                      <div
                        class="item-file-img"
                        style="display: flex;align-items: center;"
                      >
                        <i
                          class="iconfont icon-caidan"
                          style="cursor: pointer;"
                        ></i>
                        <div
                          class="img-box"
                          (click)="handlePreview(item.fileId)"
                        >
                          <img
                            nz-tooltip
                            nzTooltipTitle="点击预览"
                            style="width: 60px;height: 38px;object-fit: cover;"
                            [src]="imgUrl + item.fileId"
                            alt=""
                          />
                        </div>
                        <span class="img-name">{{ item.fileName }}</span>
                      </div>
                      <div class="img-del">
                        <img
                          (nzOnConfirm)="del(i)"
                          nz-popconfirm
                          nzPopconfirmTitle="确定删除图片?"
                          nzPopconfirmPlacement="bottomRight"
                          src="assets/images/report-escription-del.png"
                          alt=""
                        />
                      </div>
                    </div>
                  </ng-container>
                </div>
              </div>
            </ng-template>
          </nz-tab>
        </nz-tabset>
      </div>
    </div>
  </div>
</div>
<nz-modal
  [nzVisible]="previewVisible"
  nzWidth="620"
  style="padding: 24px 10px;"
  [nzContent]="modalContent"
  [nzFooter]="null"
  (nzOnCancel)="previewVisible = false"
>
  <ng-template #modalContent>
    <div style="width: 100%;box-sizing: border-box;">
      <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
    </div>
  </ng-template>
</nz-modal>

<nz-modal
  [nzVisible]="previewStaticVisible"
  nzWidth="30%"
  style="padding: 24px 10px;"
  [nzContent]="modalContentPre"
  [nzFooter]="null"
  (nzOnCancel)="previewStaticVisible = false"
>
<ng-template #modalContentPre>
  <div style="width: 100%; margin-bottom: 20px; box-sizing: border-box;max-height: 360px;overflow: auto;" class="scroll" >
    <img [src]="item" [ngStyle]="{ width: '100%',marginBottom:'5px' }" *ngFor="let item of previewStaticImages" />
  </div>
</ng-template>
</nz-modal>
<div class="footer">
  <button nz-button nzType="default" (click)="ok()">关闭</button>
</div>
