<div class="penetration-questions">
  <i class="iconfont icon-book"></i>
  <!-- 打包报错了 应该是改完抽屉 不需要 参数了 没删干净 -->
  <!-- <span class="penetration-span" (click)="createPenetrationQuestions(tplTitle, tplContent, tplFooter)">题本追问</span> -->
  <span class="penetration-span" (click)="createPenetrationQuestions()"
    >题本关联</span
  >
  <nz-divider nzType="vertical"></nz-divider>
</div>

<!-- 题本追问弹窗 -->
<nz-drawer
  [nzBodyStyle]="{
    height: 'calc(100% - 55px)',
    overflow: 'hidden'
  }"
  [nzMaskClosable]="false"
  [nzWidth]="960"
  [nzVisible]="visible"
  [nzTitle]="'题本关联'"
  (nzOnClose)="closePenetraModal()"
  nzWrapClassName="round-right-drawer2"
>
  <div class="topic-contact-box  ">
    <nz-tabset
      [nzSelectedIndex]="activeIndex"
      (nzSelectedIndexChange)="tabsetChange($event)"
    >
      <nz-tab nzTitle="追问问题">
        <div class="penetra-modal ">
          <header>
            <div class="penetra-modal-title">
              <div class="title-left">
                <nz-upload
                  [nzCustomRequest]="customReq"
                  [nzShowUploadList]="false"
                >
                  <span class="title-icon">
                    <span class="iconfont icon-icon_import"></span> 导入</span
                  >
                </nz-upload>
                <nz-divider
                  style="margin: 0 16px;"
                  nzType="vertical"
                ></nz-divider>
                <span class="title-icon" (click)="exportR()">
                  <span class="iconfont icon-icon_export"></span> 导出</span
                >
              </div>
              <nz-divider
                style="margin: 0 16px;"
                nzType="vertical"
              ></nz-divider>
              <div
                class="associate-btn"
                nz-popover
                [nzPopoverTitle]="titleTemplate"
                nzPopoverTrigger="click"
                [(nzVisible)]="popoverVisible"
                [nzPopoverContent]="contentTemplate"
                nzPopoverPlacement="bottomRight"
              >
                已选关联({{ penetrationList.length }})
              </div>
              <ng-template #titleTemplate>
                <div class="popover-header">
                  <span>关联详情</span
                  ><a
                    nz-popconfirm
                    nzPopconfirmTitle="是否清空所有关联？"
                    nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="confirm()"
                    (nzOnCancel)="cancel()"
                    >清空所有关联</a
                  >
                </div>
              </ng-template>
              <ng-template #contentTemplate>
                <div class="popover-body">
                  <ul>
                    <li *ngFor="let ques of penetrationList">
                      <div class="condition-box">
                        <div class="popover-tips">条件</div>
                        <div class="popover-text">
                          <p
                            [innerHTML]="ques.parentQuestionName[lan] | html"
                          ></p>
                          <p>
                            <span
                              *ngFor="
                                let opt of ques.surveyStandardOptions;
                                let index = index
                              "
                              >{{
                                (index === 0 ? "" : "/") + opt.name[lan]
                              }}</span
                            >
                          </p>
                        </div>
                        <div
                          class="popover-del"
                          nz-popconfirm
                          nzPopconfirmTitle="是否删除当前题目？"
                          nzPopconfirmPlacement="bottom"
                          (nzOnConfirm)="
                            delOne(
                              ques.parentQuestionId,
                              ques.questionId,
                              ques.surveyStandardOptions
                            )
                          "
                        ></div>
                      </div>
                      <div class="result-box">
                        <div class="popover-res-tips">结果</div>
                        <div class="popover-text">
                          <p [innerHTML]="ques.questionName[lan] | html"></p>
                        </div>
                      </div>
                      <div class="popup-method">
                        {{
                          ques.answerType === "POPUP"
                            ? "选中后，立即弹出弹窗"
                            : "所有条件全部答完"
                        }}
                      </div>
                    </li>
                  </ul>
                  <div class="close-association">
                    <a (click)="closeAssociation()">收起已选关联</a>
                  </div>
                </div>
              </ng-template>
            </div>
          </header>
          <div class="penetra-modal-body">
            <div class="left">
              <div class="filter-header">
                <div>条件</div>
                <nz-select
                  [(ngModel)]="questionType"
                  [nzSize]="'large'"
                  (ngModelChange)="changeQuestionType($event)"
                  nzAllowClear
                  style="width: 150px; margin: 0 20px 0 15px;"
                  nzPlaceHolder="请选择条件"
                >
                  <nz-option [nzLabel]="'量表'" nzValue="SCALE"></nz-option>
                  <nz-option [nzLabel]="'单选'" nzValue="SINGLE"></nz-option>
                  <nz-option
                    [nzLabel]="'评价'"
                    nzValue="EVALUATION"
                  ></nz-option>
                  <nz-option
                    [nzLabel]="'多选式开放题'"
                    nzValue="MULTIPLE_CHOICE_ESSAY_QUESTION"
                  ></nz-option>
                </nz-select>
                <nz-input-group
                  nzSize="large"
                  style="width: 205px;"
                  (keyup.enter)="searchQuestion()"
                  [nzPrefix]="prefixTemplateUser"
                >
                  <input
                    type="text"
                    nz-input
                    placeholder="请输入关键词"
                    [(ngModel)]="questionStr"
                  />
                </nz-input-group>
                <ng-template #prefixTemplateUser
                  ><i nz-icon nzType="search" nzTheme="outline"></i
                ></ng-template>
              </div>
              <p class="tips">请在题本选中选项（可多选），作为穿透条件</p>
              <ul class="question-box">
                <li *ngFor="let ques of questionList; let queIdx = index">
                  <div class="label">
                    <label
                      nz-checkbox
                      [(ngModel)]="ques.checked"
                      [nzIndeterminate]="ques.indeterminate"
                      (ngModelChange)="chooseQues($event, ques.id)"
                    ></label>
                  </div>
                  <ul>
                    <li>
                      <i
                        class="iconfont icon-xiala"
                        [ngClass]="ques.selectFlag ? 'turn' : ''"
                        (click)="selectQuestion(queIdx)"
                      ></i>
                      <p [innerHTML]="ques.name[lan] | html"></p>
                    </li>
                    <ng-container *ngIf="ques.selectFlag">
                      <li class="opt" *ngFor="let opt of ques.options.options">
                        <label
                          nz-checkbox
                          [(ngModel)]="opt.checked"
                          style="margin-right: 8px;"
                          (ngModelChange)="chooseOpt($event, ques.id, opt.id)"
                        ></label>
                        {{ opt.name[lan] }}
                      </li>
                    </ng-container>
                  </ul>
                </li>
              </ul>
            </div>
            <div class="right">
              <div class="filter-header">
                <div>结果</div>
                <nz-select
                  [(ngModel)]="resultType"
                  [nzSize]="'large'"
                  (ngModelChange)="changeResultType($event)"
                  nzAllowClear
                  style="width: 150px; margin: 0 20px 0 15px;"
                  nzPlaceHolder="请选择条件"
                >
                  <nz-option
                    *ngFor="let item of resultTypeList"
                    [nzValue]="item.value"
                    [nzLabel]="item.label"
                  >
                  </nz-option>
                </nz-select>
                <nz-input-group
                  nzSize="large"
                  style="width: 205px;"
                  (keyup.enter)="searchResult()"
                  [nzPrefix]="prefixTemplateUser"
                >
                  <input
                    type="text"
                    nz-input
                    placeholder="请输入关键词"
                    [(ngModel)]="resultStr"
                  />
                </nz-input-group>
                <ng-template #prefixTemplateUser
                  ><i nz-icon nzType="search" nzTheme="outline"></i
                ></ng-template>
              </div>
              <p class="tips">请为选中的穿透条件设置关联题本，作为穿透结果</p>
              <ul class="question-box">
                <li *ngFor="let ques of resultList; let queIdx = index">
                  <div class="label">
                    <label
                      nz-radio
                      [(ngModel)]="ques.checked"
                      (ngModelChange)="chooseRes($event, ques.id, queIdx)"
                    ></label>
                  </div>
                  <ul>
                    <!-- 题目 -->
                    <li [innerHTML]="ques.name[lan] | html"></li>
                    <!-- 选项 -->
                    <!-- <li *ngFor="let option of ques.options.options">
                      {{ option.name[lan] }}
                    </li> -->
                  </ul>
                </li>
              </ul>
            </div>
          </div>
          <div class="bottom">
            <span style="margin-right: 42px; font-size: 14px; color: #495970;"
              >显示方式：</span
            >
            <nz-radio-group [(ngModel)]="answerType">
              <label style="margin-right: 100px;" nz-radio nzValue="FINAL"
                >所有条件题全部答完后</label
              >
              <label nz-radio nzValue="POPUP">选中后，立即弹出弹窗</label>
            </nz-radio-group>
          </div>
        </div>
        <div class="drawer-footer">
          <button
            nz-button
            nzType="primary"
            (click)="createQues()"
            [nzLoading]="tplModalButtonLoading"
          >
            关联
          </button>
          <button class="cancel-btn" nz-button (click)="reset()">
            恢复默认
          </button>
        </div>
      </nz-tab>
      <nz-tab nzTitle="跳过问题">
        <ng-container *ngIf="visible">
          <app-topic-skip
            #topicSkip
            [questionnaireId]="questionnaireId"
            [lan]="lan"
            (loadData)="loadDataSkip()"
          >
          </app-topic-skip>
        </ng-container>
      </nz-tab>
    </nz-tabset>
  </div>
</nz-drawer>
<!-- 题本穿透弹窗 end-->
