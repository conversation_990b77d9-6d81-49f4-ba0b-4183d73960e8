import { Injectable } from "@angular/core";
import { HttpHeaders, HttpClient } from "@angular/common/http";
import { Observable } from "rxjs";
import { NzMessageService } from "ng-zorro-antd";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Injectable({
  providedIn: "root",
})
export class ReportService {
  tenantUrl: string = "/tenant-api";
  private httpOptions = {};
  constructor(
    private http: HttpClient,
    private msgServ: NzMessageService,
    private customMsg: MessageService
  ) {
    this.httpOptions = {
      headers: new HttpHeaders({ "Content-Type": "application/json" }),
    };
  }

  listReportScheduleByPage(params: {
    page: any;
    reportType?: string;
    reportName?: string;
    name?: string;
    status?: string;
  }): Observable<any> {
    // 下载列表
    const api = `${this.tenantUrl}/sagittarius/report/content/listReportScheduleByPage`;
    return this.http.post(api, params);
  }
  // 视频解读
  getReportTypeList(): Observable<any> {
    // 视频解读报告类型下拉框调接口获取数据
    const api = `${this.tenantUrl}/sagittarius/report/content/getStandardReportType`;
    return this.http.get(api);
  }
  createOne(params: any): Observable<any> {
    // 视频解读创建人工解读
    const api = `${this.tenantUrl}/survey/standard/report/interpretation/createOne`;
    return this.http.post(api, params);
  }

  gettoollist(json): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/questionnaire/scene/type/listByTypes`;
    return this.http.post(api, json);
  }

  getStandardReportList(param: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/list`;
    return this.http.post(api, param);
  }

  downloadTemplate(): Observable<any> {
    // let httpOptions = ;
    const api = `${this.tenantUrl}/survey/person/download/simpleTemplate`;
    return this.http.post(
      api,
      {},
      { responseType: "blob", observe: "response" }
    );
  }

  uploadTemplate(json): Observable<any> {
    const api = `${this.tenantUrl}/survey/person/import/simpleTemplate`;
    return this.http.post(api, json);
  }

  getGroupedReportList(param: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/groupList`;
    return this.http.post(api, param);
  }

  getCompareReportList(param: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/listCompareReportByPage`;
    return this.http.post(api, param);
  }

  getComparePersonOrg(
    projId: string,
    nairId: string,
    reportType: string
  ): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/getCompareReportPersonOrganizationList?projectId=${projId}&questionnaireId=${nairId}&reportType=${reportType}`;
    return this.http.get(api);
  }
  // 修改报告名称
  updateGroupReportFileName(params: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/updateGroupReportFileName`;
    return this.http.post(api, params);
  }

  // 清除下载历史
  deleteReportSchedule(): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/deleteReportSchedule?isAll=false`;
    return this.http.post(api, {});
  }

  createCompareReport(
    projId: string,
    nairId: string,
    idList: any[],
    reportType
  ): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/createCompareReport`;
    let param = {
      projectId: projId,
      questionnaireId: nairId,
      personOrganizationIds: idList,
      reportType: reportType,
    };
    return this.http.post(api, param);
  }

  formatTime(d, type) {
    const date = new Date(d);
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const strDate = date
      .getDate()
      .toString()
      .padStart(2, "0");
    let time;
    if (type === 1) {
      time = `${date.getFullYear()}-${month}-${strDate} 23:59:59`;
    } else {
      time = `${date.getFullYear()}-${month}-${strDate} 00:00:00`;
    }
    return new Date(time);
  }
  formatTime1(date) {
    const year = date.getFullYear(); // 获取年份
    const month = (date.getMonth() + 1).toString().padStart(2, "0"); // 获取月份，+1是因为getMonth()返回的是0-11
    const day = date
      .getDate()
      .toString()
      .padStart(2, "0"); // 获取日期
    const hours = date
      .getHours()
      .toString()
      .padStart(2, "0"); // 获取小时
    const minutes = date
      .getMinutes()
      .toString()
      .padStart(2, "0"); // 获取分钟
    const seconds = date
      .getSeconds()
      .toString()
      .padStart(2, "0"); // 获取秒数
    const time = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    return new Date(time);
  }

  downFile(data) {
    this.downFileWithName(data, "");
  }

  downFileWithName(data, forcefileName) {
    console.log(data);
    // return

    this.showBlobErrorMessage(data);

    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });

    let fileName;
    if (forcefileName) {
      fileName = decodeURIComponent(forcefileName);
    }
    if (!fileName) {
      fileName = data.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      if (!fileName) {
        fileName = data.headers
          .get("Content-Disposition")
          .split(";")[1]
          .split("fileName=")[1];
      }

      let fileNameUnicode = data.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      if (!fileNameUnicode) {
        fileNameUnicode = data.headers
          .get("Content-Disposition")
          .split("fileName*=")[1];
      }
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
        // .split('\'\'')[1]
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
    }

    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  showBlobErrorMessage(data: any) {
    let body = data.body;
    if (body.type === "application/json") {
      let that = this;
      const reader = new FileReader();
      reader.readAsText(body, "utf-8");
      reader.onload = () => {
        // 处理报错信息
        // JSON.parse(reader.result) 拿到报错信息
        let resp: any = JSON.parse(reader.result + "");
        let code: number = resp.result.code;
        let errMsg: string = resp.result.message;
        console.log("errMsg = " + errMsg);
        if (code === 210115 || code === 210117) {
          // that.msgServ.error(`${errMsg}`);
          that.customMsg.open("error", `${errMsg}`);
        } else if (code !== 0) {
          // that.msgServ.error(`${errMsg}，请联系管理员。`);
          that.customMsg.open("error", `${errMsg}，请联系管理员。`);
        }
      };
    }
  }
  getReportScheduleInitCount(): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/getReportScheduleInitCount`;
    return this.http.get(api);
  }

  // 人才地图
  // 修改报告名称
  createMappingReport(params: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/create`;
    return this.http.post(api, params);
  }
  // 九宫格列表
  getMappingReportSudokuList(itemId): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/detail/${itemId}`;
    return this.http.get(api);
  }
  // 九宫格创建
  createMappingReportSudoku(params: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/create`;
    return this.http.post(api, params);
  }
  // 九宫格修改
  updateMappingReportSudoku(params: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/update`;
    return this.http.post(api, params);
  }

  // 九宫格详情
  getMappingReportSudokuItems(itemId): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/${itemId}`;
    return this.http.get(api);
  }
  // 九宫格字典 呈现名称 工具类型
  getMappingReportSudokuDictionaries(
    type: "DISPLAY_NAME" | "QUESTIONNAIRE_TYPE",
    reportId: string
  ): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/type/list/${type}/${reportId}`;
    return this.http.get(api);
  }
  // 九宫格字典 自定义
  createMappingReportSudokuDictionaries(params: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/type/create`;
    return this.http.post(api, params);
  }
  // 获取人才地图报告九宫格呈现设置区域列表接口
  getMappingReportSudokuDisplayAreaList(params: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/display/area/list`;
    return this.http.post(api, params);
  }
  // 创建或修改人才地图报告九宫格呈现接口
  getMappingReportSudokuCreateOrUpdate(params: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/display/createOrUpdate`;
    return this.http.post(api, params);
  }
  // 分页获取人才地图报告九宫格人员列表接口
  getMappingReportSudokuObjectListByPage(params: any): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/object/listByPage`;
    return this.http.post(api, params);
  }
  // 导出人才地图报告九宫格人员列表接口
  getObjectDownLoad(itemId): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    let api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/object/export?itemId=${itemId}`;
    return this.http.get(api, httpOptions);
  }
  // 导入人才地图报告九宫格人员列表接口
  postObjectUpload(json: any, itemId: string): Observable<any> {
    let api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/object/import?itemId=${itemId}`;
    return this.http.post(api, json);
  }
  // 确认人才地图报告九宫格对象设置人员数据接口
  postMappingReportSudokuItemsObjectConfirm(itemId): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/object/confirm/${itemId}`;
    return this.http.post(api, {});
  }
  // 删除人才地图报告九宫格接口
  postMappingReportSudokuItemsDel(itemId): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/delete/${itemId}`;
    return this.http.post(api, {});
  }
  // 获取人才地图报告九宫格未完善人员数量接口
  getMappingReportSudokuItemObjectUnRefineCount(itemId): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/item/object/unRefineCount/${itemId}`;
    return this.http.get(api);
  }
  // 获取人才地图报告人员数量接口
  getMappingReportPersonCount(reportId): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/talent/mapping/report/personCount/${reportId}`;
    return this.http.get(api);
  }
  // 保存报告列表选中的数据
  public saveBatchReportEmailObjects(data: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/saveBatchReportEmailObjects`;
    return this.http.post(url, data);
  }

  // 按报告邮件内容id分页查询
  public pageByReportMailId(
    data: any,
    reportMailContentId: string
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/pageByReportMailId?reportMailContentId=${reportMailContentId}`;
    return this.http.post(url, data);
  }

  // 发送报告邮件 sendReportEmail
  public sendReportEmail(data: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/sendReportEmail`;
    return this.http.post(url, data);
  }
  // 导入批量发送数据
  public readReportSendExcel(
    formData: any,
    reportMailContentId,
    isGroup
  ): Observable<any> {
    // 导入
    const url = `${this.tenantUrl}/survey/project/readReportSendExcel?reportMailContentId=${reportMailContentId}&isGroup=${isGroup}`;
    return this.http.post(url, formData);
  }
  // 导出批量发送数据
  public exportReportSendExcel(reportMailContentId: any): Observable<any> {
    // 导出
    this.httpOptions = { responseType: "Blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/project/exportReportSendExcel?reportMailContentId=${reportMailContentId}`;
    return this.http.post(url, {}, this.httpOptions);
  }
  //组织架构下载模板
  downloadSimpleTemplate(projectId): Observable<any> {
    // let httpOptions = ;
    const api = `${this.tenantUrl}/survey/project/download/orgaztion/simpleTemplate?projectId=${projectId}`;
    return this.http.get(api, { responseType: "blob", observe: "response" });
  }
  // 组织架构高级筛选导入文件
  uploadSimpleTemplate(json, projectId): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/import/simpleTemplate/orgaztion?projectId=${projectId}`;
    return this.http.post(api, json);
  }

  //人口标签下载模板
  downloadDemographicSimpleTemplate(projectId): Observable<any> {
    // let httpOptions = ;
    const api = `${this.tenantUrl}/survey/project/download/demographic/simpleTemplate?projectId=${projectId}`;
    return this.http.get(api, { responseType: "blob", observe: "response" });
  }
  // 人口标签高级筛选导入文件
  uploadSimpleTemplateDemographic(json, projectId): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/import/simpleTemplate/demographic?projectId=${projectId}`;
    return this.http.post(api, json);
  }

  // 高级筛选-团队报告-人员&组织-下载模板
  downloadOrgPersonTemplate(type:string): Observable<any> {
    // let httpOptions = ;
    const api = `${this.tenantUrl}/sagittarius/report/content/download/orgPerson/simpleTemplate?type=${type}`;
    return this.http.post(
      api,
      {},
      { responseType: "blob", observe: "response" }
    );
  }

  // 高级筛选-团队报告-人员&组织-上传文件
  uploadOrgPersonTemplate(json): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/import/orgPerson/simpleTemplate`;
    return this.http.post(api, json);
  }

  // 查询个人报告中生成失败的数量
  getPersonReportFailCount(): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/getPersonReportFailCount`;
    return this.http.post(api, {});
  }
  // 查询团队报告中生成失败的数量
  getGroupReportFailCount(): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/getGroupReportFailCount`;
    return this.http.post(api, {});
  }
}
