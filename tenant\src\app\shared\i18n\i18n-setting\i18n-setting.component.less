/**
 *
 *  @author: <PERSON>
 *  @Date: 2023/09/18
 *  @content: 调研-高级设置-语言设置组件
 *
*/
.i18nSetting {
  position: relative;
  .mr-8 {
    margin-right: 8px;
  }
  .mr-16 {
    margin-right: 16px;
  }
  .mr-24 {
    margin-right: 24px;
  }
  .fixed-top {
    position: absolute;
    top: 0;
    background-color: #fff;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.05); /* 水平偏移量 垂直偏移量 模糊半径 阴影颜色 */
  }
  &-tabs {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ececec;
    margin: 16px 16px 0 16px;
    &-tab {
      display: flex;
      max-width: 700px;
      overflow-x: auto;
      &-item {
        font-size: 16px;
        font-weight: 400;
        color: #595959;
        line-height: 22px;
        border-bottom: 4px solid transparent;
        padding-bottom: 8px;
        // margin-right: 40px;
        margin-right: 32px;
        white-space: nowrap;
        cursor: pointer;
      }
      .active {
        color: #409eff;
        border-color: #409eff;
      }
    }
    &-extra {
      display: flex;
      justify-content: flex-start;
      ::ng-deep {
        .ant-upload-list-item {
          margin-top: 0;
          margin-left: 8px;
        }
      }
      button {
        margin-left: 40px;
        height: 24px;
        line-height: 24px;
        padding: 0;
      }
    }
  }
  &-operation {
    width: 100%;
    background: #ffffff;
    > div {
      margin: 16px;
      height: 52px;
      background: linear-gradient(
        90deg,
        #f8f8f8 0%,
        rgba(248, 248, 248, 0) 100%
      );
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
    }
    &-left {
      span {
        font-size: 14px;
        font-weight: 500;
        color: #595959;
        line-height: 20px;
      }
      .text-red {
        color: #ff4f40;
      }
    }
  }
  &-group {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    height: calc(100vh - 243px);
    overflow-y: auto;
    position: relative;
    top: 135px;
    padding: 16px;
    &-item {
      width: 48%;
      p {
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        line-height: 20px;
      }
      > div {
        margin-bottom: 16px;
      }
    }
  }
  &-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }
  ::ng-deep {
    .ant-tabs .ant-tabs-tab {
      padding: 2px 0 10px 0;
    }
    .ant-tabs-ink-bar {
      height: 4px;
      border-radius: 2px;
    }
    .ant-tabs-extra-content {
      line-height: 24px;
    }
  }
}

::ng-deep {
  .i18n-setting-drawer {
    .ant-drawer-body {
      // padding: 16px;
      padding: 0;
      height: calc(100% - 55px);
      overflow: auto;
      padding-bottom: 66px;
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-size: 20px;
      font-weight: bold;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
    .footer {
      position: absolute;
      bottom: 0px;
      width: 100%;
      border-top: 1px solid rgb(232, 232, 232);
      padding: 10px 16px;
      text-align: right;
      left: 0px;
      background: #fff;
    }
  }
}
