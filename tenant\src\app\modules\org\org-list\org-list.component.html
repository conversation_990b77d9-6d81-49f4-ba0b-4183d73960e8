<div class="org" id="orgDiv">
  <div class="content client-width">
    <div class="header">
      <div class="title">
        <span>组织架构</span>
        <div class="search" *ngIf="listType === 'list'">
          <nz-input-group [nzSuffix]="IconSearch">
            <input
              type="text"
              nz-input
              placeholder="请输入关键词"
              [(ngModel)]="keyWord"
              (keydown.enter)="search()"
            />
          </nz-input-group>
          <ng-template #IconSearch>
            <i
              nz-icon
              nzType="search"
              class="icon-search"
              (click)="search()"
            ></i>
          </ng-template>
        </div>
      </div>
      <app-break-crumb
        [Breadcrumbs]="Breadcrumbs"
        [queryParams]="queryParams"
      ></app-break-crumb>
    </div>
    <!-- 国际化切换 -->
    <app-i18n-select
      [active]="lan"
      (selectChange)="onSelectI18n($event)"
    ></app-i18n-select>
    <div class="btns">
      <div>
        <button
          *ngIf="listType === 'list'"
          class="btn-primary"
          (click)="edit('0')"
        >
          <i nz-icon nzType="plus-circle" nzTheme="fill"></i>新增
        </button>
        <nz-upload
          class="upload"
          [nzCustomRequest]="customReq"
          [nzFilter]=""
          [nzShowUploadList]="false"
        >
          <button class="btn-ghost">
            <i class="iconfont icon-icon_export"></i>导入
          </button>
        </nz-upload>
        <button class="btn-link" (click)="exportExcel()" *ngIf="!typeshow">
          导出模板
        </button>
        <button
          class="btn-link"
          (click)="exportList(projectId)"
          *ngIf="projectType && projectType !== 'ANNOUNCED'"
        >
          导出列表
        </button>
        <button
          class="btn-link"
          nz-popconfirm
          [nzPopconfirmTitle]="'是否一键清除组织？'"
          nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="clearOrganization(projectId)"
          *ngIf="
            projectType &&
            (projectType == 'ANNOUNCED' || projectType == 'PREVIEW')
          "
        >
          一键清除
        </button>
      </div>
      <div>
        <button
          [ngClass]="listType === 'list' ? 'btn-link' : 'btn-text'"
          (click)="listType = 'list'"
        >
          <i class="iconfont icon-list-model"></i>列表模式
        </button>
        <nz-divider nzType="vertical"></nz-divider>
        <button
          [ngClass]="listType === 'tree' ? 'btn-link' : 'btn-text'"
          (click)="listType = 'tree'"
        >
          <i class="iconfont icon-zuzhiqunzu"></i>树形模式
        </button>
      </div>
    </div>
    <div class="tableOrTree">
      <ng-container *ngIf="listType === 'list'">
        <div class="scroll tableBorder">
          <nz-table #nzTable [nzData]="orgList" nzFrontPagination="false">
            <!-- [nzScroll]="{ x: '1180px' }" -->
            <thead>
              <tr>
                <th nzWidth="5%">序号</th>
                <ng-container *ngFor="let item of i18n">
                  <th *ngIf="lan === item.value" nzWidth="17.5%">
                    组织名称（{{ item.name }}）
                  </th>
                </ng-container>
                <th nzWidth="15.5%">组织编码</th>
                <th nzWidth="15.5%">组织说明</th>
                <th nzWidth="11%">上级组织</th>
                <th nzWidth="11%">上级组织编码</th>
                <th nzWidth="8%">&nbsp;&nbsp;操作</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let tbData of nzTable.data; let i = index">
                <td>{{ i + 1 + (currentPage - 1) * pageSize }}</td>
                <td>
                  <ng-container *ngFor="let item of i18n">
                    <div
                      class="ellips"
                      style="max-width: 220px;"
                      *ngIf="lan === item.value"
                      nz-tooltip
                      nzPlacement="topLeft"
                      [nzTitle]="tbData.name[item.value]"
                    >
                      {{ tbData.name[item.value] }}
                      <span
                        class="tag_invalid"
                        *ngIf="tbData.status == 'DISABLE'"
                        >禁用</span
                      >
                    </div>
                  </ng-container>
                </td>
                <td>
                  <div
                    class="ellips"
                    style="max-width: 180px;"
                    nz-tooltip
                    nzPlacement="topLeft"
                    [nzTitle]="tbData.code"
                  >
                    {{ tbData.code }}
                  </div>
                </td>
                <td>
                  <div
                    class="ellips"
                    style="max-width: 210px;"
                    nz-tooltip
                    nzPlacement="topLeft"
                    [nzTooltipTitle]="TooltipTemp"
                  >
                    {{ tbData.description ? tbData.description + ";" : "" }}
                    {{ tbData.isVirtual ? "虚拟组织" : "" }}
                  </div>
                  <ng-template #TooltipTemp>
                    {{ tbData.description ? tbData.description + ";" : "" }}
                    {{ tbData.isVirtual ? "虚拟组织" : "" }}
                  </ng-template>
                </td>
                <td>
                  <div
                    class="ellips"
                    style="max-width: 150px;"
                    nz-tooltip
                    nzPlacement="topLeft"
                    [nzTitle]="tbData.parentOrganization?.name?.zh_CN"
                  >
                    {{ tbData.parentOrganization?.name?.zh_CN }}
                  </div>
                </td>
                <td>
                  <div
                    class="ellips"
                    style="max-width: 130px;"
                    nz-tooltip
                    nzPlacement="topLeft"
                    [nzTitle]="tbData.parentOrganization?.code"
                  >
                    {{ tbData.parentOrganization?.code }}
                  </div>
                </td>
                <td>
                  <app-btn
                    [text]="''"
                    [image]="'./assets/images/org/edit.png'"
                    [hoverColor]="'#409EFF'"
                    nz-tooltip
                    nzTitle="编辑"
                    [hoverImage]="'./assets/images/org/edit_hover.png'"
                    (btnclick)="edit(tbData.id)"
                  >
                  </app-btn>
                  <app-btn
                    *ngIf="tbData.distance !== 0 && !typeshow"
                    [text]="''"
                    [image]="'./assets/images/org/del.png'"
                    [hoverColor]="'#409EFF'"
                    nz-tooltip
                    nzTitle="删除"
                    [hoverImage]="'./assets/images/org/del_hover.png'"
                    nz-popconfirm
                    [nzPopconfirmTitle]="
                      '确定要删除组织【' + tbData.name?.zh_CN + '】？'
                    "
                    nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="delete(tbData.id)"
                  >
                  </app-btn>
                </td>
              </tr>
            </tbody>
          </nz-table>
        </div>
        <div class="flex-end mt-20">
          <nz-pagination
            [(nzPageIndex)]="currentPage"
            [(nzPageSize)]="pageSize"
            [nzTotal]="totalCount"
            [nzSize]="'small'"
            (nzPageIndexChange)="loadData()"
            nzShowQuickJumper
          >
          </nz-pagination>
        </div>
      </ng-container>
      <ng-container *ngIf="listType !== 'list'">
        <div class="tree-box">
          <app-org-charts
            #datepicker
            (fatherAdd)="add($event)"
            (fatherEdit)="editTree($event)"
            [typeshow]="typeshow"
            [lan]="lan"
            [projectCode]="projectCode"
          ></app-org-charts>
        </div>
      </ng-container>

      <div class="flex-end mt-20 mb-20">
        <button class="iptBtn" (click)="orgConfirm()" appDisableTime>
          <span>确认</span>
        </button>
      </div>
    </div>
  </div>
</div>
