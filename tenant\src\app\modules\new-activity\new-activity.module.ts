import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { SharedModule } from "@shared";

import { Routes, RouterModule } from "@angular/router";
import { NewActivityComponent } from "./new-activity.component";
import { SurveyBookComponent } from "./survey-book/survey-book.component";
import { SubjectEditComponent } from "./subject-edit/subject-edit.component";
import { NameEditComponent } from "./name-edit/name-edit.component";
import { SubjectAddComponent } from "./subject-add/subject-add.component";
import { SubjectBatchEditComponent } from "./subject-batch-edit/subject-batch-edit.component";
import { OpenSubjectComponent } from "./open-subject/open-subject.component";
import { BookSettingComponent } from "./book-setting/book-setting.component";

import { ToolCardComponent } from "./tool_cards/tool_cards.component";
import { NameDescEditComponent } from "./name-desc-edit/name-desc-edit.component";
import { SurveyBookNewComponent } from "./survey-book-new/survey-book-new.component";
import { SurveyBookModalComponent } from "./survey-book-new/modal-content/survey-book-modal.component";
import { PrismaCustomBookComponent } from "./prisma-custom-book/prisma-custom-book.component";
import { AddPrismaCustomBookComponent } from "./prisma-custom-book/add-prisma-custom-book/add-prisma-custom-book.component";
import { PentrateModal } from "./prisma-custom-book/pentrate-modal/pentrate-modal.component";
import { MultiView } from "./prisma-custom-book/multi-view/multi-view.component";
import { TopicSkipComponent } from "./prisma-custom-book/pentrate-modal/component/topic-skip.component";


const routes: Routes = [
  {
    path: "",
    component: NewActivityComponent,
    data: { permissionCode: "SAG:TENANT:PROJECT_MGT:CREATE_SELECT" },
  },
  { path: "book", component: SurveyBookNewComponent },
  { path: "custom-book", component: PrismaCustomBookComponent },
];

@NgModule({
  declarations: [
    NewActivityComponent,
    SurveyBookComponent,
    SubjectEditComponent,
    NameEditComponent,
    SubjectAddComponent,
    SubjectBatchEditComponent,
    OpenSubjectComponent,
    BookSettingComponent,
    ToolCardComponent,
    NameDescEditComponent,
    SurveyBookNewComponent,
    SurveyBookModalComponent,
    PrismaCustomBookComponent,
    AddPrismaCustomBookComponent,
    PentrateModal,
    TopicSkipComponent,
    MultiView,
  ],
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
  exports: [RouterModule],
  entryComponents: [
    SubjectEditComponent,
    SubjectBatchEditComponent,
    SubjectAddComponent,
    NameEditComponent,
    NameDescEditComponent,
    OpenSubjectComponent,
    BookSettingComponent,
    ToolCardComponent,
    SurveyBookModalComponent,
    AddPrismaCustomBookComponent,
    PentrateModal,
    TopicSkipComponent,
    MultiView,
  ],
})
export class NewActivityModule {}
