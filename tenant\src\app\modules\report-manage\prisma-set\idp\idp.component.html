<div class="title">
    <div class="col1">
        <span class="label">分类</span>
        <button nz-button [nzSize]="'small'" nzType="link" (click)="add()">
            <img src="./assets/images/org/rpt_add.png "><span style="padding-left: 5px;">添加</span>
        </button>
    </div>
    <div class="col2">
        <span class="label">内容</span>
        <button nz-button [nzSize]="'small'" nzType="link" (click)="saveAll()">
            <span>全部保存</span>
        </button>
    </div>
</div>


<div class="con">

    <div *ngFor="let item of modelList" class="idp">
    
        <div class="col1">
            <input nz-input placeholder="中文" [(ngModel)]="item.category.zh_CN" />
            <input nz-input placeholder="English" [(ngModel)]="item.category.en_US" style="margin-top: 10px;" />
        </div>
    
        <div class="col2">
            <textarea nz-input rows="1" placeholder="中文" nz-tooltip [nzTooltipTitle]="item.content.zh_CN" [(ngModel)]="item.content.zh_CN" ></textarea>
            <textarea nz-input rows="1" placeholder="English" nz-tooltip [nzTooltipTitle]="item.content.en_US" [(ngModel)]="item.content.en_US" style="margin-top: 10px;" ></textarea>
        </div>
        
        <div style="margin-left: 10px;">
            <button [disabled]="item.isDefault === true" nz-button nzType="default" nzShape="circle" (click)="delete(item.delId)">
                <i nz-icon nzType="delete" nzTheme="outline" ></i>
            </button>
        </div>
    
    </div>

</div>

