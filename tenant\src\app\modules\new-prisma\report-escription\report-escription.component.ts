import { Component, OnInit, Input, On<PERSON><PERSON>roy } from "@angular/core";
import _ from "lodash";

import { NzDrawerRef } from "ng-zorro-antd/drawer";
import { NewPrismaService } from "../new-prisma.service";
import { UploadXHRArgs, UploadFile } from "ng-zorro-antd/upload";
import { NzModalService, NzMessageService } from "ng-zorro-antd";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { DragulaService } from "ng2-dragula";
import { Observable, Observer, Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-report-escription",
  templateUrl: "./report-escription.component.html",
  styleUrls: ["./report-escription.component.less"],
})
export class ReportEscription implements OnInit, OnDestroy {
  @Input() questionnaireId: string;
  @Input() reportType: string;
  tenantUrl: string = "/tenant-api";
  previewImage: string | undefined = "";
  previewStaticImages: any[];
  previewVisible = false;
  previewStaticVisible = false;

  imgUrl: string = "";
  staticImgUrl: string = "";
  fileList = {
    ZH_CN: [],
    EN_US: [],
  };
  isUploadLoading = false;
  titleName = null;
  listdata = [];
  activeIndex = 0;
  tabList = [
    {
      name: "中文版",
      type: "ZH_CN",
    },
    {
      name: "英文版",
      type: "EN_US",
    },
  ];
  selectedIndex = 0;
  private routerSubscription: Subscription;
  constructor(
    private drawerRef: NzDrawerRef,
    private api: NewPrismaService,
    private nzModalService: NzModalService,
    private msg: NzMessageService,
    private http: HttpClient,
    private dragulaService: DragulaService,
    private customMsg: MessageService,
    private router: Router
  ) {}
  submitForm(form: any) {}
  closeModal() {
    // this.nzModalService.closeAll()
    this.drawerRef.close();
  }

  ngOnInit() {
    let baseUrl: string = window.location.origin + "/";
    this.staticImgUrl = "assets/images/report-module/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      // baseUrl = 'http://***********/'
      baseUrl = "https://sag-qa.knxdevelop.com/";
    }
    this.imgUrl = `${baseUrl}api/file/www/`;
    this.api.pictureSettingList(this.questionnaireId).subscribe((res) => {
      // console.log(res);
      if (res.result.code === 0) {
        let data = res.data;
        let obj = {};
        this.tabList.forEach((item) => {
          obj[item.type] = [];
        });
        data.forEach((item) => {
          item.list = _.cloneDeep(obj);
          item.settings.forEach((element) => {
            if (item.list[element.language]) {
              item.list[element.language] = _.cloneDeep(element.files);
            }
          });
        });
        this.listdata = _.cloneDeep(data);
        this.fileList = _.cloneDeep(this.listdata[0].list);
        this.titleName = _.cloneDeep(this.listdata[0].moduleName);
      }
    });
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy() {
    // 销毁事件
    this.dragulaService.destroy("ESCRIPTION");
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
  handClick(item) {
    if (!item.disabled) item.onclick();
  }
  ok() {
    this.closeModal();
  }
  del(i) {
    this.fileList[this.tabList[this.selectedIndex].type].splice(i, 1);
    this.getNewList();
  }
  default(i) {
    this.listdata[this.activeIndex].list = _.cloneDeep(this.fileList);
    let params = _.cloneDeep(this.listdata[i]);
    let obj = {};
    this.tabList.forEach((item) => {
      obj[item.type] = [];
    });
    params.list = _.cloneDeep(obj);
    params.settings = [];
    this.api.pictureSettingModify(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("恢复默认值成功");
        params.showTip = false;
        this.listdata[i] = params;
        this.fileList = _.cloneDeep(this.listdata[this.activeIndex].list);
      }
    });
  }
  changePopconfirm(e, i) {
    this.listdata.forEach((element) => {
      element.showTip = false;
    });
    if (e) {
      this.listdata[i].showTip = true;
    }
  }
  doSomething(e) {
    e.stopPropagation();
  }
  getNewList() {
    let lan = Object.keys(this.fileList);
    let lanlist = Object.values(this.fileList);
    let settings = [];
    lan.forEach((item, index) => {
      settings.push({
        language: item,
        files: lanlist[index],
      });
    });
    this.listdata[this.activeIndex].settings = _.cloneDeep(settings);
    this.listdata[this.activeIndex].list = _.cloneDeep(this.fileList);
  }
  dragnumber(e) {
    console.log(e, this.fileList);
  }
  uploadFileList() {
    this.getNewList();
    this.isUploadLoading = true;
    this.api
      .pictureSettingModify(this.listdata[this.activeIndex])
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.msg.success("保存成功");
        }
        this.isUploadLoading = false;
      });
  }
  handClickActive(item, i) {
    if (this.activeIndex != i) {
      this.listdata[this.activeIndex].list = _.cloneDeep(this.fileList);
      this.activeIndex = i;
      this.fileList = _.cloneDeep(item.list);
      this.titleName = item.moduleName;
      this.selectedIndex = 0;
    }
  }
  /**
   *图片预览
   *@author:wangxiangxin
   *@Date:2023/08/24
   */
  handlePreview = (id) => {
    this.previewImage = this.imgUrl + id;
    this.previewVisible = true;
  };

  /**
   *静态图片预览
   */
  handleStaticPreview = (id) => {
    this.previewStaticImages = [];
    if (this.reportType === "TENCENT_INVESTIGATION_RESEARCH_CUSTOM") {
      if (id === "IAS_METHODOLOGY") {
        for (let i = 1; i <= 3; i++) {
          this.previewStaticImages.push(
            this.staticImgUrl + "tencent/" + id + i + ".png"
          );
        }
      } else {
        this.previewStaticImages.push(
          this.staticImgUrl + "tencent/" + id + ".png"
        );
      }
    } else if (this.reportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
      if (id === "ADVANTAGE_DISADVANTAGE") {
        for (let i = 1; i <= 2; i++) {
          this.previewStaticImages.push(
            this.staticImgUrl + "netease/" + id + i + ".png"
          );
        }
      } else if (id === "IAS_METHODOLOGY") {
        for (let i = 1; i <= 3; i++) {
          this.previewStaticImages.push(
            this.staticImgUrl + "netease/" + id + i + ".png"
          );
        }
      } else {
        this.previewStaticImages.push(
          this.staticImgUrl + "netease/" + id + ".png"
        );
      }
    } else if (this.reportType === "EPSON_INVESTIGATION_RESEARCH_CUSTOM") {
      if (id === "IAS_METHODOLOGY") {
        for (let i = 1; i <= 4; i++) {
          this.previewStaticImages.push(
            this.staticImgUrl + "epson/" + id + i + ".png"
          );
        }
      } else {
        this.previewStaticImages.push(
          this.staticImgUrl + "epson/" + id + ".png"
        );
      }
    } else if (
      this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM" ||
      this.reportType === "OC_INVESTIGATION_RESEARCH"
    ) {
      if (id === "ADVANTAGE_DISADVANTAGE") {
        for (let i = 1; i <= 2; i++) {
          this.previewStaticImages.push(
            this.staticImgUrl + "dual-perspective/" + id + i + ".png"
          );
        }
      } else if (id === "RELEVANT_ANALYTICAL_NOTES") {
        for (let i = 1; i <= 2; i++) {
          this.previewStaticImages.push(
            this.staticImgUrl + "dual-perspective/" + id + i + ".png"
          );
        }
      } else {
        this.previewStaticImages.push(
          this.staticImgUrl + "dual-perspective/" + id + ".png"
        );
      }
    } else if (
      this.reportType === "INVESTIGATION_RESEARCH_CUSTOM" ||
      this.reportType === "INVESTIGATION_RESEARCH"
    ) {
      if (id === "ADVANTAGE_DISADVANTAGE") {
        for (let i = 1; i <= 2; i++) {
          this.previewStaticImages.push(
            this.staticImgUrl + "custom/" + id + i + ".png"
          );
        }
      } else if(id === "FOUR_WINDOW_DESCRIPTION"){
        for (let i = 1; i <= 2; i++) {
          this.previewStaticImages.push(
            this.staticImgUrl + "custom/" + id + i + ".png"
          );
        }
      } else {
        this.previewStaticImages.push(
          this.staticImgUrl + "custom/" + id + ".png"
        );
      }
    }

    this.previewStaticVisible = true;
  };
  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    let type = [".jpg", ".jpeg", ".png"];
    formData.append("file", item.file as any);
    formData.append("isPublic", "false");
    formData.append("effectiveFileTypes", type as any);
    formData.append("businessType", "DEFAULT");

    this.uploadAttachmentUrl(formData, item);
  };
  /**
   * beforeUpload 校验上传的图片格式
   * @param file
   */
  beforeUpload = (file: File) => {
    return new Observable((observer: Observer<boolean>) => {
      let imgType = ["image/jpeg", "image/jpg", "image/png"];
      // file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/gif' || file.type === 'image/bmp'
      const isPNG = imgType.includes(file.type);
      const isSize = file.size / 1024 < 500;
      if (!isPNG) {
        // this.msg.error("文件类型不合法,只能是png/jpg/jpeg类型！");
        this.customMsg.open("error", "文件类型不合法,只能是png/jpg/jpeg类型！");
        observer.complete();
        return;
      }
      if (!isSize) {
        // this.msg.error("图片应小于500KB!");
        this.customMsg.open("error", "图片应小于500KB!");
        observer.complete();
        return;
      }

      observer.next(isPNG && isSize);
      observer.complete();
    });
  };
  /**
   * uploadAttachmentUrl 上传pdf报告文件
   */
  uploadAttachmentUrl(formData, item) {
    const url = `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`;
    return this.http.post(url, formData).subscribe(
      (res: any) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.msg.success("上传文件成功");
          this.fileList[this.tabList[this.selectedIndex].type].push({
            fileId: res.data.id,
            fileName: item.file.name,
          });
        } else {
          item.onError!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }
}
