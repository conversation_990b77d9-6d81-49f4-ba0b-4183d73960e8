import { HttpClient } from "@angular/common/http";
import { Component, Input, OnInit } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd";
import _ from "lodash";
import { Observable } from "rxjs";
import { element } from "protractor";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-xi-shu",
  templateUrl: "./xi-shu.component.html",
  styleUrls: ["./xi-shu.component.less"],
})
export class XiShuComponent implements OnInit {
  @Input() prismaReportDataId: string;

  updateId: string;

  page: number = 1;

  name: any = { zh_CN: "", en_US: "" };

  catList = [];
  catOtherList = [];

  catMap = {};
  catOtherMap = {};

  groupHidden: boolean = true;

  groupList: any[] = [];
  groupData;

  dimQuesList: any[] = [];
  dimQuesList2: any[] = []; // 第二页数据

  twoDash: string = "__";

  tenantUrl: string = "/tenant-api";

  constructor(
    private http: HttpClient,
    private msgServ: NzMessageService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    this.bgGetGroupList();
    this.bgGetDimList();
  }

  nextPage(e) {
    if (!this.name.zh_CN) {
      // this.msgServ.error("名称不能为空。");
      this.customMsg.open("error", "名称不能为空。");
      return;
    }
    let tmpList = [];
    for (let index = 0; index < this.catList.length; index++) {
      let cat: any = this.catList[index];
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const it = items[j];
        if (it.isShow && it.checked) {
          tmpList.push(this.getSingleParam(it.value));
        }
      }
    }
    // if (tmpList.length === 0) return this.msgServ.warning("请选择维度");
    if (tmpList.length === 0)
      return this.customMsg.open("warning", "请选择维度");
    if (e) {
      this.page = 2;
      this.bgGetPage2DimList();
    } else {
      this.page = 1;
      this.catOtherList = [];
    }
  }

  intData() {
    // 大维度
    let tmp = [];
    let list: any[] = this.getSelection1();
    let obj = {
      id: "dimension",
      name: "大维度",
      allChecked: false,
      indeterminate: false,
      items: list[0],
      type: 1,
      checkedValues: [],
      searchText: "",
    };
    this.catList.push(obj);
    this.catMap[obj.type] = obj;

    // 子维度
    tmp = [];
    obj = {
      id: "subDimension",
      name: "子维度",
      allChecked: false,
      indeterminate: false,
      items: list[1],
      type: 2,
      checkedValues: [],
      searchText: "",
    };
    this.catList.push(obj);
    this.catMap[obj.type] = obj;

    // 题本
    tmp = [];
    obj = {
      id: "question",
      name: "题本",
      allChecked: false,
      indeterminate: false,
      items: list[2],
      type: 3,
      checkedValues: [],
      searchText: "",
    };
    this.catList.push(obj);
    this.catMap[obj.type] = obj;
  }
  intData1() {
    // 第二页数据
    // 大维度
    this.catOtherList = [];
    let tmp = [];
    let list: any[] = this.getSelection1(true);
    let obj = {
      id: "dimension",
      name: "大维度",
      allChecked: false,
      indeterminate: false,
      items: list[0],
      type: 1,
      checkedValues: [],
      searchText: "",
    };
    this.catOtherList.push(obj);
    this.catOtherMap[obj.type] = obj;

    // 子维度
    tmp = [];
    obj = {
      id: "subDimension",
      name: "子维度",
      allChecked: false,
      indeterminate: false,
      items: list[1],
      type: 2,
      checkedValues: [],
      searchText: "",
    };
    this.catOtherList.push(obj);
    this.catOtherMap[obj.type] = obj;

    // 题本
    tmp = [];
    obj = {
      id: "question",
      name: "题本",
      allChecked: false,
      indeterminate: false,
      items: list[2],
      type: 3,
      checkedValues: [],
      searchText: "",
    };
    this.catOtherList.push(obj);
    this.catOtherMap[obj.type] = obj;
  }

  getSelection1(isPage2?: boolean): any[] {
    let tmp1: any[] = [];
    let tmp2: any[] = [];
    let tmp3: any[] = [];
    if (!isPage2) {
      for (let index = 0; index < this.dimQuesList.length; index++) {
        const element = this.dimQuesList[index];
        let val = element.type;
        tmp1.push({
          label: element.name.zh_CN,
          value: val,
          checked: false,
          isShow: true,
        });
        this.getSelection2(element.child, tmp2, tmp3, val);
      }
    } else {
      for (let index = 0; index < this.dimQuesList2.length; index++) {
        const element = this.dimQuesList2[index];
        let val = element.type;
        tmp1.push({
          label: element.name.zh_CN,
          value: val,
          checked: false,
          isShow: true,
        });
        this.getSelection2(element.child, tmp2, tmp3, val);
      }
    }
    return [tmp1, tmp2, tmp3];
  }

  getSelection2(
    dataList: any[],
    itemList: any[],
    subItemList: any[],
    parentId: string
  ) {
    for (let index = 0; index < dataList.length; index++) {
      const element = dataList[index];
      // let val = element.type + '-' + element.dimensionCode;
      let val = element.type + this.twoDash + element.dimensionCode;
      itemList.push({
        label: element.name.zh_CN,
        value: val,
        checked: false,
        isShow: false,
        pId: parentId,
      });
      this.getSelection3(
        element.child,
        subItemList,
        val,
        element.dimensionCode
      );
    }
  }

  getSelection3(
    dataList: any[],
    itemList: any[],
    parentId: string,
    dimCode: string
  ) {
    for (let index = 0; index < dataList.length; index++) {
      const element = dataList[index];
      // let val = element.type + '-' + element.questionId;
      let val =
        element.type +
        this.twoDash +
        dimCode +
        this.twoDash +
        element.questionId;
      itemList.push({
        label: element.name.zh_CN,
        value: val,
        checked: false,
        isShow: false,
        pId: parentId,
      });
    }
  }

  updateAllChecked(tabData, e) {
    tabData.indeterminate = false;
    tabData.items.forEach((item) => {
      if (item.isShow) {
        item.checked = e;
      }
    });

    this.changeCheckModel(tabData);
  }

  updateSingleChecked(tabData, itemData, e) {
    let arr = [];
    tabData.items.map((item) => {
      arr.push(item);
    });
    if (arr.every((item) => !item.checked)) {
      tabData.allChecked = false;
      tabData.indeterminate = false;
    } else if (arr.every((item) => item.checked)) {
      tabData.allChecked = true;
      tabData.indeterminate = false;
    } else {
      tabData.indeterminate = true;
    }

    this.changeCheckModel(tabData);
  }

  changeCheckModel(tabData) {
    let type = tabData.type;
    if (type === 3) {
      return;
    }

    let checkArr: string[] = [];
    let items: any[] = tabData.items;
    for (let index = 0; index < items.length; index++) {
      const element = items[index];
      if (element.checked && element.isShow) {
        checkArr.push(element.value);
      }
    }
    // 下一级是否显示
    let children: any[];
    if (this.page === 1) {
      children = this.catMap[type + 1].items;
    } else {
      children = this.catOtherMap[type + 1].items;
    }
    if (children && children.length > 0) {
      for (let j = 0; j < children.length; j++) {
        const ch = children[j];
        if (_.includes(checkArr, ch.pId)) {
          ch.isShow = true;
        } else {
          ch.isShow = false;
        }
      }
    }

    // 如果是大维度，检查问题是否显示
    if (type === 1) {
      this.checkQuestion();
    }
  }

  checkQuestion() {
    // 子维度选择
    let checkArr: string[] = [];
    let items: any[];
    if (this.page === 1) {
      items = this.catMap[2].items;
    } else {
      items = this.catOtherMap[2].items;
    }
    for (let index = 0; index < items.length; index++) {
      const element = items[index];
      if (element.checked && element.isShow) {
        checkArr.push(element.value);
      }
    }

    // 问题是否显示
    let children: any[];
    if (this.page === 1) {
      children = this.catMap[3].items;
    } else {
      children = this.catOtherMap[3].items;
    }
    if (children && children.length > 0) {
      for (let j = 0; j < children.length; j++) {
        const ch = children[j];
        if (_.includes(checkArr, ch.pId)) {
          ch.isShow = true;
        } else {
          ch.isShow = false;
        }
      }
    }
  }

  getSingleParam(str: string): any {
    let arr: string[] = _.split(str, this.twoDash);
    let tmp: any = {};
    if (arr.length > 0) {
      tmp.type = arr[0];
    }
    if (arr.length > 1) {
      tmp.dimensionCode = arr[1];
    }
    if (arr.length > 2) {
      tmp.questionId = arr[2];
    }
    return tmp;
  }
  /**
   *
   * @param isPage2 是否为下一页操作
   * @returns
   */

  makeGroup(isPage2?: boolean) {
    if (!this.name.zh_CN) {
      // this.msgServ.error("名称不能为空。");
      this.customMsg.open("error", "名称不能为空。");
      return;
    }

    let tmpList: any[] = [];
    let tmpList1: any[] = [];

    for (let index = 0; index < this.catList.length; index++) {
      let cat: any = this.catList[index];
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const it = items[j];
        if (it.isShow && it.checked) {
          tmpList.push(this.getSingleParam(it.value));
        }
      }
    }
    for (let index = 0; index < this.catOtherList.length; index++) {
      let cat: any = this.catOtherList[index];
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const it = items[j];
        if (it.isShow && it.checked) {
          if (!isPage2) tmpList1.push(this.getSingleParam(it.value));
        }
      }
    }

    if (tmpList1.length == 0 && !isPage2)
      // return this.msgServ.warning("请选择维度");
      return this.customMsg.open("warning", "请选择维度");
    let param: any = {
      prismaReportDataId: this.prismaReportDataId,
      name: this.name,
      detailList: tmpList,
    };
    if (!isPage2) {
      param.analysisDetailList = tmpList1;
      let sub = null;
      if (this.updateId) {
        param.id = this.updateId;
        sub = this.bgUpdate(param);
      } else {
        sub = this.bgCreate(param);
      }
      sub.subscribe((res: any) => {
        if (res.result.code === 0) {
          this.msgServ.success("保存成功");
          this.groupData = null;
          this.clearOption(true);
          this.bgGetGroupList();
        }
      });
    } else {
      return param;
    }
  }

  delete(e, id) {
    e.stopPropagation();
    this.bgDelete(id).subscribe((res) => {
      if (res.result.code === 0) {
        if (id === this.updateId) {
          this.updateId = undefined;
        }
        this.msgServ.success("删除成功");
        this.bgGetGroupList();
      }
    });
  }

  edit(e, groupData: any) {
    e.stopPropagation();

    this.clearOption(true);
    this.groupData = groupData;
    let arr1: string[] = [];
    let arr2: string[] = [];
    let arr3: string[] = [];

    let selList = groupData.selectedList;
    for (let index = 0; index < selList.length; index++) {
      const selItem = selList[index];
      let type: string = selItem.type;
      let dimensionCode: string = selItem.dimensionCode;
      let questionId: string = selItem.questionId;
      let val: string = "";
      if (questionId && dimensionCode && type) {
        val = type + this.twoDash + dimensionCode + this.twoDash + questionId;
        arr3.push(val);
      } else if (dimensionCode && type) {
        val = type + this.twoDash + dimensionCode;
        arr2.push(val);
      } else {
        val = type;
        arr1.push(val);
      }
    }
    this.restoreCheckState(1, arr1, []);
    this.restoreCheckState(2, arr2, arr1);
    this.restoreCheckState(3, arr3, arr2);
    this.showGroup();

    this.name.zh_CN = groupData.name.zh_CN;
    this.name.en_US = groupData.name.en_US;
    this.updateId = groupData.id;
  }
  edit1(groupData: any) {
    let arr4: string[] = [];
    let arr5: string[] = [];
    let arr6: string[] = [];

    console.log(groupData.analysisSelectedList);
    let selList2 = groupData.analysisSelectedList;
    for (let index = 0; index < selList2.length; index++) {
      const selItem = selList2[index];
      let type: string = selItem.type;
      let dimensionCode: string = selItem.dimensionCode;
      let questionId: string = selItem.questionId;
      let val: string = "";
      if (questionId && dimensionCode && type) {
        val = type + this.twoDash + dimensionCode + this.twoDash + questionId;
        arr6.push(val);
      } else if (dimensionCode && type) {
        val = type + this.twoDash + dimensionCode;
        arr5.push(val);
      } else {
        val = type;
        arr4.push(val);
      }
    }

    this.restoreCheckState2(1, arr4, []);
    this.restoreCheckState2(2, arr5, arr4);
    this.restoreCheckState2(3, arr6, arr5);
  }

  restoreCheckState(
    type: number,
    checkedValues: string[],
    checkParentValues: string[]
  ) {
    let items: any[];
    items = this.catMap[type].items;
    if (items && items.length > 0) {
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        // set show state
        if (type === 1 || _.includes(checkParentValues, item.pId)) {
          item.isShow = true;
        } else {
          item.isShow = false;
        }
        // set check state
        item.checked = _.includes(checkedValues, item.value);
      }
    }
  }
  restoreCheckState2(
    type: number,
    checkedValues: string[],
    checkParentValues: string[]
  ) {
    let items: any[];
    items = this.catOtherMap[type].items;
    if (items && items.length > 0) {
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        // set show state
        if (type === 1 || _.includes(checkParentValues, item.pId)) {
          item.isShow = true;
        } else {
          item.isShow = false;
        }
        // set check state
        item.checked = _.includes(checkedValues, item.value);
      }
    }
  }

  clearOption(clearName?: boolean) {
    if (clearName) {
      this.name.zh_CN = "";
      this.name.en_US = "";
    }
    this.updateId = undefined;
    this.page = 1;
    // clear select
    for (let index = 0; index < this.catList.length; index++) {
      const cat = this.catList[index];
      cat.searchText = "";
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        item.checked = false;
        item.isShow = cat.type === 1;
      }
      cat.allChecked = false;
      cat.indeterminate = false;
    }
    for (let index = 0; index < this.catOtherList.length; index++) {
      const cat = this.catOtherList[index];
      cat.searchText = "";
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        item.checked = false;
        item.isShow = cat.type === 1;
      }
      cat.allChecked = false;
      cat.indeterminate = false;
    }
  }

  showGroup() {
    this.groupHidden = !this.groupHidden;
  }

  clearGroup() {
    this.bgEmptyGroup().subscribe((res: any) => {
      if (res.result.code === 0) {
        this.msgServ.success("清空所有组合成功");
        this.updateId = undefined;
        this.bgGetGroupList();
      }
    });
  }

  bgGetGroupList() {
    let api = `${this.tenantUrl}/survey/prisma/correlation/coefficient/combination/listByPrismaReportDataId/${this.prismaReportDataId}`;
    this.http.get(api).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.groupList = res.data;
        for (let index = 0; index < this.groupList.length; index++) {
          const element = this.groupList[index];
          element.active = true;
        }
      }
    });
  }

  bgGetDimList() {
    let api = `${this.tenantUrl}/survey/prisma/correlation/coefficient/combination/listQuestionDimensionByPrismaReportDataId/${this.prismaReportDataId}`;
    this.http.get(api).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.dimQuesList = res.data;
        this.intData();
      }
    });
  }

  bgGetPage2DimList() {
    let params = this.makeGroup(true);

    let api = `${this.tenantUrl}/survey/prisma/correlation/coefficient/combination/listQuestionDimensionList`;
    this.http.post(api, params).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.dimQuesList2 = res.data;
        this.intData1();
        if (this.groupData) {
          this.edit1(this.groupData);
        }
      }
    });
  }

  bgCreate(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/correlation/coefficient/combination/create`;
    return this.http.post(api, param);
  }

  bgUpdate(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/correlation/coefficient/combination/update`;
    return this.http.post(api, param);
  }

  bgDelete(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/correlation/coefficient/combination/delete/${id}`;
    return this.http.post(api, {});
  }

  bgEmptyGroup(): Observable<any> {
    let api = `${this.tenantUrl}/survey/prisma/correlation/coefficient/combination/deleteAll/${this.prismaReportDataId}`;
    return this.http.post(api, {});
  }
}
