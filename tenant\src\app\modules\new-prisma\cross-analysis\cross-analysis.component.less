@primaryColor: #40a9ff;
@minorColor: #f5f8ff;
@fgColor: #fff;
@pageBg: #e6e6e6;
@textColor_1: #262626;
@textColor_2: #262626;
@textColor_3: #ececec;
@textColor_4: #bfbfbf;
@textColor_5: #c4c4c4;
@textColor_6: #efefef;
@itemBgColor: #f5f6fa;
@tagBgColor: #ececec;
@tagBgColor2: #f5faff;

:root {
  --ifm-scrollbar-size: 7px;
  --ifm-scrollbar-track-background-color: #f1f1f1;
  --ifm-scrollbar-thumb-background-color: silver;
  --ifm-scrollbar-thumb-hover-background-color: #a7a7a7;
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
// 滑块背景
::-webkit-scrollbar-track {
  // background-color: transparent;
  background-color: #f1f1f1;
  box-shadow: none;
}
// 滑块
::-webkit-scrollbar-thumb {
  // background-color: #e9e9e9;
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}
.box {
  font-family: PingFangSC, PingFang SC;
  height: calc(100% - 4px);
  .row {
    border-radius: 8px;
    border: 1px solid @textColor_3;
    > div {
      border-left: 1px solid @textColor_3;
      height: 100%;
      &:nth-child(1) {
        border-left: none;
      }
      .title {
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid @textColor_3;
        min-height: 60px;
        > div {
          display: flex;
          align-items: center;
        }
        &-text{
          font-weight: 600;
          font-size: 16px;
          line-height: 22px;
          color: @textColor_1;
          margin-right: 16px;
          white-space: nowrap;
        }
        &-tip{
          font-size: 12px;
          font-weight: 400;
          color: @textColor_4;
          line-height: 12px;
          margin-right: 16px;
        }
        a {
          font-size: 12px;
          white-space: nowrap;
          i {
            font-size: 12px;
            margin-right: 4px;
          }
        }
      }
      .scroll {
        &-inside {
          margin: 4px;
          padding: 11px;
          height: calc(100vh - 208px);
          overflow-y: auto;
          .left {
            &-title {
              padding: 11px 16px;
              background: @itemBgColor;
              font-size: 14px;
              font-weight: 400;
              color: @textColor_2;
              line-height: 20px;
              border-radius: 4px;
            }
            &-checkbox {
              margin: 16px 0;
              > div {
                margin: 4px 0;
              }
            }
          }
          .right {
            .pointer {
              cursor: pointer;
            }
            &-title {
              padding-bottom: 16px;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              div {
                width: 3px;
                height: 14px;
                background-color: @primaryColor;
                margin-right: 8px;
              }
              b {
                font-weight: 400;
                font-size: 14px;
                color: @textColor_1;
                line-height: 20px;
              }
            }
            &-card {
              border-radius: 8px;
              border: 1px solid @textColor_6;
              padding: 12px 16px 16px 16px;
              margin-bottom: 16px;
              &-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                position: relative;
                span {
                  font-weight: 400;
                  color: @textColor_1;
                }
                i {
                  color: @textColor_4;
                  font-size: 16px;
                  &:hover {
                    color: @primaryColor;
                  }
                  cursor: pointer;
                }
                .del {
                  position: absolute;
                  top: -24px;
                  right: -24px;
                }
              }
              &-tags {
                display: flex;
                justify-content: flex-start;
                flex-wrap: wrap;
                > span {
                  border-radius: 4px;
                  background-color: @tagBgColor;
                  padding: 4px 8px;
                  margin-right: 8px;
                  display: flex;
                  align-items: center;
                  font-size: 12px;
                  font-weight: 400;
                  color: @textColor_2;
                  line-height: 17px;
                  cursor: default;
                  // i{
                  //   margin-left: 4px;
                  //   cursor: pointer;
                  //   &:hover{
                  //     color: @primaryColor;
                  //   }
                  // }
                  // &:hover{
                  //   background-color: @minorColor;
                  // }
                }
              }
            }
          }
        }
      }
      .hasBtns {
        height: calc(100vh - 260px);
      }
      .btns {
        height: 52px;
        padding: 0 16px;
        border-top: 1px solid @textColor_3;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        // span{
        //   font-size: 14px;
        //   font-weight: 500;
        //   color: @primaryColor;
        //   line-height: 20px;
        //   border: 1px solid @primaryColor;
        //   padding: 5px 22px;
        //   border-radius: 16px;
        //   margin-right: 9px;
        //   cursor: pointer;
        //   &:hover{
        //     background: linear-gradient(270deg, #74CDFF 0%, #409EFF 100%);
        //     padding: 6px 22px;
        //     border: none;
        //     color: @fgColor;
        //   }
        // }
      }
    }
  }
  .ml-4{
    margin-left: 4px;
  }
}
.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
/* 覆盖拖拽元素的样式 */
.gu-mirror {
  background: rgba(64, 158, 255, 0.07);
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
  border: 1px solid #b7daff;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 400;
  color: #17314c;
  line-height: 20px;
  padding: 12px 16px 16px 16px;
  cursor: move;
  .right-card {
    &-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      position: relative;
      span {
        font-weight: 400;
        color: @textColor_1;
      }
      i {
        color: @textColor_4;
        font-size: 16px;
        &:hover {
          color: @primaryColor;
        }
        cursor: pointer;
      }
      .del {
        position: absolute;
        top: -24px;
        right: -24px;
      }
    }
    &-tags {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      > span {
        border-radius: 4px;
        background-color: @tagBgColor;
        padding: 4px 8px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
        color: @textColor_2;
        line-height: 17px;
        cursor: default;
        // i{
        //   margin-left: 4px;
        //   cursor: pointer;
        //   &:hover{
        //     color: @primaryColor;
        //   }
        // }
        // &:hover{
        //   background-color: @minorColor;
        // }
      }
    }
  }
}