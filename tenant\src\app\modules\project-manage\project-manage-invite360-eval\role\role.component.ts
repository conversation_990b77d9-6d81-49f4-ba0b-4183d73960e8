import { Component, Input, OnInit, ChangeDetectorRef } from '@angular/core';
import { ProjectManageService } from '../../../service/project-manage.service';
import { ComponentService } from '../../../../shared/component.service';
import _ from 'lodash';

@Component({
  selector: 'app-role-eval',
  templateUrl: './role.component.html',
  styleUrls: ['./role.component.less']
})
export class RoleEvalComponent implements OnInit {

  constructor(
    private api: ProjectManageService,
    private common:ComponentService,
    private cdr: ChangeDetectorRef
  ) { }
  @Input() projectId;
  @Input() standardReportType
  @Input() sonrolelist
  @Input() roleskey
  @Input() isCustomRoleWeight
  @Input() showtype
  @Input() Projectstatus
  @Input() standardQuestionnaireId

  demoValue:number = 3;
  newRolelist:any[] =[];
  rolelist: any[] = [];
  standardType = false
  precision = 2
  
  ngOnInit() {

    this.getRoleList()
    if(this.standardReportType.indexOf("270") != -1){
      this.standardType = true
    }
    // this.standardReportType.indexOf("270") != -1
  }

  addRole(data?) {
    this.rolelist.push({
      name: data ? data.name : {zh_CN:'', en_US:''},
      investigatorTitle:  data ? data.investigatorTitle : {zh_CN:'', en_US:''},
      projectId: this.projectId,
      percentage:data?data.percentage:1
    })
  }

  deleteRole(index: number): void {
    this.rolelist.splice(index, 1);
  }

  getRoleList() {
    this.api.listRole(this.projectId).subscribe(res => {
      if (res.result.code === 0) {
  
        if(this.sonrolelist){
          this.sonrolelist.forEach(val => {
            res.data.forEach(item => {
              if(val.roleId == item.id){
                item.selet = true
                item.percentage = val.weights
              }
           })
         })
         this.rolelist = res.data.filter(item => {
           return item.selet 
         })
        }else{
          this.rolelist = res.data
        }
        
      }
    })
  }

  submit() {
    // this.api.createSurveyRole({ projectId: this.projectId, surveyRoles: this.rolelist }).subscribe(res => {
    //   if (res.result.code === 0) {
    //     this.common.message('success');
    //   }
    // });
  }

}
