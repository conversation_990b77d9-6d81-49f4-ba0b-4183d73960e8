import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Router } from "@angular/router";
import { NzMessageService } from "ng-zorro-antd";
import { TouristService } from '../tourist.service';

@Component({
  selector: 'app-pta',
  templateUrl: './pta.component.html',
  styleUrls: ['./pta.component.less']
})
export class PtaComponent implements OnInit {

  tourist:boolean=true;
  isNeedLogin:boolean=false;
  token:any;
  _token:any;
  constructor(private http: HttpClient,private router: Router,private message: NzMessageService,private TouristService:TouristService) { }

  ngOnInit() {
    // this.token = JSON.parse(localStorage.getItem("token"));
    this._token = JSON.parse(localStorage.getItem("_token"));
    if (this._token) {
        this.tourist=false;
        this.isNeedLogin=true;
    }
  }
  downloadReport(){
    this.TouristService.downLoad('pta');
  }

}
