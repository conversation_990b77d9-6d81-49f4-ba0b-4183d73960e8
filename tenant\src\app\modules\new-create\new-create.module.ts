/*
 * @Author: <PERSON> sid<PERSON>@knx.com.cn
 * @Date: 2025-01-15 15:43:55
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2025-06-03 17:30:51
 * @FilePath: \tenant\src\app\modules\new-create\new-create.module.ts
 */
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@shared';
import { Routes, RouterModule } from '@angular/router';
import { NewCreateComponent } from './new-create.component';

import { EvalComponent } from './360-type/360eval.component';//360
import { OtherEvalComponent } from './other-type/othereval.component';//其他活动
import { AdvancedMoreSetting } from './adv_set/advsetting.component';
import { CheckboxGroupBtnsComponent } from './checkbox_btn/checkbox_btn.component'; // 多选组件
import { TipMenuComponent } from './tip-menu/tip-menu.component'; // Tip菜单
import { TipTypeComponent } from './tip-type/tip-type.component'; // Tip问卷类型
import { TipDimensionComponent } from './tip-dimension/tip-dimension.component'; // Tip问卷类型
import { TipDescComponent } from './tip-desc/tip-desc.component'; // Tip作答说明


import { ScaleExpansionComponent } from './scale-expansion/scale-expansion.component'; // 量表尺度扩展
import { TopicDistributionComponent } from './topic-distribution/topic-distribution.component'; // 题本分发


import { TopicDistribution360Component } from './topic-distribution-360/topic-distribution-360.component'; // 360 关联设置（题本分发）

const routes: Routes = [
  { path: '', component: NewCreateComponent},];

@NgModule({
  declarations: [
    NewCreateComponent,
    EvalComponent,
    OtherEvalComponent,
    AdvancedMoreSetting, 
    CheckboxGroupBtnsComponent,
    TipMenuComponent,
    TipTypeComponent,
    TipDimensionComponent,
    TipDescComponent,
    ScaleExpansionComponent,
    TopicDistributionComponent,
    TopicDistribution360Component
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes),
  ],
  entryComponents:[
    EvalComponent,
    OtherEvalComponent,
    AdvancedMoreSetting,
    CheckboxGroupBtnsComponent,
    TipDescComponent,
    ScaleExpansionComponent,
    TopicDistributionComponent,
    TopicDistribution360Component
  ],//组件声明
  exports: [RouterModule]
  
})
export class NewCreateModule { }