import { ThrowStmt } from "@angular/compiler";
import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import _ from "lodash";
import {
  NzFormatEmitEvent,
  NzMessageService,
  NzModalRef,
  NzTreeComponent,
  NzTreeNode,
} from "ng-zorro-antd";
import { forkJoin } from "rxjs";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-survey-create",
  templateUrl: "./survey-create.component.html",
  styleUrls: ["./survey-create.component.less"],
})
export class SurveyCreateComponent implements OnInit {
  @Input() projectId: string;

  orgList: any[] = [];

  demoList: any[] = [];

  normList: any[] = [];

  searchValue = "";

  selectedNodes: any[] = [];

  // 人口标签 tab model
  demoTabs = [];
  indexNum: number = 0;
  position = "left";

  lans: any[] = [
    { label: "中文", value: "zh_CN", checked: true },
    { label: "ENG", value: "en_US", checked: false },
  ];

  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;

  constructor(
    private projSerivce: ProjectManageService,
    private surveySerivce: SurveyApiService,
    private msg: NzMessageService,
    private ref: NzModalRef,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    this.loadData();
  }

  loadData() {
    forkJoin([
      this.surveySerivce.listOrganizationByTree(this.projectId),
      this.surveySerivce.listDemographicByProjectId(this.projectId),
      this.surveySerivce.listNormsByProjectId(this.projectId),
    ]).subscribe((res) => {
      this.loadOrgData(res[0]);
      this.loadDemoData(res[1]);
      this.loadNormData(res[2]);
      this.initTab();
    });
  }

  loadOrgData(res: any) {
    if (res.result.code === 0) {
      this.orgList = res.data;
    }
  }

  loadDemoData(res: any) {
    if (res.result.code === 0) {
      this.demoList = res.data;
    }
  }

  loadNormData(res: any) {
    if (res.result.code === 0) {
      this.normList = res.data;
    }
  }

  initTab() {
    for (let index = 0; index < this.demoList.length; index++) {
      const element = this.demoList[index];
      let items: any[] = element.children;
      let tmp: any[] = [];
      items.forEach((item) => {
        tmp.push({ label: item.name.zh_CN, value: item.id, checked: false });
      });
      this.demoTabs.push({
        id: element.id,
        name: element.name.zh_CN,
        allChecked: false,
        indeterminate: false,
        items: tmp,
        isNorm: false,
        checkedValues: [],
      });
    }

    // 常模
    let tmp: any[] = [];
    this.normList.forEach((item) => {
      tmp.push({ label: item.name.zh_CN, value: item.id, checked: false });
    });
    this.demoTabs.push({
      id: "norm",
      name: "组织常模",
      allChecked: false,
      indeterminate: false,
      items: tmp,
      isNorm: true,
      checkedValues: [],
    });
  }

  getShowList(): any[] {
    let typeList: any[] = [];
    typeList.push({
      id: "org",
      name: "组织架构",
      itemList: this.selectedNodes,
    });

    for (let index = 0; index < this.demoTabs.length; index++) {
      let itemList: any[] = [];
      const element = this.demoTabs[index];
      element.items.forEach((item) => {
        if (item.checked) {
          itemList.push({ id: item.value, name: item.label });
        }
      });
      typeList.push({ id: element.id, name: element.name, itemList: itemList });
    }
    return typeList;
  }

  // get should select map
  selectChild(node: any, isSelected: boolean, map: any) {
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        map[child.key] = 1;
        this.selectChild(child, isSelected, map);
      }
    }
  }

  // set select node by map
  setSelectState(node: NzTreeNode, isSelected: boolean, map: any) {
    if (map[node.key] === 1) {
      node.isDisabled = isSelected;
      // node.setSelected(isSelected);
      node.isSelected = false;
    }
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        this.setSelectState(child, isSelected, map);
      }
    }
  }

  nzEvent(event: NzFormatEmitEvent): void {
    if (event.node.isDisabled) {
      return;
    }
    // select children
    let currentNode: any = event.node.origin;
    let isSelected: boolean = currentNode.selected;
    let map = {};
    this.selectChild(currentNode, isSelected, map);

    let allNodes: NzTreeNode[] = this.nzTreeComponent.getTreeNodes();
    allNodes.forEach((n: NzTreeNode) => {
      this.setSelectState(n, isSelected, map);
    });

    // update displaying selected nodes
    let nodeList: NzTreeNode[] = this.nzTreeComponent.getSelectedNodeList();
    let tmp: any[] = [];
    for (let index = 0; index < nodeList.length; index++) {
      const element = nodeList[index].origin;
      tmp.push({ id: element.key, name: element.title });
    }
    this.selectedNodes = tmp;
  }

  updateAllChecked(tabData, e) {
    if (e && tabData.isNorm && tabData.items.length > 3) {
      // this.msg.warning("常模选择不能超过3个。");
      this.customMsg.open("warning", "常模选择不能超过3个");
      setTimeout(function() {
        tabData.allChecked = false;
      }, 1000);
      return;
    }

    tabData.indeterminate = false;
    tabData.items.forEach((item) => {
      item.checked = e;
    });
    this.recordChecks(tabData);
  }

  updateSingleChecked(tabData, e) {
    if (this.isCheckDisabled(tabData)) {
      // this.msg.warning("常模选择不能超过3个。");
      this.customMsg.open("warning", "常模选择不能超过3个");
      // revert selection
      setTimeout(function() {
        tabData.items.forEach((item) => {
          item.checked = _.includes(tabData.checkedValues, item.value);
        });
      }, 1000);
      return;
    }

    if (tabData.items.every((item) => !item.checked)) {
      tabData.allChecked = false;
      tabData.indeterminate = false;
    } else if (tabData.items.every((item) => item.checked)) {
      tabData.allChecked = true;
      tabData.indeterminate = false;
    } else {
      tabData.indeterminate = true;
    }
    this.recordChecks(tabData);
  }

  recordChecks(tabData) {
    let tmp: any[] = [];
    tabData.items.forEach((item) => {
      if (item.checked) {
        tmp.push(item.value);
      }
    });
    tabData.checkedValues = tmp;
  }

  isCheckDisabled(tabData): boolean {
    let checkCount: number = 0;
    tabData.items.forEach((item) => {
      if (item.checked) {
        checkCount++;
      }
    });
    return tabData.isNorm && checkCount > 3;
  }

  getParamData() {
    let param = {};
    let showList: any[] = this.getShowList();
    showList = _.filter(showList, function(o) {
      return o.itemList.length > 0;
    });
    showList.forEach((item) => {
      let children = _.map(item.itemList, (o) => o.id);
      param[item.id] = children;
    });
    return param;
  }

  ok() {
    let tmpLans: string[] = _.map(
      _.filter(this.lans, function(l) {
        return l.checked;
      }),
      (o) => o.value
    );
    if (tmpLans.length === 0) {
      // this.msg.error("没有选择报告语言");
      this.customMsg.open("error", "没有选择报告语言");
      return;
    }

    let tmp: any = this.getParamData();
    let factorList: any[] = [];
    for (let i in tmp) {
      if (i !== "org" && i !== "norm") {
        factorList.push({ demographicRootId: i, demographicIds: tmp[i] });
      }
    }

    // 常模不能为空
    if (!tmp.norm) {
      // this.msg.error("没有选择常模");
      this.customMsg.open("error", "没有选择常模");
      return;
    }

    let param = {
      projectId: this.projectId,
      demographicContents: factorList,
      normIds: tmp.norm,
      organizationIds: tmp.org,
      reportLanguages: tmpLans,
    };

    this.surveySerivce.createPrismaReport(param).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("创建报告成功。");
        this.ref.triggerOk();
      } else {
        // this.msg.warning(res.result.message)
      }
    });
  }

  getTotal() {
    let arr: any[] = _.filter(this.demoTabs, function(o) {
      return o.id === "norm";
    });
    let norm: any = arr[0];
    let selItems: any[] = _.filter(norm.items, (o) => o.checked);
    let normIds: string[] = _.map(selItems, "value");

    let total: number = 0;
    for (let index = 0; index < this.normList.length; index++) {
      let element = this.normList[index];
      if (
        _.includes(normIds, element.id) &&
        !element.isFree &&
        !element.isBuy
      ) {
        let curPrice: number = 0;
        if (element.amount) {
          curPrice = element.amount;
        }
        total = total + curPrice;
      }
    }
    return total;
  }
}
