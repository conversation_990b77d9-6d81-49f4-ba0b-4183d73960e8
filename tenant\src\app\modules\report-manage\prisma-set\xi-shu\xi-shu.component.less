.con {
    position: relative;

    .name {
        height: 70px;

        .input {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;

            input {
                width: 48%;
            }
        }

    }

    .title {
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .label {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #17314C;
        line-height: 20px;
    }

    .content {
        width: 100%;
        height: 450px;
        display: flex;
        justify-content: flex-start;

        border-radius: 4px;
        border: 1px solid #E6E6E6;

        .list {
            width: 255px;
            min-height: 400px;
            border-right: 1px solid #E6E6E6;

            .listItem {
                height: 380px;
            }
        }
    }

    .action {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        border-bottom: 1px solid #E6E6E6;

        .button1 {
            border-radius: 15px;
            background-color: white;

            span {
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #409EFF;
                line-height: 20px;
            }
        }

        .button2 {
            background: linear-gradient(90deg, #A1A9FF 0%, #BD97FF 100%);
            box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
            border-radius: 15px;
            border: none;

            span {
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: white;
                line-height: 20px;
            }
        }
        .button3 {
            background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 15px;
            border: none;

            span {
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: white;
                line-height: 20px;
            }
        }

    }


    .group {
        width: 700px;
        height: 610px;
        background:#E8E8E8;
        box-shadow: 0px 0px 10px 4px rgba(0, 0, 0, 0.04);
        padding: 20px;
        position: absolute;
        top: 0;
        right: 0;
        z-index: 100;

        .gHeader {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;

            .gTitle {
                height: 25px;
                font-size: 18px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 600;
                color: #17314C;
                line-height: 25px;
            }
        }

        .gContent {
            height: 495px;
        }

        .gAction {
            margin-top: 10px;
            text-align: center;
        }

    }

}

.treeScroll {
    overflow-y: auto;
    overflow-x: auto;
}

.takeRemain {
    flex: 1;
}

.background-color {
    background-color: rgba(142, 255, 142, .3);
}

:host .con ::ng-deep {

    nz-tag {
        margin-top: 10px;
    }

    .ant-collapse-content-box {
        padding-top: 5px;
    }

    .ant-collapse {
        min-width: 290px;
    }

    .ant-checkbox-wrapper {
        display: block;
        margin-left: 0;
    }

    .ant-collapse-extra {
        &:hover {
            color: sandybrown;
        }
    }
}

::ng-deep {
    .ant-modal-close-x {
        width: 36px !important;
        height: 36px !important;
    }
}