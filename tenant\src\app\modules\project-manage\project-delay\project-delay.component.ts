import { Component, OnInit, Input } from '@angular/core';
import { NzModalRef } from 'ng-zorro-antd';
// import * as differenceInCalendarDays from 'date-fns/differenceInCalendarDays';

@Component({
  selector: 'app-project-delay',
  templateUrl: './project-delay.component.html',
  styleUrls: ['./project-delay.component.less']
})
export class ProjectDelayComponent implements OnInit {

  @Input() public id : string;
  @Input() public startDate : string;
  @Input() public endDate : string;

  constructor(private modalRef:NzModalRef) {}

  ngOnInit(): void {
  }

  // disabledEndDate = (current: Date): boolean => {
  //   // Can not select days before today and today
  //   return differenceInCalendarDays(current, new Date(this.startDate) ) < 0;
  // };

  disabledEndDate = (endValue: Date): boolean => {
    return endValue.getTime() <= new Date(this.startDate).getTime();
  };

  triggerOk() {

    this.modalRef.triggerOk();
  }

  triggerCancel() {
  
    this.modalRef.triggerCancel();
  }

  onChange(e){
    if(e && e.getMinutes() !== 0 && e.getMinutes() !== 30){
      e.setMinutes(30);
    }
  }

}
