/**
 *
 *  @author: <PERSON>
 *  @Date: 2023/09/18
 *  @content: 带icon的btn，支持nz7.5.x版本以及iconfont
 *
 */
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";

@Component({
  selector: "app-btn-iconfont",
  templateUrl: "./btn-iconfont.component.html",
  styleUrls: ["./btn-iconfont.component.less"],
})
export class BtnIconfontComponent implements OnInit {
  /*
   *@type: 类型，使用default或iconfont
   *@text：文本
   *@iconFont：iconfont
   *@iconType：nzType
   *@color:颜色，默认#D0D0D0
   *@hoverColor：悬浮颜色，默认#1890ff
   *@filled：是否实心，默认false
   *
   */
  @Input() type: "iconfont" | "default"; // 类型：iconfont 或者nz
  @Input() text: string;
  @Input() iconFont: string;
  @Input() iconType: string;
  @Input() color: string = "#D0D0D0";
  @Input() hoverColor: string = "#1890ff";
  @Input() theme: string;
  @Input() filled: boolean = false;
  isHover: boolean = false;
  @Output() btnclick = new EventEmitter<any>();

  constructor() {}

  ngOnInit() {}

  clickLink() {
    this.btnclick.emit("");
  }

  enter() {
    this.isHover = true;
  }

  leave() {
    this.isHover = false;
  }
}
