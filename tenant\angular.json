{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ng-alain": {"projectType": "application", "root": "", "sourceRoot": "src", "prefix": "app", "schematics": {"@schematics/angular:component": {"styleext": "less"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets", "src/favicon.ico", "src/assets", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}, {"glob": "**/*", "input": "./node_modules/@knx/knx-ngx/assets/", "output": "/assets/"}], "styles": ["node_modules/ng-zorro-antd/src/ng-zorro-antd.min.css", "src/styles.less", "src/assets/iconfonts/iconfont.css"], "scripts": ["node_modules/@antv/g2/build/g2.js", "node_modules/@antv/data-set/dist/data-set.min.js", "node_modules/@antv/g2-plugin-slider/dist/g2-plugin-slider.min.js", "node_modules/ajv/dist/ajv.bundle.js", "node_modules/qrious/dist/qrious.min.js", "node_modules/echarts/dist/echarts.js", "src/assets/libs/jquery-1.11.0.min.js", "src/assets/libs/super_slider.js", "src/assets/iconfonts/iconfont.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "50kb"}]}, "stage": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "qa": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "test": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "hmr": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.hmr.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "ng-alain:build"}, "configurations": {"production": {"browserTarget": "ng-alain:build:production"}, "hmr": {"browserTarget": "ng-alain:build:hmr", "hmr": true}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ng-alain:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "karmaConfig": "karma.conf.js", "tsConfig": "tsconfig.spec.json", "scripts": ["node_modules/@antv/g2/dist/g2.min.js", "node_modules/@antv/data-set/dist/data-set.min.js", "node_modules/@antv/g2-plugin-slider/dist/g2-plugin-slider.min.js", "node_modules/ajv/dist/ajv.bundle.js", "node_modules/qrious/dist/qrious.min.js"], "styles": [], "assets": ["src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "ng-alain:serve"}, "configurations": {"production": {"devServerTarget": "ng-alain:serve:production"}}}}}}, "defaultProject": "ng-alain", "cli": {"analytics": "d31116e3-c207-4d42-8292-81b9fb78d9b4"}}