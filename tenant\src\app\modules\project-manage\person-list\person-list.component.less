.container {
    // background-color: cornflowerblue;
    // margin-top: 20px;
    width: 100%;
    height: auto;
    .icon-icon- {
      margin-left: 20px;
      cursor: pointer;
    }
    .icon-icon-:hover {
      color: orangered;
    }
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .text {
            height: 33px;
            font-size: 24px;
            font-family: PingFangSC-Light, PingFang SC;
            font-weight: 300;
            color: #17314C;
            line-height: 33px;
            white-space: nowrap;
            overflow: hidden;
        }
        
    }

    .tree {
        // width: 100%;
        width: 465px;
        max-height: 450px;
    }

    .body {

        min-height: 60px;
        margin-bottom: 5px;
        position: relative;

        .input {
            position: absolute;
            top: 11px;
            right: 10px;

            .search {
                width: 166px;
                height: 30px;
                background: rgba(255, 255, 255, 1);
                border-radius: 15px;
                .ant-input {
                    border-radius: 15px;
                    border: none;
                    // border: solid 1px gainsboro;
                }
            }
        }

        .tit {
            padding-top: 15px;
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 600;
            color: #17314C;
            margin-bottom: 15px;
            
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}

.maxW {
    max-width: 350px;
}

.nowrap {
    white-space: nowrap;
}

button {
    padding: 0;
}

.inprogress {
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #45BFD9;
    line-height: 20px;
}

td {
    padding: 10px 16px!important;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #17314C;
}

tr {
    background-color: #fff;
}

.bg {
    background: #F5F6FA;
    // background: #fff;
}

.orgSearch {
    border-bottom: 1px solid #E6E6E6;
}

::ng-deep .ant-popover-buttons {
    text-align: center!important;
}

:host ::ng-deep {

    .ant-input {
        border-radius: 15px;
        border: none;
    }

    .ant-tree-title {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #17314C;
    }

}

  
// 滚动条
.treeScroll {
overflow-y:auto;
overflow-x:auto;
white-space:nowrap;
}
