import { Component, Input, OnInit } from '@angular/core';
import { ProjectManageService } from '../../../service/project-manage.service';
import { TransferItem, TransferChange } from 'ng-zorro-antd/transfer';
import { UploadXHRArgs,NzMessageService } from 'ng-zorro-antd';
import { HttpClient, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import _ from 'lodash';

@Component({
  selector: 'app-choose-invitor',
  templateUrl: './choose-invitor.component.html',
  styleUrls: ['./choose-invitor.component.less']
})
export class ChooseInvitorComponent implements OnInit { // 指定邀请人

  @Input() list: TransferItem[] = [];
  @Input() projectId;
  @Input() type
  @Input() currentpage
  @Input() totalpage
  @Input() totalnumber
  @Input() listtype
  @Input() showcCode
  @Input() tabsetIndex
  @Input() multipleValue

  @Input() isFetchData : boolean;

  selectkeys = []

  isLoading = false;
  tenantUrl: string = "/tenant-api";

  tbData1: any[] = [];
  tbData2: any[] = [];
  text1 : string = "";
  text2 : string = "";
  allFlag1: boolean = true;
  allFlag2: boolean = true;
  buttonload = false
  constructor(private api: ProjectManageService,private http:HttpClient,private msg:NzMessageService) { }

  ngOnInit() {
  
    if(this.isFetchData) {
      this.initData();
    } else {
      this.listMap();
    }
  }

  initData() {
    
    if (this.listtype == 'message') {
      this.isLoading = true;
      this.api.listNotInvitedprisma([this.projectId, ...this.multipleValue], this.type).subscribe(res => {
        this.isLoading = false;
        if (res.result.code === 0) {
          this.list = res.data
          this.listMap()
        }
      })
    }

    if (this.listtype == 'code') {
      this.isLoading = true;
      this.api.listInvestigatorGroup(this.projectId).subscribe(res => {
        this.isLoading = false;
        if (res.result.code === 0) {
          this.list = res.data
          this.listMap()
        }
      })
    }
    if(this.listtype == 'orgcode'){
      this.listMap()
    }

  }

  listMap() {
    this.list = this.list.map(item => {
      let name: string = item.name && item.name.zh_CN ? item.name.zh_CN : item.name;
      return {
        key: item.id,
        title:
          (name ? name : item.firstName ? item.firstName : "") +
          `( ${item.code} )`,
        description: name ? name : item.firstName,
        direction: "left",
        email: item.email,
        phone: item.phone,
        firstName: name ? name : item.firstName, //人员名称
        captcha: item.captcha, //验证码
        wxWorkUserId: item.wxWorkUserId, //企业微信
        dingAccount: item.dingAccount, // 钉钉用户账号
        feishuAccount: item.feishuAccount,//飞书
      };
    })
    this.tbData1 = this.list;
    console.log(this.tbData1);
    
  }

  search(type : number) {
    let content = type === 1 ? this.text1 : this.text2;
    let str = type === 1 ? 'left' : 'right';
    let ll = _.filter(this.list, function(o) { return o.direction === str && o.title.indexOf(content) !== -1 });
    if(type === 1) {
      this.tbData1 = ll;
    } else {
      this.tbData2 = ll;
    }
    
  }

  selectAll(type : number) {
    let str = ( type === 1 ? 'left' : 'right' );
    let checkFlag : boolean = ( type === 1 ? this.allFlag1 : this.allFlag2 );
    let content : string = ( type === 1 ? this.text1 : this.text2);
    
    let ll = _.filter(this.list, function(o) {
      return o.direction === str && (!content || o.title.indexOf(content) !== -1); 
    });
    for (let index = 0; index < ll.length; index++) {
      const ele = ll[index];
      ele.checked = checkFlag;
    }

    setTimeout(() => {
      if(type === 1) {
        this.allFlag1 = !this.allFlag1;
      } else {
        this.allFlag2 = !this.allFlag2;
      }  
    }, 200);

  }
  

  toLeft() {
    let ll = _.filter(this.list, function(o) { return o.direction === "right" && o.checked; });
    for (let index = 0; index < ll.length; index++) {
      const ele = ll[index];
      ele.direction = "left";
    }
    this.afterClick(false);
  }


  toRight() {
    let ll = _.filter(this.list, function(o) { return o.direction === "left" && o.checked; });
    for (let index = 0; index < ll.length; index++) {
      const ele = ll[index];
      ele.direction = "right";
    }
    this.afterClick(true);
  }

  afterClick(toRight: boolean) {
    let ll = _.filter(this.list, function(o) { return o.direction === "left"; });
    ll.forEach(element => {
      element.checked = false
    });
    
    this.tbData1 = [...ll];

    this.selectkeys = [];
    let rr = _.filter(this.list, function(o) { return o.direction === "right"; });
    rr.forEach(element => {
      element.checked = false
    });
    this.tbData2 = [...rr];
    this.selectkeys = [...rr];

    this.text1 = "";
    this.text2 = "";

    if(toRight) {
      this.allFlag1 = true;
    } else {
      this.allFlag2 = true;
    }

  }
  importTenantCongif = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append('excel', item.file as any);
    this.importTenantCongifReq(formData, item);
  };

  /**
* importTenantCongifReq 
*/
  importTenantCongifReq(formData, item) {
    console.log(this.tabsetIndex)
    let type = null
    if(this.tabsetIndex == 1){
      if (this.showcCode === 'mail' || this.showcCode == 1) {
        type = 'MAIL'
      }  
      if (this.showcCode === 'message' || this.showcCode == 2) {
        type = 'SMS'
      }
      if (this.showcCode === 'vchart' || this.showcCode == 3) {
        type = 'THIRD_PARTY_ENTERPRISE_WECHAT'
      }
    }
    
    

    let api: string = `${this.tenantUrl}/survey/person/filterWithFile?projectId=${this.projectId}&type=${type}`;
    let sub: Observable<any>;

    sub = this.http.post(api, formData);

    return sub.subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success('查询成功')
          this.list = res.data
          this.listMap()
        } else {
          if (res.result.code < 10000) {
            this.msg.success(res.result.message)
          }
        }
      },
      err => {
        item.onError!(err, item.file!);
      }
    )
  }

  downLoad(){
    this.buttonload = true
    this.api.simpleTemplate().subscribe(res => {

      const blob = new Blob([res.body], { type: 'application/vnd.ms-excel' });
      let fileName = res.headers.get('Content-Disposition').split(';')[1].split('filename=')[1];
      const fileNameUnicode = res.headers.get('Content-Disposition').split('filename*=')[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split('\'\'')[1]);
      }
      const link = document.createElement('a');
      link.setAttribute('href', URL.createObjectURL(blob));
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.buttonload = false
    })
  }

}
