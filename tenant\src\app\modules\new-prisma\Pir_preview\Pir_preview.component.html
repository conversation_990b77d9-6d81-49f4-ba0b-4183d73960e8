<div style="display: flex; justify-content: center;">
  <div style="display: flex;">
    <div class="tab">
      <nz-tabset
        [nzSize]="tabSize"
        [(nzSelectedIndex)]="lanIndexuser"
        (nzSelectChange)="changeLanuser()"
      >
        <nz-tab nzTitle="手机端"> </nz-tab>
        <nz-tab nzTitle="PC端"> </nz-tab>
      </nz-tabset>
    </div>
  </div>
</div>

<div
  style="background:#F5F6FA; height: 100%;"
  id="orgDiv"
  *ngIf="!lanIndexuser"
>
  <div class="content client-width">
    <div class="body">
      <div style="display: flex; align-items: center; ">
        <div class="tab">
          <nz-tabset
            [nzSize]="tabSize"
            [(nzSelectedIndex)]="lanIndex"
            (nzSelectChange)="changeLan()"
          >
            <nz-tab nzTitle="中文"> </nz-tab>
            <nz-tab nzTitle="ENG"> </nz-tab>
          </nz-tabset>
        </div>
      </div>
      <div style=" display: flex; justify-content: center; ">
        <div *ngIf="projectInfo" class="projName">
          {{ projectInfo?.name }}
        </div>
      </div>
      <div class=" topiclist">
        <div style="display: flex;justify-content: center;position: relative;">
          <img src="./assets/images/preview.png" alt="" style="width: 700px;" />
          <div
            class="scroll"
            style="position: absolute;top: 120px;width: 500px;height: 1000px;overflow-y: auto;"
          >
            <ng-container
              *ngFor="let item of topicList; let i = index; last as isLast"
            >
              <div
                *ngIf="item.type !== 'PAGE_SPLIT'"
                class="topic"
                [ngClass]="{ double: item.active, last_topic: isLast }"
                (mouseenter)="mouseState(item, true)"
                (mouseleave)="mouseState(item, false)"
              >
                <div class="ques">
                  <div class="quesName">
                    <span>{{ i + 1 }}.&nbsp; </span>
                    <span
                      *ngIf="item.replaceName"
                      [innerHTML]="item.replaceName[lan] | html"
                    ></span>
                  </div>
                </div>
                <div class="optionlist">
                  <ng-container
                    *ngFor="
                      let option of item.options.options;
                      let i = index;
                      let isLast = last
                    "
                  >
                    <div [ngClass]="{ marg: !isLast }">
                      <label nz-radio [(ngModel)]="checked">
                        {{ option.name[lan] }}
                      </label>
                    </div>
                  </ng-container>

                  <div
                    style="border:dashed 1px grey; width: 100%; padding: 5px 15px; text-align: center;"
                    *ngIf="item.type === 'ESSAY_QUESTION'"
                  >
                    {{ lan === "zh_CN" ? "开放题" : "Open question" }}
                  </div>
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div style="background:#F5F6FA; height: 100%;" id="orgDiv" *ngIf="lanIndexuser">
  <div class="content client-width">
    <div class="body">
      <div style="display: flex; align-items: center; ">
        <div class="tab">
          <nz-tabset
            [nzSize]="tabSize"
            [(nzSelectedIndex)]="lanIndex"
            (nzSelectChange)="changeLan()"
          >
            <nz-tab nzTitle="中文"> </nz-tab>
            <nz-tab nzTitle="ENG"> </nz-tab>
          </nz-tabset>
        </div>
      </div>
      <div style=" display: flex; justify-content: center; ">
        <div *ngIf="projectInfo" class="projName">
          {{ projectInfo?.name }}
        </div>
      </div>
      <div class="scroll topiclist">
        <ng-container
          *ngFor="let item of topicList; let i = index; last as isLast"
        >
          <div
            *ngIf="item.type !== 'PAGE_SPLIT'"
            class="topic"
            [ngClass]="{ double: item.active, last_topic: isLast }"
            (mouseenter)="mouseState(item, true)"
            (mouseleave)="mouseState(item, false)"
          >
            <div class="ques">
              <div class="quesName">
                <span>{{ i + 1 }}.&nbsp; </span>
                <span
                  *ngIf="item.replaceName"
                  [innerHTML]="item.replaceName[lan] | html"
                ></span>
              </div>
            </div>
            <div class="optionlist">
              <ng-container
                *ngFor="
                  let option of item.options.options;
                  let i = index;
                  let isLast = last
                "
              >
                <div [ngClass]="{ marg: !isLast }">
                  <label nz-radio [(ngModel)]="checked">
                    {{ option.name[lan] }}
                  </label>
                </div>
              </ng-container>

              <div
                style="border:dashed 1px grey; width: 100%; padding: 5px 15px; text-align: center;"
                *ngIf="item.type === 'ESSAY_QUESTION'"
              >
                {{ lan === "zh_CN" ? "开放题" : "Open question" }}
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</div>
