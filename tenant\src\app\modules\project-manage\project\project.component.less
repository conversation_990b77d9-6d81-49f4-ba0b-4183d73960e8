* {
  margin: 0;
  padding: 0;
}

.panel {
  width: 30px;
  height: 30px;
  position: relative;
}

.panel_img {
  position: absolute;
  right: 15px;
  top: -16px;
}
.drop_ul {
  padding: 5px !important;
  li {
    padding: 5px;
    text-align: center;
    // width: 76px;
  }
}
.modal_div {
  display: flex;
  justify-content: space-between;
}
.project-model {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  max-width: 180px;
}

.content_name {
  height: 20px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(23, 49, 76, 1);
  line-height: 20px;
}

.content_tool {
  margin-top: 10px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(170, 170, 170, 1);
  line-height: 20px;
  cursor: pointer;
}

.icon_green {
  width: 5px;
  height: 5px;
  background: rgba(96, 203, 127, 1);
  display: inline-block;
  margin-bottom: 3px;
  border-radius: 100%;
}

.icon_red {
  width: 5px;
  height: 5px;
  background: rgba(255, 117, 117, 1);
  display: inline-block;
  margin-bottom: 3px;
  border-radius: 100%;
}

.status {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
}

.status_finish {
  height: 17px;
  font-size: 12px;
  font-weight: 500;
  color: rgba(73, 89, 112, 1);
  line-height: 17px;
}

.status_not_finish {
  height: 17px;
  font-size: 12px;
  font-weight: 500;
  color: rgba(73, 89, 112, 1);
  line-height: 17px;
}
.update-btn {
  text-align: right;
  margin-top: 10px;
  & > a {
    font-size: 12px;
  }
  & > a:not(:last-child) {
    margin-right: 10px;
  }
}

.label_active {
  float: right;
  padding: 2px 5px;
  background: #419eff;
  border-radius: 4px;
  font-size: 12px;
  color: #ffffff;
}
.label_over {
  float: right;
  padding: 2px 5px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
  color: #aaaaaa;
}
.round-btn {
  padding: 2px 10px;
  border-radius: 30px;
  border: 1px solid #419eff;
  color: #419eff;
  background-color: #ffffff;
  margin-top: 10px;
  cursor: pointer;
}

.code_ul {
  .code_li {
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #eee;
    .code_div {
      display: flex;
      padding: 10px 0;
      .big_lable {
        // display: flex;
        width: 100px;
        font-size: 14px;
        font-weight: bold;
      }
      ::ng-deep .big_lable {
        .ant-checkbox-checked {
          // padding-top: 4px;
        }
      }
      .min_div {
        margin-left: 30px;
        .label_div {
          display: flex;
          flex-wrap: wrap;
          .col_div {
            width: 200px;
            padding: 0 0 10px 10px;
          }
        }
      }
    }
  }
}
.correct-footer {
  button {
    width: 128px;
    height: 38px;
    // background: #FAFAFA;
    border-radius: 19px;
  }
  a {
    margin-left: 10px;
  }
}
//滚动条
.vxscrollbar() {
  scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #f1f1f1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #c1c1c1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}
::ng-deep {
  .cdk-global-scrollblock {
    overflow: auto;
  }
  .round-right-drawer7 {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 108px);
      overflow-y: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer-report-config {
    .ant-drawer-body {
      padding: 0;
      height: calc(100% - 55px);
      overflow-y: auto;
      .vxscrollbar();
      .ant-tabs-ink-bar {
        height: 4px;
      }
      .ant-tabs-tab {
        padding: 16px 0;
        margin: 0 16px;
      }
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
