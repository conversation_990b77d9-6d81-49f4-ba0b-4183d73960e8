@bgColor: #40a9ff;
@fgColor: white;
@pageBg: #f5f6fa;
@textColor: #495970;

:host .org {
  background-color: @fgColor;
  height: 100%;

  .ellips {
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    white-space: nowrap;
    cursor: default;
  }

  .content {
    background-color: @fgColor;
    height: 100%;

    .header {
      display: flex;
      justify-content: space-between;
      padding: 20px 20px 0 0;
      margin-bottom: 16px;

      .title {
        font-size: 24px;
        font-weight: 100;
        color: #495970;
        line-height: 34px;
        display: flex;
        align-items: center;
        height: 34px;

        > span {
          margin-right: 30px;
        }

        .icon-search {
          color: #409eff;
        }
      }
    }

    .btns {
      font-size: 14px;
      line-height: 20px;
      border-radius: 2px;
      margin: 16px 0;
      display: flex;
      justify-content: space-between;
      > div {
        display: flex;
        align-items: center;
        button {
          margin-right: 15px;
          cursor: pointer;
          opacity: 1;
          transition: opacity 0.3s ease-in-out;
          i {
            margin-right: 7px;
            font-size: 20px;
          }

          &:hover {
            opacity: 0.9;
          }
        }

        .btn-primary {
          border-radius: 19px;
          padding: 7px 18px 7px 12px;
          color: @fgColor;
          background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
          box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          > i {
            font-size: 24px;
          }
        }

        .btn-ghost {
          border-radius: 19px;
          padding: 6px 17px 6px 11px;
          color: @bgColor;
          background-color: @fgColor;
          border: 1px solid @bgColor;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 500;
          font-size: 16px;
          > i {
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            font-size: 12px;
            margin-right: 9px;
            color: #fff;
            border-radius: 50%;
            background-color: #409eff;
          }
        }

        .btn-link {
          padding: 7px;
          color: @bgColor;
          background-color: @fgColor;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .btn-text {
          padding: 7px;
          color: #495970;
          background-color: @fgColor;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    .tableOrTree {
      .tableBorder {
        border-top: 1px solid #e8e8e8;
        border-right: 1px solid #e8e8e8;
        border-left: 1px solid #e8e8e8;
        border-radius: 4px;
      }

      th {
        font-size: 14px;
        font-weight: 500;
        color: @textColor;
        line-height: 17px;
        padding: 10px 10px;
        background-color: #f3f7fb;
      }

      td {
        font-size: 14px;
        font-weight: 500;
        color: @textColor;
        line-height: 17px;
        padding: 8px 10px;
      }

      .flex-end {
        display: flex;
        justify-content: flex-end;
      }

      .mt-20 {
        margin-top: 20px;
      }

      .mb-20 {
        margin-bottom: 20px;
      }

      .tree-box {
        border: 1px solid #ececec;
        border-radius: 4px;
      }
    }

    .iptBtn {
      padding: 8px 37px;
      border-radius: 19px;
      font-size: 16px;
      color: @fgColor;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      cursor: pointer;
      opacity: 1;
      transition: opacity 0.3s ease-in-out;
      &:hover {
        opacity: 0.9;
      }
    }

    .blue-text {
      color: #409eff !important;
    }
  }

  .tag {
    &_invalid {
      font-size: 12px;
      font-weight: 400;
      border-radius: 2px;
      padding: 2px 5px;
      color: #8c8c8c;
      white-space: nowrap;
      background: rgba(140, 140, 140, 0.1);
      margin-left: 10px;
    }
  }
}
