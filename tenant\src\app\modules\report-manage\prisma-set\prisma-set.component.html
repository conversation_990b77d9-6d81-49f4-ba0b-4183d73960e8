<div class="title">
    <span>报告设置</span>
    
    <div class="title_right_1">
        <div class="linelin_left" [ngClass]="mode === '1' ? 'linear':''" (click)="getActive('1')">
            相关系数
        </div>
        <div class="linelin_left mid_line" [ngClass]="mode === '3' ? 'linear':''" (click)="getActive('3')">
            自定义指数
        </div>
        <div class="linelin_right" [ngClass]="mode === '2' ? 'linear':''" (click)="getActive('2')">
            发展建议
        </div>
    </div>
</div>

<div class="body">
    <app-xi-shu *ngIf="mode === '1'" [prismaReportDataId]="prismaReportDataId"></app-xi-shu>
    <app-custom-mark *ngIf="mode === '3'" [prismaReportDataId]="prismaReportDataId" [projectId]="projectId"></app-custom-mark>
    <app-idp *ngIf="mode === '2'" [prismaReportDataId]="prismaReportDataId"></app-idp>
</div>

<div class="bottom">
    <div class="pre">
        <button nz-button (click)="redoGroupPdf()" [nzLoading]="isCreating">
            <span>重新生成报告</span>
        </button>
    </div>
    
</div>

