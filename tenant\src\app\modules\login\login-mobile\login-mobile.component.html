<div class="warp-bg">
  <div class="warp">
    <img src="./assets/images/mobile_logo.png" class="logo" />
    <section class="reg-warp">
      <h2 class="title">注册</h2>
      <form nz-form [formGroup]="validateForm">
        <nz-form-item>
          <nz-form-control
            nzHasFeedback
            nzValidatingTip="Validating..."
            nzErrorTip="请输入姓名"
          >
            <input
              nz-input
              formControlName="realName"
              placeholder="*请输入姓名"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control
            nzHasFeedback
            nzValidatingTip="Validating..."
            nzErrorTip="请输入联系电话"
          >
            <input
              nz-input
              formControlName="mobile"
              placeholder="*请输入联系电话"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control
            nzHasFeedback
            nzValidatingTip="Validating..."
            nzErrorTip="请选择职位"
          >
            <nz-select
              formControlName="position"
              class="select"
              style="border: 0;"
              placeholder="*请选择职位"
            >
              <nz-option
                nzValue="董事长/创始人"
                nzLabel="董事长/创始人"
              ></nz-option>
              <nz-option nzValue="首席执行官" nzLabel="首席执行官"></nz-option>
              <nz-option nzValue="人力副总裁" nzLabel="人力副总裁"></nz-option>
              <nz-option nzValue="人力总监" nzLabel="人力总监"></nz-option>
              <nz-option nzValue="人力经理" nzLabel="人力经理"></nz-option>
              <nz-option nzValue="人力主管" nzLabel="人力主管"></nz-option>
              <nz-option nzValue="HR/HRBP" nzLabel="HR/HRBP"></nz-option>
              <nz-option
                nzValue="IT或职能部门"
                nzLabel="IT或职能部门"
              ></nz-option>
              <nz-option nzValue="其他" nzLabel="其他"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control
            nzHasFeedback
            nzValidatingTip="Validating..."
            nzErrorTip="请输入企业邮箱"
          >
            <input
              nz-input
              formControlName="email"
              placeholder="*请输入企业邮箱"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control
            nzHasFeedback
            nzValidatingTip="Validating..."
            nzErrorTip="请输入企业全称"
          >
            <input
              nz-input
              formControlName="name"
              placeholder="*请输入企业全称"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control
            nzHasFeedback
            nzValidatingTip="Validating..."
            nzErrorTip="请输入登陆密码"
          >
            <input
              nz-input
              type="password"
              formControlName="password"
              placeholder="*请输入登陆密码"
              minlength="6"
              maxlength="20"
            />
          </nz-form-control>
        </nz-form-item>
        <div class="tip">必须由英文字母、数字或符号组合，长度在6-20之间</div>
      </form>
    </section>
    <section class="reg-warp2">
      <h2 class="title-sub">
        卡片密码
        <a class="add-btn" (click)="onAdd()">
          <img src="./assets/images/add.png" alt="" /> 添加卡片</a
        >
      </h2>
      <form nz-form>
        <nz-form-item *ngFor="let psd of cardPasswords; let i = index">
          <nz-form-control
            nzValidatingTip="Validating..."
            nzErrorTip="请输入卡片密码"
          >
            <nz-input-group [nzSuffix]="suffixTpl">
              <input
                #cardPassword
                nz-input
                [name]="'cardPassword' + i"
                [(ngModel)]="psd"
                placeholder="请输入卡片密码"
                (input)="changePsd(i)"
                (blur)="checkPassword(cardPassword.value, i)"
              />
            </nz-input-group>
            <ng-template #suffixTpl>
              <ng-container *ngIf="!errTipInfo[i] && i != 0 && !success"
                ><i
                  nz-icon
                  nzType="minus-circle"
                  nzTheme="fill"
                  (click)="onDelete(i)"
                ></i
              ></ng-container>
              <ng-container *ngIf="success">
                <span style="color:#54D386;"
                  >使用成功 <i nz-icon nzType="check-circle" nzTheme="fill"></i
                ></span>
              </ng-container>
              <ng-container *ngIf="errTipInfo[i]"
                ><span style="color: red;"
                  >{{ errTipInfo[i] }}
                  <i nz-icon nzType="close-circle" nzTheme="fill"></i
                ></span>
              </ng-container>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
      </form>
      <button
        nz-button
        nzType="primary"
        class="submit-btn"
        (click)="onSubmit()"
        [disabled]="disabled"
      >
        提交
      </button>
    </section>
  </div>
</div>

<ng-template #tplContent>
  <h2 style="text-align: center;font-size: 20px;">提交成功！</h2>
  <p>稍后管理员为您开通账户，并发送至邮箱，请留意查收。</p>
</ng-template>
