/**
 *
 *  @author: <PERSON>
 *  @Date: 2023/11/15
 *  @content: 个人中心-设置权限
 *
 */
import { Component, OnInit } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd";

import _ from "lodash";

import { ProjectManageService } from "../../service/project-manage.service";
import { NewActivityService } from "../../new-activity/new-activity.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-set-permission-modal",
  templateUrl: "./set-permission-modal.component.html",
  styleUrls: ["./set-permission-modal.component.less"],
})
export class SetPermissionModalComponent implements OnInit {
  listOfOption: Array<{ label: string; value: string }> = [];
  size = "default";
  singleValue = "a10";
  multipleValue = ["a10", "c12"];
  tagValue = ["a10", "c12", "tag"];
  // 账户搜索
  searchUserName: string = "";
  // 权限搜索
  searchPermissionName: string = "";
  // 账户列表
  userList: any[] = [];
  // 权限列表
  permissionList: any[] = [];
  // 选中的账户
  clickUserId: string;
  // 选中的权限
  clickPermissionId: string;
  // hover
  hoverid: string;

  permissionBoxStatus: any = "add";

  // 标准
  stanProListList: any[] = [];

  stanAllChecked: boolean = false;
  stanIndeterminate: boolean = false;
  checkedStanProListList: any[] = [];

  // 组合
  andProListList: any[] = [];
  andAllChecked: boolean = false;
  andIndeterminate: boolean = false;
  checkedAndProListList: any[] = [];

  // 专属
  likeProListList: any[] = [];
  likeAllChecked: boolean = false;
  likeIndeterminate: boolean = false;
  checkedLikeProListList: any[] = [];

  perEditId: string; // 修改权限时带入

  permissionObj: any = {
    name: {
      zh_CN: "",
    },
    addMasterManager: false, // 主
    addSubManagerCreateReport: false, // 子
    addSubManagerViewProgress: false,
    onlyViewSelf: false,
    combinationProducts: [], // 组合
    exclusiveProducts: [], // 专属
    standardProducts: [],
  };

  // 恢复默认使用
  permissionDefaultObj: any = {
    name: {
      zh_CN: "",
    },
    addMasterManager: false, // 主
    addSubManagerCreateReport: false, // 子
    addSubManagerViewProgress: false,
    onlyViewSelf: false,
    combinationProducts: [], // 组合
    exclusiveProducts: [], // 专属
    standardProducts: [],
  };
  permissionName: any = {
    zh_CN: "",
  };

  currentUserIndex: number = null;
  // 设置权限 抽屉visible
  visible: boolean = false;
  // 添加/修改 抽屉visible
  childrenVisible: boolean = false;

  constructor(
    private api: ProjectManageService,
    private proApi: NewActivityService,
    private msg: NzMessageService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    const children: Array<{ label: string; value: string }> = [];
    for (let i = 10; i < 36; i++) {
      children.push({ label: i.toString(36) + i, value: i.toString(36) + i });
    }
    this.listOfOption = children;
  }

  // 获取账户列表
  loadUserList() {
    this.api.getPermissionUser(this.searchUserName).subscribe((res) => {
      if (res.result.code === 0) {
        this.userList = res.data;
      }
    });
  }

  /**
   * 数据清除
   */
  clear() {
    this.searchPermissionName = "";
    this.searchUserName = "";
    this.clickUserId = "";
    this.clickPermissionId = "";
  }

  /**
   * 新增or编辑侧边栏
   * @param type
   * @param e
   */
  show(type, e?) {
    this.childrenVisible = true;
    this.permissionBoxStatus = type;
    if (type === "add") {
      // 触发重置数据
      this.clearPerParams(false);
    } else {
      this.permissionObj = e;
      this.StanSelectModelChange(e.standardProducts);
      this.andSelectModelChange(e.combinationProducts);
      this.likeSelectModelChange(e.exclusiveProducts);
      this.stanIndeterminateChange();
      this.andIndeterminateChange();
      this.likeIndeterminateChange();
    }
  }

  /**
   * 恢复默认
   * @param e
   */
  clearPerParams(e) {
    if (e) {
      let id = this.permissionObj.id;
      this.permissionObj = _.cloneDeep(this.permissionDefaultObj);
      this.permissionObj.id = id;
    } else {
      this.permissionObj = _.cloneDeep(this.permissionDefaultObj);
    }

    this.stanAllChecked = false;
    this.andAllChecked = false;
    this.likeAllChecked = false;
    this.stanIndeterminate = false;
    this.andIndeterminate = false;
    this.likeIndeterminate = false;
    this.checkedStanProListList = [];
    this.checkedAndProListList = [];
    this.checkedLikeProListList = [];
    this.likeProListList.forEach((item) => {
      item.checked = false;
    });
    this.andProListList.forEach((item) => {
      item.checked = false;
    });
    this.stanProListList.forEach((item) => {
      item.checked = false;
    });
  }

  /**
   * 编辑权限
   * @returns
   */
  editPermission() {
    if (!this.permissionObj.name.zh_CN)
      // return this.msg.error("请填写权限名称");
      return this.customMsg.open("error", "请填写权限名称");
    this.permissionObj.standardProducts = this.checkedStanProListList;
    this.permissionObj.combinationProducts = this.checkedAndProListList;
    this.permissionObj.exclusiveProducts = this.checkedLikeProListList;

    if (this.permissionBoxStatus !== "add") {
      this.api.editPermissions(this.permissionObj).subscribe((res) => {
        if (res.result.code === 0) {
          this.msg.success("修改成功");
          this.childrenVisible = false;
          this.loadPermissionList();
        }
      });
    } else {
      this.api.addPermissions(this.permissionObj).subscribe((res) => {
        if (res.result.code === 0) {
          this.msg.success("创建成功");
          this.childrenVisible = false;
          this.loadPermissionList();
        }
      });
    }
  }

  /**
   * 删除权限
   * @param id
   */
  delPermission(id) {
    this.api.delPermissions(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("删除成功");
        if (
          this.currentUserIndex !== null &&
          this.userList[this.currentUserIndex].permission.id === id
        ) {
          this.userList[this.currentUserIndex].permission.id = null;
        }
        // this.loadUserList()
        this.clearPerParams(false);
        this.loadPermissionList();
      }
    });
  }

  /**
   * 获取权限列表
   */
  loadPermissionList() {
    this.api.getPermissionList(this.searchPermissionName).subscribe((res) => {
      if (res.result.code === 0) {
        this.permissionList = res.data;
      }
    });
  }

  /**
   * 获取标准产品
   */
  loadstanProList() {
    let params = {
      orderBy: "COMPREHENSIVE_DESC",
      questionnaireSceneTypeEnum: "STANDARD_PRODUCT",
      page: { current: 1, size: 500 },
    };
    this.proApi.gettoollist(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.stanProListList = this.mapData(
          res.data.standardQuestionnaireResultVOs
        );
      }
    });
  }

  /**
   * 获取组合产品
   */
  loadandProList() {
    // 组合
    let params = {
      orderBy: "COMPREHENSIVE_DESC",
      questionnaireSceneTypeEnum: "SOLUTION",
      page: { current: 1, size: 500 },
    };
    this.proApi.gettoollist(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.andProListList = this.mapData(
          res.data.standardQuestionnaireResultVOs
        );
      }
    });
  }

  /**
   * 获取专属产品
   */
  loadlikeProList() {
    this.proApi.getmybestlike({}).subscribe((res) => {
      if (res.result.code === 0) {
        this.likeProListList = this.mapData(
          res.data.standardQuestionnaireResultVOs
        );
      }
    });
  }

  /**
   * mapData
   * @param data
   * @returns
   */
  mapData(data) {
    if (data.length !== 0) {
      data.forEach((element) => {
        element.checked = false;
      });
    }
    return data;
  }

  /**
   * 获取数据
   */
  loadData() {
    this.loadUserList();
    this.loadPermissionList();
    this.loadstanProList();
    this.loadandProList();
    this.loadlikeProList();
  }

  /**
   * 标准产品-全选
   */
  StanAllChange(e) {
    let arr = [];
    this.stanProListList.forEach((item) => {
      item.checked = e;
    });
    this.stanProListList.map((item, index) => {
      if (item.zh_CN !== "all") {
        if (item.checked) {
          arr.push(item.id);
        }
      }
    });
    this.checkedStanProListList = arr;
  }

  /**
   * 标准产品-全选动画
   */
  stanIndeterminateChange() {
    if (
      this.checkedStanProListList.length > 0 &&
      this.checkedStanProListList.length < this.stanProListList.length
    ) {
      this.stanAllChecked = false;
      this.stanIndeterminate = true;
    }
    if (this.checkedStanProListList.length === 0) {
      this.stanAllChecked = false;
      this.stanIndeterminate = false;
    }
    if (this.checkedStanProListList.length === this.stanProListList.length) {
      this.stanAllChecked = true;
      this.stanIndeterminate = false;
    }
  }

  /**
   * 标准产品-checkbox单选
   */
  StanModelChange(id) {
    this.checkedStanProListList = [];
    this.stanProListList.forEach((item) => {
      if (item.checked) {
        this.checkedStanProListList.push(item.id);
      }
    });
    this.stanIndeterminateChange();
  }

  /**
   * 标准产品-下拉单选
   */
  StanSelectModelChange(e) {
    if (e[e.length - 1] === "all") {
      e.splice(e.length - 1, 1);
    }
    this.checkedStanProListList = e;
    if (this.checkedStanProListList.length === 0) {
      this.stanProListList.forEach((ele) => {
        ele.checked = false;
      });
    } else {
      this.stanProListList.forEach((ele) => {
        for (
          let index = 0;
          index < this.checkedStanProListList.length;
          index++
        ) {
          const element = this.checkedStanProListList[index];
          if (element === ele.id) {
            ele.checked = true;
            return;
          } else {
            ele.checked = false;
          }
        }
      });
    }
    this.stanIndeterminateChange();
  }

  /**
   * 组合产品-全选
   */
  andAllChange(e) {
    let arr = [];
    this.andProListList.forEach((item) => {
      item.checked = e;
    });
    this.andProListList.map((item) => {
      if (item.zh_CN !== "all") {
        if (item.checked) {
          arr.push(item.id);
        }
      }
    });
    this.checkedAndProListList = arr;
  }

  /**
   * 组合产品-全选动画
   */
  andIndeterminateChange() {
    if (
      this.checkedAndProListList.length > 0 &&
      this.checkedAndProListList.length < this.andProListList.length
    ) {
      this.andAllChecked = false;
      this.andIndeterminate = true;
    }
    if (this.checkedAndProListList.length === 0) {
      this.andAllChecked = false;
      this.andIndeterminate = false;
    }
    if (this.checkedAndProListList.length === this.andProListList.length) {
      this.andAllChecked = true;
      this.andIndeterminate = false;
    }
  }

  /**
   * 组合产品-checkbox单选
   * @param id
   */
  andModelChange(id) {
    this.checkedAndProListList = [];
    this.andProListList.forEach((item) => {
      if (item.checked) {
        this.checkedAndProListList.push(item.id);
      }
    });
    this.andIndeterminateChange();
  }

  /**
   * 组合产品-下拉单选
   * @param e
   */
  andSelectModelChange(e) {
    if (e[e.length - 1] === "all") {
      let index = e.findIndex((item) => {
        return item === "all";
      });
      if (index !== -1) {
        e.splice(index, 1);
      }
    }
    this.checkedAndProListList = e;
    if (this.checkedAndProListList.length === 0) {
      this.andProListList.forEach((ele) => {
        ele.checked = false;
      });
    } else {
      this.andProListList.forEach((ele) => {
        for (
          let index = 0;
          index < this.checkedAndProListList.length;
          index++
        ) {
          const element = this.checkedAndProListList[index];
          if (element === ele.id) {
            ele.checked = true;
            return;
          } else {
            ele.checked = false;
          }
        }
      });
    }
    this.andIndeterminateChange();
  }

  /**
   * 专属产品-全选
   * @param e
   */
  likeAllChange(e) {
    let arr = [];
    this.likeProListList.forEach((item) => {
      item.checked = e;
    });
    this.likeProListList.map((item) => {
      if (item.zh_CN !== "all") {
        if (item.checked) {
          arr.push(item.id);
        }
      }
    });
    this.checkedLikeProListList = arr;
  }

  /**
   * 专属产品-全选动画
   */
  likeIndeterminateChange() {
    if (
      this.checkedLikeProListList.length > 0 &&
      this.checkedLikeProListList.length < this.likeProListList.length
    ) {
      this.likeAllChecked = false;
      this.likeIndeterminate = true;
    }
    if (this.checkedLikeProListList.length === 0) {
      this.likeAllChecked = false;
      this.likeIndeterminate = false;
    }
    if (this.checkedLikeProListList.length === this.likeProListList.length) {
      this.likeAllChecked = true;
      this.likeIndeterminate = false;
    }
  }

  /**
   * 专属产品-checkbox单选
   * @param id
   */
  likeModelChange(id) {
    this.checkedLikeProListList = [];
    this.likeProListList.forEach((item) => {
      if (item.checked) {
        this.checkedLikeProListList.push(item.id);
      }
    });
    this.likeIndeterminateChange();
  }

  /**
   * 专属产品-下拉单选
   * @param e
   */
  likeSelectModelChange(e) {
    if (e[e.length - 1] === "all") {
      let index = e.findIndex((item) => {
        return item === "all";
      });
      if (index !== -1) {
        e.splice(index, 1);
      }
    }
    this.checkedLikeProListList = e;
    if (this.checkedLikeProListList.length === 0) {
      this.likeProListList.forEach((ele) => {
        ele.checked = false;
      });
    } else {
      this.likeProListList.forEach((ele) => {
        for (
          let index = 0;
          index < this.checkedLikeProListList.length;
          index++
        ) {
          const element = this.checkedLikeProListList[index];
          if (element === ele.id) {
            ele.checked = true;
            return;
          } else {
            ele.checked = false;
          }
        }
      });
    }
    this.likeIndeterminateChange();
  }

  /**
   * 打开权限设置侧边栏
   */
  setPermissions() {
    this.visible = true;
    this.loadData();
  }

  /**
   * 选中账户
   * @param userid
   * @param index
   * @param permissionid
   */
  clickUser(userid, index: number, permissionid?) {
    this.clickUserId = userid;
    this.currentUserIndex = index;
    permissionid
      ? (this.clickPermissionId = permissionid)
      : (this.clickPermissionId = "");
  }

  /**
   * 点击选中权限
   * @param perid
   */
  clickPermission(perid) {
    this.clickPermissionId = perid;
    if (this.clickUserId) {
      this.userList.forEach((item) => {
        if (item.id === this.clickUserId) {
          item.permission = {
            id: perid,
          };
          item.isEditPer = true;
        }
      });
    }
  }

  /**
   * 关闭外层侧边烂
   */
  close() {
    this.clear();
    this.visible = false;
  }

  /**
   * 关闭子侧边烂
   */
  childrenClose() {
    this.childrenVisible = false;
    this.clearPerParams(false);
  }

  /**
   * 保存
   * @returns
   */
  save() {
    let arr = [];
    this.userList.map((item) => {
      if (item.isEditPer) {
        if (item.permission.id) {
          arr.push({
            userId: item.id,
            permissionId: item.permission.id,
          });
        }
      }
    });
    if (arr.length === 0) {
      // this.msg.error("没有需要保存的修改");
      this.customMsg.open("error", "没有需要保存的修改");
      return;
    } else {
      this.api.setPermissions(arr).subscribe((res) => {
        if (res.result.code === 0) {
          this.msg.success("权限修改成功");
          this.close();
        }
      });
    }
  }
}
