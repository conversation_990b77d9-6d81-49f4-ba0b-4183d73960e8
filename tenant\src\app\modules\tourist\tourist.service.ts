import {Injectable} from '@angular/core';
import { Router } from "@angular/router";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Inject } from "@angular/core";
import { ITokenService, DA_SERVICE_TOKEN } from '@knz/auth';
import { environment } from '@env/environment';

@Injectable({
  providedIn: 'root'
})
export class TouristService {
  
  url:string;
  constructor(private router: Router,private http: HttpClient,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService
    ) { }

  downLoad(type) {
    switch (type){
      case 'tip':this.url='sag_report_sample-TIP.pdf';break;
      case 'epa':this.url='sag_report_sample-EPA.pdf';break;
      case 'ama':this.url='sag_report_sample-AMA.pdf';break;
      case 'at':this.url='sag_report_sample-AT.pdf';break;
      case 'ca':this.url='sag_report_sample-CA.pdf';break;
      case 'csi':this.url='sag_report_sample-CSI.pdf';break;
      case 'pwvo':this.url='sag_report_sample-PWVO.pdf';break;
      case 'pca':this.url='sag_report_sample-PCA.pdf';break;
      case 'pta':this.url='sag_report_sample-PTA.pdf';break;
      case '360':this.url='sag_report_sample-360.pdf';break;
      case 'mca':this.url='sag_report_sample-MCA.pdf';break;
      case 'prisma':this.url='sag_report_sample-PRISMA.pdf';break;
      case 'train':this.url='sag_report_sample-360-TRAIN.pdf';break;
      case '270':this.url='sag_report_sample-270.pdf';break;
      case 'ren':this.url='sag_report_sample-101.pdf';break;
      case 'culture':this.url='sag_report_sample-1V1.pdf';break;
      case 'she':this.url='sag_report_sample-1X.pdf';break;
      case 'xiao':this.url='sag_report_sample-1+1.pdf';break;
      case 'pdp':this.url='sag_report_sample-PDP.pdf';break;
      case 'sjtp':this.url='sag_report_sample-SJTP.pdf';break;
      case 'mhs':this.url='sag_report_sample-MHS.pdf';break;
      case 'prismaculture':this.url='sag_report_sample-IAS-CULTURE.pptx';break;
      case 'doublePerspective':this.url='sag_report_sample-DP.pptx';break;
      case 'orgSurvey':this.url='sag_report_sample-OC.pptx';break;
    }
    let tokenObj = this.tokenService.get();
    if (tokenObj && tokenObj.token) {
      let baseUrl: string = window.location.origin + "/";
      if(baseUrl.indexOf('http://localhost') !== -1) {
        baseUrl = 'http://***********/'
      }
      window.open(`${baseUrl}static/files/report_sample/${this.url}`,"_blank");
    } else {
      this.router.navigateByUrl('/user/register');
    }
    
  }

}

