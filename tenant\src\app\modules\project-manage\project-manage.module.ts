import { NgModule } from '@angular/core';
import { Routes, RouterModule } from "@angular/router";

// modules
import { SharedModule } from '@shared';

// pipes
import { IdnamePipe } from './idname.pipe';

// components
import { ProjectManageHomeComponent } from './project-manage-home/project-manage-home.component'; // 新版活动列表
import { ProjectManageNormComponent } from './project-manage-norm/project-manage-norm.component'; // 内外部常模
import { ProjectManageInviteComponent } from './project-manage-invite/project-manage-invite.component';
import { ProjectManageComponent } from './project-manage.component';
import { ProjectComponent } from './project/project.component';
import { ProjectDelayComponent } from './project-delay/project-delay.component';
import { ProjectReopenComponent } from './project-reopen/project-reopen.component';
import { InviteSingleComponent } from './invite-single/invite-single.component';
import { TenantConfigComponent } from './tenant-config/tenant-config.component';
import { TenantNormConfigComponent } from './tenant-norm-config/tenant-norm-config.component';
import { ProjectManageInvite360Component } from './project-manage-invite360/project-manage-invite360.component';
import { ProjectManageInvite360EvalComponent } from './project-manage-invite360-eval/project-manage-invite360.component';
import { SpecifiedComponent } from './project-manage-invite360/specified/specified.component';
import { SpecifiedEvalComponent } from './project-manage-invite360-eval/specified/specified.component';
import { RoleComponent } from "./project-manage-invite360/role/role.component";
import { RoleNewComponent } from "./project-manage-invite360/role-new/role-new.component";
import { RoleEvalComponent } from './project-manage-invite360-eval/role/role.component';


import { PrismaManageComponent } from './project-manage-invite360/project-prisma-invite/project-prisma.component';
import { PersonListComponent } from './person-list/person-list.component'

import { tablelistconfig } from './project/tablelist/tablelist.component';
import { SetTypeComponent } from './set-type/set-type.component';
import { HistoricalComparativeContentComponent } from './historical-comparative-content/historical-comparative-content.component';
import { HistoryModalContentComponent } from './historical-comparative-content/history-modal-content/history-modal-content.component';
import { CardComponent } from './project-manage-home/card/card.component';
import { ProjectManageDetailComponent } from './project-manage-detail/project-manage-detail.component';
import { AddHistoricalComponent } from './historical-comparative-content/add-historical/add-historical.component';
import { ChooseInvitorComponent } from './project-manage-invite360/choose-invitor/choose-invitor.component';
import { PrismaDrawerComponent } from './project-manage-detail/prisma-drawer/prisma-drawer.component';
import { PopulationLabelPageComponent } from './project-manage-detail/population-label-page/population-label-page.component';
import { OrganizationPageComponent } from './project-manage-detail/organization-page/organization-page.component';
import { AdministratorModalComponent } from './project-manage-detail/administrator-modal/administrator-modal.component';
import { SubAdministratorModalComponent } from './project-manage-detail/sub-administrator-modal/sub-administrator-modal.component';
import { WordCloudComponent } from './word-cloud/word-cloud.component';
import { BatchExportModalComponent } from './project-manage-detail/batchexport-modal/batchexport-modal.component';
// import { ProjectManageInviteRefactorComponent } from './project-manage-invite-refactor/project-manage-invite-refactor.component'
import { SelfInviteComponent } from './project-manage-invite360/self-invite/self-invite.component';

// routes
const routes: Routes = [
  { path: 'home', component: ProjectManageHomeComponent, data: { permissionCode: "SAG:TENANT:PROJECT_MGT:LIST" } },
  { path: 'norm', component: ProjectManageNormComponent},
  { path: 'home-detail', component: ProjectManageDetailComponent ,data:{permissionCode: "SAG:TENANT:PROJECT_MGT:DETAIL"}},
  { path: 'word-cloud', component: WordCloudComponent },
  { path: 'invite', component: ProjectManageInviteComponent },
  { path: 'invite360', component: ProjectManageInvite360Component, runGuardsAndResolvers: 'paramsChange'},
  { path: 'invite360-eval', component: ProjectManageInvite360EvalComponent, runGuardsAndResolvers: 'paramsChange'},
  { path: 'inviteprisma', component: PrismaManageComponent ,runGuardsAndResolvers: 'paramsChange'},//敬业度邀请
  { path: 'tenant-config', component: TenantConfigComponent },
  { path: 'tenant-norm-config', component: TenantNormConfigComponent },
  { path: 'historical-comparison', component: HistoricalComparativeContentComponent },
  { path: 'add-historical-comparison', component: AddHistoricalComponent },
  { path: '**', redirectTo: 'home' },
  
];

@NgModule({
  declarations: [
    // pipes
    IdnamePipe,

    // components
    ProjectManageHomeComponent,
    ProjectManageInviteComponent,
    ProjectManageComponent,
    ProjectComponent,
    ProjectDelayComponent,
    ProjectReopenComponent,
    InviteSingleComponent,
    TenantConfigComponent,
    TenantNormConfigComponent,
    ProjectManageInvite360Component,
    ProjectManageInvite360EvalComponent,
    SpecifiedComponent,
    SpecifiedEvalComponent,
    RoleComponent,
    RoleNewComponent,
    RoleEvalComponent,
    PrismaManageComponent,
    PersonListComponent,
    tablelistconfig,
    SetTypeComponent,
    HistoricalComparativeContentComponent,
    HistoryModalContentComponent,
    CardComponent,
    ProjectManageDetailComponent,
    AddHistoricalComponent,
    ChooseInvitorComponent,
    PrismaDrawerComponent,
    PopulationLabelPageComponent,
    OrganizationPageComponent,
    AdministratorModalComponent,
    SubAdministratorModalComponent,
    WordCloudComponent,
    BatchExportModalComponent,
    ProjectManageNormComponent,
    SelfInviteComponent, // 设置自邀请人数
  ],
  imports: [
    RouterModule.forChild(routes),
    SharedModule,
  ],
  entryComponents: [
    ProjectDelayComponent,
    ProjectReopenComponent,
    ProjectManageInviteComponent,
    SpecifiedComponent,
    SpecifiedEvalComponent,
    RoleComponent,
    RoleNewComponent,
    RoleEvalComponent,
    PersonListComponent,
    PrismaDrawerComponent,
    SetTypeComponent,
    HistoryModalContentComponent,
    CardComponent,
    ChooseInvitorComponent,
    PopulationLabelPageComponent, // 调研 人口标签 tab
    OrganizationPageComponent, // 调研 组织 tab
    AdministratorModalComponent,
    SubAdministratorModalComponent,
    SelfInviteComponent, // 设置自邀请人数
    BatchExportModalComponent,
  ]
})
export class ProjectManageModule {
}
