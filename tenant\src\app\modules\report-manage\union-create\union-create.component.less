nz-input-group {
  padding: 10px 0;
}

// .title {
//     margin-top: 15px;
//     margin-bottom: 15px;
//     display: flex;
//     justify-content: space-between;
//     align-items: center;

//     span {
//         font-size: 24px;
//         font-family: PingFangSC-Light, PingFang SC;
//         font-weight: 300;
//         color: #17314C;
//         line-height: 33px;
//     }

//     .lan {
//         font-size: 14px;
//         font-family: PingFangSC-Regular, PingFang SC;
//         font-weight: 400;
//         color: #17314C;
//         line-height: 20px;
//         z-index: 10;
//     }

// }

.lan {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #17314c;
  line-height: 20px;
  z-index: 10;
  position: absolute;
  top: 16px;
  left: 180px;
}

.container {
  height: calc(100vh - 123px);
  display: flex;
  justify-content: space-between;
  // border: solid 1px #E6E6E6;
}
.footer-box {
  border-top: solid 1px #e6e6e6;
  width: 100%;
  height: 68px;
  padding: 10px 16px;
  bottom: 0px;
  left: 0px;
  position: absolute;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .after {
    display: flex;
    align-items: center;
    > span {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #262626;
      line-height: 20px;
    }
    .total {
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ff4f40;
      line-height: 28px;
      margin: 0 6px;
    }
  }
}
:host .container ::ng-deep .ant-checkbox-group-item {
  margin-bottom: 15px;
  display: block;
}
