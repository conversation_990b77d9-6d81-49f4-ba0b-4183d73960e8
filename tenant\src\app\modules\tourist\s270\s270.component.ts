import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Router } from "@angular/router";
import { NzMessageService } from "ng-zorro-antd";
import { TouristService } from '../tourist.service';

@Component({
  selector: 'app-s270',
  templateUrl: './s270.component.html',
  styleUrls: ['./s270.component.less']
})
export class S270Component implements OnInit {
  tourist:boolean=true;
  isNeedLogin:boolean=false;
  token:any;
  _token:any;
  constructor(private http: HttpClient,private router: Router,private message: NzMessageService,private TouristService:TouristService) { }

  ngOnInit() {
    
    // this.token = JSON.parse(localStorage.getItem("token"));
    this._token = JSON.parse(localStorage.getItem("_token"));
    if (this._token) {
      // let nowDate = new Date().getTime() - new Date(this.token.time).getTime();
      // let minutes = (nowDate % (1000 * 60 * 60)) / (1000 * 60);
      // if (minutes < 10) {
        this.tourist=false;
        this.isNeedLogin=true;
      // }
    }
  }
  downloadReport(){
    this.TouristService.downLoad('270');
  }

}
