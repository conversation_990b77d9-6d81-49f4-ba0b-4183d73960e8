@import '~@knz/theme/styles/default';
:host ::ng-deep {
  display: block;
  width: 100%;
  min-width: 1080px;
  height: 100%;

  .log {
    position: fixed;
    z-index: 999;
    padding: 19px;

    img {
      width: 142px;
      height: auto;
    }
  }

  .content-auto {
    position: fixed;
    top: 50%;
    left: 50%;
    display: flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: -webkit-flex;
    min-width: 1080px;
    color: #fff;
    text-align: center;
    -ms-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
  }

  input::-webkit-input-placeholder {
    color: #cdcdce !important;
    font-size: 14px !important;
  }

  input:-moz-placeholder {
    color: #cdcdce !important;
    font-size: 14px !important;
  }

  input::-moz-placeholder {
    color: #cdcdce !important;
    font-size: 14px !important;
  }

  input:-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #cdcdce !important;
    font-size: 14px !important;
  }

  ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  input[type="button"],
  input[type="submit"] {
    width: 136px;
    height: 37px;
    margin: 0;
    padding: 0;
    font-size: 16px;
    line-height: 37px;
    text-align: center;
    background-color: #fff;
    border: 0;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    -webkit-transition: all .3s;
    transition: all .3s;
  }

  .paddingbottom {
    padding-bottom: 0;
  }


  .inputcontrol {
    position: relative;
    width: 350px;
    height: 50px;
    margin-bottom: 20px;
    padding-top: 33px;
    font-size: 14px;

    label {
      position: absolute;
      top: 0;
      left: 0;
      display: block;
      width: 100%;
      height: 20px;
      margin-top: 10px;
      margin-bottom: 0;
      color: #40a9ff;
      font-weight: normal;
      font-size: 12px;
      text-align: left;
      opacity: 1;
    }

    ul {
      height: 38px;
      background-color: #fff;
      border: 1px solid #cdcdce;
      border-radius: 4px;

      li {
        float: left;
        margin: 0;
        padding: 0;

        i {
          color: #666;
          font-size: 16px;
        }

        img {
          max-width: 18px;
          height: auto;
        }

        input {
          width: 240px;
          height: 35px;
          margin: 0;
          padding: 0 2px;
          color: #666;
          font-size: 14px;
          background-color: White;
          border: 0;

          &:focus {
            border: 0;
            outline: none;
          }
        }
      }

      &:first-child {
        li:nth-child(1) {
          padding: 6px 10px 8px 12px;
        }
      }

      li:nth-child(1) {
        padding: 9px 10px 8px 12px;
      }
    }


  }


  .content {
    display: flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: -webkit-flex;
    width: 1132px;
    height: 567px;
    margin-left: -100px;
    .content-left {
      float: left;
      width: 768px;
      height: 567px;
      margin-right: 74px;
      //background-image: url(../../../assets/images/login-left.png);
      background-size: 100% 100%;
    }

    .content-right {
      float: left;
      width: 350px;
      height: 340px;
      padding-top: 0;

      .title {
        height: 20px;
        margin-top: 30px;
        margin-bottom: 35px;
        color: #40a9ff;
        font-size: 24px;
        text-align: center;
      }

      .text {
        height: 230px;
      }

      .forget {
        a {
          color: #666;
        }
      }

      .forget {
        height: 30px;
        padding: 0;
        padding-top: 25px;
        color: #666;
        font-size: 12px;
        text-align: left;
      }

      .btn-login {
        width: 350px;
        height: 48px;
        margin-top: 45px !important;
        color: #fff;
        font-size: 16px !important;
        background-image: linear-gradient(107deg, #7697fd, #5166e5);
        border-radius: 4px;
        opacity: 0.95;

        &:hover {
          // color: #A7E1EC !important;
        }

        // &:active{
        //     color: #1F98AF !important;

        // }

        &:disabled {
          color: rgba(0, 0, 0, .25) !important;
          background: #f5f5f5;

        }
      }


    }
  }



  .ant-tabs-bar{
    border: 0 !important;
  }

  .ant-tabs-nav{
    font-size: 18px;
    transform:none !important;
  }
  .ant-tabs-nav .ant-tabs-tab
  {
    padding: 15px 55px 15px 0;
  }
  .ant-tabs-nav .ant-tabs-tab-active{
    color: #40a9ff;
  }

  .ant-tabs-content{
    padding: 20px 0 0 0;
  }
  .ant-form-item{
    margin-bottom: 30px;
  }

  .btnCaptcha {
    width: 110px !important;
    height: 35px;
    margin-top: 0 !important;
    padding: 0 !important;
  }

  .ant-form-item .has-error .ant-form-explain, .ant-form-item .has-error .ant-form-split{
  text-align: left;
  }
  .ant-input {
    height: 40px !important;
  }
}

