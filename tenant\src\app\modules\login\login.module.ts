import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { SharedModule } from "@shared/shared.module";
import { LoginRoutingModule } from "./login-routing.module";
import { RegisterComponent } from "./register/register.component";
import { LoginComponent } from "./login/login.component";
import { ForgotPasswordComponent } from "./forgot-password/forgot-password.component";
import { SuccessComponent } from "./success/success.component";
import { WrapperLoginComponent } from "./wrapper-login/wrapper-login.component";
import { WrapperRegisterComponent } from "./wrapper-register/wrapper-register.component";
import { WrapperForgotComponent } from "./wrapper-forgot/wrapper-forgot.component";
import { LayoutModule } from "@src/layout/layout.module";
import { LoginMobileComponent } from "./login-mobile/login-mobile.component";
import { SuccessMobileComponent } from "./login-mobile/success/success.component";

import { AgreeComponent } from "./login/Userknx/agreement.component";
import { PolicyComponent } from "./login/Privacy/policy.component";
import { ResetComponent } from "./reset/reset.component";

import { ForgetPasswordModule } from "@knx/knx-ngx/forget-password";

@NgModule({
  declarations: [
    RegisterComponent,
    LoginComponent,
    ForgotPasswordComponent,
    SuccessComponent,
    WrapperLoginComponent,
    WrapperRegisterComponent,
    WrapperForgotComponent,
    LoginMobileComponent,
    SuccessMobileComponent,
    AgreeComponent,
    PolicyComponent,
    ResetComponent,
  ],
  imports: [
    CommonModule,
    LoginRoutingModule,
    SharedModule,
    LayoutModule,
    ForgetPasswordModule, // knx忘记密码
  ],
  entryComponents: [
    LoginComponent,
    RegisterComponent,
    ForgotPasswordComponent,
    SuccessComponent,
    AgreeComponent,
    PolicyComponent,
  ],
})
export class LoginModule {}
