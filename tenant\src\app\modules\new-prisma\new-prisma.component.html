<div class="index_xy">
  <div class="big_contant client-width">
    <div class="create_p">
      <span class="span_left">
        活动设置
        <img
          style="cursor: pointer;"
          src="assets/images/shownew.png"
          (click)="getnewlead()"
          alt=""
        />
      </span>
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </div>
    <ul class="create_name">
      <li style="margin-right: 10px;max-width: 500px;width: 500px;">
        <p>活动名称</p>
        <input
          nz-input
          nzSize="large"
          class="pri_name"
          placeholder="请输入活动名称"
          [(ngModel)]="prismaData.name.zh_CN"
          [disabled]="
            !permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:NAME_EDIT'
            )
          "
        />
      </li>
      <li>
        <p>活动周期</p>
        <div class=" time-picker-con">
          <nz-range-picker
            class="time_picker"
            [(ngModel)]="dateRange"
            [nzDisabledDate]="disabledDate"
            [nzShowTime]="{ nzMinuteStep: '30', nzFormat: 'HH:mm' }"
            [nzFormat]="'YYYY-MM-DD HH:mm'"
            [nzPlaceHolder]="['请选择开始日期', '请选择结束日期']"
            (ngModelChange)="onChange($event)"
            [nzDisabled]="
              !permissionService.isPermissionOrSag(
                'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:PERIOD_EDIT'
              )
            "
          ></nz-range-picker>
          <ng-template #suffixIconSearch>
            <i class="icon-time"></i>
          </ng-template>
        </div>
      </li>
    </ul>

    <div class="label_title">
      <p class="title">人口标签</p>
      <div
        class="div_right"
        *ngIf="
          permissionService.isPermissionOrSag(
            'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DEMOGRAPHIC_EDIT'
          )
        "
      >
        <div
          class="custom_add_xy"
          (click)="showAllCheck()"
          *ngIf="!isUpdateing && isShowAll"
        >
          <i class="iconfont icon-xianshi" style="font-size: 14px;"></i>
          <span>开启</span>
        </div>
        <div
          class="custom_add_xy"
          (click)="hiddenAllcheck()"
          *ngIf="!isUpdateing && !isShowAll"
        >
          <i class="iconfont icon-yingcang" style="font-size: 14px;"></i>
          <span>隐藏</span>
        </div>
        <div class="border_left_d" *ngIf="!isUpdateing"></div>
        <div class="custom_add_xy" (click)="checkAll()" *ngIf="!isUpdateing">
          <i nz-icon nzType="check-square" nzTheme="outline"></i>
          <span>全选</span>
        </div>
        <div class="border_left_d" *ngIf="!isUpdateing"></div>
        <div class="custom_add_xy" (click)="clearcheck()" *ngIf="!isUpdateing">
          <i class="iconfont icon-icon-" style="font-size: 14px;"></i>
          <span>清空</span>
        </div>
        <div class="custom_add_xy" *ngIf="isUpdateing">
          <span (click)="addfactor(true)">查看</span>
        </div>
        <div class="border_left_d" *ngIf="!isUpdateing"></div>
        <div class="custom_add_xy" *ngIf="!isUpdateing">
          <span (click)="addfactor(false)">+ 自定义</span>
        </div>
      </div>
    </div>
    <div class="top_div">
      <ul class="div_left">
        <li class="li_list">
          <nz-checkbox-wrapper (nzOnChange)="ngModelChange($event)">
            <div
              style="display: flex; justify-content: flex-start;flex-wrap: wrap;"
            >
              <div
                *ngFor="
                  let item of prismaData.factorname.standardAnalysisFactorVOS
                "
              >
                <div
                  class="li_span"
                  nz-checkbox
                  [nzDisabled]="isUpdateing || item.isRequire"
                  [(ngModel)]="item.isChecked"
                  [(nzValue)]="item.standardDemographicId"
                >
                  <div style="display: flex;align-items: center;">
                    <!-- <span nz-tooltip [(nzTooltipTitle)]="item.name.zh_CN"
                          *ngIf="item.name.zh_CN.length > 5">{{item.name.zh_CN}}</span> -->
                    <span>{{ item.name.zh_CN }}</span>
                    <ng-container
                      *ngIf="
                        permissionService.isPermissionOrSag(
                          'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DEMOGRAPHIC_EDIT'
                        )
                      "
                    >
                      <ng-container *ngIf="isUpdateing; else tmp">
                        <i
                          *ngIf="item.isChecked && !item.isHidden"
                          class="iconfont icon-xianshi"
                        ></i>
                        <i
                          *ngIf="item.isChecked && item.isHidden"
                          class="iconfont icon-yingcang"
                        ></i>
                      </ng-container>
                      <ng-template #tmp>
                        <i
                          *ngIf="item.isChecked && !item.isHidden"
                          class="iconfont icon-xianshi hover-icon"
                          style="cursor: pointer !important;"
                          (click)="changeHidden($event, item, i)"
                        ></i>
                        <i
                          *ngIf="item.isChecked && item.isHidden"
                          class="iconfont icon-yingcang hover-icon"
                          style="cursor: pointer !important;"
                          (click)="changeHidden($event, item, i)"
                        ></i>
                      </ng-template>
                    </ng-container>
                  </div>
                </div>
              </div>
            </div>
          </nz-checkbox-wrapper>
        </li>
      </ul>
    </div>

    <div class="setmodal">
      <div class="setmodal_top">
        <span style="color: #17314C;font-weight: bold;">模型设置</span>
        <span class="cur_span" (click)="showRoleModal()">高级设置</span>
      </div>
      <ul class="setmodal_card">
        <!-- *ngIf="!isUpdateing" -->
        <li class="card_left">
          <div
            class="left_div"
            *ngFor="let item of ['1']; let i = index"
            [ngClass]="showIndex == i ? 'showClass' : ''"
            (click)="getshow(i)"
          >
            <span
              nz-tooltip
              [nzTooltipTitle]="prismatitle.name.zh_CN"
              style="width: 100px;"
              >{{ prismatitle.name.zh_CN }}
            </span>
          </div>
        </li>
        <li class="card_right">
          <div class="right_top">
            <div style="width: 90%;">
              <div style="display: flex;align-items: center;">
                <p class="P_1">{{ prismatitle.name.zh_CN }}</p>
                <ng-container
                  *ngIf="
                    permissionService.isPermissionOrSag(
                      'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:TOOL_NAME_EDIT'
                    )
                  "
                >
                  <i
                    nz-icon
                    nzType="edit"
                    style="margin-left: 10px;cursor: pointer;"
                    nzTheme="outline"
                    (click)="EditName()"
                  ></i>
                </ng-container>
              </div>
              <div
                style="margin-top: 10px;"
                [innerHtml]="prismatitle.description"
              ></div>
            </div>

            <ng-container
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:UPLOAD_MODULE_IMAGE'
                )
              "
            >
              <div
                style="color: #409EFF;cursor: pointer;"
                (click)="getModalPie()"
              >
                查看模型
              </div>
            </ng-container>
          </div>
          <ul class="right_bottom">
            <li class="bottom_left">
              <ng-container *ngIf="!emptyShow">
                <div class="left_div">
                  <ul
                    class="div_one"
                    *ngFor="
                      let item of prismaData.factorname.detailedScoreConfigs;
                      let i = index
                    "
                    [ngClass]="i != 0 ? 'topClass' : ''"
                  >
                    <li class="li_title">
                      <span>{{ item.name.zh_CN }}</span>
                    </li>
                    <li class="li_list">
                      <span
                        class="li_span"
                        *ngFor="let ite of item.detailedScoreChildDimensions"
                      >
                        <span nz-tooltip [(nzTooltipTitle)]="ite.smallname">{{
                          ite.name.zh_CN
                        }}</span>
                        <!-- <span *ngIf="ite.name.zh_CN.length <= 5">{{ite.name.zh_CN}}</span> -->
                      </span>
                    </li>
                  </ul>
                </div>
              </ng-container>
              <!-- <nz-empty *ngIf="emptyShow" nzNotFoundImage="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg" [nzNotFoundContent]="contentTpl" [nzNotFoundFooter]="footerTpl">
                                    <ng-template #contentTpl>
                                        <span> 暂无模型!请先导入题本 </span>
                                    </ng-template>
                                    <ng-template #footerTpl>
                                        <button nz-button nzType="primary" (click)="goTobook('/new-activity/custom-book','cusbook')">定制题本</button>
                                    </ng-template>
                                </nz-empty> -->
              <!-- 空白占位  -->
              <div>
                <app-topic-empty
                  *ngIf="emptyShow"
                  text="暂无模型!请先导入题本"
                  (onClick)="goTobook('/new-activity/custom-book', 'cusbook')"
                ></app-topic-empty>
              </div>
            </li>
            <li
              class="bottom_right"
              *ngIf="
                prismaData.isShowQuestionBook || prismaData.isShowOrganization
              "
            >
              <div class="right_top">
                <span class="diss_span">关联任务</span>
              </div>
              <!-- 定制题本 -->
              <ng-container
                *ngIf="
                  prismaData.isShowQuestionBook &&
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:QUESTION'
                  )
                "
              >
                <app-task-card
                  *ngIf="!custom"
                  text="定制您的专属题本"
                  btnText="定制题本"
                  [isConfirmed]="prismaData?.isConfirmQuestionBook"
                  (onClick)="goTobook('/new-activity/book', 'cusbook')"
                >
                </app-task-card>
                <app-task-card
                  *ngIf="custom"
                  text="定制您的专属题本"
                  btnText="定制题本"
                  [isConfirmed]="prismaData?.isConfirmQuestionBook"
                  (onClick)="
                    goTobook('/new-activity/custom-book', 'defaultbook')
                  "
                >
                </app-task-card>
              </ng-container>
              <!-- 组织架构 -->
              <app-task-card
                *ngIf="
                  prismaData.isShowOrganization &&
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:ORGANIZATION'
                  )
                "
                text="开始构建组织架构"
                btnText="组织架构"
                [isConfirmed]="prismaData?.isConfirmOrganization"
                (onClick)="goTobook('/org', 'orgbook')"
              >
              </app-task-card>
              <!-- 作答说明 -->
              <app-task-card
                *ngIf="
                  prismaData.isShowanswer &&
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:ANSWER_DESCRIPTION'
                  )
                "
                text="自定义作答说明"
                btnText="作答说明"
                [isConfirmed]="prismaData?.isConfirmanswer"
                (onClick)="showModalDesc()"
              >
              </app-task-card>
              <!-- 题本分发 -->
              <app-task-card
                *ngIf="
                  prismaData.isShowPassages &&
                  !isUpdateing &&
                  prismaData.isEnableQuestionBookDistribute &&
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                  )
                "
                text="设置题本分发关系"
                btnText="题本分发"
                [showConfirmed]="false"
                (onClick)="showPassages('create')"
              >
              </app-task-card>
              <!-- 已选关联 -->
              <app-task-card
                *ngIf="
                  prismaData.isShowPassages &&
                  isUpdateing &&
                  prismaData.isEnableQuestionBookDistribute &&
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                  )
                "
                text="题本关联关系"
                btnText="已选关联"
                [showConfirmed]="false"
                (onClick)="showPassages('check')"
              >
              </app-task-card>
              <!-- 抽奖福利 -->
              <app-task-card
                *ngIf="
                  prismaData.isShowLottery &&
                  prismaData.isEnableAnswerLotteryDraw &&
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:LOTTERY'
                  )
                "
                text="设置题本填答中奖规则"
                btnText="抽奖福利"
                [showConfirmed]="false"
                (onClick)="goTobook('/new-prisma/lottery', 'lottery')"
              >
              </app-task-card>
              <!-- 报告设置 -->
              <app-task-card
                *ngIf="
                  prismaData.isEnableReportSettings &&
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:REPORT_CONGIF'
                  )
                "
                text="自定义报告内容"
                btnText="报告设置"
                [showConfirmed]="false"
                (onClick)="showReportSettings()"
              >
              </app-task-card>
              <!-- 分析指数 -->
              <!-- <app-task-card *ngIf="prismaData.isEnableAnalysisCustomIndex" 
                                text="组建自定义分析视角" btnText="分析指数" 
                                [showConfirmed]="false" (onClick)="showAnalytical()">
                            </app-task-card> -->
              <!-- 相关分析 -->
              <!-- <app-task-card *ngIf="prismaData.isEnableAnalysisTask" 
                                text="组建自定义交叉分析" btnText="相关分析" 
                                [showConfirmed]="false" (onClick)="showanalysis()">
                            </app-task-card> -->
            </li>
          </ul>
        </li>
      </ul>
    </div>
    <!-- 查看题本关系 -->
    <nz-drawer
      [(nzBodyStyle)]="nzBodyStyle"
      nzWrapClassName="round-right-drawer6-nofooter"
      [(nzVisible)]="showRelations"
      nzTitle="查看题本关系"
      (nzOnClose)="handleRamodal()"
      [nzWidth]="960"
    >
      <div class="shiwRa">
        <div *ngIf="ShowQuestion.length != 0">
          <!-- 题本分发导入导出 -->
          <div class="details-head">
            <button
              nz-button
              nzType="link"
              class="btn3"
              [nzLoading]="isDispenseDownLoadSpinning"
              (click)="onDispenseDownLoad()"
            >
              <i class="iconfont icon-export_ic"></i> 导出
            </button>
          </div>
          <nz-collapse class="question-book-distribution" [nzBordered]="false">
            <section
              dragula="VAMPIRESNEWPRISMA"
              [(dragulaModel)]="ShowQuestion"
            >
              <nz-collapse-panel
                *ngFor="let item of ShowQuestion; let i = index"
                [nzHeader]="titltHeader"
                [nzActive]="item.active"
                [ngStyle]="customStyle"
                [nzExpandedIcon]="expandedIcon"
              >
                <div
                  *ngFor="let res of item.questionList; let j = index"
                  [ngClass]="j % 2 == 0 ? '' : 'bg_blue_tip'"
                  [innerHTML]="res.name.zh_CN | html"
                ></div>
                <ng-template #titltHeader>
                  <p
                    style="
                    display: inline-block;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: bold;
                    font-size: 14px;
                    color: #262626;
                    line-height: 20px;
                    padding:14px 16px 14px 0;"
                  >
                    {{ item.relationName }}
                  </p>
                </ng-template>
                <ng-template #expandedIcon let-active>
                  <i
                    class="iconfont icon-caidan mover"
                    (click)="$event.stopPropagation()"
                    style="cursor: pointer;font-weight: normal;
                    color: #C4C4C4;
                    width: 40px;
                    padding-left: 16px;
                    height: 40px;
                    margin: 0;"
                  ></i>
                </ng-template>
              </nz-collapse-panel>
            </section>
          </nz-collapse>
        </div>
        <div *ngIf="ShowQuestion.length == 0">
          <nz-empty></nz-empty>
        </div>
      </div>

      <div class="drawer-footer" *ngIf="ShowQuestion.length != 0">
        <ng-container>
          <button nz-button nzType="default" (click)="showRelations = false">
            取消
          </button>
          <button nz-button nzType="primary" (click)="sort()">
            确定
          </button>
        </ng-container>
      </div>
    </nz-drawer>
    <!-- 题本分发 -->
    <nz-drawer
      [(nzBodyStyle)]="nzBodyStyle"
      nzWrapClassName="round-right-drawer6"
      [(nzVisible)]="isShowpagebook"
      nzTitle="题本分发"
      (nzOnClose)="handlebookmodal()"
      [nzWidth]="960"
    >
      <div class="drawer-content">
        <div class="drawer-head">
          <div>
            <button
              class="btn1"
              nz-button
              nzType="primary"
              nz-popover
              [nzPopoverPlacement]="'bottomLeft'"
              nzPopoverTrigger="click"
              [nzPopoverContent]="contentTemplate"
            >
              已选关联 ({{ ShowQuestion.length }})
            </button>
            <ng-template #contentTemplate>
              <ul class="ul_tips">
                <li class="top_tips">
                  <p style="font-size: 18px;color:#17314C;font-weight: bold;">
                    关联详情
                  </p>

                  <p style="font-size: 12px;color:#409EFF;cursor: pointer;">
                    <a
                      nz-popconfirm
                      nzPopconfirmTitle="确定清空所有关联选项吗?"
                      nzPopconfirmPlacement="bottom"
                      (nzOnConfirm)="ClearAll()"
                      (nzOnCancel)="Clearcancel()"
                    >
                      清空所有关联
                    </a>
                  </p>
                </li>
                <li>
                  <nz-collapse
                    class="question-book-distribution"
                    style="margin-top: 20px;"
                    [nzBordered]="false"
                  >
                    <section
                      dragula="VAMPIRESNEWPRISMA"
                      [(dragulaModel)]="ShowQuestion"
                    >
                      <nz-collapse-panel
                        *ngFor="let item of ShowQuestion; let i = index"
                        [nzHeader]="titltHeader"
                        [nzActive]="item.active"
                        [ngStyle]="customStyle"
                        [nzExpandedIcon]="expandedIcon1"
                      >
                        <li
                          *ngFor="let res of item.questionList; let j = index"
                          [ngClass]="j % 2 == 0 ? '' : 'bg_blue_tip'"
                          [innerHTML]="res.name.zh_CN | html"
                        ></li>
                        <ng-template #titltHeader>
                          <div
                            style="
                            display: inline-block;
                            padding:14px 16px 14px 0;
                            "
                          >
                            <p
                              style="
                            display: inline-block;
                            width: 500px;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: bold;
                            font-size: 14px;
                            color: #262626;
                            line-height: 20px;"
                            >
                              {{ item.relationName }}
                            </p>
                            <a
                              style="
                            display: inline-block;
                            "
                              nz-popconfirm
                              nzPopconfirmTitle="确定删除这条关联吗?"
                              (click)="$event.stopPropagation()"
                              nzPopconfirmPlacement="bottom"
                              (nzOnConfirm)="ClearOne(item.id)"
                              (nzOnCancel)="Clearcancel()"
                            >
                              <i nz-icon nzType="delete" nzTheme="twotone"></i>
                            </a>
                          </div>
                        </ng-template>
                        <ng-template #expandedIcon1 let-active>
                          <i
                            class="iconfont icon-caidan mover"
                            (click)="$event.stopPropagation()"
                            style="
                              display: inline-block;
                              cursor: pointer;
                              font-weight: normal;
                              color: #C4C4C4;
                              width: 40px;
                              padding-left: 16px;
                              height: 40px;
                              line-height: 40px;
                              margin: 0;"
                          ></i>
                        </ng-template>
                      </nz-collapse-panel>
                    </section>
                  </nz-collapse>
                </li>
              </ul>
              <div
                *ngIf="ShowQuestion.length != 0"
                style="display: flex;justify-content: end;padding-right: 20px;"
              >
                <button
                  nz-button
                  nzSize="small"
                  nzType="primary"
                  (click)="sort()"
                >
                  确定
                </button>
              </div>
            </ng-template>
            <button
              class="btn2"
              nz-button
              nzType="default"
              *ngIf="Step == 2"
              (click)="getRelation()"
            >
              关联
            </button>
          </div>
          <div>
            <!-- 题本分发导入导出 -->
            <nz-upload
              [nzCustomRequest]="onDispenseImport"
              [nzShowUploadList]="false"
            >
              <button nz-button nzType="link" class="btn3">
                <i class="iconfont icon-import"></i> 导入
              </button>
            </nz-upload>
            <button
              nz-button
              nzType="link"
              class="btn3"
              [nzLoading]="isDispenseDownLoadSpinning"
              (click)="onDispenseDownLoad()"
            >
              <i class="iconfont icon-export_ic"></i> 导出
            </button>
          </div>
        </div>
        <div class="drawer-card">
          <ng-container *ngIf="Step == 1">
            <ul class="Pagebook">
              <li class="left_page">
                <div
                  (click)="changepage('1')"
                  [ngClass]="pageshow == 1 ? 'pagesclass' : ''"
                >
                  组织架构
                </div>
                <div
                  (click)="changepage('2')"
                  [ngClass]="pageshow == 2 ? 'pagesclass' : ''"
                >
                  人口标签
                </div>
              </li>
              <li class="right_page">
                <ng-container *ngIf="pageshow == '1'">
                  <div class="tree_top">
                    <span></span>
                    <nz-input-group
                      style="width: 200px;margin-right: 16px;"
                      [nzSuffix]="suffixIconSearch"
                    >
                      <input
                        type="text"
                        nz-input
                        placeholder="请输入关键词"
                        [(ngModel)]="OrganizesearchValue"
                      />
                    </nz-input-group>
                  </div>
                  <div>
                    <nz-tree
                      #nzTreeComponent
                      nzCheckable
                      [nzData]="Organizationlist.children"
                      [nzCheckStrictly]="true"
                      (nzCheckBoxChange)="nzCheck($event)"
                      (nzClick)="expanded($event)"
                      [nzSearchValue]="OrganizesearchValue"
                    >
                    </nz-tree>
                  </div>
                </ng-container>
                <ng-container *ngIf="pageshow == '2'">
                  <!-- <div class="tree_top">
                                      <nz-input-group style="width: 200px;" [nzSuffix]="suffixIconSearch">
                                          <input class="put_search" type="text" nz-input placeholder="请输入关键词"
                                              [(ngModel)]="searchRenValue" />
                                      </nz-input-group>
                                  </div>
                                  <nz-tree #nzTreeComponentRenkou
                                      nzCheckable
                                      [nzData]="Factorslist" 
                                      [nzExpandAll]="false"
                                      [nzExpandedKeys]="expandedRenNodes"
                                      [nzSearchValue]="searchRenValue"
                                      (nzCheckBoxChange)="nzEventRen($event)"
                                      [nzCheckedKeys]="checkedKeysRenkou"
                                      >
                                  </nz-tree> -->
                  <nz-collapse style="margin: 10px 10px;">
                    <nz-collapse-panel
                      *ngFor="let item of Factorslist; let i = index"
                      [nzHeader]="item.name.zh_CN"
                      [nzActive]="item.active"
                      [nzExtra]="extraTpl"
                    >
                      <ng-template #extraTpl>
                        <nz-input-group
                          style="width: 200px;margin-top: -5px;"
                          [nzSuffix]="suffixIconSearch"
                        >
                          <input
                            type="text"
                            nz-input
                            placeholder="请输入关键词"
                            [(ngModel)]="item.searchValue"
                            (click)="factorSelectAll($event)"
                          />
                        </nz-input-group>
                      </ng-template>
                      <li *ngFor="let val of item.children; let j = index">
                        <nz-checkbox-wrapper
                          style="width: 100%;"
                          *ngIf="
                            !item.searchValue ||
                            val.name.zh_CN.indexOf(item.searchValue) !== -1
                          "
                        >
                          <div nz-row>
                            <div
                              nz-col
                              [ngClass]="
                                j == item.children.length - 1 ? '' : 'padd_bot'
                              "
                            >
                              <label
                                nz-checkbox
                                [nzValue]="val.name.zh_CN"
                                [(ngModel)]="val.checked"
                                (ngModelChange)="collapseChecked(i, j)"
                                [disabled]="val.disabled"
                              >
                                {{ val.name.zh_CN }}
                              </label>
                            </div>
                          </div>
                        </nz-checkbox-wrapper>
                        <ng-container
                          *ngFor="let data of val.children; let idx = index"
                        >
                          <div style="margin:0 0 10px 20px;">
                            <label
                              nz-checkbox
                              [nzValue]="data.name.zh_CN"
                              [(ngModel)]="data.checked"
                              (ngModelChange)="collapsemask(i, data.id)"
                            >
                              {{ data.name.zh_CN }}
                            </label>
                          </div>
                        </ng-container>
                      </li>
                    </nz-collapse-panel>
                  </nz-collapse>
                </ng-container>
              </li>
            </ul>
          </ng-container>
          <div *ngIf="Step == 2">
            <ul class="Pagebook">
              <li class="left_page scroll_page">
                <div
                  *ngFor="let item of ProjectIdItem; let i = index"
                  (click)="checkedItem(i)"
                  [ngClass]="pageItem == i ? 'pagesclass' : ''"
                >
                  <span
                    class="li_span_new"
                    nz-tooltip
                    [(nzTooltipTitle)]="item.name.zh_CN"
                    *ngIf="item.name.zh_CN.length >= 8"
                    >{{ item.name.zh_CN }}</span
                  >
                  <span
                    class="li_span_new"
                    *ngIf="item.name.zh_CN.length < 8"
                    >{{ item.name.zh_CN }}</span
                  >
                </div>
              </li>
              <li class="right_page">
                <ng-container *ngFor="let item of ProjectIdItem; let i = index">
                  <div *ngIf="pageItem == i">
                    <div *ngIf="item.child.length != 0">
                      <div class="tree_top">
                        <div style="margin-left: 0px;">
                          <label
                            nz-checkbox
                            [(ngModel)]="item.allChecked"
                            (ngModelChange)="updateAllChecked(i)"
                            [(nzIndeterminate)]="item.indeterminate"
                          >
                            全选
                          </label>
                        </div>
                        <nz-input-group
                          style="width: 200px;margin-right: 16px;"
                          [nzSuffix]="suffixIconSearch"
                        >
                          <input
                            type="text"
                            nz-input
                            placeholder="请输入关键词"
                            [(ngModel)]="OrganizesearchValue"
                          />
                        </nz-input-group>
                      </div>
                      <div *ngFor="let res of item.child; let j = index">
                        <nz-checkbox-wrapper
                          style="width: 100%;"
                          *ngIf="
                            !OrganizesearchValue ||
                            res.name.zh_CN.indexOf(OrganizesearchValue) !== -1
                          "
                        >
                          <div nz-row>
                            <div
                              nz-col
                              class="item_Question"
                              [ngClass]="j % 2 == 0 ? '' : 'bg_blue'"
                            >
                              <label
                                nz-checkbox
                                [nzValue]="res.questionId"
                                style="display: flex;align-items: center;"
                                [(ngModel)]="res.checked"
                                (ngModelChange)="updateSingleChecked(i)"
                              >
                                <div [innerHTML]="res.name.zh_CN | html"></div>
                                <div *ngIf="res.resultQuestion">
                                  {{ res.resultQuestion.name.zh_CN }}
                                </div>
                              </label>
                            </div>
                          </div>
                        </nz-checkbox-wrapper>
                      </div>
                    </div>
                    <div
                      *ngIf="item.child.length == 0"
                      style="display: flex;justify-content: center;align-items: center;height:350px;"
                    >
                      <nz-empty></nz-empty>
                    </div>
                  </div>
                </ng-container>
              </li>
            </ul>
          </div>
          <ng-template #suffixIconSearch>
            <img src="./assets/images/icon_search.png" />
          </ng-template>
        </div>
      </div>
      <div class="drawer-footer">
        <ng-container *ngIf="Step == 1">
          <button nz-button nzType="default" (click)="Returndefault()">
            恢复默认
          </button>
          <button nz-button nzType="primary" (click)="nextStep()">
            下一步
          </button>
        </ng-container>
        <ng-container *ngIf="Step == 2">
          <button nz-button nzType="default" (click)="ReturnStep()">
            上一步
          </button>
        </ng-container>
      </div>
    </nz-drawer>
    <!-- 产品模型 -->
    <nz-drawer
      [(nzVisible)]="isVisiblemodal"
      nzWrapClassName="round-right-drawer6-nofooter"
      nzTitle="产品模型"
      (nzOnClose)="handleCancelmodal()"
      [nzWidth]="1000"
    >
      <ul
        style="display: flex;justify-content: flex-end;align-items: center; margin-bottom: 16px;"
      >
        <li style="color: #17314C;font-size: 12px;margin-right: 20px;">
          建议：250px*250px以上的1:1尺寸，500K以内，PNG格式
        </li>
        <nz-upload
          [nzShowUploadList]="false"
          [nzBeforeUpload]="beforeUpload"
          [nzCustomRequest]="customReq"
          (nzChange)="handleChange($event)"
        >
          <li class="upload_img">上传专属模型图</li>
        </nz-upload>
      </ul>
      <div *ngIf="prismaData.factorname?.surveyStandardSagReportTemplate">
        <div
          style="display: flex;justify-content: center;align-items: center;width: 100%;height: calc(100vh - 177px);"
        >
          <div
            *ngIf="
              prismaData.factorname?.surveyStandardSagReportTemplate
                .modelType == 'DYNAMIC'
            "
          >
            <app-echarts-pie
              *ngIf="echartData.length != 0"
              [data]="echartData"
              [sunecharts]="sunecharts"
              [containerWidth]="'1000'"
              [containerHeight]="'750'"
            >
            </app-echarts-pie>
            <div *ngIf="echartData.length == 0">
              <nz-empty></nz-empty>
            </div>
          </div>
          <div
            *ngIf="
              prismaData.factorname?.surveyStandardSagReportTemplate
                .modelType == 'STATIC'
            "
          >
            <div
              *ngIf="
                prismaData.factorname?.surveyStandardSagReportTemplate
                  .modelImageUrl != ''
              "
            >
              <img
                [attr.src]="modelImageUrl"
                alt=""
                style="width: 100%;height: 100%;"
              />
            </div>
            <div
              *ngIf="
                prismaData.factorname?.surveyStandardSagReportTemplate
                  .modelImageUrl == ''
              "
            >
              <app-echarts-pie
                *ngIf="echartData.length != 0"
                [data]="echartData"
                [sunecharts]="sunecharts"
                [containerWidth]="'1000'"
                [containerHeight]="'600'"
              >
              </app-echarts-pie>
            </div>
          </div>
        </div>
      </div>
    </nz-drawer>
  </div>
  <div class="submit_xy">
    <div class="client-width center_menu">
      <div>
        <span style="color: #B2B8C2;"
          >K米将在报告生成时，自动从您的账户中扣除</span
        >
      </div>
      <ul class="menus_xy">
        <li
          class="menus_left"
          (click)="submitSave('SavePage')"
          nz-button
          [nzLoading]="isNzOkLoading"
          *ngIf="
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:SAVE'
            )
          "
        >
          保存
        </li>
        <li
          class="menus_right_new"
          (click)="submitPreview()"
          *ngIf="
            (projectType == 'ANNOUNCED' || !projectType) &&
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:RELEASE'
            )
          "
          nz-button
          [nzLoading]="isNzPRELoading"
        >
          预发布
        </li>
        <!--  -->
        <li
          class="menus_right"
          *ngIf="
            !isUpdateing &&
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:RELEASE'
            )
          "
          (click)="submitRelase()"
          nz-button
          [nzLoading]="isNzRELoading"
        >
          发布
        </li>
      </ul>
    </div>
  </div>
  <div class="nz_spin" *ngIf="isSpinning">
    <nz-spin nzSimple [nzSize]="'large'" [nzSpinning]="isSpinning"></nz-spin>
  </div>
  <!-- 人口标签 -->
  <nz-drawer
    class="nz_modal"
    [(nzVisible)]="addfactorshow"
    [nzWidth]="1000"
    nzWrapClassName="round-right-drawer6"
    [(nzBodyStyle)]="nzBodyStyle"
    [nzTitle]="'人口标签-' + analysisFactorTitle"
    (nzOnClose)="handleCancel()"
  >
    <app-factors
      *ngIf="addfactorshow"
      [factorlist]="prismaData.factorname"
      [shownumber]="shownumber"
      [surveyType]="prismaData.surveyType"
      [reportType]="reportType"
      [showAnalysisFactor]="showAnalysisFactor"
      [addfactorshow]="addfactorshow"
      [projectId]="projectId"
      (closemodal)="closemodal($event)"
      (cancelmodal)="handleCancel()"
      (loadDataMap)="loadDataMap($event)"
      (getdefaultlist)="getdefaultlist($event)"
    >
    </app-factors>
  </nz-drawer>
  <!-- 作答说明 -->
  <nz-drawer
    nzTitle="作答说明"
    [(nzVisible)]="visibleDesc"
    nzWrapClassName="round-right-drawer6"
    [nzWidth]="600"
    (nzOnClose)="cancelModalDesc()"
  >
    <ng-container *ngIf="visibleDesc">
      <app-i18n-select
        [active]="lan"
        (selectChange)="onSelectI18n($event)"
      ></app-i18n-select>
      <div style="min-height: 500px; margin-top: 16px;">
        <ng-container *ngFor="let item of i18n">
          <div [hidden]="lan !== item.value">
            <tinymce
              [config]="tinyconfig"
              id="formula-textareanew"
              *ngIf="visibleDesc"
              [(ngModel)]="prismaData.answerDescription[item.value]"
              delay="10"
            >
            </tinymce>
          </div>
        </ng-container>
        <!-- <div [hidden]="lan !== 'zh_CN'" >
                  <tinymce [config]="tinyconfig" id="formula-textareanew" *ngIf="visibleDesc" [(ngModel)]="prismaData.answerDescription.zh_CN" delay=10>
                  </tinymce>
              </div>
              <div [hidden]="lan !== 'en_US'">
                  <tinymce [config]="tinyconfig" id="formula-textareanew" *ngIf="visibleDesc" [(ngModel)]="prismaData.answerDescription.en_US" delay=10>
                  </tinymce>
              </div> -->
      </div>
      <div class="footer">
        <button
          type="button"
          class="ant-btn"
          style="margin-right: 8px;"
          (click)="setDescDefault()"
        >
          恢复默认
        </button>
        <button
          type="button"
          class="ant-btn ant-btn-primary"
          (click)="okModalDesc()"
        >
          确认
        </button>
      </div>
    </ng-container>
  </nz-drawer>
  <!-- 修改工具名称 -->
  <nz-drawer
    [(nzVisible)]="showname"
    [nzWidth]="440"
    nzWrapClassName="round-right-drawer6"
    nzTitle="修改工具名称"
    (nzOnClose)="namehandleCancel()"
  >
    <!-- <div style="margin-bottom: 16px;">
            <p style="margin-bottom: 8px;">活动工具名称(中)</p>
            <input nz-input placeholder="活动工具名称" [(ngModel)]="changeeditorName.zh_CN" />
        </div>
        <div>
            <p style="margin-bottom: 8px;">活动工具名称(eng)</p>
            <input nz-input placeholder="Activity tool name" [(ngModel)]="changeeditorName.en_US" />
        </div> -->

    <ng-container *ngFor="let item of i18n">
      <div style="margin-bottom: 16px;">
        <p style="margin-bottom: 8px;">{{ [item.name] }}</p>
        <input
          nz-input
          placeholder="活动工具名称"
          [(ngModel)]="changeeditorName[item.value]"
        />
      </div>
    </ng-container>

    <div class="footer">
      <button
        type="button"
        class="ant-btn ant-btn-primary"
        (click)="namehandleOk()"
      >
        确认
      </button>
    </div>
  </nz-drawer>
</div>

<div class="mock_div " *ngIf="showmock">
  <ul class="bg_ul" *ngIf="showmock"></ul>
  <ul class="img_ul" *ngIf="noviceGuidance">
    <li>
      <div style="position: relative;">
        <img src="assets/images/create_2.png" alt="" />
      </div>
      <div style="margin-top: 20px;cursor: pointer;" (click)="closed()">
        <img src="assets/images/dele_new.png" alt="" />
      </div>
    </li>
  </ul>
</div>
