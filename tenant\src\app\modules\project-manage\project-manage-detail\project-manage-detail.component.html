<nz-spin [nzSpinning]="isloading">
  <div class="detail-box">
    <!-- header -->
    <header>
      <!-- title -->
      <h1>
        活动详情
        <img
          style="cursor: pointer;"
          src="assets/images/shownew.png"
          (click)="getnewlead()"
          alt=""
        />
      </h1>
      <!-- 面包屑 -->
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </header>

    <!-- 活动信息1 -->
    <div class="title">
      <h2>
        <nz-tag [nzColor]="'blue'">ID：{{ projectData.code }}</nz-tag>
        <p>{{ projectData.name }}</p>
        <div [class]="'process process-' + statusClass">{{ statusName }}</div>
      </h2>
      <div class="icon-box">
        <!-- 删除 -->
        <a
          nz-popconfirm
          nzPopconfirmTitle="确定删除当前活动?"
          nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="deleteFacNameok()"
          href="javascript:void(0)"
          style="margin-right: 20px;"
          *knxFunctionPermission="'SAG:TENANT:PROJECT_MGT:DETAIL:DELETE'"
        >
          <div class="delete-icon"></div>
        </a>
        <!-- 编辑 -->
        <a
          href="javascript:void(0)"
          (click)="edit()"
          *knxFunctionPermission="'SAG:TENANT:PROJECT_MGT:DETAIL:EDIT'"
        >
          <div class="edit-icon"></div>
        </a>
      </div>
    </div>

    <!-- 活动信息2 -->
    <ul class="introduction">
      <li>
        <div class="name-box">活动状态</div>
        <div class="data-box dots-box">
          <p>
            <span class="dots">已完成</span>{{ projectData.completedCount }}
          </p>

          <p>
            <span class="dots dots-blue">进行中</span
            >{{ projectData.onGoingCount }}
          </p>
          <p>
            <span class="dots dots-red">未开始</span
            >{{ projectData.inCompletedCount }}
          </p>
          <a
            *ngIf="
              projectData.status == 'ANSWERING' &&
              knxFunctionPermissionService.has(
                'SAG:TENANT:PROJECT_MGT:DETAIL:STOP'
              )
            "
            (click)="deadline()"
          >
            截止活动
          </a>
          <a
            *ngIf="
              projectData.status != 'ANSWERING' &&
              projectData.status != 'PREVIEW'
            "
            (click)="reopen()"
          >
            重新开启
          </a>
          <!-- <span *ngIf="projectData.status != 'ANSWERING'"></span> -->
        </div>
        <div class="name-box">活动工具</div>
        <div class="data-box">
          <span
            class="questionnaires"
            nz-tooltip
            [nzTooltipTitle]="titleTemplate1"
          >
            <ng-container
              *ngFor="let item of projectData.questionnaires; let i = index"
            >
              {{ item.name.zh_CN }}
              <ng-container *ngIf="i != projectData.questionnaires.length - 1"
                >、</ng-container
              >
            </ng-container>
          </span>
          <ng-template #titleTemplate1>
            <ng-container
              *ngFor="let item of projectData.questionnaires; let i = index"
            >
              {{ item.name.zh_CN }}
              <ng-container *ngIf="i != projectData.questionnaires.length - 1"
                >、</ng-container
              >
            </ng-container>
          </ng-template>
          <div
            *ngIf="projectData.surveyType == 'ASSESSMENT'"
            class="type-evaluation"
          >
            测评
          </div>
          <div
            *ngIf="projectData.surveyType == 'EMPLOYEE_ENGAGEMENT'"
            class="type-evaluation type-research"
          >
            调研
          </div>
        </div>
      </li>
      <li>
        <div class="name-box">管理员</div>
        <div class="data-box">
          <span
            nz-tooltip
            [nzTooltipTitle]="projectData.creator"
            [ngClass]="
              projectData.surveyType == 'EMPLOYEE_ENGAGEMENT' ? 'ell' : ''
            "
            >创建人：{{ projectData.creator }}</span
          >
          <ng-container *ngIf="mainManagerPermission || subManagerPermission">
            <span *ngIf="mainManagerPermission">
              主管理员(
              <span
                style="color: #409EFF;"
                *ngIf="listMainManager.length === 0"
                >{{ listMainManager.length }}</span
              >
              <span
                style="color: #409EFF; cursor: pointer;"
                *ngIf="listMainManager.length !== 0"
                nz-tooltip
                [nzTooltipTitle]="MainManager"
                >{{ listMainManager.length }}</span
              >
              )
            </span>
            <span *ngIf="subManagerPermission">
              子管理员(
              <span
                style="color: #409EFF;"
                *ngIf="listSubMainManager.length === 0"
                >{{ listSubMainManager.length }}</span
              >
              <span
                style="color: #409EFF; cursor: pointer;"
                *ngIf="listSubMainManager.length !== 0"
                nz-tooltip
                [nzTooltipTitle]="SubMainManager"
                >{{ listSubMainManager.length }}</span
              >
              )
            </span>
            <ng-template #MainManager>
              <span *ngFor="let data of listMainManager; let index = index">
                {{ index !== 0 ? "，" : "" }} {{ data.userName }}
              </span>
            </ng-template>
            <ng-template #SubMainManager>
              <span *ngFor="let data of listSubMainManager; let index = index">
                {{ index !== 0 ? "，" : "" }} {{ data.name }}
              </span>
            </ng-template>
            <a *ngIf="mainManagerPermission" (click)="administratorModal()"
              >+主管理员</a
            >
            <a *ngIf="subManagerPermission" (click)="subAdministratorModal()"
              >+子管理员</a
            >
          </ng-container>
        </div>
        <div class="name-box">活动周期</div>
        <div class="data-box">
          <span>
            {{ projectData.startTime | date: "yyyy-MM-dd HH:mm" }}至{{
              projectData.endTime | date: "yyyy-MM-dd HH:mm"
            }}
            <span *ngIf="projectData.isClosingSoon" class="tips-box"
              >7天内到期活动</span
            >
          </span>
          <a
            *ngIf="
              (projectData.status == 'ANSWERING' ||
                projectData.status == 'PREVIEW') &&
              knxFunctionPermissionService.has(
                'TENANT:PROJECT_MGT:DETAIL:DELAY'
              )
            "
            (click)="delay()"
            >延期</a
          >
        </div>
      </li>
    </ul>

    <!-- 活动相关操作 -->
    <div class="tool-box">
      <div class="left">
        <div
          class="new-event"
          *ngIf="
            !is360Invite &&
            knxFunctionPermissionService.has(
              'SAG:TENANT:PROJECT_MGT:DETAIL:INVITE_ANSWER'
            )
          "
          (click)="invite()"
        >
          <img src="assets/images/event-management/home/<USER>" alt="" />
          邀请
        </div>
        <div
          class="new-event"
          *ngIf="
            is360Invite &&
            knxFunctionPermissionService.has(
              'SAG:TENANT:PROJECT_MGT:DETAIL:INVITE_ANSWER'
            )
          "
          nz-dropdown
          [nzDropdownMenu]="menuInvite"
        >
          <img src="assets/images/event-management/home/<USER>" alt="" />
          邀请
        </div>
        <nz-dropdown-menu #menuInvite="nzDropdownMenu">
          <ul nz-menu nzSelectable class="down-menu">
            <li nz-menu-item (click)="inviteEval()">
              邀请被评估人完善评价关系<span
                class="iconfont icon-icon_arrow_right"
              ></span>
            </li>
            <li nz-menu-item (click)="invite()">
              邀请所有人进行评价作答<span
                class="iconfont icon-icon_arrow_right"
              ></span>
            </li>
          </ul>
        </nz-dropdown-menu>
        <div
          class="new-event"
          *ngIf="transType === 3 && permissionService.isPermissionOrSag('SAG:TENANT:PROJECT_MGT:DETAIL:ANSWER_RATE')"
          (click)="fillInProgress()"
        >
          填答进度
        </div>
        <div class="new-event" *ngIf="transType === 2" (click)="toEvl()">
          评价关系
        </div>
        <div>
          <nz-input-group [nzSuffix]="suffixIconSearch">
            <input
              style="width: 190px; padding-left: 10px;"
              [(ngModel)]="searchField"
              (keydown.enter)="search()"
              type="text"
              nz-input
              placeholder="请输入关键词"
            />
          </nz-input-group>
          <ng-template #suffixIconSearch>
            <i nz-icon nzType="search"></i>
          </ng-template>
        </div>
      </div>
      <ul class="right">
        <ng-container *ngIf="transType === 2">
          <li
            class="btn"
            nz-popconfirm
            nzTitle="一键清除所有人员数据?"
            (nzOnConfirm)="clearPersonAll()"
          >
            <i class="iconfont icon-a-deltiny"></i>
            <span>人员清除</span>
          </li>
          <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
        </ng-container>
        <ng-container *ngIf="transType === 2">
          <li
            class="btn"
            nz-popconfirm
            nzTitle="是否一键删除?"
            (nzOnConfirm)="deleteAll()"
          >
            <i class="iconfont icon-icon_delete"></i>
            <span>一键删除</span>
          </li>
          <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
        </ng-container>
        <li class="btn" (click)="refresh()">
          <i class="iconfont icon-icon_refresh"></i><span>刷新</span>
        </li>
        <!-- 不为360 且有管理员权限才显示 -->
        <ng-container *ngIf="permissionService.isPermissionOrSag(
          'SAG:TENANT:PROJECT_MGT:DETAIL:IMPORT_LIST'
        )">
          <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
          <li class="btn">
            <nz-upload
              class="upload"
              [nzCustomRequest]="customReq"
              nzAccept=".xlsx,.xls"
              [nzFilter]=""
              [nzShowUploadList]="false"
            >
              <i class="iconfont icon-icon_import"></i><span>导入列表</span>
            </nz-upload>
          </li>
        </ng-container>
        <li
          class="divider"
          *knxFunctionPermission="'SAG:TENANT:PROJECT_MGT:DETAIL:EXPORT'"
        >
          <nz-divider nzType="vertical"></nz-divider>
        </li>
        <li
          class="btn"
          (click)="exportUsers()"
          *knxFunctionPermission="'SAG:TENANT:PROJECT_MGT:DETAIL:EXPORT'"
        >
          <i class="iconfont icon-icon_export"></i><span>导出列表</span>
        </li>
        <!-- </ng-container> -->
        <!-- <ng-container *ngIf="transType === 3">
          <li class="divider" *knxFunctionPermission="'SAG:TENANT:PROJECT_MGT:DETAIL:ANSWER_RATE'"><nz-divider nzType="vertical"></nz-divider></li>
          <li class="btn" (click)="exportOrgDetail()" *knxFunctionPermission="'SAG:TENANT:PROJECT_MGT:DETAIL:ANSWER_RATE'"><i class="iconfont icon-icon_export"></i><span>填答进度</span></li>
        </ng-container> -->
        <ng-container *ngIf="permission">
          <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
          <li class="btn" nz-tooltip nzTooltipTitle="导入原始填答">
            <nz-upload
              class="upload"
              [nzCustomRequest]="customReqOriginal"
              nzAccept=".xlsx,.xls"
              [nzFilter]=""
              [nzShowUploadList]="false"
            >
              <i class="iconfont icon-icon_import"></i><span>导入</span>
            </nz-upload>
          </li>
        </ng-container>
        <ng-container *ngIf="permission">
          <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
          <li
            class="btn"
            (click)="exportOriginal()"
            nz-tooltip
            nzTooltipTitle="导出原始填答"
          >
            <i class="iconfont icon-icon_export"></i><span>导出</span>
          </li>
        </ng-container>
      </ul>
    </div>

    <!-- table -->
    <nz-spin [nzSpinning]="tableIsSpinning">
      <!-- 其他类型项目人员列表 -->
      <nz-table
        #filterTable
        [nzData]="tableList"
        *ngIf="transType === 1 || (transType === 3 && !isShowOrg)"
        [nzFrontPagination]="false"
      >
        <thead (nzSortChange)="sort($event)" nzSingleSort>
          <tr>
            <th nzWidth="60px">序号</th>
            <th nzWidth="200px" nzShowSort nzSortKey="first_Name">测评者</th>
            <th nzWidth="180px" nzShowSort nzSortKey="startTime">邀请时间</th>
            <th nzWidth="180px" nzShowSort nzSortKey="endTime">完成时间</th>
            <th nzWidth="120px">完成进度</th>
            <th nzWidth="80px">
              <a
                class="table-change-box"
                nz-dropdown
                [nzDropdownMenu]="menu"
                nzTrigger="click"
              >
                {{ thName }}
                <i nz-icon nzType="down"></i>
              </a>
            </th>
            <th nzWidth="80px">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of filterTable.data; let index = index">
            <td>{{ index + 1 }}</td>
            <td>
              <div class="ell" nz-tooltip [nzTooltipTitle]="data.firstName">
                {{ data.firstName }}
              </div>
            </td>
            <td>{{ data.createTime | date: "yyyy-MM-dd HH:mm" }}</td>
            <td>{{ data.endTime | date: "yyyy-MM-dd HH:mm" }}</td>
            <td>
              <span
                class="remind"
                nz-popover
                [nzPopoverContent]="contentTemplate"
                nzPopoverPlacement="rightTop"
                *ngIf="data.answerStatus != 'ANSWERED' ; else elsetemp"
              >
                提醒
              </span>
              <ng-template #contentTemplate >
                <ul class="bubble-card" *ngIf="isDisplayMail">
                  <li
                    (click)="
                      sendEmailForRemind(data.projectId, data.email, data)
                    "
                  >
                    <img
                      src="assets/images/event-management/home/<USER>"
                      alt=""
                    /><span>邮箱</span>
                  </li>
                  <!-- <li (click)="sendEmailForRemind(projectData.projectId, projectData.email,projectData)"><img src="assets/images/event-management/home/<USER>" alt=""><span>短信</span></li>
                  <li (click)="sendEmailForRemind(projectData.projectId, projectData.email,projectData)"><img src="assets/images/event-management/home/<USER>" alt=""><span>企业微信</span></li> -->
                </ul>
                <ul class="bubble-card" *ngIf="!isDisplayMail">
                  <li>
                    <img
                      src="assets/images/event-management/home/<USER>"
                      alt=""
                    /><span>暂未开通邮件服务</span>
                  </li>
                </ul>
              </ng-template>
              
              <ng-template #elsetemp>/</ng-template>
              
            </td>
            <td>
              <ng-container
                *ngIf="
                  data.answerStatus == 'ANSWERED' &&
                    transType === 1 &&
                    statusName != '预发布';
                  else elsetem
                "
              >
                <a (click)="gotoReport(data.id)"> 查看报告</a>
              </ng-container>
              <ng-template #elsetem>{{
                status[data.answerStatus]
              }}</ng-template>
            </td>
            <td>
              <div style="display: flex;align-items: center;">
                <i
                  class="iconfont icon-icon_delete"
                  title="删除"
                  nz-popconfirm
                  [nzPopconfirmTitle]="IsDelete"
                  nzOkText="确认"
                  nzPopconfirmPlacement="left"
                  (nzOnConfirm)="deleteById(data.id)"
                ></i>
                <ng-container *ngIf="permission">
                  <i
                    *ngIf="data.answerStatus !== 'NOT_INVITED'; else IconTem"
                    class="iconfont icon-icon-"
                    nzOkText="确认"
                    title="清除"
                    nz-popconfirm
                    [nzPopconfirmTitle]="IsRemove"
                    nzPopconfirmPlacement="left"
                    (nzOnConfirm)="clearHistory(data.id)"
                  ></i>
                  <ng-template #IconTem>
                    <i
                      class="iconfont icon-icon-"
                      style="cursor:not-allowed;"
                      title="清除"
                    ></i>
                  </ng-template>
                </ng-container>
                <ng-template #IsDelete>
                  <img
                    nz-icon
                    src="assets/images/event-management/home/<USER>"
                    class="icon"
                  />
                  <div class="popconfirm_div_top">
                    删除后不可修复，确定吗？
                  </div>
                  <!-- <div class="popconfirm_div_bottom">数据删除后将不再展示</div> -->
                </ng-template>
                <ng-template #IsRemove>
                  <img
                    nz-icon
                    src="assets/images/event-management/home/<USER>"
                    class="icon"
                  />
                  <div class="popconfirm_div_top">
                    清除后不可修复，确定吗？
                  </div>
                  <!-- <div class="popconfirm_div_bottom">清除后不可修复</div> -->
                </ng-template>
              </div>
            </td>
          </tr>
        </tbody>
      </nz-table>

      <!-- 360类型项目人员列表 -->
      <nz-table
        #dataTable
        *ngIf="transType === 2"
        [nzData]="tableListOf360"
        [nzPageSize]="10"
        [nzFrontPagination]="false"
      >
        <thead (nzSortChange)="sortChange($event)" nzSingleSort>
          <tr>
            <th nzWidth="50px"></th>
            <th nzWidth="100px">序号</th>
            <th nzWidth="200px" nzShowSort nzSortKey="name">邀请者</th>
            <th nzWidth="300px" nzShowSort nzSortKey="create_Time">创建时间</th>
            <th nzWidth="180px">完成进度</th>
            <th nzWidth="150px">
              全部
              <!-- <a class="table-change-box" nz-dropdown [nzDropdownMenu]="menu" nzTrigger="click">
                {{thName}}
                <i nz-icon nzType="down"></i>
              </a> -->
            </th>
            <th nzWidth="200px">操作</th>
          </tr>
        </thead>

        <tbody>
          <ng-template
            ngFor
            let-data
            [ngForOf]="dataTable.data"
            let-index="index"
          >
            <!-- 父 table -->
            <tr>
              <td
                [nzShowExpand]="data.surveyPeople.length > 0"
                [(nzExpand)]="data.expand"
                (nzExpandChange)="
                  nzExpandChange(data.expand, data.surveyInvestigator.id)
                "
              ></td>
              <td>{{ index + 1 }}</td>
              <td>
                <div
                  class="ell"
                  nz-tooltip
                  [nzTooltipTitle]="data.surveyInvestigator.name"
                >
                  {{ data.surveyInvestigator.name }}
                </div>
              </td>
              <td>
                {{
                  data.surveyInvestigator.createTime | date: "yyyy-MM-dd HH:mm"
                }}
              </td>
              <td>{{ data.progress }}</td>
              <td>
                <ng-container *ngIf="data.isComplete; else completion">
                  <a
                    *ngIf="statusName != '预发布'"
                    class="action view-report"
                    (click)="gotoReport(data.surveyInvestigator.id, '360')"
                    >查看报告</a
                  >
                  <p *ngIf="statusName == '预发布'">已完成</p>
                </ng-container>
                <ng-template #completion>
                  未完成
                </ng-template>
              </td>
              <td>
                <div class="action-box">
                  <i
                    class="iconfont icon-icon_delete"
                    title="删除"
                    nz-popconfirm
                    [nzPopconfirmTitle]="IsDelete"
                    nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="deleteListById(data.surveyInvestigator.id)"
                  ></i>
                  <i
                    class="iconfont icon-icon_edit"
                    title="编辑"
                    (click)="geteditors(data)"
                  ></i>
                  <ng-template #IsDelete>
                    <img
                      nz-icon
                      src="assets/images/event-management/home/<USER>"
                      class="icon"
                    />
                    <div class="popconfirm_div_top">
                      删除后不可修复，确定吗？
                    </div>
                    <!-- <div class="popconfirm_div_bottom">数据删除后将不再展示</div> -->
                  </ng-template>
                </div>
              </td>
            </tr>

            <!-- 嵌套的子 table -->
            <tr [nzExpand]="data.expand">
              <td colspan="7">
                <nz-table
                  [nzShowPagination]="false"
                  [nzFrontPagination]="false"
                  #subTable
                  [nzData]="data.surveyPeople"
                >
                  <tbody>
                    <tr *ngFor="let sub of subTable.data">
                      <td style="width: 50px;"></td>
                      <td style="width: 100px;">
                        <div class="invite-status" [ngClass]="sub.status">
                          {{ inviteStatus[sub.status] }}
                        </div>
                      </td>
                      <td style="width: 200px;">
                        <div
                          class="ell"
                          nz-tooltip
                          [nzTooltipTitle]="sub.firstName"
                        >
                          {{ sub.firstName }}
                        </div>
                      </td>
                      <td style="width: 300px;">
                        {{ sub.createTime | date: "yyyy-MM-dd HH:mm" }}
                      </td>
                      <td style="width: 180px;">
                        <!-- 根据评测者的邀请状态来决定如何跟进该评测者 -->
                        <ng-container
                          *ngIf="sub.answerStatus !== 'ANSWERED'; else unremind"
                          [ngSwitch]="sub.status"
                        >
                          <div
                            class="action invite"
                            *ngSwitchCase="'NEW'"
                            (click)="
                              goAndInvite360(
                                sub.projectId,
                                2,
                                sub.email,
                                sub.id
                              )
                            "
                          >
                            邀请
                          </div>

                          <div *ngSwitchCase="'SENT'" class="action alert">
                            <span
                              class="remind"
                              nz-popover
                              [nzPopoverContent]="contentTemplate360"
                              nzPopoverPlacement="rightTop"
                              *ngIf="
                                data.answerStatus != 'ANSWERED';
                                else elsetemp
                              "
                            >
                              提醒
                            </span>
                            <ng-template #contentTemplate360>
                              <ul class="bubble-card">
                                <li
                                  (click)="
                                    sendEmailForRemind(
                                      sub.projectId,
                                      sub.email,
                                      sub
                                    )
                                  "
                                >
                                  <img
                                    src="assets/images/event-management/home/<USER>"
                                    alt=""
                                  /><span>邮箱</span>
                                </li>
                                <!-- <li (click)="sendEmailForRemind(sub.projectId, sub.email)"><img src="assets/images/event-management/home/<USER>" alt=""><span>短信</span></li>
                                  <li (click)="sendEmailForRemind(sub.projectId, sub.email)"><img src="assets/images/event-management/home/<USER>" alt=""><span>企业微信</span></li> -->
                              </ul>
                            </ng-template>
                          </div>
                          <div
                            class="action re-invite"
                            *ngSwitchCase="'FAIL'"
                            (click)="
                              goInvite360(
                                data.surveyInvestigator.id,
                                sub.id,
                                data.surveyInvestigator.projectId,
                                'reSendEmail',
                                2
                              )
                            "
                          >
                            重新邀请
                          </div>
                          <div *ngSwitchDefault class="action">无</div>
                        </ng-container>
                        <ng-template #unremind>/</ng-template>
                      </td>
                      <td style="width: 150px;" class="answer-td">
                        {{
                          sub.answerStatus === "ANSWERED" ? "已完成" : 
                          sub.answerStatus === "ANSWERING" ? "进行中" : "未开始"
                        }} 
                      </td>
                      <td style="width: 200px;">
                        <div style="display: flex;align-items: center;">
                          <i
                            *ngIf="data.surveyInvestigator.personId === sub.id"
                            style="cursor: not-allowed; color: #253238;"
                            class="iconfont icon-icon_delete"
                            title="删除"
                          ></i>
                          <i
                            *ngIf="data.surveyInvestigator.personId !== sub.id"
                            nz-popconfirm
                            [nzPopconfirmTitle]="IsDelete"
                            nzPopconfirmPlacement="bottom"
                            class="iconfont icon-icon_delete"
                            title="删除"
                            (nzOnConfirm)="
                              deleteListById(data.surveyInvestigator.id, sub.id)
                            "
                          ></i>
                          <ng-container *ngIf="permission">
                            <i
                              *ngIf="
                                sub.answerStatus !== 'NOT_INVITED';
                                else IconTem
                              "
                              class="iconfont icon-icon-"
                              title="清除"
                              nz-popconfirm
                              [nzPopconfirmTitle]="IsRemove"
                              nzPopconfirmPlacement="bottom"
                              (nzOnConfirm)="
                                clearHistory(sub.id, data.surveyInvestigator.id)
                              "
                            ></i>
                            <ng-template #IconTem>
                              <i
                                class="iconfont icon-icon-"
                                style="cursor:not-allowed;"
                                title="清除"
                              ></i>
                            </ng-template>
                          </ng-container>
                          <ng-template #IsDelete>
                            <img
                              nz-icon
                              src="assets/images/event-management/home/<USER>"
                              class="icon"
                            />
                            <div class="popconfirm_div_top">
                              删除后不可修复，确定吗？
                            </div>
                            <!-- <div class="popconfirm_div_bottom">数据删除后将不再展示</div> -->
                          </ng-template>
                          <ng-template #IsRemove
                            ><img
                              nz-icon
                              src="assets/images/event-management/home/<USER>"
                              class="icon"
                            />
                            <div class="popconfirm_div_top">
                              清除后不可修复，确定吗？
                            </div>
                            <!-- <div class="popconfirm_div_bottom">清除后不可修复</div> -->
                          </ng-template>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </nz-table>
              </td>
            </tr>
          </ng-template>
        </tbody>
      </nz-table>

      <!-- survey 组织列表 -->
      <nz-table
        #filterTable
        *ngIf="transType === 3 && isShowOrg"
        [nzData]="tableListOfresearch"
        [nzFrontPagination]="false"
      >
        <thead (nzSortChange)="sort($event)" nzSingleSort>
          <tr>
            <th nzWidth="10%">序号</th>
            <th nzWidth="16%">组织</th>
            <th nzWidth="20%">开始时间</th>
            <th nzWidth="20%">应答数</th>
            <th nzWidth="6%">应答率</th>
            <th nzWidth="8%">有效应答率</th>
            <th nzWidth="10%">&nbsp;&nbsp;操作</th>
          </tr>
        </thead>

        <tbody>
          <tr *ngFor="let data of filterTable.data; let ind = index">
            <td>{{ (page.current - 1) * page.size + ind + 1 }}</td>
            <td>
              <div class="ell" nz-tooltip [nzTooltipTitle]="data.name?.zh_CN">
                {{ data.name?.zh_CN }}
              </div>
            </td>
            <td>{{ data.createTime | date: "yyyy-MM-dd HH:mm" }}</td>
            <td>
              {{
                data.completedCount +
                  (data.inviteCount ? "/" + data.inviteCount : "")
              }}
            </td>
            <td>{{ data.finishAnswerRate }}</td>
            <td>{{ data.validAnswerRate }}</td>
            <td>
              <!-- <button nz-button nzType="link" nzSize="small" (click)="openPersonList(data.id, data.name?.zh_CN)">查看</button> -->
            </td>
          </tr>
        </tbody>
      </nz-table>
    </nz-spin>

    <!-- 分页 -->
    <footer
      *ngIf="
        tableList.length !== 0 ||
        tableListOf360.length !== 0 ||
        tableListOfresearch.length !== 0
      "
    >
      <nz-pagination
        [nzPageIndex]="page.current"
        [nzTotal]="page.total"
        nzShowSizeChanger
        [nzPageSizeOptions]="[1, 5, 6, 10, 20, 30, 40]"
        [nzPageSize]="page.size"
        (nzPageIndexChange)="pageIndexChange($event)"
        (nzPageSizeChange)="pageSizeChange($event)"
      ></nz-pagination>
    </footer>

    <!-- 操作指南 -->
    <div class="mock_div" *ngIf="showmock">
      <div class="bg_ul" *ngIf="showmock"></div>
      <ul class="img_ul" *ngIf="setp1">
        <img src="./assets/images/project.png" />
        <div>
          <p>点击这里，发送邮件、短信、和二维码邀请</p>
          <div class="btn_div">
            <div class="left_d" (click)="jumprun()">跳过</div>
            <div class="right_d" (click)="next1()">下一步</div>
          </div>
        </div>
      </ul>
      <ul class="img_ul_2" *ngIf="setp2">
        <img src="./assets/images/project_2.png" />
        <div>
          <p>点击这里，导出填答进度表</p>
          <div class="btn_div">
            <div class="left_d" (click)="jumprun()">跳过</div>
            <div class="right_d" (click)="next2()">下一步</div>
          </div>
        </div>
      </ul>
      <ul class="img_ul_3" *ngIf="setp3">
        <img src="./assets/images/project_3.png" />
        <div style="display: flex;justify-content: center;">
          <div>
            <p>查看并追踪本次活动的填答进度</p>
            <div class="btn_div">
              <div class="left_d" (click)="jumprun()">跳过</div>
              <div class="right_d" (click)="next3()">我知道了</div>
            </div>
          </div>
        </div>
      </ul>
    </div>
  </div>
</nz-spin>

<nz-dropdown-menu #menu="nzDropdownMenu">
  <ul nz-menu nzSelectable>
    <li nz-menu-item (click)="filter('', '全部')">全部</li>
    <li nz-menu-item (click)="filter('ANNOUNCED', '未开始')">未开始</li>
    <li nz-menu-item (click)="filter('ANSWERING', '进行中')">进行中</li>
    <li nz-menu-item (click)="filter('ANSWERED', '已完成')">已完成</li>
  </ul>
</nz-dropdown-menu>
