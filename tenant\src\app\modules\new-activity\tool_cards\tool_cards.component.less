.tool_cards {
  margin-top: 15px;

  .after_3 {
    &:after {
      content: "";
      width: 30%;
      height: 0;
    }

    li {
      width: 30%;
    }
  }

  .after_2 {
    &:after {
      content: "";
      width: 48%;
      height: 0;
    }

    li {
      width: 48%;
    }
  }

  .cards_ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    li {
      min-height: 150px;
      padding-bottom: 5px;
      background-color: #fff;
      border: 1px solid #efefef;
      margin-top: 15px;
      border-radius: 8px;
      overflow: hidden;

      .title_top {
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        color: #fff;
        // cursor: pointer;
        position: relative;

        i {
          background: url("../../../../assets/images/circle.png") no-repeat center;
          width: 21px;
          height: 21px;
          border-radius: 50%;
          background: #fff;
        }

        .title_tips {
          background: url("../../../../assets/images/v_tips.png") no-repeat center;
          position: absolute;
          top: -4px;
          width: 68px;
          text-align: center;
          padding: 0 8px;
          line-height: 30px;
          height: 30px;
          right: 0;
          font-size: 12px;
        }
      }
    }
  }
}

.tips_w {
  margin-top: 20px;
}

.tools_use_1 {
  // flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .use_d {
    margin: 5px 5px;
    padding: 2px 20px;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    color: #aaaaaa;
    border-radius: 30px;
    cursor: pointer;
  }

  .use_c {
    color: #409eff !important;
    background-color: #ecf5ff !important;
    border: 1px solid #40a9ff !important;
  }
}

.tools_use {
  margin-top: 10px;
  flex: 1;
  display: flex;
  // align-items: center;
  flex-wrap: wrap;

  .tags {
    height: 25px;
    line-height: 25px;
    margin: 2px;
  }

  .use_d {
    margin: 5px 5px;
    padding: 0px 10px;
    background-color: #fafafa;
    color: #aaaaaa;
    border-radius: 30px;
    line-height: 30px;
    height: 30px;
    cursor: pointer;
  }

  .use_c {
    color: #409eff !important;
    background-color: #ecf5ff !important;
  }
}

.line_throw {
  width: 113%;
  position: absolute;
  top: 15px;
  left: -5px;
}

.un_choose {
  cursor: not-allowed !important;
}