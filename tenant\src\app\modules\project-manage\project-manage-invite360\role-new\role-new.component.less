.flex {
  display: flex;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.title {
  font-size: 14px;
  font-weight: 500;
  color: #17314C;
  line-height: 20px;
  margin-bottom: 20px;
}

.link-del {
  font-size: 14px;
  font-weight: 400;
  color: #F19672;
}

.item {
  margin-bottom: 20px;
}

.mr20 {
  margin-right: 20px;
}

.text-left {
  text-align: left;
}

.add-btn {
  width: 100px;
  height: 54px;
  border: none;
  background-color: #fff;
  color: #419EFF;
  font-size: 14px;
  font-weight: 500;
  border-radius: 0;
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}