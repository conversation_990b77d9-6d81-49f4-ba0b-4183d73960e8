<ng-container *ngIf="oldDemoshow === 1">
  <div
    *ngIf="nametip == '新增'"
    style="position: relative;display: flex;justify-content: flex-end;top: -70px;right: 60px;"
  >
    <img src="./assets/images/addfactor.png" alt="" />
    <div class="word_sel" (click)="addNewlist()">
      新增题目
    </div>
  </div>

  <div>
    <div class="auto_div">
      <ng-container
        *ngFor="let questionData of batchquestionData; let i = index"
      >
        <div
          style="border: 1px solid #eee;padding: 10px 0 10px 20px;border-radius: 10px;"
          [ngClass]="i != 0 ? 'top_div' : ''"
        >
          <div
            style="display: flex;justify-content: space-between;padding-right: 20px;"
          >
            <nz-radio-group
              [(ngModel)]="questionData.isRequire"
              (ngModelChange)="ngquireChange(questionData)"
            >
              <label nz-radio [nzValue]="true">必填</label>
              <label nz-radio [nzValue]="false">选填</label>
            </nz-radio-group>

            <i
              nz-icon
              nzType="delete"
              *ngIf="batchquestionData?.length > 1"
              style="font-size: 20px;cursor: pointer;"
              nzTheme="twotone"
              (click)="deletelist(i)"
            ></i>
          </div>
          <form nz-form [formGroup]="questionData.validateForm">
            <div class="question">
              <div class="list1">
                <div class="title">题目编码</div>
                <div>
                  <nz-form-item nz-row>
                    <nz-form-control nz-col [nzSpan]="12">
                      <input
                        nz-input
                        placeholder="请输入题目编码"
                        name="code"
                        [(ngModel)]="questionData.code"
                        formControlName="code"
                      />
                    </nz-form-control>
                  </nz-form-item>
                </div>
              </div>
            </div>
            <div class="question">
              <div class="list1">
                <div class="title">
                  题干
                  <span
                    class="add-btn"
                    *ngIf="permission && !!id && !standard_360"
                    (click)="toRevision()"
                    ><img src="./assets/images/vew_editor.png" alt="" />
                    题本修订
                  </span>
                </div>
                <div>
                  <nz-form-item nz-row>
                    <nz-form-control nz-col [nzSpan]="12">
                      <input
                        nz-input
                        placeholder="请输入中文题干内容"
                        name="name"
                        [(ngModel)]="questionData.name"
                        formControlName="name"
                      />
                    </nz-form-control>
                    <nz-form-control nz-col [nzSpan]="11" [nzOffset]="1">
                      <input
                        nz-input
                        placeholder="请输入英文题干内容"
                        name="name"
                        [(ngModel)]="questionData.nameEn"
                        formControlName="nameEn"
                      />
                    </nz-form-control>
                  </nz-form-item>
                </div>
              </div>
              <div class="list2">
                <div class="title">题型</div>
                <div>
                  <nz-form-item>
                    <nz-form-control>
                      <nz-select
                        class="select"
                        name="type"
                        formControlName="type"
                        nzAllowClear
                        nzPlaceHolder="Choose"
                        [(ngModel)]="questionData.type"
                        (ngModelChange)="ngModelChange($event)"
                      >
                        <nz-option
                          *ngFor="let item of questionTypeList"
                          [nzValue]="item.id"
                          [nzLabel]="item.name.zh_CN"
                          required
                        >
                        </nz-option>
                      </nz-select>
                    </nz-form-control>
                  </nz-form-item>
                </div>
              </div>
            </div>

            <div class="question">
              <div class="list1">
                <div class="title">
                  维度
                  <span class="add-btn" (click)="ManageOption()"
                    ><img src="./assets/images/vew_editor.png" alt="" />
                    维度管理</span
                  >
                </div>
                <nz-form-item
                  [nzType]="'flex'"
                  [nzJustify]="'space-between'"
                  *ngIf="isedited"
                >
                  <nz-form-control
                    [nzSpan]="
                      standardReportType !== 'BLANK_CUSTOM' &&
                      currentReportType != 'BLANK_CUSTOM'
                        ? 7
                        : 12
                    "
                  >
                    <nz-select
                      class="select"
                      name="parentCustomDimension"
                      nzPlaceHolder="一级维度"
                      formControlName="parentCustomDimension"
                      nzAllowClear
                      [(ngModel)]="questionData.parentCustomDimension"
                      nzShowSearch
                      [nzFilterOption]="filterOption"
                    >
                      <nz-option
                        *ngFor="let item of ClassAfactor"
                        [nzValue]="item.code"
                        [nzLabel]="item.name.zh_CN"
                        required
                      >
                      </nz-option>
                    </nz-select>
                  </nz-form-control>
                  <nz-form-control
                    [nzSpan]="
                      standardReportType !== 'BLANK_CUSTOM' &&
                      currentReportType != 'BLANK_CUSTOM'
                        ? 7
                        : 11
                    "
                  >
                    <nz-select
                      class="select"
                      name="customDimension"
                      formControlName="customDimension"
                      nzAllowClear
                      nzPlaceHolder="二级维度"
                      [(ngModel)]="questionData.customDimension"
                      nzShowSearch
                      [nzFilterOption]="filterOption"
                    >
                      <nz-option
                        *ngFor="let item of ClassBfactor"
                        [nzValue]="item.code"
                        [nzLabel]="item.name.zh_CN"
                        required
                      >
                      </nz-option>
                    </nz-select>
                  </nz-form-control>
                  <ng-container
                    *ngIf="
                      standardReportType !== 'BLANK_CUSTOM' &&
                      currentReportType != 'BLANK_CUSTOM'
                    "
                  >
                    <nz-form-control [nzSpan]="7">
                      <nz-select
                        class="select"
                        name="childCustomDimension"
                        formControlName="childCustomDimension"
                        nzAllowClear
                        nzPlaceHolder="三级维度"
                        [(ngModel)]="questionData.childCustomDimension"
                        nzShowSearch
                        [nzFilterOption]="filterOption"
                      >
                        <nz-option
                          *ngFor="let item of ClassCfactor"
                          [nzValue]="item.code"
                          [nzLabel]="item.name.zh_CN"
                          required
                        >
                        </nz-option>
                      </nz-select>
                    </nz-form-control>
                  </ng-container>
                </nz-form-item>
                <nz-form-item
                  [nzType]="'flex'"
                  [nzJustify]="'space-between'"
                  *ngIf="!isedited"
                >
                  <nz-form-control [nzSpan]="12">
                    <nz-select
                      class="select"
                      name="parentCustomDimension"
                      nzPlaceHolder="一级维度"
                      formControlName="parentCustomDimension"
                      nzAllowClear
                      [(ngModel)]="questionData.parentCustomDimension"
                      nzShowSearch
                      [nzFilterOption]="filterOption"
                    >
                      <nz-option
                        *ngFor="let item of ClassAfactor"
                        [nzValue]="item.code"
                        [nzLabel]="item.name.zh_CN"
                        required
                      >
                      </nz-option>
                    </nz-select>
                  </nz-form-control>
                  <nz-form-control [nzSpan]="11">
                    <nz-select
                      class="select"
                      name="customDimension"
                      formControlName="customDimension"
                      nzAllowClear
                      nzPlaceHolder="二级维度"
                      [(ngModel)]="questionData.customDimension"
                      nzShowSearch
                      [nzFilterOption]="filterOption"
                    >
                      <nz-option
                        *ngFor="let item of ClassBfactor"
                        [nzValue]="item.code"
                        [nzLabel]="item.name.zh_CN"
                        required
                      >
                      </nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <ng-container
                *ngIf="
                  standardReportType !== 'BLANK_CUSTOM' &&
                  currentReportType != 'BLANK_CUSTOM'
                "
              >
                <div
                  class="list2"
                  [hidden]="
                    questionData.type === 'ESSAY_QUESTION' ||
                    questionData.type === 'MULTIPLE_CHOICE_ESSAY_QUESTION'
                  "
                >
                  <div class="title">比重</div>
                  <div>
                    <nz-form-item>
                      <nz-form-control>
                        <input
                          type="number"
                          nz-input
                          placeholder="请输入比重"
                          name="proportion"
                          formControlName="proportion"
                          [(ngModel)]="questionData.proportion"
                          required
                        />
                      </nz-form-control>
                    </nz-form-item>
                  </div>
                </div>
              </ng-container>
              <!-- <div class="list2" *ngIf="questionData.type === 'MULTIPLE_CHOICE_ESSAY_QUESTION'">
                <div class="title">计算</div>
              </div> 后期用到，暂时隐藏-->
            </div>
          </form>

          <div class="question" *ngIf="questionData.type !== 'ESSAY_QUESTION'">
            <div class="list1">
              <div class="title">
                选项
                <ng-container
                  *ngIf="questionData.type == 'MULTIPLE_CHOICE_ESSAY_QUESTION'"
                >
                  <span style="margin-left: 40px;">多选个数</span>
                  <nz-input-number
                    style="margin-left: 10px;width: 60px;"
                    [disabled]="!questionData.isRequire"
                    [(ngModel)]="questionData.multiSelectMin"
                    [nzStep]="1"
                    [nzMin]="1"
                  ></nz-input-number>
                  -
                  <nz-input-number
                    [(ngModel)]="questionData.multiSelectMax"
                    style="width: 60px;"
                    [nzMin]="
                      questionData.multiSelectMin
                        ? questionData.multiSelectMin
                        : 1
                    "
                    [nzStep]="1"
                  ></nz-input-number>
                </ng-container>
                <span class="add-btn" (click)="onAddOption(i)"
                  ><img src="./assets/images/add.png" alt="" /> 添加选项</span
                >
              </div>
            </div>
            <div class="list2" *ngIf="questionData.optionList.length > 0">
              <div class="title">
                计算
                <label
                  nz-checkbox
                  [(ngModel)]="questionData.totalScoreStatus"
                  *ngIf="questionData.type == 'PROPORTION_MULTIPLE'"
                  >设置总分</label
                >
              </div>
            </div>
          </div>
          <ng-container *ngIf="questionData.type !== 'ESSAY_QUESTION'">
            <div dragula="VAMPIRES" [(dragulaModel)]="questionData.optionList">
              <div
                class="question"
                *ngFor="let option of questionData.optionList; let j = index"
                style="margin: 0;"
              >
                <div class="list1" nz-row>
                  <div class="option" nz-col nzSpan="12">
                    <nz-input-group
                      class="option-input"
                      [nzPrefix]="prefixTemplateUser"
                    >
                      <input
                        class="option-input"
                        nz-input
                        placeholder="请输入选项中文内容"
                        [(ngModel)]="option.name.zh_CN"
                        [placeholder]="'请输入选项中文'"
                      />
                    </nz-input-group>
                    <ng-template #prefixTemplateUser>
                      <span class="option-index">{{ j + 1 }}</span>
                    </ng-template>
                  </div>
                  <div class="option" nz-col nzSpan="12">
                    <input
                      class="option-input"
                      nz-input
                      placeholder="请输入选项英文内容"
                      [(ngModel)]="option.name.en_US"
                      [placeholder]="'请输入选项英文'"
                    />
                    <span
                      class="delete-btn"
                      style="margin-left: 5px;"
                      (click)="onDeleteOption(i, j)"
                      >-</span
                    >
                  </div>
                </div>
                <div class="list2">
                  <ng-container
                    *ngIf="
                      questionData.type != 'PROPORTION' &&
                      questionData.type != 'PROPORTION_MULTIPLE' &&
                      questionData.type != 'MULTIPLE_CHOICE_ESSAY_QUESTION'
                    "
                  >
                    <div class="option">
                      <nz-select
                        class="option-select"
                        [(ngModel)]="option.isScoring"
                        nzAllowClear
                        nzPlaceHolder="Choose"
                      >
                        <nz-option [nzValue]="true" nzLabel="计分"></nz-option>
                        <nz-option
                          [nzValue]="false"
                          nzLabel="不计分"
                        ></nz-option>
                      </nz-select>
                      <nz-input-number
                        class="weight-input"
                        placeholder="分值"
                        [(ngModel)]="option.value"
                      >
                      </nz-input-number>
                      <!-- <input  class="weight-input" nz-input placeholder="分值" [(ngModel)]="option.value" /> -->
                    </div>
                  </ng-container>
                  <ng-container
                    *ngIf="
                      questionData.type == 'MULTIPLE_CHOICE_ESSAY_QUESTION'
                    "
                  >
                    <div class="option">
                      <label
                        style="margin-top: 5px;"
                        nz-checkbox
                        [(ngModel)]="option.isEnableOpenAnswer"
                        >开放式回答</label
                      >
                      <nz-radio-group
                        [(ngModel)]="option.isRequireOpenAnswer"
                        *ngIf="option.isEnableOpenAnswer"
                        style="margin-top: 5px;margin-left: 15px"
                      >
                        <label nz-radio [nzValue]="true">必填</label>
                        <label nz-radio [nzValue]="false">选填</label>
                      </nz-radio-group>
                    </div>
                  </ng-container>
                  <ng-container
                    *ngIf="
                      questionData.type == 'PROPORTION' ||
                      questionData.type == 'PROPORTION_MULTIPLE'
                    "
                  >
                    <div class="option">
                      <nz-select
                        class="option-select"
                        [(ngModel)]="option.isScoring"
                        nzAllowClear
                        nzPlaceHolder="Choose"
                      >
                        <nz-option [nzValue]="true" nzLabel="计分"></nz-option>
                        <nz-option
                          [nzValue]="false"
                          nzLabel="不计分"
                        ></nz-option>
                      </nz-select>
                      <nz-input-number
                        class="weight-input-1"
                        [nzPlaceHolder]="'最大值'"
                        [(ngModel)]="option.highestScore"
                        [nzStep]="1"
                      ></nz-input-number>
                      <nz-input-number
                        style="margin: 0 5px;"
                        class="weight-input-1"
                        [nzPlaceHolder]="'最小值'"
                        [(ngModel)]="option.lowestScore"
                        [nzStep]="1"
                      ></nz-input-number>
                      <nz-input-number
                        class="weight-input-1"
                        [nzPlaceHolder]="'刻度值'"
                        [nzMin]="0.1"
                        [(ngModel)]="option.stepScore"
                        [nzStep]="0.1"
                      ></nz-input-number>
                    </div>
                  </ng-container>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </ng-container>

      <ul class="modal_footer" *nzModalFooter>
        <li class="footer_left" (click)="getcommit()">
          确认
        </li>
        <li class="footer_right" (click)="getrelease()">
          取消
        </li>
      </ul>
    </div>
  </div>
</ng-container>

<div *ngIf="oldDemoshow === 2" style="display: flex;flex-direction: column;">
  <ul class="newfactor">
    <li>维度管理</li>
    <li class="add_factor" (click)="addfactor()">
      <img src="./assets/images/add.png" alt="" />添加维度
    </li>
  </ul>

  <div>
    <nz-row>
      <nz-col [nzSpan]="3" class="dimension_div">
        <span>类别</span>
      </nz-col>
      <nz-col [nzSpan]="2" class="dimension_div">
        <span>编码</span>
      </nz-col>
      <nz-col [nzSpan]="3" class="dimension_div">
        <span>维度名称（中文）</span>
      </nz-col>
      <nz-col [nzSpan]="3" class="dimension_div">
        <span>维度名称（英文）</span>
      </nz-col>
      <nz-col [nzSpan]="2" class="dimension_div">
        <span>{{
          standardReportType != "BLANK_CUSTOM" &&
          currentReportType != "BLANK_CUSTOM"
            ? "维度比重"
            : "算法"
        }}</span>
      </nz-col>
      <nz-col [nzSpan]="5" class="dimension_div">
        <span>维度说明(中)</span>
      </nz-col>
      <nz-col [nzSpan]="5" class="dimension_div">
        <span>维度说明(英)</span>
      </nz-col>
      <nz-col [nzSpan]="1" class="dimension_div"> </nz-col>
    </nz-row>
  </div>
  <div *ngFor="let item of factorlist; let i = index">
    <div nz-row style="padding:10px 0;">
      <div nz-col [nzSpan]="3" class="dimension_div">
        <nz-select
          style="width: 100%;"
          [(ngModel)]="item.type"
          nzAllowClear
          nzPlaceHolder="Choose"
        >
          <nz-option [nzValue]="'ONE_RANK'" nzLabel="一级维度"></nz-option>
          <nz-option [nzValue]="'TWO_RANK'" nzLabel="二级维度"></nz-option>
          <nz-option
            [nzValue]="'THREE_RANK'"
            nzLabel="三级维度"
            *ngIf="
              isedited &&
              standardReportType !== 'BLANK_CUSTOM' &&
              currentReportType != 'BLANK_CUSTOM'
            "
          ></nz-option>
        </nz-select>
      </div>
      <nz-col [nzSpan]="2" class="dimension_div">
        <input
          nz-input
          placeholder="请输入"
          [(ngModel)]="item.code"
          required
          [disabled]="!!item.id"
        />
      </nz-col>
      <div nz-col [nzSpan]="3" class="dimension_div">
        <input
          nz-input
          placeholder="请输入"
          [(ngModel)]="item.name.zh_CN"
          required
        />
      </div>
      <div nz-col [nzSpan]="3" class="dimension_div">
        <input
          nz-input
          placeholder="请输入"
          [(ngModel)]="item.name.en_US"
          required
        />
      </div>

      <div nz-col [nzSpan]="2" class="dimension_div">
        <ng-container
          *ngIf="
            standardReportType !== 'BLANK_CUSTOM' &&
              currentReportType != 'BLANK_CUSTOM';
            else algorithm
          "
        >
          <input
            type="number"
            nz-input
            placeholder="请输入比重"
            [disabled]="item.type == 'ONE_RANK'"
            [(ngModel)]="item.proportion"
            required
          />
        </ng-container>
        <ng-template #algorithm>
          <app-algorithm-select
            [disabled]="item.type == 'ONE_RANK'"
            [algorithmData]="item.algorithmData"
            [algorithmType]="item.algorithmType"
            (onCheck)="algorithmChange($event, item)"
          ></app-algorithm-select>
        </ng-template>
      </div>

      <div nz-col [nzSpan]="5" class="dimension_div">
        <input
          nz-input
          placeholder="请输入中文维度说明"
          [(ngModel)]="item.description.zh_CN"
          required
        />
        <ng-template #prefixTemplateUser>
          <span class="option-index">{{ i + 1 }}</span>
        </ng-template>
      </div>
      <div nz-col [nzSpan]="5" class="dimension_div">
        <input
          style="margin-right: 10px;"
          nz-input
          placeholder="请输入英文维度说明"
          [(ngModel)]="item.description.en_US"
        />
      </div>
      <div nz-col [nzSpan]="1" class="dimension_div">
        <span class="delete-btn" (click)="onDeletefactorlist(i, item.id)"
          >-</span
        >
      </div>
    </div>
  </div>

  <ul class="modal_footer" *nzModalFooter>
    <li class="footer_left" (click)="getSaveSet()">
      保存
    </li>
    <li class="footer_right" (click)="getDefault()">
      返回上一步
    </li>
  </ul>
</div>
<div *ngIf="oldDemoshow === 3">
  <ul class="newfactor">
    <li>题本修订</li>
    <li>
      <a
        nz-popconfirm
        nzPopconfirmTitle="确定清空当前修订吗？"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="clear()"
        >清空</a
      >
    </li>
  </ul>
  <div class="revision-content">
    <div class="left">
      <div class="top">
        <nz-select
          style="width: 180px;"
          [(ngModel)]="selectConditionVal"
          nzPlaceHolder="请选择"
        >
          <nz-option
            *ngFor="let option of revisionConditionObj"
            [nzValue]="option.value"
            [nzLabel]="option.label"
          ></nz-option>
        </nz-select>
        <div class="create" (click)="createRevision()">
          <img src="./assets/images/add.png" alt="" /><span>创建</span>
        </div>
      </div>
      <div>
        <!-- 角色 -->
        <div [hidden]="selectConditionVal !== 1" class="tree treeScroll">
          <nz-tree
            #nzTreeComponent
            nzCheckable
            [nzCheckStrictly]="true"
            [nzData]="roleList"
            [nzExpandAll]="false"
            [nzExpandedKeys]="expandedroles"
            [nzSearchValue]="searchValue"
            (nzCheckBoxChange)="nzEvent($event)"
          >
          </nz-tree>
        </div>
        <!-- 人口标签 -->
        <div [hidden]="selectConditionVal !== 2" class="tree treeScroll">
          <nz-tree
            #nzTreeComponentRenkou
            nzCheckable
            [nzData]="demographics"
            [nzExpandAll]="false"
            [nzExpandedKeys]="expandedRenNodes"
            [nzSearchValue]="searchRenValue"
            (nzCheckBoxChange)="nzEventRen($event)"
          >
          </nz-tree>
        </div>
        <div [hidden]="selectConditionVal !== 3" class="tree treeScroll">
          <nz-tree
            #nzTreeComponentQues
            nzCheckable
            [nzData]="revisionTypes"
            [nzExpandAll]="false"
            [nzExpandedKeys]="expandedRevisionTypes"
            [nzSearchValue]="searchRenValue"
            (nzCheckBoxChange)="nzEventQues($event)"
          >
          </nz-tree>
        </div>
      </div>
    </div>
    <div class="right">
      <ul class="res-list">
        <li *ngFor="let res of resultList; let resIndex = index">
          <ng-container *ngIf="res.revisionInfoVos.length > 0">
            <nz-collapse>
              <nz-collapse-panel [nzHeader]="collTemp" [nzActive]="true">
                <div class="res-ques-box">
                  <ul
                    class="res-ques"
                    *ngFor="
                      let que of res.revisionInfoVos;
                      let queIndex = index
                    "
                  >
                    <li>
                      <div style="margin-bottom: 5px;">
                        <div style="height: 100%; margin-right: 10px;">
                          {{ que.type === "QUESTION" ? "题干" : "选项" }}
                        </div>
                        <input
                          nz-input
                          disabled
                          style="width: 370px; height: 30px;"
                          [(ngModel)]="que.name.zh_CN"
                        />
                      </div>
                      <div>
                        <div
                          style="height: 100%; margin-right: 10px;"
                          class="delete-icon-box"
                        >
                          <div
                            class="delete-icon"
                            nz-popconfirm
                            [nzPopconfirmTitle]="
                              '确定删除当前' +
                              (que.type === 'QUESTION' ? '题干吗?' : '选项吗?')
                            "
                            nzPopconfirmPlacement="bottom"
                            (nzOnConfirm)="deleteOne(resIndex, queIndex)"
                            (nzOnCancel)="cancel()"
                          ></div>
                        </div>
                        <input
                          nz-input
                          disabled
                          style="width: 370px; height: 30px;"
                          [(ngModel)]="que.name.en_US"
                        />
                      </div>
                    </li>
                    <li class="center">调整后</li>
                    <li>
                      <div style="margin-bottom: 5px;">
                        <input
                          nz-input
                          style="width: 370px; height: 30px;"
                          placeholder="请输入"
                          [(ngModel)]="que.anotherName.zh_CN"
                        />
                      </div>
                      <div>
                        <input
                          nz-input
                          style="width: 370px; height: 30px;"
                          placeholder="请输入"
                          [(ngModel)]="que.anotherName.en_US"
                        />
                      </div>
                    </li>
                  </ul>
                </div>
              </nz-collapse-panel>
            </nz-collapse>
            <ng-template #collTemp>
              <div class="res-box">
                <span class="type-name">结果{{ resIndex + 1 }}</span>
                <div class="content-text" *ngIf="res.roleId">
                  {{ res.roleName.zh_CN }}
                </div>
                <div>
                  <span *ngFor="let demo of res.demographics; let i = index">
                    <span *ngIf="res.roleId || i !== 0" style="color: #64A2F7;"
                      >且</span
                    >
                    {{
                      demo.name.zh_CN +
                        (demo.children.length !== 0
                          ? " - " + demo.children[0].name.zh_CN
                          : "")
                    }}
                  </span>
                </div>
              </div>
            </ng-template>
          </ng-container>
        </li>
      </ul>
    </div>
  </div>

  <ul class="modal_footer" *nzModalFooter>
    <li class="footer_left" (click)="getSaveRevision()">
      保存
    </li>
    <li class="footer_right" (click)="getDefault()">
      返回上一步
    </li>
  </ul>
</div>
