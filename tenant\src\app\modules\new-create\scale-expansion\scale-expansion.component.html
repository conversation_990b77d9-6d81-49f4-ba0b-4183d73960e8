<!-- 自定义问卷-量表尺度拓展 -->
<nz-drawer
  nzWrapClassName="round-right-drawer4"
  [nzWidth]="960"
  [(nzVisible)]="isVisible"
  nzTitle="量表尺度扩展"
  (nzOnClose)="handleCancel()"
>
  <nz-spin [nzSpinning]="isSpinning">
    <div class="scaleExpansion">
      <!-- 第一页 -->
      <div *ngIf="step == 0" class="scaleExpansion_content borderCard" nz-row>
        <!-- 量表题扩展条件 -->
        <div nz-col nzSpan="12" class="scaleExpansion_content_left">
          <div class="titleBox">
            <h3>量表题扩展条件</h3>
            <a
              style="margin-right: 16px;"
              nz-popover
              [nzPopoverContent]="viewScale"
              nzPopoverPlacement="bottomRight"
              >查看量表</a
            >
            <!-- 量表详情 popover -->
            <ng-template #viewScale>
              <div class="viewScale">
                <h3>量表详情</h3>
                <div class="overflowY">
                  <nz-empty
                    *ngIf="!gaugeOptionList.length"
                    nzNotFoundImage="assets/images/org/group_empty.png"
                    nzNotFoundContent="暂无数据"
                  ></nz-empty>
                  <ng-container *ngIf="!!gaugeOptionList.length">
                    <div class="table" *ngFor="let ele of gaugeOptionList">
                      <div class="tr" *ngFor="let item of ele">
                        <div class="td">{{ item.name.zh_CN }}</div>
                        <div class="td">{{ item.value }}</div>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </div>
            </ng-template>
          </div>
          <!-- 量表组数选择 -->
          <div class="select">
            <div>
              <span>量表尺度组数，包含</span>
              <nz-select
                nzShowSearch
                nzAllowClear
                nzPlaceHolder="请选择"
                [(ngModel)]="gaugeScaleExtendValue"
                style="min-width: 150px;max-width: 286px;"
                nzMode="multiple"
                (ngModelChange)="changeScaleExtendList($event)"
              >
                <ng-container *ngFor="let item of gaugeScaleExtendList">
                  <nz-option
                    [nzLabel]="item.name"
                    [nzValue]="item.optionCount"
                  ></nz-option>
                </ng-container>
              </nz-select>
            </div>
            <p>不同尺度的量表题目，每页显示题数相同且整除</p>
          </div>
          <!-- 量表组数配置 -->
          <div class="slectlist">
            <ng-container *ngIf="gaugeScaleExtendListSelected.length">
              <ng-container *ngFor="let value of gaugeScaleExtendListSelected">
                <div class="slectlist_item radius-8">
                  <span>{{ value.name }}总计</span>
                  <div class="bgBox">{{ value.questionCount }}</div>
                  <span>题 每页填答题数</span>
                  <nz-select
                    [(ngModel)]="value.pageSize"
                    nzAllowClear
                    nzPlaceHolder="请选择"
                    style="width: 80px;margin: 0 5px;"
                    (ngModelChange)="changePageSize($event, value)"
                  >
                    <ng-container *ngFor="let item of questionsNumberOptions">
                      <nz-option [nzLabel]="item" [nzValue]="item"></nz-option>
                    </ng-container>
                  </nz-select>
                  <span>共计</span>
                  <div
                    class="bgBox"
                    [ngClass]="{ na: value.pageCount == '#NA' }"
                  >
                    {{ value.pageCount }}
                  </div>
                  <span>页</span>
                  <span>每页选中相同选项的个数为</span>
                  <nz-select
                    [(ngModel)]="value.selectedCount"
                    nzAllowClear
                    nzPlaceHolder="请选择"
                    style="width: 80px;margin: 0 5px;"
                  >
                    <ng-container *ngFor="let item of itemNumberOptions">
                      <nz-option [nzLabel]="item" [nzValue]="item"></nz-option>
                    </ng-container>
                  </nz-select>
                  <span>个及以上，需要扩展</span>
                </div>
              </ng-container>
            </ng-container>
            <ng-container *ngIf="!gaugeScaleExtendListSelected.length">
              <div style="height: 100%;">
                <app-empty text="暂无数据"></app-empty>
              </div>
            </ng-container>
          </div>
        </div>
        <!-- 量表题扩展规则 -->
        <div nz-col nzSpan="12" class="scaleExpansion_content_right">
          <div class="titleBox">
            <h3>量表题扩展规则</h3>
            <a
              nz-popover
              [nzPopoverContent]="viewCase"
              nzPopoverPlacement="bottomRight"
              >查看案例</a
            >
            <!-- 查看案例 popover -->
            <ng-template #viewCase>
              <div class="viewCase">
                <h3>查看案例</h3>
                <p>
                  每页中选择相同选项的题目，进行排序，取两端。剩余选项再次进行排序，依次循环
                </p>
                <div>
                  <div>
                    <p>循环第1次：</p>
                    <p>最符合N-1分 最不符合1分</p>
                  </div>
                  <div>
                    <p>循环第2次：</p>
                    <p>最符合N-2分 最不符合1+1分</p>
                  </div>
                  <div>
                    <p>循环第X次：</p>
                    <p>最符合N-X分 最不符合N+X分</p>
                  </div>
                </div>
              </div>
            </ng-template>
          </div>
          <!-- 扩展计算规则 -->
          <div class="autoBox  mt-35">
            <!-- title -->
            <div class="rowlist">
              <div class="rowlist_left">
                <span class="th" style="margin-left: 46px;">选项</span>
              </div>
              <div class="rowlist_right">
                <span class="th">选中后得分</span>
              </div>
            </div>
            <!-- 1 -->
            <div class="rowlist">
              <div class="rowlist_left">
                <div class="index">1</div>
                <input
                  class="optionInput"
                  nz-input
                  placeholder="请输入"
                  [(ngModel)]="gaugeScaleExtend.highestScoreOption"
                />
              </div>
              <div class="rowlist_right">
                <nz-select
                  [(ngModel)]="gaugeScaleExtend.highestScore"
                  nzAllowClear
                  nzPlaceHolder="请选择"
                  style="width: 100%;"
                  (ngModelChange)="chnageScoreOption($event, 1)"
                >
                  <ng-container *ngFor="let item of suitableOptions">
                    <nz-option
                      [nzValue]="item.value"
                      [nzLabel]="item.label"
                    ></nz-option>
                  </ng-container>
                </nz-select>
              </div>
            </div>
            <!-- 2 -->
            <div class="rowlist suspendedBox">
              <div class="rowlist_left">
                <div class="index">2</div>
                <div class="option disabled">再次循环排序</div>
              </div>
              <div class="rowlist_right">
                <div class="option disabled">N-循环次数</div>
              </div>
              <!-- 悬浮物 -->
              <div class="suspended" *ngIf="step == 0">
                <div class="suspended_connect">
                  <div class="suspended_connect_dot"></div>
                  <div class="suspended_connect_dot"></div>
                  <div class="suspended_connect_box">N/2次循环</div>
                </div>
              </div>
            </div>
            <!-- 3 -->
            <div class="rowlist">
              <div class="rowlist_left">
                <div class="index">3</div>
                <div class="option disabled">再次循环排序</div>
              </div>
              <div class="rowlist_right">
                <span class="th">...</span>
              </div>
            </div>
            <!-- 4 -->
            <div class="rowlist">
              <div class="rowlist_left">
                <div class="index">4</div>
                <div class="option disabled">再次循环排序</div>
              </div>
              <div class="rowlist_right"></div>
            </div>
            <div class="rowlist">
              <div class="rowlist_left">
                <div></div>
                <div
                  class="th"
                  style="width: 160px;text-align: center;padding: 12px 0;"
                >
                  ...
                </div>
              </div>
              <div class="rowlist_right">
                <div class="th" style="padding: 12px 0;">...</div>
              </div>
            </div>
            <!-- N-1 -->
            <div class="rowlist">
              <div class="rowlist_left">
                <div class="index">N-1</div>
                <div class="option disabled">再次循环排序</div>
              </div>
              <div class="rowlist_right">
                <div class="option disabled">1+循环次数</div>
              </div>
            </div>
            <!-- N -->
            <div class="rowlist">
              <div class="rowlist_left">
                <div class="index">N</div>
                <input
                  class="optionInput"
                  nz-input
                  placeholder="请输入"
                  [(ngModel)]="gaugeScaleExtend.lowestScoreOption"
                />
              </div>
              <div class="rowlist_right">
                <nz-select
                  [(ngModel)]="gaugeScaleExtend.lowestScore"
                  nzAllowClear
                  nzPlaceHolder="请选择"
                  style="width: 100%;"
                  (ngModelChange)="chnageScoreOption($event, 0)"
                >
                  <ng-container *ngFor="let item of suitableOptions">
                    <nz-option
                      [nzValue]="item.value"
                      [nzLabel]="item.label"
                    ></nz-option>
                  </ng-container>
                </nz-select>
              </div>
            </div>
            <div class="warning radius-8">
              <p>* N=奇数，剩余1个不需要循环，得分=（最高分+最低分）/2</p>
              <p>* 未进入扩展的选项，得分=（最高分+最低分）/2</p>
              <p>* 得分为整数，小数点按四舍五入计算</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 第二页 -->
      <div *ngIf="step == 1" class="scaleExpansion_content">
        <p class="scaleExpansion_content_top">
          由于扩展题追加赋分，各维度常模请更新
        </p>
        <!-- 维度常模 -->
        <div class="scaleExpansion_content_bottom">
          <div class="listItem" *ngFor="let item of dimensionList">
            <p>{{ item.dimonsionName }}</p>
            <span>总分区间 {{ item.scoreRange }}</span>
            <textarea
              [nzAutosize]="{ minRows: 7, maxRows: 7 }"
              nz-input
              [(ngModel)]="item.normMapping"
              style="margin-top: 10px;"
              placeholder="请输入"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </nz-spin>
  <div class="drawer-footer">
    <button nz-button nzType="default" *ngIf="step == 0" (click)="clearData()">
      恢复默认
    </button>
    <button
      nz-button
      nzType="primary"
      *ngIf="step == 0"
      (click)="changeStep(1)"
    >
      下一页
    </button>
    <button
      nz-button
      nzType="default"
      *ngIf="step == 1"
      (click)="changeStep(0)"
    >
      上一页
    </button>
    <button nz-button nzType="primary" *ngIf="step == 1" (click)="handleOk()">
      确认
    </button>
  </div>
</nz-drawer>
