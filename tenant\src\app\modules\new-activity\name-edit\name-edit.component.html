<div class="container" *ngIf="selectionList.length !== 0">
  <div class="lan">
    {{ lan === "zh_CN" ? "中文称谓" : "English Title" }}
  </div>

  <div style="width: 100%; min-height: 100px;">
    <table>
      <tr>
        <th class="td_title" width="150px">{{ th2 }}</th>
        <th class="td_title" width="150px">
          <div style="padding-left: 24px;white-space: nowrap;">
            {{ th3 }}
          </div>
        </th>
      </tr>
      <tr *ngFor="let selectionObj of selectionList">
        <td>
          <nz-select
            [(ngModel)]="selectionObj.value.oldValue"
            [nzShowArrow]="true"
            [disabled]="fDisabled"
            style="width: 100%;"
            (ngModelChange)="itemChange(selectionObj.value)"
          >
            <nz-option
              *ngFor="let item of selectionObj.value.options"
              [nzValue]="item.id"
              [nzLabel]="item.name"
            ></nz-option>
            <nz-option [nzValue]="selfValue" [nzLabel]="selfValue"></nz-option>
          </nz-select>
        </td>
        <td class="arrow">
          <div>
            <img src="assets/images/arrow-up.png" alt="" />
          </div>
          <div>
            <input
              nz-input
              [ngClass]="{ redBorder: selectionObj.value.selfDefined }"
              [(ngModel)]="selectionObj.value.newValue"
              [disabled]="!selectionObj.value.selfDefined || fDisabled"
            />
          </div>
        </td>
      </tr>
    </table>
  </div>

  <!-- <div class="pre">
        {{lan === 'zh_CN'? '预览' : 'Preview'}}
    </div>
    
    <div class="preview" [innerHTML]="getShowName() | html">
    </div> -->

  <!-- <div>
        {{getSaveName()}}
    </div> -->
</div>
