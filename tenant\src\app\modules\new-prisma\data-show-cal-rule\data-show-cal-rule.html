<!--
    *@author: <PERSON>
    *@Date: 2025年3月21日
    *@content: 数据呈现/计算规则
-->
<div class="rule-box">
  <div class="rule-box-title">
    <span class="tip">* 当组织或标签在报告和报表中【不需要展示】，可在此设置。此时如【向上计算】选中，不展示的数据将纳入当前的上级部门/标签的结果中，反之，不纳入计算。</span>
    <button nz-button nzType="link" (click)="onReset()">恢复默认</button>
  </div>
  <div>
    <nz-table
      #nzTable
      [nzData]="listOfData"
      [nzShowPagination]="false"
      [nzLoading]="isTableSpinning"
    >
      <thead>
        <tr>
          <th nzShowExpand></th>
          <th>类型</th>
          <th nzWidth="190px" nzAlign="center">报告展示</th>
          <th nzWidth="190px" nzAlign="center">报表展示</th>
          <th nzWidth="190px" nzAlign="center">向上计算</th>
          <th nzWidth="150px" nzAlign="center" class="border-left">
            下沉详细数据设置
          </th>
        </tr>
      </thead>
      <tbody>
        <ng-template ngFor let-data [ngForOf]="nzTable.data">
          <tr>
            <td nzShowExpand [(nzExpand)]="mapOfExpandData[data.code]"></td>
            <td [attr.colspan]="4">{{ data.name }}</td>
            <td class="options border-left">
              <ng-container *ngIf="data.code === 'DEMOGRAPHIC'">
                <nz-upload
                  [nzCustomRequest]="customReqload_DEMOGRAPHIC"
                  [nzShowUploadList]="false"
                  [nzDisabled]="currentCode === data.code && isImportSpinning"
                >
                  <button
                    nz-button
                    nzType="link"
                    nzSize="small"
                    [nzLoading]="currentCode === data.code && isImportSpinning"
                  >
                    导入
                  </button>
                </nz-upload>
              </ng-container>
              <ng-container *ngIf="data.code === 'ORGANIZATION'">
                <nz-upload
                  [nzCustomRequest]="customReqload_ORGANIZATION"
                  [nzShowUploadList]="false"
                  [nzDisabled]="currentCode === data.code && isImportSpinning"
                >
                  <button
                    nz-button
                    nzType="link"
                    nzSize="small"
                    [nzLoading]="currentCode === data.code && isImportSpinning"
                  >
                    导入
                  </button>
                </nz-upload>
              </ng-container>
              <ng-container *ngIf="data.code === 'DIMENSION'">
                <nz-upload
                  [nzCustomRequest]="customReqload_DIMENSION"
                  [nzShowUploadList]="false"
                  [nzDisabled]="currentCode === data.code && isImportSpinning"
                >
                  <button
                    nz-button
                    nzType="link"
                    nzSize="small"
                    [nzLoading]="currentCode === data.code && isImportSpinning"
                  >
                    导入
                  </button>
                </nz-upload>
              </ng-container>
              <button
                nz-button
                nzType="link"
                nzSize="small"
                (click)="onExport(data.code)"
                [nzLoading]="currentCode === data.code && isDownLoadSpinning"
              >
                导出
              </button>
            </td>
          </tr>
          <tr
            [nzExpand]="mapOfExpandData[data.code]"
            *ngFor="let rule of data.rules"
          >
            <td></td>
            <td>{{rule.name.zh_CN}}</td>
            <td nzAlign="center">
              <label nz-checkbox [(ngModel)]="rule.isShowReport" (ngModelChange)="onChangeCheckbox(rule)"></label>
            </td>
            <td nzAlign="center">
              <label nz-checkbox [(ngModel)]="rule.isShowExcel" (ngModelChange)="onChangeCheckbox(rule)"></label>
            </td>
            <td nzAlign="center">
              <label nz-checkbox [(ngModel)]="rule.isUpCalculate" [nzDisabled]="rule.isShowReport && rule.isShowExcel"></label>
            </td>
            <td></td>
          </tr>
        </ng-template>
      </tbody>
    </nz-table>
  </div>
</div>
<div class="footer">
  <button nz-button nzType="default" (click)="handClose()">关闭</button>
  <button nz-button nzType="primary" (click)="onSave()">确认</button>
</div>
