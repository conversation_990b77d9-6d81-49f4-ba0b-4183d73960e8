// .add-box {
//   max-height: 540px;
// }
// 题本修订相关
.revision-content {
  display: flex;
  border-top: 1px solid #e6e6e6;

  >div {
    height: 100%;
    padding: 10px;
  }

  .left {
    width: 360px;

    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .create {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #419eff;
        img {
          margin-right: 7px;
        }
      }
    }

    .question-box {
      // height: calc(100% - 80px);
      flex: 1;
      overflow-y: auto;

      >li {
        display: flex;

        .label {
          display: flex;
          width: 18px;
          height: 18px;
        }

        >ul {
          margin-left: 14px;
          margin-bottom: 20px;

          li {
            display: flex;
            align-items: center;

            .icon-xiala {
              cursor: pointer;
              transform: rotate(0deg);
              margin-right: 10px;
              // margin-top: -20px;
            }

            .turn {
              transform: rotate(90deg);
            }

            .icon-xiala,
            .turn {
              transition: all 0.1s linear;
            }
          }
        }
      }
    }
  }

  .right {
    border-left: 1px solid #e6e6e6;
    background: #f5f6fa;
    overflow-y: auto;
    height: 400px;

    .res-list {

      // width: 878px;
      > li {
        // padding: 20px 0 10px 15px;
        margin-bottom: 10px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #495970;
        line-height: 20px;

        .res-box {
          display: flex;
          align-items: center;

          // margin-bottom: 15px;
          .type-name {
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #495970;
            line-height: 20px;
            margin-right: 25px;
          }

          .content-text {
            margin-right: 10px;
          }
        }

        .res-ques-box {
          height: 100%;
        }

        .res-ques {
          display: flex;
          margin-bottom: 10px;

          li {
            height: 65px;

            span {
              margin-bottom: 14px;
            }

            >div {
              display: flex;
              align-items: center;
            }

            .delete-icon-box {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 28px;

              .delete-icon {
                cursor: pointer;
                width: 18px;
                height: 18px;
                background-size: cover;
                background-image: url("../../../../../assets/images/delete-ico.png");
              }

              .delete-icon:hover {
                background-image: url("../../../../../assets/images/delete-ico-hover.png");
              }
            }
          }

          li.center {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 72px;
          }
        }
      }
    }
  }

  .tree {
    width: 100%;
    height: 348px;
    // max-height: 280px;
    display: flex;
  }

  .treeScroll {
    overflow-y: auto;
    overflow-x: auto;
  }
}

// 题本修订相关 End

.question {
  display: flex;
  align-items: flex-start;
  margin: 20px 0;

  .title {
    color: #17314c;
    margin-bottom: 10px;
    font-size: 14px;
  }
}

.over-flow-box {
  overflow-y: auto;
  max-height: 540px;
}

.list1 {
  width: 55%;
  margin-right: 20px;
}

.list2 {
  width: 20%;
  margin-right: 16px;
}

.list3 {
  width: 35%;
}

.option-input {
  display: inline-block;
  width: 97%;
}

.weight-input {
  display: inline-block;
  width: 50%;
}

.weight-input-1 {
  display: inline-block;
  width: 100px;
}

.delete-btn {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-color: #d0d0d0;
  color: #ffffff;
  text-align: center;
  line-height: 13px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 16px;
  cursor: pointer;

  &:hover {
    background-color: #f19672;
  }
}

.option-select {
  width: 45%;
  margin-right: 10px;
}

.add-btn {
  color: #409eff;
  float: right;
  cursor: pointer;
}

::ng-deep .add-box .ant-input {
  height: 40px;
}

::ng-deep .add-box .ant-select-selection--single {
  height: 40px;
}

::ng-deep {
  .ant-tooltip {
    max-width: none;
  }

  .revision-temp-box {
    background-color: #ffffff;

    .ant-tooltip-arrow {
      &:before {
        background-color: #ffffff !important;
      }
    }

    .ant-tooltip-inner {
      width: 928px;
      height: 452px;
      background: #ffffff;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
      color: #495970;
      padding: 0;

      .revision-header-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding: 24px 21px 0 24px;

        .revision-title {
          width: 72px;
          height: 25px;
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #17314c;
          line-height: 25px;
        }

        .revision-handler {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #419eff;
          line-height: 20px;
          cursor: pointer;

          img {
            margin-right: 7px;
          }
        }
      }

      .scro-rev-box {
        height: 320px;
        overflow-y: auto;
        margin: 0 21px 0 24px;
        // width: 928px;
        // background: linear-gradient(90deg, rgba(0,0,0,0.08) 0%, rgba(255,255,255,0) 100%);
      }

      .revision-condition {
        display: flex;
        align-items: center;
        // width: 878px;
        height: 60px;
        background: #f5f6fa;
        margin-bottom: 10px;
        .box1,
        .box2 {
          display: flex;
          align-items: center;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #495970;
          line-height: 20px;

          span {
            margin-right: 10px;
          }
        }

        .box1 {
          span {
            margin-left: 15px;
          }
        }

        .box2 {
          margin-left: 34px;
        }
      }

      .res-list {
        // width: 878px;
        min-height: 250px;
        background: #f5f6fa;

        >li {
          padding: 20px 0 10px 15px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #495970;
          line-height: 20px;

          .res-box {
            display: flex;
            align-items: center;
            margin-bottom: 15px;

            .type-name {
              height: 20px;
              font-size: 14px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #495970;
              line-height: 20px;
              margin-right: 25px;
            }

            .content-text {
              margin-right: 10px;
            }
          }

          .res-ques-box {
            height: 100%;
          }

          .res-ques {
            display: flex;

            li {
              height: 65px;

              span {
                margin-bottom: 14px;
              }

              >div {
                display: flex;
                align-items: center;
              }

              .delete-icon-box {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 28px;

                .delete-icon {
                  cursor: pointer;
                  width: 18px;
                  height: 18px;
                  background-size: cover;
                  background-image: url("../../../../../assets/images/delete-ico.png");
                }

                .delete-icon:hover {
                  background-image: url("../../../../../assets/images/delete-ico-hover.png");
                }
              }
            }

            li.center {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 72px;
            }
          }
        }
      }

      .revision-footer {
        display: flex;
        align-items: center;
        justify-content: right;
        height: 70px;
        padding-right: 21px;

        button {
          width: 69px;
          height: 30px;
          border-radius: 15px;
          border: 1px solid #409eff;
        }
      }
    }
  }
}

.option {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.select {
  width: 100%;
}

.option-index {
  display: inline-block;
  width: 25px;
  height: 26px;
  border-radius: 3px;
  background-color: #e3f1ff;
  color: #409eff;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  line-height: 26px !important;
  margin-left: -8px;
}

.newfactor {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20px;
}

.modal_footer {
  display: flex;
  align-items: center;
  margin-left: 30px;

  .footer_left {
    background: linear-gradient(90deg, #27d0f1 0%, #409eff 100%);
    width: 144px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    color: #fff;
    border-radius: 30px;
    cursor: pointer;
  }

  .footer_right {
    margin-left: 30px;
    cursor: pointer;
    width: 128px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    background: #fafafa;
    border-radius: 19px;
  }
}

.delete_finish {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auto_div {
  height: 350px;
  overflow-y: auto;
}

.word_sel {
  position: absolute;
  top: 0;
  padding-left: 15px;
  width: 129px;
  height: 38px;
  text-align: center;
  line-height: 38px;
  cursor: pointer;
}

.top_div {
  margin-top: 30px;
}

.dimension_div {
  padding-left: 5px;
  padding-right: 5px;
}

.mark_label {
  position: absolute;
  top: 30px;
  right: -5px;
  width: 1210px;
  height: 422px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  z-index: 9999;
  padding: 20px;
  p {
    color: #17314c;
    font-size: 18px;
    font-weight: 600;
  }
  .mark_title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #495970;
  }
}

.top_title {
  margin-top: 25px;
}

.top_mark {
  margin-top: 10px;

  span {
    margin-right: 5px;
    color: #aaaaaa;
  }
}
.but_fotter {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  box-shadow: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}
