import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Router } from "@angular/router";
import { NzMessageService } from "ng-zorro-antd";
import { TouristService } from '../tourist.service';

@Component({
  selector: 'app-culturee',
  templateUrl: './culture.component.html',
  styleUrls: ['./culture.component.less']
})
export class SheComponent implements OnInit {

  tourist: boolean = true;
  isNeedLogin: boolean = false;
  token: any;
  _token: any;
  showtitle = false
  constructor(private http: HttpClient, private router: Router, private message: NzMessageService, private TouristService: TouristService) { }

  ngOnInit() {
    // this.token = JSON.parse(localStorage.getItem("token"));
    this._token = JSON.parse(localStorage.getItem("_token"));
    if (this._token) {
        this.tourist = false;
        this.isNeedLogin = true;
    } else {
      this.showtitle = true
    }
  }

  gotoHome(type?){
    
    if(this._token){
      if(type == 'create'){
        this.router.navigate(['/new-activity']);
      }
      if(type == 'download'){
        this.TouristService.downLoad('she');
      }
    }else{
      this.router.navigate(['/home']);
    }
    
  }
  //   downloadReport(){
  //     this.TouristService.downLoad('prisma');
  //   }
}
