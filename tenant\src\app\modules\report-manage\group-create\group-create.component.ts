import { HttpClient } from "@angular/common/http";
import { Component, Input, OnInit } from "@angular/core";
import { NzMessageService, NzModalRef } from "ng-zorro-antd";
import { Observable } from "rxjs";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-group-create",
  templateUrl: "./group-create.component.html",
  styleUrls: ["./group-create.component.less"],
})
export class GroupCreateComponent implements OnInit {
  @Input() paramList: any[];

  createList: any[] = [];

  detailList: any[] = [];

  searchList: any[] = [];

  currentNairId: string = "";

  searchText: string = "";

  isAgree: boolean = true;

  isDataMode: boolean = true;

  taskRunning: boolean = false;

  tenantBalance: number = 0;

  dash: string = "__";

  tenantUrl: string = "/tenant-api";

  constructor(
    private ref: NzModalRef,
    private msgServ: NzMessageService,
    private http: HttpClient,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    this.getServerData();
    this.getBalacne();
  }

  getBalacne() {
    const beanUrl =
      this.tenantUrl + `/sagittarius/report/content/getTenantBalance`;
    this.http.get(beanUrl).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.tenantBalance = res.data;
      }
    });
  }

  getServerData() {
    const personUrl = this.tenantUrl + `/survey/person/getDataWithGroup`;

    this.http.post(personUrl, this.paramList).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.createList = res.data;
        this.markReturnedData();
      }
    });
  }

  markReturnedData() {
    let firstNairId = "";
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      // test common service
      // this.getQuestionnareCommonInfo(element.standardNairId);

      element.isSelect = false;
      element.isCurrent = false;
      if (index === 0) {
        firstNairId = element.nairId;
        // element.isSelect = true;
      }
      for (let j = 0; j < element.detailList.length; j++) {
        const detail = element.detailList[j];
        detail.key = !!detail.personId
          ? detail.personId
          : detail.investigatorId;
      }
    }
    this.clickNair(firstNairId);
  }

  // 此功能改到后端
  getQuestionnareCommonInfo(id: string) {
    const quesUrl =
      this.tenantUrl +
      `/survey/standard/questionnaire/getGroupReportInfo/${id}`;
    this.http.get(quesUrl).subscribe((res: any) => {
      if (res.result.code === 0) {
        let tmpData = res.data;
        console.info(
          `questionnaire id = ${id},  data = ` + JSON.stringify(tmpData)
        );
      }
    });
  }

  clickNair(nairId) {
    this.currentNairId = nairId;
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      if (nairId === element.nairId) {
        element.isCurrent = true;
        this.detailList = element.detailList;
      } else {
        element.isCurrent = false;
      }
    }
    this.isDataMode = true;
    this.searchText = "";
  }

  delPerson(data: any) {
    // 删除搜索结果中数据
    _.remove(this.searchList, function(userData) {
      return userData.key === data.key;
    });
    this.searchList = [...this.searchList];

    // 搜索结果删完，则显示剩余数据
    if (this.searchList.length === 0) {
      this.isDataMode = true;
      this.searchText = "";
    }

    // 删除当前table中数据
    _.remove(this.detailList, function(userData) {
      return userData.key === data.key;
    });
    this.detailList = [...this.detailList];

    // 删除问卷中数据
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      if (this.currentNairId === element.nairId) {
        element.isCurrent = true;
        let dList: any[] = element.detailList;
        _.remove(dList, function(userData) {
          return userData.key === data.key;
        });
        break;
      }
    }
  }

  searchData() {
    let txt: string = this.searchText;
    if (txt.trim() !== "") {
      this.isDataMode = false;
      this.searchList = _.filter(this.detailList, function(item) {
        return (
          item.name && item.name.toLowerCase().indexOf(txt.toLowerCase()) > -1
        );
      });
    } else {
      this.isDataMode = true;
    }
    this.searchList = [...this.searchList];
    this.detailList = [...this.detailList];
  }

  getDataList() {
    return this.isDataMode ? this.detailList : this.searchList;
  }

  getTotal() {
    let total: number = 0;
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      if (element.isSelect) {
        let curPrice: number = 2000;
        if (!!element.groupStyleType) {
          curPrice = element.cost;
        }
        total = total + curPrice;
      }
    }
    return total;
  }

  doCreateJob() {
    let nairCount: number = 0;
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      if (element.isSelect) {
        let dList: any[] = element.detailList;
        if (dList.length === 0) {
          // this.msgServ.error(
          //   `[ ${element.nairName.zh_CN} ]，选择人员为空，不能创建报告。`
          // );
          this.customMsg.open(
            "error",
            `[ ${element.nairName.zh_CN} ]，选择人员为空，不能创建报告。`
          );
          return;
        }
        nairCount++;
      }
    }

    if (nairCount === 0) {
      // this.msgServ.error("请选择团队报告");
      this.customMsg.open("error", "请选择团队报告");
      return;
    }

    if (this.getTotal() > this.tenantBalance) {
      // this.msgServ.error("余额不足，请先充值！");
      this.customMsg.open("error", "余额不足，请先充值！");
      return;
    }

    let tmpAttr: any[] = _.filter(this.createList, function(item) {
      return item.isSelect;
    });

    // 360自定义 standardNairId nairId 中包含 '__'
    tmpAttr.forEach((createVo) => {
      this.handleDash(createVo, "nairId");
      this.handleDash(createVo, "standardNairId");
    });

    this.taskRunning = true;
    this.bgMakeGeneralGroupReport(tmpAttr).subscribe(
      (res) => {
        this.taskRunning = false;
        if (res.result.code === 0) {
          this.msgServ.success("创建团队报告成功");

          let that = this;
          setTimeout(function() {
            that.ref.triggerCancel();
          }, 1000);
        } else {
          // this.msgServ.error(`创建团队报告失败: ${res.result.message}`);
          this.customMsg.open(
            "error",
            `创建团队报告失败: ${res.result.message}`
          );
        }
      },
      (err) => {
        this.taskRunning = false;
      }
    );
  }

  handleDash(data: any, propName: string) {
    let val: string = data[propName];
    if (val && val.indexOf(this.dash) > -1) {
      let attrs: string[] = val.split(this.dash);
      let tmp = attrs[0];
      data[propName] = tmp;
    }
  }

  bgMakeGeneralGroupReport(json): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/makeGeneralGroupReport`;
    return this.http.post(api, json);
  }
}
