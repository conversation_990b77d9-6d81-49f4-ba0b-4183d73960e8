@text-color: #17314C;
.container {
    margin:0px auto;
    height: auto;
}
.index-title{ font-size:30px; color: @text-color; margin: 25px 0;font-weight:300; }
.report-header{ 
        align-items: center;
      .search{ width: 186px; height: 30px;background:rgba(255,255,255,1);border-radius:15px; border: none;
        .ant-input{border-radius:15px; border: none;}
        margin-right: 30px;
      }
      nz-select {
        width: 200px;
        height: 30px;
        ::ng-deep .ant-select-selection{
            background:rgba(255,255,255,1);border-radius:15px; border: none;
        }
        
      }
    
  }
  .tbody{
      height: 600px;
      .vxscrollbar();
      th{ background-color: #F3F7FB;text-align: center;}
      td{ background: #ffffff;span{width: 45px;padding:0 10px;display: inline-block;}}
      .M-input,.SD-input{width: 90px;height: 24px;}
      .CM-input{width: 226px;height: 24px;}
      
  }
  .tbody th{
    text-align: left;
  }
  .tbody td{
    white-space: normal;
    word-wrap: break-word;
    max-width: 150px;

  }
  footer{
      text-align: right;
      height: 60px;
      margin-top: 30px;
      .btn{
        width: 100px;
        height: 34px;
        line-height: 34px;
        background: -webkit-gradient(linear, left top, right top, from(#26d0f1), to(#409eff));
        background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
        cursor: pointer;
        box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
        border-radius: 27px;
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
      }
  }
  //滚动条
.vxscrollbar() {
      scrollbar-color: auto;
  scrollbar-width: auto;
    overflow-y: overlay;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    // 滑块背景
    &::-webkit-scrollbar-track {
      // background-color: transparent;
      background-color: #F1F1F1;
      box-shadow: none;
    }
    // 滑块
    &::-webkit-scrollbar-thumb {
      // background-color: #e9e9e9;
      background-color: #C1C1C1;
      outline: none;
      -webkit-border-radius: 2px;
      -moz-border-radius: 2px;
      border-radius: 2px;
    }
}

.right {
    display: flex;
    align-items: center;
    li {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #495970;
      line-height: 20px;
      .iconfont {
        font-size: 18px;
        margin-right: 6px;
      }
      .ant-divider {
        background: #CBCBCB;
      }
    }
    .divider {
      margin: 0 10px;
    }
    .btn {
      cursor: pointer;
    }
    .btn:hover {
      color: #409EFF;
    }
  }