import { Component, OnInit } from "@angular/core";
import { NzMessageService, NzModalService } from "ng-zorro-antd";
import { Router } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { RegisterComponent } from "../register/register.component";
import { SuccessComponent } from "../success/success.component";

@Component({
  selector: "app-wrapper-register",
  templateUrl: "./wrapper-register.component.html",
  styleUrls: ["./wrapper-register.component.less"],
})
export class WrapperRegisterComponent implements OnInit {
  constructor(
    private msg: NzMessageService,
    private modalService: NzModalService,
    private router: Router,
    private http: HttpClient
  ) {}
  tourist: boolean = true;
  isNeedLogin: boolean = false;
  isLoadingOne = false;
  isVisible = true;
  tenantApi: string = "/tenant-api";

  ngOnInit(): void {}

  onCancel(e) {
    this.isVisible = false;
    this.router.navigateByUrl("/");
  }

  register(model) {
    this.isLoadingOne = true;

    const api = `${this.tenantApi}/knx/tenant/tenantRegister/create?_allow_anonymous=true`;
    this.http.post(api, model).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.isVisible = false;
        this.registerSuccess();
      }
      this.isLoadingOne = false;
    });
  }

  registerSuccess() {
    const modal = this.modalService.create({
      nzContent: SuccessComponent,
      nzComponentParams: {
        successModel: {
          text1: "提交成功",
          text2: "稍后会有专业顾问将与您联系。",
        },
      },
      nzWidth: "480px",
      nzStyle: {},
      nzMaskClosable: false,
      nzClosable: false,
      nzFooter: [
        {
          label: "返回首页",
          shape: "round",
          type: "primary",
          onClick: () => {
            modal.destroy();
            this.router.navigateByUrl("/home");
          },
        },
      ],
    });
  }
}
