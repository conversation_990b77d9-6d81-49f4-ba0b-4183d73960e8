<div class="content client-width">
    <layout-header class="vx-default__header" [showtitle]="showtitle" [tourist]="tourist" [isNeedLogin]="isNeedLogin"></layout-header>

    <section class="s1">
        <div class="client-width">
            <div class="s1-l">
                <h5>校招 1+1 </h5>
                <p>聚焦冰山下的个人潜能，从天资和性格倾向两方面（1个用来劣汰，1个用来择优）考察应届求职者的潜在优势。通过大数据算法，快速输出人才匹配度，一目了然匹配结果：高、中、低。便于划定录用标准，筛选最佳适配人才。 </p>
                <button *ngIf="!tourist" class="btn" (click)="downloadReport()">下载报告样例</button>
                <button *ngIf="tourist" class="btn" routerLink="/new-activity">即刻体验</button>
            </div>
            <div class="s1-r">
                <img src="assets/images/v_xiao_0.png" alt="">
            </div>
        </div>
    </section>

    <section class="s2">
        <div class="client-width">
            <h5>如何准确识别潜力人才？</h5>
            <img src="assets/images/v_xiao_1.png" alt="">
        </div>
    </section>

    <section class="s3" style="display: flex;justify-content: center;padding-bottom: 40px;">
        <div class="client-width" style="display: flex;flex-direction: column;justify-content: center;">
            <h5>冰山法则</h5>
            <div style="display: flex;justify-content: center;">
                <img src="assets/images/v_xiao_2.png" alt="">
            </div>
        </div>
    </section>
    <section class="s4" style="padding-top: 100px;display: flex;justify-content: center;">
        <div class="client-width" style="display: flex;flex-direction: column;justify-content: center;">
            <h5>测评优势</h5>
            <div style="display: flex;justify-content: center;">
                <img src="assets/images/v_xiao_3.png" style="width: 70%;" alt="">
            </div>
        </div>
    </section>

    <section class="s5" style="padding-top: 100px;display: flex;justify-content: center;padding-bottom: 30px;">
        <div class="client-width">
            <h5>应用场景</h5>
            <div>
                <img src="assets/images/v_xiao_4.png" alt="">
            </div>
        </div>
    </section>

    <section class="s2">
        <div class="client-width" style="display: flex;justify-content: center;">
            <div style="width: 160px;
            line-height: 38px;color: #fff;
            background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 19px;cursor: pointer;" (click)="gotoHome('create')">
                即刻体验
            </div>
        </div>
    </section>

    <app-footer></app-footer>
</div>