<div style="background:#F5F6FA;height: 100%;">
  <div class="container client-width">
    <header>
      <h2>历史数据关联</h2>
      <button
        class="savebtn"
        (click)="save()"
        nz-button
        nzType="primary"
        [nzLoading]="isSaveLoading"
      >
        保存并更新
      </button>
    </header>

    <div class="historical">
      <!-- 左 -->
      <div class="historical_left">
        <div class="card">
          <div class="card_title">
            <span
              >数据列表<i
                nz-icon
                nzType="question-circle"
                nzTheme="outline"
                nz-tooltip
                nzTooltipTitle="*找不到对比活动？创建并发布新的调研活动，在填答列表中导入对比的填答数据"
              ></i
            ></span>
          </div>
          <div class="card_content">
            <nz-descriptions nzBordered nzSize="small" [nzColumn]="1">
              <nz-descriptions-item nzTitle="基线">
                <p
                  class="ell"
                  style="width: 125px;"
                  nz-tooltip
                  nzTooltipPlacement="top"
                  [nzTooltipTitle]="projectSource?.name"
                >
                  {{ projectSource?.name }}
                </p>
              </nz-descriptions-item>
              <nz-descriptions-item nzTitle="匹配活动">
                <nz-select
                  style="width: 125px"
                  nzAllowClear
                  nzPlaceHolder="请选择"
                  nzShowSearch
                  [(ngModel)]="matchActivityId"
                  (ngModelChange)="matchActivityChange($event)"
                >
                  <nz-option
                    *ngFor="let option of listOfOption"
                    [nzLabel]="option.name"
                    [nzCustomContent]="true"
                    [nzValue]="option.id"
                  >
                    <label
                      nz-tooltip
                      nzTooltipPlacement="left"
                      [nzTooltipTitle]="option.name"
                    >
                      {{ option.name }}</label
                    >
                  </nz-option>
                </nz-select>
              </nz-descriptions-item>
              <nz-descriptions-item nzTitle="数据名称">
                <!-- <input style="width: 125px" nz-input placeholder="请输入数据名称" [(ngModel)]="dataName" /> -->
                <app-i18n-input
                  [value]="dataName"
                  placeholder="请输入数据名称"
                  (changeValue)="changeNameValue($event)"
                  [isDefault]="true"
                ></app-i18n-input>
              </nz-descriptions-item>
            </nz-descriptions>
          </div>
          <div style="margin-top: 24px">
            <div class="card_title">
              <span
                >匹配度计算<i
                  nz-icon
                  nzType="question-circle"
                  nzTheme="outline"
                  nz-tooltip
                  nzTooltipTitle="如需更新匹配度计算，请重新勾选或取消需要分类的复选框，并点击生成匹配结果"
                ></i
              ></span>
            </div>
            <div class="card_content">
              <table>
                <thead>
                  <tr>
                    <th>分类</th>
                    <th>编号</th>
                    <th>文本</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>题本</td>
                    <td>
                      <label
                        nz-checkbox
                        [nzDisabled]="!enableQuestionTextMatch"
                        [(ngModel)]="enableQuestionCodeMatch"
                      ></label>
                    </td>
                    <td>
                      <label
                        nz-checkbox
                        [nzDisabled]="!enableQuestionCodeMatch"
                        [(ngModel)]="enableQuestionTextMatch"
                      ></label>
                    </td>
                  </tr>
                  <tr>
                    <td>组织架构</td>
                    <td>
                      <label
                        nz-checkbox
                        [nzDisabled]="!enableOrganizationTextMatch"
                        [(ngModel)]="enableOrganizationCodeMatch"
                      ></label>
                    </td>
                    <td>
                      <label
                        nz-checkbox
                        [nzDisabled]="!enableOrganizationCodeMatch"
                        [(ngModel)]="enableOrganizationTextMatch"
                      ></label>
                    </td>
                  </tr>
                  <tr>
                    <td>人口标签</td>
                    <td>
                      <label
                        nz-checkbox
                        [nzDisabled]="!enableDemographicTextMatch"
                        [(ngModel)]="enableDemographicCodeMatch"
                      ></label>
                    </td>
                    <td>
                      <label
                        nz-checkbox
                        [nzDisabled]="!enableDemographicCodeMatch"
                        [(ngModel)]="enableDemographicTextMatch"
                      ></label>
                    </td>
                  </tr>
                  <tr>
                    <td>维度</td>
                    <!-- <td colspan="2">以基线维度绑定关系为基础，计算历史数据</td> -->
                    <td>
                      <label
                        nz-checkbox
                        [(ngModel)]="enableDimensionCodeMatch"
                      ></label>
                    </td>
                    <td>
                      <label
                        nz-checkbox
                        [(ngModel)]="enableDimensionTextMatch"
                      ></label>
                    </td>
                  </tr>
                </tbody>
              </table>
              <button
                class="mt-16"
                nz-button
                nzType="primary"
                nzShape="round"
                nzBlock
                (click)="generateResult()"
              >
                生成匹配结果
              </button>
              <div class="flex-end">
                <a class="link" (click)="clear()">清空数据</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 中 -->
      <div class="historical_center">
        <div class="card">
          <div class="card_title">
            <span>匹配结果</span>
            <div *ngIf="showResult">
              <nz-upload
                [nzCustomRequest]="customReq"
                [nzShowUploadList]="false"
              >
                <button nz-button nzType="link">
                  <i class="importIcon iconfont icon-icon_import"></i>导入数据
                </button>
              </nz-upload>
              <button nz-button nzType="link" (click)="export()">
                <i class="exportIcon iconfont icon-icon_export"></i>导出数据
              </button>
            </div>
          </div>
          <p *ngIf="quesType == 4" class="card_description">
            基线维度与历史维度匹配，报告中的VS差值=基线维度得分-历史维度得分。即，维度层面的差值，不再按照基线题本重新计算维度。反之，基线维度与历史维度无匹配关系，将基于基线题本VS历史的绑定关系，重新计算历史的维度得分。
          </p>
          <div class="card_content">
            <div class="content-table">
              <ng-container *ngIf="showResult">
                <div class="header">
                  <div class="tableBox">
                    <span
                      [ngClass]="{ active: quesType == item.key }"
                      (click)="changeResData(item.key)"
                      *ngFor="let item of tableList"
                      >{{ item.title }}</span
                    >
                  </div>
                  <p>
                    使用&nbsp;
                    <nz-select
                      [(ngModel)]="matchWay"
                      [nzSize]="'small'"
                      (ngModelChange)="matchWayChange($event)"
                    >
                      <nz-option nzLabel="全部" nzValue="ALL"></nz-option>
                      <nz-option nzLabel="编号" nzValue="CODE"></nz-option>
                      <nz-option
                        nzLabel="文本"
                        nzValue="TEXT"
                      ></nz-option> </nz-select
                    >&nbsp; 计算出匹配度:
                    {{ calculate.matchRate + "%" }}&nbsp;&nbsp;{{
                      calculate.matchNumber + "/" + calculate.totalNumber
                    }}
                  </p>
                </div>
                <div
                  class="row"
                  nz-row
                  [ngClass]="tableBodyHeight > 656 ? 'rowScroll' : 'rowFixd'"
                >
                  <div class="baseline" nz-col nzSpan="9">基线</div>
                  <div
                    class="vs-old ell"
                    nz-col
                    nzSpan="9"
                    nz-tooltip
                    nzTooltipPlacement="top"
                    [nzTooltipTitle]="oldDataName.zh_CN"
                  >
                    VS {{ oldDataName.zh_CN }}
                  </div>
                  <div class="vs-status" nz-col nzSpan="6">状态</div>
                </div>
                <nz-table
                  #fixedTable
                  nzBordered
                  nzShowPagination
                  nzShowSizeChanger
                  nzFrontPagination
                  [(nzPageIndex)]="pageIndex"
                  [nzPageSizeOptions]="[10, 20, 30, 50, 100]"
                  [nzData]="quesList"
                  [nzScroll]="{ y: '580px' }"
                >
                  <thead>
                    <tr>
                      <!-- 基线 -->
                      <th nzWidth="14.9%">
                        <div>
                          <nz-input-group
                            [nzSize]="'small'"
                            [nzSuffix]="prefixTemplateUser"
                          >
                            <input
                              type="text"
                              nz-input
                              placeholder="编号"
                              [(ngModel)]="codeKeyWord"
                              (blur)="searchQuesText()"
                              (keydown.enter)="searchQuesText()"
                            />
                          </nz-input-group>
                          <ng-template
                            ><i nz-icon nzType="search" #prefixTemplateUser></i
                          ></ng-template>
                        </div>
                      </th>
                      <th nzWidth="22%">
                        <nz-input-group
                          [nzSize]="'small'"
                          [nzSuffix]="prefixTemplateUser"
                        >
                          <input
                            type="text"
                            nz-input
                            placeholder="文本"
                            [(ngModel)]="quesKeyWord"
                            (blur)="searchQuesText()"
                            (keydown.enter)="searchQuesText()"
                          />
                        </nz-input-group>
                        <ng-template #prefixTemplateUser
                          ><i nz-icon nzType="search"></i
                        ></ng-template>
                      </th>
                      <th nzWidth="14.9%">
                        <nz-input-group
                          [nzSize]="'small'"
                          [nzSuffix]="prefixTemplateUser"
                        >
                          <input
                            type="text"
                            nz-input
                            placeholder="编号"
                            [(ngModel)]="oldKeyWord"
                            (blur)="searchQuesText()"
                            (keydown.enter)="searchQuesText()"
                          />
                        </nz-input-group>
                        <ng-template #prefixTemplateUser
                          ><i nz-icon nzType="search"></i
                        ></ng-template>
                      </th>
                      <th nzWidth="22%">
                        <nz-input-group
                          [nzSize]="'small'"
                          [nzSuffix]="prefixTemplateUser"
                        >
                          <input
                            type="text"
                            nz-input
                            placeholder="文本"
                            [(ngModel)]="quesOldKeyWord"
                            (blur)="searchQuesText()"
                            (keydown.enter)="searchQuesText()"
                          />
                        </nz-input-group>
                        <ng-template #prefixTemplateUser
                          ><i nz-icon nzType="search"></i
                        ></ng-template>
                      </th>
                      <th nzWidth="18%">
                        <nz-select
                          style="width: 100%;"
                          [(ngModel)]="searchType"
                          [nzSize]="'small'"
                          [nzMaxTagCount]="0"
                          nzMode="multiple"
                          nzPlaceHolder="请选择"
                          [nzMaxTagPlaceholder]="tagPlaceHolder"
                          (ngModelChange)="searchTypeChange($event)"
                        >
                          <!-- <nz-option nzLabel="全部" nzValue="all"></nz-option> -->
                          <nz-option
                            nzLabel="code_T"
                            nzValue="code_T"
                          ></nz-option>
                          <nz-option
                            nzLabel="code_F"
                            nzValue="code_F"
                          ></nz-option>
                          <nz-option
                            nzLabel="文本_T"
                            nzValue="text_T"
                          ></nz-option>
                          <nz-option
                            nzLabel="文本_F"
                            nzValue="text_F"
                          ></nz-option>
                        </nz-select>
                        <ng-template #tagPlaceHolder let-searchType
                          >已选择 {{ searchType.length }} 项
                        </ng-template>
                      </th>
                      <!-- 状态 -->
                      <!-- <th nzWidth="7%" nzRight="0px" nzAlign="center">操作</th> -->
                    </tr>
                  </thead>
                  <tbody id="tableBody">
                    <tr *ngFor="let ques of fixedTable.data; let index = index">
                      <td>{{ ques.code }}</td>
                      <td>{{ ques.name.zh_CN }}</td>
                      <td dragula="PERSON" [(dragulaModel)]="ques.relativeList">
                        <div
                          *ngFor="
                            let childQues of ques.relativeList;
                            let idx = index
                          "
                        >
                          {{ childQues.code }}
                        </div>
                      </td>
                      <!-- <td dragula="PERSON" [(dragulaModel)]="ques.relativeList"> -->
                      <td>
                        <div
                          *ngFor="
                            let childQues of ques.relativeList;
                            let idx = index
                          "
                        >
                          {{ childQues.name.zh_CN }}
                        </div>
                      </td>
                      <!-- <td dragula="PERSON" [(dragulaModel)]="ques.relativeList"> -->
                      <td>
                        <div
                          *ngFor="
                            let childQues of ques.relativeList;
                            let idx = index
                          "
                          class="flex-wrap"
                        >
                          <ng-container>
                            <nz-tag
                              *ngIf="childQues.isCodeMatch"
                              [nzColor]="'green'"
                              >code_T</nz-tag
                            >
                            <nz-tag
                              *ngIf="!childQues.isCodeMatch"
                              [nzColor]="'red'"
                              >code_F</nz-tag
                            >
                          </ng-container>
                          <ng-container>
                            <nz-tag
                              *ngIf="childQues.isTextMatch"
                              [nzColor]="'blue'"
                              >文本_T</nz-tag
                            >
                            <nz-tag
                              *ngIf="!childQues.isTextMatch"
                              [nzColor]="'volcano'"
                              >文本_F</nz-tag
                            >
                          </ng-container>
                        </div>
                      </td>
                      <!-- 删除功能 12.14 取消 -->
                      <!-- <td nzRight="0px">
                        <div
                          *ngFor="
                            let childQues of ques.relativeList;
                            let idx = index
                          "
                        >
                          <a class="del" (click)="del(index, idx)"
                            ><span class="iconfont icon-icon_delete"></span
                          ></a>
                        </div>
                      </td> -->
                    </tr>
                  </tbody>
                </nz-table>
              </ng-container>
              <div *ngIf="!showResult">
                <nz-empty style="margin: 200px;"></nz-empty>
              </div>
            </div>
          </div>
          <!-- <footer class="card_footer" *ngIf="quesList.length !== 0 && projectCount !== 0">
            <nz-pagination
              [nzPageIndex]="page.current"
              [nzTotal]="page.total"
              nzShowSizeChanger
              [nzPageSizeOptions]="[10, 20, 30, 50, 100]"
              [nzPageSize]="page.size"
              (nzPageIndexChange)="pageIndexChange($event)"
              (nzPageSizeChange)="pageSizeChange($event)"
            ></nz-pagination>
          </footer> -->
        </div>
      </div>
      <!-- 右 -->
      <div class="historical_right" *ngIf="false">
        <div class="card" *ngIf="showResult">
          <div class="card_title">
            <span>拖拽内容</span>
          </div>
          <div class="card_content">
            <div
              class="oldTitle ell"
              nz-tooltip
              nzTooltipPlacement="top"
              [nzTooltipTitle]="oldDataName.zh_CN"
            >
              <span>VS {{ oldDataName.zh_CN }}</span>
            </div>
            <div class="tip">提示：选择以下内容，拖拽至左侧对应列表中</div>
            <div style="margin-bottom: 8px;">
              <div>
                <nz-input-group [nzSuffix]="prefixTemplateUser">
                  <input
                    type="text"
                    nz-input
                    placeholder="请输入"
                    [(ngModel)]="dragKeyWord"
                    (blur)="searchdragList()"
                    (keydown.enter)="searchdragList()"
                  />
                </nz-input-group>
                <ng-template #prefixTemplateUser
                  ><i nz-icon nzType="search"></i
                ></ng-template>
              </div>
            </div>
            <ul
              class="scrollUl"
              dragula="PERSON"
              id="right"
              [(dragulaModel)]="oldQuesList"
              *ngIf="oldQuesList"
            >
              <li *ngFor="let ques of oldQuesList; let index = index">
                {{ index + 1 }}、{{ ques.name.zh_CN }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
