<!--
 * @Author: <PERSON> si<PERSON>@knx.com.cn
 * @Date: 2025-06-03 17:20:41
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2025-06-17 11:41:54
 * @FilePath: \tenant\src\app\modules\new-create\topic-distribution-360\topic-distribution-360.component.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<nz-drawer
  class="topicDistribution360"
  nzWrapClassName="round-right-drawer5"
  [nzWidth]="960"
  [(nzVisible)]="isVisible"
  nzTitle="关联设置"
  (nzOnClose)="handleCancel()"
>
<nz-spin [nzSpinning]="isSpinning" >
  <div class="topicDistribution360-content">
    <div class="topicDistribution360-content-card">
      <div class="topicDistribution360-content-card-header">
        <h3>条件</h3>
        <button nz-button nzType="primary" nzGhost class="btn-primary" (click)="onBind()" [disabled]="isShowImport">关联</button>
      </div>
      <div class="topicDistribution360-content-card-body">
        <!-- 被评估人 -->
        <div class="item">
          <div class="item-title">
            <span>被评估人</span>
          </div>
          <div class="item-label">
            <nz-radio-group
              [(ngModel)]="personType"
              nzName="radiogroup"
              style="width: 100%;"
                  (ngModelChange)="changePersonType($event)"
                  [nzDisabled]="!!rightResult.length"
            >
              <label nz-radio nzValue="demographic" style="width: calc(50% - 8px);" *ngIf="personDemographicList.length > 0">
                <nz-select
                  [(ngModel)]="personDemographicActive"
                  nzAllowClear
                  nzPlaceHolder="请选择"
                  style="width: 150px;"
                  (ngModelChange)="changeDemographics($event)"
                  [nzDisabled]="!!rightResult.length"
                >
                <ng-container *ngFor="let item of personDemographicList">
                  <nz-option [nzValue]="item.id" [nzLabel]="item.name[lan]"></nz-option>
                  </ng-container>
                </nz-select>
              </label>
              <label nz-radio nzValue="specific" style="width: calc(50% - 8px);"
                >具体人员</label
              >
            </nz-radio-group>
          </div>
          <div class="item-content" style="padding-bottom: 12px;" 
          *ngIf="!(personType === 'specific'  || personDemographicListChildren.length > 10)"
          >
            <label
            nz-checkbox
            [(ngModel)]="allChecked"
            (ngModelChange)="updateAllChecked()"
            [nzIndeterminate]="indeterminate"
          >
            全选
          </label>
        </div>
          <div class="item-content" >
            <ng-container *ngIf="personType === 'specific'  || personDemographicListChildren.length > 10; else elsePersonBlock">
              <h1>{{personDemographicListChildren.length > 10?'标签内容过多，':''}}请在右侧通过导入/导出的方式上传</h1>
            </ng-container>
            <ng-template #elsePersonBlock>
            
              <nz-checkbox-wrapper style="width: 100%;" (nzOnChange)="onCheckDemographics($event)">
                <div nz-row>
                  <ng-container *ngFor="let item of personDemographicListChildren">
                    <div nz-col nzSpan="12" class="item-content-checkbox">
                      <label
                        nz-checkbox
                        [nzValue]="item.id"
                        [(ngModel)]="item.checked"
                        ><span 
                        nzTooltipPlacement="topLeft"
                        nz-tooltip
                        [nzTooltipTitle]="item.name[lan]">{{ item.name[lan] }}
                        <i nz-icon nzType="check" nzTheme="outline" class="icon-close" style="color: #52c41a;" *ngIf="personDemographicBinds.includes(item.id)"></i>
                      </span></label
                      >
                    </div>
                  </ng-container>
                </div>
              </nz-checkbox-wrapper>
            </ng-template>
          </div>
        </div>
        <!-- 评估人 -->
        <div class="item">
          <div class="item-title">
            <span>评估人</span>
          </div>
          <div class="item-label">
            <nz-radio-group
              [(ngModel)]="investigatorType"
              nzName="radiogroup"
              style="width: 100%;"
              (ngModelChange)="changeInvType($event)"
              [nzDisabled]="!!rightResult.length"
            >
              <label nz-radio nzValue="role" style="width: calc(50% - 8px);">
                角色
              </label>
              <label nz-radio nzValue="specific" style="width: calc(50% - 8px);"
                >具体人员</label
              >
            </nz-radio-group>
          </div>
          <div class="item-content">
            <ng-container *ngIf="investigatorType === 'specific'; else elseInvestigatorBlock">
              <h1>请在右侧通过导入/导出的方式上传</h1>
            </ng-container>
            <ng-template #elseInvestigatorBlock>
              <nz-checkbox-wrapper style="width: 100%;" (nzOnChange)="onCheckRoles($event)">
                <div nz-row>
                  <ng-container *ngFor="let item of investigatorsurveyRoles">
                    <div nz-col nzSpan="12" class="item-content-checkbox">
                      <label
                        nz-checkbox
                        [nzValue]="item.id"
                        [(ngModel)]="item.checked"
                        ><span 
                        nzTooltipPlacement="topLeft"
                        nz-tooltip
                        [nzTooltipTitle]="item.name[lan]">{{ item.name[lan] }}
                        <i nz-icon nzType="check" nzTheme="outline" class="icon-close" style="color: #52c41a;" *ngIf="investigatorsurveyRolesBinds.includes(item.id)"></i>
                      </span></label
                      >
                    </div>
                  </ng-container>
                </div>
              </nz-checkbox-wrapper>
            </ng-template>
          </div>
        </div>
        <!-- 维度/题目 -->
        <div class="item">
          <div class="item-title">
            <span>维度/题目</span>
          </div>
          <div class="item-label">
            <nz-radio-group
              [(ngModel)]="type"
              nzName="radiogroup"
              style="width: 100%;"
              (ngModelChange)="changeDimOrQues($event)"
              [nzDisabled]="!!rightResult.length"
            >
              <label nz-radio nzValue="dimensions" style="width: calc(50% - 8px);">
                维度
              </label>
              <label nz-radio nzValue="questions" style="width: calc(50% - 8px);"
                >题目</label
              >
            </nz-radio-group>
          </div>
          <div class="item-content">
            <ng-container *ngIf="type === 'questions'; else elseBlock">
              <h1>请在右侧通过导入/导出的方式上传</h1>
            </ng-container>
            <ng-template #elseBlock>
              <nz-checkbox-wrapper style="width: 100%;" (nzOnChange)="onCheckDimensions($event)">
                <div nz-row>
                  <ng-container *ngFor="let item of dimensions">
                    <div nz-col nzSpan="12" class="item-content-checkbox">
                      <label
                        nz-checkbox
                        [nzValue]="item.dimensionCode"
                        [(ngModel)]="item.checked"
                        >
                        <span 
                        nzTooltipPlacement="topLeft"
                        nz-tooltip
                        [nzTooltipTitle]="item.dimensionName">{{ item.dimensionName }}
                        <i nz-icon nzType="check" nzTheme="outline" class="icon-close" style="color: #52c41a;" *ngIf="dimensionsBinds.includes(item.dimensionCode)"></i>
                      </span>
                        </label
                      >
                    </div>
                  </ng-container>
                </div>
              </nz-checkbox-wrapper>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
    <div class="topicDistribution360-content-card">
      <div class="topicDistribution360-content-card-header">
        <h3>结果<span>被评估人-评估人角色-维度</span></h3>
        <button class="btn-link" (click)="onClear()">清空</button>
      </div>
      <div class="topicDistribution360-content-card-body">
        <!-- 上传 -->
        <ng-container *ngIf="isShowImport && !isShowDownload">
          <div class="template-3">
            <div class="template-3-empty">
              <img
                src="assets/images/topic-distribution-360-empty.png"
                alt=""
              />
              <p>内容过多展示不下，请通过表格配置后上传</p>
              <nz-upload
                [nzCustomRequest]="onDispenseImport"
                [nzShowUploadList]="false"
              >
              <button
                nz-button
                nzType="primary"
                style="min-width: 88px;margin-bottom: 20px;"
              >导入
              </button>
            </nz-upload>
              <button nz-button nzType="link" 
              [nzLoading]="isDispenseDownLoadSpinning"
              (click)="onDispenseDownLoad()" *ngIf="type === 'dimensions'">导出模板</button>
              <button nz-button nzType="link" 
              [nzLoading]="isDispenseDownLoadSpinning"
              (click)="onDispenseQuestionDownLoad()" *ngIf="type === 'questions'">导出模板</button>
            </div>
            <!-- <div class="template-3-tip">
              <span
                ><i
                  nz-icon
                  nzType="check-circle"
                  nzTheme="fill"
                  class="success"
                ></i
                >上传成功</span
              >
              <span
                ><i
                  nz-icon
                  nzType="close-circle"
                  nzTheme="fill"
                  class="error"
                ></i
                >上传失败和其他报错信息</span
              >
            </div> -->
          </div>
        </ng-container>
        <!-- 列表 -->
        <ng-container *ngIf="isShowImport && isShowDownload">
          <!-- <div class="template-4">
            <div class="template-4-file">
              <i nz-icon nzType="link" nzTheme="outline"></i>
              <p
                class="ing"
                nz-tooltip
                nzTooltipPlacement="topLeft"
                [nzTooltipTitle]="
                  '文件名称文件名称文件名称文件名称文件名称文件名称文件名称文件名称'
                "
              >
                文件名称文件名称文件名称文件名称文件名称文件名称文件名称文件名称
              </p>
              <span>.xls</span>
            </div>
            <div class="template-4-progress">
              <div class="template-4-progress-bar ">
                <nz-progress
                  [nzStrokeWidth]="6"
                  [nzPercent]="50"
                  nzStatus="active"
                  [nzShowInfo]="false"
                ></nz-progress>
              </div>
              <span class="template-4-progress-text">上传中...</span>
            </div>
          </div> -->
          <div class="template-4">
            <div class="template-4-file">
              <i nz-icon nzType="link" nzTheme="outline"></i>
              <p>{{fileName}}_{{type === 'dimensions' ? '维度' : '题目'}}分发表</p>
              <span>.xls</span>
            </div>
            <div class="template-4-btns">
              <!-- 下载 -->
              <button nz-button nzType="link" style="padding: 0;" nzSize="small"
              [nzLoading]="isDispenseDownLoadSpinning"
              (click)="onDispenseDownLoad()" *ngIf="type === 'dimensions'">
              <i class="iconfont icon-icon_import"></i>
              </button>
              <button nz-button nzType="link" style="padding: 0;" nzSize="small"
              [nzLoading]="isDispenseDownLoadSpinning"
              (click)="onDispenseQuestionDownLoad()" *ngIf="type === 'questions'">
              <i class="iconfont icon-icon_import"></i>
              </button>
              <!-- 删除 -->
              <button nz-button nzType="link" style="padding: 0; margin-left: 16px;" nzSize="small" (click)="onClear()">
                <i class="iconfont icon-icon_delete"></i>
              </button>
            </div>
          </div>
        </ng-container>
        <ng-container  *ngIf="!isShowImport">
        <!-- 配置 -->
          <!-- list-导入导出 -->
          <div class="template-1">
            <nz-upload [nzCustomRequest]="onDispenseImport" [nzShowUploadList]="false">
            <button nz-button nzType="link" style="padding: 0;" nzSize="small">
              <i class="iconfont icon-import"></i> 导入模板
            </button>
            </nz-upload>
            <button nz-button nzType="link" style="padding: 0;" nzSize="small"
            [nzLoading]="isDispenseDownLoadSpinning"
            (click)="onDispenseDownLoad()" *ngIf="type === 'dimensions'">
              <i class="iconfont icon-export_ic"></i> 导出模板
            </button>
            <button nz-button nzType="link" style="padding: 0;" nzSize="small"
            [nzLoading]="isDispenseDownLoadSpinning"
            (click)="onDispenseQuestionDownLoad()" *ngIf="type === 'questions'">
              <i class="iconfont icon-export_ic"></i> 导出模板
            </button>
          </div>
        <!-- 暂无数据占位 -->
        <div *ngIf="!rightResult.length" style="height: calc(100% - 45px);">
          <app-empty ></app-empty>
        </div>
        <ng-container *ngIf="rightResult.length">
          <ng-container *ngFor="let val of rightResult">
            <div class="template-2">
              <div class="template-2-title">
                <span *ngIf="!val.group[0].investigatorParentDemId">所有被评估人，【{{val.group[0].roleName}}】评价以下维度</span>
                <span *ngIf="val.group[0].investigatorParentDemId">被评估人【{{val.group[0].investigatorChildDemIdsName}}】时，他的【{{val.group[0].roleName}}】评价以下维度</span>
              </div>
              <div class="template-2-content" >
                <!-- <nz-tag nzMode="closeable" *ngFor="let item of val.group">{{item.dimensionName}}</nz-tag> -->
                <nz-tag *ngFor="let item of val.group">{{item.dimensionName}}</nz-tag>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </ng-container>
      </div>
    </div>
  </div>
</nz-spin>
  <div class="footer">
    <button nz-button  (click)="handleCancel()" [nzLoading]="isSpinning">
      取消
    </button>
    <button nz-button nzType="primary" (click)="onSave()" [nzLoading]="isSpinning">
      确认
    </button>
  </div>
</nz-drawer>