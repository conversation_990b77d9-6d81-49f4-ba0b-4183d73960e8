///<reference path="../../../../../node_modules/@angular/router/router.d.ts"/>
import {
  Component,
  ChangeDetectionStrategy,
  Input,
  Inject,
  OnInit,
  ChangeDetectorRef,
  Output,
  EventEmitter,
  SimpleChanges,
} from "@angular/core";
import { SettingsService } from "@knz/theme";
import { ReuseTabService } from "@knz/assembly";
import { ActivatedRoute, Router } from "@angular/router";
import { DA_SERVICE_TOKEN, ITokenService } from "@knz/auth";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { LoginService } from "../../../modules/login/login.service";
import { Subscription } from "rxjs/Subscription";
import { type } from "os";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "layout-header",
  templateUrl: "./header.component.html",
  styleUrls: ["./header.component.less"],
})
export class HeaderComponent implements OnInit {
  @Input() indexStyle: boolean = false;
  @Input() background: boolean = true;
  @Input() tourist: boolean = false;
  @Input() isNeedLogin: boolean = true;
  @Output() getreturn = new EventEmitter<any>();
  @Input() showtitle: boolean = false;
  @Input() showhidden;
  permission;
  searchToggleStatus: boolean;
  routerEventsListener: any;
  toKen = this.tokenService.get();
  user: any;
  title: string;
  subscription: Subscription;
  showindex = 0;
  showdiv = false;
  showdiv2 = false;

  constructor(
    public settings: SettingsService,
    private reuseTabService: ReuseTabService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private http: HttpClient,
    private ref: ChangeDetectorRef,
    private router: Router,
    private loginService: LoginService,
    private routeInfo: ActivatedRoute,
    public permissionService: PermissionService
  ) {
    this.subscription = this.loginService.userObservable.subscribe(
      (res: any) => {
        // console.log(res,'12312312312')
        if (res === "err") {
          this.indexStyle = true;
          this.background = false;
        } else {
          this.user = res.data.username;
          this.indexStyle = false;
          this.background = true;
        }
      },
      (res) => {
        this.loginService.emitUser(res);
      }
    );
  }

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    let type = this.routeInfo.snapshot.queryParams["type"];
    if (this.isNeedLogin && type !== "email") {
      this.loginService.login().subscribe((res) => {
        if (res.result.code === 0) {
           // 将用户信息存储到sessionStorage
          sessionStorage.setItem("userInfo", JSON.stringify(res));
          this.loginService.emitUser(res);
        }
      });
    }
  }
  ngOnChanges(changesQuestion: SimpleChanges) {
    this.showindex = this.showhidden;
  }

  toggleCollapsedSidebar() {
    this.settings.setLayout("collapsed", !this.settings.layout.collapsed);
  }

  searchToggleChange() {
    this.searchToggleStatus = !this.searchToggleStatus;
  }

  logOut() {
    // 清空路由复用信息
    localStorage.removeItem("token");
    sessionStorage.removeItem("permission");
    sessionStorage.removeItem("noviceGuidance");
    // sessionStorage.clear();
    this.reuseTabService.clear();
    // 设置用户Token信息
    this.tokenService.set(null);
    this.router.navigateByUrl("/user");
  }

  /**
   * 点击logo跳转：登录-首页，未登录-游客端
   */
  gotoHome() {
    if (this.toKen.token) {
      this.router.navigate(["/home"]);
    } else {
      this.router.navigate(["/tourist/home"]);
    }
  }

  getchoose(i) {
    this.showindex = i;
    this.showhidden = i;

    console.log(this.showindex);
    if (this.showindex == 1) {
      this.showdiv = !this.showdiv;
      this.showdiv2 = false;
    }
    if (this.showindex == 2) {
      this.showdiv2 = !this.showdiv2;
      this.showdiv = false;
    }
    // console.log(this.showindex)
    // console.log(this.showdiv)
    // this.getreturn.emit(this.showindex)
  }
}
