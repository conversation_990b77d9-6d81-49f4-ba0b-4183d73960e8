.container {
  margin: 0 auto;
}

header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
  padding: 12px 0 0;

  h2 {
    font-size: 24px;
    color: #17314c;
  }

  .savebtn {
    width: 118px;
    height: 36px;
    background: #409eff;
    border-radius: 8px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
    text-align: center;
    cursor: pointer;
  }
}

.ell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: default;
}

.historical {
  display: flex;
  justify-content: space-between;

  .ell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: default;
  }

  .mt-16 {
    margin-top: 16px;
  }

  .mb-16 {
    margin-bottom: 16px;
  }

  .flex-end {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  &_left {
    width: 240px;
    margin-right: 16px;
  }

  &_center {
    width: 944px;
  }

  &_right {
    width: 180px;
  }

  .card {
    width: 100%;
    background: #ffffff;
    border-radius: 4px;
    padding: 24px 0 16px 0;

    &_title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        &:nth-child(1) {
          height: 25px;
          font-size: 18px;
          font-weight: 500;
          color: #17314c;
          line-height: 25px;
          border-left: 5px solid #409eff;
          padding-left: 16px;

          > i {
            margin-left: 8px;
            color: #c4c4c4;
            font-size: 16px;
          }
        }

        &:nth-child(2) {
          font-size: 14px;
          font-weight: 400;
          color: #495970;
          margin-right: 16px;
          cursor: pointer;

          > i {
            margin-right: 2px;
            color: #495970;
            font-size: 16px;
          }
        }
      }

      .ant-btn {
        padding: 0 12px;
      }

      .importIcon,
      .exportIcon {
        display: inline-block;
        transform: rotate(90deg);
        margin-right: 6px;
      }
    }

    &_description {
      font-size: 13px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #262626;
      line-height: 18px;
      padding: 0 16px 0 21px;
    }

    &_content {
      margin: 16px 16px 0 16px;

      .link {
        font-size: 12px;
        font-weight: 400;
        color: #419eff;
        margin: 8px 8px 0 8px;
      }

      .btns {
        display: flex;
        justify-content: space-between;

        button {
          width: 100px;
        }

        // margin-bottom: 86px;
      }

      ::ng-deep {
        .ant-descriptions-item-label {
          padding: 16px 6px;
          font-size: 12px;
          color: #17314c;
        }

        .ant-descriptions-item-content {
          padding: 16px 8px;
        }
      }

      table {
        border: 1px solid #e2e2e2;
        background: #ffffff;
        border-radius: 4px;
        width: 100%;

        thead {
          background: #f3f3f3;
          border-bottom: 1px solid #e6e6e6;

          tr {
            th {
              border-right: 1px solid #e6e6e6;
              font-size: 14px;
              font-weight: 400;
              color: #495970;
              padding: 16px 15px;
              text-align: center;
              width: 33%;

              &:nth-child(1) {
                text-align: left;
              }

              &:nth-last-child(1) {
                border-right: none;
              }
            }
          }
        }

        tbody {
          tr {
            border-bottom: 1px solid #e6e6e6;

            td {
              border-right: 1px solid #e6e6e6;
              text-align: center;

              &:nth-child(1) {
                text-align: left;
                font-size: 12px;
                font-weight: 500;
                color: #17314c;
                padding: 16px 0 16px 12px;
                cursor: pointer;
              }

              &:nth-last-child(1) {
                border-right: none;
              }
            }

            &:nth-last-child(1) {
              border-bottom: none;

              td {
                font-size: 12px;
                font-weight: 500;
                color: #17314c;
                padding: 8px;

                &:nth-child(1) {
                  text-align: left;
                  font-size: 12px;
                  font-weight: 500;
                  color: #17314c;
                  padding: 16px 0 16px 12px;
                  cursor: text;
                }
              }
            }
          }
        }

        .active {
          // background-color: #F2F9FF;
        }
      }

      // 中
      .content-table {
        .header {
          height: 35px;
          margin-bottom: 9px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid #ececec;

          .tableBox {
            span {
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #595959;
              line-height: 22px;
              margin-right: 40px;
              position: relative;
              cursor: pointer;

              &.active {
                color: #409eff;

                &::after {
                  content: "";
                  position: absolute;
                  bottom: -8px;
                  left: 50%;
                  transform: translateX(-50%);
                  width: 100%;
                  height: 4px;
                  background: #409eff;
                  border-radius: 2px;
                }
              }
            }
          }

          nz-select {
            width: 80px;
          }

          span,
          p {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #17314c;
          }
        }

        .row {
          margin-bottom: 10px;

          div {
            padding: 0 8px;
          }

          .baseline {
            background-color: #419eff;
            color: #ffffff;
          }

          .vs-old {
            background-color: #45bfd9;
            color: #ffffff;
          }

          .vs-status {
            background-color: #efbc30;
            color: #ffffff;
          }

          div {
            text-align: center;
          }
        }

        .rowFixd {
          width: 100%;
        }

        .rowScroll {
          width: 100%;
          padding-right: 8px;
          background-color: #efbc30;
        }

        .title {
          border-top: 1px solid #e6e6e6;
          border-left: 1px solid #e6e6e6;
          border-right: 1px solid #e6e6e6;
          background: #f3f3f3;
          // margin-left: 0 !important;
          // margin-right: 0 !important;
          height: 48px;

          div {
            // margin-left: 0 !important;
            // margin-right: 0 !important;
            height: 100%;
          }

          &_col {
            height: 100%;
            display: flex;
            align-items: center;
            border-right: 1px solid #e6e6e6;
          }
        }

        .mlr-8 {
          margin: 0 8px;
        }

        .flex-wrap {
          display: flex;
          justify-content: flex-start;
          flex-wrap: wrap !important;
        }

        ::ng-deep {
          .ant-tag {
            margin-right: 2px;
            margin-bottom: 2px;
          }

          .ant-table-thead > tr > th,
          .ant-table-tbody > tr > td {
            padding: 16px 7px;
            color: #17314c;
            word-break: break-word;

            &:nth-last-child(1) {
              text-align: center;
            }
          }

          .ant-table-body {
            border-bottom: 1px solid #e6e6e6;
          }

          .ant-table-placeholder {
            border-right: 1px solid #e6e6e6;
            border-left: 1px solid #e6e6e6;
          }

          .ant-table-header-column {
            width: 100%;
          }

          .ant-tag {
            border: none;
            padding: 1px 6px;
          }
        }

        .del {
          color: #17314c;

          &:hover {
            color: #ff4d4f;
          }
        }
      }

      // 右侧
      .oldTitle {
        width: 100%;
        background: linear-gradient(
          90deg,
          rgba(38, 208, 241, 0.15) 0%,
          rgba(64, 158, 255, 0.15) 100%
        );
        border-radius: 2px;
        padding: 4px 7px;
        text-align: center;

        span {
          font-size: 14px;
          font-weight: 600;
          background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .tip {
        background: #fffbf0;
        border-radius: 2px;
        border: 1px solid #ffeec1;
        font-size: 12px;
        font-weight: 400;
        color: #f09600;
        padding: 4px 7px;
        line-height: 17px;
        margin: 8px 0;
        cursor: default;
      }

      .scrollUl {
        margin-top: 8px 0;
        overflow-y: auto;
        max-height: 660px;

        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        // 滑块背景
        &::-webkit-scrollbar-track {
          // background-color: transparent;
          background-color: #f1f1f1;
          box-shadow: none;
        }
        // 滑块
        &::-webkit-scrollbar-thumb {
          // background-color: #e9e9e9;
          background-color: #c1c1c1;
          outline: none;
          -webkit-border-radius: 2px;
          -moz-border-radius: 2px;
          border-radius: 2px;
        }

        li {
          background: #f3f3f3;
          border: 1px solid #f3f3f3;
          border-radius: 2px;
          font-size: 12px;
          font-weight: 400;
          color: #17314c;
          line-height: 20px;
          margin-bottom: 4px;
          cursor: move;

          &:nth-last-child(1) {
            margin-bottom: 0;
          }

          &:hover {
            background: rgba(64, 158, 255, 0.07);
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
            border-color: #b7daff;
            cursor: move;
          }

          &:active {
            background: rgba(64, 158, 255, 0.07);
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
            border-color: #b7daff;
            cursor: move;
          }

          padding: 8px;
        }
      }
    }

    &_footer {
      display: flex;
      justify-content: end;
      align-items: center;
      padding-top: 16px;
      padding-right: 8px;
    }
  }
}

// 落点样式
// .gu-transit{
// opacity: 0.9;
// -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
// filter: alpha(opacity=20);
// background-color: gold;
// border: 1px solid #B7DAFF;
// }
// 拖拽中样式
.gu-mirror {
  background: rgba(64, 158, 255, 0.07);
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
  border: 1px solid #b7daff;
  border-radius: 2px;
  font-size: 12px;
  font-weight: 400;
  color: #17314c;
  line-height: 20px;
  padding: 8px;
  cursor: move;
}
