<div class="index " style="background: #ffff;height: 100%;">
  <div class="content client-width">
    <div
      style="display: flex;justify-content: space-between;align-items: center;"
    >
      <div class=" active_titile">
        <p class="index-title">
          {{ title }}
          <img
            style="cursor: pointer;"
            src="assets/images/shownew.png"
            (click)="getnewlead()"
            alt=""
          />
        </p>

        <div class="put_div">
          <nz-input-group [nzSuffix]="suffixIconSearch_new" class="search">
            <input
              style="border: 1px solid #E4E4E4;"
              type="text"
              nz-input
              placeholder="请输入关键词"
              [(ngModel)]="keyWord"
              (keydown.enter)="getcardone()"
            />
          </nz-input-group>
          <ng-template #suffixIconSearch_new>
            <img src="assets/images/new_search.png" />
          </ng-template>
        </div>
      </div>
      <div>
        <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
      </div>
    </div>

    <div class="choose_list">
      <ul class="flex_ul">
        <li>
          <nz-tabset
            (nzSelectChange)="nzSelectChange($event)"
            style="width: 100%;"
            [nzAnimated]="true"
          >
            <nz-tab
              *ngFor="let tab of tabs; let i = index"
              [nzTitle]="titleTemplate"
            >
              <ng-template #titleTemplate>
                <!-- <i nz-icon [nzType]="tab.icon"></i> -->
                <img [hidden]="tabshowindex != i" [attr.src]="tab.img" alt="" />
                <img
                  [hidden]="tabshowindex == i"
                  [attr.src]="tab.icon"
                  alt=""
                />
                <span style="margin-left: 8px;">
                  {{ tab.name }}
                </span>
              </ng-template>
              <div class="clear_div" (click)="clearchoosed(i)">
                清空条件
              </div>
              <ul class="new_create">
                <li style="display: flex;">
                  <div class="news_span_f">
                    <div>场景</div>
                  </div>
                  <div class="check_div">
                    <nz-checkbox-wrapper (nzOnChange)="nzOnChangeStype($event)">
                      <div nz-row class="row_div" style="position: relative;">
                        <div
                          style="padding:0 15px;"
                          *ngFor="let item of sceneTypes; let i = index"
                          [ngClass]="unique == i ? 'uniqueclass' : ''"
                        >
                          <label
                            nz-checkbox
                            [nzValue]="item.id"
                            (ngModelChange)="
                              ngModelChange(item.id, item.checked, i)
                            "
                            [(ngModel)]="item.checked"
                            [(nzIndeterminate)]="item.indeterminate"
                          >
                          </label>
                          <span
                            class="news_span"
                            (click)="uniqueshow(item.id, item.checked, i)"
                            >{{ item.name }}</span
                          >
                        </div>
                      </div>
                    </nz-checkbox-wrapper>
                    <div class="children_div" *ngIf="childrenlist.length != 0">
                      <nz-checkbox-wrapper
                        (nzOnChange)="nzOnChangechildren($event)"
                      >
                        <label
                          *ngFor="let res of childrenlist"
                          nz-checkbox
                          [nzValue]="res.id"
                          [(ngModel)]="res.checked"
                          (ngModelChange)="updateSingleChecked(res)"
                          >{{ res.name }}
                        </label>
                      </nz-checkbox-wrapper>
                    </div>
                  </div>
                </li>
                <li style="border-top: 1px solid #E2E2E2;">
                  <div class="news_span_f">
                    <div>类型</div>
                  </div>
                  <div class="check_div">
                    <nz-checkbox-wrapper (nzOnChange)="nzOnChangeQtype($event)">
                      <div nz-row class="row_div">
                        <div
                          nz-col
                          *ngFor="let item of questionnaireTypes; let i = index"
                          class="list_div"
                        >
                          <label
                            nz-checkbox
                            [nzValue]="item.id"
                            [(ngModel)]="item.checked"
                            >{{ item.name }}</label
                          >
                        </div>
                      </div>
                    </nz-checkbox-wrapper>
                  </div>
                </li>
              </ul>

              <ul class="select_tags">
                <li>
                  <span>已选工具：</span>
                  <ng-container *ngFor="let item of choosecards; let i = index">
                    <nz-tag
                      nzMode="closeable"
                      (nzOnClose)="onClose(i)"
                      nz-tooltip
                      [nzTooltipTitle]="item.name.zh_CN"
                    >
                      {{ item.name.zh_CN }}
                    </nz-tag>
                  </ng-container>
                  <span *ngIf="choosecards.length == 0">
                    暂未选择
                  </span>
                </li>
              </ul>

              <ul class="end_ul">
                <div class="tool_title">
                  <li class="left_li">
                    <span
                      style="cursor: pointer;"
                      [ngClass]="
                        OrderBy.indexOf('COMPREHENSIVE') != -1
                          ? 'font_color'
                          : ''
                      "
                      (click)="showsortup('COMPREHENSIVE_DESC')"
                      >综合排序</span
                    >
                    <div class="san_div">
                      <span
                        [ngClass]="
                          OrderBy.indexOf('LATEST') != -1 ? 'font_color' : ''
                        "
                        >最新</span
                      >
                      <li class="img_flex">
                        <img
                          *ngIf="OrderBy != 'LATEST_ASC'"
                          src="assets/images/san_g.png"
                          alt=""
                          (click)="showsortup('LATEST_ASC')"
                        />
                        <img
                          *ngIf="OrderBy == 'LATEST_ASC'"
                          class="tans_img"
                          src="assets/images/san_b.png"
                          alt=""
                        />

                        <img
                          *ngIf="OrderBy != 'LATEST_DESC'"
                          style=" margin-top: 3px;"
                          class="tans_img"
                          src="assets/images/san_g.png"
                          alt=""
                          (click)="showsortup('LATEST_DESC')"
                        />
                        <img
                          *ngIf="OrderBy == 'LATEST_DESC'"
                          style=" margin-top: 3px;"
                          src="assets/images/san_b.png"
                          alt=""
                        />
                      </li>
                    </div>

                    <div class="san_div">
                      <span
                        [ngClass]="
                          OrderBy.indexOf('POPULARITY') != -1
                            ? 'font_color'
                            : ''
                        "
                        >人气</span
                      >
                      <li class="img_flex">
                        <img
                          *ngIf="OrderBy != 'POPULARITY_ASC'"
                          src="assets/images/san_g.png"
                          alt=""
                          (click)="showsortup('POPULARITY_ASC')"
                        />
                        <img
                          *ngIf="OrderBy == 'POPULARITY_ASC'"
                          class="tans_img"
                          src="assets/images/san_b.png"
                          alt=""
                        />

                        <img
                          *ngIf="OrderBy != 'POPULARITY_DESC'"
                          style=" margin-top: 3px;"
                          class="tans_img"
                          src="assets/images/san_g.png"
                          alt=""
                          (click)="showsortup('POPULARITY_DESC')"
                        />
                        <img
                          *ngIf="OrderBy == 'POPULARITY_DESC'"
                          style=" margin-top: 3px;"
                          src="assets/images/san_b.png"
                          alt=""
                        />
                      </li>
                    </div>
                  </li>
                  <li style="display: flex;">
                    <div class="tool_right">
                      <div (click)="gettoolmap()">
                        <img
                          src="assets/images/tool_map.png"
                          style="width: 20px;height: 18px;margin-right: 5px;"
                          alt=""
                        />
                        <span>人才地图</span>
                      </div>
                      <div style="margin-left: 30px;" (click)="getmylike()">
                        <img
                          src="assets/images/tool_like.png"
                          style="width: 18px;height: 16px;margin-right: 5px;"
                          alt=""
                        />
                        <span>我的专属</span>
                      </div>
                    </div>
                    <div
                      style="margin-left: 20px;display: flex;align-items: center;"
                    >
                      <i
                        nz-icon
                        nzType="left"
                        nzTheme="outline"
                        style="cursor: pointer;"
                        (click)="addnext('left')"
                      ></i>
                      <span
                        class="body_span"
                        style="font-size: 16px;margin: 0 5px;"
                        >{{ PageIndex }}/{{ PageNumber }}</span
                      >
                      <i
                        nz-icon
                        nzType="right"
                        nzTheme="outline"
                        style="cursor: pointer;"
                        (click)="addnext('right')"
                      ></i>
                    </div>
                  </li>
                </div>
                <div
                  style="background: #FBFBFB;height: 50%;padding: 0 20px;display: flex;align-items: center;"
                >
                  <nz-checkbox-wrapper
                    style="width: 100%;"
                    (nzOnChange)="selectlog($event)"
                  >
                    <div nz-row>
                      <div nz-col nzSpan="4">
                        <label
                          nz-checkbox
                          [nzValue]="'热卖'"
                          [(ngModel)]="hotsale"
                          >热卖</label
                        >
                      </div>
                      <div nz-col nzSpan="4">
                        <label
                          nz-checkbox
                          [nzValue]="'在线建模'"
                          [(ngModel)]="online"
                          >在线建模</label
                        >
                      </div>
                    </div>
                  </nz-checkbox-wrapper>
                </div>
              </ul>
              <div style="padding-bottom: 70px;" *ngIf="toolstands.length != 0">
                <div
                  *ngIf="SimpleSpin"
                  style="width: 100%;height:500px;display: flex;justify-content: center;align-items: center;"
                >
                  <nz-spin nzSimple [nzSize]="'large'"></nz-spin>
                </div>
                <div *ngIf="!SimpleSpin">
                  <app-tool-card
                    [tabshowindex]="tabshowindex"
                    [toolslist]="toolstands"
                    [nolike]="'no'"
                    [keyWordlist]="keyWordlist"
                    [chooselistids]="chooselistids"
                    [fl_num]="3"
                    (selectcard)="selectcard($event)"
                    (getReturn)="getReturn($event)"
                  >
                  </app-tool-card>
                  <ul class="pagination">
                    <nz-pagination
                      [(nzPageIndex)]="PageIndex"
                      [nzTotal]="PageTotal"
                      nzShowSizeChanger
                      [nzHideOnSinglePage]="true"
                      [nzPageSizeOptions]="[9, 12, 15, 18]"
                      [(nzPageSize)]="PageSize"
                      (nzPageIndexChange)="nzPageIndexChange()"
                      (nzPageSizeChange)="nzPageSizeChange()"
                    >
                    </nz-pagination>
                  </ul>
                </div>
              </div>
            </nz-tab>
          </nz-tabset>
        </li>
      </ul>
    </div>

    <div class="search_none" *ngIf="keyWordlist.length == 0">
      <img src="assets/images/nolistnew.png" alt="" />
      <p class="one_word">暂无活动工具</p>
      <p class="two_word">
        当前条件暂无搜索到活动工具，请刷新后重新选择，或点击右上角“清空条件”，进行选择。
      </p>
      <div class="btn_release" (click)="getrelease()">
        刷新页面
      </div>
    </div>

    <div>
      <nz-modal
        [(nzVisible)]="isVisible"
        nzTitle="我的专属"
        (nzOnCancel)="likeCancel()"
        (nzOnOk)="likeOk()"
        nzWidth="800px"
      >
        <div
          style="background-color: #f5f7fa; height: 60px; display: flex; align-items: center;"
        >
          <div class="put_div">
            <nz-input-group [nzSuffix]="suffixIconSearch_new" class="search">
              <input
                style="border: 1px solid #E4E4E4;"
                type="text"
                nz-input
                placeholder="请输入关键词"
                [(ngModel)]="likekeyWord"
                (keydown.enter)="searchMy()"
              />
            </nz-input-group>
            <ng-template #suffixIconSearch_new>
              <img src="assets/images/new_search.png" />
            </ng-template>

            <!-- 
          <nz-input-group [nzPrefix]="suffixIconSearch" class="search">
            <input type="text" nz-input placeholder="请输入关键词" [(ngModel)]="likekeyWord" (keydown.enter)="searchMy()" />
          </nz-input-group>
          <ng-template #suffixIconSearch>
            <img src="assets/images/icon_search.png">
          </ng-template> -->
          </div>
        </div>

        <div class="modal_div scroll" *ngIf="mylikelist.length != 0">
          <app-tool-card
            [tabshowindex]="tabshowindex"
            [fl_num]="2"
            [toolslist]="mylikelist"
            [chooselistids]="chooselistids"
            (selectcard)="selectcard($event)"
          >
          </app-tool-card>
        </div>
        <div *ngIf="mylikelist.length == 0">
          <nz-empty></nz-empty>
        </div>
      </nz-modal>
    </div>

    <div>
      <nz-modal
        [(nzVisible)]="mapVisible"
        nzTitle="人才地图"
        [nzFooter]="null"
        (nzOnCancel)="mapCancel()"
        nzWidth="800px"
      >
        <div class="map_table" style="padding:15px ;">
          <ul class="tab_card">
            <li>
              <div *ngFor="let item of transverse" style="text-align: center;">
                {{ item.name }}
              </div>
            </li>
            <li *ngFor="let item of portrait; let i = index">
              <div
                *ngFor="let res of transverse; let j = index"
                style="display: flex;justify-content: center;align-items: center;"
              >
                <span *ngIf="j == 0">{{ item.name }}</span>

                <div *ngIf="j == 1 && item.isxiao" style="height: 100%;">
                  <img
                    src="assets/images/bingo.png"
                    style="width: 100%;height: 30px;"
                    alt=""
                  />
                </div>
                <div *ngIf="j == 2 && item.isShe">
                  <img
                    src="assets/images/bingo.png"
                    style="width: 100%;height: 30px;"
                    alt=""
                  />
                </div>
                <div *ngIf="j == 3 && item.ispan">
                  <img
                    src="assets/images/bingo.png"
                    style="width: 100%;height: 30px;"
                    alt=""
                  />
                </div>
                <div *ngIf="j == 4 && item.isxuan">
                  <img
                    src="assets/images/bingo.png"
                    style="width: 100%;height: 30px;"
                    alt=""
                  />
                </div>
                <div *ngIf="j == 5 && item.ispei">
                  <img
                    src="assets/images/bingo.png"
                    style="width: 100%;height: 30px;"
                    alt=""
                  />
                </div>
                <div *ngIf="j == 6 && item.isjob">
                  <img
                    src="assets/images/bingo.png"
                    style="width: 100%;height: 30px;"
                    alt=""
                  />
                </div>
                <div *ngIf="j == 7 && item.isteam">
                  <img
                    src="assets/images/bingo.png"
                    style="width: 100%;height: 30px;"
                    alt=""
                  />
                </div>
                <div *ngIf="j == 8 && item.isprisma">
                  <img
                    src="assets/images/bingo.png"
                    style="width: 100%;height: 30px;"
                    alt=""
                  />
                </div>
              </div>
            </li>
          </ul>
        </div>
      </nz-modal>
    </div>
  </div>
  <div class="nz_spin" *ngIf="loadingspin">
    <nz-spin nzSimple [nzSpinning]="loadingspin" [nzSize]="'large'"></nz-spin>
  </div>
</div>
<div class="settlement">
  <div class="content client-width flex space-between">
    <p class="p1">K米将在报告生成时，自动从您的账户中扣除</p>
    <div *ngIf="choosecards.length != 0">
      <button
        nz-button
        class="btn"
        (click)="submit()"
        *knxFunctionPermission="'SAG:TENANT:PROJECT_MGT:CREATE_SELECT_CONFIRM'"
      >
        确认
      </button>
    </div>
    <div *ngIf="choosecards.length == 0">
      <button
        nz-button
        class="btn_w"
        (click)="submit()"
        *knxFunctionPermission="'SAG:TENANT:PROJECT_MGT:CREATE_SELECT_CONFIRM'"
      >
        确认
      </button>
    </div>
  </div>
</div>

<div class="mock_div " *ngIf="showmock">
  <ul class="bg_ul" *ngIf="showmock"></ul>
  <ul class="img_ul" *ngIf="noviceGuidance">
    <li>
      <div style="position: relative;">
        <img src="assets/images/create.png" alt="" />
      </div>
      <div style="margin-top: 20px;cursor: pointer;" (click)="closed()">
        <img src="assets/images/dele_new.png" alt="" />
      </div>
    </li>
  </ul>
</div>
