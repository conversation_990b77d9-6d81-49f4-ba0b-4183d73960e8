import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Router } from "@angular/router";
import { NzMessageService } from "ng-zorro-antd";
import { TouristService } from '../tourist.service';

@Component({
  selector: 'app-ama',
  templateUrl: './ama.component.html',
  styleUrls: ['./ama.component.less']
})
export class AmaComponent implements OnInit {
  tourist:boolean=true;
  isNeedLogin:boolean=false;
  token:any;
  _token:any;
  constructor(private http: HttpClient,private router: Router,private message: NzMessageService,private TouristService:TouristService) { }

  ngOnInit() {
    this._token = JSON.parse(localStorage.getItem("_token"));
    if (this._token) {
        this.tourist=false;
        this.isNeedLogin=true;
    }
  }
  downloadReport(){
    this.TouristService.downLoad('ama');
  }
}
