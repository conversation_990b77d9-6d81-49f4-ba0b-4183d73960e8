import { Component, OnInit, Input, OnD<PERSON>roy } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd";
import _ from "lodash";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { NewPrismaService } from "../new-prisma.service";
import { NzDrawerRef } from "ng-zorro-antd/drawer";
import { DragulaService } from "ng2-dragula";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-cross-analysis",
  templateUrl: "./cross-analysis.component.html",
  styleUrls: ["./cross-analysis.component.less"],
})
export class CrossAnalysisComponent implements OnInit, OnDestroy {
  @Input() projectId: string;

  constructor(
    private api: NewPrismaService,
    private msgServ: NzMessageService,
    private drawerRef: NzDrawerRef,
    private dragulaService: DragulaService,
    private customMsg: MessageService,
    private router: Router
  ) {}
  private routerSubscription: Subscription;
  loading = false;
  isSpinning = false;
  // 左侧-分类
  classifyOptions = [
    { label: "部门", value: "DEPT", checked: false },
    { label: "人口标签", value: "DEMOGRAPHIC", checked: false },
  ];
  classifyCheckedKey = "";
  // 左侧-分类-下沉组织
  organizationLevelOptions = [
    { label: "下一级", value: "NEXT_LEVEL", checked: false },
    { label: "下两级", value: "TWO_LEVELS_LOWER", checked: false },
    { label: "下到最细", value: "DOWN_TO_THE_FINEST", checked: false },
  ];
  organizationLevel = "";
  organizationLevelOptionsMap = {};
  // 左侧-对比数据
  correlationOptions = [
    { label: "分析主体", value: "ANALYSIS_MAIN", checked: false },
    { label: "公司整体", value: "COMPANY_ALL", checked: false },
    { label: "上一级部门", value: "SUP_DEPARTMENT", checked: false },
    { label: "外部主常模", value: "OUTSIDE_NORM", checked: false },
  ];
  correlationOptionsMap = {
    ANALYSIS_MAIN: "分析主体",
    COMPANY_ALL: "公司整体",
    SUP_DEPARTMENT: "上一级部门",
    OUTSIDE_NORM: "外部常模",
  };
  correlationCheckedKey = "";
  // 左侧-指数数据-横纵轴
  indexOptions = [];
  indexOptionsMap = {};
  XKey = "";
  YKey = "";
  // 右侧
  rightAllData = [];
  rightDataSort1 = [];
  rightDataSort2 = [];
  // 部门
  // rightDataSort1_1 = []; // 默认
  // rightDataSort1_2 = []; // 自定义
  // 人口标签
  // rightDataSort2_1 = []; // 默认
  // rightDataSort2_2 = []; // 自定义
  ngOnInit() {
    this.loadData();
    this.routerSubscriptionFun();
    this.dragulaService.createGroup('CROSS_ANALYSIS_1', {
      moves: function (el) {
        // 如果元素包含 'no-drag' 类，则禁止拖拽
        return !el.classList.contains('no-drag');
      },
      accepts: (el, target, source, sibling) => {
        // 如果目标位置的元素有 no-drag 类，则禁止拖入
        return !sibling || !sibling.classList.contains('no-drag');
      }
    });
    this.dragulaService.createGroup('CROSS_ANALYSIS_2', {
      moves: function (el) {
        // 如果元素包含 'no-drag' 类，则禁止拖拽
        return !el.classList.contains('no-drag');
      },
      accepts: (el, target, source, sibling) => {
        // 如果目标位置的元素有 no-drag 类，则禁止拖入
        return !sibling || !sibling.classList.contains('no-drag');
      }
    });
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy() {
    // 销毁事件
    this.dragulaService.destroy("CROSS_ANALYSIS_1");
    this.dragulaService.destroy("CROSS_ANALYSIS_2");
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
  loadData() {
    this.api.getCrosslistAllLabels(this.projectId).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.indexOptions = res.data.map((val) => ({
          label: val.name.zh_CN,
          value: val.id,
          checked: false,
          disabled: false,
        }));
        this.indexOptions.forEach((item) => {
          this.indexOptionsMap[item.value] = item.label;
        });
        this.organizationLevelOptions.forEach((item) => {
          this.organizationLevelOptionsMap[item.value] = item.label;
        });
      }
    });
    this.initRightData();
  }
  // 左侧-清空
  onClearLeft() {
    this.classifyCheckedKey = "";
    this.correlationCheckedKey = "";
    this.XKey = "";
    this.YKey = "";
    this.indexOptions.forEach((val) => {
      val.disabled = false;
      val.checked = false;
    });
  }
  initRightData() {
    this.isSpinning = true;
    this.api
      .getReportCrossAnalysisConfig(this.projectId)
      .subscribe((res: any) => {
        if (res.result.code === 0) {
          console.log("获取交叉分析配置", res.data);
          this.rightAllData = res.data;
          this.dataSortFun();
          this.isSpinning = false;
        } else {
          this.isSpinning = false;
        }
      });
  }
  // 恢复默认
  onClearRight() {
    this.isSpinning = true;
    this.api
      .getListDefaultCrossAnalysisConfig(this.projectId)
      .subscribe((res: any) => {
        if (res.result.code === 0) {
          console.log("恢复默认", res.data);
          this.rightAllData = res.data.map(val => ({...val, id: Math.round(Math.random() * 900000 + 100000)}));
          this.dataSortFun();
          this.isSpinning = false;
        } else {
          this.isSpinning = false;
        }
      });
  }
  onChangeIndex() {
    const checked = this.indexOptions
      .filter((val) => val.checked)
      .map((val) => val.value);
    if (checked.length > 1) {
      if (checked.length === 2) {
        this.YKey = checked.filter((val) => val !== this.XKey)[0];
      }
      this.indexOptions.forEach((val) => {
        val.disabled = ![this.XKey, this.YKey].includes(val.value);
      });
    } else {
      if (checked.length === 0) {
        this.XKey = "";
        this.YKey = "";
      } else if (checked.length === 1) {
        this.XKey = checked[0];
        this.YKey = "";
      }
      this.indexOptions.forEach((val) => {
        val.disabled = false;
      });
    }
  }
  onChangeClass(e) {
    if (e !== "DEPT") {
      this.organizationLevel = "";
    } else {
      this.organizationLevel = "NEXT_LEVEL";
    }
  }
  // 关联
  onRelation() {
    if (!this.classifyCheckedKey) {
      this.customMsg.open("error", "请选择分类！");
      return;
    }
    if (this.classifyCheckedKey === "DEPT" && !this.organizationLevel) {
      this.customMsg.open("error", "请选择分类！");
      return;
    }
    if (!this.correlationCheckedKey) {
      this.customMsg.open("error", "请选择下沉组织！");
      return;
    }
    if (!this.XKey && !this.YKey) {
      this.customMsg.open("error", "请选择横纵轴！");
      return;
    } else {
      if (!this.XKey) {
        this.customMsg.open("error", "请选择横纵轴-X轴！");
        return;
      } else if (!this.YKey) {
        this.customMsg.open("error", "请选择横纵轴-Y轴！");
        return;
      }
    }
    const id = Math.round(Math.random() * 900000 + 100000);
    const obj = {
      categoryType: this.classifyCheckedKey,
      compareDataCode: this.correlationCheckedKey,
      organizationLevel: this.organizationLevel,
      x: this.XKey,
      y: this.YKey,
      sort:
        this.classifyCheckedKey === "DEPT"
          ? this.rightDataSort1.length + 1
          : this.rightDataSort2.length + 1,
      id: `t-${id}`,
    };
    if (obj.categoryType === "DEPT") {
     const findIndex = this.rightAllData.findIndex(
        (val) =>
          val.categoryType === obj.categoryType &&
          val.compareDataCode === obj.compareDataCode &&
          val.organizationLevel === obj.organizationLevel &&
          val.x === obj.x &&
          val.y === obj.y
      );
       if (findIndex >= 0) {
      // this.msgServ.error('当前结果已存在，请重新配置！');
      this.customMsg.open("error", "当前组合（分类+对比数据+部门层级+X轴+Y轴）已存在，请检查并重新配置！");
      return;
    }
    } else {
     const findIndex = this.rightAllData.findIndex(
        (val) =>
          val.categoryType === obj.categoryType &&
          val.compareDataCode === obj.compareDataCode &&
          val.x === obj.x &&
          val.y === obj.y
      );
      if (findIndex >= 0) {
        // this.msgServ.error('当前结果已存在，请重新配置！');
        this.customMsg.open("error", "当前组合（分类+对比数据+X轴+Y轴）已存在，请检查并重新配置！");
        return;
      }
     }
   
    this.rightAllData.push(obj);
    this.dataSortFun();
    this.onClearLeft();
  }
  // 右侧-数据分类
  dataSortFun() {
    this.rightDataSort1 = this.rightAllData.filter(
      (val) => val.categoryType === "DEPT"
    ).sort((a, b) => {
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      return 0;
    });
    this.rightDataSort2 = this.rightAllData.filter(
      (val) => val.categoryType === "DEMOGRAPHIC"
    ).sort((a, b) => {
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      return 0;
    });
  }
  // 右侧-删除
  onDel(item) {
    this.rightAllData = this.rightAllData.filter((val) => val.id !== item.id);
    if (item.categoryType === "DEPT") {
      this.rightDataSort1 = this.rightDataSort1.filter(
        (val) => val.id !== item.id
      );
    } else {
      this.rightDataSort2 = this.rightDataSort2.filter(
        (val) => val.id !== item.id
      );
    }
  }
  // 右侧-拖拽
  dragulaChangeSort1(e) {
    e.forEach((val, index) => {
      val.sort = index + 1;
    });
    this.rightAllData = [
      ...e,
      ...this.rightAllData.filter((val) => val.categoryType === "DEMOGRAPHIC"),
    ];
  }
  dragulaChangeSort2(e) {
    e.forEach((val, index) => {
      val.sort = index + 1;
    });
    this.rightAllData = [
      ...this.rightAllData.filter((val) => val.categoryType === "DEPT"),
      ...e,
    ];
  }
  // 确认
  onConfirm() {
    this.loading = true;
    const allData = this.rightAllData.map((val) => ({
      categoryType: val.categoryType,
      categoryTypeName: val.categoryTypeName,
      compareDataCode: val.compareDataCode,
      compareDataName: val.compareDataName,
      isDefault: val.isDefault,
      organizationLevel: val.organizationLevel,
      sort: val.sort,
      x: val.x,
      y: val.y,
    }));
    // 验证 categoryType, compareDataCode, x, y 的唯一性
    const seenCombinations = new Set<string>();
    let hasDuplicate = false;

    for (const data of allData) {
      const combination =`${data.categoryType}-${data.compareDataCode}`+(data.categoryType==="DEPT"? `-${data.organizationLevel}`:``)+`-${data.x}-${data.y}`;
      if (seenCombinations.has(combination)) {
        hasDuplicate = true;
        break;
      }
      seenCombinations.add(combination);
    }

    if (hasDuplicate) {
      this.loading = false;
      this.customMsg.open(
        "error",
        "存在重复的组合（分类+对比数据+X轴+Y轴），请检查并重新配置！"
      );
      return;
    }
    const params = {
      projectId: this.projectId,
      reportCrossAnalysisConfigVoList: allData,
    };
    console.log(JSON.stringify(params))
    this.api.saveReportCrossAnalysisConfig(params).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.loading = false;
        this.msgServ.success("交叉分析保存成功！");
        this.drawerRef.close();
      } else {
        this.loading = false;
      }
    });
  }
  // 关闭
  onClose() {
    this.drawerRef.close();
  }
}
