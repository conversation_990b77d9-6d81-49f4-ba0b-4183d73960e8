/*import custom font*/
.ant-drawer-body{
  position: relative;
}
.container {
    // background-color: cornflowerblue;
    // margin-top: 20px;
    height: auto;
    .icon-icon- {
      margin-left: 20px;
      cursor: pointer;
    }
    .icon-icon-:hover {
      color: orangered;
    }
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
  
        .text {
            height: 33px;
            font-size: 28px;
            font-family: PingFangSC-Light, PingFang SC;
            font-weight: 500;
            color: #262626;
            line-height: 33px;
            white-space: nowrap;
            overflow: hidden;
        }
        &::after {
          position: absolute;
          content: '';
          top: 65px;
          left: 0;
          background-color: rgba(236, 236, 236, 1);
          width: 100%;
          height: 1px;
        }
    }
    
    .subject {
        // margin-top: 10px;
        margin-bottom: 10px;
        .text {
            height: 33px;
            font-size: 16px;
            font-family: PingFangSC-Light, PingFang SC;
            font-weight: 500;
            color: #262626;
            line-height: 33px;
            white-space: nowrap;
            overflow: hidden;
        }
        .message {
            font-size: 14px;
            font-weight: 400;
        }
    }
    .condition_main {
        margin-bottom: 30px;
    }
    .condition {
        .add_button {
            width: 80px;
         color: #409EFF;
         font-size: 14px;
        }
    }
    .condition_detail {
        margin-top: 5px;
    }
    .foot_btn {
        position: fixed;
        bottom: 10px;
    }
  
  //滚动条
  .vxscrollbar() {
      scrollbar-color: auto;
  scrollbar-width: auto;
    overflow-y: overlay;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    // 滑块背景
    &::-webkit-scrollbar-track {
      // background-color: transparent;
      background-color: #F1F1F1;
      box-shadow: none;
    }
    // 滑块
    &::-webkit-scrollbar-thumb {
      // background-color: #e9e9e9;
      background-color: #C1C1C1;
      outline: none;
      -webkit-border-radius: 2px;
      -moz-border-radius: 2px;
      border-radius: 2px;
    }
  }
}
 
.drawer-footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
} 