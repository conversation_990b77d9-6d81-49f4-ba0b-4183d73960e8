.content {
  flex: 4;

  .details {
    background-color: #f8f8f8;
    border-radius: 4px;
    line-height: 45px;
    font-size: 13px;
    padding-left: 10px;

    nz-input-number {
      width: 60px;
      margin-left: 5px;
    }

    nz-select {
      width: 60px;
    }

    .label {
      margin-left: 10px;
    }
  }
}

.newcontent {
  flex: 2;

  // border-left: 1px solid #E6E6E6;
  .details {
    background-color: #f8f8f8;
    border-radius: 4px;
    line-height: 45px;
    font-size: 13px;
    padding-left: 10px;
  }
}

.act_tit {
  font-size: 16px;
  line-height: 14px;
  font-weight: bold;
  margin: 0 0 20px 0;
}

.ant-radio-wrapper {
  margin-right: 0;
}

.tipIcon {
  margin-right: 8px;
  font-size: 22px;
  color: #c4c4c4;
  vertical-align: middle;
  margin-left: -4px;
  cursor: pointer;
}

// .footer {
//   margin-top: 20px;
//   border-top: solid 1px #E6E6E6;
//   padding-top: 20px;
//   display: flex;
//   justify-content: space-between;

//   .iptBtn {
//     width: 128px;
//     height: 38px;
//     background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
//     box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
//     border-radius: 19px;
//     font-weight: 500;
//     color: #FFFFFF;
//   }

// }

.disable {
  pointer-events: none;
  opacity: 0.5;
}

::ng-deep .cdk-overlay-pane:has(.tooltip_div_tip) {
  .ant-popover-title {
    padding: 0;
    border-bottom: 0px solid rgba(236, 236, 236, 1);

    .tooltip_div_tip {
      width: 375px;
      height: auto;
    }
  }

  .ant-popover-inner-content {
    padding: 0;
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}

.showSettingView {
  background-image: url(../../../../assets/images/shownew.png);
  background-size: 100% 100%;
  width: 30px;
  height: 30px;
  position: relative;
  color: transparent;
  vertical-align: middle;
  margin-left: 4px;
  cursor: pointer;
}
::ng-deep .cdk-overlay-pane:has(.tooltip_div_view) {
  .ant-popover-title {
    padding: 5;
    border-bottom: 0px solid rgba(236, 236, 236, 1);
  }
}
 .show-person-details {
   background-color: #f8f8f8;
   border-radius: 4px;
   line-height: 45px;
   font-size: 13px;
   padding-left: 24px;

   .label {
     margin-left: 10px;
   }
 }


//显示设置
 .show-setting-tabs .tab-header {
   display: flex;
   }

 .show-setting-tabs .tab-item {
   padding: 8px 20px;
   cursor: pointer;
   background: #fff;
   border-radius: 6px 6px 0 0;
   margin-right: 8px;
   transition: background 0.2s;
 }

 .show-setting-tabs .tab-item.active {
   background: #f5f5f5;
 }

 .show-setting-tabs .tab-content {
   background: #f5f5f5;
   border-radius: 0 0 6px 6px;
   margin-top: -1px;
   display: none;
 }

 .show-setting-tabs .tab-content.active {
   display: block;
   padding: 16px;
   min-height: 48px;
 }
 .show-setting-tabs .tab-content.active-none {
  display: block;
}

 //得分设置
 .score-setting-tabs .tab-header {
   display: flex;
 }

 .score-setting-tabs .tab-item {
   padding: 8px 20px;
   cursor: pointer;
   background: #fff;
   border-radius: 6px 6px 0 0;
   margin-right: 8px;
   transition: background 0.2s;
 }

 .score-setting-tabs .tab-item.active {
   background: #f5f5f5;
 }

 .score-setting-tabs .tab-content {
   background: #f5f5f5;
   border-radius: 0 0 6px 6px;
   margin-top: -1px;
   display: none;
 }

 .score-setting-tabs .tab-content.active {
   display: block;
   padding: 16px;
   min-height: 48px;
 }
 .score-setting-tabs .tab-content.active label {
  margin-left: 0px !important;
}
.score-setting-tabs .tab-content.active-none {
  display: block;
}

//评语设置
 .comment-setting-tabs .tab-header {
   display: flex;
 }

 .comment-setting-tabs .tab-item {
   padding: 8px 20px;
   cursor: pointer;
   background: #fff;
   border-radius: 6px 6px 0 0;
   margin-right: 8px;
   transition: background 0.2s;
 }

 .comment-setting-tabs .tab-item.active {
   background: #f5f5f5;
 }

 .comment-setting-tabs .tab-content {
   background: #f5f5f5;
   border-radius: 0 0 6px 6px;
   margin-top: -1px;
   display: none;
 }

 .comment-setting-tabs .tab-content.active {
   display: block;
   padding: 16px;
   min-height: 48px;
 }
.comment-setting-tabs .tab-content.active-none {
  display: block;
}

:host ::ng-deep .tab-content .editBtn {
  margin-left: 20px !important;
}
.ant-input-number {
  width: 60px !important;
}