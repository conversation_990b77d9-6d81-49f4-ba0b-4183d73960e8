import {
  Component,
  <PERSON><PERSON>ni<PERSON>,
  ViewChild,
  ViewContainerRef,
  ElementRef,
  Inject,
} from "@angular/core";
import { Router, ActivatedRoute, NavigationEnd } from "@angular/router";
import { ProjectManageService } from "../../service/project-manage.service";
import {
  NzModalService,
  NzMessageService,
  NzDrawerService,
} from "ng-zorro-antd";
import { UploadXHRArgs } from "ng-zorro-antd/upload";
import _ from "lodash";
import { RoleComponent } from "./role/role.component";
import { RoleNewComponent } from "./role-new/role-new.component";
import { ComponentService } from "../../../shared/component.service";
import { HttpResponseWrapper } from "../../../shared/interfaces/http-common";
import { DownloadUtilService } from "@src/modules/service/download-util.service";
import * as XLSX from "xlsx";
import { concat, Observable } from "rxjs";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { ChooseInvitorComponent } from "./choose-invitor/choose-invitor.component";
import { SelfInviteComponent } from "./self-invite/self-invite.component";
import { isString } from "util";
import { copy } from "clipboard";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";
const JSZip = require("jszip");

function isEmpty(value: string): boolean {
  if (value != undefined) {
    return value.trim() === "";
  }
}
type inviteSetting = {
  roleId: string;
  min: number;
  max: number;
  disableLimit: boolean;
};

interface ParentItemData {
  id?: string;
  key: string;
  name: string;
  code: string;
  email: string;
  phone: string;
  roleId: number | string;
  percentage: string;
  weights: number;
  isSelfEvaluation: boolean;
  persons: any[];
  roleslistnew: any[];
  roleWeightList: any[];
  submitroles: any[];
  projectId: string;
  expand: boolean;
  disabled: boolean;
  inviteSettings: inviteSetting[];
}

interface FormData {
  [key: string]: any;
  emailList: Array<{ email: string; id: string; phone: string }>;
}
declare const document: any;
@Component({
  selector: "app-project-manage-invite360",
  templateUrl: "./project-manage-invite360.component.html",
  styleUrls: ["./project-manage-invite360.component.less"],
})
export class ProjectManageInvite360Component implements OnInit {
  @ViewChild("settingHost", { read: ViewContainerRef, static: true })
  private settingHost: ViewContainerRef;
  @ViewChild(SelfInviteComponent, { static: false })
  selfInvite: SelfInviteComponent;
  tenantName: string="";
  isFetching = false;
  precision = 2;
  isLoadingOne = false;
  tenantUrl: string = "/tenant-api";

  constructor(
    private routerInfo: ActivatedRoute,
    private api: ProjectManageService,
    private router: Router,
    private modalService: NzModalService,
    private common: ComponentService,
    private http: HttpClient,
    private msg: NzMessageService,
    private downUtil: DownloadUtilService,
    private el: ElementRef,
    private drawerService: NzDrawerService,
    private customMsg: MessageService,
    public permissionService: PermissionService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService
  ) {
    this.navigationSubscription = this.router.events.subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        this.initLoad(event);
      }
    });
  }
  tabs: any[] = [
    { id: "single", name: "分享邀请码", active: false },
    { id: "multi", name: "发送邮件/短信邀请", active: false },
  ];
  effect = "scrollx";
  countMoney: string = "0";

  singleYuan;
  peopleNum;

  showType = true;
  showcCode: number = 1;
  invitecode: string = "";
  invitecode_one: string = "";
  oneindex: number = 0;
  codename: string = "公共码";
  Investigators = [];
  Investigatorsoption = [];
  downloadlist = [];
  selectedValue = null;
  roleslist = [];
  firstNameId: number = 0;
  codemenu = false;
  textarea: string = "";
  tabsetIndex: number = 0;
  fileIds: any[] = []; // 附件ids
  fileList: any[] = []; // 批量上传的文件列表
  fileListAttachment: any[] = []; // 批量上传的文件列表
  keyWord: string = "";
  pageStart: number = 1;
  pageEnd: number = 10;

  strNum: number = 0;

  //评价关系上传相关
  uploadList: any[] = []; // 评价关系批量上传的文件列表
  uploadSearchList: any[] = []; // 评价关系批量上传的文件列表
  searchShow: boolean = false;
  pageIndex: number = 1; // 当前页码

  navigationSubscription; //路由监听
  keyword: string; //搜索关键字
  listOfParentData: ParentItemData[] = [];
  projectId: string; //活动id
  projectname: string; //活动名称
  projectCode: string; //活动编码
  step; //当前邀请第几步
  allNoInvitedUser = []; //所有未邀请人名单

  rolelist: any[] = [];
  num: number = 0;
  messageData: string = "";
  formData: FormData = {
    inviteDate: new Date(), //邀请日期
    inviteTime: new Date(), //邀请时间
    userType: "", //指定人specified 全部all
    content: {}, //邮件内容
    emailList: [], //发送人员列表
    title: {}, //邮件主题,
    codeType: "one",
  };
  isInviteAnswer: boolean = false; // 是否自邀请
  isInviteReviewEvaluatee: boolean = false; // 是否审核

  nextAfterSave: boolean = false;
  // 调查者id
  investigatorId: string = "";
  // 测评者id
  personId: string = "";
  // 判断当前页面是做何种操作
  action: "edit" | "reSendEmail" = null;
  // url上获取邮件地址
  email: string = "";
  title: "邀请" | "评价关系";

  sourceData; //原始数据
  backtype = "";
  projectType = "";
  questionnaireId = "";
  PageSize = 5;
  CurrentPage = 1;
  PageTotal = 0;
  Projectstatus = "";
  standardReportType = "";
  isCustomRoleWeight = false;
  isQuickMutualEvaluation : boolean = false;
  addtyceshow = false;
  eamilTemplate = [];
  radioValue = "";
  chooseformData: any = {
    content: {
      zh_CN: "",
      en_US: "",
    },
    name: "",
    valueId: "",
    isDefault: false,
    type: "",
    isStandard: false,
    id: "",
    editors: false,
    theme: {
      zh_CN: "",
      en_US: "",
    },
  };
  chooseformDataTemplate: any = {
    content: {
      zh_CN: "",
      en_US: "",
    },
    name: "",
    valueId: "",
    isDefault: false,
    type: "",
    isStandard: false,
    id: "",
    editors: false,
    theme: {
      zh_CN: "",
      en_US: "",
    },
  };
  isEnableWxWorkMsg: boolean = false;
  isEnableDingMsg: boolean = false;
  isEnableFeishuMsg: boolean = false;

  permission: boolean;
  standardQuestionnaireId;
  tinyconfig = {};

  showcards = false;
  multipleValue = [];
  listSimilar = [];
  ismergeproject = false;
  showprojectcard = null;
  buttonload = false;
  longCodeNum;
  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/project-manage",
      name: "活动管理",
      Highlight: false,
    },
    {
      path: "",
      name: "邀请",
      Highlight: true,
    },
  ];
  queryParams = {};
  isLoadingspend = false;
  isAuditPersonText = "审核人";
  currentInviteSettings = []; // 当前邀请设置
  currentkey = ""; // 当前邀请设置
  isDisplaySMS = false; //短信功能是否展示
  isDisplayMail = false; //邮件功能是否展示
  saveLoading = false; //保存按钮添加防抖
  mailTotal = {
    mailStrNum: 0,
    peopleNum: 0,
    countMoney: 0,
  };
  availableLanguages = [];
  // 模板type
  templateTypeMap = {
    1: "MAIL", // 邮件
    2: "SMS", // 短信
    3: "THIRD_PARTY_ENTERPRISE_WECHAT", // 企微
    4: "DING_DING", // 钉钉
    5: "THIRD_PARTY_LARK_TEXT_CARD", // 飞书
  };
  templateTypeNameMap = {
    1: "邮件", // 邮件
    2: "短信", // 短信
    3: "企业微信", // 企微
    4: "钉钉", // 钉钉
    5: "飞书", // 飞书
  };
  messagePrefix = {
    zh_CN: "【肯耐珂萨】",
    en_US: "【肯耐珂萨】",
  };
  messageSuffix = {
    zh_CN: "拒收请回复R",
    en_US: "拒收请回复R",
  };
  // isHideMask = false; // 是否开启分享码遮罩（人口标签姓名隐藏）
  ngOnInit() {
    console.info("测评-360");
    this.getLans(this.routerInfo.snapshot.queryParams.projectId);
    // 合并发送邀请相关是否展示以及默认值
    this.getShowTypeCode();
    const _this = this;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.tinyconfig = {
      images_upload_url: `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`, // 配置你图片上传的url
      fontsize_formats:
        "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 32pt 36pt",
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.http
          .post(
            `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`,
            formData,
            { headers: headers }
          )
          .subscribe(
            (response: any) => {
              if (response) {
                this.http
                  .get(
                    `${this.tenantUrl}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                    { headers: headers }
                  )
                  .subscribe((imgurl: any) => {
                    let baseUrl: string = window.location.origin + "/";
                    if (baseUrl.indexOf("http://localhost") !== -1) {
                      baseUrl = "https://sag-qa.knxdevelop.com/";
                      // baseUrl = "http://***********/";;
                    }
                    let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                    this.getMailTotal();
                    success(url);
                  });
              } else {
                if (response && response.rtnMsg) {
                  failure(response.rtnMsg);
                } else {
                  failure("上传失败：未知错误");
                }
              }
            },
            (error1) => {
              failure("上传失败：未知错误");
            }
          );
      },
      init_instance_callback: (editor) => {
        editor.on("input", (e) => {
          this.getMailTotal();
        });
        editor.on("paste", (e) => {
          this.getMailTotal();
        });

        editor.on("ExecCommand", (e) => {
          this.getMailTotal();
        });
      },
    };
    // this.common.message('invited');
    this.Projectstatus = this.routerInfo.snapshot.queryParams.listChecked;
    this.standardReportType = this.routerInfo.snapshot.queryParams.standardReportType;
    this.action = this.routerInfo.snapshot.queryParams.action;
    this.backtype = this.routerInfo.snapshot.queryParams.type;
    this.projectType = this.routerInfo.snapshot.queryParams.projectType;
    this.questionnaireId = this.routerInfo.snapshot.queryParams.questionnaireId;
    this.standardQuestionnaireId = this.routerInfo.snapshot.queryParams.standardQuestionnaireId;
    const { email } = this.routerInfo.snapshot.queryParams;
    const { id } = this.routerInfo.snapshot.queryParams;
    const { phone } = this.routerInfo.snapshot.queryParams;
    if (this.backtype == "create") {
      this.queryParams = this.routerInfo.snapshot.queryParams;
      this.Breadcrumbs = JSON.parse(localStorage.getItem("break"));
      this.Breadcrumbs.forEach((item) => {
        if (item.Highlight) {
          item.path = "/new-create";
          item.Highlight = false;
        }
      });
      this.Breadcrumbs.push({
        path: "",
        name: "评价关系",
        Highlight: true,
      });
    }

    if (email && id) {
      this.formData.emailList.push({
        email,
        id,
        phone,
      });
    }
    this.projectId = this.routerInfo.snapshot.queryParams.projectId;
    this.projectname = this.routerInfo.snapshot.queryParams.name;
    this.projectCode = this.routerInfo.snapshot.queryParams.projectCode;
    this.isCustomRoleWeight = this.routerInfo.snapshot.queryParams.isCustomRoleWeight;
    this.step = this.routerInfo.snapshot.queryParams.step;
    this.title = this.step === "1" ? "评价关系" : "邀请";
    this.investigatorId = this.routerInfo.snapshot.queryParams.investigatorId;
    this.personId = this.routerInfo.snapshot.queryParams.personId;
    this.listOfParentData = [];
    this.api.getLongestPersonCaptcha(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.longCodeNum = res.data;
      }
    });

    if (this.step == 2) {
      let backUrl = localStorage.getItem("backurl");
      this.queryParams = this.routerInfo.snapshot.queryParams;
      this.Breadcrumbs = JSON.parse(localStorage.getItem("break"));
      this.Breadcrumbs.forEach((item) => {
        if (item.Highlight) {
          if (item.name == "活动管理") {
            item.path = "/project-manage/home";
          }
          if (item.name == "活动详情") {
            item.path = backUrl;
          }
        }
        item.Highlight = false;
      });
      this.Breadcrumbs.push({
        path: "",
        name: "邀请",
        Highlight: true,
      });
      this.getcaptchacode();
    }

    this.getlistmodalMail();
    this.api.getDetail(this.projectId).subscribe((res) => {
      this.isInviteAnswer = res.data.isInviteAnswer;
      this.isInviteReviewEvaluatee = res.data.isInviteReviewEvaluatee;
      this.isQuickMutualEvaluation = !this.isInviteAnswer && res.data.isQuickMutualEvaluation;
    });
    // 判断当前页面做何种操作
    // 1: 重新发送邮件,将数据回显
    // 2: 编辑时,将数据回显
    let subRole: Observable<any> = this.api.listRole(this.projectId);
    let subNext: Observable<any> = this.http.get(
      `${this.tenantUrl}/survey/person/getSurveyInvestigator/${this.investigatorId}`
    );

    if (this.action === "reSendEmail") {
      subNext = this.http.get(
        `${this.tenantUrl}/survey/person/getInvestigatorPerson/${this.investigatorId}/${this.personId}`
      );
    } else if (this.action === "edit") {
      subNext = this.http.get(
        `${this.tenantUrl}/survey/person/getSurveyInvestigator/${this.investigatorId}`
      );
    }
    let seq: number = 1;
    if (this.action === "edit") {
      if (this.investigatorId) {
        concat(subRole, subNext).subscribe((data) => {
          if (data.result.code == 0) {
            if (seq === 1) {
              this.getRoleList(data);
            } else if (this.action === "reSendEmail") {
              this.showDataForReSendEmail(data);
            } else if (this.action === "edit") {
              this.editSurvey(data);
            }
            seq++;
          }
        });
      } else {
        concat(subRole).subscribe((data) => {
          if (data.result.code == 0) {
            this.getRoleList(data);
          }
        });
      }
    } else {
      concat(subRole).subscribe((data) => {
        if (data.result.code == 0) {
          this.getRoleList(data);
        }
      });
    }
      // 获取sessionStorage中的用户信息
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
    this.tenantName = userInfo.data.name;
  }

  getEmailThemeName(defaultTitle) {
    let templateType = this.templateTypeMap[this.showcCode];
    this.api
      .getLatestMailSubject({
        projectId: this.projectId,
        type: templateType,
        purpose: "ANSWER",
      })
      .subscribe((res) => {
        if (res.result.code === 0) {
          if (res.data) {
            if (res.data.zh_CN) {
              this.formData.title.zh_CN = res.data.zh_CN || defaultTitle.zh_CN;
            }
            if (res.data.en_US) {
              this.formData.title.en_US = res.data.en_US || defaultTitle.en_US;
            }
          }
        }
      });
  }

  /**
   * getBase64 获取图片base64
   * @param img
   * @param callback
   */
  private getBase64(url, success) {
    var that = this;
    var image = new Image();
    image.src = url + "?v=" + Math.random(); // 处理缓存
    image.crossOrigin = "*"; // 支持跨域图片
    var base64;
    image.onload = function() {
      base64 = that.drawBase64Image(image);
      success(base64);
    };
  }
  drawBase64Image(img) {
    var canvas = document.createElement("canvas");
    canvas.width = img.width;
    canvas.height = img.height;
    var ctx = canvas.getContext("2d");
    ctx.drawImage(img, 0, 0, img.width, img.height);
    var dataURL = canvas.toDataURL("image/png");
    return dataURL;
  }

  ngOnDestroy(): void {
    // 销毁navigationSubscription，避免内存泄漏
    if (this.navigationSubscription) {
      this.navigationSubscription.unsubscribe();
    }
  }
  async listInvestigatorGroup(): Promise<any> {
    const res = await this.api
      .listInvestigatorGroup(this.projectId)
      .toPromise();
    if (res.result.code === 0) {
      //
      this.Investigators = res.data;
      this.downloadlist = res.data;
      this.Investigatorsoption = res.data;
      this.Investigatorsoption.forEach((res, index) => {
        res.indexId = index;
      });

      this.invitecode_one = this.Investigators[0].inviteUrl;
      // roleslist
    }
  }

  getnewroleslist(id) {
    let data = {
      projectId: id,
      page: {
        size: this.PageSize,
        current: this.CurrentPage,
      },
    };
    const sourceData: Array<any> = [];
    // this.listOfParentData = []
    this.api.newconfirmRelation(data).subscribe((res) => {
      if (res.result.code == 0) {
        // this.PageTotal = res.page.pages * 10;
        // this.PageTotal = res.page.pages * this.PageSize;
        this.PageTotal = res.page.total;
        res.data.forEach((val) => {
          sourceData.push(val);
        });
        sourceData.forEach((val) => {
          if (val.auditPerson) {
            const auditPerson = val.auditPerson;
            val.persons.unshift({
              projectId: auditPerson.projectId,
              firstName: auditPerson.firstName,
              code: auditPerson.code,
              email: auditPerson.email,
              phone: auditPerson.phone,
              isAuditPerson: true,
            });
          }
        });
        sourceData.map((perdata) => {
          this.addObserver(perdata);
          this.sourceData = JSON.parse(JSON.stringify(this.listOfParentData));
        });
      }
    });
  }
  nzPageIndexChange(e) {
    this.CurrentPage = e;
    this.getnewroleslist(this.projectId);
  }

  searchrolename(item): void {
    let selectlist = [];
    this.Investigators.forEach((res, index) => {
      res.indexId = index;
      if (res.firstName.indexOf(item) != -1) {
        selectlist.push(res);
      }
    });
    this.Investigatorsoption = selectlist;
  }
  testblur(e) {}
  nzSelectedIndexChange(e) {
    this.tabsetIndex = e;
    this.formData.userType = "all";
    this.changeSendUserType(this.formData.userType);
    if (e === 1) {
      this.getshowType();
    }
  }

  getchange(type) {
    if (type == "code") {
      this.codename = "公共码";
      this.showType = true;
    } else {
      this.codename = "一人一码";
      this.showType = false;
      this.listInvestigatorGroup();
    }
  }
  getmessage(type) {
    this.multipleValue = [];
    this.formData.userType = "all";
    let codeType = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    const typeMap = {
      email: 1,
      message: 2,
      vchart: 3,
      dingcode: 4,
      fscode: 5,
    };
    this.showcCode = typeMap[type];
    this.getlistmodalMail(codeType, true);
    // if (type == "email") {
    //   this.showcCode = 1;
    //   let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    //   this.getlistmodalMail(type);
    // } else if (type == "message") {
    //   this.showcCode = 2;
    //   this.getSmsDemo();
    // } else if (type == "vchart") {
    //   this.showcCode = 3;
    //   this.getVxDemo();
    // } else if (type == "dingcode") {
    //   this.showcCode = 4;
    //   this.getDingDemo();
    // } else {
    //   // 飞书
    //   this.showcCode = 5;
    //   this.getFeiShuDemo();
    // }
    // this.changeSendUserType("all");
  }
  nzBeforeChange(e) {
    this.invitecode_one = this.Investigators[e.to].inviteUrl;
  }
  nzAfterChange(e) {
    // this.invitecode_one = this.Investigators[e].inviteUrl
  }

  //监听路由，修改后重新加载
  initLoad(e) {
    this.step = this.routerInfo.snapshot.queryParams.step;
  }

  getRoleList(res: any) {
    if (res.result.code === 0) {
      this.rolelist = res.data;
      this.rolelist.forEach((res) => {
        if (res.type == "DEFAULT") {
          res.disabled = true;
        }
      });
      if (this.backtype == "create") {
        this.getnewroleslist(this.projectId);
      }
    }
  }

  filterData() {
    console.log(this.keyword, this.listOfParentData);
    const resource = JSON.stringify(this.sourceData);
    if (this.keyword) {
      const sonData = JSON.parse(resource)[0]["persons"].filter(
        (son) => son.firstName.indexOf(this.keyword) !== -1
      );
      this.listOfParentData[0]["persons"] = [...sonData];
    } else {
      this.listOfParentData = JSON.parse(resource);
    }
  }

  //添加调查者
  addObserver(perdata?): void {
    this.searchShow = false;
    let name: string = "";
    if (perdata) {
      if (perdata.name) {
        name = perdata.name;
      } else if (perdata.firstName) {
        name = perdata.firstName;
      }
    }
    if (this.backtype == "create") {
    }

    const [{ id }] = this.rolelist.filter((item) => {
      return item.type === "DEFAULT";
    });

    let roleId: string = "";
    const inviteSettingsList = this.rolelist
      .filter((val) => val.type != "DEFAULT")
      .map((val) => ({
        roleId: val.id,
        min: null,
        max: null,
        disableLimit: false,
      }));
    let item = {
      projectId: this.projectId,
      key: new Date().getTime() + "" + Math.random() * 100,
      name,
      code: perdata ? perdata.code : "",
      email: perdata ? perdata.email : "",
      phone: perdata ? perdata.phone : "",
      roleId: perdata && perdata.roleId ? perdata.roleId : id,
      percentage: "",
      isSelfEvaluation: perdata ? perdata.isSelfEvaluation : true,
      weights: perdata && perdata.weights ? perdata.weights : 1,
      persons: [],
      roleslistnew: [],
      roleWeightList:
        perdata && perdata.roleWeightList ? perdata.roleWeightList : [],
      submitroles: [],
      expand: true,
      disabled: false,
      groupCode: perdata ? perdata.groupCode : "",
      groupLeaderCode: perdata ? perdata.groupLeaderCode : "",
      inviteSettings:
        perdata && perdata.inviteSettings && perdata.inviteSettings.length
          ? perdata.inviteSettings
          : inviteSettingsList,
          
    };
    // 审核人
    const auditPerson = {
      projectId: this.projectId,
      key: new Date().getTime() + "" + Math.random() * 100,
      answerStatus: "",
      firstName: "",
      code: "",
      email: "",
      phone: "",
      roleId: "",
      weights: null,
      isAuditPerson: true, // 是否审核人标识
    };
    // 如果是编辑,那么将id存进去
    if (this.action === "edit") {
      Object.assign(item, { id: perdata.id });
    }
    if (perdata && perdata.persons && perdata.persons.length > 0) {
      perdata.persons.forEach((son) => {
        this.addChild(item, son);
      });
    }
    if (!perdata && this.isInviteReviewEvaluatee) {
      item.persons.push(auditPerson);
    }
    this.listOfParentData.unshift(item);
    this.listOfParentData.forEach((res) => {
      if (res.persons.length == 0) {
        if (this.action === "edit") {
          res.disabled = true;
        }
      }
      res.persons.forEach((val) => {
        if (this.action === "edit") {
          val.disabled = true;
          res.disabled = true;
        }
      });
    });

    if (this.standardReportType.indexOf("270") != -1) {
      this.listOfParentData.forEach((item) => {
        item.isSelfEvaluation = false;
      });
    }
    this.getsonroles();
    this.num++;
    this.pageIndexChange(1);
  }

  //添加评测者
  addChild(data, item?) {
    const obj = {
      projectId: this.projectId,
      key: new Date().getTime() + "" + Math.random() * 100,
      answerStatus: item ? item.answerStatus : "",
      firstName: item ? item.firstName : "",
      code: item ? item.code : "",
      email: item ? item.email : "",
      phone: item ? item.phone : "",
      roleId: item ? item.roleId : "",
      percentage: "",
      weights: item ? item.weights : 1,
      groupPersonType: item ? item.groupPersonType : "",
      isAuditPerson: item ? item.isAuditPerson : false, // 是否审核人标识
    };
    // if (this.standardReportType.indexOf("270") != -1) {

    // }

    if (this.action === "edit" && item) {
      Object.assign(obj, { id: item.id });
    }
    data.persons.push(obj);
    data.persons = [...data.persons];
    // this.changeList()
  }

  // 保存评价关系
  saveInfo(): void {
    this.isLoadingOne = true;
    let params = this.listOfParentData;
    console.log("saveInfo", this.listOfParentData);

    let isSubmit: boolean = true;
    if (this.listOfParentData.length != 0) {
      const [data] = this.listOfParentData;
      if (isEmpty(data.name)) {
        isSubmit = false;
      }
      if (isEmpty(data.code)) {
        isSubmit = false;
      }
      if (isEmpty(data.email)) {
        isSubmit = false;
      }
      if (isEmpty(<string>data.roleId)) {
        isSubmit = false;
      }
      if (!Number.isFinite(data.weights)) {
        isSubmit = false;
      }

      data.persons.map((item) => {
        if (isEmpty(item.email)) {
          isSubmit = false;
        }
        if (isEmpty(item.firstName)) {
          isSubmit = false;
        }
        if (isEmpty(data.code)) {
          isSubmit = false;
        }
        if (isEmpty(item.roleId) || !item.roleId) {
          isSubmit = false;
          if (item.isAuditPerson) isSubmit = true; // 审核人特殊处理
        }
        if (!Number.isFinite(item.weights)) {
          isSubmit = false;
          if (item.isAuditPerson) isSubmit = true; // 审核人特殊处理
        }
      });

      if (!isSubmit) {
        // this.msg.error("带星号的为必填项；比重必须是数字");
        this.customMsg.open("error", "带星号的为必填项；比重必须是数字");
        this.isLoadingOne = false;
        return;
      } else {
        params.forEach((res) => {
          res.roleWeightList = res.roleslistnew;
        });
      }
      // return
      let parmsadata = {
        projectId: this.projectId,
        // questionnaireId: this.questionnaireId,
        type: "EVALUATION_RELATIONSHIP",
        isConfirmed: true,
      };
      this.api.confirmRelation(parmsadata).subscribe((item) => {
        if (item.result.code == 0) {
          this.api.createSurveyInvestigator(params).subscribe((res) => {
            if (res.result.code === 0) {
              this.common.message("success");
              if (this.backtype === "home-detail") {
                this.router.navigateByUrl(
                  `/project-manage/${this.backtype}?id=${this.projectId}`
                );
              } else if (this.backtype != "create") {
                this.router.navigateByUrl(`/project-manage/home`);
              } else {
                localStorage.setItem(
                  "backurl",
                  this.router.routerState.snapshot.url
                );
                this.router.navigate(["/new-create"], {
                  queryParams: {
                    projectId: this.projectId,
                    questionnaireId: this.questionnaireId,
                    projectType: this.projectType,
                    standardReportType: this.standardReportType,
                    standardQuestionnaireId: this.standardQuestionnaireId,
                  },
                });
              }
            } else {
              if (res.result.code < 10000) {
                // this.msg.error(res.result.message);
                this.customMsg.open("error", res.result.message);
              }
              this.isLoadingOne = false;
            }
          });
        }
      });
    } else {
      this.isLoadingOne = false;
      // this.msg.warning("请添加评价角色！");
      this.customMsg.open("warning", "请添加评价角色");
    }
  }

  nextStep(step) {
    if (step == 1) {
      let params = this.listOfParentData;

      let isSubmit: boolean = true;
      const [data] = this.listOfParentData;
      if (isEmpty(data.name)) {
        isSubmit = false;
      }
      if (isEmpty(data.email)) {
        isSubmit = false;
      }
      if (!Number.isFinite(data.weights)) {
        isSubmit = false;
      }

      if (isEmpty(<string>data.roleId)) {
        isSubmit = false;
      }
      data.persons.map((item) => {
        if (isEmpty(item.email)) {
          isSubmit = false;
        }
        if (isEmpty(item.firstName)) {
          isSubmit = false;
        }
        if (isEmpty(item.roleId)) {
          isSubmit = false;
        }
        if (!Number.isFinite(item.weights)) {
          isSubmit = false;
        }
      });

      if (!isSubmit) {
        // this.msg.error("带星号的为必填项；比重必须是数字");
        this.customMsg.open("error", "带星号的为必填项；比重必须是数字");
        return;
      }

      this.api.createSurveyInvestigator(params).subscribe((res) => {
        if (res.result.code === 0) {
          this.common.message("success");
          this.router.navigate(["project-manage/invite360"], {
            queryParams: { projectId: this.projectId, step: 2 },
          });
          // location.reload();
        } else {
          if (res.result.code < 10000) {
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      });
    } else {
      this.isLoadingspend = true;
      if (
        !(
          isString(this.formData.title[this.lan]) &&
          this.formData.title[this.lan].trim() !== ""
        ) &&
        this.showcCode == 1
      ) {
        this.isLoadingspend = false;
        // this.msg.error("请输入中文邮件主题");
        this.customMsg.open("error", "请输入邮件主题");
        return;
      } //this.showcCode 为真 校验邮件主题
      let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";

      let Ids = [];
      this.fileIds.map((item) => {
        const index = this.fileListAttachment.findIndex(
          (itm) => itm.uid === item.uid
        );
        if (index !== -1) {
          Ids.push(item.id);
        }
      });

      let content = null;
      if (this.showcCode === 2) {
        content = this.addMessagePrefixAndSuffix(this.formData.content);
      } else {
        content = this.formData.content;
      }
      let params = {
        surveyPeople: this.formData.emailList,
        content: content,
        projectId: this.projectId,
        title: this.formData.title,
        answerCodeTypeEnum: type,
        fileIds: Ids,
        isMergeProject:
          this.showcCode !== 2 && this.multipleValue.length != 0 ? true : false,
        mergeProjectIds: this.showcCode !== 2 ? this.multipleValue : [],
      };
      let whitephone = [];
      let whiteemail = [];
      params.surveyPeople.forEach((res) => {
        if (res.phone == "" || res.phone == undefined) {
          whitephone.push(res);
        }
        if (res.email == "") {
          whiteemail.push(res);
        }
      });
      if (this.showcCode == 1) {
        if (whiteemail.length == 0 && params.surveyPeople.length != 0) {
          this.api.sendEmail360(params).subscribe((res) => {
            if (res.result.code == 0) {
              this.isLoadingspend = false;
              this.fileIds = [];
              this.common.message("invited");
              this.router.navigateByUrl("/project-manage");
            } else {
              if (res.result.code < 10000) {
                this.isLoadingspend = false;
                // this.msg.error(res.result.message);
                this.customMsg.open("error", res.result.message);
              } else {
                this.isLoadingspend = false;
              }
            }
          }); //360邮件接口
        } else {
          this.isLoadingspend = false;
          // this.msg.error("邮件发送，邮箱不能为空！");
          this.customMsg.open("error", "邮件发送，邮箱不能为空！");
        }
      } else if (this.showcCode == 2) {
        if (whitephone.length == 0 && params.surveyPeople.length != 0) {
          this.api.sendMessage360(params).subscribe((res) => {
            if (res.result.code == 0) {
              this.fileIds = [];
              this.isLoadingspend = false;
              this.common.message("invited");
              this.router.navigateByUrl("/project-manage");
            } else {
              if (res.result.code < 10000) {
                this.isLoadingspend = false;
                // this.msg.error(res.result.message);
                this.customMsg.open("error", res.result.message);
              } else {
                this.isLoadingspend = false;
              }
            }
          }); //360短信接口
        } else {
          this.isLoadingspend = false;
          // this.msg.error("短信发送，手机号不能为空！");
          this.customMsg.open("error", "短信发送，手机号不能为空！");
        }
      } else if (this.showcCode == 3) {
        // 企业微信未配置账号时点击发送邀请提示 #5872
        if (params.surveyPeople.length === 0) {
          this.isLoadingspend = false;
          // this.msg.error("企业微信发送，企业微信号不能为空！");
          this.customMsg.open("error", "企业微信发送，企业微信号不能为空！");
          return;
        }
        this.api.sendBehaviorWxWorkMsg(params).subscribe((res) => {
          if (res.result.code == 0) {
            this.isLoadingspend = false;
            this.fileIds = [];
            this.common.message("invited");
            this.router.navigateByUrl("/project-manage");
          } else {
            if (res.result.code < 10000) {
              this.isLoadingspend = false;
              // this.msg.error(res.result.message);
              this.customMsg.open("error", res.result.message);
            } else {
              this.isLoadingspend = false;
            }
          }
        }); //企业微信接口
      } else if (this.showcCode == 4) {
        // 钉钉消息
        if (params.surveyPeople.length === 0) {
          this.isLoadingspend = false;
          // this.msg.error("钉钉发送，钉钉用户账号不能为空！");
          this.customMsg.open("error", "钉钉发送，钉钉用户账号不能为空！");
          return;
        }
        this.api.sendDingMsg(params).subscribe((res) => {
          if (res.result.code == 0) {
            this.isLoadingspend = false;
            this.fileIds = [];
            this.common.message("invited");
            this.router.navigateByUrl("/project-manage");
          } else {
            if (res.result.code < 10000) {
              this.isLoadingspend = false;
              // this.msg.error(res.result.message);
              this.customMsg.open("error", res.result.message);
            } else {
              this.isLoadingspend = false;
            }
          }
        });
      } else {
        // 飞书消息
        if (params.surveyPeople.length === 0) {
          this.isLoadingspend = false;
          // this.msg.error("飞书发送，飞书用户账号不能为空！");
          this.customMsg.open("error", "飞书发送，飞书用户账号不能为空！");
          return;
        }
        this.api.sendFeiShuMsg(params).subscribe((res) => {
          if (res.result.code == 0) {
            this.isLoadingspend = false;
            this.fileIds = [];
            this.common.message("invited");
            this.router.navigateByUrl("/project-manage");
          } else {
            if (res.result.code < 10000) {
              // this.msg.error(res.result.message);
              this.customMsg.open("error", res.result.message);
              this.isLoadingspend = false;
            } else {
              this.isLoadingspend = false;
            }
          }
        });
      }
    }
  }
  downLoadMail() {
    // this.api.exportmail(type).subscribe(res => {
    //   // var blob = new Blob([res], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
    //   // var objectUrl = URL.createObjectURL(blob);
    //   // window.open(objectUrl);
    //   this.downFile(res);
    // })
  }
  downFile(data) {
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];
    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
      // .split('\'\'')[1]
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  // 删除调查者
  deleteObserver(key) {
    _.remove(this.listOfParentData, { key });
    _.remove(this.uploadSearchList, { key });
    this.listOfParentData = [...this.listOfParentData];
    this.changeList();
  }

  // 删除测评者
  deleteChild(data, key, index) {
    _.remove(data, { key });
    // remove会改变原数组,将删除后的data赋给listOfParentData的persons
    if (this.searchShow) {
      this.uploadSearchList[index].persons = data;
    } else {
      if (this.listOfParentData) {
        this.listOfParentData[index].persons = data;
      }
    }
    this.getsonroles();
  }
  getsonroles() {
    this.listOfParentData.forEach((res) => {
      res.roleslistnew = [];
      res.roleslistnew.push({
        roleId: res.roleId,
        projectId: this.projectId,
        weights: 1,
      });
      res.persons.forEach((item) => {
        if (item && item.roleId) {
          res.roleslistnew.push({
            roleId: item.roleId,
            projectId: this.projectId,
            weights: 1,
          });
        }
      });
      res.roleslistnew = this.removaldata(res.roleslistnew);
      res.roleWeightList.forEach((item) => {
        res.roleslistnew.forEach((val) => {
          if (item.roleId === val.roleId) {
            val.weights = item.weights;
          }
        });
      });
      if (res.submitroles.length != 0) {
        res.roleslistnew.forEach((item) => {
          res.submitroles.forEach((val) => {
            if (item.roleId == val.id) {
              item.weights = val.percentage;
            }
          });
        });
      }
    });
    console.log(this.listOfParentData);
  }
  removaldata(arr) {
    let result = [];
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      if (!obj[arr[i].roleId]) {
        result.push(arr[i]);
        obj[arr[i].roleId] = true;
      }
    }
    return result;
  } //去重
  // 切换人员
  async changeSendUserType(e) {
    let type;
    if (this.showcCode == 1) {
      type = "MAIL";
    } else if (this.showcCode == 2) {
      type = "SMS";
    } else if (this.showcCode == 3) {
      type = "THIRD_PARTY_ENTERPRISE_WECHAT";
    } else if (this.showcCode == 4) {
      type = "DING_DING";
    } else {
      type = "THIRD_PARTY_LARK_TEXT_CARD";
    }
    await this.getAllNoInvitedUser(type);
    //
    if (e === "all") {
      let emailList = [];
      this.allNoInvitedUser.forEach((item) => {
        emailList.push({
          email: item.email,
          id: item.id,
          captcha: item.captcha,
          firstName: item.firstName,
          phone: item.phone,
          wxWorkUserId: item.wxWorkUserId,
          dingAccount: item.dingAccount,
          feishuAccount: item.feishuAccount,
        });
      });
      this.formData.emailList = emailList;
      let obj = this.api.countMoney(
        this.formData.emailList.length,
        this.strNum
      );
      this.countMoney = obj.countMoney;
      this.singleYuan = obj.yuan;
      this.peopleNum = obj.numPeople;
      this.getMailTotal();
    } else {
      this.showSpecifiedModal();
    }
  }
  changonecode(e) {
    if (e == "one") {
      this.codemenu = false;
    } else {
      this.codemenu = true;
    }

    let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    this.getlistmodalMail(type, true);
    // if (this.showcCode == 1) {
    //   let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    //   this.getlistmodalMail(type);
    // }

    // if (this.showcCode == 2) {
    //   this.getSmsDemo();
    // }
    // if (this.showcCode == 3) {
    //   this.getVxDemo();
    // }
    // // 钉钉
    // if (this.showcCode == 4) {
    //   this.getDingDemo();
    // }
    // // 飞书
    // if (this.showcCode == 5) {
    //   this.getFeiShuDemo();
    // }
  }

  // 正文-富文本插入字段
  getPushWord(val, type, id?, name?) {
    if (type) {
      let text = "";
      let dom = document.querySelector("#formula-tinymce").children[0]
        .children[0].children[1].children;
      if (dom[0].contentWindow.getSelection().type != "None") {
        let demo = dom[0].contentWindow.getSelection().getRangeAt(0);
        //IE浏览器
        if (document.selection) {
        } else {
          //Chrome等浏览器

          if (val == "name") {
            text = "{{姓名}} ";
          }
          if (val == "start") {
            text = "{{开始日期}} ";
          }
          if (val == "end") {
            text = "{{结束日期}}";
          }
          if (val == "url") {
            text = "{{链接}}";
          }
          if (val == "urls") {
            if (!this.codemenu) {
              text = `${name}{{链接}}`;
            } else {
              text = `${name}{{链接}} {{验证码}}`;
            }
          }
          if (val == "code") {
            text = "{{验证码}}";
          }
          if (val == "qrcode") {
            if (name) {
              text = `${name}{{登录二维码}}`;
            } else {
              text = "{{登录二维码}}";
            }
          }

          let newNode = document.createElement("span");
          if (id) {
            newNode.id = id;
          } else {
            this.showprojectcard = null;
          }
          newNode.innerText = text;
          demo.insertNode(newNode);
          let newContent =
            dom[0].contentWindow.document.activeElement.innerHTML;
          this.formData.content[this.lan] = newContent;
        }
      }
      this.getMailTotal();
    }
  }
  // 正文-文本域插入字段
  getPushWordTextarea(val, type, id?, name?) {
    if (type) {
      let text = "";
      let textarea = document.querySelector("#formula-textarea");
      // 获取当前光标位置
      const startPos = textarea.selectionStart;
      const endPos = textarea.selectionEnd;
      console.log("获取当前光标位置", startPos, endPos, textarea.value);
      // 获取当前 textarea 的值
      const currentValue = textarea.value;
      if (!id) {
        this.showprojectcard = null;
      }
      //IE浏览器
      if (document.selection) {
      } else {
        //Chrome等浏览器
        if (val == "name") {
          text = "{{姓名}} ";
        }
        if (val == "start") {
          text = "{{开始日期}} ";
        }
        if (val == "end") {
          text = "{{结束日期}}";
        }
        if (val == "url") {
          text = "{{链接}}";
        }
        if (val == "urls") {
          if (!this.codemenu) {
            text = `${name}{{${id}链接}}`;
          } else {
            text = `${name}{{${id}链接}} {{${id}验证码}}`;
          }
        }
        if (val == "code") {
          text = "{{验证码}}";
        }
        if (val == "qrcode") {
          if (name) {
            text = `${name}{{登录二维码}}`;
          } else {
            text = "{{登录二维码}}";
          }
        }
        // 构造新的值：在光标处插入文字
        const newValue =
          currentValue.slice(0, startPos) + text + currentValue.slice(endPos);
        // 更新 textarea 的值
        textarea.value = newValue;
        // 将光标移动到插入文字之后的位置
        textarea.selectionStart = startPos + text.length;
        textarea.selectionEnd = textarea.selectionStart;
        // 重新聚焦到 textarea
        textarea.focus();
        this.formData.content[this.lan] = newValue;
      }
      this.getMailTotal();
    }
  }
  // 自定义模板-富文本插入字段
  getPushWordTemplate(val, type) {
    if (type) {
      let text = "";
      let dom = document.querySelector("#formula-tinymce-template").children[0]
        .children[0].children[1].children;
      if (dom[0].contentWindow.getSelection().type != "None") {
        let demo = dom[0].contentWindow.getSelection().getRangeAt(0);
        //IE浏览器
        if (document.selection) {
        } else {
          //Chrome等浏览器
          if (val == "name") {
            text = "{{姓名}} ";
          }
          if (val == "start") {
            text = "{{开始日期}} ";
          }
          if (val == "end") {
            text = "{{结束日期}}";
          }
          if (val == "url") {
            text = "{{链接}}";
          }
          if (val == "code") {
            text = "{{验证码}}";
          }
          if (val == "qrcode") {
            text = "{{登录二维码}}";
          }
          let newNode = document.createElement("span");
          newNode.innerText = text;
          demo.insertNode(newNode);
          let newContent =
            dom[0].contentWindow.document.activeElement.innerHTML;
          this.chooseformData.content[this.lan] = newContent;
        }
      }
    }
  }

  // 自定义模板-文本域插入字段
  getPushWordTextareaTemplate(val, type) {
    if (type) {
      let text = "";
      // 编辑中才生效
      if (this.chooseformData.editors) return;
      let textarea = document.querySelector("#formula-textarea-template");
      // 获取当前光标位置
      const startPos = textarea.selectionStart;
      const endPos = textarea.selectionEnd;
      console.log("获取当前光标位置", startPos, endPos);
      // 获取当前 textarea 的值
      const currentValue = textarea.value;
      //IE浏览器
      if (document.selection) {
      } else {
        //Chrome等浏览器
        if (val == "name") {
          text = "{{姓名}} ";
        }
        if (val == "start") {
          text = "{{开始日期}} ";
        }
        if (val == "end") {
          text = "{{结束日期}}";
        }
        if (val == "url") {
          text = "{{链接}}";
        }
        if (val == "code") {
          text = "{{验证码}}";
        }
        if (val == "qrcode") {
          text = "{{登录二维码}}";
        }
        // 构造新的值：在光标处插入文字
        const newValue =
          currentValue.slice(0, startPos) + text + currentValue.slice(endPos);
        // 更新 textarea 的值
        textarea.value = newValue;

        // 将光标移动到插入文字之后的位置
        textarea.selectionStart = startPos + text.length;
        textarea.selectionEnd = textarea.selectionStart;
        // 重新聚焦到 textarea
        textarea.focus();
        this.chooseformData.content[this.lan] = newValue;
      }
    }
  }

  // 获取所有未邀请人
  async getAllNoInvitedUser(type): Promise<any> {
    let res: any;

    if (this.tabsetIndex === 0) {
      res = await this.api.listInvestigatorGroup(this.projectId).toPromise();
    } else {
      if (type !== "SMS") {
        res = await this.api
          .listNotInvitedprisma([this.projectId, ...this.multipleValue], type)
          .toPromise();
      } else {
        res = await this.api
          .listNotInvitedprisma([this.projectId], type)
          .toPromise();
      }
    }
    if (res.result.code === 0) {
      this.allNoInvitedUser = res.data || [];
    }
  }
  async getcaptchacode(): Promise<any> {
    //验证码 // public
    let str = "CAPTCHA";
    const res = await this.api.captchacodeurl(this.projectId, str).toPromise();
    if (res.result.code === 0) {
      this.invitecode = res.data;
    }
  }

  // 获取邮件模板
  getEmailDemo() {
    let templateType = this.templateTypeMap[this.showcCode];
    this.api.showEmailDemo(templateType).subscribe((res) => {
      if (res.result.code === 0) {
        // this.formData.content = res.data.content;
        // var newstr = res.data.content.replace(/\//g, '');
        // let reNewstr = newstr.replace(/\<br>/g, '\n')
        // this.messageData = reNewstr
        const data = res.data.content || {};
        this.formData.content = data;
        Object.entries(data).forEach(([key, value]) => {
          if (typeof value === "string") {
            value = value.replace(/\//g, "").replace(/\<br>/g, "\n");
          }
        });
        // this.formData.title = res.data.theme;
        this.messageData = data;
        this.getEmailThemeName(res.data.theme);
        this.getMailTotal();
      }
    });
  }
  getSmsDemo() {
    let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    this.api.showMessDemo(type, this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        // this.formData.content = res.data;
        // console.log("获取短信模板", this.removeMessagePrefixAndSuffix(res.data));
        this.formData.content = this.removeMessagePrefixAndSuffix(res.data);
        this.formData.title = res.data.subject || {};
        this.nzChangetextarea();
      }
    });
  }

  nzChangetextarea() {
    let content = "";
    if (this.showcCode === 2) {
      content =
        this.messagePrefix[this.lan] +
        this.formData.content[this.lan] +
        this.messageSuffix[this.lan];
    } else {
      content = this.formData.content[this.lan];
    }
    this.strNum = this.api.calculateNum(content, this.longCodeNum);
    let obj = this.api.countMoney(this.formData.emailList.length, this.strNum);
    this.countMoney = obj.countMoney;
    this.singleYuan = obj.yuan;
    this.peopleNum = obj.numPeople;
  }

  // 获取微信模板
  getVxDemo() {
    let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    this.api.showWxWorkMsgDemo(type, this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.formData.content = res.data.content;
        this.formData.title = res.data.title || {};
        this.nzChangetextarea();
      }
    });
  }

  // 钉钉消息示例
  getDingDemo() {
    let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    this.api.showDingMsgDemo(type, this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.formData.content = res.data.content;
        this.formData.title = res.data.title || {};
        this.nzChangetextarea();
      }
    });
  }

  // 飞书消息示例
  getFeiShuDemo() {
    let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    this.api.showFeiShuMsgDemo(type, this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.formData.content = res.data.content;
        this.formData.title = res.data.title || {};
        this.nzChangetextarea();
      }
    });
  }

  //下载评价关系
  getshiplist() {
    if(this.isQuickMutualEvaluation){
      this.api
      .MutualEvaluationRelationship(this.projectId, this.isCustomRoleWeight)
      .subscribe((res) => {
        this.downUtil.downFile(res);
      });
    } else{
      this.api
      .Relationship(this.projectId, this.isCustomRoleWeight)
      .subscribe((res) => {
        this.downUtil.downFile(res);
      });
    }
  }

  // 下载模板
  downLoad() {
    this.buttonload = true;
    this.api.exportprismaPerson(this.projectId).subscribe((res) => {
      // var blob = new Blob([res], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      // var objectUrl = URL.createObjectURL(blob);
      // window.open(objectUrl);
      this.downUtil.downFile(res);
      this.buttonload = false;
    });
  }
  downLoad360() {
    if(this.isQuickMutualEvaluation){
      this.api
          .MutualEvaluationRelationship(this.projectId, this.isCustomRoleWeight)
          .subscribe((res) => {
            this.downUtil.downFile(res);
          });
    } else {
       this.api
          .Relationship(this.projectId, this.isCustomRoleWeight)
          .subscribe((res) => {
            this.downUtil.downFile(res);
          });
    }

  }

  /**
   * preview 预览上传的文件
   * @param file
   */
  preview(file) {
    window.open(window.URL.createObjectURL(file.originFileObj));
  }

  /**
   * uploadAttachment 上传附件
   * @param item
   */
  uploadAttachment = (item: UploadXHRArgs) => {
    const formData = new FormData();
    let type = [
      ".txt",
      ".doc",
      ".docx",
      ".xls",
      ".xlsx",
      ".pdf",
      ".ppt",
      ".pptx",
      ".zip",
      ".rar",
      ".jpg",
      ".jpeg",
      ".png",
    ];
    formData.append("file", item.file as any);
    formData.append("isPublic", "false");
    formData.append("effectiveFileTypes", type as any);
    formData.append("businessType", "DEFAULT");
    this.uploadAttachmentUrl(formData, item);
  };

  fileChange(e) {
    const arr = e.fileList;
    this.fileListAttachment = [...arr];
    if ((e.type = "removed")) {
      let filids = _.cloneDeep(this.fileIds);
      this.fileIds = [];
      filids.map((item) => {
        const index = this.fileListAttachment.findIndex(
          (itm) => itm.uid === item.uid
        );
        if (index !== -1) {
          this.fileIds.push(item);
        }
      });
      this.getMailTotal();
    }
  }

  /**
   * 获取文件名后缀
   * @param item
   */
  getFileType(file: any): string {
    let type = "PDF";
    if (file && file.name) {
      let name: string = file.name;
      let names: string[] = name.split(".");
      if (names.length > 0) {
        type = names[names.length - 1];
      }
    }
    return `.${type}`;
  }

  /**
   * uploadAttachmentUrl 上传pdf报告文件
   */
  uploadAttachmentUrl(formData, item) {
    const url = `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`;
    return this.http.post(url, formData).subscribe(
      (res: any) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.msg.success("上传文件成功");
          this.fileIds.push({
            id: res.data.id,
            uid: item.file.uid,
            size: this.api.getFileSize(item.file.size),
          });
          this.getMailTotal();
        } else {
          item.onError!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    this.fileList = [
      {
        uid: item.file.uid,
        name: item.file.name,
        status: "uploading",
        originFileObj: item.file,
      },
    ];
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let params = {
      fileType: "." + item.file.name.split(".")[1],
    };
    this.uploadExcel(formData, params, item);
  };
  /**
   * customReq 360上传
   * @param item
   */
  customReq360 = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let params = {
      fileType: "." + item.file.name.split(".")[1],
    };

    if (this.isQuickMutualEvaluation) {
      this.uploadMutualEvaluationExcel360(formData, params, item);
    } else {
      this.uploadExcel360(formData, params, item);
    }

  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, params, item) {
    return this.api.getprismaPerson(formData, this.projectId).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.api.getLongestPersonCaptcha(this.projectId).subscribe((res) => {
            if (res.result.code === 0) {
              this.longCodeNum = res.data;
            }
          });
          item.onSuccess!();
          const sourceData = res.data;
          sourceData.map((perdata) => {
            this.addObserver(perdata);
          });
          this.fileList[0].status = "done";
        } else {
          this.fileList = [
            {
              uid: item.file.uid,
              name: item.file.name,
              status: "error",
              originFileObj: item.file,
            },
          ];
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  // 评价关系 分页
  pageIndexChange(pageIndex) {
    if (pageIndex === 1) {
      this.pageStart = pageIndex;
    } else {
      // this.pageStart = pageIndex * 10 - 9;
      this.pageStart = pageIndex * this.PageSize - (this.PageSize - 1);
    }
    // this.pageEnd = pageIndex * 10;
    this.pageEnd = pageIndex * this.PageSize;
    this.changeList();
  }

  // 评价关系 操作数据
  changeList() {
    let arr = [];
    if (this.searchShow) {
      this.uploadSearchList.map((item, index) => {
        if (index + 1 >= this.pageStart && index + 1 <= this.pageEnd) {
          arr.push(item);
        }
      });
    } else {
      this.listOfParentData.map((item, index) => {
        if (index + 1 >= this.pageStart && index + 1 <= this.pageEnd) {
          arr.push(item);
        }
      });
    }
    this.uploadList = [...arr];
  }

  /**
   * uploadExcel 上传配置
   */
  uploadExcel360(formData, params, item) {
    return this.api
      .getPerson(formData, this.projectId, this.isCustomRoleWeight)
      .subscribe(
        (res) => {
          if (res.result.code === 0) {
            item.onSuccess!();
            const sourceData = res.data;
            sourceData.map((perdata, index) => {
              this.addObserver(perdata);
              //rng
            });
            this.changeList();
            this.api
              .getLongestPersonCaptcha(this.projectId)
              .subscribe((res) => {
                if (res.result.code === 0) {
                  this.longCodeNum = res.data;
                }
              });
          }
        },
        (err) => {
          item.onError!(err, item.file!);
        }
      );
  }

  /**
   * uploadExcel 上传配置
   */
  uploadMutualEvaluationExcel360(formData, params, item) {
    return this.api
        .getMutualEvaluationPerson(formData, this.projectId, this.isCustomRoleWeight)
        .subscribe(
            (res) => {
              if (res.result.code === 0) {
                item.onSuccess!();
                const sourceData = res.data;
                sourceData.map((perdata, index) => {
                  this.addObserver(perdata);
                  //rng
                });
                this.changeList();
                this.api
                    .getLongestPersonCaptcha(this.projectId)
                    .subscribe((res) => {
                      if (res.result.code === 0) {
                        this.longCodeNum = res.data;
                      }
                    });
              }
            },
            (err) => {
              item.onError!(err, item.file!);
            }
        );
  }

  /**
   * 编辑时,根据id获取数据并回显
   * @param investigatorId 调查者id
   */
  public editSurvey(res: any): void {
    if (res.result.code === 0) {
      const sourceData: Array<any> = [];
      sourceData.push(res.data);
      sourceData.forEach((val) => {
        if (val.auditPerson) {
          const auditPerson = val.auditPerson;
          val.persons.unshift({
            projectId: auditPerson.projectId,
            firstName: auditPerson.firstName,
            code: auditPerson.code,
            email: auditPerson.email,
            phone: auditPerson.phone,
            isAuditPerson: true,
          });
        }
      });
      sourceData.map((perdata) => {
        this.addObserver(perdata);
        this.sourceData = JSON.parse(JSON.stringify(this.listOfParentData));
      });
    }
  }

  /**
   * 重新发送邮件时,需要将原来的数据回显,只有邮箱可以编辑,其余不可编辑
   * @param investigatorId 调查者id
   * @param personId 测评者id
   */
  public showDataForReSendEmail(res: any): void {
    if (res.result.code === 0) {
      const { data } = res;
      let sourceData: Array<any> = [];
      if (Array.isArray(data)) {
        sourceData = data;
      } else {
        sourceData.push(data);
      }
      sourceData.map((perdata) => {
        this.addObserver(perdata);
      });
    }
  }

  /**
   * 编辑完成时保存回调
   */
  public saveSurvey(): void {
    this.listOfParentData.forEach((res) => {
      res.persons.forEach((val) => {
        if (val.firstName == "") {
          val.firstClass = true;
        } else {
          val.firstClass = false;
        }
        if (val.code == "") {
          val.codeClass = true;
        } else {
          val.codeClass = false;
        }
        if (val.email == "") {
          val.emailClass = true;
        } else {
          val.emailClass = false;
        }
        if (val.roleId == "") {
          val.roleIdClass = true;
        } else {
          val.roleIdClass = false;
        }
      });
    });
    //修改没填的地方变红色

    let isSubmit: boolean = true;
    const [data] = this.listOfParentData;
    if (isEmpty(data.name)) {
      isSubmit = false;
    }
    if (isEmpty(data.code)) {
      isSubmit = false;
    }
    if (isEmpty(data.email)) {
      isSubmit = false;
    }
    if (isEmpty(<string>data.roleId)) {
      isSubmit = false;
    }

    if (data.isSelfEvaluation) {
      if (!Number.isFinite(data.weights)) {
        isSubmit = false;
      }
    }

    data.persons.map((item) => {
      if (isEmpty(item.email)) {
        isSubmit = false;
      }
      if (isEmpty(item.firstName)) {
        isSubmit = false;
      }
      if (isEmpty(item.code)) {
        isSubmit = false;
      }
      if (isEmpty(item.roleId)) {
        isSubmit = false;
        if (item.isAuditPerson) isSubmit = true; // 审核人特殊处理
      }
      if (!Number.isFinite(item.weights)) {
        isSubmit = false;
        if (item.isAuditPerson) isSubmit = true; // 审核人特殊处理
      }
    });

    if (!isSubmit) {
      // this.msg.error("带星号的为必填项; 比重必须是数字");
      this.customMsg.open("error", "带星号的为必填项; 比重必须是数字");
      return;
    }
    if (this.saveLoading) {
      return;
    }
    this.saveLoading = true;
    let parmsa = {
      projectId: this.projectId,
      // questionnaireId: this.questionnaireId,
      type: "EVALUATION_RELATIONSHIP",
      isConfirmed: true,
    };

    this.listOfParentData[0].roleWeightList = this.listOfParentData[0].roleslistnew;
    // return
    this.api.confirmRelation(parmsa).subscribe((item) => {
      if (item.result.code == 0) {
        console.log("this.listOfParentData[0]", this.listOfParentData[0]);
        this.http
          .request(
            "post",
            `${this.tenantUrl}/survey/person/saveOrUpdateSurveyInvestigatorAndPerson`,
            {
              body: this.listOfParentData[0],
            }
          )
          .toPromise()
          .then((res: HttpResponseWrapper) => {
            if (res.result.code === 0) {
              if (this.backtype != "create") {
                this.msg.success("保存成功");
                if (this.backtype === "home-detail") {
                  this.router.navigateByUrl(
                    `/project-manage/home-detail?id=${this.projectId}`
                  );
                } else {
                  this.router.navigateByUrl("/project-manage");
                }
                sessionStorage.setItem("href", location.href);
              } else {
                localStorage.setItem(
                  "backurl",
                  this.router.routerState.snapshot.url
                );
                this.router.navigate(["/new-create"], {
                  queryParams: {
                    projectId: this.projectId,
                    questionnaireId: this.questionnaireId,
                    projectType: this.projectType,
                    standardReportType: this.standardReportType,
                    standardQuestionnaireId: this.standardQuestionnaireId,
                  },
                });
              }
            } else {
              if (res.result.code < 10000) {
                // this.msg.error(res.result.message);
                this.customMsg.open("error", res.result.message);
              }
            }
            this.saveLoading = false;
          })
          .catch((err) => {
            this.saveLoading = false;
          });
      } else {
        this.saveLoading = false;
      }
    });
  }

  // 对邀请失败的评测者重新发送邮件
  public reSendEmail(): void {
    const { email, projectId } = this.listOfParentData[0];
    this.http
      .request(
        "post",
        `${this.tenantUrl}/survey/person/updateInvestigatorPerson`,
        {
          body: {
            email,
            projectId,
            investigatorId: this.investigatorId,
            personId: this.personId,
          },
        }
      )
      .toPromise()
      .then((res: HttpResponseWrapper) => {
        if (res.result.code === 0) {
          this.step = 2;
        }
      })
      .catch((err) => {});
  }

  /**
   * 保存之后跳转到第二步
   */
  afterSaveToStep2(): void {
    this.router.navigate(["project-manage/invite360"], {
      queryParams: { projectId: this.projectId, step: 2 },
    });
  }
  doCopyurl() {
    const input = document.querySelector(".copytext");
    // const range = document.createRange();
    // range.selectNode(input);
    // window.getSelection().removeAllRanges();
    // window.getSelection().addRange(range);
    // document.execCommand('Copy');
    copy(input.value);
    this.msg.success("复制成功");
  }
  doCopy() {
    const div = document.getElementById("text");
    const range = document.createRange();
    // range.selectNode(div);
    // window.getSelection().removeAllRanges();
    // window.getSelection().addRange(range);
    // document.execCommand('Copy');
    copy(div.value);
    this.msg.success("复制成功");
  }
  downloadImg() {
    let img: any = "";
    let imageName = "";
    let prefix = this.tenantName + "_"+this.projectCode +":"+this.projectname+"_";
    if (this.showType) {
      img = document.querySelector(".qrcode img");
      imageName = prefix + "个人验证码邀请码【二维码图片】";
    } else {
      img = document.querySelector(".invitecode_one .qrcode img");
      imageName = prefix + "个人邀请码【二维码图片】";
    }
    let timestamp = new Date().getTime();
    imageName = imageName + "_" + timestamp;
    let src = img.getAttribute("src");
    let aLink = document.createElement("a");
    let blob = this.base64ToBlob(src);
    let evt = document.createEvent("HTMLEvents");

    evt.initEvent("click", true, true);
    aLink.download = imageName+".png";
    aLink.href = URL.createObjectURL(blob);
    aLink.click();
  }
  moreload() {
    let urllist = [];
    const morecode = document.getElementById("moreqrcode").children;
    for (let index = 0; index < morecode.length; index++) {
      urllist.push({
        img: morecode[index].querySelector(".qrcode img").getAttribute("src"),
      });
    }
    this.downloadlist.forEach((item, index) => {
      urllist.forEach((res, inde) => {
        if (index == inde) {
          res.name = item.firstName;
        }
      });
    });

    this.zipClick(urllist);
  }
  zipClick(imgData) {
    const zip = new JSZip();
    imgData.forEach((res, index) => {
      zip.file(res.name + ".png", res.img.substring(22), { base64: true });
    });
    let imageName = "";
    let prefix = this.tenantName + "_"+this.projectCode +":"+this.projectname+"_";
    imageName = prefix+"个人邀请码【二维码图片】";
    let timestamp = new Date().getTime();
    imageName = imageName + "_" + timestamp;
    zip.generateAsync({ type: "blob" }).then(function(content) {
      let aLink = document.createElement("a");

      aLink.setAttribute("href", URL.createObjectURL(content));
    
      aLink.setAttribute("download", imageName+".zip");
      aLink.click();
    });
  }
  private excelData;
  updateFile($event) {
    const target: DataTransfer = $event.target as DataTransfer;
    if (target.files.length !== 0) {
      if (target.files.length !== 1) {
        throw new Error("Cannot use multiple files");
      }
    }

    const reader: FileReader = new FileReader();

    reader.onload = (e: any) => {
      /* read workbook */
      const bstr: string = e.target.result;
      const wb: XLSX.WorkBook = XLSX.read(bstr, { type: "binary" });

      /* grab first sheet */
      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];

      this.excelData = XLSX.utils.sheet_to_json(ws);
      let emailList = [];
      if (this.showcCode == 1) {
        for (let i in this.excelData) {
          emailList.push(this.excelData[i]["邮箱"]);
        }
      } else if (this.showcCode == 2) {
        for (let i in this.excelData) {
          emailList.push(this.excelData[i]["手机号"]);
        }
      }

      this.formData.email = emailList.join("\n");
    };
    reader.readAsBinaryString(target.files[0]);
  }
  base64ToBlob(code) {
    let parts = code.split(";base64,");
    let contentType = parts[0].split(":")[1];
    let raw = window.atob(parts[1]);
    let rawLength = raw.length;

    let uInt8Array = new Uint8Array(rawLength);

    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array], { type: contentType });
  }

  // 指定人弹窗
  showSpecifiedModal() {
    const modal = this.modalService.create({
      nzContent: ChooseInvitorComponent,
      nzTitle: "指定邀请人",
      nzWidth: 800,
      nzComponentParams: {
        list: this.allNoInvitedUser,
        projectId: this.projectId,
        showcCode: this.showcCode,
        tabsetIndex: this.tabsetIndex,
        multipleValue:
          this.tabsetIndex === 1 && this.showcCode === 1
            ? this.multipleValue
            : [],
        // currentpage: this.currentpage,
        // totalpage: this.totalpage,
        // totalnumber: this.totalnumber
      },
      nzFooter: [
        {
          label: "确认",
          shape: "round",
          type: "primary",
          onClick: () => {
            let child: ChooseInvitorComponent = modal.getContentComponent();
            const list = child.selectkeys;

            let emailList = [];
            list.forEach((item) => {
              emailList.push({
                email: item.email,
                id: item.key,
                captcha: item.captcha,
                firstName: item.firstName,
                phone: item.phone,
                wxWorkUserId: item.wxWorkUserId,
                dingAccount: item.dingAccount,
                feishuAccount: item.feishuAccount,
              });
            });
            this.formData.emailList = emailList;
            let obj = this.api.countMoney(
              this.formData.emailList.length,
              this.strNum
            );
            this.countMoney = obj.countMoney;
            this.singleYuan = obj.yuan;
            this.peopleNum = obj.numPeople;
            // let smsNum = Math.ceil(this.strNum / 50)
            // console.log(smsNum);
            //rng
            // let num = (this.formData.emailList.length * 0.1).toFixed(1).toString()
            // this.countMoney = num//rng
            this.getMailTotal();
            modal.destroy();
          },
        },
      ],
    });
  }

  ngModelChange() {
    this.getsonroles();
  }

  // 角色弹窗
  showRoleModal(roles?, key?, index?, type?) {
    let typename = "";
    if (type) {
      typename = "角色权重";
    } else {
      typename = "角色管理";
    }
    this.getsonroles();
    let sonroles = null;
    if (this.listOfParentData[index]) {
      sonroles = this.listOfParentData[index].roleslistnew;
    }
    if (type) {
      const modal = this.modalService.create({
        nzContent: RoleComponent,
        nzTitle: typename,
        nzWidth: 800,
        nzComponentParams: {
          showtype: type,
          projectId: this.projectId,
          standardReportType: this.standardReportType,
          standardQuestionnaireId: this.standardQuestionnaireId,
          sonrolelist: sonroles,
          roleskey: key,
          isCustomRoleWeight: this.isCustomRoleWeight,
          Projectstatus: this.Projectstatus,
        },
        nzFooter: [
          {
            label: "确认",
            shape: "round",
            type: "primary",
            onClick: () => {
              let child: RoleComponent = modal.getContentComponent();
              if (child.sonrolelist) {
                modal.destroy();
                this.listOfParentData.forEach((val) => {
                  if (val.key == child.roleskey) {
                    val.submitroles = child.rolelist;
                  }
                });
                this.common.message("success");
                this.getsonroles();
              } else {
                let isSave = true;
                child.rolelist.forEach((val) => {
                  if (!isSave) {
                    return;
                  }
                  if (!val.name || !val.name.zh_CN) {
                    isSave = false;
                    // this.msg.error("角色名称不能为空");
                    this.customMsg.open("error", "角色名称不能为空");
                  }

                  if (!val.investigatorTitle || !val.investigatorTitle.zh_CN) {
                    isSave = false;
                    // this.msg.error("对被评估人的称呼不能为空");
                    this.customMsg.open("error", "对被评估人的称呼不能为空");
                  }
                });

                if (!isSave) {
                  return;
                }
                this.api
                  .createSurveyRole({
                    projectId: child.projectId,
                    surveyRoles: child.rolelist,
                  })
                  .subscribe((res) => {
                    if (res.result.code === 0) {
                      this.api.listRole(this.projectId).subscribe((res1) => {
                        this.getRoleList(res1);
                      });
                      modal.destroy();
                      this.common.message("success");
                    } else {
                      if (res.result.code < 10000) {
                        // this.msg.error(res.result.message);
                        this.customMsg.open("error", res.result.message);
                      }
                    }
                  });
              }

              return false;
            },
          },
        ],
      });
    } else {
      const modal = this.drawerService.create({
        nzContent: RoleNewComponent,
        nzTitle: typename,
        nzWidth: 1000,
        nzContentParams: {
          projectId: this.projectId,
          standardReportType: this.standardReportType,
          standardQuestionnaireId: this.standardQuestionnaireId,
          sonrolelist: sonroles,
          roleskey: key,
          isCustomRoleWeight: this.isCustomRoleWeight,
          Projectstatus: this.Projectstatus,
        },
        nzWrapClassName: "round-right-drawer10",
      });
      modal.afterClose.subscribe((rolelist) => {
        if (rolelist && rolelist.length > 0) {
          this.api
            .createSurveyRole({
              projectId: this.projectId,
              surveyRoles: rolelist,
            })
            .subscribe((res) => {
              if (res.result.code === 0) {
                this.api.listRole(this.projectId).subscribe((res1) => {
                  this.getRoleList(res1);
                });
                this.common.message("success");
              } else {
                if (res.result.code < 10000) {
                  // this.msg.error(res.result.message);
                  this.customMsg.open("error", res.result.message);
                }
              }
            });
        }
      });
    }
  }
  plusAdd() {
    this.addtyceshow = true;
    this.getEmaillist();
  }
  getEmaillist() {
    let templateType = this.templateTypeMap[this.showcCode];
    this.api.getEmaillist(templateType).subscribe((res) => {
      if (res.result.code == 0) {
        this.eamilTemplate = res.data;
        this.eamilTemplate.forEach((res) => {
          res.valueId = res.id;
          res.editors = true;
        });
        let choosed = this.eamilTemplate.filter((res) => {
          return res.isStandard;
        });
        if (choosed.length > 0) {
          this.chooseformData = choosed[0];
        }
        let templateType = this.templateTypeMap[this.showcCode];
        console.log("templateType", templateType);
        this.api.getEmailDefault(templateType).subscribe((res) => {
          console.log("getEmailDefault", templateType, res);
          // 若有默认数据，赋值当前编辑
          if (res.data) {
            this.radioValue = res.data.id;
            this.chooseformData = res.data;
            this.chooseformData.valueId = this.chooseformData.id;
            this.chooseformData.editors = true;
            if (this.showcCode === 2) {
              this.chooseformData.content = this.removeMessagePrefixAndSuffix(
                this.chooseformData.content
              );
            }
          } else {
            // 若无默认数据，用模板数据赋值当前编辑
            const chooseformDataDeep = _.cloneDeep(this.chooseformDataTemplate);
            if (this.showcCode === 2) {
              chooseformDataDeep.theme = null;
            }
            chooseformDataDeep.type = templateType;
            this.chooseformData = chooseformDataDeep;
          }
        });
      }
    });
  }
  addtyceCancel() {
    this.addtyceshow = false;
  }
  addtyceOk() {
    if (!this.chooseformData.id) {
      // this.msg.warning("请先保存该条模板！");
      this.customMsg.open("warning", "请先保存该条模板");
    } else {
      let editors = false;
      this.eamilTemplate.forEach((res) => {
        if (!res.editors) {
          editors = true;
        }
      });
      if (editors) {
        // this.msg.warning("请先保存编辑内容！");
        this.customMsg.open("warning", "请先保存编辑内容");
      } else {
        this.api.useTemplate(this.chooseformData.valueId).subscribe((res) => {
          if (res.result.code == 0) {
            this.addtyceshow = false;
            if (this.showcCode == 2) {
              this.formData.content = this.removeMessagePrefixAndSuffix(
                res.data.content
              );
            } else {
              this.formData.content = res.data.content; // 正文
            }
            this.formData.title = res.data.theme; // 主题
            this.getMailTotal();
          }
        });
      }
    }
  }
  chooseemail() {
    let choosed = this.eamilTemplate.filter((res) => {
      return res.valueId == this.radioValue;
    });
    const choosedData = choosed[0];
    let content = choosedData.content;
    if (this.showcCode === 2) {
      content = this.removeMessagePrefixAndSuffix(choosedData.content);
    }
    this.chooseformData = {
      ...choosedData,
      editors: !!choosed[0].id,
      content: content,
    };
  }
  addDefaultemail() {
    // var timestamp = Date.parse(new Date())
    let list = this.eamilTemplate.filter((res) => {
      return !res.id;
    });
    if (list.length == 0) {
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      let monthnew;
      let daynew;
      if (month < 10) {
        monthnew = "0" + month;
      } else {
        monthnew = month;
      }
      if (day < 10) {
        daynew = "0" + day;
      } else {
        daynew = day;
      }
      var nowDate = year + "-" + monthnew + "-" + daynew;
      const template = _.cloneDeep(this.chooseformDataTemplate);
      template.name = "新增模板";
      template.editors = false;
      template.createTime = nowDate;
      template.valueId = date.getMilliseconds().toString();
      template.type = this.templateTypeMap[this.showcCode];
      this.eamilTemplate.unshift(template);
      this.radioValue = date.getMilliseconds().toString();
      this.chooseformData = this.eamilTemplate[0];
    } else {
      // this.msg.warning("请逐一完成新增模板编辑！");
      this.customMsg.open("warning", "请逐一完成新增模板编辑");
    }
  }
  deleteEmail(index) {
    if (!this.eamilTemplate[index].id) {
      this.eamilTemplate.splice(index, 1);
      this.radioValue = this.eamilTemplate[0].id;
      this.chooseformData = this.eamilTemplate[0];
    } else {
      let isDefault = this.eamilTemplate[index].isDefault;
      if (isDefault) {
        // this.msg.warning("默认邮件模板不可删除！");
        const typeStr = this.templateTypeNameMap[this.showcCode];
        this.customMsg.open("error", `默认${typeStr}模板不可删除！`);
      } else {
        let id = this.eamilTemplate[index].id;
        this.api.getDeletelist(id).subscribe((res) => {
          if (res.result.code == 0) {
            this.getEmaillist();
          }
        });
      }
    }
  }
  cancel() {}
  setcustom() {
    if (!this.chooseformData.id) {
      this.eamilTemplate.forEach((res) => {
        res.isDefault = false;
      });
    } else {
      let id = this.chooseformData.valueId;
      let isDefault = !this.chooseformData.isDefault;
      let templateType = this.templateTypeMap[this.showcCode];
      this.api.settingdeffault(id, isDefault, templateType).subscribe((res) => {
        if (res.result.code == 0) {
          this.getEmaillist();
        } else {
          if (res.result.code == 210048) this.chooseformData.isDefault = true;
        }
      });
    }
  }
  getdefaultcontant() {
    this.api.ResetProject(this.chooseformData.valueId).subscribe((res) => {
      if (res.result.code == 0) {
        this.getEmaillist();
      }
    });
    // this.chooseformData.content = JSON.parse(sessionStorage.getItem('defaultcontant'))
  }
  saveInfolist() {
    let json = this.chooseformData;
    if (this.chooseformData.name.length > 50) {
      // this.msg.error("模板名称最多50个字符！");
      this.customMsg.open("error", "模板名称最多50个字符！");
      return;
    }
    if (this.chooseformData.name == "") {
      // this.msg.error("请输入模板名称！");
      this.customMsg.open("error", "请输入模板名称！");
      return;
    }
    if (this.chooseformData.theme.zh_CN == "" && this.showcCode !== 2) {
      const typeStr = this.templateTypeNameMap[this.showcCode];
      this.customMsg.open("error", `请输入${typeStr}主题中文！`);
      return;
    }
    // 邮件验证-内容至少填写一种
    // const contents = Object.values(this.chooseformData.content);
    // if (contents.filter(val => !!val).length === 0) {
    //   this.msg.error('请输入至少一种邮件正文！')
    //   return
    // }
    if (
      !(
        isString(this.chooseformData.content.zh_CN) &&
        this.chooseformData.content.zh_CN.trim() !== ""
      )
    ) {
      // this.msg.error("请输入中文邮件正文！");
      const typeStr = this.templateTypeNameMap[this.showcCode];
      this.customMsg.open("error", `请输入中文${typeStr}正文！`);
      return;
    }
    if (this.showcCode === 2) {
      json.content = this.addMessagePrefixAndSuffix(json.content);
    }
    if (!this.chooseformData.id) {
      this.api.createlist(json).subscribe((res) => {
        if (res.result.code == 0) {
          this.getEmaillist();
          this.chooseformData.editors = true;
          this.msg.success("新增成功！");
        }
      });
    } else {
      this.api.updatelist(json).subscribe((res) => {
        if (res.result.code == 0) {
          this.getEmaillist();
          this.chooseformData.editors = true;
          this.msg.success("更新成功！");
        }
      });
    }
  }

  getcardone() {
    if (this.keyWord === "") {
      this.searchShow = false;
      this.pageIndexChange(1);
    } else {
      this.searchShow = true;
      this.uploadSearchList = [];
      this.listOfParentData.map((res) => {
        let flag = res.name.indexOf(this.keyWord);
        if (flag !== -1) {
          this.uploadSearchList.push(res);
          return;
        } else {
          res.persons.map((item) => {
            let childFlag = item.firstName.indexOf(this.keyWord);
            if (childFlag !== -1) {
              this.uploadSearchList.push(res);
              return;
            }
          });
        }
      });
      this.pageIndexChange(1);
    }
  }

  editorInfolist() {
    this.chooseformData.editors = false;
  }
  radioChange() {
    this.eamilTemplate.forEach((res) => {
      if (!res.editors) {
        res.editors = true;
      }
    });
  }

  getMerge() {
    this.showcards = true;
    if (this.listSimilar.length == 0) {
      this.getlistSame();
    }
  }
  getMerges() {
    this.showcards = false;
  }
  MergeClear() {
    this.ismergeproject = false;
    this.multipleValue = [];
  }
  MergeSunmmit() {
    if (this.multipleValue.length != 0) {
      this.ismergeproject = true;
    } else {
      this.ismergeproject = false;
    }
    this.showcards = false;
    this.listSimilar.forEach((item) => {
      item.checked = false;
      this.multipleValue.forEach((val) => {
        if (item.id == val) item.checked = true;
      });
    });
    this.changeSendUserType("all");
  }
  getShowcards(e) {
    this.showprojectcard = e;
    this.getMailTotal();
  }

  getlistSame() {
    this.api.getlistSimilar(this.projectId).subscribe((res) => {
      this.listSimilar = res.data;
      this.listSimilar.forEach((item) => {
        item.checked = false;
        this.multipleValue.forEach((val) => {
          if (item.id == val) item.checked = true;
        });
      });
    });
  }
  getlistmodalMail(type?, isGetUser?) {
    let ptype = "";
    if (type) ptype = type;
    let templateType = this.templateTypeMap[this.showcCode];
    this.api
      .getlistMail(this.projectId, ptype, templateType, "ANSWER")
      .subscribe((res) => {
        if (res.data) {
          this.multipleValue = res.data.mergeProjectIds
            ? res.data.mergeProjectIds
            : [];
          if (isGetUser) {
            this.changeSendUserType("all");
          }
          this.formData.title = res.data.subject || {};
          this.formData.content = res.data.content || {};
          console.log(this.formData.content);

          this.formData.codeType =
            res.data.answerCodeType == "CAPTCHA" ? "prov" : "one";
          if (this.formData.codeType == "one") {
            this.codemenu = false;
          } else {
            this.codemenu = true;
          }
          console.log(this.formData.codeType);
          if (this.multipleValue.length != 0) {
            this.ismergeproject = true;
          } else {
            this.ismergeproject = false;
          }
          this.getlistSame();
          if (this.showcCode == 1) {
            this.getMailTotal();
          } else {
            if (this.showcCode == 2) {
              this.formData.content = this.removeMessagePrefixAndSuffix(
                res.data.content
              );
            }
            this.nzChangetextarea();
          }
        } else {
          if (this.showcCode == 1) {
            this.getEmailDemo();
          } else if (this.showcCode == 2) {
            this.getSmsDemo();
          } else if (this.showcCode == 3) {
            this.getVxDemo();
          } else if (this.showcCode == 4) {
            this.getDingDemo();
          } else {
            this.getFeiShuDemo();
          }
          this.multipleValue = [];
          if (isGetUser) {
            this.changeSendUserType("all");
          }
        }
      });
  }

  // 自邀请角色人数
  showSelfInviteDrawer(item) {
    this.currentInviteSettings = item.inviteSettings;
    this.currentkey = item.key;
    this.selfInvite.openModal();
  }
  saveInviteSettings(e) {
    this.listOfParentData.forEach((val) => {
      if (val.key == this.currentkey) {
        val.inviteSettings = e;
      }
    });
  }

  // 国际化
  lan = "zh_CN";
  onChangeLan(e) {
    this.lan = e;
  }
  getLans(projectId) {
    this.api.getProjectSetting(projectId).subscribe((res) => {
      if (res.result.code === 0) {
        const nameFactor = res.data.analysisFactorDtos.find(
          (val) => val.basicLabel === "NAME"
        );
        // console.log("人口标签-姓名", nameFactor);
        // if (nameFactor && nameFactor.isHidden) {
        //   this.isHideMask = true;
        // }
        sessionStorage.setItem(
          "projectLanguages",
          JSON.stringify(res.data.availableLanguages)
        );
        sessionStorage.setItem("language", res.data.language);
        this.availableLanguages = res.data.availableLanguages;
      }
    });
  }
  getMailTotal() {
    if (this.showcCode === 2) {
      return;
    }
    let dom: any = null;
    let htmlData = document.querySelector("#formula-tinymce");
    if (
      htmlData &&
      htmlData.children &&
      htmlData.children[0].children[0] &&
      htmlData.children[0].children[0].children[1] &&
      htmlData.children[0].children[0].children[1].children
    ) {
      dom = htmlData.children[0].children[0].children[1].children;
    }
    let text = null;
    if (dom && dom[0]) {
      text = dom[0].contentWindow.document.activeElement.innerHTML;
    } else {
      text = this.formData.content[this.lan];
    }

    let num1 = this.api.getStringSizeInBytes(text);
    let mailStrNum = this.api.getMailTotalSize(num1, this.fileIds);
    let peopleNum = this.peopleNum || 0;
    // console.log(this.peopleNum, "--");
    this.mailTotal = {
      mailStrNum,
      peopleNum,
      countMoney: Math.round(0.05 * mailStrNum * peopleNum * 100) / 100,
    };
  }

  /**
   * 合并发送邀请相关是否展示以及默认值
   * @author:sidwang
   * @Date:2024/12/31
   */
  async getShowTypeCode() {
    this.permission = this.permissionService.isPermission();
    this.isEnableWxWorkMsg = JSON.parse(
      sessionStorage.getItem("isEnableWxWorkMsg")
    );
    this.isEnableDingMsg = JSON.parse(
      sessionStorage.getItem("isEnableDingMsg")
    );
    this.isEnableFeishuMsg = JSON.parse(
      sessionStorage.getItem("isEnableFeishuMsg")
    );
    // 获取邮件功能是否展示
    const res1 = await this.api.isOpenMailSend().toPromise();
    if (res1.result.code === 0) {
      this.isDisplayMail = res1.data || false;
    }
    // 获取短信功能是否展示
    const res2 = await this.api.isOpenMessageSend().toPromise();
    if (res2.result.code === 0) {
      this.isDisplaySMS = res2.data || false;
    }
    const typeArr = [
      { code: 1, isShow: this.isDisplayMail || false }, // 发送邮件
      { code: 2, isShow: this.isDisplaySMS || false }, // 短信邀请
      { code: 3, isShow: this.isEnableWxWorkMsg || false }, // 企业微信
      { code: 4, isShow: this.isEnableDingMsg || false }, // 钉钉
      { code: 5, isShow: this.isEnableFeishuMsg || false }, // 飞书
    ];
    const showType = typeArr.filter((val) => val.isShow);
    this.showcCode = showType[0].code || 0;

    // let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    // this.getlistmodalMail(type);
    //   if (this.showcCode == 1) {
    //     let type = !this.codemenu ? "PRIVATE" : "CAPTCHA";
    //     this.getlistmodalMail(type);
    //   }

    //   if (this.showcCode == 2) {
    //     this.getSmsDemo();
    //   }
    //   if (this.showcCode == 3) {
    //     this.getVxDemo();
    //   }
    //   // 钉钉
    //   if (this.showcCode == 4) {
    //     this.getDingDemo();
    //   }
    //   // 飞书
    //   if (this.showcCode == 5) {
    //     this.getFeiShuDemo();
    //   }
  }
  getshowType() {
    const typeArr = [
      { code: 1, isShow: this.isDisplayMail || false }, // 发送邮件
      { code: 2, isShow: this.isDisplaySMS || false }, // 短信邀请
      { code: 3, isShow: this.isEnableWxWorkMsg || false }, // 企业微信
      { code: 4, isShow: this.isEnableDingMsg || false }, // 钉钉
      { code: 5, isShow: this.isEnableFeishuMsg || false }, // 飞书
    ];
    const showType = typeArr.filter((val) => val.isShow);
    if (showType && showType.length > 0) {
      this.showcCode = showType[0].code;
    } else {
      this.showcCode = 0;
    }
    this.changonecode(this.formData.codeType);
  }

  /**
   * 剔除文本中的前后缀
   * @param text 需要处理的文本
   * @returns 剔除前后缀后的文本
   */
  removeMessagePrefixAndSuffix(content: { [key: string]: string }) {
    const content_ = _.cloneDeep(content);
    Object.keys(content_).forEach((key) => {
      let result = content_[key];
      const prefix = this.messagePrefix[key] || "";
      const suffix = this.messageSuffix[key] || "";
      // 剔除前缀
      if (prefix && result.startsWith(prefix)) {
        result = result.substring(prefix.length);
      }
      // 剔除后缀
      if (suffix && result.endsWith(suffix)) {
        result = result.substring(0, result.length - suffix.length);
      }
      content_[key] = result;
    });
    return content_;
  }
  /**
   * 增加文本中的前后缀
   * @param text 需要处理的文本
   * @returns 增加前后缀后的文本
   */
  addMessagePrefixAndSuffix(content: { [key: string]: string }) {
    const content_ = _.cloneDeep(content);
    Object.keys(content_).forEach((key) => {
      let result = content_[key];
      const prefix = this.messagePrefix[key] || "";
      const suffix = this.messageSuffix[key] || "";
      // 增加前缀
      if (prefix && !result.startsWith(prefix)) {
        result = prefix + result;
      }
      // 增加后缀
      if (suffix && !result.endsWith(suffix)) {
        result = result + suffix;
      }
      content_[key] = result;
    });
    return content_;
  }
}
