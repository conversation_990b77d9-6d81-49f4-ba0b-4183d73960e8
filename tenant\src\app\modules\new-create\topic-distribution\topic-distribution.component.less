.topicDistribution {
  // ::ng-deep {

  //   .ant-modal-body {
  //     padding: 0;
  //   }

  //   .ant-modal-header {
  //     padding: 32px 32px 21px 30px;
  //     border: none;

  //     .ant-modal-title {
  //       font-size: 24px;
  //       font-family: PingFangSC-Thin, PingFang SC;
  //       font-weight: 100;
  //       color: #495970;
  //       line-height: 33px;
  //     }
  //   }

  //   .ant-modal-close {
  //     top: 7px;
  //     right: 7px;

  //     .ant-modal-close-x {
  //       width: auto;
  //       height: auto;
  //       line-height: 1;

  //       .anticon {
  //         color: #495970;
  //       }
  //     }
  //   }

  //   .ant-modal-footer {
  //     border: none;
  //   }
  // }
  &_head {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  &_content {
    height: calc(100% - 40px);
    position: relative;
    display: flex;
    justify-content: space-between;

    // &::after {
    //   content: '';
    //   position: absolute;
    //   width: 100%;
    //   height: 1px;
    //   bottom: 0;
    //   left: 0;
    //   transform: scaleY(0.8);
    //   background-color: #E6E6E6;
    // }

    &_left {
      width: 308px;
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #efefef;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;

      &_list {
        flex: 1 0 402px;
        overflow-y: auto;
        padding: 15px 0 0 15px;

        .populationLabels,
        .dimension {
          .title {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: bold;
            color: #495970;
            line-height: 20px;
          }

          .list {
            display: flex;
            flex-wrap: wrap;

            li {
              width: 130px;
              min-height: 36px;
              background: #ffffff;
              border-radius: 2px;
              margin-top: 10px;

              &:nth-child(odd) {
                margin-right: 16px;
              }
            }
          }
        }

        .dimension {
          margin-top: 20px;
          padding-bottom: 30px;

          &_label {
            display: flex;
            flex-wrap: wrap;

            .label_div {
              width: 134px;
              margin-top: 10px;

              ::ng-deep .ant-checkbox-wrapper {
                .ant-checkbox {
                  top: 3px;
                }
              }

              &:nth-child(odd) {
                margin-right: 14px;
              }
            }
          }
        }
      }

      &_btn {
        width: 100%;
        height: 48px;
        border-top: 1px solid #efefef;
        padding-right: 9px;
        padding-left: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        ::ng-deep {
          .ant-btn-link {
            padding: 0;
          }
        }
        // span {
        //   font-size: 12px;
        //   font-family: PingFangSC-Regular, PingFang SC;
        //   font-weight: 400;
        //   color: #495970;
        //   line-height: 17px;
        //   cursor: pointer;

        //   &.association {
        //     width: 72px;
        //     height: 30px;
        //     border-radius: 15px;
        //     border: 1px solid #409eff;
        //     font-size: 14px;
        //     font-family: PingFangSC-Medium, PingFang SC;
        //     font-weight: 500;
        //     color: #409eff;
        //     line-height: 1;
        //     display: flex;
        //     justify-content: center;
        //     align-items: center;
        //   }
        // }
      }
    }

    &_right {
      width: calc(100% - 326px);
      height: 100%;
      overflow-y: auto;

      li {
        width: 100%;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #efefef;
        padding: 15px 15px 15px 10px;
        margin-bottom: 15px;

        .title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: 5px;

          h5 {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            color: #495970;
            line-height: 20px;
            font-weight: bold;
          }
          // a {
          //   margin-top: -3px;
          //   font-size: 12px;
          //   font-family: PingFangSC-Regular, PingFang SC;
          //   font-weight: 400;
          //   color: #62afff;
          //   line-height: 17px;
          //   cursor: pointer;
          // }
          ::ng-deep {
            .ant-btn-link {
              padding: 0;
            }
          }
        }

        .item {
          display: flex;
          align-items: center;
          flex-wrap: wrap;

          ::ng-deep .ant-tag {
            margin-right: 0;
            margin-left: 5px;
            margin-top: 10px;
            height: 26px;
            padding: 0 8px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #495970;
            border: none;
            background: #f6f6f6;
            border-radius: 2px;
            display: flex;
            align-items: center;

            &:hover {
              background-color: rgba(64, 158, 255, 0.05);

              .anticon {
                color: #62afff;
              }
            }

            .anticon {
              margin-left: 6px;
            }
          }
        }
      }
    }

    .scroll {
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      // 滑块背景
      &::-webkit-scrollbar-track {
        // background-color: transparent;
        background-color: #F1F1F1;
        box-shadow: none;
      }
      // 滑块
      &::-webkit-scrollbar-thumb {
        // background-color: #e9e9e9;
        background-color: #C1C1C1;
        outline: none;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
      }
    }
    .card {
      border-radius: 8px;
      border: 1px solid #efefef;
    }
  }
}
//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}
::ng-deep {
  .round-right-drawer5 {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 108px);
      overflow: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer5-nofooter {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-size: 20px;
      font-weight: bold;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
  &_shade {
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.05) 0%,
      rgba(255, 255, 255, 0) 100%
    );
    width: 960px;
    height: 38px;
    position: absolute;
    top: -48px;
    left: -16px;
  }

  span {
    width: 128px;
    height: 38px;
    text-align: center;
    line-height: 38px;
    border-radius: 19px;
    margin-right: 25px;
    cursor: pointer;
  }

  .primary {
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    color: #fff;
  }

  .default {
    background-color: #fafafa;
    color: #aaaaaa;
    font-weight: 500;
  }
}
