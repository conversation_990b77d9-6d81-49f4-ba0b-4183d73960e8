import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class NewActivityService {
  url: string;
  tenantUrl: string;
  constructor(private http: HttpClient) {
    this.tenantUrl = "/tenant-api";
  }

  getTotal(param): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getTotal`;
    // const httpOptions = {headers : new HttpHeaders({'Content-Type': 'application/json'})};
    return this.http.post(api, param);
  }

  gettoollist(json): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/questionnaire/scene/type/listPageByTypes`;
    return this.http.post(api, json);
  }

  getPermissiontoollist(json): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/listStandardPageByTypes`;
    return this.http.post(api, json);
  }

  getmybestlike(parmas): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/questionnaire/scene/type/listExclusiveQuestionnaire`;
    return this.http.post(api, parmas);
  }

  getPermissionmybestlike(parmas): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/standardExclusiveQuestionnaire/list`;
    return this.http.post(api, parmas);
  }

  // 根据分类列出问卷题目
  getTopicsBasedOnClassification(projectId): Observable<any> {
    const api = `${this.tenantUrl}/survey/question/listOptionQuestionByType?projectId=${projectId}`;
    return this.http.get(api);
  }
}
