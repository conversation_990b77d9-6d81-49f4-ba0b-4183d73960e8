<div class="custom_mock" *ngIf="confirmvisible"></div>
<ul class="modal_show">
  <li class="show_li_left">
    <div
      class="add-box"
      *ngIf="!showAnalysisFactor || (showAnalysisFactor && permission)"
    >
      <button nz-button nzType="primary" nzGhost nzBlock (click)="addNewlist()">
        <i nz-icon nzType="plus" nzTheme="outline"></i>添加人口标签
      </button>
      <div class="custom_xy" *ngIf="confirmvisible">
        <ul class="nz_menu_ul">
          <!-- <li class="nz_menu_li">
            <span>中文</span>
            <input nz-input placeholder="请输入" maxlength="50" [(ngModel)]="prismaData.name_cn" />
          </li>
          <li class="nz_menu_li" style="margin-top: 15px;">
            <span>ENG</span>
            <input nz-input placeholder="请输入" maxlength="50" [(ngModel)]="prismaData.name_eng" />
          </li> -->
          <li class="nz_menu_li">
            <span>中文</span>
            <div class="input">
              <!-- <app-i18n-input [value]="prismaData.name" (changeValue)="changeNameValue($event)" [isDefault]="isDefault"></app-i18n-input> -->
              <app-i18n-input
                [value]="prismaData.name"
                (changeValue)="changeNameValue($event)"
              ></app-i18n-input>
            </div>
          </li>
          <li class="nz_menu_li" style="margin-top: 15px;">
            <span>编码</span>
            <input
              nz-input
              placeholder="请输入"
              maxlength="50"
              [(ngModel)]="prismaData.code"
            />
          </li>
          <li class="list_word">*最多输入50个字符</li>
          <li class="list_button">
            <button nz-button nzType="primary" (click)="getnzOnConfirm('-1')">
              确定
            </button>
            <button nz-button nzType="default" (click)="getnzOnCancel('-1')">
              取消
            </button>
          </li>
        </ul>
      </div>
    </div>
    <div
      class="scroll"
      [ngClass]="showAnalysisFactor ? 'modal-box-view' : 'modal-box-edit'"
      [dragula]="'VAMPIRESCUSFACTOR'"
      [(dragulaModel)]="checklist"
    >
      <div *ngFor="let item of checklist; let i = index">
        <div
          class="modal_div"
          [id]="'scroll' + i"
          (click)="getModalcheck(i)"
          [ngClass]="{ Modalcheckclass: Modalcheck == i }"
        >
          <div
            class="namesdiv"
            *ngIf="item.name.zh_CN.length > 6"
            nz-tooltip
            [(nzTooltipTitle)]="item.name.zh_CN"
          >
            {{ item.name.zh_CN }}
          </div>
          <div class="namesdiv" *ngIf="item.name.zh_CN.length <= 6">
            {{ item.name.zh_CN }}
          </div>
          <ng-container *ngIf="Modalcheck == i">
            <ul
              class="point_xy"
              nz-dropdown
              nzTrigger="click"
              [nzClickHide]="false"
              [(nzVisible)]="item.visible"
              [nzDropdownMenu]="menu"
              (nzVisibleChange)="getcuston(i)"
            >
              <li></li>
              <li></li>
              <li></li>
            </ul>
          </ng-container>
          <nz-dropdown-menu #menu="nzDropdownMenu" class="Nz_menus">
            <ul
              nz-menu
              [ngClass]="{
                modal_div_top: isPositionTop,
                modal_div_bottom: !isPositionTop
              }"
            >
              <li nz-menu-item *ngIf="permission && showAnalysisFactor">
                <div
                  nz-popconfirm
                  nzPopconfirmTitle="是否确认要禁用当前选项?"
                  [nzCondition]="!item.statusNew"
                  (nzOnConfirm)="confirmSwitchParent(i)"
                >
                  <nz-switch
                    [(ngModel)]="item.statusNew"
                    [nzControl]="true"
                    (click)="clickIsVisitAnswerParent($event, i)"
                    nzCheckedChildren="启用"
                    nzUnCheckedChildren="禁用"
                  ></nz-switch>
                </div>
              </li>
              <!-- <li *ngIf="!showAnalysisFactor" nz-menu-item (click)="addlist(i)">新增选项</li> -->
              <li
                *ngIf="!showAnalysisFactor || permission"
                [ngClass]="{ disClick: !item.statusNew }"
                nz-menu-item
                (click)="addlist(i)"
              >
                新增选项
              </li>
              <li>
                <span nz-menu-item (click)="editName(i)">编辑名称</span>
                <div class="custom_xy_1" *ngIf="item.showble">
                  <ul class="nz_menu_ul">
                    <!-- <li class="nz_menu_li">
                      <span>中文</span>
                      <input nz-input placeholder="请输入" maxlength="50" [(ngModel)]="prismaData.name_cn" />
                    </li>
                    <li class="nz_menu_li" style="margin-top: 15px;">
                      <span>ENG</span>
                      <input nz-input placeholder="请输入" maxlength="50" [(ngModel)]="prismaData.name_eng" />
                    </li> -->
                    <li class="nz_menu_li">
                      <span>中文</span>
                      <div class="input">
                        <!-- <app-i18n-input [value]="prismaData.name" (changeValue)="changeNameValue($event)" [isDefault]="isDefault"></app-i18n-input> -->
                        <app-i18n-input
                          [value]="prismaData.name"
                          (changeValue)="changeNameValue($event)"
                        ></app-i18n-input>
                      </div>
                    </li>
                    <li class="nz_menu_li" style="margin-top: 15px;">
                      <span>编码</span>
                      <input
                        nz-input
                        placeholder="请输入"
                        maxlength="50"
                        [disabled]="item.id"
                        [(ngModel)]="prismaData.code"
                      />
                    </li>
                    <li class="list_word">*最多输入50个字符</li>
                    <li class="list_button">
                      <button
                        nz-button
                        nzType="primary"
                        (click)="getnzOnConfirm(i)"
                      >
                        确定
                      </button>
                      <button
                        nz-button
                        nzType="default"
                        (click)="getnzOnCancel(i)"
                      >
                        取消
                      </button>
                    </li>
                  </ul>
                </div>
              </li>
              <li
                *ngIf="
                  reportType === 'DP_INVESTIGATION_RESEARCH_CUSTOM' &&
                  permission
                "
                nz-menu-item
                (click)="checkRelationLabel(i)"
              >
                关联标签
              </li>
              <!-- <li nz-menu-item >
                <span (click)="editReport(i)">报告显示</span>
                <div class="custom_xy_1" *ngIf="item.showReport">
                  <ul class="nz_menu_ul nz_menu_ul_1">
                    <li class="nz_menu_li">
                      <nz-checkbox-wrapper>
                        <div>
                          <div style="margin-bottom: 6px;"
                            *ngFor="let eln of prismaData.labelShowReportPage ;let j = index">
                            <label nz-checkbox [nzValue]="eln.name" [(ngModel)]="eln.checked">{{eln.type}}</label>
                          </div>
                        </div>
                      </nz-checkbox-wrapper>
                    </li>
                    <li class="list_button">
                      <button nz-button nzType="primary" (click)="getRepotrOnConfirm(i)">确定</button>
                    </li>
                  </ul>
                </div>
              </li> -->
              <a
                nz-popconfirm
                nzPopconfirmTitle="确定删除这条选项?"
                nzPopconfirmPlacement="bottom"
                (nzOnConfirm)="deleteFacNameok(i)"
                (nzOnCancel)="cancel()"
              >
                <li
                  nz-menu-item
                  *ngIf="
                    (!showAnalysisFactor && !item.standardDemographicId) ||
                    (showAnalysisFactor && permission && !item.id)
                  "
                >
                  删除
                </li>
                <!-- (click)="deleteFacName(i)" -->
              </a>
            </ul>
          </nz-dropdown-menu>
        </div>
      </div>
    </div>
  </li>
  <li class="show_li_right">
    <div class="right-header">
      <!-- 调研用 -->
      <!-- <app-i18n-select [active]="lan" (selectChange)="onSelectI18n($event)" [isDefault]="isDefault"></app-i18n-select> -->
      <app-i18n-select
        [active]="lan"
        (selectChange)="onSelectI18n($event)"
      ></app-i18n-select>
      <div class="title-handle">
        <nz-upload
          *ngIf="!showAnalysisFactor"
          [nzCustomRequest]="customReqAnalysisFactor"
          [nzShowUploadList]="false"
        >
          <!-- <app-btn [text]="'导入'" [image]="'./assets/images/org/import.png'" [hoverColor]="'#27C091'"
          [hoverImage]="'./assets/images/org/import_hover.png'">
        </app-btn> -->
          <button
            nz-button
            nz-popover
            nzPopoverContent="导入将覆盖已有数据。"
            nzPopoverPlacement="bottom"
            nzType="link"
          >
            <i class="iconfont icon-icon_export"></i> 导入
          </button>
        </nz-upload>
        <!-- <i *ngIf="!showAnalysisFactor"  class="iconfont icon-help-circle"></i> -->
        <div>
          <!-- <app-btn [text]="'导出'" [image]="'./assets/images/org/export.png'" [hoverColor]="'#B483D6'"
          [hoverImage]="'./assets/images/org/export_hover.png'" (btnclick)="exportQusBook()">
        </app-btn> -->
          <button nz-button nzType="link" (click)="exportQusBook()">
            <i class="iconfont icon-icon_import"></i> 导出
          </button>
        </div>
      </div>
    </div>
    <div class="right-box  scroll-textarea">
      <div
        *ngFor="let item of checklist; let j = index"
        (click)="isDragChange($event, 1)"
      >
        <ul
          class="right_ul"
          *ngIf="j == Modalcheck"
          [dragula]="'OPTIONS'"
          [(dragulaModel)]="item.child"
        >
          <li
            class="righg_li "
            *ngFor="let ite of item.child; let k = index"
            [ngClass]="ite.child && ite.child.length != 0 ? 'childclass' : ''"
          >
            <div>
              <span class="li_span">
                <i
                  class="handle iconfont icon-caidan"
                  style="margin-right: 8px;"
                ></i
                >选项 {{ k + 1 }}
              </span>
              <ng-container *ngFor="let parent of i18n">
                <ng-container *ngIf="lan === parent.value">
                  <input
                    class="put_ui"
                    style="width: 616px;"
                    maxlength="50"
                    nz-input
                    placeholder="请输入因子名称({{ parent.name }})"
                    [(ngModel)]="ite.name[parent.value]"
                  />
                </ng-container>
              </ng-container>
              <a
                *ngIf="
                  !showAnalysisFactor ||
                  (showAnalysisFactor && permission && !ite.id)
                "
                nz-popconfirm
                nzPopconfirmTitle="确定删除这条选项?"
                nzPopconfirmPlacement="bottom"
                (nzOnConfirm)="confirm(j, k)"
                (nzOnCancel)="cancel()"
                style="margin-left: 8px;"
              >
                <!-- <i *ngIf="item.child.length > 1" style="cursor: pointer;" nz-icon nzType="delete" nzTheme="twotone"></i> -->
                <i
                  *ngIf="item.child.length > 1"
                  style="cursor: pointer;"
                  class="iconfont icon-icon_delete"
                ></i>
              </a>
            </div>
            <div style="margin-top: 5px;">
              <span class="li_span">
                <ng-container
                  *ngIf="permission && showAnalysisFactor; else blank"
                >
                  <!-- <ng-container *ngIf="false;else blank"> -->
                  <div class="li_span">
                    <div
                      nz-popconfirm
                      nzPopconfirmTitle="是否确认要禁用当前选项?"
                      [nzCondition]="!ite.statusNew"
                      (nzOnConfirm)="confirmSwitch(j, k)"
                    >
                      <nz-switch
                        [(ngModel)]="ite.statusNew"
                        [nzDisabled]="!item.statusNew"
                        [nzControl]="true"
                        (click)="clickIsVisitAnswer($event, j, k)"
                        nzCheckedChildren="启用"
                        nzUnCheckedChildren="禁用"
                      ></nz-switch>
                    </div>
                  </div>
                </ng-container>
                <ng-template #blank>
                  <div class="li_span" style="display: inline-block;"></div>
                </ng-template>
              </span>
              <input
                [disabled]="ite.id"
                class="put_ui"
                style="width: 616px;"
                maxlength="50"
                nz-input
                placeholder="请输入编码"
                [(ngModel)]="ite.code"
              />
            </div>
            <div style="display: flex;justify-content: space-between;">
              <div
                *ngIf="
                  !showAnalysisFactor ||
                  (showAnalysisFactor && permission && ite.statusNew)
                "
                class="add_child"
                (click)="getaddchild(ite.child)"
              >
                添加子选项
              </div>
              <div
                *ngIf="item.isRelationLabel && permission && ite.statusNew"
                class="relation_label"
              >
                <span (click)="relationLabel(ite, item)">关联标签 </span>
                <div
                  class="custom_relation_label_1"
                  *ngIf="ite.showRelationLabel && ite.statusNew"
                >
                  <ul class="nz_menu_ul">
                    <li class="nz_menu_li" style="margin-top: 15px;">
                      <span style="padding-top: 5px;">关联的标签</span>
                      <nz-select
                        [(ngModel)]="ite.demographicMapId"
                        nzAllowClear
                        nzPlaceHolder="请选择要关联的标签"
                        class="relation_label_select"
                      >
                        <nz-option
                          *ngFor="let option of currentRelationLabelList"
                          [nzValue]="option.id"
                          [nzLabel]="option.name.zh_CN"
                        ></nz-option>
                      </nz-select>
                    </li>
                    <li class="list_button">
                      <button
                        nz-button
                        nzType="primary"
                        (click)="relationLabelOnConfirm(ite, i)"
                      >
                        确定
                      </button>
                      <button
                        nz-button
                        nzType="default"
                        (click)="relationLabelOnCancel(ite, i)"
                      >
                        取消
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- <div *ngIf="!showAnalysisFactor" class="add_child" (click)="getaddchild(ite.child)">添加子选项</div> -->
            <!-- <ul class="over_high scroll-textarea" [dragula]="'CHILDOPTIONS' + k" [(dragulaModel)]="ite.child"> -->
            <ul [dragula]="'CHILDOPTIONS' + k" [(dragulaModel)]="ite.child">
              <ul
                *ngFor="let val of ite.child; let l = index"
                class="righg_li"
                style="margin-left: 64px;padding-right: 0;"
              >
                <div>
                  <span class="li_span">子选项 {{ l + 1 }}</span>
                  <ng-container *ngFor="let son of i18n">
                    <ng-container *ngIf="lan === son.value">
                      <input
                        maxlength="50"
                        nz-input
                        placeholder="请输入因子名称({{ son.name }})"
                        [(ngModel)]="val.name[son.value]"
                        style="width: 535px;"
                      />
                    </ng-container>
                  </ng-container>
                  <a
                    *ngIf="
                      !showAnalysisFactor ||
                      (showAnalysisFactor && permission && !val.id)
                    "
                    nz-popconfirm
                    nzPopconfirmTitle="确定删除这条选项?"
                    nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="confirmchild(k, l, item.child)"
                    (nzOnCancel)="cancel()"
                    style="margin-left: 8px;"
                  >
                    <!-- <i style="cursor: pointer;" nz-icon nzType="delete" nzTheme="twotone"></i> -->
                    <i
                      style="cursor: pointer;"
                      class="iconfont icon-icon_delete"
                    ></i>
                  </a>
                </div>
                <div style="margin-top: 5px;">
                  <span class="li_span">
                    <ng-container *ngIf="permission && showAnalysisFactor">
                      <div class="li_span">
                        <div
                          nz-popconfirm
                          nzPopconfirmTitle="是否确认要禁用当前选项?"
                          [nzCondition]="!val.statusNew"
                          (nzOnConfirm)="confirmSwitchChild(j, k, l)"
                        >
                          <nz-switch
                            [(ngModel)]="val.statusNew"
                            [nzDisabled]="!ite.statusNew"
                            [nzControl]="true"
                            (click)="clickIsVisitAnswerChild($event, j, k, l)"
                            nzCheckedChildren="启用"
                            nzUnCheckedChildren="禁用"
                          ></nz-switch>
                        </div>
                      </div>
                    </ng-container>
                  </span>
                  <input
                    [disabled]="val.id"
                    maxlength="50"
                    nz-input
                    placeholder="请输入编码"
                    [(ngModel)]="val.code"
                    style="width: 535px;"
                  />
                </div>
              </ul>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </li>
</ul>
<div class="footer">
  <ng-container *ngIf="!showAnalysisFactor">
    <button type="button" (click)="settingBack()" class="ant-btn">
      <span>恢复默认</span>
    </button>
    <button
      nz-button
      nzType="primary"
      [nzLoading]="isLoadingOne"
      (click)="settingSave()"
    >
      确定
    </button>
  </ng-container>
  <ng-container *ngIf="showAnalysisFactor">
    <button type="button" (click)="goback()" class="ant-btn">
      <span>返回</span>
    </button>
    <button
      nz-button
      nzType="primary"
      [nzLoading]="isLoadingOne"
      (click)="settingSave()"
    >
      确定
    </button>
  </ng-container>
</div>
