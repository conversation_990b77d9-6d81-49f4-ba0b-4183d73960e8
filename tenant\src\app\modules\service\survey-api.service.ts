import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class SurveyApiService {
  private url: string;
  private tenantUrl: string;
  private httpOptions = {};

  constructor(private http: HttpClient) {
    this.tenantUrl = "/tenant-api";
    this.httpOptions = {
      headers: new HttpHeaders({ "Content-Type": "application/json" }),
    };
  }

  public getQuestionsByProjId(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/listSurveyQuestionnaireAndQuestion/${projectId}`;
    return this.http.get(url);
  }

  public getBackupQuestionsByProjId(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/listOptionQuestion?projectId=${projectId}`;
    return this.http.get(url);
  }

  public addQuestions(param: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/addOptionQuestion`;
    return this.http.post(url, param);
  }

  public replaceQuestion(param: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/replaceQuestion`;
    return this.http.post(url, param);
  }

  public deleteQuestionById(id: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/logicDelete/${id}`;
    return this.http.post(url, {});
  }

  public addOpenQuestion(param: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/createOpenQuestion`;
    return this.http.post(url, param);
  }

  public getProjectById(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/get/${projectId}`;
    return this.http.get(url);
  }

  //之前prisma用来确认的关联任务接口
  // public confirmQuestionBook(projectId: string): Observable<any> {
  //   const url = `${this.tenantUrl}/survey/project/confirmQuestionBook?projectId=${projectId}`
  //   return this.http.post(url, {});
  // }

  //确认关联任务
  confirmRelation(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/confirmRelationPermission`;
    return this.http.post(url, json);
  }

  // public confirmOrganization(projectId: string): Observable<any> {
  //   const url = `${this.tenantUrl}/survey/project/confirmOrganization?projectId=${projectId}`
  //   return this.http.post(url, {});
  // }

  public getNairById(id: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/get/${id}`;
    return this.http.get(url);
  }

  public updateLanguageInfo(param: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/updateLanguageInfo`;
    return this.http.post(url, param);
  }

  public getOrgsByProjectId(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listFirstLevelOrganization/${projectId}`;
    return this.http.get(url);
  }

  public listFirstLevelOrganizationByPage(param: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listFirstLevelOrganizationByPage`;
    return this.http.post(url, param);
  }

  public getOrganizationPersonByPage(param: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listOrganizationPersonByPage`;
    return this.http.post(url, param);
  }

  public deletePerson(id: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/delete`;
    return this.http.post(url, { id });
  }

  public listOrganizationByTree(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listOrganizationByTree/${projectId}`;
    return this.http.get(url);
  }

  public listDemographicByProjectId(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/demographic/listByProjectId/${projectId}`;
    return this.http.get(url);
  }

  public listNormsByProjectId(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/norm/prisma/listByProjectId/${projectId}`;
    return this.http.get(url);
  }

  public createPrismaReport(param: any): Observable<any> {
    const url = `${this.tenantUrl}/sagittarius/report/content/createPrismaReport`;
    return this.http.post(url, param);
  }

  public exportPrismaDimensions(id: any): Observable<any> {
    const url = `${this.tenantUrl}/sagittarius/report/dimension/score/exportPrisma`;
    let param = { prismaReportDataId: id };
    return this.http.post(url, param, {
      responseType: "blob",
      observe: "response",
    });
  }

  public uploadFilePerson(
    formData,
    type: number,
    projectId?: string,
  ): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/readPersonExcel`;
    if (type !== 3) {
      api = `${this.tenantUrl}/survey/project/readPersonExcel`;
    }
    if (projectId) {
      api = api + `?projectId=${projectId}`;
    }
    return this.http.post(api, formData);
  }

  public importAnswer(formData, projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/importAnswer`;
    api = api + `?projectId=${projectId}`;
    return this.http.post(api, formData);
  }

  // 敬业度批量逻辑刪除题目
  public batchLogicDelete(params): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/batchLogicDelete`;
    return this.http.post(api, params);
  }
  // 导出prisma题本包含数据
  exportPrismaQuestionBook(questionnaireId: string): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    let api =
      `${this.tenantUrl}/survey/question/prismaExport?questionnaireId=` +
      questionnaireId;
    return this.http.get(api, httpOptions);
  }

  // 导入prisma题本
  uploadPrismaQuestionBook(formData, questionnaireId): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/prismaImport?questionnaireId=${questionnaireId}`;
    return this.http.post(api, formData);
  }

  // 导入prisma题本
  uploadPrismaCustomQuestionBook(formData, questionnaireId): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/prismaCustomImport?questionnaireId=${questionnaireId}`;
    return this.http.post(api, formData);
  }

  // 题本拖拽排序
  reSort(ids, questionnaireId): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/reSort/${questionnaireId}`;
    return this.http.post(api, ids);
  }

  // 调研  查询人口学填答率
  searchDemographicAnswerRate(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/pageDemographicAnswerRate`;
    return this.http.post(api, params);
  }
  // 调研  查询组织部门填答率
  pageOrganizationAnswerRate(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/pageOrganizationAnswerRate`;
    return this.http.post(api, params);
  }
  // 调研  导出组织部门填答率
  exportOrganizationAnswerRate(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/exportOrganizationAnswerRate`;
    return this.http.post(api, params, {
      responseType: "blob",
      observe: "response",
    });
  }
  // 调研  导出人口信息填答率
  exportDemographicAnswerRate(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/exportDemographicAnswerRate`;
    return this.http.post(api, params, {
      responseType: "blob",
      observe: "response",
    });
  }
  // 调研  查询组织树
  listChildOrganizationsByProjectId(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/listChildOrganizationsByProjectId/${projectId}`;
    return this.http.get(api);
  }

  // 详情权限  展示活动主管理员
  getListMainManager(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/listMainManager?projectId=${projectId}`;
    return this.http.get(api);
  }

  // 详情权限  展示活动主管理员 穿梭 右侧
  getListUnSelectMainManager(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/listUnSelectMainManager?projectId=${projectId}`;
    return this.http.get(api);
  }

  // 详情权限  展示活动子管理员
  getListSubManager(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/listSubManager?projectId=${projectId}`;
    return this.http.get(api);
  }

  // 详情权限  创建主管理
  createMainManager(
    projectId: string,
    params: {
      projectId: string;
      userId: string;
    }[]
  ): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/createMainManager?projectId=${projectId}`;
    return this.http.post(api, params);
  }

  // 获取组织架构等数据
  getSubPermissionContent(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/getSubPermissionContent?projectId=${projectId}`;
    return this.http.get(api);
  }

  // 详情创建子管理
  creatSubManager(projectId: string, json: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/creatSubManager?projectId=${projectId}`;
    return this.http.post(api, json);
  }

  // 获取当前活动权限
  getCurrentUserProjectPermission(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/user/getCurrentUserProjectPermission?projectId=${projectId}`;
    return this.http.get(api);
  }
  deleteAll(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/deleteAllEvaluators?projectId=${projectId}`;
    return this.http.post(api, {});
  }
  // 获取当前活动的所有组织信息以及人口学信息
  getBatchExportAnswerRateBasicData(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/getBatchExportAnswerRateBasicData?projectId=${projectId}`;
    return this.http.get(api);
  }
  //执行批量导出
  batchExportAnswerRate(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/batchExportAnswerRate`;
    return this.http.post(api, params, {
      responseType: "blob",
      observe: "response",
    });
  }
  //清除人员
  clearPersonAll(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/clearAllAnswerData?projectId=${projectId}`;
    return this.http.post(api, {});
  }

 // 导入子管理员
 importSubManager(formData, projectId: string): Observable<any> {
  let api = `${this.tenantUrl}/survey/project/importUserPermission?projectId=${projectId}`;
  return this.http.post(api, formData);
}
 // 导出子管理员
 exportSubManager(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/exportUserPermission?projectId=${projectId}`;
    return this.http.post(api, null, {
      responseType: "blob",
      observe: "response",
    });
}

}
