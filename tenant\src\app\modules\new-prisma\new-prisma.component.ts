import { Component, OnInit, ViewChild, Inject } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import {
  NzModalService,
  NzMessageService,
  NzTreeComponent,
  NzFormatEmitEvent,
  NzTreeNode,
} from "ng-zorro-antd";
import { NzDrawerService } from "ng-zorro-antd/drawer";
import { UploadFile, UploadXHRArgs } from "ng-zorro-antd/upload";
import _ from "lodash";
import { NewPrismaService } from "./new-prisma.service";
import { AdvancedSetting } from "./adv_set/advsetting.component";
import { IndexAnalysisComponent } from "./index-analysis/index-analysis.component";
import { ReportSettings } from "./report-settings/report-settings.component";
import { ReportEscription } from "./report-escription/report-escription.component";
import { CorrelationCoefficientComponent } from "./correlation-coefficient/correlation-coefficient.component";
// 交叉分析
import { CrossAnalysisComponent } from "./cross-analysis/cross-analysis.component";
// 多群体分析
import { GroupAnalysisComponent } from "./group-analysis/group-analysis.component";
// 多模块人口显示
import { ReportDisplay } from "./report-display/report-display.component";
// 数据呈现/计算规则
import { DataShowCalRuleComponent } from "./data-show-cal-rule/data-show-cal-rule";

import * as differenceInCalendarDays from "date-fns/difference_in_calendar_days";
import { timer, Observable, Observer } from "rxjs";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { ModalContentComponent } from "../../shared/tip-modal/modal-content/modal-content.component";
import { DragulaService } from "ng2-dragula";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import { PermissionService } from "../service/permission-service.service";

@Component({
  selector: "app-new-prisma",
  templateUrl: "./new-prisma.component.html",
  styleUrls: ["./new-prisma.component.less"],
})
export class NewPrismaComponent implements OnInit {
  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent!: NzTreeComponent;
  @ViewChild("nzTreeComponentRenkou", { static: false })
  nzTreeComponentRenkou!: NzTreeComponent;
  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/new-activity",
      name: "新建活动",
      Highlight: false,
    },
    {
      path: "",
      name: "活动设置",
      Highlight: true,
    },
  ];
  tenantApi: string = "/tenant-api";
  showname = false;
  echartData = [];
  isVisiblemodal = false;

  expandedRenNodes: any[];
  searchRenValue: string;
  checkedKeysRenkou: any[];

  projectId: null;
  projectCode: string;
  JumpUrl: string;
  showIndex = 0;
  confirmvisible = false;
  addfactorshow = false; //自定义弹窗
  nzBodyStyle: any = { padding: "0" };
  standardQuestionnaireId: string; //获取数据用的id
  dateRange = []; //活动周期

  IsAdd = false; //是否添加人口标签
  today = new Date();
  modelImageUrl = "";
  showmock = false;
  noviceGuidance = false;
  formDataUrl = null;
  avatarUrl = "";
  showAnalysisFactor: boolean = false; // 人口标签查看  or 自定义
  analysisFactorTitle: string = "自定义"; // 人口标签查看  or 自定义
  //所有数据
  prismaData: any = {
    factorname: {},
    isShowOrganization: false,
    isShowQuestionBook: false,
    isConfirmOrganization: false, //组织架构
    isConfirmQuestionBook: false, //题本
    isShowanswer: false,
    isShowPreAnswer: true,
    isConfirmanswer: false,
    isShowPassages: false,
    isShowLottery: false,
    name: {
      zh_CN: "",
      en_US: "",
    },
    startTime: null,
    endTime: null,

    answerEffectiveRange: "1", //默认有效填答
    answerEffectiveTime: "1", //默认时间
    answerSameRate: "100", //默认填答一致性
    isOpenValidQuestion: false, //填答有效题本
    isNotCalculateLackAnswer: false, //【因不计分/未填写题本等】致不足有效人数
     
    questionNumInOnePage: "5",
    sequence: "QUESTION_TYPE",
    analysisFactorDto: [], //选中的人口标签  只有敬业度传这个
    isCheckLicenseNotice: true, //许可声明
    isCheckedAnswerValid: false, //是否检查答案是否有效

    isEnableWelcomePage: false,
    isEnableEndPage: false,

    welcomePage: {
      zh_CN: "",
      en_US: "",
    },
    endPage: {
      zh_CN: "",
      en_US: "",
    },

    isCheckAnswerInstructions: true, //作答说明
    isPublicReport: false, //允许测评者查看报告
    isShowDemographicQuestion: true, //是否显示人口信息学题
    language: "zh_CN",
    optionalLanguages: ["zh_CN"],
    availableLanguages: ["zh_CN", "en_US"], // 新增语言
    isShowKnxLogo: true, //显示射手座logo
    isShowBackgroundPic: false,
    logoFile: "", //企业客户logo
    pcBackgroundPic: "",
    mobileBackgroundPic: "",
    standardDemographicDTO: {
      standardDemographicIds: [],
      type: "DEMOGRAPHIC_DEFAULT", // 人口学 默认
    }, //敬业度不用传这个

    standardQuestionnaireDTO: {
      // code: 'JYD_PRISMA',
      standardQuestionnaireDimensionDTO: [],
    },
    surveyType: "EMPLOYEE_ENGAGEMENT",
    answerDescription: {
      zh_CN: "",
      en_US: "",
    },
    isQuestionCustomSort: false,
    isAutoPage: true, //  一页一题时，自动翻页
    isAutoFillQuestionBook: false, // 题本分发致页面题目有缺时，自动补位后续题本
  };

  isSpinning = false;
  isNzOkLoading = false;
  isNzRELoading = false;
  isNzPRELoading = false;

  permission: boolean = this.permissionService.isPermission();

  isUpdate: boolean = false;
  isUpdateing: boolean = false;
  projectType: string = "";
  visibleDesc = false;
  Isactivelan = true;
  questionnaireId;
  nzSimple = false;
  shownumber = 0;
  isShowpagebook = false;
  showRelations = false;
  pageshow = "0";
  pageItem;
  Organizationlist: any = {};
  Factorslist: any = [];
  listProjectlist = [];

  OrganizesearchValue;
  FactorsearchValue;
  Step = 1;
  ProjectIdItem: any = [];

  organizationCode = "";
  organizationId = "";
  demographicId = [];
  questionId = [];
  ShowQuestion = [];
  confirmModal;
  reportType;

  backUrl: string = "/new-activity";
  backUrlName: string = "新建活动";
  sunecharts = false;

  lan = "zh_CN";

  i18n = [];
  isShowAll = true; //一键显示/隐藏人口学标签
  // 题本分发导出loading
  isDispenseDownLoadSpinning: boolean = false;
  customStyle = {
    background: "#F8F8F8",
    borderRadius: " 8px",
    border: "1px solid #ECECEC",
    marginBottom: "16px",
    overflow: "hidden",
  };
  constructor(
    private routerInfo: ActivatedRoute,
    private router: Router,
    private Http: NewPrismaService,
    private modalService: NzModalService,
    private drawerService: NzDrawerService,
    private msg: NzMessageService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private HttpClient: HttpClient,
    private modal: NzModalService,
    private dragulaService: DragulaService,
    private customMsg: MessageService,
    public knxFunctionPermissionService: KnxFunctionPermissionService,
    public permissionService: PermissionService
  ) {
    dragulaService.createGroup("VAMPIRESNEWPRISMA", {
      moves: (el, container, handle) => {
        // 保证只有图标才能拖动
        return handle.className.includes("mover");
      },
    });
  }

  tmp: any = {};
  prismatitle: any = {
    name: "",
    description: "",
  };
  changeeditorName: any = {};
  custom: boolean = false;
  emptyShow: boolean = false; // 显示自定义空状态
  tinyconfig = {};

  changeHidden(e, item, index) {
    e.stopPropagation();
    item.isHidden = !item.isHidden;
  }

  ngOnInit(): void {
    this.backUrl = localStorage.getItem("backurl");
    const url = localStorage.getItem("backurl").split("?")[0];
    switch (url) {
      case "/project-manage/home-detail":
        this.backUrlName = "活动详情";
        break;
      case "/project-manage/home":
        this.backUrlName = "活动管理";
        break;
      case "/new-activity":
        this.backUrlName = "新建活动";
        break;

      default:
        this.backUrlName = "新建活动";
        break;
    }

    this.Breadcrumbs[1].name = this.backUrlName;
    this.Breadcrumbs[1].path = this.backUrl;

    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    const _this = this;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.tinyconfig = {
      height: 300,
      fontsize_formats:
        "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 32pt 36pt",
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      toolbar: [
        "removeformat undo redo formatselect fontsizeselect bold italic underline strikethrough forecolor backcolor",
        "bullist numlist alignleft aligncenter alignright alignjustify outdent indent image link",
      ],
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      images_upload_url: `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`, // 配置你图片上传的url
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.HttpClient.post(
          `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`,
          formData,
          { headers: headers }
        ).subscribe(
          (response: any) => {
            if (response) {
              this.HttpClient.get(
                `${this.tenantApi}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                { headers: headers }
              ).subscribe((imgurl: any) => {
                let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                success(url);
              });
            } else {
              if (response && response.rtnMsg) {
                failure(response.rtnMsg);
              } else {
                failure("上传失败：未知错误");
              }
            }
          },
          (error1) => {
            failure("上传失败：未知错误");
          }
        );
      },
    };
    this.projectId = this.routerInfo.snapshot.queryParams.projectId; //修改用的
    this.projectCode = this.routerInfo.snapshot.queryParams.projectCode;
    if (this.routerInfo.snapshot.queryParams.type) {
      this.projectType = this.routerInfo.snapshot.queryParams.type;
    }

    //ANNOUNCED   ANSWERING
    if (this.projectId) {
      this.getProjectSetting();
    } else {
      this.setLanOptions();
      this.prismaData.standardQuestionnaireDTO = JSON.parse(
        localStorage.getItem("standardQuestionnaireDimensionDTO")
      );

      let newtitle = this.prismaData.standardQuestionnaireDTO;
      this.prismatitle.name =
        newtitle.standardQuestionnaireDimensionDTO[0].name;
      this.prismatitle.description =
        newtitle.standardQuestionnaireDimensionDTO[0].description;
      this.standardQuestionnaireId = this.routerInfo.snapshot.queryParams.id
        ? this.routerInfo.snapshot.queryParams.id
        : this.prismaData.standardQuestionnaireDTO
            .standardQuestionnaireDimensionDTO[0].questionnaireId;
      this.getdefaultMenus(this.projectId, this.standardQuestionnaireId);
    }
  }

  ngOnDestroy() {
    // 销毁事件
    this.dragulaService.destroy("VAMPIRESNEWPRISMA");
  }
  getProjectSetting() {
    this.Http.getProjectSetting(this.projectId).subscribe((res) => {
      // 存储高级设置-可选语言
      sessionStorage.setItem(
        "projectLanguages",
        JSON.stringify(res.data.availableLanguages)
      );
      sessionStorage.setItem("language", res.data.language);
      // 获取当前活动语言
      this.getLanOptions();
      // this.lan = res.data.optionalLanguages[0] || 'zh_CN'
      this.prismatitle.name = res.data.questionnaires[0].name;
      this.prismatitle.description = res.data.questionnaires[0].description;
      this.projectType = res.data.status;
      this.questionnaireId = res.data.questionnaires[0].id;
      this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO = [];
      res.data.questionnaires.forEach((val) => {
        this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO.push(
          {
            name: val.name.zh_CN,
            questionnaireId: res.data.standardQuestionnaireIds[0],
            standardQuestionnaireDimensionIds: [],
            styles: val.styles,
          }
        );
      });
      this.prismaData.name = res.data.projectName;
      this.prismaData.startTime = res.data.startTime;
      this.prismaData.endTime = res.data.endTime;
      this.dateRange = [res.data.startTime, res.data.endTime];

      // 高级设置属性
      this.prismaData.isShowKnxLogo = res.data.isShowKnxLogo;
      this.prismaData.isShowBackgroundPic = res.data.isShowBackgroundPic;
   
      this.prismaData.isCheckLicenseNotice = res.data.isCheckLicenseNotice;
      this.prismaData.isCheckedAnswerValid = res.data.isCheckedAnswerValid;
      this.prismaData.isEnableWelcomePage = res.data.isEnableWelcomePage;
      this.prismaData.isEnableEndPage = res.data.isEnableEndPage;
      this.prismaData.isShowPreAnswer = res.data.isShowPreAnswer;
      this.prismaData.isEnableAnalysisCustomIndex =
        res.data.isEnableAnalysisCustomIndex; // 是否开启分析指数
      this.prismaData.isEnableReportSettings = res.data.isEnableReportSettings; // 是否开启分析指数
      this.prismaData.isEnableAnalysisTask = res.data.isEnableAnalysisTask; // 是否开启相关分析
      this.prismaData.welcomePage = res.data.welcomePage
        ? res.data.welcomePage
        : {};
      this.prismaData.endPage = res.data.endPage ? res.data.endPage : {};

      this.prismaData.isCheckAnswerInstructions =
        res.data.isCheckAnswerInstructions;

      this.prismaData.language = res.data.language;
      this.prismaData.optionalLanguages = res.data.optionalLanguages;
      this.prismaData.availableLanguages = res.data.availableLanguages.length
        ? res.data.availableLanguages
        : ["zh_CN", "en_US"];
      this.prismaData.logoFile = res.data.logoFile
        ? res.data.logoFile.split("/")[3]
        : null;
      this.prismaData.pcBackgroundPic = res.data.pcBackgroundPic
        ? res.data.pcBackgroundPic.split("/")[3]
        : null;
      this.prismaData.mobileBackgroundPic = res.data.mobileBackgroundPic
        ? res.data.mobileBackgroundPic.split("/")[3]
        : null;
      this.prismaData.answerEffectiveRange =
        res.data.answerEffectiveRange || "1";
      this.prismaData.answerEffectiveTime = res.data.answerEffectiveTime;
      this.prismaData.answerSameRate = res.data.answerSameRate;
      this.prismaData.questionNumInOnePage = res.data.questionNumInOnePage;
      this.prismaData.isDimensionAlgorithmCustom = res.data
        .isDimensionAlgorithmCustom
        ? res.data.isDimensionAlgorithmCustom
        : false; // 调研平均计算层级
      this.prismaData.calculationMethod = res.data.calculationMethod
        ? res.data.calculationMethod
        : "STANDARD_APPROVAL_DEGREE"; // 调研-赞成度/平均分-计算方式
      this.prismaData.coefficientAlgorithm = res.data.coefficientAlgorithm
        ? res.data.coefficientAlgorithm
        : "CORRELATION"; // 调研交叉计算方式
      this.prismaData.calculationApprovalType = res.data.calculationApprovalType
        ? res.data.calculationApprovalType
        : "APPROVAL_NEUTRAL_DISAPPROVAL"; // 调研交叉计算方式
      this.prismaData.signAlgorithm = res.data.signAlgorithm; // 调研计算方式 指数
      this.prismaData.oneRankDimensionAlgorithm =
        res.data.oneRankDimensionAlgorithm; // 调研计算方式 一级
      this.prismaData.twoRankDimensionAlgorithm =
        res.data.twoRankDimensionAlgorithm; // 调研计算方式 2
      this.prismaData.threeRankDimensionAlgorithm =
        res.data.threeRankDimensionAlgorithm; // 调研计算方式 3
      this.prismaData.sequence = res.data.sequence;
      this.prismaData.isEnableQuestionBookDistribute =
        res.data.isEnableQuestionBookDistribute;
      this.prismaData.isEnableAnswerLotteryDraw =
        res.data.isEnableAnswerLotteryDraw;
      this.prismaData.isOpenValidQuestion =
        res.data.isOpenValidQuestion || false; // 有效性题本
      this.prismaData.isNotCalculateLackAnswer =
        res.data.isNotCalculateLackAnswer || false; // 不计算【因不计分/未填写题本等】致不足有效人数
      this.prismaData.isShow = res.data.isShow;
      this.prismaData.isQuestionCustomSort =
        res.data.isQuestionCustomSort || false; //每页题数是否自定义
      this.prismaData.isAutoPage =
        res.data.isAutoPage !== undefined ? res.data.isAutoPage : true; //一页一题是否自动翻页
      this.prismaData.isAutoFillQuestionBook =
        res.data.isAutoFillQuestionBook !== undefined
          ? res.data.isAutoFillQuestionBook
          : false; //题本分发致页面题目有缺时，自动补位后续题本
      this.standardQuestionnaireId = res.data.standardQuestionnaireIds[0];
      this.getCreateaCodePrisma(this.standardQuestionnaireId, this.projectId);

      if (this.projectType == "ANNOUNCED" || this.projectType == "PREVIEW") {
        this.isUpdate = true; //未发布状态下的更新
      }

      if (
        this.projectType != "PREVIEW" &&
        this.projectType != "ANNOUNCED" &&
        this.projectType != undefined
      ) {
        this.isUpdateing = true; //发布状态下的更新
      }
    });
  }

  getprojectDefault(id) {
    this.Http.getProject(id).subscribe((res) => {
      if (res.result.code == 0) {
        this.prismaData.isConfirmQuestionBook = res.data.isConfirmQuestionBook;
        this.prismaData.isConfirmOrganization = res.data.isConfirmOrganization;

        this.prismaData.factorname.detailedScoreConfigs.forEach((res) => {
          res.detailedScoreChildDimensions.forEach((val) => {
            this.echartData.push({
              name: val.name.zh_CN,
              value: Math.ceil(Math.random() * 30),
              parentName: res.name.zh_CN,
              children: res.detailedScoreChildDimensions,
            });
          });
        });
        this.echartData = this.classification(this.echartData);
      } else {
      }
    });
  }

  isZero(m) {
    return m < 10 ? "0" + m : m;
  }

  formatDate(shijianchuo, type) {
    const time = new Date(shijianchuo); // 需要使用Date格式进行日期转化，若是时间戳、字符串时间，需要通过new Date(..)转化

    const y = time.getFullYear();

    const m = time.getMonth() + 1;

    const d = time.getDate();

    const h = time.getHours();

    const mm = time.getMinutes();

    const s = time.getSeconds();
    if (type === "date") {
      return y + "-" + this.isZero(m) + "-" + this.isZero(d);
    } else {
      return this.isZero(h) + ":" + this.isZero(mm) + ":" + this.isZero(s);
    }
  }

  onChange(result: Date): void {
    if (result[0]) {
      if (!this.prismaData.startTime) {
        this.dateRange[0].setHours(0);
        this.dateRange[0].setMinutes(0);
      } else {
        const startTime_ = new Date(this.prismaData.startTime);
        if (
          startTime_.getHours() !== this.dateRange[0].getHours() &&
          startTime_.getMinutes() !== this.dateRange[0].getMinutes()
        ) {
          this.dateRange[0].setHours(startTime_.getHours());
          this.dateRange[0].setMinutes(startTime_.getMinutes());
        }
      }
      this.prismaData.startTime = `${this.formatDate(
        this.dateRange[0],
        "date"
      )}T${this.formatDate(this.dateRange[0], "time")}`;
    } else {
      this.prismaData.startTime = null;
    }

    if (result[1]) {
      if (!this.prismaData.endTime) {
        this.dateRange[1].setHours(23);
        this.dateRange[1].setMinutes(30);
      } else {
        const endTime_ = new Date(this.prismaData.endTime);
        if (
          endTime_.getHours() !== this.dateRange[1].getHours() &&
          endTime_.getMinutes() !== this.dateRange[1].getMinutes()
        ) {
          this.dateRange[1].setHours(endTime_.getHours());
          this.dateRange[1].setMinutes(endTime_.getMinutes());
        }
      }
      this.prismaData.endTime = `${this.formatDate(
        this.dateRange[1],
        "date"
      )}T${this.formatDate(this.dateRange[1], "time")}`;
    } else {
      this.prismaData.endTime = null;
    }

    this.prismaData.dateRange = this.dateRange;
  }

  disabledDate = (current: Date): boolean => {
    // Can not select days before today and today
    return differenceInCalendarDays(current, this.today) < 0;
  };
  /**
   *
   * 指数分析 弹窗
   */
  showAnalytical() {
    const tplModal = this.drawerService.create({
      nzTitle: "指数分析",
      nzContent: IndexAnalysisComponent,
      // nzFooter: null,
      nzWrapClassName: "round-right-drawer6",
      nzContentParams: {
        projectId: this.projectId,
      },
      nzWidth: 1200,
      // nzMaskClosable: false,
      // nzClosable: false,
    });
  }

  /**
   * 多模块人口显示弹框
   *@author:wangxiangxin
   *@Date:2023/09/14
   */
  showReportDisplay() {
    const tplModal = this.drawerService.create({
      nzTitle: "多模块人口显示",
      nzContent: ReportDisplay,
      // nzFooter: null,
      nzWrapClassName: "round-right-drawer6",
      nzContentParams: {
        standardAnalysisFactorVOS: this.prismaData.factorname
          .standardAnalysisFactorVOS,
        projectId: this.projectId,
        reportType: this.reportType,
      },
      nzWidth: 960,
      // nzMaskClosable: false,
      // nzClosable: false,
    });

    tplModal.afterClose.subscribe((data) => {
      // 高级设置保存调用
      if (data) {
        this.prismaData.factorname.standardAnalysisFactorVOS = data;
        this.prismaData.analysisFactorDto = data;
      }
    });
  }
  /**
   *报告设置弹框
   *@author:wangxiangxin
   *@Date:2023/08/23
   */
  async showReportSettings() {
    const reportList = [];
    // 报告说明在那些活动下展示 'INVESTIGATION_RESEARCH_CUSTOM', 腾讯 网易 爱普森
    // const typeList = ['TENCENT_INVESTIGATION_RESEARCH_CUSTOM','NETEASE_INVESTIGATION_RESEARCH_CUSTOM']
    // 开放给所有调研
    if (
      this.reportType === "TENCENT_INVESTIGATION_RESEARCH_CUSTOM" ||
      this.reportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM" ||
      this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM" ||
      this.reportType === "OC_INVESTIGATION_RESEARCH" ||
      this.reportType === "INVESTIGATION_RESEARCH_CUSTOM" ||
      this.reportType === "INVESTIGATION_RESEARCH" ||
      this.reportType === "EPSON_INVESTIGATION_RESEARCH_CUSTOM"
    ) {
      reportList.push({
        name: "报告说明",
        title: "我们的方法论是",
        img: "assets/images/report_sttting_1.png",
        img_dis: "assets/images/report_sttting_dis_1.png",
        btnText: "进入设置",
        labelText: "可替换",
        disabled: !this.projectType,
        sag:
          "SAG:TENANT:PROJECT_MGT:CREATE_SELECT:CUSTOM_REPORT_CONTENT:REPORT_EXPLANATION",
        onclick: () => {
          this.showReportEscription();
        },
      });
    }
    // 组织敬业度调研
    // 相关分析
    const coefficientAlgorithmMap = {
      CORRELATION: "相关系数",
      REGRESSION: "回归系数",
    };
    const coefficientAlgorithmText =
      coefficientAlgorithmMap[this.prismaData.coefficientAlgorithm];
    if (this.reportType !== "CULTURE_INVESTIGATION_RESEARCH") {
      reportList.push({
        name: coefficientAlgorithmText,
        title: "某维度与敬业度",
        img: "assets/images/report_sttting_4.png",
        img_dis: "assets/images/report_sttting_dis_4.png",
        btnText: "进入设置",
        labelText: "Top 10",
        disabled:
          !this.projectType ||
          this.projectType === "ANNOUNCED" ||
          this.projectType === "PREVIEW",
        sag:
          "SAG:TENANT:PROJECT_MGT:CREATE_SELECT:CUSTOM_REPORT_CONTENT:CORRELATION",
        onclick: () => {
          this.showanalysis();
        },
      });
      // VIVO组织敬业度调研 20250725 取消自定义指数设置显示判断-this.reportType !== "VIVO_INVESTIGATION_RESEARCH_CUSTOM"
      reportList.push({
        name: "自定义指数",
        title: "小白兔指数",
        img: "assets/images/report_sttting_3.png",
        img_dis: "assets/images/report_sttting_dis_3.png",
        btnText: "进入设置",
        labelText: "仅一页",
        disabled:
          !this.projectType ||
          this.projectType === "ANNOUNCED" ||
          this.projectType === "PREVIEW",
        sag:
          "SAG:TENANT:PROJECT_MGT:CREATE_SELECT:CUSTOM_REPORT_CONTENT:CUSTOM_INDEX",
        onclick: () => {
          this.showAnalytical();
        },
      });
    }
     
    // 组织敬业度调研
    if (this.reportType.indexOf("INVESTIGATION_RESEARCH") !== -1) {
      reportList.push({
        name: "多模块人口显示",
        title: "是否显示人口分析",
        img: "assets/images/report_sttting_6.png",
        img_dis: "assets/images/report_sttting_dis_6.png",
        btnText: "进入设置",
        labelText: "多模块",
        disabled: false,
        imgExtraStyles: {
          'padding': '12px 40px 0px 12px'
        },
        sag:
          "SAG:TENANT:PROJECT_MGT:CREATE_SELECT:CUSTOM_REPORT_CONTENT:DEMOGRAPHIC",
        onclick: () => {
          this.showReportDisplay();
        },
      });
    }
    /**
     *
     *  @author: Sid Wang
     *  @Date: 2023/09/14
     *  @code: #8340
     *  @todo: 多群体分析 交叉分析
     *
     */
    // 网易组织敬业度调研
    if (this.reportType === "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
      // 网易
      reportList.push({
        name: "多群体分析",
        title: "男性 且 本科",
        img: "assets/images/report_sttting_5.png",
        img_dis: "assets/images/report_sttting_dis_5.png",
        btnText: "进入设置",
        labelText: "仅一页",
        disabled: false,
        sag:
          "SAG:TENANT:PROJECT_MGT:CREATE_SELECT:CUSTOM_REPORT_CONTENT:MULTIP_ROUP",
        onclick: () => {
          this.showGroupAnalytical();
        },
      });
    }
    //  屏蔽定制工具腾讯、网易、爱普生、vivo 'INVESTIGATION_RESEARCH_CUSTOM',
    console.log("this.reportType", this.reportType);
    // this.reportType.indexOf("_INVESTIGATION_RESEARCH_CUSTOM") !== -1
    // const customization = ['TENCENT_INVESTIGATION_RESEARCH_CUSTOM', 'EPSON_INVESTIGATION_RESEARCH_CUSTOM', 'VIVO_INVESTIGATION_RESEARCH_CUSTOM', 'NETEASE_INVESTIGATION_RESEARCH_CUSTOM']
    // todo: 交叉分析 必须包含 敬业度指数 满意度指数或组织能力指数
    // 双视角调研 DP_INVESTIGATION_RESEARCH_CUSTOM
    // 标准组织能力-调研（不要?） OC_INVESTIGATION_RESEARCH
    // 组织敬业度调研 INVESTIGATION_RESEARCH_CUSTOM
    // 乾象 QIANXINAG_INVESTIGATION_RESEARCH_CUSTOM
    const customization = [
      "DP_INVESTIGATION_RESEARCH_CUSTOM",
      // "OC_INVESTIGATION_RESEARCH",
      // "INVESTIGATION_RESEARCH_CUSTOM",
      "QIANXINAG_INVESTIGATION_RESEARCH_CUSTOM",
    ];
    const crossRes = await this.Http.getCrosslistAllLabels(
      this.projectId
    ).toPromise();
    const indexKeys = crossRes.data.map((val) => val.id);
    const isShowCross =
      indexKeys.includes("EEI") &&
      (indexKeys.includes("ESI") || indexKeys.includes("OCI"));
    // if (!customization.includes(this.reportType) && isShowCross) {
    if (customization.includes(this.reportType) && isShowCross) {
      reportList.push({
        name: "交叉分析",
        title: "多视角交叉",
        img: "assets/images/report_sttting_2.png",
        img_dis: "assets/images/report_sttting_dis_2.png",
        btnText: "进入设置",
        labelText: "人口/部门",
        disabled:
          !this.projectType ||
          this.projectType === "ANNOUNCED" ||
          this.projectType === "PREVIEW",
        sag:
          "SAG:TENANT:PROJECT_MGT:CREATE_SELECT:CUSTOM_REPORT_CONTENT:CROSS_ANALYSIS",
        onclick: () => {
          this.showCrossAnalytical();
        },
      });
    }
      // 数据呈现/计算规则（自定义组织调研，和自定义双视角调研）
      // 双视角调研 DP_INVESTIGATION_RESEARCH_CUSTOM
      // 组织敬业度调研 INVESTIGATION_RESEARCH_CUSTOM
      const dataShowCalRuleCustomization = [
          "DP_INVESTIGATION_RESEARCH_CUSTOM",
          "INVESTIGATION_RESEARCH_CUSTOM",
      ];
      if (dataShowCalRuleCustomization.includes(this.reportType)) {
          reportList.push({
              name: "数据呈现/计算规则",
              title: "",
              img: "assets/images/report_sttting_7.png",
              img_dis: "assets/images/report_sttting_dis_7.png",
              btnText: "进入设置",
              labelText: "进入设置",
              disabled: false,
              imgExtraStyles: {
                  'padding': '0 10px 16px 0'
              },
              sag:
                  "SAG:TENANT:PROJECT_MGT:CREATE_SELECT:CUSTOM_REPORT_CONTENT:DATA_SHOW_CAL_RULE",
              onclick: () => {
                  this.showDataShowCalRule();
              },
          });
      }
    this.drawerService.create({
      nzTitle: "报告设置",
      nzContent: ReportSettings,
      // nzFooter: null,
      nzWrapClassName: "round-right-drawer6",
      nzContentParams: {
        reportList,
      },
      nzWidth: 1000,
      // nzMaskClosable: false,
      // nzClosable: false,
    });
  }
  /**
   *报告说明弹框
   *@author:wangxiangxin
   *@Date:2023/08/24
   */
  showReportEscription() {
    const reportList = [];
    const reportEscriptionModal = this.drawerService.create({
      nzTitle: "报告说明",
      nzContent: ReportEscription,
      // nzFooter: null,
      nzWrapClassName: "round-right-drawer6",
      nzContentParams: {
        questionnaireId: this.questionnaireId,
        reportType: this.reportType,
      },
      nzWidth: 960,
      // nzMaskClosable: false,
      // nzClosable: false,
    });
  }
  showanalysis() {
    // 相关分析
    const coefficientAlgorithmMap = {
      CORRELATION: "相关系数",
      REGRESSION: "回归系数",
    };
    const coefficientAlgorithmText =
      coefficientAlgorithmMap[this.prismaData.coefficientAlgorithm];
    const tplModal = this.drawerService.create({
      nzTitle: coefficientAlgorithmText,
      nzContent: CorrelationCoefficientComponent,
      // nzFooter: null,
      nzWrapClassName: "round-right-drawer6",
      nzContentParams: {
        projectId: this.projectId,
        coefficientAlgorithm: this.prismaData.coefficientAlgorithm
      },
      nzWidth: 1200,
      // nzMaskClosable: false,
      // nzClosable: false,
    });
  }

  showRoleModal() {
    const {
      startTime,
      endTime,
      name: { zh_CN },
      analysisFactorDto,
    } = this.prismaData;
    // 人口标签选中内容
    const analysisFactorDtoChecked =
      analysisFactorDto.filter((val) => val.isChecked) || [];
    if (!zh_CN) {
      // // this.msg.warning("请填写活动名称!");
      this.customMsg.open("warning", "请填写活动名称");
      return;
    }
    if (!endTime || !startTime) {
      // this.msg.warning("请填写活动周期 !");
      this.customMsg.open("warning", "请填写活动周期");
      return;
    }
    if (analysisFactorDtoChecked.length === 0) {
      // this.msg.warning("请选择人口标签!");
      this.customMsg.open("warning", "请选择人口标签");
      return;
    }
    const modal = this.drawerService.create({
      nzContent: AdvancedSetting,
      nzTitle: "高级设置",
      nzWidth: 455,
      nzContentParams: {
        listdata: this.prismaData,
        projectId: this.projectId,
      },
      nzWrapClassName: "round-right-drawer6",
    });
    // const modal = this.modalService.create({
    //   nzContent: AdvancedSetting,
    //   nzTitle: '',
    //   nzWidth: 960,
    //   nzComponentParams: {
    //     listdata: this.prismaData,
    //   },
    // });
    // this.modalService.afterAllClose.subscribe(() => {
    //   const child: AdvancedSetting = modal.getContentComponent();
    //   this.prismaData = child.settingData

    // })
    modal.afterClose.subscribe((settingData) => {
      // 高级设置保存调用
      if (settingData) {
        this.prismaData = settingData;
        this.submitSave("SeniorPage");
      }
    });
  } //高级设置

  samelist(res) {
    this.isSpinning = false;

    this.prismaData.factorname = res.data;

    this.prismaData.factorname.detailedScoreConfigs.forEach((item) => {
      item.detailedScoreChildDimensions.forEach((val) => {
        if (
          val.detailedScoreSubChildDimensions &&
          val.detailedScoreSubChildDimensions.length !== 0
        ) {
          this.sunecharts = true;
          val.childname = [];
          val.detailedScoreSubChildDimensions.forEach((res) => {
            val.childname.push(res.name.zh_CN);
            if (val.name.zh_CN) {
              val.smallname =
                val.name.zh_CN + " ( " + val.childname.join("、") + " )";
            } else {
              val.name.zh_CN = " ( " + val.childname.join("、") + " )";
              val.smallname = " ( " + val.childname.join("、") + " )";
            }
          });
        } else {
          val.smallname = val.name.zh_CN;
        }
      });
    });

    let bookConfirm = this.prismaData.factorname.relationPermissions.filter(
      (res) => {
        return res.type == "QUESTION_BOOK";
      }
    );

    let orgConfirm = this.prismaData.factorname.relationPermissions.filter(
      (res) => {
        return res.type == "ORGANIZATION";
      }
    );
    let answer = this.prismaData.factorname.relationPermissions.filter(
      (res) => {
        return res.type == "ANSWER_DESCRIPTION";
      }
    );

    let bookpage = this.prismaData.factorname.relationPermissions.filter(
      (res) => {
        return res.type == "QUESTION_BOOK_DISTRIBUTE";
      }
    );
    let lottery = this.prismaData.factorname.relationPermissions.filter(
      (res) => {
        return res.type == "ANSWER_LOTTERY_DRAW";
      }
    );
    let analytical = this.prismaData.factorname.relationPermissions.filter(
      (res) => {
        return res.type == "ANALYSIS_CUSTOM_INDEX";
      }
    );
    let analysis = this.prismaData.factorname.relationPermissions.filter(
      (res) => {
        return res.type == "ANALYSIS_TASK";
      }
    );

    if (bookConfirm.length != 0) {
      this.prismaData.isConfirmQuestionBook = bookConfirm[0].isConfirmed;
      this.prismaData.isShowQuestionBook = bookConfirm[0].isNeedConfirm;
    }

    if (orgConfirm.length != 0) {
      this.prismaData.isShowOrganization = orgConfirm[0].isNeedConfirm;
      this.prismaData.isConfirmOrganization = orgConfirm[0].isConfirmed;
    }

    if (answer.length != 0) {
      this.prismaData.isShowanswer = answer[0].isNeedConfirm;
      this.prismaData.isConfirmanswer = answer[0].isConfirmed;
    }
    if (bookpage.length != 0) {
      this.prismaData.isShowPassages = bookpage[0].isNeedSaveProject;
    }
    if (lottery.length != 0) {
      this.prismaData.isShowLottery = lottery[0].isNeedSaveProject;
    }
    if (analytical.length != 0) {
      this.prismaData.isAnalytical = analytical[0].isConfirmed;
    }
    if (analysis.length != 0) {
      this.prismaData.isAnalysis = analysis[0].isConfirmed;
    }

    this.prismaData.factorname.standardAnalysisFactorVOS.forEach((item) => {
      item.visible = false;
      item.showble = false;
    });

    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      // baseUrl = 'http://***********/'
      baseUrl = "https://sag-qa.knxdevelop.com/";
    }

    this.modelImageUrl = `${baseUrl}api/file/www/${this.prismaData.factorname.surveyStandardSagReportTemplate.modelImageUrl}`;
    this.prismaData.answerDescription = res.data.answerDescription
      ? res.data.answerDescription
      : res.data.surveyStandardQuestionnaire.answerDescription;
    this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO[0].modelImageUrl = this.prismaData.factorname.surveyStandardSagReportTemplate.modelImageUrl;
    this.prismaData.analysisFactorDto = this.prismaData.factorname.standardAnalysisFactorVOS;
    let checkedCount = this.prismaData.analysisFactorDto.filter((item) => {
      return item.isChecked;
    }).length;
    let hiddenCount = this.prismaData.analysisFactorDto.filter((item) => {
      return item.isChecked && item.isHidden;
    }).length;
    let showCount = this.prismaData.analysisFactorDto.filter((item) => {
      return item.isChecked && !item.isHidden;
    }).length;
    if (hiddenCount == checkedCount) {
      this.isShowAll = true;
    }

    if (showCount == checkedCount) {
      this.isShowAll = false;
    }
  }
  getdefaultMenus(projectId, standardQuestionnaireId) {
    let pramas = {
      projectId: projectId,
      standardQuestionnaireId: standardQuestionnaireId,
      editFlag: true,
    };
    this.Http.listByQuestionnaireCodeCreate(pramas).subscribe((res) => {
      if (res.result.code == 0) {
        // console.log(
        //   "getdefaultMenus",
        //   "surveyQuestionnaire-reportType",
        //   res.data.surveyQuestionnaire.reportType
        // );
        console.log(
          "getdefaultMenus",
          "surveyStandardQuestionnaire-reportType",
          res.data.surveyStandardQuestionnaire.reportType
        );
        let reportType = res.data.surveyStandardQuestionnaire.reportType;
        this.reportType = reportType;
        if (reportType.indexOf("CUSTOM") != -1) {
          this.custom = true;
        } else if (reportType == "CULTURE_INVESTIGATION_RESEARCH") {
          this.custom = true;
        } else {
          this.custom = false;
        }
        if (res.data.detailedScoreConfigs.length === 0) {
          this.emptyShow = true;
        } else {
          this.emptyShow = false;
        }

        this.samelist(res);
        if (this.prismaData.factorname.standardAnalysisFactorVOS) {
          this.prismaData.factorname.standardAnalysisFactorVOS.forEach(
            (item) => {
              item.visible = false;
              item.showble = false;
            }
          );
        }

        this.prismaData.factorname.detailedScoreConfigs.forEach((res) => {
          res.detailedScoreChildDimensions.forEach((val) => {
            this.echartData.push({
              name: val.name.zh_CN,
              value: Math.ceil(Math.random() * 30),
              parentName: res.name.zh_CN,
              children: res.detailedScoreChildDimensions,
            });
          });
        });
        this.echartData = this.classification(this.echartData);
      } else {
      }
    });
  } //获取敬业度指数 驱动因素 人口标签

  classification(arr) {
    var map = {},
      dest = [];
    for (var i = 0; i < arr.length; i++) {
      var ai = arr[i];
      if (!map[ai.parentName]) {
        //key 依赖字段 可自行更改
        dest.push({
          name: ai.parentName,
          data: [ai],
        });
        map[ai.parentName] = ai;
      } else {
        for (var j = 0; j < dest.length; j++) {
          var dj = dest[j];
          if (dj.name == ai.parentName) {
            //key 依赖字段 可自行更改
            dj.data.push(ai);
            break;
          }
        }
      }
    }
    return dest;
  } //数据归类

  //修改活动的时候，拿自己修改的人口标签
  getCreateaCodePrisma(standId, projectId) {
    let pramas = {
      projectId: projectId,
      standardQuestionnaireId: standId,
      editFlag: true,
    };
    this.Http.listByQuestionnaireCodeCreate(pramas).subscribe((res) => {
      if (res.result.code == 0) {
        // let reportType = res.data.surveyStandardQuestionnaire.reportType;
        console.log(
          "getCreateaCodePrisma",
          "surveyQuestionnaire-reportType",
          res.data.surveyQuestionnaire.reportType
        );
        console.log(
          "getCreateaCodePrisma",
          "surveyStandardQuestionnaire-reportType",
          res.data.surveyStandardQuestionnaire.reportType
        );
        let reportType = res.data.surveyQuestionnaire.reportType;
        this.reportType = reportType;
        if (reportType.indexOf("CUSTOM") != -1) {
          this.custom = true;
        } else if (reportType == "CULTURE_INVESTIGATION_RESEARCH") {
          this.custom = true;
        } else {
          this.custom = false;
        }
        if (res.data.detailedScoreConfigs.length === 0) {
          this.emptyShow = true;
        } else {
          this.emptyShow = false;
        }
        res.data.detailedScoreConfigs.forEach((item) => {
          item.detailedScoreChildDimensions.forEach((val) => {
            this.echartData.push({
              name: val.name.zh_CN,
              value: Math.ceil(Math.random() * 30),
              parentName: item.name.zh_CN,
              children: item.detailedScoreChildDimensions,
            });
          });
        });
        this.echartData = this.classification(this.echartData);
        this.samelist(res);
      } else {
      }
    });
  }

  //工具名称选择
  getshow(e) {
    this.showIndex = e;
  }

  //选中人口标签
  ngModelChange(e) {
    this.prismaData.analysisFactorDto = this.prismaData.factorname.standardAnalysisFactorVOS;
  }

  //清空选中的人口标签
  clearcheck() {
    this.prismaData.factorname.standardAnalysisFactorVOS.forEach((res) => {
      if (res.isChecked && !res.isRequire) {
        res.isChecked = false;
      }
    });
    this.prismaData.analysisFactorDto = [];
  }

  //全选的人口标签
  checkAll() {
    this.prismaData.factorname.standardAnalysisFactorVOS.forEach((res) => {
      if (!res.isChecked && !res.isRequire) {
        res.isChecked = true;
      }
    });
    this.prismaData.analysisFactorDto = this.prismaData.factorname.standardAnalysisFactorVOS;
  }

  //新增人口标签弹窗显示（自定义）
  addfactor(status: boolean) {
    this.showAnalysisFactor = status;
    if (status === false) {
      this.analysisFactorTitle = "自定义";
    } else {
      this.analysisFactorTitle = "查看";
    }
    this.addfactorshow = true;
  }

  handleCancelmodal() {
    this.isVisiblemodal = false;
  }

  handleCancel() {
    this.addfactorshow = false;
    this.prismaData.analysisFactorDto = this.prismaData.factorname.standardAnalysisFactorVOS;
    this.prismaData.factorname.standardAnalysisFactorVOS = this.prismaData.factorname.standardAnalysisFactorVOS.filter(
      (item) => {
        return item.standardDemographicId || item.saveisAdd || item.id;
      }
    );
  } //弹窗取消

  synthesisTime(type?) {
    if (!this.prismaData.name.zh_CN) {
      if (type != "img") {
        // this.msg.error("请填写活动名称");
        this.customMsg.open("error", "请填写活动名称");
      }
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPRELoading = false;
      this.isSpinning = false;
      this.nzSimple = false;
      return;
    }

    if (this.prismaData.name.zh_CN.length > 50) {
      if (type != "img") {
        // this.msg.error("活动名称不能超过50个字符！");
        this.customMsg.open("error", "活动名称不能超过50个字符！");
      }
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPRELoading = false;
      this.isSpinning = false;
      return;
    }
    if (
      this.prismaData.name.zh_CN.indexOf("/") != -1 ||
      this.prismaData.name.zh_CN.indexOf("\\") != -1
    ) {
      if (type != "img") {
        // this.msg.error("活动名称包含非法字符'/''\\'！");
        this.customMsg.open("error", "活动名称包含非法字符'/''\\'！");
      }
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPRELoading = false;
      this.isSpinning = false;
      return;
    }

    if (this.dateRange.length === 0) {
      if (type != "img") {
        // this.msg.error("请选择活动周期");
        this.customMsg.open("error", "请选择活动周期");
      }
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPRELoading = false;
      this.isSpinning = false;
      return;
    }

    let analysisFactorDto = this.prismaData.analysisFactorDto.filter((res) => {
      return res.isChecked;
    });

    if (analysisFactorDto.length == 0 && !this.isUpdateing) {
      if (type != "img") {
        // this.msg.error("请选择至少一个人口标签");
        this.customMsg.open("error", "请选择至少一个人口标签");
      }

      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPRELoading = false;
      this.isSpinning = false;
      return;
    }

    if (this.prismaData.optionalLanguages.length == 0) {
      if (type != "img") {
        // this.msg.error("填答时可选语言至少选一个！");
        this.customMsg.open("error", "填答时可选语言至少选一个！");
      }

      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPRELoading = false;
      this.isSpinning = false;

      return;
    }
    if (this.prismaData.isEnableWelcomePage) {
      if (this.prismaData.welcomePage.zh_CN == "") {
        if (type != "img") {
          // this.msg.error("欢迎页内容不能为空！");
          this.customMsg.open("error", "欢迎页内容不能为空！");
        }

        this.isNzOkLoading = false;
        this.isNzRELoading = false;
        this.isNzPRELoading = false;
        this.isSpinning = false;

        return;
      }
    }

    if (this.prismaData.isEnableEndPage) {
      if (this.prismaData.endPage.zh_CN == "") {
        if (type != "img") {
          // this.msg.error("结束页内容不能为空！");
          this.customMsg.open("error", "结束页内容不能为空！");
        }

        this.isNzOkLoading = false;
        this.isNzRELoading = false;
        this.isNzPRELoading = false;
        this.isSpinning = false;

        return;
      }
    }

    this.prismaData.startTime = `${this.formatDate(
      this.dateRange[0],
      "date"
    )}T${this.formatDate(this.dateRange[0], "time")}`;
    this.prismaData.endTime = `${this.formatDate(
      this.dateRange[1],
      "date"
    )}T${this.formatDate(this.dateRange[1], "time")}`;
    let submitData = JSON.parse(JSON.stringify(this.prismaData));

    /**
     * 删除无用字段
     */
    submitData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO[0].relationPermissions = this.prismaData.factorname.relationPermissions;

    submitData.projectName = submitData.name;
    delete submitData.factorname;
    delete submitData.standardDemographicDTO;
    delete submitData.isPublicReport;
    return submitData;
  }

  ActiveCreate(submitData, type?) {
    this.isSpinning = true;
    this.Http.create(submitData).subscribe((res) => {
      if (res.result.code == 0) {
        if (type == "SeniorPage") {
          this.projectId = res.data[0].projectId;
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPRELoading = false;
          this.isSpinning = false;
          this.getProjectSetting();
        } else {
          this.msg.success("创建成功！");
          this.router.navigateByUrl("/project-manage/home");
        }
      } else {
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPRELoading = false;
        this.isSpinning = false;
      }
    });
  } //创建活动

  BookandOrgan(submitData, type?) {
     console.log("submitData", submitData);
    this.Http.create(submitData).subscribe((res) => {
      if (res.result.code == 0) {
        this.isSpinning = false;
        // this.prismaData.projectType = 'ANNOUNCED'
        this.projectId = res.data[0].projectId;
        this.questionnaireId = res.data[0].id;
        let params = {};
        type === "lottery"
          ? (params = {
              projectId: this.projectId,
              name: submitData.projectName.zh_CN,
              projectCode: this.projectCode,
              projectType: this.projectType ? this.projectType : null,
              questionnaireId: this.questionnaireId,
              reportType: this.reportType,
            })
          : (params = {
              projectId: this.projectId,
              name: submitData.projectName.zh_CN,
              projectCode: this.projectCode,
              type: submitData.status,
              projectType: this.projectType ? this.projectType : null,
              questionnaireId: this.questionnaireId,
              reportType: this.reportType,
            });
        console.log("params", params);
        this.router.navigate([this.JumpUrl], { queryParams: params });
        this.prismaData.projectId = this.projectId;
      } else {
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPRELoading = false;
        this.isSpinning = false;
      }
    });
  } //题本或者组织创建活动

  ActiveUpdata(submitData, type?) {
    this.isSpinning = true;
    submitData.id = this.projectId;
    this.Http.updateAnnouncedProject(submitData).subscribe((res) => {
      if (res.result.code == 0) {
        if (type == "SeniorPage") {
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPRELoading = false;
          this.isSpinning = false;
          this.getProjectSetting();
        } else {
          this.msg.success("操作成功！");
          this.router.navigateByUrl("/project-manage/home");
        }
      } else {
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPRELoading = false;
        this.isSpinning = false;
      }
    });
  } //未发布的情况下调用的更新活动

  ProjectSetsave(submitData, type?) {
    this.isSpinning = true;
    submitData.projectId = this.projectId;
    this.Http.updateProjectSetting(submitData).subscribe((res) => {
      if (res.result.code == 0) {
        if (type == "SeniorPage") {
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPRELoading = false;
          this.isSpinning = false;
          this.getProjectSetting();
        } else {
          this.msg.success("修改成功！");
          this.router.navigateByUrl("/project-manage/home");
        }
      } else {
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPRELoading = false;
        this.isSpinning = false;
      }
    });
  } //已发布的情况下更新活动

  // 发布
  submitRelase() {
    this.isNzRELoading = true;
    if (
      this.prismaData.isShowQuestionBook &&
      !this.prismaData.isConfirmQuestionBook
    ) {
      this.isNzRELoading = false;
      // this.msg.error("请将题本编辑完善！");
      this.customMsg.open("error", "请将题本编辑完善！");
      return;
    }
    if (
      this.prismaData.isShowOrganization &&
      !this.prismaData.isConfirmOrganization
    ) {
      this.isNzRELoading = false;
      // this.msg.error("请将组织编辑完善！");
      this.customMsg.open("error", "请将组织编辑完善！");
      return;
    }
    if (!this.projectId) {
      let submitData = this.synthesisTime();
      if (submitData) {
        submitData.status = "ANSWERING"; //(进行中) ----发布
        this.ActiveCreate(submitData);
      }
    } else {
      if (this.isUpdate) {
        //未发布修改活动
        let submitData = this.synthesisTime();
        if (submitData) {
          if (this.projectType == "PREVIEW") {
            this.isNzRELoading = false;
            const that = this;
            this.confirmModal = this.modal.create({
              nzTitle: null,
              nzContent: ModalContentComponent,
              nzFooter: null,
              nzComponentParams: {
                father: that,
                submitData: submitData,
                type: "prisma",
              },
              nzClosable: false,
              nzMaskClosable: false,
              // nzOnOk: () => new Promise(resolve => {
              //   this.isNzRELoading = true;
              //   setTimeout(resolve, 500);
              //   submitData.status = "ANSWERING"
              //   this.ActiveUpdata(submitData)//修改活动
              // }),
              // nzOnCancel: () =>
              //     new Promise(resolve => {
              //       resolve();
              //       this.hideProcess();
              //     }),
            });
          } else {
            submitData.status = "ANSWERING";
            this.ActiveUpdata(submitData); //修改活动
          }
        }
      } else {
        let submitData = this.synthesisTime();
        if (submitData) {
          submitData.status = "ANSWERING"; //(进行中) ----发布
          this.ActiveUpdata(submitData); //发布未发布的活动
        }
      }
    }
  }

  submitPreview() {
    this.isNzPRELoading = true;
    if (
      this.prismaData.isShowQuestionBook &&
      !this.prismaData.isConfirmQuestionBook
    ) {
      this.isNzPRELoading = false;
      // this.msg.error("请将题本编辑完善！");
      this.customMsg.open("error", "请将题本编辑完善！");
      return;
    }
    if (
      this.prismaData.isShowOrganization &&
      !this.prismaData.isConfirmOrganization
    ) {
      this.isNzPRELoading = false;
      // this.msg.error("请将组织编辑完善！");
      this.customMsg.open("error", "请将组织编辑完善！");
      return;
    }
    if (!this.projectId) {
      let submitData = this.synthesisTime();
      if (submitData) {
        submitData.status = "PREVIEW";
        this.ActiveCreate(submitData);
      }
    } else {
      if (this.isUpdate) {
        //未发布修改活动
        let submitData = this.synthesisTime();
        if (submitData) {
          submitData.status = "PREVIEW";
          this.ActiveUpdata(submitData); //修改活动
        }
      } else {
        let submitData = this.synthesisTime();
        if (submitData) {
          submitData.status = "PREVIEW"; //(进行中) ----发布
          this.ActiveUpdata(submitData); //发布未发布的活动
        }
      }
    }
  }

  // 保存
  submitSave(type = "SavePage") {
    this.isSpinning = true;
    this.isNzRELoading = true;
    this.isNzPRELoading = true;
    this.isNzOkLoading = true;

    if (this.isUpdate || this.isUpdateing) {
      if (this.isUpdate) {
        //保存未发布修改活动
        let submitData = this.synthesisTime();
        if (submitData) {
          this.ActiveUpdata(submitData, type); //修改活动
        }
      }
      if (this.isUpdateing) {
        //保存已发布修改活动
        let submitData = this.synthesisTime();
        if (submitData) {
          this.ProjectSetsave(submitData, type);
        }
      }
    } else {
      if (this.projectId) {
        let submitData = this.synthesisTime();
        if (submitData) {
          this.ActiveUpdata(submitData, type); //修改活动
        }
      } else {
        let submitData = this.synthesisTime();
        if (submitData) {
          submitData.status = "ANNOUNCED"; //(待发布)
          this.ActiveCreate(submitData, type);
        }
      }
    }
  }
  BookandOrganUp(submitData, type, url) {
    this.isSpinning = true;

    if (this.isUpdateing) {
      //发布的活动，查看

      submitData.projectId = this.projectId;
      this.Http.updateProjectSetting(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          this.isSpinning = false;
          if (type === "defaultbook") {
            if (this.projectType == null || this.projectType === "ANNOUNCED") {
              this.router.navigate([url], {
                queryParams: {
                  projectId: this.projectId,
                  name: submitData.projectName.zh_CN,
                  projectCode: this.projectCode,
                  projectType: this.projectType ? this.projectType : null,
                  questionnaireId: this.questionnaireId,
                  reportType: this.reportType,
                },
              });
            } else {
              this.router.navigate([url], {
                queryParams: {
                  projectId: this.projectId,
                  name: submitData.projectName.zh_CN,
                  projectCode: this.projectCode,
                  listChecked: "checked",
                  projectType: this.projectType ? this.projectType : null,
                  questionnaireId: this.questionnaireId,
                  reportType: this.reportType,
                },
              });
            }
          } else {
            this.router.navigate([url], {
              queryParams: {
                projectId: this.projectId,
                name: submitData.projectName.zh_CN,
                projectCode: this.projectCode,
                projectType: this.projectType ? this.projectType : null,
                questionnaireId: this.questionnaireId,
                reportType: this.reportType,
              },
            });
          }
        } else {
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPRELoading = false;
          this.isSpinning = false;
        }
      });
    } else {
      //未发布的活动，查看
      if (this.projectType === "PREVIEW") {
        submitData.status = "PREVIEW"; //(待发布)
      } else {
        submitData.status = "ANNOUNCED"; //(待发布)
      }

      submitData.id = this.projectId;
      this.Http.updateAnnouncedProject(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          this.isSpinning = false;
          if (type === "defaultbook") {
            if (
              this.projectType == null ||
              this.projectType === "ANNOUNCED" ||
              this.projectType === "PREVIEW"
            ) {
              this.router.navigate([url], {
                queryParams: {
                  projectId: this.projectId,
                  name: submitData.projectName.zh_CN, 
                  projectCode: this.projectCode,
                  projectType: this.projectType ? this.projectType : null,
                  questionnaireId: this.questionnaireId,
                  reportType: this.reportType,
                },
              });
            } else {
              this.router.navigate([url], {
                queryParams: {
                  projectId: this.projectId,
                  name: submitData.projectName.zh_CN,
                  projectCode: this.projectCode,
                  listChecked: "checked",
                  projectType: this.projectType ? this.projectType : null,
                  questionnaireId: this.questionnaireId,
                  reportType: this.reportType,
                },
              });
            }
          } else {
            this.router.navigate([url], {
              queryParams: {
                projectId: this.projectId,
                name: submitData.projectName.zh_CN,
                projectCode: this.projectCode,
                projectType: this.projectType ? this.projectType : null,
                questionnaireId: this.questionnaireId,
                reportType: this.reportType,
              },
            });
          }
        } else {
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPRELoading = false;
          this.isSpinning = false;
        }
      });
    }
  }
  goTobook(url: string, type) {
    let submitData = this.synthesisTime();
    this.JumpUrl = url;
    if (!this.projectId) {
      this.isSpinning = true;
      if (submitData) {
        submitData.status = "ANNOUNCED"; //(待发布)
        this.BookandOrgan(submitData, type);
      } else {
        this.isSpinning = false;
      }
    } else {
      let analysisFactorDto = this.prismaData.analysisFactorDto.filter(
        (res) => {
          return res.isChecked;
        }
      );
      // if (analysisFactorDto.length == 0 && !this.isUpdateing) {
      //   this.msg.error('请选择至少一个人口标签');
      //   return;
      // }
      if (submitData) {
        this.BookandOrganUp(submitData, type, url);
      } else {
        this.isSpinning = false;
      }
    }
  }

  getModalPie() {
    this.isVisiblemodal = true;
  }

  getnewlead() {
    this.showmock = true;
    this.noviceGuidance = true;
  }
  closed() {
    this.showmock = false;
    this.noviceGuidance = false;
  }

  showModalDesc() {
    let submitData = this.synthesisTime();
    if (submitData) {
      this.visibleDesc = true;
      window.document.documentElement.scrollTop = 0;
    }
  }

  getActivenew(e) {
    if (e == "zh_CN") {
      this.Isactivelan = true;
    } else {
      this.Isactivelan = false;
    }
  }
  okModalDesc() {
    this.nzSimple = true;
    if (!this.projectId) {
      let submitData = this.synthesisTime();
      this.isSpinning = true;
      if (submitData) {
        this.Http.create(submitData).subscribe((res) => {
          if (res.result.code == 0) {
            this.projectId = res.data[0].projectId;
            this.questionnaireId = res.data[0].id;
            this.saveDesc();
          }
        });
      }
    } else {
      let submitData = this.synthesisTime();
      this.isSpinning = true;
      if (this.isUpdateing) {
        //发布的活动，
        if (submitData) {
          submitData.projectId = this.projectId;
          this.Http.updateProjectSetting(submitData).subscribe((res) => {
            if (res.result.code == 0) {
              this.isSpinning = false;
              this.saveDesc();
            }
          });
        } else {
          this.isSpinning = false;
        }
      } else {
        //未发布的活动，
        if (submitData) {
          if (this.projectType === "PREVIEW") {
            submitData.status = "PREVIEW";
          } else {
            submitData.status = "ANNOUNCED"; //(待发布)
          }
          submitData.id = this.projectId;
          this.Http.updateAnnouncedProject(submitData).subscribe((res) => {
            if (res.result.code == 0) {
              this.isSpinning = false;
              this.saveDesc();
            }
          });
        } else {
          this.isSpinning = false;
        }
      }
    }
  }
  saveDesc() {
    let params = {
      // answerDescription: {
      //   en_US: this.prismaData.answerDescription.en_US,
      //   zh_CN: this.prismaData.answerDescription.zh_CN,
      // },
      answerDescription: this.prismaData.answerDescription,
      questionnaireId: this.questionnaireId,
    };

    this.Http.updateAnswer(params).subscribe((res) => {
      if (res.result.code == 0) {
        this.visibleDesc = false;
        this.nzSimple = false;
        this.confirmRelation("ANSWER_DESCRIPTION", true);
        this.msg.success("更新成功！");
      } else {
        this.nzSimple = false;
      }
    });
  }
  confirmRelation(type, isConfirmed) {
    let data = {
      projectId: this.projectId,
      questionnaireId: this.questionnaireId,
      type: type,
      isConfirmed: isConfirmed,
    };
    this.Http.confirmRelation(data).subscribe((res) => {
      if (res.result.code == 0) {
        this.getProjectSetting();
      }
    });
  }
  setDescDefault() {
    this.Http.getDimensions(this.standardQuestionnaireId).subscribe((res) => {
      let tmpDesc = res.data.answerDescription;
      // this.prismaData.answerDescription.zh_CN = tmpDesc.zh_CN
      // this.prismaData.answerDescription.en_US = tmpDesc.en_US
      this.prismaData.answerDescription = tmpDesc;
    });
  }
  cancelModalDesc() {
    this.visibleDesc = false;
  }
  getpreview() {
    if (this.projectId) {
      const submitData = this.synthesisTime();

      let relationPermissions =
        submitData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO[0]
          .relationPermissions;
      const booquestion = relationPermissions.filter((res) => {
        return res.type == "QUESTION_BOOK";
      });

      const relationship = relationPermissions.filter((res) => {
        return res.type == "ORGANIZATION";
      });

      const answer = relationPermissions.filter((res) => {
        return res.type == "ANSWER_DESCRIPTION";
      });

      if (booquestion.length != 0) {
        if (!booquestion[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--题本");
          this.customMsg.open("error", "请先完成关联任务--题本");
          return;
        }
      }

      if (relationship.length != 0) {
        if (!relationship[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--组织架构");
          this.customMsg.open("error", "请先完成关联任务--组织架构");
          return;
        }
      }

      if (answer.length != 0) {
        if (!answer[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--作答说明");
          this.customMsg.open("error", "请先完成关联任务--作答说明");
          return;
        }
      }
      if (submitData) {
        this.preview(submitData);
      }
    } else {
      // this.msg.warning("请先保存活动！");
      this.customMsg.open("warning", "请先保存活动");
    }
  }
  preview(submitData) {
    submitData.id = this.projectId;
    this.Http.updateAnnouncedProject(submitData).subscribe((res) => {
      if (res.result.code == 0) {
        this.Http.PreviewUrl(this.projectId).subscribe((res) => {
          if (res.result.code == 0) {
            window.open(res.data, "_blank");
          }
        });
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPRELoading = false;
        this.isSpinning = false;
      } else {
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPRELoading = false;
        this.isSpinning = false;
      }
    });
  }

  // upload_png(){

  // }
  beforeUpload = (file: File) => {
    return new Observable((observer: Observer<boolean>) => {
      // file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/gif' || file.type === 'image/bmp'
      const isPNG = file.type === "image/png";
      const isSize = file.size / 1024 < 500;
      if (!isPNG) {
        // this.msg.error("文件类型不合法,只能是png类型！");
        this.customMsg.open("error", "文件类型不合法,只能是png类型!");
        observer.complete();
        return;
      }
      if (!isSize) {
        // this.msg.error("图片应小于500KB!");
        this.customMsg.open("error", "图片应小于500KB!");
        observer.complete();
        return;
      }

      observer.next(isPNG && isSize);
      observer.complete();
    });
  };
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.formDataUrl = formData;
    if (this.questionnaireId) {
      this.upload(formData, null, item);
    } else {
      let submitData = this.synthesisTime("img");
      if (submitData) {
        this.upload(formData, null, item);
      } else {
        // this.msg.warning("请先保存活动！");
        this.customMsg.open("warning", "请先保存活动");
      }
    }
  };
  upload(formData, params, item) {
    return this.Http.uploadFile(formData, params).subscribe(
      (res) => {
        if (res.result.code === 0) {
          let baseUrl: string = window.location.origin + "/";
          if (baseUrl.indexOf("http://localhost") !== -1) {
            // baseUrl = 'http://***********/'
            baseUrl = "https://sag-qa.knxdevelop.com/";
          }

          this.prismaData.factorname.surveyStandardSagReportTemplate.modelType =
            "STATIC";
          this.prismaData.factorname.surveyStandardSagReportTemplate.modelImageUrl =
            res.data.id;
          this.modelImageUrl = `${baseUrl}api/file/www/${this.prismaData.factorname.surveyStandardSagReportTemplate.modelImageUrl}`;
          this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO[0].modelImageUrl =
            res.data.id;
          item.onSuccess!();
          this.uploadmodal();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }
  handleChange(info: { file: UploadFile }): void {
    switch (info.file.status) {
      case "uploading":
        // this.loading = true;
        this.getBase64(info.file!.originFileObj!, (img: string) => {
          this.avatarUrl = img;
        });
        break;
      case "done":
        // Get this url from response in real world.
        // this.loading = false;
        break;
      case "error":
        // this.msg.error("Network error");
        this.customMsg.open("error", "Network erro");
        //this.loading = false;
        break;
    }
  }
  private getBase64(img, callback: (img: string) => void): void {
    const reader = new FileReader();
    reader.addEventListener("load", () => callback(reader.result!.toString()));
    reader.readAsDataURL(img);
  }
  uploadmodal() {
    if (this.questionnaireId) {
      let json = {
        modelImageUrl: this.prismaData.factorname
          .surveyStandardSagReportTemplate.modelImageUrl,
        questionnaireId: this.questionnaireId,
      };
      this.Http.updateTemplateModel(json).subscribe((res) => {});
    } else {
      let submitData = this.synthesisTime();
      if (submitData) {
        submitData.status = "ANNOUNCED"; //(待发布)
        this.Http.create(submitData).subscribe((res) => {
          if (res.result.code == 0) {
            this.projectId = res.data[0].projectId;
            this.questionnaireId = res.data[0].id;
          }
        });
      }
    }
  }

  goHome(type) {
    if (type == "home") {
      this.router.navigateByUrl("/home");
    }
    if (type == "create") {
      this.router.navigateByUrl("/new-activity");
    }
  }

  closemodal(type) {
    this.addfactorshow = type.addfactorshow;
    this.prismaData.factorname.standardAnalysisFactorVOS =
      type.factorlist.standardAnalysisFactorVOS;
    this.prismaData.analysisFactorDto = this.prismaData.factorname.standardAnalysisFactorVOS;
  }

  loadDataMap(data) {
    this.prismaData.factorname.standardAnalysisFactorVOS = data;
    this.prismaData.analysisFactorDto = data;
    this.isNzOkLoading = false;
    this.isNzRELoading = false;
    this.isSpinning = false;
    this.isNzPRELoading = false;
    this.shownumber++;
  }

  getdefaultlist() {
    this.isSpinning = true;
    timer(500).subscribe(() => {
      this.Http.listByQuestionnaireCode(this.standardQuestionnaireId).subscribe(
        (res) => {
          if (res.result.code == 0) {
            this.prismaData.factorname.standardAnalysisFactorVOS =
              res.data.standardAnalysisFactorVOS;
            this.prismaData.analysisFactorDto = this.prismaData.factorname.standardAnalysisFactorVOS;
            this.isSpinning = false;
            this.shownumber++;
          } else {
            this.isSpinning = false;
          }
        }
      );
    });
  }

  showPassages(type) {
    if (
      this.prismaData.isConfirmOrganization &&
      this.prismaData.isConfirmQuestionBook
    ) {
      if (type == "create") {
        //创建绑定
        this.isSpinning = true;
        this.isNzOkLoading = true;
        this.isNzPRELoading = true;
        this.isNzRELoading = true;
        let submitData = this.synthesisTime();
        submitData.id = this.projectId;
        this.Http.updateAnnouncedProject(submitData).subscribe((res) => {
          if (res.result.code == 0) {
            //先提交数据用以保存客户自定义的人口标签
            this.pageshow = "1";
            this.getOrganizationByTree();
            this.getlistByProjectId();
            setTimeout(() => {
              this.isShowpagebook = true;
            }, 100);
            this.isNzOkLoading = false;
            this.isNzRELoading = false;
            this.isNzPRELoading = false;
            this.isSpinning = false;
          } else {
            this.isNzOkLoading = false;
            this.isNzRELoading = false;
            this.isNzPRELoading = false;
            this.isSpinning = false;
          }
        });
      } else {
        //查看绑定
        this.showRelations = true;
      }
      this.ShowQuestionMapping();
    } else {
      // this.msg.warning("请先完成题本与组织架构的定制！");
      this.customMsg.open("warning", "请先完成题本与组织架构的定制");
    }
  }

  handlebookmodal() {
    this.pageshow = "1";
    this.Step = 1;
    this.isShowpagebook = false;
    this.organizationId = "";
    this.demographicId = [];
    this.questionId = [];
  }
  handleRamodal() {
    this.showRelations = false;
  }
  getOrganizationByTree() {
    let ids;
    if (this.projectId) {
      ids = this.projectId;
    } else {
      ids = this.standardQuestionnaireId;
    }
    this.Http.listOrganizationByTree(ids).subscribe((item) => {
      if (item.result.code == 0) {
        this.Organizationlist.children = [];
        this.Organizationlist.children.push(...item.data);
      } else {
      }
    });
  }
  getlistByProjectId() {
    let ids;
    if (this.projectId) {
      ids = this.projectId;
    } else {
      ids = this.standardQuestionnaireId;
    }
    this.Http.listByProjectId(ids).subscribe((item) => {
      if (item.result.code == 0) {
        this.Factorslist = item.data;
        this.Factorslist.forEach((res) => {
          item.active = false;
          res.searchValue = "";
          res.children.forEach((val) => {
            val.checked = false;
            if (val.children.length) {
              val.disabled = true;
              val.children.forEach((ele) => {
                ele.checked = false;
              });
            }
          });
        });
      } else {
      }
    });
  }
  formatter(data) {
    data.forEach((item) => {
      item.title = item.name.zh_CN;
      item.key = item.id;
      item.isDisabled = true;
      if (item.children.length !== 0) {
        item.isLeaf = false;
        this.formatter(item.children);
      } else {
        item.isLeaf = true;
      }
    });
  }
  changepage(type) {
    this.pageshow = type;
  }

  // get should select map
  selectChild(node: any, isSelected: boolean, map: any) {
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        map[child.key] = 1;
        this.selectChild(child, isSelected, map);
      }
    }
  }

  // set select node by map
  setSelectState(node: NzTreeNode, isSelected: boolean, map: any) {
    if (map[node.key] === 1) {
      if (node.origin.isVirtual) {
        node.isDisabled = true;
        node.isChecked = false;
      } else {
        node.isDisabled = isSelected;
        // node.setSelected(isSelected);
        node.isChecked = false;
      }
    }
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        this.setSelectState(child, isSelected, map);
      }
    }
  }

  setIsDisabled(node: NzTreeNode) {
    let currentNode: any = node.origin;
    let isSelected: boolean = currentNode.checked;
    let map = {};
    this.selectChild(currentNode, isSelected, map);

    let allNodes: NzTreeNode[] = this.nzTreeComponent.getTreeNodes();
    allNodes.forEach((n: NzTreeNode) => {
      this.setSelectState(n, isSelected, map);
    });
  }

  nzCheck(event: NzFormatEmitEvent): void {
    let checkedNodeList: NzTreeNode[] = this.nzTreeComponent.getCheckedNodeList();
    checkedNodeList.forEach((node: NzTreeNode) => {
      if (node.key != event.node.key) {
        node.isChecked = false;
      }
      this.setIsDisabled(node);
    });
    this.setIsDisabled(event.node);
    if (event.node.isChecked) {
      this.organizationId = event.node.key;
    } else {
      this.organizationId = "";
    }
  }

  expanded(event: NzFormatEmitEvent): void {
    if (event.node.isExpanded) {
      event.node.isExpanded = false;
    } else {
      event.node.isExpanded = true;
    }
  }

  nzEventRen(e) {
    console.log(e);
  }

  nextStep() {
    this.pageItem = 0;
    this.demographicId = [];

    this.Factorslist.forEach((item) => {
      if (item.checked) {
        if (item.checked) {
          this.demographicId.push(item.id);
        }
      } else {
        if (item.children.length !== 0) {
          item.children.forEach((val) => {
            if (val.checked) {
              this.demographicId.push(val.id);
            } else {
              if (val.children.length !== 0) {
                val.children.forEach((ele) => {
                  if (ele.checked) {
                    this.demographicId.push(ele.id);
                  }
                });
              }
            }
          });
        }
      }
    });

    if (this.organizationId == "" && this.demographicId.length == 0) {
      // this.msg.warning("请选择关联因素！");
      this.customMsg.open("warning", "请选择关联因素");
    } else {
      this.Step = 2;
      this.OrganizesearchValue = null;
      this.getDimensionByProjectIdId();
    }
  }
  Returndefault() {
    this.getOrganizationByTree();
    this.getlistByProjectId();
  }
  ReturnStep() {
    this.Step = 1;
    if (this.demographicId.length != 0) {
      this.demographicId.forEach((val) => {
        this.Factorslist.forEach((item) => {
          item.children.forEach((res) => {
            if (res.id == val) {
              item.active = true;
            }
          });
        });
      });
    }
  }
  checkedItem(type) {
    this.pageItem = type;
  }
  getRelation() {
    this.questionId = [];
    this.ProjectIdItem.forEach((item) => {
      item.child.forEach((res) => {
        if (res.checked) {
          this.questionId.push(res.questionId);
        }
      });
    });
    if (this.questionId.length != 0) {
      let savejson = [
        {
          demographicId: this.demographicId,
          organizationId: this.organizationId,
          projectId: this.projectId,
          questionId: this.questionId,
          sort:
            this.ShowQuestion && this.ShowQuestion.length
              ? this.ShowQuestion.length + 1
              : 1,
        },
      ];
      this.SaveRelation(savejson);
    } else {
      // this.msg.error("请选择关联题目！");
      this.customMsg.open("error", "请选择关联题目！");
    }
  }

  SaveRelation(savejson) {
    this.Http.saveIasSelectedQuestionMapping(savejson).subscribe((res) => {
      if (res.result.code == 0) {
        this.organizationId = "";
        this.demographicId = [];
        this.msg.success("关联成功！");
        this.Step = 1;
        this.ShowQuestionMapping();
        this.getOrganizationByTree();
        this.getlistByProjectId();
      }
    });
  }
  ShowQuestionMapping() {
    this.Http.showSelectedQuestionMapping(this.projectId).subscribe((res) => {
      if (res.result.code == 0) {
        this.ShowQuestion = res.data;
        this.ShowQuestion.forEach((item) => {
          item.demographicsname = [];
          item.demographics.forEach((val) => {
            item.demographicsname.push(val.name.zh_CN);
          });
          item.demoname = item.demographicsname.join("_");
          if (item.organizationName) {
            if (item.demoname != "") {
              item.relationName =
                item.demoname + "_" + item.organizationName.zh_CN;
            } else {
              item.relationName = item.organizationName.zh_CN;
            }
          } else {
            item.relationName = item.demoname;
          }
        });
      }
    });
  }
  ClearAll() {
    this.Removeallrelation(this.projectId);
  }
  Clearcancel() {}
  ClearOne(id) {
    this.Removeonerelation(id);
  }

  Removeallrelation(id) {
    this.Http.removeOneProjectAllIasSelectedQuestionMapping(id).subscribe(
      (res) => {
        if (res.result.code == 0) {
          this.ShowQuestionMapping();
        }
      }
    );
  }
  Removeonerelation(id) {
    this.Http.removeIasOneSelectedQuestionMapping(id).subscribe((res) => {
      if (res.result.code == 0) {
        this.ShowQuestionMapping();
      }
    });
  }
  getDimensionByProjectIdId() {
    let ids;
    if (this.projectId) {
      ids = this.projectId;
    } else {
      ids = this.standardQuestionnaireId;
    }
    this.Http.listQuestionDimensionByProjectIdId(ids).subscribe((item) => {
      this.ProjectIdItem = item.data;
      this.ProjectIdItem.forEach((val) => {
        val.indeterminate = false;
        val.allChecked = false;
        val.child.forEach((res) => {
          res.checked = false;
        });
      });
      // console.log(this.ProjectIdItem)
    });
  }

  updateAllChecked(i): void {
    this.ProjectIdItem[i].indeterminate = false;
    if (this.ProjectIdItem[i].allChecked) {
      this.ProjectIdItem[i].child = this.ProjectIdItem[i].child.map((item) => ({
        ...item,
        checked: true,
      }));
    } else {
      this.ProjectIdItem[i].child = this.ProjectIdItem[i].child.map((item) => ({
        ...item,
        checked: false,
      }));
    }
  }
  updateSingleChecked(i): void {
    if (this.ProjectIdItem[i].child.every((item) => !item.checked)) {
      this.ProjectIdItem[i].allChecked = false;
      this.ProjectIdItem[i].indeterminate = false;
    } else if (this.ProjectIdItem[i].child.every((item) => item.checked)) {
      this.ProjectIdItem[i].allChecked = true;
      this.ProjectIdItem[i].indeterminate = false;
    } else {
      this.ProjectIdItem[i].indeterminate = true;
    }
  }

  collapsemask(i, id) {
    this.Factorslist[i].children.forEach((son1) => {
      if (son1.id === id) {
        son1.checked = true;
      } else {
        son1.checked = false;
      }
      if (son1.children.length !== 0) {
        son1.children.forEach((son2) => {
          if (son2.id === id) {
            son2.checked = true;
          } else {
            son2.checked = false;
          }
        });
      }
    });
  }
  collapseChecked(i, j) {
    let checked = this.Factorslist[i].children[j].checked;
    let name = this.Factorslist[i].name.zh_CN;
    let sonname = this.Factorslist[i].children[j].name.zh_CN;

    this.Factorslist.forEach((item) => {
      if (name == item.name.zh_CN) {
        item.children.forEach((res) => {
          if (sonname == res.name.zh_CN) {
          } else {
            if (checked) {
              res.checked = false;
            }
          }
          if (res.children.length !== 0) {
            res.children.forEach((ele) => {
              ele.checked = false;
            });
          }
        });
      }
    });
  }
  factorSelectAll(e) {
    e.stopPropagation();
  }

  EditName() {
    this.showname = true;
    this.changeeditorName = JSON.parse(JSON.stringify(this.prismatitle.name));
  }

  namehandleCancel() {
    this.showname = false;
  }
  namehandleOk() {
    if (this.changeeditorName.zh_CN.trim() == "") {
      // this.msg.error("工具名称不能为空！");
      this.customMsg.open("error", "工具名称不能为空！");
      return;
    }
    this.isSpinning = true;
    if (!this.projectId) {
      let submitData = this.synthesisTime();
      if (submitData) {
        this.Http.create(submitData).subscribe((res) => {
          if (res.result.code == 0) {
            this.projectId = res.data[0].projectId;
            this.questionnaireId = res.data[0].id;
            this.UpdateName();
          }
        });
      } else {
        this.isSpinning = false;
      }
    } else {
      let submitData = this.synthesisTime();
      if (this.isUpdateing) {
        //发布的活动，
        if (submitData) {
          submitData.projectId = this.projectId;
          this.Http.updateProjectSetting(submitData).subscribe((res) => {
            if (res.result.code == 0) {
              this.isSpinning = false;
              this.UpdateName();
            }
          });
        } else {
          this.isSpinning = false;
        }
      } else {
        //未发布的活动，
        if (submitData) {
          /**
           *
           *  @author: Sid Wang
           *  @Date: 2023/09/14
           *  @code: #8183
           *  @todo: 调研：活动预发布状态变成了未发布-同步判断逻辑
           *
           */
          if (this.projectType == "PREVIEW") {
            submitData.status = "PREVIEW"; //(待发布)
          } else {
            submitData.status = "ANNOUNCED"; //(待发布)
          }
          submitData.id = this.projectId;
          this.Http.updateAnnouncedProject(submitData).subscribe((res) => {
            if (res.result.code == 0) {
              this.isSpinning = false;
              this.UpdateName();
            }
          });
        } else {
          this.isSpinning = false;
        }
      }
    }
  }

  UpdateName() {
    let params = {
      name: this.changeeditorName,
      questionnaireId: this.questionnaireId,
    };
    this.Http.updateAnswer(params).subscribe((res) => {
      if (res.result.code == 0) {
        this.showname = false;
        this.isSpinning = false;
        this.msg.success("更新成功！");
        this.getProjectSetting();
      } else {
        this.isSpinning = false;
      }
    });
  }

  hideProcess() {
    this.isNzOkLoading = false;
    this.isNzRELoading = false;
    this.isNzPRELoading = false;
    this.isSpinning = false;
  }
  onSelectI18n(e) {
    this.lan = e;
  }

  async getLanOptions() {
    const currentLansRes = await this.Http.getLanguages().toPromise();
    const defaultCode = ["zh_CN", "en_US"];
    const projectLanguages =
      JSON.parse(sessionStorage.getItem("projectLanguages")) || defaultCode;
    this.i18n = currentLansRes.data
      .filter((val) => projectLanguages.includes(val.value))
      .sort((a, b) => {
        if (a.value === "zh_CN") return -1;
        if (b.value === "zh_CN") return 1;
        if (a.value === "en_US") return -1;
        if (b.value === "en_US") return 1;
        if (a.value === "jp") return -1;
        if (b.value === "jp") return 1;
        if (a.value === "ko") return -1;
        if (b.value === "ko") return 1;
        if (a.value === "cs_1") return -1;
        if (b.value === "cs_1") return 1;
        if (a.value === "cs_2") return -1;
        if (b.value === "cs_2") return 1;
        if (a.value === "cs_3") return -1;
        if (b.value === "cs_3") return 1;
        return 0;
      });
  }
  setLanOptions() {
    if (!this.routerInfo.snapshot.queryParams.projectId) {
      sessionStorage.setItem(
        "projectLanguages",
        JSON.stringify(["zh_CN", "en_US"])
      );
      sessionStorage.setItem("language", "zh_CN");
      this.i18n = [{ name: "中文", value: "zh_CN" }];
      return;
    }
  }
  /**
   *
   * 交叉分析 弹窗
   */
  showCrossAnalytical() {
    const tplModal = this.drawerService.create({
      nzTitle: "交叉分析",
      nzContent: CrossAnalysisComponent,
      // nzFooter: null,
      nzWrapClassName: "round-right-drawer6",
      nzContentParams: {
        projectId: this.projectId,
      },
      nzWidth: 1000,
      // nzMaskClosable: false,
      // nzClosable: false,
    });
  }
  /**
   *
   * 多群体分析 弹窗
   */
  showGroupAnalytical() {
    const allFactorVOS =
      this.prismaData.factorname.standardAnalysisFactorVOS || [];
    const checkedFactors = allFactorVOS
      .filter((val) => val.isChecked)
      .map((val) => val.code);
    const tplModal = this.drawerService.create({
      nzTitle: "多群体分析",
      nzContent: GroupAnalysisComponent,
      // nzFooter: null,
      nzWrapClassName: "round-right-drawer6",
      nzContentParams: {
        projectId: this.projectId,
        checkedCodes: checkedFactors,
      },
      nzWidth: 960,
      // nzMaskClosable: false,
      // nzClosable: false,
    });
  }

  /**
   * 题本分发-导入
   * @param item
   */
  onDispenseImport = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.onDispenseUploadExcel(formData, item);
  };
  /**
   * 题本分发-导入-上传配置
   */
  onDispenseUploadExcel(formData, item) {
    return this.Http.importSelectQuestion(formData, this.projectId).subscribe(
      (res) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.msg.success("题本分发导入成功！");
          this.organizationId = "";
          this.demographicId = [];
          this.Step = 1;
          this.ShowQuestionMapping();
          this.getOrganizationByTree();
          this.getlistByProjectId();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  /**
   * 题本分发-导出
   */
  onDispenseDownLoad() {
    this.isDispenseDownLoadSpinning = true;
    this.Http.exportSelectQuestion(this.projectId).subscribe((res) => {
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.isDispenseDownLoadSpinning = false;
    });
  }
  sort() {
    this.ShowQuestion.forEach((item, i) => {
      item.sort = i + 1;
      item.projectId = this.projectId;
    });
    this.Http.saveIasSelectedQuestionMappingSort(this.ShowQuestion).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.msg.success("修改成功！");
          this.showRelations = false;
        }
      }
    );
  }

  //一键隐藏人口学标签
  hiddenAllcheck() {
    this.isShowAll = !this.isShowAll;
    this.prismaData.factorname.standardAnalysisFactorVOS.forEach((res) => {
      if (res.isChecked && !res.isHidden) {
        res.isHidden = true;
      }
    });
    this.prismaData.analysisFactorDto = this.prismaData.factorname.standardAnalysisFactorVOS;
  }

  //一键显示人口标签
  showAllCheck() {
    this.isShowAll = !this.isShowAll;
    this.prismaData.factorname.standardAnalysisFactorVOS.forEach((res) => {
      if (res.isChecked && res.isHidden) {
        res.isHidden = false;
      }
    });
    this.prismaData.analysisFactorDto = this.prismaData.factorname.standardAnalysisFactorVOS;
  }

  /**
   *
   *  数据呈现/计算规则 弹窗
   */
  showDataShowCalRule() {
    const tplModal = this.drawerService.create({
      nzTitle: "数据呈现/计算规则",
      nzContent: DataShowCalRuleComponent,
      // nzFooter: null,
      nzWrapClassName: "round-right-drawer6-bodyNoPadding",
      nzContentParams: {
        projectId: this.projectId,
      },
      nzWidth: 1000,
      // nzMaskClosable: false,
      // nzClosable: false,
    });
  }
}
