@text-color: #17314c;
.content {
  width: 100%;
}
.s1 {
  width: 100%;
  background-color: #ffffff;
  & > div {
    margin: 0 auto;
    padding: 60px 0;
    display: flex;
  }
  .s1-l {
    flex: 2;
    padding-right: 180px;
  }
  .s1-r {
    flex: 1;
  }
  h5 {
    font-size: 30px;
    line-height: 42px;
    margin-bottom: 25px;
    span {
      font-size: 12px;
      color: #409eff;
      border-radius: 14px;
      padding: 2px 10px;
      margin-left: 30px;
      background-color: rgba(64, 158, 255, 0.1);
    }
  }
  p {
    font-size: 14px;
    line-height: 24px;
    margin-bottom: 20px;
    position: relative;
    padding-left: 20px;
    &::before {
      content: "";
      display: block;
      width: 5px;
      height: 5px;
      border-radius: 5px;
      background-color: #409eff;
      position: absolute;
      top: 10px;
      left: 0;
    }
    &:last-of-type {
      margin-bottom: 44px;
    }
  }
  .btn {
    width: 160px;
    height: 38px;
    line-height: 38px;
    background: linear-gradient(
      90deg,
      rgba(38, 208, 241, 1) 0%,
      rgba(64, 158, 255, 1) 100%
    );
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
  }
}
.s2 {
  width: 100%;
  background-color: #f5f6fa;
  padding-top: 40px;
  padding-bottom: 100px;
  h5 {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 76px 0 56px;
  }
  .s2-main {
    margin: 0 auto;
    padding: 30px 0 60px;
    display: flex;
    li {
      flex: 1;
      text-align: center;
      padding-right: 80px;
      &:last-of-type {
        padding-right: 0;
      }
      h3 {
        font-size: 20px;
        margin: 45px 0 25px;
      }
      p {
        font-size: 16px;
        text-align: left;
      }
    }
  }
  .btn {
    text-align: center;
    button {
      width: 160px;
      height: 38px;
      line-height: 38px;
      background: linear-gradient(
        90deg,
        rgba(38, 208, 241, 1) 0%,
        rgba(64, 158, 255, 1) 100%
      );
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
    }
  }
}
.s3 {
  width: 100%;
  background-color: #ffffff;
  .s3-main {
    margin: 0 auto;
    padding: 60px 0;
    display: flex;
  }
  h5 {
    font-size: 24px;
    font-weight: bold;
    padding: 150px 0 40px;
  }
  .s3-r {
    flex: 1;
    font-size: 16px;
  }
  .s3-l {
    flex: 1;
    img {
      margin-top: 30px;
    }
    text-align: center;
  }
}
.s4 {
  width: 100%;
  background-color: #f5f6fa;
  padding-top: 40px;
  .s4-main {
    margin: 0 auto;
    padding: 60px 0;
  }
  .s4-top{
    display: flex;
  }
  h5 {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 60px 0 60px;
    background: url(../../../../assets/images/q.png) no-repeat center;
  }
  .s4-r {
    flex: 2;
    font-size: 16px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    li {
      background-color: #ffffff;
      border-radius: 8px;
      padding: 20px;
      width: 48%;
      margin-bottom: 40px;
      h3 {
        font-size: 18px;
        line-height: 25px;
        font-weight: bold;
        // color: #495970;
      }
      &:nth-child(3),
      &:nth-child(4) {
        margin-bottom: 0;
      }
      img {
        margin: 13px 0 30px;
      }
    }
  }
  .s4-l {
    flex: 1;
    padding: 40px 40px 0 40px;
    margin-right: 40px;
    background-color: #ffffff;
    border-radius: 8px;
    .s4-l-t {
      font-size: 18px;
      font-weight: bold;
      display: flex;
      h3 {
        font-size: 30px;
        margin-top: 20px;
        font-weight: bold;
        // color: #495970;
      }
      div {
        display: inline-block;
        flex: 1;
        padding-top: 30px;
      }
    }
    .s4-l-b {
      li {
        border-radius: 46px;
        background-color: #f1f8ff;
        font-size: 16px;
        padding: 0 28px;
        margin-bottom: 21px;
        line-height: 40px;
        &:first-child {
          margin-top: 42px;
        }
        img {
          margin-right: 20px;
        }
        span {
          color: #6cb4ff;
        }
      }
    }
  }
  .s4-bottom {
    background-color: #ffffff;
    border-radius: 8px;
    margin-top: 38px;
    padding: 22px 46px;
    h3 {
      font-size: 18px;
      line-height: 32px;
      font-weight: bold;
      display: flex;
      align-items: center;
      // color: #495970;
      img {
        width: 32px;
        height: 32px;
        margin-right: 16px;
      }
    }
    p {
      font-size: 14px;
      color: #17314C;
      line-height: 20px;
      margin: 30px 0 40px 0;
    }
    img{
      width: 100%;
    }
  }
}
.s5 {
  width: 100%;
  background-color: #ffffff;
  padding-top: 40px;
  padding-bottom: 100px;
  h5 {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 76px 0 56px;
  }
  .s5-main {
    margin: 0 auto;
    padding: 30px 0 60px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    li {
      width: 49.2%;
      padding: 29px 32px;
      position: relative;
      background: #f4faff;
      margin: 3px;
      h3 {
        font-size: 19px;
        margin-bottom: 22px;
      }
      p {
        font-size: 16px;
      }
      img {
        position: absolute;
        top: 22px;
        right: 40px;
      }
    }
  }
  .btn {
    text-align: center;
    button {
      width: 160px;
      height: 38px;
      line-height: 38px;
      background: linear-gradient(
        90deg,
        rgba(38, 208, 241, 1) 0%,
        rgba(64, 158, 255, 1) 100%
      );
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
    }
  }
}
