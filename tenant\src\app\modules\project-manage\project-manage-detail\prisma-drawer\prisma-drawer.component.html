<div class="container">
  <!-- <div class="header">
      <div class="text">
          填答进度
      </div>
  </div> -->
  <ng-template #extraTemplate>
    <!-- <div class="batchexport" (click)="funbatchexport()" *knxFunctionPermission="'SAG:TENANT:PROJECT_MGT:DETAIL:BATCH_EXPORT_ANSWER_RATE'">
      <i class="iconfont icon-export_ic"></i>批量导出
    </div> -->
    <a
      (click)="funbatchexport()"
      *ngIf="
        permissionService.isPermissionOrSag(
          'SAG:TENANT:PROJECT_MGT:DETAIL:BATCH_EXPORT_ANSWER_RATE'
        )
      "
      ><i class="iconfont icon-export_ic"></i>批量导出</a
    >
  </ng-template>
  <nz-tabset
    (nzSelectChange)="tabChange($event)"
    [nzTabBarExtraContent]="extraTemplate"
  >
    <nz-tab nzTitle="组织部门">
      <div class="tab-box" *ngIf="tabIndex === 0">
        <app-organization-page [projectId]="projectId"></app-organization-page>
      </div>
    </nz-tab>
    <nz-tab nzTitle="人口标签">
      <div class="tab-box" *ngIf="tabIndex === 1">
        <app-population-label-page
          [projectId]="projectId"
        ></app-population-label-page>
      </div>
    </nz-tab>
    <nz-tab
      nzTitle="填答人"
      *ngIf="permission"
    >
      <div class="tab-box" *ngIf="tabIndex === 2">
        <!-- 人员列表 -->
        <div class="body bg">
          <div class="top">
            <div style="flex: 1;margin-right: 20px;">
              <nz-input-group [nzPrefix]="suffixIconSearch1">
                <input
                  type="text"
                  nz-input
                  placeholder="请输入关键词"
                  (keydown.enter)="search(true)"
                  [(ngModel)]="searchRespondentValue"
                />
              </nz-input-group>

              <ng-template #suffixIcon>
                <i nz-icon nzType="search"></i>
              </ng-template>

              <ng-template #suffixIconSearch1>
                <img src="./assets/images/icon_search.png" />
              </ng-template>
            </div>
            <app-advanced-model
              #advancedChild
              [isMin]="true"
              [father]="this"
              [type]="4"
            ></app-advanced-model>
          </div>
          <div class="respondent-btns">
            <button
              class="draw-button"
              nz-button
              nzSize="small"
              nzType="primary"
              (click)="changeRespondentAllChecked()"
            >
              {{ isAll ? "全不选" : "全选" }}
            </button>
            <button
              class="draw-button"
              nz-button
              nzSize="small"
              nzType="primary"
              (click)="exportRespondentList()"
            >
              导出
            </button>
            <button
              class="draw-button"
              nz-button
              nzSize="small"
              nzType="primary"
              (click)="showDeleteConfirm()"
            >
              删除
            </button>
            <a (click)="showClearConfirm()">清除数据</a>
            <a (click)="loadingData()">清空条件</a>
            <ng-container *ngIf="isAll || respondentCheckedIds.length">
              <nz-tag [nzColor]="'blue'" style="margin-left: 10px;"
                >已选择
                {{ isAll ? totalCount : respondentCheckedIds.length }}
                条数据</nz-tag
              >
            </ng-container>
          </div>
          <nz-table
            style="background-color: white;"
            #basicTable
            [nzData]="respondentList"
            [nzFrontPagination]="false"
          >
            <!-- [nzScroll]="{ y: 'calc(100vh - 360px)' }" -->
            <thead>
              <tr>
                <th></th>
                <th nzWidth="60px">姓名</th>
                <!-- <th>编号</th> -->
                <th nzWidth="90px">完成时间</th>
                <th nzWidth="100px">所在部门</th>
                <th>填答来源</th>
                <th>填答状态</th>
              </tr>
            </thead>

            <tbody>
              <tr *ngFor="let data of basicTable.data; let ind = index">
                <td
                  nzShowCheckbox
                  [nzDisabled]="isAll"
                  [(nzChecked)]="data.checked"
                  (nzCheckedChange)="changeRespondentChecked($event, data.id)"
                ></td>
                <!-- <td>{{(currentPage - 1) * pageSize + ind + 1 }}</td> -->
                <td>
                  <span
                    class="text-wrap"
                    nz-tooltip
                    [nzTooltipTitle]="data.firstName+data.code"

                    
                    >{{ data.firstName }}</span
                  >
                </td>
                <!-- <td>{{data.createTime | date:'yyyy-MM-dd HH:mm' }}</td> -->
                <!-- <td>{{data.code}}</td> -->
                <td>{{ data.endTime | date: "yyyy-MM-dd HH:mm" }}</td>
                <td>
                  <span
                    class="text-wrap"
                    nz-tooltip
                    [nzTooltipTitle]="data.detailedOrganizationName?.zh_CN"
                    >{{ data.detailedOrganizationName?.zh_CN }}</span
                  >
                </td>
                <td>{{ data.answerSourceDesc }}</td>
                <td>
                  <nz-tag
                    *ngIf="data.answerStatus === 'ANSWERED'"
                    [nzColor]="'green'"
                    >已完成</nz-tag
                  >
                  <nz-tag
                    *ngIf="data.answerStatus !== 'ANSWERED'"
                    [nzColor]="'red'"
                    >未完成</nz-tag
                  >
                  <!-- {{data.answerStatus === 'ANSWERED' ? '已完成' : '未完成'}} -->
                  <!-- <app-btn [text]="''" [image]="'./assets/images/org/del.png'" [hoverColor]="'#409EFF'"
                              [hoverImage]="'./assets/images/org/del_hover.png'" nz-popconfirm
                              [nzPopconfirmTitle]="'确定要删除人员?'" nzPopconfirmPlacement="bottom"
                              (nzOnConfirm)="deletePerson(data.id)">
                          </app-btn>
                          <ng-container *ngIf="permission">
                            <i *ngIf="data.answerStatus !== 'NOT_INVITED', else IconTem" class="iconfont icon-icon-" title="清除"
                            (click)="clearHistory(data.id)"></i>
                            <ng-template #IconTem>
                              <i class="iconfont icon-icon-" style="cursor:not-allowed;" title="清除"></i>
                            </ng-template>
                          </ng-container> -->
                </td>
              </tr>
            </tbody>
          </nz-table>

          <!-- 分页控件 -->
          <div
            style="display: flex; justify-content: flex-end; padding: 12px 0;"
          >
            <nz-pagination
              [(nzPageIndex)]="page.current"
              [(nzPageSize)]="page.size"
              [nzTotal]="page.total"
              [nzSize]="'small'"
              (nzPageIndexChange)="loadData()"
              nzShowQuickJumper
            >
            </nz-pagination>
          </div>
        </div>
      </div>
    </nz-tab>
  </nz-tabset>
</div>
