@text-color: #17314c;

.index-title {
  font-size: 24px;
  line-height: 24px;
  color: @text-color;
  margin: 25px 0;
  font-weight: 300;
}

.nz_spin {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #eee;
  opacity: 0.4;
  z-index: 999;
}

.active_titile {
  display: flex;
  align-items: center;
}

.put_div {
  width: 262px;
  height: 30px;
  background: #fff;
  border-radius: 15px;
  margin-left: 60px;

  input {
    border: none;
    border-radius: 15px;
  }
}

.choose_list {
  .flex_ul {
    display: flex;
    flex-direction: column;

    .clear_div {
      cursor: pointer;
      margin-bottom: 15px;
      display: flex;
      justify-content: flex-end;
    }

    .new_create {
      border: 1px solid #e2e2e2;
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      li {
        display: flex;
        align-items: center;
        flex: 1;
        background-color: #f3f3f3;

        .news_span_f {
          font-weight: bold;
          width: 73px;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
        }

        .check_div {
          // margin-left: 20px;
          padding: 10px 5px;
          flex: 1;
          background-color: #fff;

          .row_div {
            display: flex;
            flex-wrap: wrap;

            .list_div {
              // margin-top: 10px;
              padding: 0 0 0 15px;
              width: 120px;
              height: 30px;
              line-height: 30px;
              overflow: hidden;
              /*超出部分隐藏*/
              white-space: nowrap;
              /*不换行*/
              text-overflow: ellipsis;
              /*超出部分文字以...显示*/
            }

            .news_list {
              margin-left: 0px;

            }

            .news_span {
              display: inline-block;
              min-width: 60px;
              height: 30px;
              line-height: 30px;
              margin-left: 8px;
              cursor: pointer;
            }
          }
        }
      }
    }

    .select_tags {
      margin-top: 20px;
      height: 50px;
      background: #fbfbfb;
      border-radius: 4px;
      border: 1px solid #e2e2e2;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
  }
}

.end_ul {
  margin-top: 20px;
  height: 93px;
  background: #F3F7FB;
  border-radius: 4px 4px 0px 0px;
  border: 1px solid #e2e2e2;
  display: flex;
  flex-direction: column;

  .tool_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-size: 16px;
    flex: 1;

    .left_li {
      width: 300px;
      display: flex;
      justify-content: space-between;
      color: #aaaaaa;

      .san_div {
        display: flex;
        align-items: center;

        .img_flex {
          display: flex;
          flex-direction: column;
          margin-left: 8px;

          img {
            cursor: pointer;
          }

          .tans_img {
            transform: rotate(180deg);
          }
        }
      }
    }

    .tool_right {
      display: flex;

      div {
        margin-left: 16px;
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }
  }
}

.modal_div {
  width: 100%;
  height: 400px;
  overflow-y: auto;
  padding: 0 20px;

  .cards_ul_2 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    &:after {
      content: "";
      width: 48%;
      height: 0;
    }

    li {
      width: 48%;
      height: 157px;
      background-color: #fff;
      border: 1px solid red;
      margin-top: 15px;
      border-radius: 8px;
    }
  }
}

.map_table {
  width: 100%;

  .tab_card {
    width: 100%;
    border-top: 1px solid #e6e6e6;
    border-left: 1px solid #e6e6e6;
    border-right: 1px solid #e6e6e6;

    li {
      width: 100%;
      display: flex;
      border-bottom: 1px solid #e6e6e6;

      >div {
        flex: 1;
        line-height: 30px;
        border-right: 1px solid #e6e6e6;

        &:last-child {
          border: none;
        }
      }
    }
  }
}

.settlement {
  width: 100%;
  height: 70px;
  line-height: 70px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);
  position: fixed;
  bottom: 0px;
  z-index: 1000;

  .content {
    align-items: center;
  }

  .p1 {
    font-size: 14px;
    color: #495970;
  }

  .btn {
    width: 128px;
    height: 38px;
    cursor: pointer;
    background: linear-gradient(90deg,
        rgba(38, 208, 241, 1) 0%,
        rgba(64, 158, 255, 1) 100%);
    box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    font-size: 16px;
    text-align: center;
    color: #fff;
    transition: all 0.3s ease 0s;

    &:hover {
      transform: scale(1.05);
    }
  }

  .btn_w {
    width: 128px;
    height: 38px;
    background: #FAFAFA;
    border-radius: 19px;
    text-align: center;
    cursor: pointer;
    border: 0px solid #409eff;
    color: #AAAAAA;
  }

  .btn[disabled] {
    background: #ebebeb;
    cursor: not-allowed;
  }
}

.search_none {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 60px;

  img {
    width: 150px;
    height: 144px;
  }

  .one_word {
    margin-left: -30px;
    font-weight: bold;
    color: #17314c;
    padding: 10px 0;
    font-size: 20px;
  }

  .two_word {
    color: #495970;
    font-size: 16px;
    padding: 10px 0;
  }

  .btn_release {
    margin-top: 10px;
    cursor: pointer;
    width: 128px;
    line-height: 38px;
    text-align: center;
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    color: #fff;
    font-size: 16px;
  }
}

.mock_div {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999999999;

  .bg_ul {
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.6;
  }

  .img_ul {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;

    >li {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .btn_div {
      width: 160px;
      line-height: 38px;
      text-align: center;
      color: #fff;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      cursor: pointer;
    }
  }
}

.body_span {
  -moz-user-select: none;
  /*火狐*/
  -webkit-user-select: none;
  /*webkit浏览器*/
  -ms-user-select: none;
  /*IE10*/
  user-select: none;
}

.pagination {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 50px;
}

.font_color {
  color: #000;
}

.uniqueclass {
  background-color: #F3F7FB;

}

.children_div {
  background-color: #F3F7FB;
  display: flex;
  flex-wrap: wrap;
  margin-top: 0px;
  padding: 10px;
}