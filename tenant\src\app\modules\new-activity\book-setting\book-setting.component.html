<nz-tabset
  [(nzSelectedIndex)]="indexNum"
  [nzTabPosition]="position"
  [nzType]="'line'"
>
  <nz-tab *ngFor="let tab of tabs" [nzTitle]="tab">
    <div
      style="display: flex; justify-content: space-between; flex-direction: column; margin-left: 10px;"
      *ngIf="indexNum === 0; else other"
    >
      <div>
        <div class="label">填答时可选语言</div>
        <nz-checkbox-group
          [(ngModel)]="languageList"
          (ngModelChange)="updateChecked()"
        ></nz-checkbox-group>
      </div>
      <div style="height: 30px;"></div>
      <div>
        <div class="label">默认填答语言</div>
        <nz-radio-group [(ngModel)]="radioValue">
          <ng-container *ngFor="let item of defaultList">
            <label nz-radio [nzValue]="item.value">{{ item.label }}</label>
          </ng-container>
        </nz-radio-group>
      </div>
    </div>
    <ng-template #other>
      <div>pls wait {{ tab }}</div>
    </ng-template>
  </nz-tab>
</nz-tabset>

<div style="height: 10px;"></div>

<div class="footer">
  <button nz-button class="iptBtn" (click)="ok()">
    <span>确认</span>
  </button>
</div>
