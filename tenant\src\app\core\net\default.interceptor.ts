import {
  Component,
  Injectable,
  Injector,
  TemplateRef,
  ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpErrorResponse,
  HttpEvent,
  HttpResponseBase,
  HttpResponse,
} from "@angular/common/http";
import { Observable, of, throwError } from "rxjs";
import { mergeMap, catchError } from "rxjs/operators";
import { NzMessageService, NzNotificationService } from "ng-zorro-antd";
import { _HttpClient } from "@knz/theme";
import { environment } from "@env/environment";
import { DA_SERVICE_TOKEN, ITokenService } from "@knz/auth";
import { OnDestroy, Inject, Optional } from "@angular/core";
// import { MsgContentComponent } from './msg-content/msg-content.component';
import { MessageService } from "@src/shared/custom-message/message-service.service";

const CODEMESSAGE = {
  200: "服务器成功返回请求的数据。",
  201: "新建或修改数据成功。",
  202: "一个请求已经进入后台排队（异步任务）。",
  204: "删除数据成功。",
  400: "发出的请求有错误，服务器没有进行新建或修改数据的操作。",
  401: "用户没有权限（令牌、用户名、密码错误）。",
  403: "用户得到授权，但是访问是被禁止的。",
  404: "发出的请求针对的是不存在的记录，服务器没有进行操作。",
  406: "请求的格式不可得。",
  410: "请求的资源被永久删除，且不会再得到的。",
  422: "当创建一个对象时，发生一个验证错误。",
  500: "服务器发生错误，请检查服务器。",
  502: "网关错误。",
  503: "服务不可用，服务器暂时过载或维护。",
  504: "网关超时。",
};
let loginStatus;

/**
 * 默认HTTP拦截器，其注册细节见 `app.module.ts`
 */
@Injectable({
  providedIn: "root",
})
export class DefaultInterceptor implements HttpInterceptor {
  // @ViewChild('template', { static: true }) template!: MsgContentComponent;

  constructor(
    private injector: Injector,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private notiService: NzNotificationService,
    private msg: NzMessageService,
    private customMsg: MessageService
  ) {}

  private get notification(): NzNotificationService {
    return this.injector.get(NzNotificationService);
  }

  private goTo(url: string) {
    setTimeout(() => this.injector.get(Router).navigateByUrl(url));
  }

  private checkStatus(ev: HttpResponseBase) {
    if ((ev.status >= 200 && ev.status < 300) || ev.status === 401) {
      return;
    }

    if (loginStatus !== "notLogin") {
      const errortext = CODEMESSAGE[ev.status] || ev.statusText;
      //this.notification.error(`请求错误 ${ev.status}`, errortext);
      //: ${ev.url}
    }
  }

  private solveToken(token_code: number) {
    switch (token_code) {
      case 14:
      case 15:
      case 16:
        this.jumpToLogin("登录过期，请重新登录");
        break;
      case 17:
        this.jumpToLogin("您的账号已在其它地方登录");
        break;
    }
  }

  private jumpToLogin(message: string) {
    this.notiService.error("登录信息", message);
    this.tokenService.clear();
    localStorage.removeItem("token");
    this.goTo("/user/login");
  }

  private handleData(ev: HttpResponseBase): Observable<any> {
    // 可能会因为 `throw` 导出无法执行 `_HttpClient` 的 `end()` 操作
    let responseToken = ev.headers.get("token");
    if (responseToken) {
      console.log("handleData response token=" + responseToken);
      let storedData = {
        token: responseToken,
        time: Date(),
      };
      localStorage.setItem("token", JSON.stringify(storedData));
      this.tokenService.set(storedData);
    }
    if (ev.status > 0) {
      this.injector.get(_HttpClient).end();
    }
    this.checkStatus(ev);
    // 业务处理：一些通用操作

    switch (ev.status) {
      case 200:
        // 业务层级错误处理，以下是假定restful有一套统一输出格式（指不管成功与否都有相应的数据格式）情况下进行处理
        // 例如响应内容：
        //  错误内容：{ status: 1, msg: '非法参数' }
        //  正确内容：{ status: 0, response: {  } }
        // 则以下代码片断可直接适用
        // if (event instanceof HttpResponse) {
        //     const body: any = event.body;
        //     if (body && body.status !== 0) {
        //         this.msg.error(body.msg);
        //         // 继续抛出错误中断后续所有 Pipe、subscribe 操作，因此：
        //         // this.http.get('/').subscribe() 并不会触发
        //         return throwError({});
        //     } else {
        //         // 重新修改 `body` 内容为 `response` 内容，对于绝大多数场景已经无须再关心业务状态码
        //         return of(new HttpResponse(Object.assign(event, { body: body.response })));
        //         // 或者依然保持完整的格式
        //         return of(event);
        //     }
        // }
        break;
      case 401:
        let result_ev = ev["error"].result;

        this.solveToken(result_ev.code);
        break;
      case 403:
      case 404:
      case 500:
        // this.goTo(`/exception/${ev.status}`);

        // this.notification.error(`${ev.status}`, ``);
        break;
      default:
        if (ev instanceof HttpErrorResponse) {
          console.warn(
            "未可知错误，大部分是由于后端不支持CORS或无效配置引起",
            ev
          );
        }
        break;
    }

    if (ev instanceof HttpErrorResponse) {
      return throwError(ev);
    } else {
      this.popLogicError(ev);
      return of(ev);
    }
  }

  // 逻辑错误，抛出异常
  popLogicError(event) {
    if (event instanceof HttpResponse) {
      const body: any = event.body;
      if (body && body.result) {
        let result = body.result;
        if (result.code >= 10000) {
          this.customMsg.open("error", result.message);
          // this.msg.error(
          //   result.message,
          //   {
          //     nzDuration: 3000,
          //     nzPauseOnHover: false,
          //     nzAnimate: true
          //   });
        }
      }
    }
  }

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // 统一加上服务端前缀
    let url = req.url;
    //
    // if (!url.startsWith('https://') && !url.startsWith('http://') && url.indexOf("assets") < 0) {
    //   url = environment.SERVER_URL + url;
    // }

    let token = this.tokenService.get().token;
    //  console.log("get userData : " + JSON.stringify(token));

    const headers = {
      Authorization: `${token}`,

      superadmin: sessionStorage.getItem("permission")
        ? sessionStorage.getItem("permission")
        : "false",
    };

    const newReq = req.clone({
      url,
      setHeaders: headers,
      params: req.params.append("_allow_anonymous", "true"),
    });
    loginStatus = req.headers.get("type");
    return next.handle(newReq).pipe(
      mergeMap((event: any) => {
        // 允许统一对请求错误处理
        if (event instanceof HttpResponseBase) return this.handleData(event);
        // 若一切都正常，则后续操作
        return of(event);
      }),
      catchError((err: HttpErrorResponse) => this.handleData(err))
    );
  }
}
