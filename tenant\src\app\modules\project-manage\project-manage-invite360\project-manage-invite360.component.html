<div class="warp" style="background-color: #F5F6FA;height: 100%;">
  <div class="container client-width">
    <header class="title-warp">
      <div>
        <span class="title">{{ title }}</span>
        <div class="title-handle" [hidden]="step == 1">
          <nz-upload
            [nzPreview]="preview"
            [nzCustomRequest]="customReq"
            style="width: 130px;"
            [nzFileList]="fileList"
          >
            <button class="upload_button" nz-button>
              <span>批量上传</span>
            </button>
          </nz-upload>
          <a
            (click)="downLoad()"
            nz-button
            nzType="link"
            [nzLoading]="buttonload"
            >下载模板</a
          >
        </div>
      </div>

      <div>
        <a *ngIf="Projectstatus === 'checked' && isQuickMutualEvaluation" (click)="getshiplist()"
          >导出互评关系</a
        >
        <a *ngIf="Projectstatus === 'checked' && !isQuickMutualEvaluation" (click)="getshiplist()"
          >导出评价关系</a
        >
      </div>
      <nz-input-group
        *ngIf="projectId && action === 'edit'"
        [nzPrefix]="suffixIconSearch"
        class="search"
      >
        <input
          type="text"
          nz-input
          placeholder=" 请输入关键词"
          [(ngModel)]="keyword"
          (keydown.enter)="filterData()"
          (blur)="filterData()"
        />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <img src="./assets/images/icon_search.png" />
      </ng-template>

      <ul style="display: flex;justify-content: flex-end;">
        <app-break-crumb
          [Breadcrumbs]="Breadcrumbs"
          [queryParams]="queryParams"
        ></app-break-crumb>
      </ul>
    </header>

    <div [hidden]="step == 2">
      <div
        *ngIf="action !== 'edit' && action !== 'reSendEmail'"
        class="sub-title-warp"
      >
        <div>
          <button class="add_button" (click)="addObserver()">
            <span>
              新增
            </span>
          </button>
          <a (click)="showRoleModal()">角色管理</a>
        </div>
        <div style="display: flex;align-items: center;">
          <div class="put_div">
            <nz-input-group [nzPrefix]="suffixIconSearch" class="search">
              <input
                type="text"
                nz-input
                placeholder=" 请输入关键词"
                [(ngModel)]="keyWord"
                (blur)="getcardone()"
                (keydown.enter)="getcardone()"
              />
            </nz-input-group>
            <ng-template #suffixIconSearch>
              <img src="assets/images/icon_search.png" />
            </ng-template>
          </div>
          <a *ngIf="isQuickMutualEvaluation"
            style="display: inline-block; width: 90px; margin-left: 10px;"
            (click)="downLoad360()"
            >下载互评模板</a
          >
          <a *ngIf="!isQuickMutualEvaluation"
            style="display: inline-block; width: 60px; margin-left: 10px;"
            (click)="downLoad360()"
            >下载模板</a
          >
          <nz-upload
            [nzPreview]="preview"
            [nzShowUploadList]="false"
            [nzCustomRequest]="customReq360"
          >
            <button class="upload_button" nz-button>
              <span>批量上传</span>
            </button>
          </nz-upload>
        </div>
      </div>
      <ng-container *ngIf="action !== 'reSendEmail'; else sendEmail">
        <div class="list" *ngIf="uploadList.length > 0; else elseBlock">
          <div
            class="observer"
            *ngFor="let parentData of uploadList; let i = index"
          >
            <div class="header">
              <div>
                <span class="list-title">被评估人</span>
                <ng-container *ngIf="!isInviteAnswer">
                  <button
                    *ngIf="Projectstatus !== 'checked'"
                    class="btn-add-observer"
                    (click)="addChild(parentData)"
                  >
                    添加评估人
                  </button>
                </ng-container>
                <ng-container *ngIf="isInviteAnswer">
                  <button
                    class="btn-add-observer"
                    (click)="showSelfInviteDrawer(parentData)"
                  >
                    设置自邀请人数
                  </button>
                </ng-container>
                <!-- <a *ngIf="isCustomRoleWeight == 'true'" (click)="showRoleModal(parentData.roleslistnew,parentData.key,i,'small')" style="padding: 0 20px;">设置角色权重</a> -->
                <button
                  *ngIf="isCustomRoleWeight == 'true'"
                  class="btn-add-observer"
                  (click)="
                    showRoleModal(
                      parentData.roleslistnew,
                      parentData.key,
                      i,
                      'small'
                    )
                  "
                >
                  设置角色权重
                </button>
                <span class="label-ovserver">被评估人</span>
                <span class="label-reviewer">{{
                  isInviteReviewEvaluatee && action !== "edit"
                    ? "审核人"
                    : "评估人"
                }}</span>
              </div>
              <div
                *ngIf="
                  (action !== 'edit' || backtype == 'create') &&
                  Projectstatus !== 'checked'
                "
                class="btn-delete"
                (click)="deleteObserver(parentData.key)"
              >
                删除
              </div>
            </div>
            <nz-table
              #nestedTable
              [nzData]="[parentData]"
              [nzShowPagination]="false"
              [nzScroll]="{ x: '980px' }"
              [nzWidthConfig]="['12%', '15%', '15%', '15%', '15%', '15%']"
            >
              <thead>
                <tr>
                  <th>人员编码</th>
                  <th>姓名</th>
                  <th>邮箱地址</th>
                  <th>手机号码</th>
                  <th>角色</th>
                  <th>占比</th>
                  <!-- <th>权重</th> -->
                  <th nzAlign="right">自评</th>
                </tr>
              </thead>
              <tbody>
                <ng-template ngFor let-data [ngForOf]="nestedTable.data">
                  <tr class="tr-parent nested-table">
                    <td class="required email-col">
                      <input
                        class="choose"
                        nz-input
                        type="text"
                        [(ngModel)]="data.code"
                        maxlength="50"
                        [disabled]="data.disabled"
                      />
                    </td>
                    <td class="required name-col">
                      <input
                        [disabled]="Projectstatus == 'checked'"
                        class="choose"
                        nz-input
                        type="text"
                        [(ngModel)]="data.name"
                      />
                    </td>
                    <td class="required email-col">
                      <input
                        class="choose"
                        nz-input
                        type="email"
                        [(ngModel)]="data.email"
                      />
                    </td>
                    <td class="phone-col">
                      <input
                        [disabled]="Projectstatus == 'checked'"
                        nz-input
                        type="text"
                        [(ngModel)]="data.phone"
                        maxlength="11"
                      />
                    </td>
                    <td class="required role-col">
                      <nz-select
                        class="choose"
                        [(ngModel)]="data.roleId"
                        placeholder="请选择"
                        [nzDisabled]="true"
                      >
                        <nz-option nzValue="" nzLabel="请选择"></nz-option>
                        <nz-option
                          [nzValue]="option.id"
                          [nzLabel]="option.name?.zh_CN"
                          *ngFor="let option of rolelist"
                        >
                        </nz-option>
                      </nz-select>
                    </td>
                    <td class="required weight-col">
                      <nz-input-number
                        [nzPrecision]="precision"
                        nzPlaceHolder="toFixed"
                        nzMin="0"
                        nzMax="100"
                        [(ngModel)]="data.weights"
                        class="input-step"
                        [nzDisabled]="true"
                      >
                      </nz-input-number>
                    </td>
                    <td class="self-col" nzAlign="right">
                      <nz-switch
                        [nzDisabled]="true"
                        [(ngModel)]="data.isSelfEvaluation"
                      ></nz-switch>
                    </td>
                  </tr>
                  <tr [nzExpand]="data.expand" *ngIf="data.persons.length">
                    <td colspan="7" style="padding: 16px;">
                      <nz-table
                        #innerTable
                        [nzData]="data.persons"
                        [nzFrontPagination]="false"
                        [nzShowPagination]="false"
                        [nzWidthConfig]="[
                          '12%',
                          '15%',
                          '15%',
                          '15%',
                          '15%',
                          '15%'
                        ]"
                      >
                        <tbody>
                          <tr
                            class="tr-child"
                            *ngFor="let data of innerTable.data"
                          >
                            <td class="required email-col">
                              <input
                                class="choose"
                                nz-input
                                type="text"
                                [ngClass]="{ emailClass: data?.codeClass }"
                                [(ngModel)]="data.code"
                                maxlength="50"
                                [disabled]="data.disabled"
                              />
                            </td>
                            <td class="required name-col">
                              <input
                                class="choose"
                                [disabled]="Projectstatus == 'checked'"
                                [ngClass]="{ firstClass: data?.firstClass }"
                                nz-input
                                type="text"
                                [(ngModel)]="data.firstName"
                              />
                            </td>
                            <td class="required email-col">
                              <input
                                class="choose"
                                [ngClass]="{ emailClass: data?.emailClass }"
                                nz-input
                                type="email"
                                [(ngModel)]="data.email"
                              />
                            </td>
                            <td class="phone-col">
                              <input
                                type="text"
                                [disabled]="Projectstatus == 'checked'"
                                nz-input
                                [(ngModel)]="data.phone"
                                maxlength="11"
                              />
                            </td>
                            <td class="required role-col">
                              <ng-container
                                *ngIf="!!data.isAuditPerson; else roleCol"
                              >
                                <input
                                  nz-input
                                  [(ngModel)]="isAuditPersonText"
                                  [disabled]="true"
                                  style="padding-left: 16px;"
                                />
                              </ng-container>
                              <ng-template #roleCol>
                                <nz-select
                                  class="choose"
                                  [(ngModel)]="data.roleId"
                                  [nzDisabled]="Projectstatus == 'checked'"
                                  placeholder="请选择"
                                  [ngClass]="{ roleIdClass: data?.roleIdClass }"
                                  (ngModelChange)="ngModelChange()"
                                >
                                  <nz-option
                                    nzValue=""
                                    nzLabel="请选择"
                                  ></nz-option>
                                  <nz-option
                                    [nzValue]="option.id"
                                    [nzLabel]="option.name?.zh_CN"
                                    [nzDisabled]="option.disabled"
                                    *ngFor="let option of rolelist"
                                  >
                                  </nz-option>
                                </nz-select>
                              </ng-template>
                            </td>
                            <td
                              class="weight-col"
                              [ngClass]="{ required: !data.isAuditPerson }"
                            >
                              <ng-container
                                *ngIf="!!data.isAuditPerson; else weightCol"
                              >
                                <nz-input-number
                                  [(ngModel)]="data.weights"
                                  [disabled]="true"
                                  [nzPrecision]="precision"
                                  nzMin="0"
                                  nzMax="100"
                                  class="input-step"
                                  style="margin-left: 12px;"
                                >
                                </nz-input-number>
                              </ng-container>
                              <ng-template #weightCol>
                                <nz-input-number
                                  [(ngModel)]="data.weights"
                                  [disabled]="Projectstatus == 'checked'"
                                  [nzPrecision]="precision"
                                  nzMin="0"
                                  nzMax="100"
                                  class="input-step"
                                >
                                </nz-input-number>
                              </ng-template>
                            </td>
                            <td class="self-col" nzAlign="right">
                              <div
                                *ngIf="
                                  (action !== 'edit' || backtype == 'create') &&
                                  Projectstatus !== 'checked' &&
                                  !data.isAuditPerson
                                "
                                class="btn-delete"
                                (click)="
                                  deleteChild(innerTable.data, data.key, i)
                                "
                              >
                                删除
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </nz-table>
                    </td>
                  </tr>
                </ng-template>
              </tbody>
            </nz-table>
          </div>
          <nz-pagination
            *ngIf="PageTotal == 0"
            [nzHideOnSinglePage]="true"
            [(nzPageSize)]="PageSize"
            [nzPageIndex]="pageIndex"
            [nzTotal]="
              searchShow ? uploadSearchList.length : listOfParentData.length
            "
            (nzPageIndexChange)="pageIndexChange($event)"
          ></nz-pagination>
        </div>
        <ng-template #elseBlock>
          <div class="no-data-warp" style="background-color:#fff">
            <img src="./assets/images/no_observer_data.png" />
            <p *ngIf="keyWord === ''">
              暂无评估人，请点击左上角新增评估人吧～，如有大量评估人，建议点击右上角的批量导入。
            </p>
            <p *ngIf="keyWord !== ''">您搜索的人员不存在</p>
          </div>
        </ng-template>
      </ng-container>

      <ng-template #sendEmail>
        <div class="list">
          <div class="observer" *ngFor="let parentData of listOfParentData">
            <nz-table
              #nestedTable
              [nzData]="[parentData]"
              [nzShowPagination]="false"
              [nzScroll]="{ x: '980px' }"
              [nzWidthConfig]="['12%', '15%', '15%', '15%', '15%', '15%']"
            >
              <thead>
                <tr>
                  <th>人员编码</th>
                  <th>姓名</th>
                  <th>邮箱地址</th>
                  <th>手机号码</th>
                  <th>角色</th>
                  <th>占比</th>
                  <!-- <th>权重</th> -->
                </tr>
              </thead>
              <tbody>
                <ng-template ngFor let-data [ngForOf]="nestedTable.data">
                  <tr class="tr-parent">
                    <td>
                      <input
                        type="text"
                        [(ngModel)]="data.code"
                        maxlength="50"
                      />
                    </td>
                    <td>
                      <input
                        nz-input
                        type="text"
                        [disabled]="true"
                        [(ngModel)]="data.name"
                      />
                    </td>
                    <td><input type="email" [(ngModel)]="data.email" /></td>
                    <td>
                      <input
                        nz-input
                        type="text"
                        [disabled]="true"
                        [(ngModel)]="data.phone"
                        maxlength="11"
                      />
                    </td>
                    <td>
                      <nz-select
                        [(ngModel)]="data.roleId"
                        placeholder="请选择"
                        [nzDisabled]="true"
                      >
                        <nz-option nzValue="" nzLabel="请选择"></nz-option>
                        <nz-option
                          [nzValue]="option.id"
                          [nzLabel]="option.name?.zh_CN"
                          *ngFor="let option of rolelist"
                        >
                        </nz-option>
                      </nz-select>
                    </td>
                    <td>
                      <input
                        nz-input
                        type="number"
                        [disabled]="true"
                        [(ngModel)]="data.weights"
                        step="1"
                        class="input-step"
                      />
                    </td>
                  </tr>
                  <tr [nzExpand]="data.expand" *ngIf="data.persons.length">
                    <td colspan="7" style="padding: 16px;">
                      <nz-table
                        #innerTable
                        [nzData]="data.persons"
                        [nzShowPagination]="false"
                        [nzWidthConfig]="[
                          '12%',
                          '15%',
                          '15%',
                          '15%',
                          '15%',
                          '15%'
                        ]"
                      >
                        <tbody>
                          <tr
                            class="tr-child"
                            *ngFor="let data of innerTable.data"
                          >
                            <td>
                              <input
                                type="text"
                                [(ngModel)]="data.code"
                                maxlength="50"
                              />
                            </td>
                            <td>
                              <input type="text" [(ngModel)]="data.firstName" />
                            </td>
                            <td>
                              <input type="email" [(ngModel)]="data.email" />
                            </td>
                            <td>
                              <input
                                type="text"
                                [(ngModel)]="data.phone"
                                maxlength="11"
                              />
                            </td>
                            <td>
                              <nz-select
                                [(ngModel)]="data.roleId"
                                placeholder="请选择"
                              >
                                <nz-option
                                  nzValue=""
                                  nzLabel="请选择"
                                ></nz-option>
                                <nz-option
                                  [nzValue]="option.id"
                                  [nzLabel]="option.name?.zh_CN"
                                  *ngFor="let option of rolelist"
                                >
                                </nz-option>
                              </nz-select>
                            </td>
                            <td>
                              <nz-input-number
                                [(ngModel)]="data.weights"
                                nzMin="1"
                                nzMax="10"
                                nzStep="1"
                                [nzPrecision]="0"
                                class="input-step"
                              >
                              </nz-input-number>
                            </td>
                            <!-- <td>{{ 1/(innerTable.data.length+1) | percent }}</td> RNG-->
                          </tr>
                        </tbody>
                      </nz-table>
                    </td>
                  </tr>
                </ng-template>
              </tbody>
            </nz-table>
          </div>
        </div>
        <ng-template #elseBlock2>
          <div class="no-data-warp">
            <img src="./assets/images/no_observer_data.png" />
            <p>
              暂无评估人，请点击左上角新增评估人吧～，如有大量评估人，建议点击右上角的批量导入。
            </p>
          </div>
        </ng-template>
      </ng-template>

      <nz-pagination
        *ngIf="PageTotal != 0"
        [(nzPageIndex)]="CurrentPage"
        [(nzPageSize)]="PageSize"
        [(nzTotal)]="PageTotal"
        (nzPageIndexChange)="nzPageIndexChange($event)"
      ></nz-pagination>
      <!-- <nz-pagination [(nzPageIndex)]="CurrentPage" [(nzTotal)]="PageTotal" nzShowSizeChanger  (nzPageIndexChange)="nzPageIndexChange()"></nz-pagination> -->
    </div>

    <div [hidden]="step == 1" class="xy_invite">
      <ul
        class="checked_name"
        *ngIf="multipleValue.length != 0 && tabsetIndex == 1 && showcCode !== 2"
      >
        <li>
          <p>已合并的活动</p>
          <p style="font-size: 12px;color: #FF4F40;" *ngIf="showprojectcard">
            (点击下方活动名称，插入链接/二维码)
          </p>
        </li>
        <li class="second_li scroll">
          <ng-container *ngFor="let item of listSimilar">
            <div class="list_d" *ngIf="item.checked">
              <div></div>
              <!-- 邮件-富文本 -->
              <ng-container *ngIf="showcCode === 1">
                <span
                  (click)="
                    getPushWord(
                      showprojectcard,
                      showprojectcard ? true : false,
                      item.id,
                      item.projectName.zh_CN
                    )
                  "
                  >{{ item.projectName.zh_CN }}</span
                >
              </ng-container>
              <!-- 三方-文本域 -->
              <ng-container *ngIf="showcCode !== 1">
                <span
                  (click)="
                    getPushWordTextarea(
                      showprojectcard,
                      showprojectcard ? true : false,
                      item.id,
                      item.projectName.zh_CN
                    )
                  "
                  >{{ item.projectName.zh_CN }}</span
                >
              </ng-container>
            </div>
          </ng-container>
          <div class="list_d">
            <div></div>
            <!-- 邮件-富文本 -->
            <ng-container *ngIf="showcCode === 1">
              <span
                (click)="
                  getPushWord(showprojectcard, true, projectId, projectname)
                "
                >本活动</span
              >
            </ng-container>
            <!-- 三方-文本域 -->
            <ng-container *ngIf="showcCode !== 1">
              <span
                (click)="
                  getPushWordTextarea(
                    showprojectcard,
                    true,
                    projectId,
                    projectname
                  )
                "
                >本活动</span
              >
            </ng-container>
          </div>
        </li>
      </ul>
      <div>
        <nz-tabset
          (nzSelectedIndexChange)="nzSelectedIndexChange($event)"
          style="width: 100%;"
        >
          <nz-tab nzTitle="分享邀请码">
            <div class="xy_contant">
              <ul class="xy_letitle">
                <li
                  class="xy_li"
                  (click)="getchange('code')"
                  [ngClass]="!showType ? '' : 'blueclass'"
                >
                  个人验证码
                </li>
                <li
                  class="xy_li"
                  (click)="getchange('onecode')"
                  [ngClass]="showType ? '' : 'blueclass'"
                >
                  一人一码
                </li>
              </ul>
              <div class="xy_rititle">
                <!-- 是否开启分享码遮罩（人口标签姓名隐藏） -->
                <!-- <ng-container *ngIf="isHideMask">
                    <app-hide-mask></app-hide-mask>
                </ng-container> -->
                <div
                  class="xy_pers"
                  style="max-width: 400px;"
                  *ngIf="!showType"
                >
                  <p style="margin-bottom: 16px;">邀请范围({{ codename }})</p>
                  <p style="font-size: 12px;">
                    请在邀请模板中填写填答人姓名，在下方输入姓名定位一人一码
                  </p>
                  <ul class="xy_ser_load">
                    <div id="moreqrcode" style="display: none;">
                      <qrcode
                        *ngFor="let item of downloadlist"
                        [margin]="0"
                        [qrdata]="item.inviteUrl"
                        [width]="160"
                        [errorCorrectionLevel]="'M'"
                        [elementType]="'img'"
                      >
                      </qrcode>
                    </div>
                  </ul>
                  <button
                    nz-button
                    nzType="primary"
                    nzGhost
                    nzShape="round"
                    class="download-btn"
                    (click)="moreload()"
                  >
                    批量下载
                  </button>

                  <div class="xy_two_sele">
                    <nz-select
                      class="xy_select"
                      nzShowSearch
                      nzServerSearch
                      nzPlaceHolder="请输入名字"
                      [(ngModel)]="selectedValue"
                      [nzShowArrow]="false"
                      [nzFilterOption]="nzFilterOption"
                      (nzOnSearch)="searchrolename($event)"
                      (ngModelChange)="carousel.goTo(selectedValue)"
                    >
                      <!-- -->
                      <nz-option
                        *ngFor="let o of Investigatorsoption; let i = index"
                        [nzLabel]="o.firstName"
                        [nzValue]="o.indexId"
                      >
                      </nz-option>
                    </nz-select>
                  </div>
                </div>
                <!-- 验证码开始 -->
                <div *ngIf="showType" style="max-width: 400px;">
                  <ul class="invite-mid">
                    <li>
                      <h3 style="margin-bottom: 16px;">
                        邀请范围({{ showType ? "个人验证码" : "" }})
                      </h3>
                      <p style="font-size: 12px;">
                        请在邀请模板中填写填答人身份的个人验证码，进入公共链接后，输入个人验证码快速定位填答人身份
                      </p>
                      <div class="invite-user">
                        <nz-radio-group [(ngModel)]="formData.userType">
                          <label
                            nz-radio
                            [nzValue]="'all'"
                            (click)="changeSendUserType('all')"
                            >全部</label
                          >
                          <label
                            nz-radio
                            [nzValue]="'specified'"
                            (click)="changeSendUserType('specified')"
                            >指定人</label
                          >
                        </nz-radio-group>
                      </div>
                    </li>
                  </ul>
                  <div id="text" class="code_name">
                    <li *ngFor="let item of formData.emailList">
                      {{ item.firstName }}({{ item.captcha }})
                    </li>
                  </div>
                  <ul class="xy_co_bottom">
                    <button
                      nz-button
                      nzType="primary"
                      nzGhost
                      nzShape="round"
                      class="copy-btn"
                      (click)="doCopy()"
                    >
                      复制
                    </button>
                    <!-- <li class="xy_local" (click)="downloadcode()">下载到本地1</li> -->
                  </ul>
                </div>
                <!-- 验证码结束 -->
                <div class="xy_code">
                  <p>邀请链接({{ codename }})</p>

                  <!-- 一人一码 -->
                  <div class="xy_area_put" *ngIf="!showType">
                    <input
                      class="xy_area copytext"
                      nz-input
                      [(ngModel)]="invitecode_one"
                    />
                    <button
                      nz-button
                      nzType="primary"
                      nzGhost
                      nzShape="round"
                      class="copy-btn xy_btn"
                      (click)="doCopyurl()"
                    >
                      复制
                    </button>
                  </div>
                  <!-- 验证码 -->
                  <div class="xy_area_put" *ngIf="showType">
                    <input
                      class="xy_area copytext"
                      nz-input
                      [(ngModel)]="invitecode"
                    />
                    <button
                      nz-button
                      nzType="primary"
                      nzGhost
                      nzShape="round"
                      class="copy-btn xy_btn"
                      (click)="doCopyurl()"
                    >
                      复制
                    </button>
                  </div>

                  <p class="xy_int_code">邀请码({{ codename }})</p>
                  <ul class="xy_into">
                    <qrcode
                      *ngIf="!showType && invitecode_one"
                      class="invitecode_one"
                      [margin]="0"
                      [qrdata]="invitecode_one"
                      [width]="160"
                      [errorCorrectionLevel]="'M'"
                      [elementType]="'img'"
                    >
                    </qrcode>
                    <qrcode
                      *ngIf="showType && invitecode"
                      [margin]="0"
                      [qrdata]="invitecode"
                      [width]="160"
                      [errorCorrectionLevel]="'M'"
                      [elementType]="'img'"
                    >
                    </qrcode>
                    <li class="into_right">
                      <div class="right_top" [hidden]="showType">
                        <div (click)="carousel.pre()">
                          <img
                            class="top_img"
                            src="./assets//images//360_left.png"
                            alt=""
                          />
                        </div>
                        <div class="top_text">
                          <nz-carousel
                            ref-divElem
                            #carousel
                            [nzEffect]="effect"
                            [nzDots]="false"
                            (nzBeforeChange)="nzBeforeChange($event)"
                            (nzAfterChange)="nzAfterChange($event)"
                          >
                            <div
                              nz-carousel-content
                              class="xy_name"
                              *ngFor="let item of Investigators"
                            >
                              <h3>{{ item.firstName }}</h3>
                            </div>
                          </nz-carousel>
                        </div>
                        <div (click)="carousel.next()">
                          <img
                            class="top_img"
                            src="./assets//images//360_right.png"
                            alt=""
                          />
                        </div>
                      </div>
                      <div class="into_right" [hidden]="!showType"></div>
                      <div class="right_bottom" style="margin-top: 30px;">
                        <button
                          nz-button
                          nzType="primary"
                          nzGhost
                          nzShape="round"
                          class="download-btn"
                          (click)="downloadImg()"
                        >
                          下载到本地
                        </button>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </nz-tab>

          <nz-tab [nzTitle]="'发送邀请'" class="xy_nztab">
            <ng-container
              *ngIf="
                isDisplayMail ||
                  isDisplaySMS ||
                  isEnableWxWorkMsg ||
                  isEnableDingMsg ||
                  isEnableFeishuMsg;
                else noInvite
              "
            >
              <div class="xy_contant">
                <ul class="xy_letitle">
                  <li
                    class="xy_li"
                    (click)="getmessage('email')"
                    *ngIf="isDisplayMail"
                    [ngClass]="showcCode == 1 ? 'blueclass' : ''"
                  >
                    发送邮件
                  </li>
                  <li
                    class="xy_li"
                    (click)="getmessage('message')"
                    *ngIf="isDisplaySMS"
                    [ngClass]="showcCode == 2 ? 'blueclass' : ''"
                  >
                    短信邀请
                  </li>
                  <li
                    class="xy_li"
                    *ngIf="isEnableWxWorkMsg"
                    (click)="getmessage('vchart')"
                    [ngClass]="showcCode == 3 ? 'blueclass' : ''"
                  >
                    企业微信
                  </li>
                  <li
                    class="xy_li"
                    *ngIf="isEnableDingMsg"
                    (click)="getmessage('dingcode')"
                    [ngClass]="showcCode == 4 ? 'blueclass' : ''"
                  >
                    钉钉
                  </li>
                  <li
                    class="xy_li"
                    *ngIf="isEnableFeishuMsg"
                    (click)="getmessage('fscode')"
                    [ngClass]="showcCode == 5 ? 'blueclass' : ''"
                  >
                    飞书
                  </li>
                </ul>
                <div class="xy_rititle">
                  <div style="display: flex; flex-direction: column;">
                    <ul class="invite-mid">
                      <li>
                        <h3>邀请范围</h3>
                        <div class="invite-user">
                          <nz-radio-group [(ngModel)]="formData.userType">
                            <label
                              nz-radio
                              [nzValue]="'all'"
                              (click)="changeSendUserType('all')"
                              >全部</label
                            >
                            <label
                              nz-radio
                              [nzValue]="'specified'"
                              (click)="changeSendUserType('specified')"
                              >指定</label
                            >
                          </nz-radio-group>
                        </div>
                      </li>
                    </ul>
                    <div
                      class="label"
                      style="margin-top: 16px;position: relative;"
                    >
                      <span *ngIf="showcCode == 1">收件人邮箱</span>
                      <span *ngIf="showcCode == 2">收件人电话</span>
                      <span *ngIf="showcCode == 3">企业微信号</span>
                      <span *ngIf="showcCode == 4">钉钉用户账号</span>
                      <span *ngIf="showcCode == 5">飞书用户账号</span>
                    </div>
                    <div
                      class="code_name"
                      *ngIf="showcCode == 1"
                      style="flex: 1 0 0;"
                    >
                      <li *ngFor="let item of formData.emailList">
                        {{ item.firstName }}({{ item.email }})
                      </li>
                    </div>
                    <div
                      class="code_name"
                      *ngIf="showcCode == 2"
                      style="flex: 1 0 0;"
                    >
                      <li *ngFor="let item of formData.emailList">
                        {{ item.firstName }}({{ item.phone }})
                      </li>
                    </div>
                    <div
                      class="code_name"
                      *ngIf="showcCode == 3"
                      style="flex: 1 0 0;"
                    >
                      <li *ngFor="let item of formData.emailList">
                        {{ item.firstName }}({{ item.wxWorkUserId }})
                      </li>
                    </div>
                    <div
                      class="code_name"
                      *ngIf="showcCode == 4"
                      style="flex: 1 0 0;"
                    >
                      <li *ngFor="let item of formData.emailList">
                        {{ item.firstName }}({{ item.dingAccount }})
                      </li>
                    </div>
                    <div
                      class="code_name"
                      *ngIf="showcCode == 5"
                      style="flex: 1 0 0 ;"
                    >
                      <li *ngFor="let item of formData.emailList">
                        {{ item.firstName }}({{ item.feishuAccount }})
                      </li>
                    </div>
                    <p *ngIf="showcCode == 2" class="red-text mt-16">
                      0.1K米/50字符
                    </p>
                    <p *ngIf="showcCode === 1" class="red-text mt-16">
                      0.05K米/1M/封
                    </p>
                  </div>

                  <div class="xy_code">
                    <!-- 邀请方式 -->
                    <ul class="invite-mid">
                      <li>
                        <h3>邀请方式</h3>
                        <div class="invite-user">
                          <nz-radio-group [(ngModel)]="formData.codeType">
                            <label
                              nz-radio
                              [nzValue]="'one'"
                              (click)="changonecode('one')"
                              >一人一码</label
                            >
                            <label
                              nz-radio
                              [nzValue]="'prov'"
                              (click)="changonecode('prov')"
                              >验证码</label
                            >
                          </nz-radio-group>
                        </div>
                      </li>
                    </ul>
                    <!-- 多语言 -->
                    <div style="margin-bottom: 16px;">
                      <ng-container *ngIf="availableLanguages.length">
                        <app-i18n-select
                          [active]="lan"
                          (selectChange)="onChangeLan($event)"
                        ></app-i18n-select>
                      </ng-container>
                    </div>
                    <!-- 主题 -->
                    <div *ngIf="showcCode !== 2">
                      <div class="flex-between-center">
                        <h3>{{ templateTypeNameMap[showcCode] }}主题</h3>
                        <span
                          style="color: #FF4F40;"
                          *ngIf="[3, 4, 5].includes(showcCode)"
                          >建议{{ showcCode == 4 ? "15" : "30" }}个字符内</span
                        >
                      </div>
                      <input
                        class="title_email"
                        style="width: 100%;"
                        nz-input
                        name="title"
                        [placeholder]="
                          '请输入' + templateTypeNameMap[showcCode] + '主题'
                        "
                        [(ngModel)]="formData.title[lan]"
                      />
                    </div>
                    <!-- 正文 -->
                    <div>
                      <!-- 正文-title -->
                      <div class="flex-between-center mb-16">
                        <h3>{{ templateTypeNameMap[showcCode] }}正文</h3>
                        <div class="flex-between-center">
                          <!-- 自定义模板 -->
                          <button
                            nz-button
                            nzType="link"
                            (click)="plusAdd()"
                            *ngIf="permission || showcCode !== 2"
                          >
                            <i nz-icon type="plus-square"></i>自定义模板
                          </button>
                          <!-- 合并活动 -->
                          <ng-container *ngIf="showcCode !== 2">
                            <div style="position: relative;">
                              <button
                                nz-button
                                nzType="default"
                                nzShape="round"
                                (click)="getMerge()"
                              >
                                合并{{ templateTypeNameMap[showcCode] }}
                              </button>
                              <div class="position_cards" *ngIf="showcards">
                                <ul class="titile">
                                  <li>
                                    合并{{ templateTypeNameMap[showcCode] }}
                                  </li>
                                  <li>
                                    *请确保选中活动中的人员ID与当前活动一致
                                  </li>
                                </ul>
                                <ul class="sections">
                                  <li>请选择需要合并的活动</li>
                                  <li>
                                    <input
                                      class="input"
                                      nz-input
                                      [disabled]="true"
                                      placeholder="测评"
                                      [(ngModel)]="value"
                                    />
                                    <nz-select
                                      [nzMaxTagCount]="4"
                                      [nzMaxTagPlaceholder]="tagPlaceHolder"
                                      class="select"
                                      [(ngModel)]="multipleValue"
                                      [nzSize]="size"
                                      nzMode="multiple"
                                      nzPlaceHolder="请选择或者输入"
                                    >
                                      <nz-option
                                        *ngFor="let option of listSimilar"
                                        [nzLabel]="option.projectName.zh_CN"
                                        [nzValue]="option.id"
                                      ></nz-option>
                                    </nz-select>
                                    <ng-template
                                      #tagPlaceHolder
                                      let-multipleValue
                                      >展开查看其他{{
                                        multipleValue.length
                                      }}个选项</ng-template
                                    >
                                  </li>
                                </ul>
                                <ul class="button_ul">
                                  <button
                                    nz-button
                                    nzType="default"
                                    nzShape="round"
                                    (click)="MergeClear()"
                                  >
                                    清空
                                  </button>
                                  <button
                                    nz-button
                                    nzType="primary"
                                    nzShape="round"
                                    (click)="MergeSunmmit()"
                                  >
                                    确认
                                  </button>
                                </ul>
                              </div>
                            </div>
                          </ng-container>
                        </div>
                      </div>
                      <!-- 正文-富文本 -->
                      <ng-container *ngIf="showcCode === 1">
                        <div class="email_content">
                          <div style="flex: 1;">
                            <tinymce
                              [config]="tinyconfig"
                              id="formula-tinymce"
                              *ngIf="tabsetIndex === 1"
                              [(ngModel)]="formData.content[lan]"
                              delay="100"
                            >
                            </tinymce>
                            <ul class="email_dynamic" *ngIf="showcCode == 1">
                              <li
                                class="dynamic_li"
                                (click)="getPushWord('name', true)"
                              >
                                姓名
                              </li>
                              <li
                                class="dynamic_li"
                                (click)="getPushWord('start', true)"
                              >
                                开始日期
                              </li>
                              <li
                                class="dynamic_li"
                                (click)="getPushWord('end', true)"
                              >
                                截止日期
                              </li>
                              <!-- 合并-不开启 -->
                              <ng-container *ngIf="!ismergeproject">
                                <li
                                  class="dynamic_li"
                                  (click)="getPushWord('url', true)"
                                >
                                  登陆链接
                                </li>
                                <li
                                  *ngIf="showcCode == 1"
                                  class="dynamic_li"
                                  (click)="getPushWord('qrcode', true)"
                                >
                                  二维码
                                </li>

                                <li
                                  class="dynamic_li"
                                  [ngClass]="codemenu ? '' : 'code_li'"
                                  (click)="getPushWord('code', codemenu)"
                                >
                                  验证码
                                </li>
                              </ng-container>
                              <!-- 合并-开启 -->
                              <ng-container *ngIf="ismergeproject">
                                <li
                                  class="dynamic_li"
                                  [ngClass]="
                                    showprojectcard == 'urls' ? 'blue_li' : ''
                                  "
                                  (click)="getShowcards('urls')"
                                >
                                  登陆链接
                                </li>
                                <li
                                  *ngIf="showcCode == 1"
                                  class="dynamic_li"
                                  [ngClass]="
                                    showprojectcard == 'qrcode' ? 'blue_li' : ''
                                  "
                                  (click)="getShowcards('qrcode')"
                                >
                                  二维码
                                </li>
                                <li class="dynamic_li code_li">
                                  验证码
                                </li>
                              </ng-container>
                            </ul>
                            <li
                              style="font-size: 12px;padding-top: 10px;display: flex; align-items: center; justify-content: space-between;"
                            >
                              *选择上方动态按钮，添加到{{
                                templateTypeNameMap[showcCode]
                              }}正文中

                              <p *ngIf="showcCode == 1" class="red-text-right">
                                {{ mailTotal.mailStrNum }} M
                              </p>
                            </li>
                            <nz-upload
                              [nzPreview]="preview"
                              [nzCustomRequest]="uploadAttachment"
                              (nzChange)="fileChange($event)"
                              [nzFileList]="fileListAttachment"
                            >
                              <button
                                style="margin-top: 10px;"
                                class="upload_button"
                                nz-button
                              >
                                <span>上传附件</span>
                              </button>
                            </nz-upload>
                          </div>
                        </div>
                      </ng-container>
                      <!-- 正文-文本域 -->
                      <ng-container *ngIf="showcCode !== 1">
                        <!-- 只读 -->
                        <ng-container *ngIf="!permission && showcCode == 2">
                          <div class="code_name" style="min-height:375px;">
                            <div
                              style="height: 100%;color: #000;padding:10px 20px;"
                            >
                              {{ showcCode === 2 ? messagePrefix[lan] : "" }}
                              {{ formData.content[lan] }}
                              {{ showcCode === 2 ? messageSuffix[lan] : "" }}
                            </div>
                          </div>
                        </ng-container>
                        <!-- 可编辑 -->
                        <div class="email_content">
                          <div style="flex: 1;">
                            <ng-container *ngIf="permission || showcCode !== 2">
                              <div
                                style="margin-bottom: 4px;"
                                *ngIf="showcCode === 2"
                              >
                                <nz-tag nz-tooltip nzTitle="短信-固定前缀">{{
                                  messagePrefix[lan]
                                }}</nz-tag>
                              </div>
                              <textarea
                                id="formula-textarea"
                                (change)="nzChangetextarea()"
                                (input)="nzChangetextarea()"
                                rows="12"
                                nz-input
                                [(ngModel)]="formData.content[lan]"
                              ></textarea>
                              <div
                                style="margin-top: 4px;"
                                *ngIf="showcCode === 2"
                              >
                                <nz-tag nz-tooltip nzTitle="短信-固定后缀">{{
                                  messageSuffix[lan]
                                }}</nz-tag>
                              </div>
                            </ng-container>
                            <div *ngIf="permission || showcCode !== 2">
                              <ul class="email_dynamic">
                                <li
                                  class="dynamic_li"
                                  (click)="getPushWordTextarea('name', true)"
                                >
                                  姓名
                                </li>
                                <li
                                  class="dynamic_li"
                                  (click)="getPushWordTextarea('start', true)"
                                >
                                  开始日期
                                </li>
                                <li
                                  class="dynamic_li"
                                  (click)="getPushWordTextarea('end', true)"
                                >
                                  截止日期
                                </li>

                                <!-- 合并-不开启 -->
                                <ng-container *ngIf="!ismergeproject">
                                  <li
                                    class="dynamic_li"
                                    (click)="getPushWordTextarea('url', true)"
                                  >
                                    登陆链接
                                  </li>
                                  <li
                                    class="dynamic_li"
                                    *ngIf="showcCode == 1"
                                    (click)="
                                      getPushWordTextarea('qrcode', true)
                                    "
                                  >
                                    二维码
                                  </li>

                                  <li
                                    class="dynamic_li"
                                    [ngClass]="codemenu ? '' : 'code_li'"
                                    (click)="
                                      getPushWordTextarea('code', codemenu)
                                    "
                                  >
                                    验证码
                                  </li>
                                </ng-container>
                                <!-- 合并-开启 -->
                                <ng-container *ngIf="ismergeproject">
                                  <li
                                    class="dynamic_li"
                                    [ngClass]="
                                      showprojectcard == 'urls' ? 'blue_li' : ''
                                    "
                                    *ngIf="showcCode !== 2"
                                    (click)="getShowcards('urls')"
                                  >
                                    登陆链接
                                  </li>
                                  <li
                                    class="dynamic_li"
                                    *ngIf="showcCode === 2"
                                    (click)="getPushWordTextarea('url', true)"
                                  >
                                    登陆链接
                                  </li>
                                  <li
                                    class="dynamic_li"
                                    *ngIf="showcCode == 1"
                                    [ngClass]="
                                      showprojectcard == 'qrcode'
                                        ? 'blue_li'
                                        : ''
                                    "
                                    (click)="getShowcards('qrcode')"
                                  >
                                    二维码
                                  </li>
                                  <li class="dynamic_li code_li">
                                    验证码
                                  </li>
                                </ng-container>
                              </ul>
                            </div>
                          </div>
                        </div>
                        <div class="flex-between-center">
                          <span style="font-size: 12px">
                            <span *ngIf="permission || showcCode !== 2"
                              >*选择上方动态按钮，添加到{{
                                templateTypeNameMap[showcCode]
                              }}正文中</span
                            ></span
                          >
                          <span class="red-text" *ngIf="showcCode == 2">
                            {{ strNum }} 字符</span
                          >
                        </div>
                      </ng-container>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-template #noInvite>
              <div class="no-invite-empty">
                <app-empty text="暂无内容"></app-empty>
              </div>
            </ng-template>
          </nz-tab>
        </nz-tabset>
      </div>
    </div>
  </div>

  <footer class="invite-footer">
    <div class="container client-width" [ngSwitch]="action">
      <ng-container *ngSwitchCase="'edit'">
        <button
          *ngIf="nextAfterSave; else block"
          class="next_button"
          (click)="afterSaveToStep2()"
        >
          下一步
        </button>
        <ng-template #block>
          <button class="next_button" (click)="saveSurvey()">保存</button>
          <!-- tes -->
        </ng-template>
      </ng-container>

      <ng-container *ngSwitchCase="'reSendEmail'">
        <button [hidden]="step == 1" class="next_button" (click)="nextStep(2)">
          发送
        </button>
      </ng-container>

      <ng-container *ngSwitchDefault>
        <button
          nz-button
          [hidden]="step == 2"
          [(nzLoading)]="isLoadingOne"
          class="next_button"
          (click)="saveInfo()"
        >
          保存
        </button>

        <div *ngIf="tabsetIndex == 1" class="flex-between-center">
          <div *ngIf="showcCode == 2 || showcCode == 1">
            K米将自动从您的账户中扣除
          </div>
          <div *ngIf="[3, 4, 5].includes(showcCode)"></div>
          <div>
            <span *ngIf="showcCode == 2"
              ><span style="color: #FF4F40;font-size: 20px; margin-right: 10px;"
                >{{ singleYuan + "k/条" }} * {{ peopleNum + "人" }}</span
              >共计：<span style="color: #FF4F40;font-size: 20px;">{{
                countMoney
              }}</span
              >K米</span
            ><span *ngIf="showcCode === 1"
              ><span style="color: #FF4F40;font-size: 20px; margin-right: 10px;"
                >{{ "0.05k/封" }} *{{ mailTotal.mailStrNum + "M" }}*
                {{ mailTotal.peopleNum + "人" }}</span
              >共计：<span style="color: #FF4F40;font-size: 20px;">{{
                mailTotal.countMoney
              }}</span
              >K米</span
            >

            <ng-container
              *ngIf="
                isDisplayMail ||
                isDisplaySMS ||
                isEnableWxWorkMsg ||
                isEnableDingMsg ||
                isEnableFeishuMsg
              "
            >
              <button
                [hidden]="step == 1"
                class="next_button"
                nz-button
                [nzLoading]="isLoadingspend"
                (click)="nextStep(2)"
                style="margin-left: 40px;"
              >
                发送
              </button>
            </ng-container>
          </div>
        </div>
      </ng-container>
    </div>
  </footer>

  <nz-drawer
    [(nzVisible)]="addtyceshow"
    nzWidth="960px"
    (nzOnClose)="addtyceCancel()"
    nzWrapClassName="custom-right-drawer-custom"
  >
    <ng-container>
      <!-- 顶部 -->
      <ul class="top_ul">
        <li class="titile_add">
          <p class="left_p">自定义模版</p>
          <p class="right_p">
            *请选择需要发送的{{ templateTypeNameMap[showcCode] }}模版
          </p>
        </li>
      </ul>
      <!-- 内容 -->
      <ul class="body_contant">
        <!-- 左侧 -->
        <li class="left_body">
          <div class="add-template">
            <button
              nz-button
              nzType="primary"
              nzGhost
              (click)="addDefaultemail()"
              nzBlock
            >
              <i nz-icon nzType="plus-circle" nzTheme="outline"></i>添加模版
            </button>
          </div>
          <div class="template-list scroll">
            <ng-container *ngIf="eamilTemplate.length">
              <nz-radio-group
                [(ngModel)]="radioValue"
                style="display: flex;flex-direction: column;"
                (ngModelChange)="chooseemail()"
              >
                <div
                  *ngFor="let item of eamilTemplate; let i = index"
                  style="padding: 20px;border-bottom: 1px solid #E6E6E6;"
                >
                  <div
                    class="little_div"
                    style="display: flex;align-items: center;"
                  >
                    <label
                      nz-radio
                      [(nzValue)]="item.valueId"
                      style="display: flex;align-items: center;"
                      (click)="radioChange()"
                    >
                      <p class="white_space">
                        <span nz-tooltip [(nzTooltipTitle)]="item.name">{{
                          item.name
                        }}</span>
                      </p>
                      <span class="default" *ngIf="item.isDefault">默认</span>
                      <span>创建时间：{{ item.createTime.slice(0, 10) }}</span>
                    </label>
                    <a
                      nz-popconfirm
                      nzPopconfirmTitle="您确定要删除这条记录吗？"
                      nzPopconfirmPlacement="bottom"
                      (nzOnConfirm)="deleteEmail(i)"
                      (nzOnCancel)="cancel()"
                    >
                      <i
                        *ngIf="!item.isStandard"
                        nz-icon
                        class="delete_icon"
                        nzType="delete"
                        nzTheme="outline"
                      ></i>
                    </a>
                  </div>
                </div>
              </nz-radio-group>
            </ng-container>
          </div>
        </li>
        <!-- 右侧 -->
        <li class="right_body">
          <div style="margin-bottom: 16px;">
            <app-i18n-select
              [active]="lan"
              (selectChange)="onChangeLan($event)"
            ></app-i18n-select>
          </div>
          <div class="title_top mb-10">
            <div><span style="color: #FF4F40;">*</span> 模板名称</div>
            <span>
              <label
                (ngModelChange)="setcustom()"
                nz-checkbox
                [(ngModel)]="chooseformData.isDefault"
                >设为默认</label
              >
            </span>
          </div>
          <input
            style="margin:10px 0;"
            nz-input
            placeholder="请输入模版名称"
            [(ngModel)]="chooseformData.name"
            [(disabled)]="chooseformData.editors"
          />
          <ng-container *ngIf="showcCode !== 2">
            <div class="title_top mt-16 mb-10">
              <div>
                <span *ngIf="lan === 'zh_CN'" style="color: #FF4F40;">*</span>
                {{ templateTypeNameMap[showcCode] }}主题
              </div>
            </div>
            <input
              nz-input
              [placeholder]="'请输入' + templateTypeNameMap[showcCode] + '主题'"
              placeholder="请输入邮件主题"
              [(ngModel)]="chooseformData.theme[lan]"
              [(disabled)]="chooseformData.editors"
            />
          </ng-container>
          <div class="mt-16 flex-between-center">
            <div>
              <span *ngIf="lan === 'zh_CN'" style="color: #FF4F40;">*</span>
              {{ templateTypeNameMap[showcCode] }}正文
            </div>
            <span
              style="cursor: pointer;color: #409EFF;"
              *ngIf="chooseformData.isStandard"
              (click)="getdefaultcontant()"
              >恢复默认</span
            >
          </div>
          <ng-container *ngIf="showcCode === 1">
            <div class="mt-10" style="position: relative;">
              <tinymce
                [config]="tinyconfig"
                id="formula-tinymce-template"
                *ngIf="addtyceshow"
                [(ngModel)]="chooseformData.content[lan]"
                delay="100"
              >
              </tinymce>
              <div
                *ngIf="chooseformData.editors"
                style="position: absolute;left: 0;top: 0;right: -4px;bottom: -4px;background-color:#F5F5F5 ;opacity: 0.6;"
              >
                <!--  -->
              </div>
            </div>
          </ng-container>

          <ng-container *ngIf="showcCode !== 1">
            <div class="mt-10">
              <div style="margin-bottom: 4px;" *ngIf="showcCode === 2">
                <nz-tag nz-tooltip nzTitle="短信-固定前缀">{{
                  messagePrefix[lan]
                }}</nz-tag>
              </div>
              <textarea
                id="formula-textarea-template"
                rows="12"
                nz-input
                [(ngModel)]="chooseformData.content[lan]"
                [(disabled)]="chooseformData.editors"
              >
              </textarea>
              <div style="margin-top: 4px;" *ngIf="showcCode === 2">
                <nz-tag nz-tooltip nzTitle="短信-固定后缀">{{
                  messageSuffix[lan]
                }}</nz-tag>
              </div>
            </div>
          </ng-container>

          <ul class="xy_menus" *ngIf="showcCode == 1">
            <li class="menus_li" (click)="getPushWordTemplate('name', true)">
              姓名
            </li>
            <li class="menus_li" (click)="getPushWordTemplate('start', true)">
              开始日期
            </li>
            <li class="menus_li" (click)="getPushWordTemplate('end', true)">
              截止日期
            </li>
            <li class="menus_li" (click)="getPushWordTemplate('url', true)">
              登陆链接
            </li>
            <li
              class="menus_li"
              (click)="getPushWordTemplate('qrcode', true)"
              *ngIf="showcCode == 1"
            >
              二维码
            </li>
            <li
              class="menus_li"
              [ngClass]="codemenu ? '' : 'code_li'"
              (click)="getPushWordTemplate('code', codemenu)"
            >
              验证码
            </li>
          </ul>
          <!-- 文本域-增加动态值 -->
          <div class="xy_menus" *ngIf="showcCode !== 1">
            <li
              class="menus_li"
              (click)="getPushWordTextareaTemplate('name', true)"
            >
              姓名
            </li>
            <li
              class="menus_li"
              (click)="getPushWordTextareaTemplate('start', true)"
            >
              开始日期
            </li>
            <li
              class="menus_li"
              (click)="getPushWordTextareaTemplate('end', true)"
            >
              截止日期
            </li>
            <li
              class="menus_li"
              (click)="getPushWordTextareaTemplate('url', true)"
            >
              登陆链接
            </li>
            <li
              class="menus_li code_li"
              *ngIf="showcCode == 1"
              (click)="getPushWordTextareaTemplate('qrcode', true)"
            >
              二维码
            </li>
            <li
              class="menus_li"
              [ngClass]="codemenu ? '' : 'code_li'"
              (click)="getPushWordTextareaTemplate('code', codemenu)"
            >
              验证码
            </li>
          </div>
          <div
            style="margin-top: 16px;display: flex;justify-content: flex-end;"
          >
            <button nz-button nzType="default" (click)="editorInfolist()">
              打开编辑
            </button>
            <button nz-button nzType="primary" (click)="saveInfolist()">
              保存
            </button>
          </div>
        </li>
      </ul>
    </ng-container>
    <div class="footer">
      <button nz-button nzType="default" (click)="addtyceCancel()">取消</button>
      <button nz-button nzType="primary" (click)="addtyceOk()">确认</button>
    </div>
  </nz-drawer>
  <div class="all_fixed" *ngIf="showcards" (click)="getMerges()"></div>
</div>
<!-- 自邀请人数 -->
<app-self-invite
  [rolelist]="rolelist"
  [inviteSettings]="currentInviteSettings"
  (saveInviteSettings)="saveInviteSettings($event)"
></app-self-invite>
