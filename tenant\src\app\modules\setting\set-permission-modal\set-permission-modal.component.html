<!--
    *@author: <PERSON>
    *@Date: 2023/11/15
    *@content: 个人中心-设置权限
-->
<a (click)="setPermissions()">设置权限</a>
<!-- 设置权限侧边栏 -->
<nz-drawer
  [nzClosable]="true"
  [nzVisible]="visible"
  nzPlacement="right"
  nzTitle="设置权限"
  (nzOnClose)="close()"
  [nzWidth]="584"
  nzWrapClassName="permissions-modal"
>
  <div class="content">
    <!-- 账户列表 -->
    <div class="left">
      <div class="top">
        <!-- 账户列表-搜索 -->
        <div class="search">
          <nz-input-group [nzSuffix]="suffixIconSearch" style="height: 36px;">
            <input
              (keydown.enter)="loadUserList()"
              nz-input
              placeholder="请输入关键词"
              [(ngModel)]="searchUserName"
            />
          </nz-input-group>
          <ng-template #suffixIconSearch>
            <i nz-icon nzType="search"></i>
          </ng-template>
        </div>
        <div class="text">
          <span>账户列表</span>
        </div>
      </div>
      <ul class="scroll-box">
        <li
          *ngFor="let user of userList; let index = index"
          [ngClass]="clickUserId === user.id ? 'active' : ''"
          (click)="clickUser(user.id, index, user?.permission?.id)"
        >
          <span
            class="left-span"
            nz-tooltip
            [nzTitle]="user.username"
            nzPlacement="topLeft"
            >{{ user.username }}</span
          >
        </li>
      </ul>
    </div>
    <!-- 权限列表 -->
    <div class="right">
      <div class="top">
        <!-- 权限列表-搜索 -->
        <div class="search">
          <nz-input-group style="height: 36px;" [nzSuffix]="suffixIconSearch">
            <input
              nz-input
              (keydown.enter)="loadPermissionList()"
              placeholder="请输入关键词"
              [(ngModel)]="searchPermissionName"
            />
          </nz-input-group>
          <ng-template #suffixIconSearch>
            <i nz-icon nzType="search"></i>
          </ng-template>
        </div>
        <div class="text">
          <span>权限列表</span>
          <a (click)="show('add')">
            <i class="iconfont icon-plus-circle"></i>
            添加权限</a
          >
        </div>
      </div>
      <ul class="scroll-box">
        <li
          *ngFor="let per of permissionList"
          [ngClass]="clickPermissionId === per?.id ? 'active' : ''"
          (mouseenter)="hoverid = per?.id"
          (mouseleave)="hoverid = ''"
          (click)="clickPermission(per.id)"
        >
          <span
            class="right-span"
            nz-tooltip
            [nzTitle]="per.name.zh_CN"
            nzPlacement="topLeft"
            >{{ per.name.zh_CN }}</span
          >
          <div
            *ngIf="hoverid === per.id || clickPermissionId === per.id"
            class="handle-box"
          >
            <!-- 编辑 -->
            <i class="iconfont icon-icon_edit" (click)="show('edit', per)"></i>
            <!-- 删除 -->
            <i
              class="iconfont icon-icon_delete"
              nz-popconfirm
              nzPopconfirmTitle="是否删除当前权限以及其他账号绑定的此权限？"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="delPermission(per.id)"
              (nzOnCancel)="cancel()"
            ></i>
          </div>
        </li>
      </ul>
    </div>
    <!-- 添加权限-子侧边栏 -->
    <nz-drawer
      [nzClosable]="true"
      [nzVisible]="childrenVisible"
      nzPlacement="right"
      [nzTitle]="childrenTitle"
      (nzOnClose)="childrenClose()"
      [nzWidth]="584"
      nzWrapClassName="permissions-modal"
    >
      <ng-template #childrenTitle>
        <!-- 返回设置权限侧边 -->
        <a (click)="childrenClose()" class="back">
          <i class="iconfont icon-icon_arrow_left"></i>返回</a
        >
        <!-- title -->
        <span>{{
          permissionBoxStatus === "add" ? "添加权限" : "修改权限"
        }}</span>
      </ng-template>
      <div class="permission-tool">
        <!-- 权限名称 -->
        <div nz-row nzGutter="16" class="mb-8">
          <div nz-col nzSpan="3">
            <span class="label">权限名称</span>
          </div>
          <div nz-col nzSpan="21">
            <input
              nz-input
              style="height: 36px;"
              placeholder="请输入权限名称"
              [(ngModel)]="permissionObj.name.zh_CN"
            />
          </div>
        </div>
        <!-- 可创建的活动工具 -->
        <div class="title">可创建的活动工具</div>
        <!-- 标准产品 -->
        <div nz-row nzGutter="16" class="mb-8">
          <div nz-col nzSpan="3">
            <span class="label">标准产品</span>
          </div>
          <div nz-col nzSpan="21">
            <nz-select
              style="width: 100%;"
              (ngModelChange)="StanSelectModelChange($event)"
              [(ngModel)]="checkedStanProListList"
              nzMode="multiple"
              nzPlaceHolder="标准产品"
            >
              <nz-option nzCustomContent [nzValue]="'all'">
                <label
                  nz-checkbox
                  (ngModelChange)="StanAllChange($event)"
                  [(ngModel)]="stanAllChecked"
                  [nzIndeterminate]="stanIndeterminate"
                  >全选</label
                >
              </nz-option>
              <nz-option
                nzCustomContent
                *ngFor="let option of stanProListList"
                [nzLabel]="option.name.zh_CN"
                [nzValue]="option.id"
              >
                <label
                  nz-checkbox
                  [(ngModel)]="option.checked"
                  (ngModelChange)="StanModelChange(option.id)"
                >
                </label>
                {{ option.name.zh_CN }}
              </nz-option>
            </nz-select>
          </div>
        </div>
        <!-- 组合产品 -->
        <div nz-row nzGutter="16" class="mb-8">
          <div nz-col nzSpan="3">
            <span class="label">组合产品</span>
          </div>
          <div nz-col nzSpan="21">
            <nz-select
              style="width: 100%;"
              (ngModelChange)="andSelectModelChange($event)"
              [(ngModel)]="checkedAndProListList"
              nzMode="multiple"
              nzPlaceHolder="组合产品"
            >
              <nz-option nzCustomContent [nzValue]="'all'">
                <label
                  nz-checkbox
                  (ngModelChange)="andAllChange($event)"
                  [(ngModel)]="andAllChecked"
                  [nzIndeterminate]="andIndeterminate"
                  >全选</label
                >
              </nz-option>
              <nz-option
                nzCustomContent
                *ngFor="let option of andProListList"
                [nzLabel]="option.name.zh_CN"
                [nzValue]="option.id"
              >
                <label
                  nz-checkbox
                  [(ngModel)]="option.checked"
                  (ngModelChange)="andModelChange(option.id)"
                >
                </label>
                {{ option.name.zh_CN }}
              </nz-option>
            </nz-select>
          </div>
        </div>
        <!-- 专属产品 -->
        <div nz-row nzGutter="16" class="mb-8">
          <div nz-col nzSpan="3">
            <span class="label">专属产品</span>
          </div>
          <div nz-col nzSpan="21">
            <nz-select
              style="width: 100%;"
              (ngModelChange)="likeSelectModelChange($event)"
              [(ngModel)]="checkedLikeProListList"
              nzMode="multiple"
              nzPlaceHolder="专属产品"
            >
              <nz-option nzCustomContent [nzValue]="'all'">
                <label
                  nz-checkbox
                  (ngModelChange)="likeAllChange($event)"
                  [(ngModel)]="likeAllChecked"
                  [nzIndeterminate]="likeIndeterminate"
                  >全选</label
                >
              </nz-option>
              <nz-option
                nzCustomContent
                *ngFor="let option of likeProListList"
                [nzLabel]="option.name.zh_CN"
                [nzValue]="option.id"
              >
                <label
                  nz-checkbox
                  [(ngModel)]="option.checked"
                  (ngModelChange)="likeModelChange(option.id)"
                >
                </label>
                {{ option.name.zh_CN }}
              </nz-option>
            </nz-select>
          </div>
        </div>
        <!-- 快捷设置 -->
        <div class="title">快捷设置</div>
        <div nz-row nzGutter="16">
          <div nz-col nzSpan="24" class="switch">
            <nz-switch [(ngModel)]="permissionObj.onlyViewSelf"></nz-switch>
            <span>仅查看本人创建的活动及报告</span>
          </div>
        </div>
        <!-- 高级设置 -->
        <div class="title">高级设置</div>
        <p class="tip">
          开启后，以下权限将同步到【活动管理】模块中的所有活动，请选择具体某个活动进行细分权限设置以及子账户关联
        </p>
        <div nz-row nzGutter="16" class="mb-8 mt-8">
          <div nz-col nzSpan="10" class="switch">
            <nz-switch [(ngModel)]="permissionObj.addMasterManager"></nz-switch>
            <span>添加活动主管理员</span>
          </div>
          <div nz-col nzSpan="14" class="switch">
            <nz-switch
              [(ngModel)]="permissionObj.addSubManagerViewProgress"
            ></nz-switch>
            <span>添加子管理员查看进度</span>
          </div>
        </div>
        <div nz-row nzGutter="16" class="mb-8 mt-8">
          <div nz-col nzSpan="10" class="switch">
            <nz-switch
              [(ngModel)]="permissionObj.addSubManagerCreateReport"
            ></nz-switch>
            <span>添加子管理员生成报告</span>
          </div>
        </div>
      </div>
      <!-- 添加权限-子侧边栏-操作 -->
      <footer>
        <button nz-button nzType="default" (click)="clearPerParams(true)">
          清空
        </button>
        <button nz-button nzType="primary" (click)="editPermission()">
          确认
        </button>
      </footer>
    </nz-drawer>
  </div>
  <!-- 设置权限-侧边栏-操作 -->
  <footer>
    <button nz-button nzType="default" (click)="close()">取消</button>
    <button nz-button nzType="primary" (click)="save()">保存</button>
  </footer>
</nz-drawer>
