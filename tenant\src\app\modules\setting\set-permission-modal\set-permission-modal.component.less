/**
 *
 *  @author: <PERSON>
 *  @Date: 2023/11/15
 *  @content: 个人中心-设置权限
 *
*/
::ng-deep {
  // 外层侧边栏
  .permissions-modal {
    // 侧边栏-body
    .ant-drawer-body {
      padding: 0;
      // height: calc(100% - 55px);
      // padding-bottom: 66px;
      height: calc(100% - 108px);
      overflow: auto;
        scrollbar-color: auto;
  scrollbar-width: auto;
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      // 滑块背景
      &::-webkit-scrollbar-track {
        // background-color: transparent;
        background-color: #F1F1F1;
        box-shadow: none;
      }
      // 滑块
      &::-webkit-scrollbar-thumb {
        // background-color: #e9e9e9;
        background-color: #C1C1C1;
        outline: none;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
      }
    }
    // 侧边栏-header
    .ant-drawer-header {
      padding: 16px;
    }
    // 侧边栏-title
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    // 侧边栏-content
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
    // 设置权限
    .content {
      width: 100%;
      height: calc(100vh - 108px);
      display: flex;
      .left {
        width: 250px;
        border-right: 1px solid #e6e6e6;
      }
      .right {
        flex: 1;
      }
      .top {
        width: 100%;
        .search {
          padding: 16px;
        }
        .text {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: #f8f8f8;
          padding: 11px 16px;
          span {
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: bold;
            color: #262626;
            line-height: 22px;
          }
          a {
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #409eff;
            line-height: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            i {
              margin-right: 8px;
            }
          }
        }
      }
      ul.scroll-box {
        height: calc(100% - 112px);
        padding: 8px;
        overflow-y: auto;
        li {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          padding: 8px;
          margin: 8px;
          cursor: pointer;
          span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .left-span {
            max-width: 200px;
          }
          .right-span {
            max-width: 220px;
          }
          .handle-box {
            display: flex;
            align-items: center;
            i {
              font-size: 14px;
              margin: 0 8px;
            }
          }
        }
        li.active {
          color: #409eff;
          background: #f5f8ff;
          border-radius: 8px;
        }
        li:hover {
          color: #409eff;
          background: #f5f8ff;
          border-radius: 8px;
        }
      }
    }
    // 侧边栏-页脚/操作栏
    footer {
      position: absolute;
      bottom: 0px;
      width: 100%;
      border-top: 1px solid rgb(232, 232, 232);
      padding: 10px 16px;
      text-align: right;
      left: 0px;
      background: #fff;
    }
    // 子侧边栏-返回按钮
    .back {
      height: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #409eff;
      line-height: 20px;
      i {
        margin-right: 4px;
      }
      margin-right: 16px;
    }
    // 子侧边栏
    .permission-tool {
      width: 100%;
      padding: 16px;
      height: calc(100vh - 108px);
      overflow-y: auto;
      ::ng-deep {
        .ant-row {
          margin-left: 0 !important;
          margin-right: 0 !important;
        }
        .ant-col {
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
      }
      .mb-8 {
        margin-bottom: 8px;
      }
      .mt-8 {
        margin-top: 8px;
      }
      .title {
        height: 22px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: bold;
        color: #262626;
        line-height: 22px;
        margin: 16px 0;
      }
      .label {
        height: 36px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 36px;
      }
      .tip {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #bfbfbf;
        line-height: 17px;
        margin-bottom: 8px;
      }
      .switch {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        span {
          margin-left: 8px;
        }
      }
    }
  }
}
