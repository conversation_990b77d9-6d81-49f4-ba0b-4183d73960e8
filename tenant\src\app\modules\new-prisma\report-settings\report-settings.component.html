<!--
     *@author: <PERSON>
     *@Date: 2024/07/17
     *@content: 报告设置内容
 -->
<div class="content">
  <ng-container *ngFor="let item of reportList">
    <div
      class="list"
      [ngClass]="{
        'disabled-list': item.disabled,
        'show-list': !item.disabled
      }"
      *ngIf="permissionService.isPermissionOrSag(item.sag)"
    >
      <div class="list-top">
        <span class="title">{{ item.name }}</span>
        <span class="lab">{{ item.labelText }}</span>
        <span class="btn" (click)="handClick(item)">{{ item.btnText }}</span>
      </div>
      <div class="list-content">
        <h3 *ngIf="item.title">{{ item.title }}</h3>
        <div class="list-content-img" [ngClass]="{'h-full': !item.title}" [ngStyle]="item.imgExtraStyles">
          <img [src]="item.disabled ? item.img_dis : item.img" alt="" srcset="" />
        </div>
      </div>
    </div>
  </ng-container>
</div>
<div class="footer">
  <button nz-button nzType="default" (click)="ok()">关闭</button>
</div>
