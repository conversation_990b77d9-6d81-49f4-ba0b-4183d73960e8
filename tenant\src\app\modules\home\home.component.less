@text-color: #17314c;

.index {
  background: #fff;
}

.index-title {
  font-size: 24px;
  color: @text-color;
  margin: 39px 0 30px 0;
  font-weight: 300;
}

.user-status {
  display: flex;

  li {
    position: relative;
    margin-right: 30px;
    width: 380px;
    height: 167px;
    // overflow: hidden;
    cursor: pointer;
    border-radius: 8px;

    .img_report {
      position: absolute;
      top: 40px;
      right: 37px;
    }

    .arrows {
      position: absolute;
      bottom: 14px;
      right: 30px;
    }

    .activity_num {
      min-width: 90px;
      height: 29px;
      text-align: center;
      line-height: 29px;
      background: rgba(64, 158, 255, 0.0697);
      border-radius: 46px;
      font-size: 14px;
      color: #409eff;
      padding: 0 8px;
    }

    .new_pros {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 60px;
      height: 100%;
    }

    .progress_text {
      font-size: 14px;
      line-height: 20px;
      color: #495970;
      margin-top: 34px;
    }

    img {
      transition: all 0.3s ease 0s;
    }

    &:hover img {
      transform: scale(1.1);
    }

    &:last-child {
      margin-right: 0;
    }
  }

  .new-ctivity {
    background: rgba(59, 157, 255, 0.05);
    text-align: center;

    .add_img {
      margin-top: 33px;
    }
  }

  .new-act-tit {
    text-align: center;
    width: 100%;
    font-size: 14px;
    color: #409eff;
    position: absolute;
    top: 107px;
  }

  .user-kendou,
  .user-report {
    border: 1px solid rgba(239, 239, 239, 1);
    padding: 20px 20px 33px 30px;

    .title {
      display: flex;
      justify-content: space-between;
      line-height: 24px;

      .p1 {
        font-size: 19px;
        color: @text-color;
        font-weight: 400;
      }

      .p2 {
        font-size: 12px;
        color: #495970;
        font-weight: 400;
      }
    }

    .num {
      display: flex;
      justify-content: space-between;

      .num-txt {
        margin: 28px 0 0 28px;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .p3 {
        font-size: 19px;
        color: #409eff;
        font-weight: 400;
        line-height: 27px;
        margin-bottom: 5px;
      }

      .p4 {
        font-size: 14px;
        color: #495970;
        margin-left: 4px;
        font-weight: 400;
        line-height: 20px;
      }

      .img1 {
        margin-top: 17px;
      }
    }
  }
}

.title-sub {
  font-size: 24px;
  margin-top: 73px;
  margin-bottom: 40px;
}

.recommend-check {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  flex-wrap: wrap;

  li {
    width: 30%;
    padding-bottom: 30px;
    background: rgba(255, 255, 255, 1);
    border-radius: 10px;
    border: 1px solid rgba(239, 239, 239, 1);
    margin-bottom: 30px;
    position: relative;

    .img1 {
      margin: 56px auto;
      width: 200px;
      height: 150px;
      display: block;
    }

    .img2 {
      margin: 0 auto 63px auto;
      display: block;
      width: 100%;
    }

    .p1 {
      font-size: 20px;
      font-weight: bold;
      color: @text-color;
      margin-bottom: 12px;
      padding: 0 40px;
    }

    .p2 {
      font-size: 14px;
      font-weight: 400;
      color: @text-color;
      padding: 0 40px;
    }

    .bottom {
      display: flex;
      justify-content: space-between;
      padding: 22px 40px 0 40px;

      .btn {
        width: 90px;
        height: 30px;
        line-height: 30px;
        background: linear-gradient(90deg,
            rgba(38, 208, 241, 1) 0%,
            rgba(64, 158, 255, 1) 100%);
        cursor: pointer;
        box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        text-align: center;
        font-size: 14px;
        font-weight: 500;
        color: #fff;
      }

      a {
        color: #409eff;
      }
    }

    img {
      transition: all 0.3s ease 0s;
    }

    &:hover .img1 {
      transform: scale(1.1) rotate(10deg);
    }
  }

  &:after {
    content: "";
    width: 30%;
    height: 0;
  }
}

// @media (min-width: 1024px) {
//   .recommend-check li {
//     width: 395px;
//   }
// }

// @media (min-width: 1280px) {
//   .recommend-check li {
//     width: 400px;
//   }
// }

// @media (min-width: 1440px) {
//   .recommend-check li {
//     width: 500px;
//   }
// }
.no_date {
  margin: 128px auto 0 auto;
  text-align: center;

  .text {
    font-size: 16px;
    color: #495970;
    margin-top: 30px;
  }
}

.mock_div {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  .bg_ul {
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.6;
  }

  .img_ul {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;

    >li {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .btn_div {
      width: 160px;
      line-height: 38px;
      text-align: center;
      color: #fff;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      cursor: pointer;
    }
  }
}

.new_div {
  display: flex;
  justify-content: center;

  .div_img {
    position: relative;
    display: flex;
    justify-content: center;

    .abso_div {
      position: absolute;
      top: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;

      p {
        font-size: 30px;
        color: #495970;
        font-weight: bold;
      }

      .big_ul {
        margin-top: 100px;
        background-color: #fff;
        padding: 45px;
        border-radius: 5px;

        li {
          display: flex;
          justify-content: space-between;

          div {
            width: 450px;
          }
        }
      }

      .tip_titile {
        display: flex;
        justify-content: space-between;

        .left_s {
          color: #495970;
          font-weight: bold;
          font-size: 24px;
        }

        .right_s {
          color: #409eff;
          font-size: 16px;
          cursor: pointer;
        }
      }
    }
  }
}

.div_pos {
  position: absolute;
  top: 180px;
  right: 0;
  color: #fff;

  p {
    margin-top: 20px;
  }

  .btn_ul {
    width: 175px;

    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    margin-left: 50px;

    .btn_left {
      line-height: 28px;
      border: 1px solid #fff;
      border-radius: 15px;
      padding: 0 20px;
      cursor: pointer;
    }

    .btn_right {
      line-height: 28px;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      border-radius: 15px;
      padding: 0 20px;
      margin-left: 20px;
      cursor: pointer;
    }
  }
}

.set1class {
  z-index: 9999999999;
  background-color: #fff !important;
}

.big_user {
  position: absolute;
  z-index: 99999999999;
  top: 0px;
  left: 100px;
  color: #fff;

  .spance_div {
    display: flex;
    align-items: center;
  }

  p {
    margin-left: 120px;
    font-size: 14px;
    margin-top: 10px;
  }

  .btn_ul {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    margin-left: 120px;
    width: 200px;

    .btn_left {
      line-height: 28px;
      border: 1px solid #fff;
      border-radius: 15px;
      padding: 0 20px;
      cursor: pointer;
    }

    .btn_right {
      line-height: 28px;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      border-radius: 15px;
      padding: 0 20px;
      cursor: pointer;
    }
  }
}

.img_pos {
  position: absolute;
  top: 0;
  z-index: 9999999999999;
  color: #fff;

  p {
    margin-left: 120px;
    font-size: 14px;
    margin-top: 10px;
  }

  .btn_ul {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    margin-left: 50px;

    .btn_left {
      line-height: 28px;
      border: 1px solid #fff;
      border-radius: 15px;
      padding: 0 20px;
      cursor: pointer;
    }

    .btn_right {
      line-height: 28px;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      border-radius: 15px;
      padding: 0 20px;
      margin-left: 20px;
      cursor: pointer;
    }
  }
}

.img_pos_2 {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 9999999999999;
  color: #fff;

  p {
    font-size: 14px;
    margin-top: 10px;
  }

  .btn_ul {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    .btn_left {
      line-height: 28px;
      border: 1px solid #fff;
      border-radius: 15px;
      padding: 0 20px;
      cursor: pointer;
    }

    .btn_right {
      line-height: 28px;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      border-radius: 15px;
      padding: 0 20px;
      cursor: pointer;
    }
  }
}

.affiche {
  color: #495970;
  border: 1px solid red;
  display: block;
  width: 90%;
  height: 30px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.affiche_text0 {
  position: absolute;
  top: 0;
  // left: 100%;
  line-height: 30px;
  display: block;
  word-break: keep-all;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.affiche_text1 {
  position: absolute;
  top: 0;
  // left: 100%;
  line-height: 30px;
  display: block;
  word-break: keep-all;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.affiche_text2 {
  position: absolute;
  top: 0;
  // left: 100%;
  line-height: 30px;
  display: block;
  word-break: keep-all;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.affiche_text3 {
  position: absolute;
  top: 0;
  // left: 100%;
  line-height: 30px;
  display: block;
  word-break: keep-all;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.leftclass {
  margin-left: 200px;
}

.tips_div {
  display: flex;
  align-items: center;
  background-color: #f5faff;
  padding: 0 10px;
  // height: 40px;
}

.carousel {
  width: 85%;
  margin: 0 15px;
  height: 50px;

  .carousel_div {
    display: flex;
    align-items: center;
    // background-color: yellow;
  }
}


.card_body {
  display: flex;

  .cards_li {
    flex: 1;
    padding: 15px;
    box-shadow: -5px 6px 15px 0px rgba(0, 0, 0, 0.05);
  }

  .menus_card {
    width: 190px;
    line-height: 62px;
    text-align: center;
    cursor: pointer;
  }

  .left_li {
    padding: 15px 0;
    background: #FCFCFC;
    height: 526px;
  }

  .selected_card {
    border-left: 2px solid #409EFF;
    background-color: #F2F7FC;
    color: #409EFF;
  }
}