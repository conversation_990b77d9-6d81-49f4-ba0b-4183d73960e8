import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'idname'
})
export class IdnamePipe implements PipeTransform {

  private optionList : any[] = [];

  proStatusList = [
    { id: "ANNOUNCED", name: "未开始" },
    { id: "WAITING_ANSWER", name: "未开始" },
    { id: "ANSWERING", name: "进行中"},
    { id: "OVER", name: "已结束"},
    { id: "SUSPEND", name: "已结束"},
    { id: "NOT_INVITED", name: "未开始"},
  ];

  usrStatusList = [
    { id: "UNANSWERED", name: "未完成"},
    { id: "ANSWERED", name: "已完成"},
  ];

  constructor() {
    this.initOptionList(this.proStatusList);
    this.initOptionList(this.usrStatusList);
  }

  private initOptionList(list : any[]) : void {
    for (let index = 0; index < list.length; index++) {
      const element = list[index];
      this.optionList.push(element);
    }
  }

  transform(key: any): any {
    for (let index = 0; index < this.optionList.length; index++) {
      const element = this.optionList[index];
      if(key == element.id) {
        
        return element.name;
      }
    }
    return "N/A";
  }

}
