import { Component, OnInit, Input, ViewChild } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { NzTreeComponent, NzMessageService } from "ng-zorro-antd";
import { SurveyApiService } from "../../../service/survey-api.service";
import { DownloadUtilService } from "../../../service/download-util.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";
@Component({
  selector: "app-organization-page",
  templateUrl: "./organization-page.component.html",
  styleUrls: ["./organization-page.component.less"],
})
export class OrganizationPageComponent implements OnInit {
  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;

  @Input() projectId: string;

  constructor(
    private http: HttpClient,
    private api: SurveyApiService,
    private msgServ: NzMessageService,
    private until: DownloadUtilService,
    private customMsg: MessageService
  ) {}

  hierarchies: any[] = [];

  isAll: boolean = false;
  searchValue: string = "";
  searchName: string = "";
  ids: string[] = []; // 选中id
  treeList: any[] = [];
  orgList: any[] = [];

  selectedNodes: any = {}; // 树选中

  chartLoaded: boolean = false;

  orgPersonList: any[] = [];

  public chartOption: any;

  defaultExpandedKeys: string[] = [];
  containerWidth = "520";
  containerHeight = "350";

  checkAll: boolean = false;
  checkOne: boolean = false;
  checkTwo: boolean = false;
  checkThree: boolean = false;
  checkedKeys: any[] = [];

  page: any = {
    current: 1,
    size: 20,
    total: 1,
  };

  loadData(isSearch?: boolean) {
    if (isSearch) {
      this.page = {
        current: 1,
        size: 20,
        total: 1,
      };
    }
    let params: any = {
      page: this.page,
      projectId: this.projectId,
      value: this.searchName,
      ids: this.ids,
      hierarchies: this.hierarchies,
    };
    this.api.pageOrganizationAnswerRate(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.orgList = res.data;
        this.page = res.page;
        this.orgList.forEach((item) => {
          item.checked = false;
          if (this.isAll || this.checkOne || this.checkTwo || this.checkThree) {
            item.checked = true;
          }
        });
      }
    });
  }

  changeRespondentAllChecked(type: string) {
    // 全选
    if (type === "ALL") {
      this.checkOne = false;
      this.checkTwo = false;
      this.checkThree = false;
    } else {
      this.isAll = false;
    }
    this.hierarchies = [];
    if (this.isAll) this.hierarchies.push("ALL");
    if (this.checkOne) this.hierarchies.push("ONE");
    if (this.checkTwo) this.hierarchies.push("TWO");
    if (this.checkThree) this.hierarchies.push("THREE");
    if (this.isAll || this.checkOne || this.checkTwo || this.checkThree) {
      this.ids = [];
    }
    this.loadData(true);
  }

  changeRespondentChecked(e, id) {
    // 单选
    let index = this.ids.findIndex((item) => {
      return item === id;
    });
    if (index === -1) {
      if (e) {
        this.ids.push(id);
      }
    } else {
      if (!e) {
        this.ids.splice(index, 1);
      }
    }
  }

  exportRespondentList() {
    // 导出填答人列表
    console.log(this.ids);

    if (this.ids.length === 0 && this.hierarchies.length === 0)
      // return this.msgServ.warning("请选择要导出的人员");
      return this.customMsg.open("warning", "请选择要导出的人员");
    let params: any = {
      page: this.page,
      projectId: this.projectId,
      value: this.searchName,
      ids: this.ids,
      hierarchies: this.hierarchies,
    };
    this.api.exportOrganizationAnswerRate(params).subscribe((res) => {
      this.until.downFile(res);
    });
  }

  ngOnInit() {
    this.loadData();
  }
}
