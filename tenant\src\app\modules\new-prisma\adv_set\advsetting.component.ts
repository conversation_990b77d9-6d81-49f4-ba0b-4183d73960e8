// 调研高级设置
import {
  Component,
  Input,
  OnInit,
  ChangeDetectorRef,
  Inject,
  OnDestroy,
} from "@angular/core";
import { ActivatedRoute, Router, NavigationEnd } from "@angular/router";
import { Observable, Observer, Subscription } from "rxjs";
import { UploadFile, UploadXHRArgs } from "ng-zorro-antd/upload";
import { NewPrismaService } from "../new-prisma.service";
import { NzModalRef, NzMessageService } from "ng-zorro-antd";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { MessageService } from "@src/shared/custom-message/message-service.service";

import { NzDrawerRef } from "ng-zorro-antd/drawer";

import _ from "lodash";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-set",
  templateUrl: "./advsetting.component.html",
  styleUrls: ["./advsetting.component.less"],
})
export class AdvancedSetting implements OnInit, OnDestroy {
  @Input() listdata;
  @Input() projectId?;
  @Input() demographicHTML1: any = [];
  @Input() demographicHTML2: any = [];

  constructor(
    private cdr: ChangeDetectorRef,
    private msg: NzMessageService,
    private api: NewPrismaService,
    private drawerRef: NzDrawerRef,
    private activatedRoute: ActivatedRoute,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private HttpClient: HttpClient,
    private customMsg: MessageService,
    private router: Router,
    public knxFunctionPermissionService: KnxFunctionPermissionService,
    public permissionService: PermissionService
  ) {}

  precision = 0;
  precision_min = 0;
  customNum = {};
  standardDemographicIds = []; //人口信息学已选字段
  standardDemographicDTOType = "DEMOGRAPHIC_DEFAULT";
  tenantApi: string = "/tenant-api";

  mm: number = 0;
  ss: number = 1;

  isEnableAnswerLotteryDraw: boolean = false; //  是否开启填答抽奖
  isEnableQuestionBookDistribute: boolean = false; // 是否开启题本分发

  Isactive = true;
  settingData: any = {
    defaultOpenValue: new Date(0, 0, 0, 0, 0),
    logoFile: "",
    pcBackgroundPic: "",
    mobileBackgroundPic: "",
    isShowKnxLogo: true, //logo
    isShowBackgroundPic: false,
    // HertDTOType:'DEMOGRAPHIC_DEFAULT',
    answerEffectiveRange: 1, // 填答完成率
    answerEffectiveTime: "1", // 填答时间
    answerSameRate: "100", // 一致性
    questionNumInOnePage: "5",
    sequence: "QUESTION_TYPE",
    isCheckLicenseNotice: true, //许可声明
    isQuestionCustomSort: false, //每页题数类型,默认false
    isAutoPage: true, //  一页一题时，自动翻页
    isAutoFillQuestionBook: false, // 题本分发致页面题目有缺时，自动补位后续题本
    isEnableAnswerLotteryDraw: false, //  是否开启填答抽奖
    isEnableQuestionBookDistribute: false, // 是否开启题本分发
    isOpenValidQuestion: false, // 是否开启有效性
    isNotCalculateLackAnswer: false, // 不计算【因不计分/未填写题本等】致不足有效人数
    isEnableWelcomePage: false,
    isCheckedAnswerValid: false, // 不满足填答有效性不可提交
    isEnableEndPage: false,
    isShowPreAnswer: true, // 人口标签页
    welcomePage: {
      zh_CN: "",
      en_US: "",
    },
    endPage: {
      zh_CN: "",
      en_US: "",
    },

    isCheckAnswerInstructions: true, //作答说明
    isShowDemographicQuestion: true, //是否显示人口信息学题
    language: "zh_CN", // 填答默认语言
    availableLanguages: ["zh_CN", "en_US"], // 新增语言
    optionalLanguages: ["zh_CN", "en_US"], // 填答可选语言
    standardDemographicDTO: {
      standardDemographicIds: [],
      type: "DEMOGRAPHIC_DEFAULT", // 人口学 默认
    },
  };
  // projectLangOptions = [
  //   {
  //     name: 'zh_CN',
  //     type: '中文',
  //     checked: false
  //   },
  //   {
  //     name: 'en_US',
  //     type: '英文',
  //     checked: false
  //   }
  // ];
  projectLangOptions = []; // 填答可选语言项/当前活动的
  // checkedLangOptions = []; // 默认语言项/可选想选中的
  formData = new FormData(); //文件数据
  loading = false;
  avatarUrl: string;
  PcavatarUrl: string;
  MbavatarUrl: string;
  isCheckLicenseNotice = true; //许可声明

  isCheckAnswerInstructions = true; //作答说明
  isShowDemographicQuestion = true; //是否显示人口信息学题
  isEnableAnalysisCustomIndex = false;
  isEnableReportSettings = false;
  isEnableAnalysisTask = false;
  permission;
  locwelcomepage = false;
  locengpage = false;
  wlcomecontant = "";
  Isactivelan = true;
  pagename = "";
  tinyconfig = {};
  reportType = "";
  type = "";
  voild: boolean = false; // 有效题本是否可选
  isShowIsNotCalculateLackAnswer: boolean = false; // 【因不计分/未填写题本等】致不足有效人数-是否显示

  tableIndex: number = 0; // tabIndex

  lanVisible: boolean = false; // 语言设置
  allLans: any[] = []; // 所有语言-当前租户下
  lansFilter_cnen: any[] = [];
  allLansOpen = false;

  addCodes: string[] = [];
  selectOptions: any[] = [];

  loceng_locwelcome_lan = "zh_CN";

  defaultLangOptions = [
    {
      value: "zh_CN",
      name: "中文",
      checked: false,
    },
    {
      value: "en_US",
      name: "英文",
      checked: false,
    },
  ];
  defaultCode = ["zh_CN", "en_US"];
  // defaultCode = ['zh_CN'];
  private routerSubscription: Subscription;
  ngOnInit() {
    this.lansFilter_cnen = _.cloneDeep(this.defaultLangOptions);
    this.projectLangOptions = _.cloneDeep(this.defaultLangOptions);
    // this.checkedLangOptions = _.cloneDeep(this.defaultLangOptions);
    this.reportType = this.listdata.factorname.surveyStandardQuestionnaire.reportType;
    if (this.listdata.factorname.surveyQuestionnaire) {
      this.reportType = this.listdata.factorname.surveyQuestionnaire.reportType;
    }
    this.type = this.activatedRoute.snapshot.queryParams.type;
    // this.loceng_locwelcome_lan = this.listdata.optionalLanguages[0] || 'zh_CN'
    if (
      this.type === undefined ||
      this.type === "ANNOUNCED" ||
      this.type === "PREVIEW"
    ) {
      this.voild = false;
    } else {
      this.voild = true;
    }
    const _this = this;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.tinyconfig = {
      height: 270,
      fontsize_formats:
        "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 32pt 36pt",
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      toolbar: [
        "removeformat undo redo formatselect fontsizeselect",
        "bold italic underline strikethrough forecolor backcolor",
        "bullist numlist alignleft aligncenter alignright alignjustify outdent indent image link",
      ],
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      images_upload_url: `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`, // 配置你图片上传的url
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.HttpClient.post(
          `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`,
          formData,
          { headers: headers }
        ).subscribe(
          (response: any) => {
            //console.log(response);
            if (response) {
              this.HttpClient.get(
                `${this.tenantApi}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                { headers: headers }
              ).subscribe((imgurl: any) => {
                let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                // if ( environment.dev ) {
                //    url = environment.SERVER_URL.substr(0, environment.SERVER_URL.length - 1)  + imgurl.data.url; // 这里是你获取图片url
                // } else {
                //   url = 'api' + imgurl.data.url; // 这里是你获取图片url
                // }
                // 把图片链接，img src标签显示图片的有效链接放到下面回调函数里
                //console.log(url);
                success(url);
              });
            } else {
              if (response && response.rtnMsg) {
                failure(response.rtnMsg);
              } else {
                failure("上传失败：未知错误");
              }
            }
          },
          (error1) => {
            //console.log(error1);
            failure("上传失败：未知错误");
          }
        );
      },
    };
    this.permission = this.permissionService.isPermission();
    if (this.listdata) {
      this.demographicHTML2.forEach((val, i) => {
        this.listdata.standardDemographicDTO.standardDemographicIds.forEach(
          (id, k) => {
            if (val.id === id) {
              this.customNum[id] = true;
            }
          }
        );
      });
      this.settingData = this.listdata;
      this.mm = parseInt(
        ((this.settingData.answerEffectiveTime / 60) % 60) + ""
      );
      this.ss = parseInt((this.settingData.answerEffectiveTime % 60) + "");
      this.standardDemographicDTOType = this.settingData.standardDemographicDTO.type;
      if (!this.settingData.isDimensionAlgorithmCustom) {
        this.settingData.isDimensionAlgorithmCustom = false;
      }
      //计算方式
      if (!this.settingData.calculationMethod) {
        this.settingData.calculationMethod = "STANDARD_APPROVAL_DEGREE";
      }
      if (!this.settingData.calculationApprovalType) {
        this.settingData.calculationApprovalType = 'APPROVAL_NEUTRAL_DISAPPROVAL';
      }

      if (!this.settingData.coefficientAlgorithm) {
        if (
          this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM" ||
          this.reportType === "OC_INVESTIGATION_RESEARCH"
        ) {
          this.settingData.coefficientAlgorithm = "REGRESSION"; //默认回归系数
        } else {
          this.settingData.coefficientAlgorithm = "CORRELATION"; //默认相关系数
        }
      }

      this.isEnableAnswerLotteryDraw = this.settingData.isEnableAnswerLotteryDraw;
      this.isEnableQuestionBookDistribute = this.settingData.isEnableQuestionBookDistribute;
      this.isEnableAnalysisCustomIndex = this.settingData.isEnableAnalysisCustomIndex;
      this.isEnableReportSettings = this.settingData.isEnableReportSettings;
      this.isEnableAnalysisTask = this.settingData.isEnableAnalysisTask;
      // 调研指数算法
      this.signAlgorithm = this.settingData.signAlgorithm
        ? this.settingData.signAlgorithm
        : "ONE_RANK";
      this.oneRankDimensionAlgorithm = this.settingData
        .oneRankDimensionAlgorithm
        ? this.settingData.oneRankDimensionAlgorithm
        : "TWO_RANK";
      this.twoRankDimensionAlgorithm = this.settingData
        .twoRankDimensionAlgorithm
        ? this.settingData.twoRankDimensionAlgorithm
        : "THREE_RANK";
      this.threeRankDimensionAlgorithm = this.settingData
        .threeRankDimensionAlgorithm
        ? this.settingData.threeRankDimensionAlgorithm
        : "QUESTION";
      let baseUrl: string = window.location.origin + "/";
      if (baseUrl.indexOf("http://localhost") !== -1) {
        baseUrl = "http://***********/";
      }

      this.avatarUrl = this.listdata.logoFile
        ? `${baseUrl}api/file/www/${this.listdata.logoFile}`
        : null;
      this.PcavatarUrl = this.listdata.pcBackgroundPic
        ? `${baseUrl}api/file/www/${this.listdata.pcBackgroundPic}`
        : null;
      this.MbavatarUrl = this.listdata.mobileBackgroundPic
        ? `${baseUrl}api/file/www/${this.listdata.mobileBackgroundPic}`
        : null;
    } else {
      this.listDemographicFromGroupId();
      this.settingData.standardDemographicDTO.type = this.standardDemographicDTOType;
    }

    // 语言默认
    this.settingData.optionalLanguages.forEach((res) => {
      this.projectLangOptions.forEach((val) => {
        val.checked = false;
        if (res == val.value) {
          val.checked = true;
        }
      });
    });
    // 获取所有语言中种类
    this.getLanOptions();
    this.routerSubscriptionFun();
    console.log(this.reportType);
    //  不计算【因不计分/未填写题本等】致不足有效人数
    //  屏蔽定制工具腾讯、网易、爱普生、vivo 'INVESTIGATION_RESEARCH_CUSTOM',
    const hideCustomization = [
      "TENCENT_INVESTIGATION_RESEARCH_CUSTOM",
      "EPSON_INVESTIGATION_RESEARCH_CUSTOM",
      "VIVO_INVESTIGATION_RESEARCH_CUSTOM",
      "NETEASE_INVESTIGATION_RESEARCH_CUSTOM",
    ];
    if (hideCustomization.includes(this.reportType)) {
      this.isShowIsNotCalculateLackAnswer = false;
    } else {
      //  目前只展示乾象,组织敬业度调研自定义,双视角调研
      const showCustomization = [
        "QIANXINAG_INVESTIGATION_RESEARCH_CUSTOM",
        "INVESTIGATION_RESEARCH_CUSTOM",
        "DP_INVESTIGATION_RESEARCH_CUSTOM",
      ];
      if (showCustomization.includes(this.reportType)) {
        this.isShowIsNotCalculateLackAnswer = true;
      }
    }
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  showCus: boolean = false;

  cusOption: any[] = [
    {
      label: "一级维度",
      value: "ONE_RANK",
    },
    {
      label: "二级维度",
      value: "TWO_RANK",
    },
    {
      label: "三级维度",
      value: "THREE_RANK",
    },
    {
      label: "题目",
      value: "QUESTION",
    },
  ];

  signAlgorithm: string = "ONE_RANK";
  oneRankDimensionAlgorithm: string = "TWO_RANK";
  twoRankDimensionAlgorithm: string = "THREE_RANK";
  threeRankDimensionAlgorithm: string = "QUESTION";
  showCusBox() {
    this.showCus = true;
  }
  cusOk() {
    this.showCus = false;
  }
  cusDefault() {
    this.signAlgorithm = "ONE_RANK";
    this.oneRankDimensionAlgorithm = "TWO_RANK";
    this.twoRankDimensionAlgorithm = "THREE_RANK";
    this.threeRankDimensionAlgorithm = "QUESTION";
  }

  lotteryChange(e: boolean) {
    if (e) {
      this.settingData.isEnableEndPage = true;
      this.api.showEndPageDemo("EMPLOYEE_ENGAGEMENT").subscribe((res) => {
        this.settingData.endPage = res.data;
      });
    }
  }

  listDemographicFromGroupId() {
    this.demographicHTML1.forEach((val, i) => {
      this.settingData.standardDemographicDTO.standardDemographicIds.push(
        val.id
      );
    });
    this.demographicHTML2.forEach((item) => {
      this.customList[item.id] = false;
    });
  }

  customList(i, id) {
    this.customNum[id] = !this.customNum[id];
    // 自定义前2项为必选
    const id0 = this.demographicHTML2[0].id;
    const id1 = this.demographicHTML2[1].id;
    this.settingData.standardDemographicDTO.standardDemographicIds[0] = id0;
    this.settingData.standardDemographicDTO.standardDemographicIds[1] = id1;

    if (this.customNum[id]) {
      this.settingData.standardDemographicDTO.standardDemographicIds.push(id);
    } else {
      // 取消
      const simpleArr = this.settingData.standardDemographicDTO.standardDemographicIds.filter(
        (item, i) => {
          return item !== id;
        }
      );
      this.settingData.standardDemographicDTO.standardDemographicIds = simpleArr;
    }
  }

  funStandardDemographic() {
    // 选择人口学信息
    this.settingData.standardDemographicDTO.standardDemographicIds = [];
    if (this.standardDemographicDTOType === "DEMOGRAPHIC_DEFAULT") {
      for (let id in this.customNum) {
        this.customNum[id] = false;
      }
    } else {
      const id0 = this.demographicHTML2[0].id;
      const id1 = this.demographicHTML2[1].id;
      this.settingData.standardDemographicDTO.standardDemographicIds[0] = id0;
      this.settingData.standardDemographicDTO.standardDemographicIds[1] = id1;
    }
  }

  // funHertDTOType(){

  // }//选择链接方式
  getValidValue() {
    // this.settingData.ValidMin = this.settingData.ValidMin.toFixed(1)
  }

  /**
   * beforeUpload 校验上传的图片格式
   * @param file
   */
  beforeUpload = (file: File) => {
    return new Observable((observer: Observer<boolean>) => {
      // file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/gif' || file.type === 'image/bmp'
      const isPNG = file.type === "image/png";
      const isSize = file.size / 1024 < 500;
      if (!isPNG) {
        // this.msg.error("文件类型不合法,只能是png类型！");
        this.customMsg.open("error", "文件类型不合法,只能是png类型！");
        observer.complete();
        return;
      }
      if (!isSize) {
        // this.msg.error("图片应小于500KB!");
        this.customMsg.open("error", "图片应小于500KB!");
        observer.complete();
        return;
      }

      observer.next(isPNG && isSize);
      observer.complete();
    });
  };
  /**
   * beforeUpload 校验上传的图片格式
   * @param file
   */
  beforeUpload1 = (file: File) => {
    return new Observable((observer: Observer<boolean>) => {
      // file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/gif' || file.type === 'image/bmp'
      const isPNG = file.type === "image/png";
      const isSize = file.size / 1024 < 300;
      if (!isPNG) {
        // this.msg.error("文件类型不合法,只能是png类型！");
        this.customMsg.open("error", "文件类型不合法,只能是png类型！");
        observer.complete();
        return;
      }
      if (!isSize) {
        // this.msg.error("图片应小于300KB!");
        this.customMsg.open("error", "图片应小于300KB!");
        observer.complete();
        return;
      }

      observer.next(isPNG && isSize);
      observer.complete();
    });
  };
  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.formData = formData;
    this.upload(formData, null, item);
  };
  /**
   * uploadExcel 上传配置
   */
  upload(formData, params, item) {
    return this.api.uploadFile(formData, params).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.settingData.logoFile = res.data.id;
          item.onSuccess!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }
  customReqPc = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.formData = formData;
    this.uploadPc(formData, null, item);
  };
  /**
   * uploadExcel 上传配置
   */
  uploadPc(formData, params, item) {
    return this.api.uploadFile(formData, params).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.settingData.pcBackgroundPic = res.data.id;
          item.onSuccess!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }
  customReqMb = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.formData = formData;
    this.uploadMb(formData, null, item);
  };
  /**
   * uploadExcel 上传配置
   */
  uploadMb(formData, params, item) {
    return this.api.uploadFile(formData, params).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.settingData.mobileBackgroundPic = res.data.id;
          item.onSuccess!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  handleChange(info: { file: UploadFile }): void {
    switch (info.file.status) {
      case "uploading":
        this.loading = true;
        this.getBase64(info.file!.originFileObj!, (img: string) => {
          this.avatarUrl = img;
        });
        break;
      case "done":
        // Get this url from response in real world.
        // this.loading = false;
        break;
      case "error":
        // this.msg.error("Network error");
        this.customMsg.open("error", "Network error");
        //this.loading = false;
        break;
    }
  }

  handleChangePic(info: { file: UploadFile }, type): void {
    switch (info.file.status) {
      case "uploading":
        this.loading = true;
        this.getBase64(info.file!.originFileObj!, (img: string) => {
          if (type == "PC") {
            this.PcavatarUrl = img;
          } else {
            this.MbavatarUrl = img;
          }
        });
        break;
      case "done":
        // Get this url from response in real world.
        // this.loading = false;
        break;
      case "error":
        // this.msg.error("Network error");
        this.customMsg.open("error", "Network error");
        //this.loading = false;
        break;
    }
  }

  /**
   * getBase64 获取图片base64
   * @param img
   * @param callback
   */
  private getBase64(img, callback: (img: string) => void): void {
    const reader = new FileReader();
    reader.addEventListener("load", () => callback(reader.result!.toString()));
    reader.readAsDataURL(img);
  }
  onChangeTime(e, type) {}
  getdefaultTime(e, type) {}

  mChange(e) {
    if (!this.ss && !e) {
      // this.msg.error("请输入有效时间！");
      this.customMsg.open("error", "请输入有效时间！");
      return;
    }
    if (!!e && !this.ss) {
      this.ss = 0;
    }
  }
  sChange(e) {
    if (!this.mm && !e) {
      // this.msg.error("请输入有效时间！");
      this.customMsg.open("error", "请输入有效时间！");
      return;
    }
    if (e > 60) {
      this.mm = parseInt(((e / 60) % 60) + "");
      this.ss = parseInt((e % 60) + "");
    }
    if (!!e && !this.mm) {
      this.mm = 0;
    }
  }

  isZero(m) {
    return m < 10 ? "0" + m : m;
  }
  formatDate(shijianchuo, type) {
    const time = new Date(shijianchuo); // 需要使用Date格式进行日期转化，若是时间戳、字符串时间，需要通过new Date(..)转化

    const y = time.getFullYear();

    const m = time.getMonth() + 1;

    const d = time.getDate();

    const h = time.getHours();

    const mm = time.getMinutes();

    const s = time.getSeconds();
    if (type === "date") {
      return y + "-" + this.isZero(m) + "-" + this.isZero(d);
    } else {
      return this.isZero(h) + ":" + this.isZero(mm) + ":" + this.isZero(s);
    }
  }
  CompareDate(t1, t2) {
    var date = new Date();
    var a = t1.split(":");
    var b = t2.split(":");
    return date.setHours(a[0], a[1]) > date.setHours(b[0], b[1]);
  }
  getSaveSet() {
    if (!this.ss && !this.mm) {
      // this.msg.error("请输入填答时间！");
      this.customMsg.open("error", "请输入填答时间！");
      return;
    }

    if (this.settingData.name.en_US.length > 100) {
      // this.msg.error("活动名称不能超过100个字符！");
      this.customMsg.open("error", "活动名称不能超过100个字符！");
      // this.settingData.name.en_US = ""
      return;
    }

    if (
      this.settingData.name.en_US.indexOf("/") != -1 ||
      this.settingData.name.en_US.indexOf("\\") != -1
    ) {
      // this.msg.error("活动名称包含非法字符'/''\\'！");
      this.customMsg.open("error", "活动名称包含非法字符'/''\\'！");
      // this.settingData.name.en_US = ""
      return;
    }

    if (
      this.settingData.answerEffectiveTime.toString().replace(" ", "") == ""
    ) {
      // this.msg.error("填答有效时间不能为空！");
      this.customMsg.open("error", "填答有效时间不能为空！");
      return;
    }

    if (this.settingData.optionalLanguages.length == 0) {
      // this.msg.error("填答时可选语言至少选一个！");
      this.customMsg.open("error", "填答时可选语言至少选一个！");

      return;
    }
    if (this.settingData.isEnableEndPage) {
      if (this.settingData.endPage.zh_CN == "") {
        // this.msg.error("结束页内容不能为空！");
        this.customMsg.open("error", "结束页内容不能为空！");

        return;
      }
    }
    if (this.settingData.isEnableWelcomePage) {
      if (this.settingData.welcomePage.zh_CN == "") {
        // this.msg.error("欢迎页内容不能为空！");
        this.customMsg.open("error", "欢迎页内容不能为空！");
        return;
      }
    }
    if (this.settingData.isShowBackgroundPic) {
      if (!this.PcavatarUrl && !this.MbavatarUrl) {
        // this.msg.error("请至少上传一个填答背景");
        this.customMsg.open("error", "请至少上传一个填答背景");
        return;
      }
    }

    this.settingData.answerEffectiveTime = this.mm * 60 + this.ss;

    this.settingData.standardDemographicDTO.type = this.standardDemographicDTOType;
    this.settingData.isEnableAnswerLotteryDraw = this.isEnableAnswerLotteryDraw;
    this.settingData.isEnableQuestionBookDistribute = this.isEnableQuestionBookDistribute;
    this.settingData.isEnableAnalysisCustomIndex = this.isEnableAnalysisCustomIndex;
    this.settingData.isEnableReportSettings = this.isEnableReportSettings;
    this.settingData.isEnableAnalysisTask = this.isEnableAnalysisTask;
    // 计算方式
    this.settingData.signAlgorithm = this.signAlgorithm;
    this.settingData.oneRankDimensionAlgorithm = this.oneRankDimensionAlgorithm;
    this.settingData.twoRankDimensionAlgorithm = this.twoRankDimensionAlgorithm;
    this.settingData.threeRankDimensionAlgorithm = this.threeRankDimensionAlgorithm;

    let that = this;
    that.drawerRef.close(this.settingData);
  } //保存设置
  getDefault() {
    //活动设置
    // if (
    //   this.permissionService.isPermissionOrSag(
    //     'SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PROJECT_SETTING'
    //   )
    // ) {
    this.settingData.name.en_US = "";
    this.settingData.isCheckAnswerInstructions = true;
    this.settingData.isCheckLicenseNotice = true;
    this.settingData.isEnableWelcomePage = false;
    this.settingData.isCheckedAnswerValid = false;
    this.settingData.isEnableEndPage = false;
    this.settingData.welcomePage = {
      zh_CN: "",
      en_US: "",
    };
    this.settingData.endPage = {
      zh_CN: "",
      en_US: "",
    };
    this.settingData.isShowKnxLogo = true;
    this.settingData.isShowBackgroundPic = false;
    this.settingData.logoFile = "";
    this.settingData.pcBackgroundPic = "";
    this.settingData.mobileBackgroundPic = "";
    this.avatarUrl = "";
    this.PcavatarUrl = "";
    this.MbavatarUrl = "";
    // }
    //发布设置
    if (
      this.permissionService.isPermissionOrSag(
        "SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PUBLISH_SETTING"
      )
    ) {
      this.cusDefault();
      this.settingData.isDimensionAlgorithmCustom = false;
      this.settingData.calculationMethod = "STANDARD_APPROVAL_DEGREE";
      this.settingData.calculationApprovalType = 'APPROVAL_NEUTRAL_DISAPPROVAL';
      this.settingData.answerEffectiveRange = "1";
      this.settingData.answerEffectiveTime = "1";
      this.mm = 0;
      this.ss = 1;
      this.settingData.answerSameRate = "100";
      if (this.type === "ANNOUNCED" || this.type === "PREVIEW") {
        this.settingData.isOpenValidQuestion = false;
      }
    }
    if (
      this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM" ||
      this.reportType === "OC_INVESTIGATION_RESEARCH"
    ) {
      this.settingData.coefficientAlgorithm = "REGRESSION"; //默认回归系数
    } else {
      this.settingData.coefficientAlgorithm = "CORRELATION"; //默认相关系数
    }
    this.settingData.questionNumInOnePage = "5";
    this.settingData.sequence = "QUESTION_TYPE";
    this.isEnableQuestionBookDistribute = false;
    this.settingData.isEnableQuestionBookDistribute = false;
    this.isEnableAnswerLotteryDraw = false;
    this.settingData.isEnableAnswerLotteryDraw = false;
    this.isEnableAnalysisCustomIndex = false; // 分析指数
    this.settingData.isEnableAnalysisCustomIndex = false; // 分析指数

    this.isEnableReportSettings = false; // 报告设置
    this.settingData.isEnableReportSettings = false; // 报告设置
    this.isEnableAnalysisTask = false; // 相关系数
    this.settingData.isEnableAnalysisTask = false; // 相关系数
    this.settingData.language = "zh_CN";
    this.settingData.optionalLanguages = ["zh_CN"];
    this.settingData.availableLanguages = ["zh_CN", "en_US"];
    this.settingData.standardDemographicDTO = {
      standardDemographicIds: [],
      type: "DEMOGRAPHIC_DEFAULT", // 人口学 默认
    };

    this.standardDemographicDTOType = "DEMOGRAPHIC_DEFAULT";
    this.listDemographicFromGroupId();
    this.customNum = {};
    this.settingData.optionalLanguages.forEach((res) => {
      this.projectLangOptions.forEach((val) => {
        val.checked = false;
        if (res == val.value) {
          val.checked = true;
        }
      });
    });
    this.addCodes = [];
    this.settingData.isShowPreAnswer = true;
    this.settingData.isShowDemographicQuestion = true;
    // 发布设置结束
    this.settingData.defaultOpenValue = new Date(0, 0, 0, 0, 0);
  }
  getActive(e) {
    if (e == "active") {
      this.Isactive = true;
    } else {
      this.Isactive = false;
    }
  } //活动设置 、发布设置
  optionalLan(e) {
    this.settingData.optionalLanguages = e.sort((a, b) => {
      if (a === "zh_CN") return -1;
      if (b === "zh_CN") return 1;
      if (a === "en_US") return -1;
      if (b === "en_US") return 1;
      return 0;
    });
  }
  funsequence(e) {}

  editorwelcome() {
    this.locwelcomepage = true;
    this.pagename = "欢迎页";
  }

  editorend() {
    this.locengpage = true;
    this.pagename = "结束页";
    if (!this.settingData.endPage.zh_CN && !this.settingData.endPage.en_US) {
      this.api.showEndPageDemo("EMPLOYEE_ENGAGEMENT").subscribe((res) => {
        this.settingData.endPage = res.data;
      });
    }
  }

  wlcomeSaveSet() {
    if (this.locwelcomepage) {
      this.locwelcomepage = false;
    }
    if (this.locengpage) {
      this.locengpage = false;
    }
    this.Isactivelan = true;
  }
  wlcomeDefault() {
    if (this.locwelcomepage) {
      this.locwelcomepage = false;
    }
    if (this.locengpage) {
      this.locengpage = false;
    }
    this.Isactivelan = true;
  }
  getActivenew(e) {
    if (e == "zh_CN") {
      this.Isactivelan = true;
    } else {
      this.Isactivelan = false;
    }
  }

  changeSelectedIndex(num) {
    this.tableIndex = num;
  }

  changeNameValue(name) {
    this.settingData.name = name;
  }

  // 显示语言设置-抽屉
  showanSetting() {
    this.allLansOpen = false; // 关闭选择器
    this.lanVisible = true;
  }
  // 语言设置-抽屉-关闭
  onLanSettingClose() {
    this.lanVisible = false;
    this.getLanOptions();
  }
  // 语言设置-抽屉-保存
  onLanSettingSave(e) {
    this.lansFilter_cnen.forEach((val) => {
      if (val.value === e.value) {
        val.name = e.name;
      }
    });
  }
  // 获取所有的语言类型
  async getLanOptions() {
    // 租户可选的语言项
    const allLansRes = await this.api.getLanguages().toPromise();
    if (allLansRes.result.code === 0) {
      // 排序-中英文最前
      const allLans = allLansRes.data.sort((a, b) => {
        if (a.value === "zh_CN") return -1;
        if (b.value === "zh_CN") return 1;
        if (a.value === "en_US") return -1;
        if (b.value === "en_US") return 1;
        if (a.value === "jp") return -1;
        if (b.value === "jp") return 1;
        if (a.value === "ko") return -1;
        if (b.value === "ko") return 1;
        if (a.value === "cs_1") return -1;
        if (b.value === "cs_1") return 1;
        if (a.value === "cs_2") return -1;
        if (b.value === "cs_2") return 1;
        if (a.value === "cs_3") return -1;
        if (b.value === "cs_3") return 1;
        return 0;
      });
      const filterCodes = ["zh_TW", "th"];
      this.allLans = allLans.filter((val) => !filterCodes.includes(val.value));
    }
    // 选项-新增语言
    const lansFilter_cnen = this.allLans.filter(
      (val) => !this.defaultCode.includes(val.value)
    );
    this.lansFilter_cnen = lansFilter_cnen;

    // 选项-可选语言
    const checkCode = this.settingData.availableLanguages; // 默认中文+填答可选语言+默认填答语言
    const projectLan = this.allLans.filter((val) =>
      checkCode.includes(val.value)
    ); //  请选择新增语言

    const selectCodes = _.uniq([...this.settingData.optionalLanguages]);

    const projectLangOptions = projectLan.map((val) => ({
      ...val,
      checked: selectCodes.includes(val.value),
    })); //  填答时可选语言

    // 新增语言-选中值
    if (this.addCodes.length === 0) {
      this.addCodes = _.uniq(
        checkCode.filter((val) => !this.defaultCode.includes(val))
      );
      this.projectLangOptions = projectLangOptions;
    } else {
      const newCheckCode = _.uniq([
        ...this.defaultCode,
        ...this.addCodes,
        ...selectCodes,
      ]);
      const newProjectLan = this.allLans.filter(
        (val) =>
          newCheckCode.includes(val.value) && !this.defaultCode.includes(val)
      );
      this.addCodes = _.uniq(
        newCheckCode.filter((val) => !this.defaultCode.includes(val))
      );
      this.projectLangOptions = newProjectLan.map((val) => ({
        ...val,
        checked: selectCodes.includes(val.value),
      }));
    }
  }

  onSelectI18n(e) {
    this.loceng_locwelcome_lan = e;
  }

  // 配置语言-填答语言的可选项
  changeProjectLan(e) {
    const fullCode = _.uniq([...e, ...this.defaultCode]);
    const lans = this.allLans.filter((val) => fullCode.includes(val.value));
    // 存储高级设置-可选语言
    this.projectLangOptions = lans.map((val) => ({
      ...val,
      // checked: this.settingData.optionalLanguages.includes(val.value)
      checked: true,
    }));

    this.settingData.availableLanguages = fullCode;
    this.settingData.optionalLanguages = fullCode;
    sessionStorage.setItem("projectLanguages", JSON.stringify(fullCode));
  }
  // 标准敬业度5g 组织能力指数 隐藏多语言设置
  isShowLan() {
    let flag = false;
    const type = ["INVESTIGATION_RESEARCH", "OC_INVESTIGATION_RESEARCH"];
    flag = !type.includes(this.reportType);
    return flag;
  }

  // 题数类型变更
  changeQuestionCustomSort(e) {
    if (!e) {
      this.settingData.isAutoFillQuestionBook = false;
    }
  }
}
