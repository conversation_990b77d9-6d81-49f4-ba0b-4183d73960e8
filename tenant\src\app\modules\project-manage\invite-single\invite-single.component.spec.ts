import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { InviteSingleComponent } from './invite-single.component';

describe('InviteSingleComponent', () => {
  let component: InviteSingleComponent;
  let fixture: ComponentFixture<InviteSingleComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ InviteSingleComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InviteSingleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
