div {
  font-family: PingFangSC-Light, PingFang SC;
}
li {
  margin-right: 4px;
  padding: 16px 10px;
  box-sizing: border-box;
}

li:hover {
  background-color: #f5faff;
  border-radius: 8px;
  // padding: 10px;
}
// ::ng-deep {
//     .ant-drawer-header {
//         border-bottom: none;
//     }
// }
.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  > div {
    display: flex;
    align-items: center;
  }

  .name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 224px;
    margin-right: 10px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #495970;
    line-height: 20px;
    cursor: default;
  }

  .common {
    background-color: #419eff;
  }

  .group {
    background-color: #60cb7f;
  }

  .type {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 58px;
    height: 21px;
    border-radius: 2px;
    margin-right: 8px;
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 17px;
  }
}

footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #495970;
    line-height: 17px;
  }
  a {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #409eff;
    line-height: 20px;
    img {
      width: 20px;
      height: 20px;
      margin-right: 6px;
    }
  }
}

.scroll {
  // .vxscrollbar();
  height: calc(100vh - 140px);
  overflow-y: auto;

  ul {
    height: 100%;
    .vxscrollbar();
    overflow-y: auto;
  }
}

.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

.temp-title {
  display: flex;
  align-items: center;
  // font-size: 24px;
  font-weight: bold;
  // color: #17314C;
  // line-height: 33px;
}

.bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  left: 0px;
  background: #fff;
  a {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #495970;
    padding: 4px 0;
    &:hover {
      color: #409eff;
    }
  }
}

.put_div {
  // 搜索
  width: 220px;
  // height: 30px;
  background: #fff;
  border-radius: 15px;

  input {
    border: none;
    border-radius: 15px;
  }
}
.tipIcon{
  border-radius: 4px;
}
.redtip {
  width: 8px;
  height: 8px;
  background-color: #e1251b;
  position: absolute;
  top: -3px;
  right: -3px;
  border-radius: 50%;
}

::ng-deep {
  .downloadLsit-right-drawer {
    .ant-drawer-body {
      padding: 16px 8px 16px 10px;
      height: calc(100% - 108px);
      overflow: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 11px 16px;
    }
    .ant-drawer-title {
      font-size: 20px;
      font-weight: bold;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
