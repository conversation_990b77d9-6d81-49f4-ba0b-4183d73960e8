<div class="container">
    <div class="title">活动延期</div>
    <div class="content client-width"> 
        <div class="label"><span>活动周期：{{startDate}} 至 </span></div>
        <div class="picker">
            <nz-date-picker  [nzDisabledDate]="disabledEndDate" [nzShowTime]="{nzMinuteStep:'30',nzFormat: 'HH:mm'}" (ngModelChange)="onChange($event)" [(ngModel)]="endDate" [nzFormat]="'YYYY-MM-DD HH:mm'" nzPlaceHolder="Select Time"></nz-date-picker>
        </div>
    </div>

    <!-- <div class="btn">
       <button class="button_cancel" (click)="triggerCancel()"><span>取消</span></button>
       <span></span>
       <button class="button_ok" (click)="triggerOk()"><span>确定</span></button>
    </div> -->
</div>