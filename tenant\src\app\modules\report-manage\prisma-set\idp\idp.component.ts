import { HttpClient } from "@angular/common/http";
import { Component, Input, OnInit } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd";
import { Observable } from "rxjs";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-idp",
  templateUrl: "./idp.component.html",
  styleUrls: ["./idp.component.less"],
})
export class IdpComponent implements OnInit {
  @Input() prismaReportDataId: string;

  modelList: any[] = [];

  seq: number = 1;

  tenantUrl: string = "/tenant-api";

  constructor(
    private http: HttpClient,
    private msgServ: NzMessageService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    this.bgGetList();
  }

  add() {
    this.modelList.push({
      category: { zh_CN: "", en_US: "" },
      content: { zh_CN: "", en_US: "" },
      delId: this.seq++,
    });
  }

  delete(delId: string) {
    let tmp: number = parseInt(delId);
    if (tmp > 1000) {
      this.bgDelete(delId).subscribe((res) => {
        if (res.result.code === 0) {
          this.remove(delId);
          this.msgServ.success("删除成功");
        }
      });
    } else {
      this.remove(delId);
      this.msgServ.success("删除成功");
    }
  }

  remove(delId) {
    _.remove(this.modelList, function(n) {
      return n.delId === delId;
    });
  }

  saveAll() {
    for (let index = 0; index < this.modelList.length; index++) {
      const element = this.modelList[index];
      let cat: any = element.category;
      if (!cat.zh_CN) {
        // this.msgServ.error("分类不能为空。");
        this.customMsg.open("error", "分类不能为空。");
        return;
      }
    }

    this.bgSaveList().subscribe((res) => {
      if (res.result.code === 0) {
        this.msgServ.success("保存成功");
      }
    });
  }

  bgGetList() {
    let api = `${this.tenantUrl}/survey/prisma/idp/listByPrismaReportDataId/${this.prismaReportDataId}`;
    this.http.get(api).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.modelList = res.data;
        for (let index = 0; index < this.modelList.length; index++) {
          const element = this.modelList[index];
          if (!element.content) {
            element.content = { zh_CN: "", en_US: "" };
          }
          element.delId = element.id;
        }
      }
    });
  }

  bgSaveList(): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/idp/batchSaveOrUpdate`;
    let param = {
      list: this.modelList,
      prismaReportDataId: this.prismaReportDataId,
    };
    return this.http.post(api, param);
  }

  bgDelete(delId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/idp/delete/${delId}`;
    return this.http.post(api, {});
  }
}
