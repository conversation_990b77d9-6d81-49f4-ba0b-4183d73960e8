<div class="index_xy" style="position: relative;">
  <div class="big_contant client-width">
    <div class="create_p">
      <span class="span_left">
        活动设置
        <img
          style="cursor: pointer;"
          src="assets/images/shownew.png"
          (click)="getnewlead()"
          alt=""
        />
      </span>
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </div>
    <ul class="create_name">
      <li style="margin-right: 10px;max-width: 500px;width: 500px;">
        <p>活动名称</p>
        <input
          nz-input
          nzSize="large"
          class="pri_name"
          placeholder="请输入活动名称"
          [(ngModel)]="prismaData.name.zh_CN"
          [disabled]="
            !permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:NAME_EDIT'
            )
          "
        />
      </li>
      <li>
        <p>活动周期</p>
        <div class="time-picker-con">
          <nz-range-picker
            class="time_picker"
            [(ngModel)]="dateRange"
            [nzDisabledDate]="disabledDate"
            [nzShowTime]="{ nzMinuteStep: '30', nzFormat: 'HH:mm' }"
            [nzFormat]="'YYYY-MM-DD HH:mm'"
            [nzPlaceHolder]="['请选择开始日期', '请选择结束日期']"
            (ngModelChange)="onChange($event)"
            [nzDisabled]="
              !permissionService.isPermissionOrSag(
                'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:PERIOD_EDIT'
              )
            "
          ></nz-range-picker>
          <ng-template #suffixIconSearch>
            <i class="icon-time"></i>
          </ng-template>
        </div>
      </li>
    </ul>
    <div class="label_title">
      <p class="title">人口标签</p>
      <div
        class="div_right"
        *ngIf="
          permissionService.isPermissionOrSag(
            'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DEMOGRAPHIC_EDIT'
          )
        "
      >
        <div
          class="custom_add_xy"
          (click)="showAllCheck()"
          *ngIf="!isUpdateing && isShowAll"
        >
          <i class="iconfont icon-xianshi" style="font-size: 14px;"></i>
          <span>开启</span>
        </div>
        <div
          class="custom_add_xy"
          (click)="hiddenAllcheck()"
          *ngIf="!isUpdateing && !isShowAll"
        >
          <i class="iconfont icon-yingcang" style="font-size: 14px;"></i>
          <span>隐藏</span>
        </div>
        <div class="border_left_d" *ngIf="!isUpdateing"></div>
        <div class="custom_add_xy" (click)="checkAll()" *ngIf="!isUpdateing">
          <i nz-icon nzType="check-square" nzTheme="outline"></i>
          <span>全选</span>
        </div>
        <div class="border_left_d" *ngIf="!isUpdateing"></div>
        <div class="custom_add_xy" (click)="clearcheck()" *ngIf="!isUpdateing">
          <i class="iconfont icon-icon-" style="font-size: 14px;"></i>
          <span>清空</span>
        </div>
        <div class="custom_add_xy" *ngIf="isUpdateing">
          <span (click)="addfactor(true)">查看</span>
        </div>
        <div class="border_left_d" *ngIf="!isUpdateing"></div>
        <div class="custom_add_xy" *ngIf="!isUpdateing">
          <span (click)="addfactor(false)">+ 自定义</span>
        </div>
      </div>
    </div>
    <div class="top_div">
      <ul class="div_left">
        <li class="li_list">
          <nz-checkbox-wrapper (nzOnChange)="ngModelChange($event)">
            <div
              style="display: flex; justify-content: flex-start;flex-wrap: wrap;"
            >
              <div
                *ngFor="
                  let item of factortable.standardAnalysisFactorVOS;
                  let i = index
                "
              >
                <div
                  class="li_span"
                  nz-checkbox
                  [nzDisabled]="isUpdateing || item.isRequire"
                  [(ngModel)]="item.isChecked"
                  [(nzValue)]="item.standardDemographicId"
                >
                  <div style="display: flex;align-items: center;">
                    <!-- <span nz-tooltip [(nzTooltipTitle)]="item.name.zh_CN"
                      *ngIf="item.name.zh_CN.length >= 4">{{item.name.zh_CN}}</span> -->
                    <span>{{ item.name.zh_CN }}</span>
                    <ng-container
                      *ngIf="
                        permissionService.isPermissionOrSag(
                          'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DEMOGRAPHIC_EDIT'
                        )
                      "
                    >
                      <i
                        style="margin-left: 5px;"
                        *ngIf="item.isEdit && !item.isRequire && !isUpdateing"
                        class="iconfont icon-bianji hover-icon"
                        (click)="spaneditor(item, i)"
                      ></i>
                      <ng-container *ngIf="isUpdateing; else tmp">
                        <i
                          *ngIf="item.isChecked && !item.isHidden"
                          class="iconfont icon-xianshi"
                        ></i>
                        <i
                          *ngIf="item.isChecked && item.isHidden"
                          class="iconfont icon-yingcang"
                        ></i>
                      </ng-container>
                      <ng-template #tmp>
                        <i
                          *ngIf="item.isChecked && !item.isHidden"
                          style="cursor: pointer !important;"
                          class="iconfont icon-xianshi hover-icon"
                          (click)="changeHidden($event, item, i)"
                        ></i>
                        <i
                          *ngIf="item.isChecked && item.isHidden"
                          style="cursor: pointer !important;"
                          class="iconfont icon-yingcang hover-icon"
                          (click)="changeHidden($event, item, i)"
                        ></i>
                      </ng-template>
                      <div
                        *ngIf="!item.isEdit || item.isRequire || isUpdateing"
                        style="width: 17px;height: 17px;"
                      ></div>
                    </ng-container>
                  </div>
                </div>
              </div>
            </div>
          </nz-checkbox-wrapper>
        </li>
        <!-- 修改因子名称 -->
        <nz-drawer
          [(nzVisible)]="factornameshow"
          nzTitle="修改因子名称"
          (nzOnClose)="spanCancel()"
          nzWrapClassName="round-right-drawer3"
          [nzWidth]="400"
        >
          <ul>
            <li>
              <p>因子名称(中)</p>
              <input
                style="margin-top: 10px;"
                nz-input
                placeholder="请输入中文名称"
                nzSize="default"
                [(ngModel)]="factornames.zh_CN"
              />
            </li>
            <li style="margin-top: 20px;">
              <p>因子名称(英)</p>
              <input
                style="margin-top: 10px;"
                nz-input
                placeholder="请输入英文名称"
                nzSize="default"
                [(ngModel)]="factornames.en_US"
              />
            </li>
          </ul>
          <div class="drawer-footer">
            <button nz-button nzType="primary" (click)="spanOk()">
              确认
            </button>
          </div>
        </nz-drawer>
      </ul>
    </div>

    <div class="setmodal">
      <div class="setmodal_top">
        <span style="color: #17314C;font-weight: bold;">模型设置</span>
        <span class="cur_span" (click)="showRoleModal()">高级设置</span>
      </div>
      <ul class="setmodal_card">
        <li class="card_left">
          <!-- 左侧菜单 -->
          <!-- 新Tip-菜单 -->
          <ng-container *ngIf="hasQuestionnaireTipItems; else oldTipMenus">
            <app-tip-menu
              [menus]="factortable"
              (checkItem)="onCheckTipMenusItem($event)"
              [reportTypes]="checkReportTypes"
              [checkReportType]="checkReportType"
            ></app-tip-menu>
          </ng-container>
          <ng-template #oldTipMenus>
            <div
              class="left_div"
              *ngFor="let item of toolsname; let i = index"
              [ngClass]="showIndex == i ? 'showClass' : ''"
            >
              <span
                nz-tooltip
                [nzTooltipTitle]="item.title.zh_CN"
                style="width: 100px;"
                (click)="getshow(i, item.questionnaireId)"
                >{{ item.title.zh_CN }}</span
              >
            </div>
          </ng-template>
        </li>
        <li class="card_right">
          <!-- 新Tip-标题/简介/查看模型 -->
          <ng-container *ngIf="hasQuestionnaireTipItems; else oldTipHead">
            <ng-container *ngIf="!!checkTipMenuItem">
              <div style="display: flex;justify-content: space-between;">
                <div style="display: flex;align-items: center;">
                  <p class="P_1">
                    {{
                      checkTipMenuItem.questionnaireTipItems
                        ? checkTipMenuItem.surveyStandardQuestionnaire.name
                            .zh_CN
                        : checkTipMenuItem.name.zh_CN
                    }}
                  </p>
                  <ng-container
                    *ngIf="
                      permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:TOOL_NAME_EDIT'
                      )
                    "
                  >
                    <i
                      *ngIf="!checkTipMenuItem.questionnaireTipItems"
                      class="iconfont icon-bianji"
                      style="margin-left: 10px;cursor: pointer;"
                      nzTheme="outline"
                      (click)="EditName()"
                    ></i>
                  </ng-container>
                </div>
                <ng-container
                  *ngIf="
                    permissionService.isPermissionOrSag(
                      'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:UPLOAD_MODULE_IMAGE'
                    )
                  "
                >
                  <div
                    style="color: #409EFF;cursor: pointer;"
                    (click)="getModalPie()"
                  >
                    查看模型
                  </div>
                </ng-container>
              </div>
              <div
                style="margin-top: 10px;"
                [innerHtml]="
                  checkTipMenuItem.questionnaireTipItems
                    ? checkTipMenuItem.surveyStandardQuestionnaire.description
                    : checkTipMenuItem.description
                "
              ></div>
            </ng-container>
          </ng-container>
          <ng-template #oldTipHead>
            <div *ngIf="toolsname.length != 0">
              <div style="display: flex;justify-content: space-between;">
                <div style="display: flex;align-items: center;">
                  <p class="P_1">{{ toolsname[showIndex].title.zh_CN }}</p>
                  <ng-container
                    *ngIf="
                      permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:TOOL_NAME_EDIT'
                      )
                    "
                  >
                    <i
                      class="iconfont icon-bianji"
                      style="margin-left: 10px;cursor: pointer;"
                      nzTheme="outline"
                      (click)="EditName()"
                    ></i>
                  </ng-container>
                </div>
                <ng-container
                  *ngIf="
                    permissionService.isPermissionOrSag(
                      'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:UPLOAD_MODULE_IMAGE'
                    )
                  "
                >
                  <div
                    style="color: #409EFF;cursor: pointer;"
                    (click)="getModalPie()"
                  >
                    查看模型
                  </div>
                </ng-container>
              </div>
              <div
                style="margin-top: 10px;"
                [innerHtml]="toolsname[showIndex].description"
              ></div>
            </div>
          </ng-template>

          <div style="flex: 1;" class="new_right">
            <div class="big_div">
              <!-- 维度指标 -->
              <ng-container *ngIf="hasQuestionnaireTipItems; else oldDimension">
                <ng-container
                  *ngIf="
                    checkTipMenuItem && !!checkTipMenuItem.questionnaireTipItems
                  "
                >
                  <app-tip-type
                    style="width: 100%;"
                    [isdisabled]="
                      projectType == 'ANSWERING' || projectType == 'OVER'
                    "
                    [items]="checkTipMenuItem.questionnaireTipItems"
                    [checked]="checkReportTypes"
                    (checkItem)="onCheckTipTypeItem($event)"
                    (onCheckType)="onCheckType($event)"
                  ></app-tip-type>
                </ng-container>
                <ng-container
                  *ngIf="
                    checkTipMenuItem && !checkTipMenuItem.questionnaireTipItems
                  "
                >
                  <app-tip-dimension
                    [isdisabled]="isdisabled || !isTipJobtip"
                    style="width: 100%;"
                    [dimensions]="checkTipMenuItem.detailedScoreConfigs"
                    [reportType]="checkReportType"
                    (checkItem)="onCheckDimension($event)"
                    [radiolist]="radiolist"
                    [radioCodelist]="radioCodelist"
                  ></app-tip-dimension>
                </ng-container>
              </ng-container>
              <ng-template #oldDimension>
                <div class="all_div" style="flex: 1;">
                  <!-- 空白占位 -->
                  <app-topic-empty
                    (onClick)="onClickbook()"
                    *ngIf="
                      !factortable.detailedScoreConfigs?.length &&
                      reportTypes[showIndex] == 'BLANK_CUSTOM'
                    "
                  >
                  </app-topic-empty>
                  <ng-container
                    *ngIf="
                      factortable.surveyStandardQuestionnaire?.cornerMark !==
                        '';
                      else elseTempTab
                    "
                  >
                    <ng-container
                      *ngIf="
                        factortable.surveyStandardQuestionnaire?.reportType.includes(
                          'MCA'
                        );
                        else elseNoMCATemlTab
                      "
                    >
                      <div class="top_tab">
                        <ul class="top_ul_subsrcript">
                          <li class="bot_bod" style="display:inline-block">
                            {{
                              factortable.surveyStandardQuestionnaire
                                ?.cornerMark
                            }}
                          </li>
                        </ul>
                      </div>
                    </ng-container>
                    <ng-template #elseNoMCATemlTab>
                      <div
                        class="top_tab"
                        *ngIf="factortable.dimensionLevelEnumsZH?.length != 0"
                      >
                        <ul class="top_ul">
                          <li
                            *ngFor="
                              let item of factortable.dimensionLevelEnumsZH;
                              let i = index
                            "
                            [ngClass]="chooseIndex == i ? 'bot_bod' : ''"
                            (click)="getchoose(i, item.code)"
                          >
                            {{ item.name }}
                          </li>
                        </ul>
                      </div>
                    </ng-template>
                  </ng-container>
                  <ng-template #elseTempTab>
                    <div
                      class="top_tab"
                      *ngIf="factortable.dimensionLevelEnumsZH?.length != 0"
                    >
                      <ul class="top_ul">
                        <li
                          *ngFor="
                            let item of factortable.dimensionLevelEnumsZH;
                            let i = index
                          "
                          [ngClass]="chooseIndex == i ? 'bot_bod' : ''"
                          (click)="getchoose(i, item.code)"
                        >
                          {{ item.name }}
                        </li>
                      </ul>
                    </div>
                  </ng-template>
                  <ul
                    class="ca_tip"
                    *ngIf="
                      factortable.surveyStandardQuestionnaire?.reportType ==
                        'CA' ||
                      standardReportType == 'TIP_NEW_2' ||
                      standardReportType == 'TIP_NEW'
                    "
                  >
                    <li class="ca_disp disp_8">
                      <div class="disp_t">
                        <div class="rantan"></div>
                        <div>推荐指标</div>
                      </div>
                      <div class="disp_r">
                        已选：<span *ngIf="radiolist.length != 0 && jobcodeshow"
                          >{{ radiolist[0]?.name.zh_CN }}岗位；</span
                        >
                        <span
                          *ngIf="radioCodelist.length != 0 && jobcodeshow"
                          >{{ radioCodelist[0]?.name }}</span
                        >
                      </div>
                    </li>
                    <li class="ca_disp disp_2">
                      <div
                        class="custom_disp"
                        *ngIf="!isdisabled && standardReportType == 'TIP_NEW_2'"
                        (click)="customfactor()"
                      >
                        + 自定义
                      </div>
                      <div
                        class="line_disp"
                        *ngIf="
                          standardReportType == 'TIP_NEW_2' ||
                          standardReportType == 'TIP_NEW'
                        "
                      ></div>
                      <div
                        class="clear_disp"
                        *ngIf="!isUpdateing"
                        (click)="clearcode()"
                      >
                        清空条件
                      </div>
                    </li>
                  </ul>
                  <nz-checkbox-wrapper
                    (nzOnChange)="WrapperOnChange($event)"
                    class="list_ul"
                    style="flex: 1;"
                    *ngIf="!!factortable.detailedScoreConfigs?.length"
                  >
                    <ul
                      [ngClass]="
                        factortable.dimensionLevelEnumsZH?.length != 0
                          ? 'no_top'
                          : 'radius_bod'
                      "
                    >
                      <li class="list_left" style="padding: 0 10px;">
                        <ng-container
                          *ngFor="
                            let item of factortable.detailedScoreConfigs;
                            let i = index
                          "
                        >
                          <div
                            class="left_div"
                            [hidden]="
                              this.standardReportType == 'TIP_NEW_2' &&
                              !item.showfactor
                            "
                          >
                            <div
                              *ngIf="item.name.zh_CN"
                              style="font-weight: bold;"
                            >
                              <span
                                class="name_span"
                                nz-tooltip
                                [(nzTooltipTitle)]="item.name.zh_CN"
                                *ngIf="item.name.zh_CN.length > 5"
                                >{{ item.name.zh_CN }}</span
                              >
                              <span
                                class="name_span"
                                *ngIf="item.name.zh_CN.length <= 5"
                                >{{ item.name.zh_CN }}</span
                              >
                            </div>
                            <div
                              class="son_div"
                              *ngIf="
                                factortable.dimensionLevelEnumsZH?.length != 0
                              "
                            >
                              <div
                                *ngFor="
                                  let val of item.detailedScoreChildDimensions;
                                  let j = index
                                "
                              >
                                <div
                                  [hidden]="
                                    val.dimensionLevelEnum != chooseName
                                  "
                                  style="margin: 0 10px;"
                                >
                                  <label
                                    *ngIf="item.isSelect"
                                    [nzDisabled]="isdisabled"
                                    nz-checkbox
                                    [nzValue]="val.code"
                                    style="display: flex;align-items: center;"
                                    (ngModelChange)="
                                      Modelchange($event, val, val.code)
                                    "
                                    [(ngModel)]="val.isSelected"
                                  >
                                    <ng-container
                                      *ngIf="val.description; else elseTemp"
                                    >
                                      <span
                                        class="name_span"
                                        nz-tooltip
                                        [nzTooltipTitle]="
                                          val.name.zh_CN +
                                          '：' +
                                          val.description.zh_CN
                                        "
                                        >{{ val.name.zh_CN }}</span
                                      >
                                    </ng-container>
                                    <ng-template #elseTemp>
                                      <span
                                        class="name_span"
                                        nz-tooltip
                                        [(nzTooltipTitle)]="val.name.zh_CN"
                                        *ngIf="val.name.zh_CN.length > 5"
                                        >{{ val.name.zh_CN }}</span
                                      >
                                      <span
                                        class="name_span"
                                        *ngIf="val.name.zh_CN.length <= 5"
                                        >{{ val.name.zh_CN }}</span
                                      >
                                    </ng-template>
                                  </label>
                                  <span
                                    class="name_span"
                                    nz-tooltip
                                    [(nzTooltipTitle)]="val.name.zh_CN"
                                    *ngIf="
                                      !item.isSelect &&
                                      val.name.zh_CN.length > 5
                                    "
                                    >{{ val.name.zh_CN }}</span
                                  >
                                  <span
                                    class="name_span"
                                    *ngIf="
                                      !item.isSelect &&
                                      val.name.zh_CN.length <= 5
                                    "
                                    >{{ val.name.zh_CN }}</span
                                  >
                                </div>
                              </div>
                            </div>
                            <div
                              class="son_div"
                              *ngIf="
                                factortable.dimensionLevelEnumsZH?.length == 0
                              "
                            >
                              <div
                                *ngFor="
                                  let val of item.detailedScoreChildDimensions;
                                  let j = index
                                "
                              >
                                <div style="margin:0 10px;width: 108px;">
                                  <label
                                    *ngIf="item.isSelect"
                                    [nzDisabled]="
                                      isdisabled ||
                                      (TipJobtip.length != 0 &&
                                        !TipJobtip[0].isConfirmed)
                                    "
                                    nz-checkbox
                                    [nzValue]="val.code"
                                    style="display: flex;align-items: center;"
                                    (ngModelChange)="
                                      Modelchange($event, item, val.code)
                                    "
                                    [(ngModel)]="val.isSelected"
                                  >
                                    <ng-container
                                      *ngIf="val.description; else elseTemp"
                                    >
                                      <span
                                        class="name_span"
                                        nz-tooltip
                                        [ngStyle]="{ color: val.color }"
                                        [nzTooltipTitle]="
                                          val.name.zh_CN +
                                          '：' +
                                          val.description.zh_CN
                                        "
                                        >{{ val.name.zh_CN }}</span
                                      >
                                    </ng-container>
                                    <ng-template #elseTemp>
                                      <span
                                        class="name_span"
                                        nz-tooltip
                                        [(nzTooltipTitle)]="val.name.zh_CN"
                                        [ngStyle]="{ color: val.color }"
                                        *ngIf="val.name.zh_CN.length > 5"
                                        >{{ val.name.zh_CN }}</span
                                      >
                                      <span
                                        class="name_span"
                                        [ngStyle]="{ color: val.color }"
                                        *ngIf="val.name.zh_CN.length <= 5"
                                        >{{ val.name.zh_CN }}</span
                                      >
                                    </ng-template>
                                  </label>
                                  <span
                                    class="name_span"
                                    nz-tooltip
                                    [(nzTooltipTitle)]="val.name.zh_CN"
                                    *ngIf="
                                      !item.isSelect &&
                                      val.name.zh_CN.length > 5
                                    "
                                    >{{ val.name.zh_CN }}</span
                                  >
                                  <span
                                    class="name_span"
                                    *ngIf="
                                      !item.isSelect &&
                                      val.name.zh_CN.length <= 5
                                    "
                                    >{{ val.name.zh_CN }}</span
                                  >
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                      </li>
                    </ul>
                  </nz-checkbox-wrapper>
                </div>
              </ng-template>
              <!-- 关联任务 -->
              <div class="small_tips">
                <div
                  class="all_tips"
                  style="margin-top: 10px;"
                  *ngIf="
                    descTip.length != 0 ||
                    atTip.length != 0 ||
                    TipJobtip.length != 0
                  "
                >
                  <div class="name_mod">
                    <span class="diss_span">关联任务</span>
                  </div>
                  <!-- At-设置考试问卷 -->
                  <app-task-card
                    *ngIf="
                      question_book?.length != 0 &&
                      permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:QUESTION'
                      )
                    "
                    text="定制您的专属题本"
                    btnText="定制题本"
                    [isConfirmed]="question_book[0].isConfirmed"
                    (onClick)="onClickbook()"
                  ></app-task-card>
                  <app-task-card
                    *ngIf="
                      prismaData.isEnableScaleExtend &&
                      reportTypes[showIndex] == 'BLANK_CUSTOM' && 
                      permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:SCALE_EXTENSION'
                      )
                    "
                    text="开启题本强制排序"
                    btnText="量表尺度扩展"
                    [showConfirmed]="false"
                    (onClick)="showScaleExpansionMode()"
                  ></app-task-card>
                  <app-task-card
                    *ngIf="
                      prismaData.isEnableQuestionBookDistribute &&
                      reportTypes[showIndex] == 'BLANK_CUSTOM' &&
                      permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                      )
                    "
                    text="设置题本分发关系"
                    btnText="题本分发"
                    [showConfirmed]="false"
                    (onClick)="showTopicDistributionMode()"
                  ></app-task-card>
                  <app-task-card
                    *ngIf="atTip?.length != 0 && permissionService.isPermissionOrSag(
                      'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:SET_EXAM_QUESTIONNAIRE'
                    )"
                    text="自定义选择试卷模版"
                    btnText="设置考试问卷"
                    [isConfirmed]="atTip[0].isConfirmed"
                    (onClick)="showModalExam()"
                  ></app-task-card>
                  <app-task-card
                    *ngIf="TipJobtip?.length != 0 && permissionService.isPermissionOrSag(
                      'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:BENCHMARKING_POSIT'
                    )"
                    text="创建岗位/层级模型"
                    btnText="对标岗位/层级"
                    [isConfirmed]="TipJobtip[0].isConfirmed"
                    (onClick)="getmodaljob()"
                  ></app-task-card>
                  <app-task-card
                    *ngIf="
                      descTip.length != 0 &&
                      permissionService.isPermissionOrSag(
                        'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:ANSWER_DESCRIPTION'
                      )
                    "
                    text="自定义填答指导语"
                    btnText="填答指导"
                    [isConfirmed]="descTip[0].isConfirmed"
                    (onClick)="showModalDesc()"
                  ></app-task-card>
                </div>
                <ng-container
                  *ngIf="hasQuestionnaireTipItems; else oldDimensionslist"
                >
                  <div
                    class="all_tips"
                    style="margin-top: 10px;"
                    *ngIf="
                      checkTipMenuItem?.reportType &&
                      tipCurrentDimensions.length > 0
                    "
                  >
                    <div class="name_mod">
                      <!-- 关键指标 -->
                      <span class="diss_span">关键指标</span>
                    </div>
                    <div class="choose_name_mod" style="padding: 10px;">
                      <ul class="choose_name">
                        <div
                          *ngFor="
                            let item of tipCurrentDimensions;
                            let i = index
                          "
                        >
                          <li
                            class="names_div_li"
                            nz-tooltip
                            [nzTooltipTitle]="item.name"
                          >
                            {{ item.name }}
                          </li>
                        </div>
                      </ul>
                    </div>
                  </div>
                </ng-container>
                <ng-template #oldDimensionslist>
                  <div
                    class="all_tips"
                    style="margin-top: 10px;"
                    *ngIf="showprojectReportlist?.length != 0"
                  >
                    <div class="name_mod">
                      <span class="diss_span">关键指标</span>
                    </div>
                    <div class="choose_name_mod" style="padding: 10px;">
                      <ul class="choose_name">
                        <div
                          *ngFor="
                            let item of projectReportDimensionslist;
                            let i = index
                          "
                        >
                          <li
                            class="names_div_li"
                            nz-tooltip
                            [nzTooltipTitle]="item.name"
                            *ngIf="
                              item.standardQuestionnaireId ==
                              standardQuestionnaireId
                            "
                          >
                            {{ item.name }}
                          </li>
                        </div>
                      </ul>
                    </div>
                  </div>
                </ng-template>
                <div style="width: 190px;"></div>
              </div>
              <!-- 产品模型 -->
              <nz-drawer
                nzWrapClassName="round-right-drawer3-nofooter"
                [(nzVisible)]="isVisiblemodal"
                nzTitle="产品模型"
                (nzOnClose)="modalCancel()"
                [nzWidth]="1000"
              >
                <div
                  style="display: flex; justify-content: center; align-items: center; height: 100%;"
                >
                  <ng-container
                    *ngIf="
                      factortable.surveyStandardSagReportTemplate?.modelType ==
                      'DYNAMIC'
                    "
                  >
                    <ng-container *ngIf="echartData.length != 0">
                      <app-echarts-pie
                        [data]="echartData"
                        [containerWidth]="'1000'"
                        [containerHeight]="'750'"
                        [sunecharts]="false"
                      >
                      </app-echarts-pie>
                    </ng-container>
                    <ng-container *ngIf="echartData.length == 0">
                      <nz-empty></nz-empty>
                    </ng-container>
                  </ng-container>
                  <ng-container
                    *ngIf="
                      factortable.surveyStandardSagReportTemplate?.modelType ==
                      'STATIC'
                    "
                  >
                    <ng-container
                      *ngIf="
                        factortable.surveyStandardSagReportTemplate
                          ?.modelImageUrl != ''
                      "
                    >
                      <img [attr.src]="modelImageUrl" alt="" />
                    </ng-container>
                    <ng-container
                      *ngIf="
                        factortable.surveyStandardSagReportTemplate
                          ?.modelImageUrl == ''
                      "
                    >
                      <app-echarts-pie
                        *ngIf="echartData.length != 0"
                        [data]="echartData"
                        [containerWidth]="'1000'"
                        [containerHeight]="'600'"
                        [sunecharts]="false"
                      >
                      </app-echarts-pie>
                      <ng-container *ngIf="echartData.length == 0">
                        <nz-empty></nz-empty>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </div>
              </nz-drawer>
              <!-- 对标岗位 -->
              <nz-drawer
                [(nzVisible)]="visibleJob"
                [nzTitle]="modalTitle"
                (nzOnClose)="handlejob()"
                [nzWidth]="800"
                nzWrapClassName="round-right-drawer3"
              >
                <ng-template #modalTitle>
                  <div class="modalTitle">
                    <div class="titleName">
                      对标岗位
                    </div>
                    <div
                      class="boxName"
                      *ngIf="
                        factortable.surveyStandardQuestionnaire?.reportType ==
                          'CA' || includeCa
                      "
                    >
                      <label
                        [nzDisabled]="
                          projectType == 'ANSWERING' || projectType == 'OVER'
                        "
                        nz-checkbox
                        [(ngModel)]="checkedvalue"
                        (ngModelChange)="ngModelSelect($event)"
                        >自选模型（以下都不匹配，您可自选或联系管理员定制）</label
                      >
                    </div>
                  </div>
                </ng-template>
                <div>
                  <p style="padding: 10px 0;font-size: 20px;">岗位</p>
                  <ul>
                    <nz-radio-group
                      [(ngModel)]="radioValue"
                      [nzDisabled]="
                        projectType == 'ANSWERING' || projectType == 'OVER'
                      "
                      (ngModelChange)="nzOnJobStype()"
                      class="radioul"
                    >
                      <li
                        *ngFor="let item of Tipjobslist; let i = index"
                        style="width: 120px;"
                      >
                        <label [nzValue]="item.id" nz-radio>{{
                          item.name.zh_CN
                        }}</label>
                      </li>
                    </nz-radio-group>
                  </ul>
                </div>
                <div
                  *ngIf="
                    factortable.surveyStandardQuestionnaire?.reportType ==
                      'CA' || includeCa
                  "
                >
                  <p style="padding: 10px 0;font-size: 20px;margin-top: 30px;">
                    层级
                  </p>
                  <ul>
                    <nz-radio-group
                      [(ngModel)]="radioCode"
                      [nzDisabled]="
                        projectType == 'ANSWERING' || projectType == 'OVER'
                      "
                      (ngModelChange)="nzOnCodeStype()"
                      class="radioul"
                    >
                      <li
                        *ngFor="
                          let item of factortable.dimensionLevelEnumsZHnew;
                          let i = index
                        "
                        style="width: 120px;"
                      >
                        <label [nzValue]="item.code" nz-radio>{{
                          item.name
                        }}</label>
                      </li>
                    </nz-radio-group>
                  </ul>
                </div>
                <!-- <ng-template #modalFooter>
                  <div class="footer" (click)="handleOk()">
                    确认
                  </div>
                </ng-template> -->
                <div class="drawer-footer">
                  <button
                    nz-button
                    nzType="primary"
                    (click)="handleOk()"
                    [nzLoading]="isSpinning"
                  >
                    确认
                  </button>
                </div>
              </nz-drawer>
              <!-- 作答说明 -->
              <nz-drawer
                nzTitle="作答说明"
                [(nzVisible)]="visibleDesc"
                nzWrapClassName="round-right-drawer3"
                [nzWidth]="600"
                (nzOnClose)="cancelModalDesc()"
              >
                <!-- <div class="example" *ngIf="nzSimple">
                <nz-spin nzSimple></nz-spin>
              </div> -->
                <!-- AT自适应 -->
                <ng-container
                  *ngIf="
                    factortable.surveyStandardQuestionnaire?.reportType ==
                      'AT' && atMode != 'FIX'
                  "
                >
                  <nz-card style="height: 500px;">
                    <div [innerHTML]="atAdaptiveDesc"></div>
                  </nz-card>
                </ng-container>
                <!-- 其他 -->
                <ng-container
                  *ngIf="
                    factortable.surveyStandardQuestionnaire?.reportType !=
                      'AT' || atMode == 'FIX'
                  "
                >
                  <app-i18n-select
                    [active]="lan"
                    (selectChange)="onSelectI18n($event)"
                  ></app-i18n-select>
                  <ng-container *ngFor="let item of i18n">
                    <div
                      [hidden]="lan !== item.value"
                      style="min-height: 500px; margin-top: 16px;"
                    >
                      <tinymce
                        [config]="tinyconfig"
                        id="formula-textareanew"
                        *ngIf="visibleDesc"
                        [(ngModel)]="prismaData.answerDescription[item.value]"
                        delay="10"
                      >
                      </tinymce>
                    </div>
                  </ng-container>
                </ng-container>
                <div class="drawer-footer">
                  <button
                    nz-button
                    style="margin-right: 8px;"
                    (click)="setDescDefault(true)"
                    *ngIf="
                      factortable.surveyStandardQuestionnaire?.reportType !=
                        'AT' || atMode == 'FIX'
                    "
                  >
                    恢复默认
                  </button>
                  <button nz-button nzType="primary" (click)="okModalDesc()">
                    确认
                  </button>
                </div>
              </nz-drawer>
              <!-- 维度选择提示 -->
              <nz-modal
                [(nzVisible)]="factorshow"
                [nzFooter]="null"
                nzTitle="维度选择提示"
                (nzOnCancel)="factorOk()"
                nzCancelDisabled="true"
              >
                <div>
                  子维度最多选择{{
                    factortable.surveyStandardSagReportTemplate
                      ?.maxDimensionNum
                  }}个！
                </div>
                <div style="display: flex;justify-content: flex-end;">
                  <button nz-button nzType="primary" (click)="factorOk()">
                    确认
                  </button>
                </div>
              </nz-modal>
              <!-- 维度分类 -->
              <nz-modal
                [(nzVisible)]="customfactorshow"
                nzKeyboard="false"
                nzMaskClosable="false"
                nzClosable="false"
                nzCancelText="恢复默认"
                nzOkText="保存设置"
                nzTitle="维度分类"
                (nzOnOk)="customOk()"
                (nzOnCancel)="customCancel()"
              >
                <div class="news_windows">
                  <nz-checkbox-wrapper
                    (nzOnChange)="gettotalmoney($event)"
                    style="width: 100%;"
                  >
                    <div
                      *ngFor="
                        let item of factortable.detailedScoreConfigs;
                        let i = index
                      "
                      style="display: flex;align-items: center;justify-content: space-between;width: 100%;"
                    >
                      <label
                        nz-checkbox
                        [(nzValue)]="item.reportType"
                        [(ngModel)]="item.checked"
                      >
                        {{ item.name.zh_CN }}
                      </label>
                      <div
                        style="margin-top: -4px;"
                        *ngIf="!item.checked && DifferencePrice"
                      >
                        售价：<span style="color: #17314C;font-size: 20px;">{{
                          (DifferencePrice / pricenums).toFixed(2)
                        }}</span>
                        K米/账号
                      </div>
                    </div>
                  </nz-checkbox-wrapper>
                </div>
                <div style="color: #495970;"></div>
              </nz-modal>
              <!-- 修改工具名称 -->
              <nz-drawer
                [(nzVisible)]="showname"
                [nzWidth]="440"
                nzWrapClassName="round-right-drawer3"
                nzTitle="修改工具名称"
                (nzOnClose)="namehandleCancel()"
              >
                <div>
                  <p>活动工具名称(中)</p>
                  <input
                    nz-input
                    placeholder="活动工具名称"
                    [(ngModel)]="changeeditorName.zh_CN"
                  />
                </div>
                <div style="margin-top: 20px;">
                  <p>活动工具名称(eng)</p>
                  <input
                    nz-input
                    placeholder="Activity tool name"
                    [(ngModel)]="changeeditorName.en_US"
                  />
                </div>
                <div class="drawer-footer">
                  <button nz-button nzType="primary" (click)="namehandleOk()">
                    确认
                  </button>
                </div>
              </nz-drawer>
              <!-- AT-设置考试问卷 -->
              <nz-drawer
                [(nzVisible)]="visibleExam"
                [nzWidth]="960"
                nzMaskClosable="false"
                (nzOnClose)="cancelModalExam()"
                [nzWrapClassName]="
                  isEditAT
                    ? 'round-right-drawer3'
                    : 'round-right-drawer3-nofooter'
                "
                [nzTitle]="examTitle"
              >
                <ng-template #examTitle>
                  <div class="modalTitle">
                    <div class="titleName">
                      设置考试问卷
                      <span *ngIf="isOpenAdaption">
                        切换试卷后已作答的测试数据会自动清除
                      </span>
                    </div>
                  </div>
                </ng-template>
                <div class="exam">
                  <div class="exam_content">
                    <!-- 试卷 -->
                    <div class="exam_content_top">
                      <!-- 固定试卷 -->
                      <!-- 开启自适应 -->
                      <ng-container *ngIf="isOpenAdaption">
                        <div
                          [ngClass]="atMode == 'FIX' ? 'boxActive' : 'box'"
                          (click)="changeExamType('FIX')"
                        >
                          <div
                            class="exam_content_top_item left"
                            [ngClass]="atMode == 'FIX' ? 'active' : ''"
                          >
                            <div class="exam_title">
                              <h3>固定试卷</h3>
                              <div *ngIf="isEditAT">
                                <span
                                  nz-icon
                                  nzType="check-circle"
                                  nzTheme="fill"
                                  class="exam_title_selected"
                                  *ngIf="atMode === 'FIX'"
                                  [ngStyle]="
                                    isEditAT ? {} : { cursor: 'no-drop' }
                                  "
                                ></span>
                                <div
                                  class="exam_title_unselected"
                                  *ngIf="atMode == 'ADAPTIVE'"
                                  [ngStyle]="
                                    isEditAT ? {} : { cursor: 'no-drop' }
                                  "
                                ></div>
                              </div>
                            </div>
                            <div class="exam_form">
                              <form
                                nz-form
                                [formGroup]="fixdForm"
                                [nzLayout]="'vertical'"
                              >
                                <nz-form-item>
                                  <nz-form-label nzRequired nzFor="difficulty">
                                    <span class="exam_form_label"
                                      >考试难度</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control nzErrorTip="请选择考试难度!">
                                    <nz-radio-group
                                      id="difficulty"
                                      formControlName="difficulty"
                                      [nzDisabled]="!isEditAT"
                                      (ngModelChange)="onFormModeChange()"
                                    >
                                      <label
                                        nz-radio
                                        *ngFor="let item of difficultyOptions"
                                        [nzValue]="item.value"
                                        >{{ item.label }}</label
                                      >
                                    </nz-radio-group>
                                  </nz-form-control>
                                </nz-form-item>
                                <nz-form-item>
                                  <nz-form-label
                                    nzRequired
                                    nzFor="answerTimeLimitMode"
                                  >
                                    <span class="exam_form_label"
                                      >作答时间</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control nzErrorTip="请选择作答时间!">
                                    <nz-radio-group
                                      id="answerTimeLimitMode"
                                      formControlName="answerTimeLimitMode"
                                      (ngModelChange)="
                                        answerTimeLimitModeChange($event)
                                      "
                                      [nzDisabled]="!isEditAT"
                                    >
                                      <label
                                        nz-radio
                                        *ngFor="
                                          let item of answerTimeLimitModeOptions
                                        "
                                        [nzValue]="item.value"
                                        >{{ item.label }}</label
                                      >
                                    </nz-radio-group>
                                  </nz-form-control>
                                  <div
                                    class="exam_form_list"
                                    *ngIf="
                                      fixdForm.get('answerTimeLimitMode')
                                        ?.value != 'NONE'
                                    "
                                  >
                                    <ng-container
                                      *ngIf="
                                        fixdForm.get('answerTimeLimitMode')
                                          ?.value == 'FULL_QUESTIONNAIRE'
                                      "
                                    >
                                      <div class="exam_form_list_item">
                                        <nz-select
                                          id="days"
                                          formControlName="days"
                                          style="width: 60px"
                                          [nzDisabled]="!isEditAT"
                                          (ngModelChange)="onFormModeChange()"
                                        >
                                          <nz-option
                                            *ngFor="let item of nums_10"
                                            [nzValue]="item"
                                            [nzLabel]="item"
                                          >
                                          </nz-option>
                                        </nz-select>
                                        <span>天</span>
                                      </div>
                                    </ng-container>
                                    <div class="exam_form_list_item">
                                      <nz-select
                                        id="hours"
                                        formControlName="hours"
                                        style="width: 60px"
                                        [nzDisabled]="!isEditAT"
                                        (ngModelChange)="onFormModeChange()"
                                      >
                                        <nz-option
                                          *ngFor="let item of nums_24"
                                          [nzValue]="item"
                                          [nzLabel]="item"
                                        >
                                        </nz-option>
                                      </nz-select>
                                      <span>时</span>
                                    </div>
                                    <div class="exam_form_list_item">
                                      <nz-select
                                        id="minutes"
                                        formControlName="minutes"
                                        style="width: 60px"
                                        [nzDisabled]="!isEditAT"
                                        (ngModelChange)="onFormModeChange()"
                                      >
                                        <nz-option
                                          *ngFor="let item of nums_60"
                                          [nzValue]="item"
                                          [nzLabel]="item"
                                        >
                                        </nz-option>
                                      </nz-select>
                                      <span>分</span>
                                    </div>
                                    <div class="exam_form_list_item">
                                      <nz-select
                                        id="seconds"
                                        formControlName="seconds"
                                        style="width: 60px"
                                        [nzDisabled]="!isEditAT"
                                        (ngModelChange)="onFormModeChange()"
                                      >
                                        <nz-option
                                          *ngFor="let item of nums_60"
                                          [nzValue]="item"
                                          [nzLabel]="item"
                                        >
                                        </nz-option>
                                      </nz-select>
                                      <span>秒</span>
                                    </div>
                                  </div>
                                </nz-form-item>
                                <nz-form-item>
                                  <nz-form-label nzFor="other">
                                    <span class="exam_form_label"
                                      >其他设置
                                      <span>
                                        <!-- 每题/整卷限时 *一页一题作答顺序，请到高级设置
                                        不限时间 *每页题数和作答顺序，请到高级设置 -->
                                        <span
                                          *ngIf="
                                            fixdForm.get('answerTimeLimitMode')
                                              ?.value != 'NONE'
                                          "
                                          >*一页一题作答顺序，请到高级设置</span
                                        >
                                        <span
                                          *ngIf="
                                            fixdForm.get('answerTimeLimitMode')
                                              ?.value == 'NONE'
                                          "
                                          >*每页题数和作答顺序，请到高级设置</span
                                        >
                                        <i
                                          nz-icon
                                          nzType="question-circle"
                                          nzTheme="outline"
                                          class="exam_form_label_icon"
                                          nz-popover
                                          nzPopoverPlacement="bottom"
                                          [nzPopoverContent]="fixed_setting"
                                        ></i>
                                      </span>
                                      <ng-template #fixed_setting>
                                        <div class="exam_form_label_tip">
                                          <img
                                            src="assets/images/at_exam_fixed_setting.png"
                                            alt=""
                                          />
                                        </div>
                                      </ng-template>
                                    </span>
                                  </nz-form-label>
                                  <div class="exam_form_col">
                                    <div class="exam_form_col_item">
                                      <nz-form-control>
                                        <nz-switch
                                          id="isRandomSampleQuestions"
                                          formControlName="isRandomSampleQuestions"
                                          [nzDisabled]="!isEditAT"
                                          (ngModelChange)="onFormModeChange()"
                                        ></nz-switch>
                                      </nz-form-control>
                                      <span>在题库中随机抽题</span>
                                    </div>
                                    <div class="exam_form_col_item">
                                      <nz-form-control>
                                        <nz-switch
                                          id="isCannotAnswerBack"
                                          formControlName="isCannotAnswerBack"
                                          [nzDisabled]="
                                            !isEditAT ||
                                            fixdForm.get('answerTimeLimitMode')
                                              ?.value == 'PER_QUESTION'
                                          "
                                          (ngModelChange)="onFormModeChange()"
                                        ></nz-switch>
                                      </nz-form-control>
                                      <span>进入下一题后不可逆</span>
                                    </div>
                                  </div>
                                </nz-form-item>
                              </form>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                      <!-- 关闭自适应 -->
                      <ng-container *ngIf="!isOpenAdaption">
                        <div class="box">
                          <div class="exam_content_top_item left">
                            <div class="exam_title">
                              <h3>固定试卷</h3>
                            </div>
                            <div class="exam_form">
                              <form
                                nz-form
                                [formGroup]="fixdForm"
                                [nzLayout]="'vertical'"
                              >
                                <nz-form-item>
                                  <nz-form-label nzRequired nzFor="difficulty">
                                    <span class="exam_form_label"
                                      >考试难度</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control nzErrorTip="请选择考试难度!">
                                    <nz-radio-group
                                      id="difficulty"
                                      formControlName="difficulty"
                                      [nzDisabled]="!isEditAT"
                                      (ngModelChange)="onFormModeChange()"
                                    >
                                      <label
                                        nz-radio
                                        *ngFor="let item of difficultyOptions"
                                        [nzValue]="item.value"
                                        >{{ item.label }}</label
                                      >
                                    </nz-radio-group>
                                  </nz-form-control>
                                </nz-form-item>
                                <nz-form-item>
                                  <nz-form-label
                                    nzRequired
                                    nzFor="answerTimeLimitMode"
                                  >
                                    <span class="exam_form_label"
                                      >作答时间</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control nzErrorTip="请选择作答时间!">
                                    <nz-radio-group
                                      id="answerTimeLimitMode"
                                      formControlName="answerTimeLimitMode"
                                      (ngModelChange)="
                                        answerTimeLimitModeChange($event)
                                      "
                                      [nzDisabled]="!isEditAT"
                                    >
                                      <label
                                        nz-radio
                                        *ngFor="
                                          let item of answerTimeLimitModeOptions
                                        "
                                        [nzValue]="item.value"
                                        >{{ item.label }}</label
                                      >
                                    </nz-radio-group>
                                  </nz-form-control>
                                  <div
                                    class="exam_form_list"
                                    *ngIf="
                                      fixdForm.get('answerTimeLimitMode')
                                        ?.value != 'NONE'
                                    "
                                  >
                                    <ng-container
                                      *ngIf="
                                        fixdForm.get('answerTimeLimitMode')
                                          ?.value == 'FULL_QUESTIONNAIRE'
                                      "
                                    >
                                      <div class="exam_form_list_item">
                                        <nz-select
                                          id="days"
                                          formControlName="days"
                                          style="width: 60px"
                                          [nzDisabled]="!isEditAT"
                                          (ngModelChange)="onFormModeChange()"
                                        >
                                          <nz-option
                                            *ngFor="let item of nums_10"
                                            [nzValue]="item"
                                            [nzLabel]="item"
                                          >
                                          </nz-option>
                                        </nz-select>
                                        <span>天</span>
                                      </div>
                                    </ng-container>
                                    <div class="exam_form_list_item">
                                      <nz-select
                                        id="hours"
                                        formControlName="hours"
                                        style="width: 60px"
                                        [nzDisabled]="!isEditAT"
                                        (ngModelChange)="onFormModeChange()"
                                      >
                                        <nz-option
                                          *ngFor="let item of nums_24"
                                          [nzValue]="item"
                                          [nzLabel]="item"
                                        >
                                        </nz-option>
                                      </nz-select>
                                      <span>时</span>
                                    </div>
                                    <div class="exam_form_list_item">
                                      <nz-select
                                        id="minutes"
                                        formControlName="minutes"
                                        style="width: 60px"
                                        [nzDisabled]="!isEditAT"
                                        (ngModelChange)="onFormModeChange()"
                                      >
                                        <nz-option
                                          *ngFor="let item of nums_60"
                                          [nzValue]="item"
                                          [nzLabel]="item"
                                        >
                                        </nz-option>
                                      </nz-select>
                                      <span>分</span>
                                    </div>
                                    <div class="exam_form_list_item">
                                      <nz-select
                                        id="seconds"
                                        formControlName="seconds"
                                        style="width: 60px"
                                        [nzDisabled]="!isEditAT"
                                        (ngModelChange)="onFormModeChange()"
                                      >
                                        <nz-option
                                          *ngFor="let item of nums_60"
                                          [nzValue]="item"
                                          [nzLabel]="item"
                                        >
                                        </nz-option>
                                      </nz-select>
                                      <span>秒</span>
                                    </div>
                                  </div>
                                </nz-form-item>
                                <nz-form-item>
                                  <nz-form-label nzFor="other">
                                    <span class="exam_form_label"
                                      >其他设置
                                      <span>
                                        <!-- 每题/整卷限时 *一页一题作答顺序，请到高级设置
                                                                            不限时间 *每页题数和作答顺序，请到高级设置 -->
                                        <span
                                          *ngIf="
                                            fixdForm.get('answerTimeLimitMode')
                                              ?.value != 'NONE'
                                          "
                                          >*一页一题作答顺序，请到高级设置</span
                                        >
                                        <span
                                          *ngIf="
                                            fixdForm.get('answerTimeLimitMode')
                                              ?.value == 'NONE'
                                          "
                                          >*每页题数和作答顺序，请到高级设置</span
                                        >
                                        <i
                                          nz-icon
                                          nzType="question-circle"
                                          nzTheme="outline"
                                          class="exam_form_label_icon"
                                          nz-popover
                                          nzPopoverPlacement="bottom"
                                          [nzPopoverContent]="fixed_setting"
                                        ></i>
                                      </span>
                                      <ng-template #fixed_setting>
                                        <div class="exam_form_label_tip">
                                          <img
                                            src="assets/images/at_exam_fixed_setting.png"
                                            alt=""
                                          />
                                        </div>
                                      </ng-template>
                                    </span>
                                  </nz-form-label>
                                  <div class="exam_form_col">
                                    <div class="exam_form_col_item">
                                      <nz-form-control>
                                        <nz-switch
                                          id="isRandomSampleQuestions"
                                          formControlName="isRandomSampleQuestions"
                                          [nzDisabled]="!isEditAT"
                                          (ngModelChange)="onFormModeChange()"
                                        ></nz-switch>
                                      </nz-form-control>
                                      <span>在题库中随机抽题</span>
                                    </div>
                                    <div class="exam_form_col_item">
                                      <nz-form-control>
                                        <nz-switch
                                          id="isCannotAnswerBack"
                                          formControlName="isCannotAnswerBack"
                                          [nzDisabled]="
                                            !isEditAT ||
                                            fixdForm.get('answerTimeLimitMode')
                                              ?.value == 'PER_QUESTION'
                                          "
                                          (ngModelChange)="onFormModeChange()"
                                        ></nz-switch>
                                      </nz-form-control>
                                      <span>进入下一题后不可逆</span>
                                    </div>
                                  </div>
                                </nz-form-item>
                              </form>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                      <!-- 自适应试卷 -->
                      <!-- 开启自适应  -->
                      <ng-container *ngIf="isOpenAdaption">
                        <div
                          [ngClass]="atMode == 'ADAPTIVE' ? 'boxActive' : 'box'"
                          (click)="changeExamType('ADAPTIVE')"
                        >
                          <div
                            class="exam_content_top_item right"
                            [ngClass]="atMode == 'ADAPTIVE' ? 'active' : ''"
                          >
                            <div class="exam_title">
                              <h3>
                                自适应试卷
                                <span
                                  nz-icon
                                  nzType="question-circle"
                                  nzTheme="outline"
                                  class="exam_title_icon"
                                  nz-popover
                                  [nzPopoverContent]="contentTemplate"
                                  nzPopoverPlacement="bottom"
                                ></span>
                                <ng-template #contentTemplate>
                                  <div class="exam_title_tip">
                                    <h3>自适应试卷</h3>
                                    <p>
                                      测验基于项目反应理论，采用计算机自适应的方法。相比固定试卷，能够以较少的作答题目获得更精准的能力评估结果，极大地提升了测验的效率和准确率
                                    </p>
                                    <img
                                      src="assets/images/at_exam_self_adaption.png"
                                      alt=""
                                    />
                                  </div>
                                </ng-template>
                              </h3>
                              <div *ngIf="isEditAT">
                                <span
                                  nz-icon
                                  nzType="check-circle"
                                  nzTheme="fill"
                                  class="exam_title_selected"
                                  *ngIf="atMode === 'ADAPTIVE'"
                                  [ngStyle]="
                                    isEditAT ? {} : { cursor: 'no-drop' }
                                  "
                                ></span>
                                <div
                                  class="exam_title_unselected"
                                  *ngIf="atMode == 'FIX'"
                                  [ngStyle]="
                                    isEditAT ? {} : { cursor: 'no-drop' }
                                  "
                                ></div>
                              </div>
                            </div>

                            <div class="exam_form">
                              <form
                                nz-form
                                [formGroup]="selfAdaptionForm"
                                [nzLayout]="'vertical'"
                              >
                                <nz-form-item>
                                  <nz-form-label
                                    nzRequired
                                    nzFor="initQuestionDifficulty"
                                  >
                                    <span class="exam_form_label"
                                      >初始难度
                                      <span>
                                        *
                                        所选维度作答开始时，第一道题目的难易程度
                                      </span>
                                    </span>
                                  </nz-form-label>
                                  <nz-form-control nzErrorTip="请选择初始难度!">
                                    <checkbox-group-btns
                                      id="initQuestionDifficulty"
                                      disabled="true"
                                      formControlName="initQuestionDifficulty"
                                      [options]="
                                        selfAdaptionOptions.initQuestionDifficultyOption
                                      "
                                      [display]="'label'"
                                      (click)="groupClick()"
                                    >
                                    </checkbox-group-btns>
                                  </nz-form-control>
                                </nz-form-item>
                                <nz-form-item>
                                  <nz-form-label
                                    nzRequired
                                    nzFor="answerTimeLimitMode"
                                  >
                                    <span class="exam_form_label"
                                      >作答时间</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control nzErrorTip="请选择作答时间!">
                                    <checkbox-group-btns
                                      id="answerTimeLimitMode"
                                      disabled="true"
                                      formControlName="answerTimeLimitMode"
                                      [options]="
                                        selfAdaptionOptions.answerTimeLimitMode
                                      "
                                      [display]="'label'"
                                      (click)="groupClick()"
                                    >
                                    </checkbox-group-btns>
                                  </nz-form-control>
                                </nz-form-item>
                                <nz-form-item>
                                  <nz-form-label
                                    nzRequired
                                    nzFor="otherSetting"
                                  >
                                    <span class="exam_form_label"
                                      >其他设置</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control nzErrorTip="请选择其他设置!">
                                    <checkbox-group-btns
                                      id="otherSetting"
                                      disabled="true"
                                      formControlName="otherSetting"
                                      [options]="
                                        selfAdaptionOptions.otherSetting
                                      "
                                      [display]="'label'"
                                      (click)="groupClick()"
                                    >
                                    </checkbox-group-btns>
                                  </nz-form-control>
                                </nz-form-item>
                                <nz-form-item>
                                  <nz-form-label
                                    nzRequired
                                    nzFor="atAdaptiveDefaultAnalysisFactors"
                                  >
                                    <span class="exam_form_label"
                                      >人口信息分析</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control
                                    nzErrorTip="请选择人口信息分析!"
                                  >
                                    <checkbox-group-btns
                                      id="atAdaptiveDefaultAnalysisFactors"
                                      disabled="true"
                                      formControlName="atAdaptiveDefaultAnalysisFactors"
                                      [options]="
                                        selfAdaptionOptions.atAdaptiveDefaultAnalysisFactors
                                      "
                                      [display]="'label'"
                                      (click)="groupClick()"
                                    >
                                    </checkbox-group-btns>
                                  </nz-form-control>
                                </nz-form-item>
                              </form>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                      <!-- 关闭自适应  -->
                      <ng-container *ngIf="!isOpenAdaption">
                        <div class="box">
                          <div class="exam_content_top_item">
                            <div class="exam_title">
                              <h3>防作弊设置</h3>
                            </div>
                            <div class="exam_form">
                              <form
                                nz-form
                                [formGroup]="otherForm"
                                [nzLayout]="'vertical'"
                              >
                                <nz-form-item>
                                  <nz-form-label nzFor="isCanSwitchScreen">
                                    <span class="exam_form_label"
                                      >切换屏幕</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control>
                                    <nz-switch
                                      formControlName="isCanSwitchScreen"
                                      [nzDisabled]="
                                        !(isEditAT && isOtherChange)
                                      "
                                    ></nz-switch>
                                    <span> 只允许考生切换考试界面 </span>
                                    <ng-container
                                      *ngIf="
                                        isEditAT && isOtherChange;
                                        else isOtherChange1
                                      "
                                    >
                                      <nz-select
                                        formControlName="switchScreenTimes"
                                        style="width: 60px"
                                        [nzDisabled]="!isEditAT"
                                        nzSize="small"
                                      >
                                        <nz-option [nzValue]="0" nzLabel="0">
                                        </nz-option>
                                        <nz-option [nzValue]="1" nzLabel="1">
                                        </nz-option>
                                        <nz-option [nzValue]="2" nzLabel="2">
                                        </nz-option>
                                        <nz-option [nzValue]="3" nzLabel="3">
                                        </nz-option>
                                      </nz-select>
                                    </ng-container>
                                    <ng-template #isOtherChange1>{{
                                      otherForm.get("switchScreenTimes")?.value
                                    }}</ng-template>
                                    <span> 次，否则强制交卷且分数记为无效</span>
                                  </nz-form-control>
                                </nz-form-item>
                                <nz-form-item>
                                  <nz-form-label nzFor="isCanReEnter">
                                    <span class="exam_form_label"
                                      >重新登录</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control>
                                    <nz-switch
                                      formControlName="isCanReEnter"
                                      [nzDisabled]="
                                        !(isEditAT && isOtherChange)
                                      "
                                    ></nz-switch>
                                    <span>
                                      允许“退出后重新登录”的时间间隔
                                    </span>
                                    <ng-container
                                      *ngIf="
                                        isEditAT && isOtherChange;
                                        else isOtherChange2
                                      "
                                    >
                                      <nz-select
                                        formControlName="reEnterInterval"
                                        style="width: 60px"
                                        [nzDisabled]="!isEditAT"
                                        nzSize="small"
                                      >
                                        <nz-option [nzValue]="2" nzLabel="2">
                                        </nz-option>
                                        <nz-option [nzValue]="5" nzLabel="5">
                                        </nz-option>
                                        <nz-option [nzValue]="10" nzLabel="10">
                                        </nz-option>
                                        <nz-option [nzValue]="15" nzLabel="15">
                                        </nz-option>
                                        <nz-option [nzValue]="30" nzLabel="30">
                                        </nz-option>
                                      </nz-select>
                                    </ng-container>
                                    <ng-template #isOtherChange2>{{
                                      otherForm.get("reEnterInterval")?.value
                                    }}</ng-template>
                                    <span> 分钟</span>
                                  </nz-form-control>
                                </nz-form-item>
                                <nz-form-item>
                                  <nz-form-label
                                    nzFor="isEnableQuestionProtection"
                                  >
                                    <span class="exam_form_label"
                                      >试题保护</span
                                    >
                                  </nz-form-label>
                                  <nz-form-control>
                                    <nz-switch
                                      formControlName="isEnableQuestionProtection"
                                      [nzDisabled]="!isEditAT"
                                    ></nz-switch>
                                    <span> 启用试题保护（水印，不可分享）</span>
                                  </nz-form-control>
                                </nz-form-item>
                              </form>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>
                    <!-- 开启自适应 -->
                    <ng-container *ngIf="isOpenAdaption">
                      <div class="exam_content_bottom">
                        <p>防作弊设置</p>
                        <form nz-form [formGroup]="otherForm">
                          <div class="exam_content_bottom_item">
                            <nz-switch
                              formControlName="isCanSwitchScreen"
                              [nzDisabled]="!(isEditAT && isOtherChange)"
                            ></nz-switch>
                            <span>只允许考生切换考试界面</span>
                            <ng-container
                              *ngIf="
                                isEditAT && isOtherChange;
                                else isOtherChange1
                              "
                            >
                              <nz-select
                                formControlName="switchScreenTimes"
                                style="width: 60px"
                                [nzDisabled]="!isEditAT"
                              >
                                <nz-option [nzValue]="0" nzLabel="0">
                                </nz-option>
                                <nz-option [nzValue]="1" nzLabel="1">
                                </nz-option>
                                <nz-option [nzValue]="2" nzLabel="2">
                                </nz-option>
                                <nz-option [nzValue]="3" nzLabel="3">
                                </nz-option>
                              </nz-select>
                            </ng-container>
                            <ng-template #isOtherChange1>{{
                              otherForm.get("switchScreenTimes")?.value
                            }}</ng-template>
                            <span>次，否则强制交卷且分数记为无效</span>
                          </div>
                          <div class="exam_content_bottom_item">
                            <nz-switch
                              formControlName="isCanReEnter"
                              [nzDisabled]="!(isEditAT && isOtherChange)"
                            ></nz-switch>
                            <span>允许“退出后重新登录”的时间间隔</span>
                            <ng-container
                              *ngIf="
                                isEditAT && isOtherChange;
                                else isOtherChange2
                              "
                            >
                              <nz-select
                                formControlName="reEnterInterval"
                                style="width: 60px"
                                [nzDisabled]="!isEditAT"
                              >
                                <nz-option [nzValue]="2" nzLabel="2">
                                </nz-option>
                                <nz-option [nzValue]="5" nzLabel="5">
                                </nz-option>
                                <nz-option [nzValue]="10" nzLabel="10">
                                </nz-option>
                                <nz-option [nzValue]="15" nzLabel="15">
                                </nz-option>
                                <nz-option [nzValue]="30" nzLabel="30">
                                </nz-option>
                              </nz-select>
                            </ng-container>
                            <ng-template #isOtherChange2>{{
                              otherForm.get("reEnterInterval")?.value
                            }}</ng-template>
                            <span>分钟</span>
                          </div>
                          <div class="exam_content_bottom_item">
                            <nz-switch
                              formControlName="isEnableQuestionProtection"
                              [nzDisabled]="!isEditAT"
                            ></nz-switch>
                            <span>启用试题保护（水印，不可分享）</span>
                          </div>
                        </form>
                      </div>
                    </ng-container>
                  </div>

                  <div class="drawer-footer" *ngIf="isEditAT">
                    <button nz-button nzType="primary" (click)="okModalExam()">
                      确认
                    </button>
                  </div>
                </div>
              </nz-drawer>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
  <div class="submit_xy">
    <div class="client-width center_menu">
      <div>
        <span style="color: #B2B8C2;"
          >K米将在报告生成时，自动从您的账户中扣除</span
        >
      </div>
      <ul class="menus_xy">
        <li
          class="menus_left"
          (click)="submitSave()"
          nz-button
          [nzLoading]="isNzOkLoading"
          *ngIf="
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:SAVE'
            )
          "
        >
          保存
        </li>
        <li
          class="menus_right_new"
          (click)="submitPreviewSave()"
          *ngIf="
            (projectType == 'ANNOUNCED' || !projectType) &&
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:RELEASE'
            )
          "
          nz-button
          [nzLoading]="isNzPreLoading"
        >
          预发布
        </li>
        <li
          class="menus_right"
          *ngIf="
            !isUpdateing &&
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:RELEASE'
            )
          "
          (click)="submitRelase()"
          nz-button
          [nzLoading]="isNzRELoading"
        >
          发布
        </li>
      </ul>
    </div>
  </div>

  <div
    style="position: fixed;
    top: 0;bottom: 0;left: 0;right: 0;display: flex;justify-content: center;align-items: center;background-color: #eee;opacity: 0.4;z-index: 999999;"
    *ngIf="isSpinning"
  >
    <nz-spin nzSimple [nzSpinning]="isSpinning" [nzSize]="'large'"></nz-spin>
  </div>
</div>

<div class="mock_div " *ngIf="showmock">
  <ul class="bg_ul" *ngIf="showmock"></ul>
  <ul class="img_ul" *ngIf="noviceGuidance">
    <li>
      <div style="position: relative;">
        <img src="assets/images/create_2.png" alt="" />
      </div>
      <div style="margin-top: 20px;cursor: pointer;" (click)="closed()">
        <img src="assets/images/dele_new.png" alt="" />
      </div>
    </li>
  </ul>
</div>

<nz-drawer
  class="nz_modal"
  [(nzVisible)]="addfactorshow"
  [nzWidth]="960"
  nzWrapClassName="round-right-drawer3"
  [(nzBodyStyle)]="nzBodyStyle"
  [nzTitle]="'人口标签-' + analysisFactorTitle"
  (nzOnClose)="handleCancel()"
>
  <app-factors
    *ngIf="addfactorshow"
    [factorlist]="factortable"
    [shownumber]="shownumber"
    [surveyType]="prismaData.surveyType"
    (loadDataMap)="loadDataMap($event)"
    [reportType]="reportTypes[0]"
    [showAnalysisFactor]="showAnalysisFactor"
    [addfactorshow]="addfactorshow"
    [projectId]="projectId"
    (cancelmodal)="handleCancel()"
    (closemodal)="closemodal($event)"
    (getdefaultlist)="getdefaultlist($event)"
    [changeNumber]="changeNumber"
  >
  </app-factors>

  <div
    style="position: absolute;
    width: 100%;
    height: 100%;
    top: 0;display: flex;justify-content: center;align-items: center;background-color: #eee;opacity: 0.4;z-index: 999999;"
    *ngIf="factorisSpinning"
  >
    <nz-spin
      nzSimple
      [nzSpinning]="factorisSpinning"
      [nzSize]="'small'"
    ></nz-spin>
  </div>
  <p></p>
</nz-drawer>
<!-- 量表拓展 -->
<app-scale-expansion
  [questionnaireId]="questionnaireId"
  [projectId]="projectId"
  (onSave)="saveScaleExpansion()"
></app-scale-expansion>
<!-- 题本分发 -->
<app-topic-distribution
  [questionnaireId]="questionnaireId"
  [projectId]="projectId"
  [isdisabled]="isdisabled"
></app-topic-distribution>
