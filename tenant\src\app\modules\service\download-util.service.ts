import { Injectable } from "@angular/core";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { NzMessageService } from "ng-zorro-antd";

@Injectable({
  providedIn: "root",
})
export class DownloadUtilService {
  constructor(
    private msgServ: NzMessageService,
    private customMsg: MessageService
  ) {}

  downFile(data) {
    this.showBlobErrorMessage(data);
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];
    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
      // .split('\'\'')[1]
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  showBlobErrorMessage(data: any) {
    let body = data.body;
    console.log(data);

    if (body.type && body.type === "application/json") {
      let that = this;
      const reader = new FileReader();
      reader.readAsText(body, "utf-8");
      reader.onload = () => {
        // 处理报错信息
        // JSON.parse(reader.result) 拿到报错信息
        let resp: any = JSON.parse(reader.result + "");
        let code: number = resp.result.code;
        let errMsg: string = resp.result.message;
        console.log("errMsg = " + errMsg);
        if (code !== 0) {
          // that.msgServ.error(`${errMsg}，请联系管理员。`);
          that.customMsg.open("error", `${errMsg}，请联系管理员。`);
        }
      };
    }
  }
}
