import { Component, OnInit, ElementRef  } from '@angular/core';
import { FormBuilder } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { Location } from '@angular/common';
import { NzMessageService } from "ng-zorro-antd";
import { Observable } from 'rxjs';
import _ from 'lodash';
import { async } from 'rxjs/internal/scheduler/async';
@Component({
  selector: 'app-document',
  templateUrl: './document.component.html',
  styleUrls: ['./document.component.less']
})
export class DocumentComponent implements OnInit {
  docList: any[];

  mainList: any[] = [];

  commonList: any[] = [];

  currentDoc: any = {}

  currentDirId : string;

  currentDirContent;

  srcPath: string;
  imgHeight: number;
  imgWidth: number;

  isListPage : boolean = true;
  tenantUrl: string = "/tenant-api";

  constructor(
    private fb: FormBuilder,
    private routeInfo: ActivatedRoute, 
    private http: HttpClient,
    private _location: Location,
    private router: Router, 
    private msg: NzMessageService,
    private el: ElementRef<HTMLElement>
    ) { }

  ngOnInit() {

    this.getList().subscribe((res: any) => {
      if (res.result.code === 0) {
        
        this.docList = res.data;

        this.docList = _.filter(this.docList, function (q) {
          return q.status === 'ENABLE';
        });

        for (let index = 0; index < this.docList.length; index++) {
          const doc = this.docList[index];
          if(doc.isCommonFile) {
            this.commonList.push(doc);
          } else {
            this.mainList.push(doc);
          }
        }

        for (let index = 0; index < this.mainList.length; index++) {
          const doc = this.mainList[index];
          let seq = index % 7;
          doc.bg = `url('assets/images/doc/p${seq}.png') no-repeat`
        }
      }
      
    });
  }

  public getList(): Observable<any> {
    let api = this.tenantUrl + "/survey/standard/documentManagement/listAllNoConetnts"
    let param = { "searchField": "" };
    return this.http.post(api, param);
  }
  public getDirectoryContent(id): Observable<any> {
    let api = this.tenantUrl + "/survey/standard/documentManagement/getDirectoryContent?id=" + id
    return this.http.get(api);
  }

  selectDoc(doc) {
    console.log(doc)
    this.getDirectoryContent(doc).subscribe(res => {
      this.currentDoc = res.data
      this.selectDir(this.currentDoc.directorys[0]);
    })
    this.isListPage = false;
    window.scrollTo(0, 0);
  }

  selectDir(dir) {
    if(dir) {
      this.currentDirId = dir.id;
      this.currentDirContent = dir.content.zh_CN
      
    } else {
      this.currentDirId = "";
      this.currentDirContent = "";
    }
    
  }

  viewCommon() {
    console.log(this.commonList[0])
    console.log(this.docList[0])
    this.selectDoc(this.commonList[0].id || this.docList[0].id);
  }

  backHome() {
    this.isListPage = true;
  }

}