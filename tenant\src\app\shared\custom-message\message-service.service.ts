import { Injectable, TemplateRef } from "@angular/core";
import { NzMessageDataOptions, NzMessageService } from "ng-zorro-antd/message";
import { Router } from "@angular/router";

@Injectable({
  providedIn: "root",
})
export class MessageService {
  msgCustomTemplate!: TemplateRef<void>;
  msg: string = "";
  msgId: string = "";

  constructor(private message: NzMessageService, private router: Router) {
    // 监听 URL 变化
    this.router.events.subscribe(() => {
      // console.log("Event:", event); // 打印事件以进行调试
      // console.log("Current URL:", this.router.url); // 打印当前 URL
      if (this.msgId) {
        this.message.remove(this.msgId);
      }
    });
  }

  open(
    type: "success" | "info" | "warning" | "error" | "loading" | string,
    msg: string,
    options: NzMessageDataOptions = {
      nzDuration: 0,
      nzAnimate: true,
    }
  ): void {
    this.msg = msg;
    if (this.msgId) {
      this.message.remove(this.msgId);
    }
    const id = this.message.create(type, this.msgCustomTemplate, options)
      .messageId;
    this.msgId = id;
  }
}