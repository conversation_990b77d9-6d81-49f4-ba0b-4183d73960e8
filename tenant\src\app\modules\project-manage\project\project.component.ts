import {
  Component,
  OnInit,
  Input,
  Output,
  ViewChild,
  EventEmitter,
} from "@angular/core";
import { ProjectManageService } from "../../service/project-manage.service";
import { Router, ActivatedRoute } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import {
  NzMessageService,
  NzModalService,
  NzDrawerService,
} from "ng-zorro-antd";
import { SetTypeComponent } from "../set-type/set-type.component";
import { HistoricalComparativeContentComponent } from "../historical-comparative-content/historical-comparative-content.component";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

declare const document: any;
@Component({
  selector: "app-project",
  templateUrl: "./project.component.html",
  styleUrls: ["./project.component.less"],
})
export class ProjectComponent implements OnInit {
  @Input() isSystemInterface: boolean;
  @Input() projectModel: any;
  @Input() ProjectManageHomeComponent;
  @Output() getProjectList = new EventEmitter<any>();
  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "",
      name: "活动管理",
      Highlight: true,
    },
  ];
  permission; //更新租户配置权限
  visible = false;
  showtip = false;
  configtation = false;
  Tipjobslist = [];
  chooselist = [];
  standardReportTemplateJobId = "";
  dimensionLevel = "";
  isSelectedModel = false;
  selectedid = "";
  Customize = false;
  selectedIndex = 0;
  tabslist = [];
  Correctshow = false;
  CorrectNextshow = false; // 矫正维度 下一步
  codelist = [];
  prismaCus: boolean = false;

  questionnaireId: any; // 问卷id

  ParentNameI18nZh;
  ParentNameI18nEn;

  constructor(
    private api: ProjectManageService,
    private router: Router,
    private message: NzMessageService,
    private modalService: NzModalService,
    private drawerService: NzDrawerService,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {}

  // 报告类型设置
  setReportType() {
    this.visible = false;
    const drawer = this.drawerService.create({
      nzTitle: `报告类型（${this.projectModel.projectName.zh_CN}）`,
      nzContent: SetTypeComponent,
      nzContentParams: {
        projectId: this.projectModel.id,
        projectName: this.projectModel.projectName,
      },
      nzWidth: 1000,
      nzMaskClosable: false,
      nzClosable: true,
      nzWrapClassName: "round-right-drawer7",
    });
    drawer.afterClose.subscribe(() => {
      this.getProjectList.emit();
    });
  }

  ngOnInit(): void {
    if (this.projectModel.questionnaires[0].reportType) {
      let arr = this.projectModel.questionnaires[0].reportType.split("_");
      arr.forEach((element) => {
        if (element === "CUSTOM") {
          this.prismaCus = true;
        } else if (
          this.projectModel.questionnaires[0].reportType.indexOf(
            "INVESTIGATION_RESEARCH"
          ) !== -1
        ) {
          this.prismaCus = true;
        } else {
          this.prismaCus = false;
        }
      });
    }
    if (this.projectModel.questionnaires[0].reportType == "BLANK_CUSTOM") {
      this.prismaCus = false;
    }
    this.permission = this.permissionService.isPermission();
  }

  updateProject(json) {
    this.api.updateProject(json).subscribe((res) => {
      if (res.result.code === 0) {
        this.getProjectList.emit();
      }
    });
  }

  interfacePush(data) {
    console.log(data);

    this.api
      .getIsPushThird({ projectId: data.id, isPushThird: !data.isPushThird })
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.getProjectList.emit();
          this.message.success("修改成功");
        }
      });
  }

  CopyActive() {
    this.api.copyproject(this.projectModel.id).subscribe((res) => {
      if (res.result.code == 0) {
        this.message.success("复制成功！");
        this.getProjectList.emit();
      }
    });
  }

  updatePaper(id): void {
    this.api
      .updatePaper(id)
      .toPromise()
      .then((res) => {
        if (res.result.code === 0) {
          this.message.success("更新成功");
        } else {
          // this.message.error(res.result.message);
          this.customMsg.open("error", res.result.message);
        }
      })
      .catch((err) => {})
      .finally(() => {});
  }

  chagnemodify(type) {
    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    if (type == "modify") {
      console.log(this.projectModel);
      if (
        this.projectModel.standardReportType == "PRISMA" ||
        this.projectModel.standardReportType == "INVESTIGATION_RESEARCH" ||
        this.projectModel.standardReportType ==
          "INVESTIGATION_RESEARCH_CUSTOM" ||
        this.projectModel.standardReportType == "CULTURE_INVESTIGATION_RESEARCH"
      ) {
        localStorage.setItem("backurl", this.router.routerState.snapshot.url);
        this.router.navigate(["new-prisma"], {
          queryParams: {
            projectId: this.projectModel.id,
            type: this.projectModel.status,
          },
        });
      } else {
        if (!this.projectModel.is360Project) {
          this.projectModel.standardReportType = this.projectModel
            .standardReportType
            ? this.projectModel.standardReportType
            : "other";
        }
        localStorage.setItem("backurl", this.router.routerState.snapshot.url);
        this.router.navigate(["new-create"], {
          queryParams: {
            projectId: this.projectModel.id,
            projectCode: this.projectModel.code,
            projectType: this.projectModel.status,
            standardQuestionnaireId: this.projectModel.questionnaires[0]
              .standardQuestionnaireId,
            questionnaireId: this.projectModel.questionnaires[0].id,
            standardReportType: this.projectModel.standardReportType,
          },
        });
        if (!this.projectModel.is360Project) {
          sessionStorage.setItem(
            "standardQuestionnaireIds",
            JSON.stringify(this.projectModel.questionnaires)
          );
        }
        localStorage.setItem("noprismadata", null);
      }
    }
    if (type == "update") {
      if (this.projectModel.questionnaires[0].reportType.indexOf("PRISMA") !== -1 ||
        this.projectModel.questionnaires[0].reportType.indexOf("INVESTIGATION_RESEARCH") !== -1) {
         this.router.navigate(["project-manage/tenant-norm-config"], {
          queryParams: { projectId: this.projectModel.id },
        });
      }else{
          this.router.navigate(["project-manage/tenant-config"], {
          queryParams: { projectId: this.projectModel.id },
        });
      }
    }
    if (type == "copy") {
      this.CopyActive();
    }
    if (type == "editor") {
      let q = this.isEditor(this.projectModel.questionnaires);
      const reportType = q.reportType;
      let typeedit = "CUSTOMIZE";
      if (reportType.indexOf("CUSTOMIZE") < 0) {
        typeedit = "STAND";
      }
      // 空白问卷特殊处理
      if (reportType == "BLANK_CUSTOM") {
        typeedit = "CUSTOMIZE";
      }
      this.router.navigate(["custom-book"], {
        queryParams: {
          type: q.projectType, // ?
          projectId: this.projectModel.id,
          questionnaireId: q.id,
          standardQuestionnaireId: q.standardQuestionnaireId,
          standardReportType: q.reportType,
          edittype: typeedit,
          backtype: "home",
          // listChecked: 'checked',
        },
      });
    }
    if (type == "editorprimsa") {
      if (
        this.projectModel.standardReportType === "INVESTIGATION_RESEARCH" ||
        this.projectModel.standardReportType === "OC_INVESTIGATION_RESEARCH"
      ) {
        this.router.navigate(["new-activity/book"], {
          queryParams: {
            projectId: this.projectModel.id,
            projectType: this.projectModel.status,
            questionnaireId: this.projectModel.questionnaires[0].id,
            backtype: "home",
            reportType: this.projectModel.standardReportType,
          },
        });
      } else {
        this.router.navigate(["new-activity/custom-book"], {
          queryParams: {
            projectId: this.projectModel.id,
            questionnaireId: this.projectModel.questionnaires[0].id,
            backtype: "home",
            reportType: this.projectModel.questionnaires[0].reportType,
          },
        });
      }
    }
    if (type == "question") {
      this.updatePaper(this.projectModel.id);
    }
    if (type == "history") {
      this.router.navigate(["project-manage/historical-comparison"], {
        queryParams: {
          projectId: this.projectModel.id,
          type: this.projectModel.status,
          questionnaireId: this.projectModel.questionnaires[0].id,
          standardReportType: this.projectModel.standardReportType,
        },
      });
    }
    if (type == "norm") {
      this.router.navigate(["project-manage/norm"], {
        queryParams: {
          projectId: this.projectModel.id,
        },
      });
    }
    // if (type == 'relationship') {
    //   this.router.navigate(['project-manage/invite360'], {
    //     queryParams: {
    //       projectId: this.projectModel.id,
    //       step: 1,
    //       type: 'home',
    //       standardReportType: this.projectModel.standardReportType,
    //       isCustomRoleWeight: this.projectModel.isCustomRoleWeight
    //     }
    //   });
    // }
    if (type == "jobber") {
      this.showtip = true;
      // standardReportTemplateId
      this.gettipfactor(
        this.projectModel.questionnaires[0].standardReportTemplateId
      );

      let reportTemplateId = this.projectModel.questionnaires[0]
        .reportTemplateId;
      this.checkfactor(reportTemplateId);
    }
    if (type == "configs") {
      this.configtation = true;
      this.tabslist = [];
      if (
        (this.projectModel.isCustomizeProject ||
          this.projectModel.questionnaires[0].reportType.indexOf(
            "INVESTIGATION"
          ) !== -1) &&
        this.projectModel.questionnaires[0].reportType.indexOf(
          "OC_INVESTIGATION_RESEARCH"
        ) === -1
      ) {
        //判断是否自定义
        this.Customize = true;
        if (
          this.projectModel.questionnaires[0].surveyType ==
          "EMPLOYEE_ENGAGEMENT"
        ) {
          this.tabslist = [
            {
              name: "报告自定义",
              surveyType: this.projectModel.questionnaires[0].surveyType,
            },
          ];
        } else {
          this.tabslist = [{ name: "自定义" }];
        }
      } else if (
        this.projectModel.questionnaires[0].reportType.indexOf(
          "DP_INVESTIGATION_RESEARCH_CUSTOM"
        ) === -1 &&
        this.projectModel.questionnaires[0].reportType.indexOf(
          "OC_INVESTIGATION_RESEARCH"
        ) === -1
      ) {
        this.Customize = false;
        this.tabslist = [{ name: "维度解释" }, { name: "维度区间" }];
      }
      if (
        this.projectModel.questionnaires[0].reportType.indexOf("_270") !== -1 ||
        this.projectModel.questionnaires[0].reportType.indexOf("_360") !== -1
      ) {
        this.tabslist.push({
          name: "报告自定义",
          surveyType: this.projectModel.questionnaires[0].surveyType,
        });
      }
      console.log("reportType", this.projectModel.questionnaires[0].reportType);
      if (
        this.projectModel.questionnaires[0].reportType.indexOf(
          "INVESTIGATION"
        ) !== -1 &&
        this.projectModel.questionnaires[0].reportType.indexOf(
          "DP_INVESTIGATION_RESEARCH_CUSTOM"
        ) === -1 &&
        this.projectModel.questionnaires[0].reportType.indexOf(
          "OC_INVESTIGATION_RESEARCH"
        ) === -1
      ) {
        this.tabslist.push({
          name: "题本落地建议",
          surveyType: this.projectModel.questionnaires[0].surveyType,
        });
      }
      if (
        // this.projectModel.questionnaires[0].reportType === "INVESTIGATION_RESEARCH" ||
        // this.projectModel.questionnaires[0].reportType === "INVESTIGATION_RESEARCH_CUSTOM" ||
        this.projectModel.questionnaires[0].reportType ===
          "DP_INVESTIGATION_RESEARCH_CUSTOM" ||
        this.projectModel.questionnaires[0].reportType ===
          "OC_INVESTIGATION_RESEARCH"
      ) {
        this.tabslist.push({
          name: "报告文字自定义",
          surveyType: this.projectModel.questionnaires[0].surveyType,
        });
      }
    }
    this.visible = false;

    let acitvedata: any = JSON.parse(sessionStorage.getItem("activepage"));
    if (acitvedata) {
      acitvedata.id = this.projectModel.id;
    }

    sessionStorage.setItem("activepage", JSON.stringify(acitvedata));
  }

  getCorrect() {
    this.visible = false;
    this.Correctshow = true;
    this.getcodelist();
  }
  getcodelist() {
    this.api.PublishedProject(this.projectModel.id).subscribe((res) => {
      // return
      this.codelist = res.data;
      this.questionnaireId = this.codelist[0].questionnaireId;
      this.codelist.forEach((val) => {
        val.Dimensionslist = this.classification(val.projectReportDimensions);
        val.Dimensionslist.forEach((item) => {
          item.data.forEach((cos) => {
            if (cos.isChecked) {
              item.isChecked = true;
            }
            if (!cos.name) {
              cos.name = {};
            }
          });
        });
      });
    });
  }
  historicalComparative() {
    this.router.navigate(["project-manage/historical-comparison"], {
      queryParams: {
        projectId: this.projectModel.id,
      },
    });
  }

  CorrectNext() {
    // 矫正维度 下一步
    this.CorrectNextshow = !this.CorrectNextshow;
  }

  CorrectToDefalt() {
    this.api
      .updateOneQuestionnaireReportSetting(this.questionnaireId)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.message.success("恢复默认成功");
          this.getcodelist();
        }
      });
  }

  Correctconfig() {
    this.Correctshow = false;
  }

  CorrectOk() {
    this.codelist.forEach((res) => {
      res.updatalist = [];

      if (res.projectReportDimensions) {
        res.updatalist = res.projectReportDimensions.filter((val) => {
          return val.isChecked;
        });
      }
    });
    console.log(this.codelist);
    let jsonlist = [];
    this.codelist.forEach((res) => {
      if (res.updatalist) {
        jsonlist.push(...res.projectReportDimensions);
      }
    });

    console.log(jsonlist);
    let flag = false;
    flag = jsonlist.some((res) => {
      return !res.name.zh_CN ? true : false;
    });
    console.log(flag);
    if (flag) {
      // return this.message.error("中文维度名称不能为空");
      return this.customMsg.open("error", "中文维度名称不能为空");
    }
    this.api.UpdateProject(jsonlist).subscribe((res) => {
      if (res.result.code == 0) {
        this.Correctshow = false;
        this.CorrectNextshow = false;
      }
    });
  }

  gettipfactor(id) {
    this.api.listTipjobsReportTemplateId(id).subscribe((res) => {
      console.log(res);
      this.Tipjobslist = res.data;
      console.log(" this.Tipjobslist", this.Tipjobslist);
      this.chooselist = this.Tipjobslist.filter((res) => {
        return this.standardReportTemplateJobId == res.id;
      });
    });
  }
  handlejob() {
    this.showtip = false;
  }
  handleOk() {
    this.updatafactor();
    // 刷新活动列表
  }
  nzOnJobStype(e) {
    this.chooselist = this.Tipjobslist.filter((res) => {
      return e == res.id;
    });
  }
  checkfactor(id) {
    this.api.querylistTipjobs(id).subscribe((res) => {
      if (res.result.code == 0) {
        this.standardReportTemplateJobId = res.data.standardReportTemplateJobId;
        this.dimensionLevel = res.data.dimensionLevel;
        this.isSelectedModel = res.data.isSelectedModel;
        this.selectedid = res.data.id;
      }
    });
  }
  updatafactor() {
    let parma = {
      id: this.selectedid,
      name: this.chooselist[0].name,
      projectId: this.projectModel.id,
      standardQuestionnaireId: this.projectModel.questionnaires[0]
        .standardQuestionnaireId,
      standardReportTemplateJobId: this.chooselist[0].id,
      dimensionLevel: this.dimensionLevel,
      isSelectedModel: this.isSelectedModel,
    };

    this.api.createlistTipjobs(parma).subscribe((res) => {
      if (res.result.code == 0) {
        this.message.success("修改成功！");
        this.showtip = false;
      } else {
        // this.message.error(res.result.message);
        this.customMsg.open("error", res.result.message);
      }
    });
  }
  deleteFacNameok() {
    this.updateProject({
      id: this.projectModel.id,
      isShow: !this.projectModel.isShow,
    });
  }
  // Okconfig(){
  //   this.configtation = false
  // }
  handleconfig() {
    this.configtation = false;
  }
  Changelog(args: any[]): void {}
  chooseselect(e, type) {
    this.codelist.forEach((res) => {
      res.Dimensionslist.forEach((val) => {
        if (e == val.name && !type) {
          val.data.forEach((item) => {
            item.isChecked = false;
          });
        }
        if (e == val.name && type) {
          val.data.forEach((item) => {
            item.isChecked = true;
          });
        }
      });
    });
  }
  // Release(){
  //   this.Activepublish()
  // }

  cancel() {}
  log(e) {}
  classification(arr) {
    console.log(arr);

    var map = {},
      dest = [];
    for (var i = 0; i < arr.length; i++) {
      var ai = arr[i];
      if (!map[ai.parentName]) {
        //key 依赖字段 可自行更改
        dest.push({
          name: ai.parentName,
          data: [ai],
          parentNameI18n: ai.parentNameI18n,
        });
        map[ai.parentName] = ai;
      } else {
        for (var j = 0; j < dest.length; j++) {
          var dj = dest[j];
          if (dj.name == ai.parentName) {
            //key 依赖字段 可自行更改
            dj.data.push(ai);
            break;
          }
        }
      }
    }
    console.log(dest);

    return dest;
  } //数据归类
  // 判断是否显示编辑题本
  isEditor(data) {
    let item = null;
    if (data && data.length > 0) {
      data.map((element) => {
        if (
          element.reportType == "BLANK_CUSTOM" ||
          element.reportType.indexOf("360") != -1 ||
          element.reportType.indexOf("270") != -1
        ) {
          item = element;
          return false;
        }
      });
    }
    return item;
  }
  /**
   * 内外部常模是否展示
   *@author:wangxiangxin
   *@Date:2023/12/18
   */
  isShowNorm(item) {
    // 测评展示内外部常模的type类型
    let type = [
      "CA",
      "LPA",
      "CA_LPA",
      "EPA",
      "MCA",
      "MCA_MID",
      "MCA_HIGH",
      "AT",
      "AMA",
      "BLANK_CUSTOM",
    ];
    let falg = false;
    if (item.surveyType == "ASSESSMENT") {
      //测评才会展示内外部常模
      if (item.is360Project) {
        falg = true;
      } else {
        item.questionnaires.forEach((element) => {
          if (element.reportType == "TIP_NEW_2") {
            if (element.reportTypeList && element.reportTypeList.length) {
              element.reportTypeList.forEach((data) => {
                if (type.includes(data)) {
                  falg = true;
                }
              });
            }
          } else {
            if (type.includes(element.reportType)) {
              falg = true;
            }
          }
        });
      }
    } else {
      falg = false;
    }
    return falg;
  }
}
