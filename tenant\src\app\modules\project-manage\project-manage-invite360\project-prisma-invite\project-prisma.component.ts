import { type } from "os";
import {
  Component,
  OnInit,
  ViewChild,
  ViewContainerRef,
  ElementRef,
  Inject,
} from "@angular/core";
import { Router, ActivatedRoute, NavigationEnd } from "@angular/router";
import { ProjectManageService } from "../../../service/project-manage.service";
import { NzModalService, NzMessageService, zh_CN, en_US } from "ng-zorro-antd";
import { UploadXHRArgs } from "ng-zorro-antd/upload";

import _ from "lodash";
import { SpecifiedComponent } from "../specified/specified.component";
import { RoleComponent } from "../role/role.component";
import { ComponentService } from "../../../../shared/component.service";
import { OrgService } from "../../../org/org.service";
import { HttpResponseWrapper } from "../../../../shared/interfaces/http-common";
import { DownloadUtilService } from "@src/modules/service/download-util.service";
import { isEmail } from "../../validate-util";
import * as XLSX from "xlsx";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { ChooseInvitorComponent } from "../choose-invitor/choose-invitor.component";
import { isString } from "util";
import { copy } from "clipboard";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { Content } from "@angular/compiler/src/render3/r3_ast";
import { PermissionService } from "@src/modules/service/permission-service.service";


const JSZip = require("jszip");

function isEmpty(value: string): boolean {
  return value.trim() === "";
}

type I18Type = {
  [key: string]: any;
};
interface ParentItemData {
  id?: string;
  key: string;
  name: string;
  email: string;
  phone: string;
  roleId: number | string;
  percentage: string;
  weights: number;
  isSelfEvaluation: boolean;
  persons: any[];
  projectId: string;
  expand: boolean;
}

interface FormData {
  [key: string]: any;
  title?: I18Type;
  emailList: Array<{ email: string; id: string; phone: string }>;
}
declare const document: any;
@Component({
  selector: "app-project-manage-invite360",
  templateUrl: "./project-prisma.component.html",
  styleUrls: ["./project-prisma.component.less"],
})
export class PrismaManageComponent implements OnInit {
  @ViewChild("settingHost", { read: ViewContainerRef, static: true })
  private settingHost: ViewContainerRef;
  isFetching = false;
  tenantUrl: string = "/tenant-api";
  tenantName: string = "";
  constructor(
    private routerInfo: ActivatedRoute,
    private api: ProjectManageService,
    private orgApi: OrgService,
    private router: Router,
    private modalService: NzModalService,
    private common: ComponentService,
    private http: HttpClient,
    private msg: NzMessageService,
    private downUtil: DownloadUtilService,
    private el: ElementRef,
    private customMsg: MessageService,
    public permissionService: PermissionService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService
    ) {
    this.navigationSubscription = this.router.events.subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        this.initLoad(event);
      }
    });
  }
  isLoadingOne = false;
  tabs: any[] = [
    { id: "single", name: "分享邀请码", active: false },
    { id: "multi", name: "发送邮件/短信邀请", active: false },
  ];
  effect = "scrollx";
  countMoney: string = "0";

  singleYuan;
  peopleNum;

  showType: string = "onecode";
  showcCode: string = "mail";
  invitecode: string = "";
  invitecode_one: string = "";
  oneindex: number = 0;
  codename: string = "公共码";
  Investigators = [];
  Investigatorsoption = [];
  downloadlist = [];
  selectedValue = null;
  roleslist = [];
  firstNameId: number = 0;
  codemenu = false;
  codetype = "";
  textarea: string = "";
  tabsetIndex: number = 0;
  fileIds: any[] = []; // 上传附件 id
  fileList: any[] = []; // 上传附件 id
  fileListAttachment: any[] = []; // 上传附件 id

  navigationSubscription; //路由监听
  keyword: string; //搜索关键字
  projectId: string; //活动id
  projectname: string; //活动名称
  projectCode: string; //活动编码
  questionnaireIds: any = [];
  step; //当前邀请第几步
  allNoInvitedUser; //所有未邀请人名单

  rolelist: any[] = [];

  messageData: string = "";
  formData: FormData = {
    inviteDate: new Date(), //邀请日期
    inviteTime: new Date(), //邀请时间
    userType_code: "", //指定人specified 全部all
    userType: "", //指定人specified 全部all
    content: {}, //邮件内容
    emailList: [], //发送人员列表
    title: {}, //邮件主题,
    codeType: "one",
  };
  formData1: FormData = {
    inviteDate: new Date(), //邀请日期
    inviteTime: new Date(), //邀请时间
    userType_code: "", //指定人specified 全部all
    userType: "", //指定人specified 全部all
    content: {}, //邮件内容
    emailList: [], //发送人员列表
    title: {}, //邮件主题,
    codeType: "one",
  };

  permission: boolean;
  nextAfterSave: boolean = false;
  // 调查者id
  investigatorId: string = "";
  // 测评者id
  personId: string = "";
  // 判断当前页面是做何种操作
  action: "edit" | "reSendEmail" = null;
  // url上获取邮件地址
  email: string = "";
  title: "邀请" | "评价关系";

  sourceData; //原始数据
  addtyceshow = false;
  eamilTemplate = [];
  radioValue = "";
  chooseformData: any = {
    content: {
      zh_CN: "",
      en_US: "",
    },
    name: "",
    valueId: "",
    isDefault: false,
    type: "",
    isStandard: false,
    id: "",
    editors: false,
    theme: {
      zh_CN: "",
      en_US: "",
    },
  };
  chooseformDataTemplate: any = {
    content: {
      zh_CN: "",
      en_US: "",
    },
    name: "",
    valueId: "",
    isDefault: false,
    type: "",
    isStandard: false,
    id: "",
    editors: false,
    theme: {
      zh_CN: "",
      en_US: "",
    },
  };
  tinyconfig = {};
  isEnableWxWorkMsg: boolean = false;
  isEnableDingMsg: boolean = false;
  isEnableFeishuMsg: boolean = false;

  sendtype = null;
  currentpage = null;
  totalpage = null;
  totalnumber = null;
  listtype = "";

  strNum;
  mailStrNum;
  longCodeNum;

  showcards = false;
  multipleValue = [];
  listSimilar = [];
  ismergeproject = false;
  showprojectcard = null;
  buttonload = false;
  queryParams = {};
  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/project-manage",
      name: "活动管理",
      Highlight: false,
    },
    {
      path: "",
      name: "邀请",
      Highlight: true,
    },
  ];
  isLoadingspend = false;

  isDisplaySMS = false; //短信功能是否展示
  isDisplayMail = false; //邮件功能是否展示
  mailTotal = {
    mailStrNum: 0,
    peopleNum: 0,
    countMoney: 0,
  };
  availableLanguages = [];

  // 模板type
  templateTypeMap = {
    mail: "MAIL", // 邮件
    message: "SMS", // 短信
    vchart: "THIRD_PARTY_ENTERPRISE_WECHAT", // 企微
    dingcode: "DING_DING", // 钉钉
    fscode: "THIRD_PARTY_LARK_TEXT_CARD", // 飞书
  };
  templateTypeNameMap = {
    mail: "邮件", // 邮件
    message: "短信", // 短信
    vchart: "企业微信", // 企微
    dingcode: "钉钉", // 钉钉
    fscode: "飞书", // 飞书
  };
  messagePrefix = {
    zh_CN: "【肯耐珂萨】",
    en_US: "【肯耐珂萨】",
  };
  messageSuffix = {
    zh_CN: "拒收请回复R",
    en_US: "拒收请回复R",
  };

  ngOnInit() {
    console.info("调研");
    this.getLans(this.routerInfo.snapshot.queryParams.projectId);
    // 合并发送邀请相关是否展示以及默认值
    this.getShowTypeCode();
    const _this = this;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.tinyconfig = {
      images_upload_url: `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`, // 配置你图片上传的url
      fontsize_formats:
        "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 32pt 36pt",
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.http
          .post(
            `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`,
            formData,
            { headers: headers }
          )
          .subscribe(
            (response: any) => {
              if (response) {
                this.http
                  .get(
                    `${this.tenantUrl}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                    { headers: headers }
                  )
                  .subscribe((imgurl: any) => {
                    let baseUrl: string = window.location.origin + "/";
                    if (baseUrl.indexOf("http://localhost") !== -1) {
                      baseUrl = "https://sag-qa.knxdevelop.com/";
                      // baseUrl = "http://***********/";;
                    }
                    let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                    // if ( environment.dev ) {
                    //    url = environment.SERVER_URL.substr(0, environment.SERVER_URL.length - 1)  + imgurl.data.url; // 这里是你获取图片url
                    // } else {
                    //   url = 'api' + imgurl.data.url; // 这里是你获取图片url
                    // }
                    // 把图片链接，img src标签显示图片的有效链接放到下面回调函数里

                    this.getMailTotal();
                    success(url);
                    // this.getBase64(url,success)//图片转base64
                  });
              } else {
                if (response && response.rtnMsg) {
                  failure(response.rtnMsg);
                } else {
                  failure("上传失败：未知错误");
                }
              }
            },
            (error1) => {
              failure("上传失败：未知错误");
            }
          );
      },
      init_instance_callback: (editor) => {
        editor.on("input", (e) => {
          this.getMailTotal();
        });
        editor.on("paste", (e) => {
          this.getMailTotal();
        });

        editor.on("ExecCommand", (e) => {
          this.getMailTotal();
        });
      },
    };
    // this.common.message('invited');

    this.action = this.routerInfo.snapshot.queryParams.action;
    const { email } = this.routerInfo.snapshot.queryParams;
    const { id } = this.routerInfo.snapshot.queryParams;
    const { phone } = this.routerInfo.snapshot.queryParams;
    if (email && id) {
      this.formData.emailList.push({
        email,
        id,
        phone,
      });
    } else {
    }

    this.projectId = this.routerInfo.snapshot.queryParams.projectId;
    this.projectname = this.routerInfo.snapshot.queryParams.name;
    this.projectCode = this.routerInfo.snapshot.queryParams.projectCode;
    this.questionnaireIds[0] = this.routerInfo.snapshot.queryParams.questionnaireIds;

    this.step = this.routerInfo.snapshot.queryParams.step;
    this.title = this.step === "1" ? "评价关系" : "邀请";
    this.investigatorId = this.routerInfo.snapshot.queryParams.investigatorId;
    this.personId = this.routerInfo.snapshot.queryParams.personId;

    this.api.getLongestPersonCaptcha(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.longCodeNum = res.data;
      }
    });
    if (this.step == 2) {
      let backUrl = localStorage.getItem("backurl");
      this.queryParams = this.routerInfo.snapshot.queryParams;
      this.Breadcrumbs = JSON.parse(localStorage.getItem("break"));
      this.Breadcrumbs.forEach((item) => {
        if (item.Highlight) {
          if (item.name == "活动管理") {
            item.path = "/project-manage/home";
          }
          if (item.name == "活动详情") {
            item.path = backUrl;
          }
        }
        item.Highlight = false;
      });
      this.Breadcrumbs.push({
        path: "",
        name: "邀请",
        Highlight: true,
      });
    }

    // 判断当前页面做何种操作
    // 1: 重新发送邮件,将数据回显
    // 2: 编辑时,将数据回显
    if (this.action === "reSendEmail") {
      this.showDataForReSendEmail(this.investigatorId, this.personId);
    } else if (this.action === "edit") {
      this.editSurvey(this.investigatorId);
    }
    this.getchange(this.showType);
      // 获取sessionStorage中的用户信息
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
    this.tenantName = userInfo.data.name;
  }

  getEmailThemeName(defaultTitle) {
    let templateType = this.templateTypeMap[this.showcCode];
    this.api
      .getLatestMailSubject({ projectId: this.projectId, type: templateType, purpose: "ANSWER" })
      .subscribe((res) => {
        if (res.result.code === 0) {
          if (res.data) {
            if (res.data.zh_CN) {
              this.formData.title.zh_CN = res.data.zh_CN || defaultTitle.zh_CN;
            }
            if (res.data.en_US) {
              this.formData.title.en_US = res.data.en_US || defaultTitle.en_US;
            }
          }
        }
      });
  }

  /**
   * getBase64 获取图片base64
   * @param img
   * @param callback
   */
  private getBase64(url, success) {
    var that = this;
    var image = new Image();
    image.src = url + "?v=" + Math.random(); // 处理缓存
    image.crossOrigin = "*"; // 支持跨域图片
    var base64;
    image.onload = function() {
      base64 = that.drawBase64Image(image);
      success(base64);
    };
  }
  drawBase64Image(img) {
    var canvas = document.createElement("canvas");
    canvas.width = img.width;
    canvas.height = img.height;
    var ctx = canvas.getContext("2d");
    ctx.drawImage(img, 0, 0, img.width, img.height);
    var dataURL = canvas.toDataURL("image/png");
    return dataURL;
  }
  ngOnDestroy(): void {
    // 销毁navigationSubscription，避免内存泄漏
    if (this.navigationSubscription) {
      this.navigationSubscription.unsubscribe();
    }
  }
  listInvestigatorGroup(type) {
    let json = {
      questionnaireIds: this.questionnaireIds,
    };
    this.api.generateQrCode(this.projectId, type, json).subscribe((res) => {
      if (res.result.code == 0) {
        this.invitecode = res.data;
        // update user list display
        // this.model.qrCodeContent = res.data;
      } else {
        // this.msg.error(res.result.message);
        this.customMsg.open("error", res.result.message);
        // this.model.qrCodeContent = undefined;
      }
    });
  }
  searchrolename(item): void {
    let selectlist = [];
    this.Investigators.forEach((res, index) => {
      res.indexId = index;
      if (res.firstName.indexOf(item) != -1) {
        selectlist.push(res);
      }
    });
    this.Investigatorsoption = selectlist;
  }
  testblur(e) {}
  async nzSelectedIndexChange(e) {
    this.showType = "onecode";
    this.tabsetIndex = e;
    this.formData.emailList = [];
    this.formData1.emailList = [];
    this.formData.userType = "all";
    this.changeSendUserType(this.formData.userType);
    if (e === 1) {
      this.getshowType();
    }
  }

  getchange(type) {
    this.showType = type;
    if (type == "onecode") {
      this.codename = "公共码";
      this.listInvestigatorGroup("PUBLIC");
    } else if (type == "orgcode") {
      this.codename = "组织验证码";
      this.listInvestigatorGroup("ORGANIZATION_CAPTCHA");
    } else {
      this.codename = "个人验证码";
      this.listInvestigatorGroup("CAPTCHA");
    }
  }
  getmessage(type) {
    this.multipleValue = [];
    this.formData.userType_code = "all";
    const typeMap = {
      mail: 'mail',
      message: 'message',
      vchart: 'vchart',
      dingcode: 'dingcode',
      fscode: 'fscode',
    }
    this.showcCode =  typeMap[type];
    // this.showcCode = type;
    // if (type == "mail") {
      let answerCodeType = "PRIVATE";
      if (this.codetype == "one") {
        answerCodeType = "PRIVATE";
      }
      if (this.codetype == "prov") {
        answerCodeType = "ORGANIZATION_CAPTCHA";
      }
      if (this.codetype == "single") {
        answerCodeType = "CAPTCHA";
      }
      if (this.codetype == "pub") {
        answerCodeType = "PUBLIC";
      }
    // }
    // } else if (type == "message") {
    //   this.getSmsDemo();
    // } else if (type == "vchart") {
    //   this.getVxDemo();
    // } else if (type == "dingcode") {
    //   // 钉钉消息示例
    //   this.getDingDemo();
    // } else {
    //   // 飞书
    //   this.getFeiShuDemo();
    // }
    // this.changeSendUserType("all");
    // console.log(222)
    this.getlistmodalMail(answerCodeType, true);
  }
  nzBeforeChange(e) {
    this.invitecode_one = this.Investigators[e.to].inviteUrl;
  }
  nzAfterChange(e) {
    // this.invitecode_one = this.Investigators[e].inviteUrl
  }

  //监听路由，修改后重新加载
  initLoad(e) {
    this.step = this.routerInfo.snapshot.queryParams.step;
  }

  //添加调查者
  addObserver(perdata?): void {
    let name: string = "";
    if (perdata) {
      if (perdata.name) {
        name = perdata.name;
      } else if (perdata.firstName) {
        name = perdata.firstName;
      }
    }
  }

  nextStep() {
    this.isLoadingspend = true;
    if (
      !(
        isString(this.formData.title[this.lan]) &&
        this.formData.title[this.lan].trim() !== ""
      ) &&
      this.showcCode === "mail"
    ) {
      this.isLoadingspend = false;
      // this.msg.error("请输入中文邮件主题");
      this.customMsg.open("error", "请输入邮件主题");
      return;
    }
    //this.showcCode 为真 校验邮件主题
    let type;
    if (this.codetype == "one") {
      type = "PRIVATE";
    }
    if (this.codetype == "prov") {
      type = "ORGANIZATION_CAPTCHA";
    }
    if (this.codetype == "single") {
      type = "CAPTCHA";
    }
    if (this.codetype == "pub") {
      type = "PUBLIC";
    }

    let Ids = [];
    this.fileIds.map((item) => {
      const index = this.fileListAttachment.findIndex(
        (itm) => itm.uid === item.uid
      );
      if (index !== -1) {
        Ids.push(item.id);
      }
    });

    // let type = !this.codemenu ? 'PRIVATE' : 'ORGANIZATION_CAPTCHA'
    let content = null;
    if (this.showcCode === "message") {
      content = this.addMessagePrefixAndSuffix(this.formData.content);
    } else {
      content = this.formData.content;
    }
    let params = {
      surveyPeople: this.formData.emailList,
      content: content,
      projectId: this.projectId,
      title: this.formData.title,
      answerCodeTypeEnum: type,
      fileIds: Ids,
      isMergeProject:
        this.showcCode != "message" && this.multipleValue.length != 0
          ? true
          : false,
      mergeProjectIds: this.showcCode != "message" ? this.multipleValue : [],
    };

    if (this.showcCode === "mail") {
      params.surveyPeople = this.formData.emailList.filter((res) => {
        return res.email != undefined;
      });
      if (params.surveyPeople.length != 0) {
        let arr: string[];
        params.surveyPeople.forEach((res) => {
          arr = res.email.split(/[(\r\n)\r\n]+/);
        });

        for (let index = 0; index < arr.length; index++) {
          const emailStr = arr[index];
          if (!isEmail(emailStr)) {
            this.isLoadingspend = false;
            // this.msg.error("不是有效的邮件地址：" + emailStr);
            this.customMsg.open("error", `不是有效的邮件地址：${emailStr}`);
            return;
          }
        }
      }
    } else if (this.showcCode === "message") {
      params.surveyPeople = this.formData.emailList.filter((res) => {
        return res.phone != undefined;
      });
    }

    if (this.showcCode === "mail") {
      // if (whiteemail.length == 0 && params.surveyPeople.length != 0) {
      this.api.sendEmail360(params).subscribe((res) => {
        if (res.result.code == 0) {
          this.isLoadingspend = false;
          this.fileIds = [];
          this.common.message("invited");
          this.router.navigateByUrl("/project-manage");
        } else {
          if (res.result.code < 10000) {
            this.isLoadingspend = false;
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          } else {
            this.isLoadingspend = false;
            // this.msg.error('企业微信发送，企业微信号不能为空！')
          }
        }
      }); //360邮件接口
      // } else {
      //   this.msg.error('邮件发送，邮箱不能为空！')
      // }
    } else if (this.showcCode === "message") {
      // if (whitephone.length == 0 && params.surveyPeople.length != 0) {
      this.api.sendMessage360(params).subscribe((res) => {
        if (res.result.code == 0) {
          this.isLoadingspend = false;
          this.fileIds = [];
          this.common.message("invited");
          this.router.navigateByUrl("/project-manage");
        } else {
          if (res.result.code < 10000) {
            this.isLoadingspend = false;
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          } else {
            this.isLoadingspend = false;
          }
        }
      }); //360短信接口
      // } else {
      //   this.msg.error('短信发送，手机号不能为空！')
      // }
    } else if (this.showcCode === "vchart") {
      // 企业微信未配置账号时点击发送邀请提示 #5872
      if (params.surveyPeople.length === 0) {
        this.isLoadingspend = false;
        // this.msg.error("企业微信发送，企业微信号不能为空！");
        this.customMsg.open("error", "企业微信发送，企业微信号不能为空！");
        return;
      }
      this.api.sendBehaviorWxWorkMsg(params).subscribe((res) => {
        if (res.result.code == 0) {
          this.isLoadingspend = false;
          this.fileIds = [];
          this.common.message("invited");
          this.router.navigateByUrl("/project-manage");
        } else {
          if (res.result.code < 10000) {
            this.isLoadingspend = false;
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          } else {
            this.isLoadingspend = false;
          }
        }
      }); //企业微信接口
    } else if (this.showcCode == "dingcode") {
      // 钉钉消息
      if (params.surveyPeople.length === 0) {
        this.isLoadingspend = false;
        // this.msg.error("钉钉发送，钉钉用户账号不能为空！");
        this.customMsg.open("error", "钉钉发送，钉钉用户账号不能为空！");
        return;
      }
      this.api.sendDingMsg(params).subscribe((res) => {
        if (res.result.code == 0) {
          this.isLoadingspend = false;
          this.fileIds = [];
          this.common.message("invited");
          this.router.navigateByUrl("/project-manage");
        } else {
          if (res.result.code < 10000) {
            this.isLoadingspend = false;
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          } else {
            this.isLoadingspend = false;
          }
        }
      });
    } else {
      // 钉钉消息
      if (params.surveyPeople.length === 0) {
        this.isLoadingspend = false;
        // this.msg.error("飞书发送，飞书用户账号不能为空！");
        this.customMsg.open("error", "飞书发送，飞书用户账号不能为空！");
        return;
      }
      this.api.sendFeiShuMsg(params).subscribe((res) => {
        if (res.result.code == 0) {
          this.isLoadingspend = false;
          this.fileIds = [];
          this.common.message("invited");
          this.router.navigateByUrl("/project-manage");
        } else {
          if (res.result.code < 10000) {
            this.isLoadingspend = false;
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          } else {
            this.isLoadingspend = false;
          }
        }
      });
    }
  }

  downFile(data) {
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];
    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
      // .split('\'\'')[1]
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  // 删除调查者
  deleteObserver(key) {}

  // 删除测评者
  deleteChild(data, key, index) {}

  // 短信/邮件 切换人员
  async changeSendUserType(e) {
    let type;
    if (this.showcCode === "mail") {
      type = "MAIL";
    } else if (this.showcCode === "message") {
      type = "SMS";
    } else if (this.showcCode === "vchart") {
      type = "THIRD_PARTY_ENTERPRISE_WECHAT";
    } else if (this.showcCode === "dingcode") {
      type = "DING_DING";
    } else {
      type = "THIRD_PARTY_LARK_TEXT_CARD";
    }
    await this.getAllNoInvitedUser(type, e);
    if (e === "all") {
      let emailList = [];
      this.allNoInvitedUser.forEach((item) => {
        emailList.push({
          email: item.email,
          id: item.id,
          captcha: item.captcha,
          firstName: item.firstName,
          phone: item.phone,
          wxWorkUserId: item.wxWorkUserId,
          dingAccount: item.dingAccount,
          feishuAccount: item.feishuAccount,
        });
      });

      this.formData.emailList = [].concat(emailList);
      let obj = this.api.countMoney(
        this.formData.emailList.length,
        this.strNum
      );
      this.countMoney = obj.countMoney;
      this.singleYuan = obj.yuan;
      this.peopleNum = obj.numPeople;
      this.getMailTotal();
    } else {
      this.showSpecifiedModal();
    }
  }

  //邀请码切换人员
  async changeSendUserTypecode(e) {
    await this.getcodeAllNoInvitedUser(e);
    if (this.showType !== "singlecode") {
      if (e === "all") {
        let emailList = [];
        this.allNoInvitedUser.forEach((item) => {
          emailList.push({
            id: item.id,
            captcha: item.captcha,
            firstName: item.name,
          });
        });
        this.formData.emailList = emailList;
        let num = (this.formData.emailList.length * 0.1).toFixed(1).toString();
        this.countMoney = num; //rng
      } else {
        this.showSpecifiedModal();
      }
    } else {
      if (e === "all") {
        let emailList = [];

        this.allNoInvitedUser.forEach((item) => {
          emailList.push({
            id: item.id,
            captcha: item.captcha,
            firstName: item.firstName,
          });
        });
        this.formData1.emailList = emailList;
        let obj = this.api.countMoney(
          this.formData.emailList.length,
          this.strNum
        );
        this.countMoney = obj.countMoney;
        this.singleYuan = obj.yuan;
        this.peopleNum = obj.numPeople;
      } else {
        this.showSpecifiedModal();
      }
    }
  }
  changonecode(e) {
    this.codetype = e;
    if (e === "prov" || e === "single") {
      this.codemenu = true;
    } else {
      this.codemenu = false;
    }
    // if (this.showcCode == "message") {
    //   this.getSmsDemo();
    // }
    // if (this.showcCode == "vchart") {
    //   this.getVxDemo();
    // }
    // // 钉钉
    // if (this.showcCode == "dingcode") {
    //   this.getDingDemo();
    // }
    // // 钉钉
    // if (this.showcCode == "fscode") {
    //   this.getFeiShuDemo();
    // }
    // if (this.showcCode == "mail") {
      let answerCodeType = "PRIVATE";
      if (this.codetype == "one") {
        answerCodeType = "PRIVATE";
      }
      if (this.codetype == "prov") {
        answerCodeType = "ORGANIZATION_CAPTCHA";
      }
      if (this.codetype == "single") {
        answerCodeType = "CAPTCHA";
      }
      if (this.codetype == "pub") {
        answerCodeType = "PUBLIC";
      }
      this.getlistmodalMail(answerCodeType, true);
  
  }
  // 正文-富文本插入字段
  getPushWord(val, type, id?, name?) {
    if (type) {
      let text = "";
      let dom = document.querySelector("#formula-tinymce").children[0]
        .children[0].children[1].children;
      if (dom[0].contentWindow.getSelection().type != "None") {
        let demo = dom[0].contentWindow.getSelection().getRangeAt(0);

        //IE浏览器
        if (document.selection) {
        } else {
          //Chrome等浏览器
          if (val == "name") {
            text = "{{姓名}} ";
          }
          if (val == "start") {
            text = "{{开始日期}} ";
          }
          if (val == "end") {
            text = "{{结束日期}}";
          }
          if (val == "url") {
            text = "{{链接}}";
          }
          if (val == "urls") {
            console.log("urls", this.codemenu);
            if (!this.codemenu) {
              text = `${name}{{链接}}`;
            } else {
              text = `${name}{{链接}} {{验证码}}`;
            }
          }
          if (val == "code") {
            text = "{{验证码}}";
          }
          if (val == "qrcode") {
            if (name) {
              text = `${name}{{登录二维码}}`;
            } else {
              text = "{{登录二维码}}";
            }
          }
          let newNode = document.createElement("span");
          if (id) {
            newNode.id = id;
          } else {
            this.showprojectcard = null;
          }
          newNode.innerText = text;
          demo.insertNode(newNode);
          let newContent =
            dom[0].contentWindow.document.activeElement.innerHTML;
          this.formData.content[this.lan] = newContent;
        }
      }
      this.getMailTotal();
    }
  }

  // 正文-文本域插入字段
  getPushWordTextarea(val, type, id?, name?) {
    if (type) {
      let text = "";
      let textarea = document.querySelector("#formula-textarea");
      // 获取当前光标位置
      const startPos = textarea.selectionStart;
      const endPos = textarea.selectionEnd;
      console.log("获取当前光标位置", startPos, endPos, textarea.value);
      // 获取当前 textarea 的值
      const currentValue = textarea.value;
      if (!id) {
        this.showprojectcard = null;
      }
      //IE浏览器
      if (document.selection) {
      } else {
        //Chrome等浏览器
        if (val == "name") {
          text = "{{姓名}} ";
        }
        if (val == "start") {
          text = "{{开始日期}} ";
        }
        if (val == "end") {
          text = "{{结束日期}}";
        }
        if (val == "url") {
          text = "{{链接}}";
        }
        if (val == "urls") {
          if (!this.codemenu) {
            text = `${name}{{${id}链接}}`;
          } else {
            text = `${name}{{${id}链接}} {{${id}验证码}}`;
          }
        }
        if (val == "code") {
          text = "{{验证码}}";
        }
        if (val == "qrcode") {
          if (name) {
            text = `${name}{{登录二维码}}`;
          } else {
            text = "{{登录二维码}}";
          }
        }
        // 构造新的值：在光标处插入文字
        const newValue =
          currentValue.slice(0, startPos) + text + currentValue.slice(endPos);
        // 更新 textarea 的值
        textarea.value = newValue;
        // 将光标移动到插入文字之后的位置
        textarea.selectionStart = startPos + text.length;
        textarea.selectionEnd = textarea.selectionStart;
        // 重新聚焦到 textarea
        textarea.focus();
        this.formData.content[this.lan] = newValue;
      }
      this.getMailTotal();
    }
  }

  // 短信、邮件 获取所有未邀请人
  async getAllNoInvitedUser(type, status: string): Promise<any> {
    this.sendtype = type;
    let params = {
      projectId: this.projectId,
      type: this.sendtype,
      searchFiled: "",
      page: {
        current: 1,
        size: 100,
      },
    };
    this.listtype = "message";
    let res;
    if (status !== "all") {
      res = await this.api.listNotInvitedprismanew(params).toPromise();
    } else {
      if (this.sendtype !== "SMS") {
        res = await this.api
          .listNotInvitedprisma([this.projectId, ...this.multipleValue], type)
          .toPromise();
      } else {
        res = await this.api
          .listNotInvitedprisma([this.projectId], type)
          .toPromise();
      }
    }
    if (res.result.code === 0) {
      if (status !== "all") {
        this.currentpage = res.page.current;
        this.totalpage = res.page.pages;
        this.totalnumber = res.page.total;
      }
      this.allNoInvitedUser = res.data;
    }
  }

  //邀请码获取所有未邀请人
  async getcodeAllNoInvitedUser(e?): Promise<any> {
    // this.sendtype = type
    if (this.showType === "orgcode") {
      this.listtype = "orgcode";
      let res;
      let params = {
        projectId: this.projectId,
        searchContent: "",
        pageRequest: {
          current: 1,
          size: 100,
        },
        filterRootOrganization: true,
      };
      if (e !== "all") {
        res = await this.api.getOrgList(params).toPromise();
      } else {
        res = await this.api
          .listNotInvitedprismacode(this.projectId)
          .toPromise();
      }
      if (res.result.code === 0) {
        this.allNoInvitedUser = res.data;
        if (e !== "all") {
          this.currentpage = res.page.current;
          this.totalpage = res.page.pages;
          this.totalnumber = res.page.total;
        }
      }
    } else {
      this.listtype = "code";
      let params = {
        projectId: this.projectId,
        searchFiled: "",
        page: {
          current: 1,
          size: 100,
        },
      };
      let res;
      if (e !== "all") {
        res = await this.api.listInvestigatorGroupnew(params).toPromise();
      } else {
        res = await this.api.listInvestigatorGroup(this.projectId).toPromise();
      }
      if (res.result.code === 0) {
        this.allNoInvitedUser = res.data;
        if (e !== "all") {
          this.currentpage = res.page.current;
          this.totalpage = res.page.pages;
          this.totalnumber = res.page.total;
        }
      }
    }
  }

  async getcaptchacode(): Promise<any> {
    //验证码  ORGANIZATION_CAPTCHA
    const res = await this.api
      .captchacodeurl(this.projectId, "ORGANIZATION_CAPTCHA")
      .toPromise();
    if (res.result.code === 0) {
      this.invitecode = res.data;
    }
  }

  // 获取邮件模板
  getEmailDemo() {
    let templateType = this.templateTypeMap[this.showcCode];
    this.api.showEmailDemo(templateType).subscribe((res) => {
      if (res.result.code === 0) {
        // this.formData.content = res.data.content;
        // var newstr = res.data.content.replace(/\//g, '');
        // let reNewstr = newstr.replace(/\<br>/g, '\n')
        // this.messageData = reNewstr
        const data = res.data.content || {};
        this.formData.content = data;
        Object.entries(data).forEach(([key, value]) => {
          if (typeof value === "string") {
            value = value.replace(/\//g, "").replace(/\<br>/g, "\n");
          }
        });
        // this.formData.title = res.data.theme;
        this.messageData = data;
        this.getEmailThemeName(res.data.theme);
        this.getMailTotal();
      }
    });
  }

  //获取短信模板
  getSmsDemo() {
    let type = "PRIVATE";
    if (this.codetype == "one") {
      type = "PRIVATE";
    }
    if (this.codetype == "prov") {
      type = "ORGANIZATION_CAPTCHA";
    }
    if (this.codetype == "single") {
      type = "CAPTCHA";
    }
    if (this.codetype == "pub") {
      type = "PUBLIC";
    }

    this.api
      .prismashowMessDemo(type, this.projectId, "EMPLOYEE_ENGAGEMENT")
      .subscribe((res) => {
        if (res.result.code === 0) {
          // this.formData.content = res.data;
          // console.log("获取短信模板", this.removeMessagePrefixAndSuffix(res.data));
          this.formData.content = this.removeMessagePrefixAndSuffix(res.data);
          this.formData.title = res.data.subject || {};
          this.nzChangetextarea();
        }
      });
  }

  nzChangetextarea() {
    let content = "";
    if (this.showcCode === "message") {
      content =
        this.messagePrefix[this.lan] +
        this.formData.content[this.lan] +
        this.messageSuffix[this.lan];
    } else {
      content = this.formData.content[this.lan];
    }
    this.strNum = this.api.calculateNum(
      content,
      this.longCodeNum,
      this.codetype
    );
    let obj = this.api.countMoney(this.formData.emailList.length, this.strNum);
    this.countMoney = obj.countMoney;
    this.singleYuan = obj.yuan;
    this.peopleNum = obj.numPeople;
  }

  // 获取微信模板
  getVxDemo() {
    let type = "PRIVATE";
    if (this.codetype == "one") {
      type = "PRIVATE";
    }
    if (this.codetype == "prov") {
      type = "ORGANIZATION_CAPTCHA";
    }
    if (this.codetype == "single") {
      type = "CAPTCHA";
    }
    if (this.codetype == "pub") {
      type = "PUBLIC";
    }
    this.api.showWxWorkMsgDemo(type, this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.formData.content = res.data.content;
        this.formData.title = res.data.title || {};
        this.nzChangetextarea();
      }
    });
  }

  // 钉钉消息示例
  getDingDemo() {
    let type = "PRIVATE";
    if (this.codetype == "one") {
      type = "PRIVATE";
    }
    if (this.codetype == "prov") {
      type = "ORGANIZATION_CAPTCHA";
    }
    if (this.codetype == "single") {
      type = "CAPTCHA";
    }
    if (this.codetype == "pub") {
      type = "PUBLIC";
    }
    this.api.showDingMsgDemo(type, this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.formData.content = res.data.content;
        this.formData.title = res.data.title || {};
        this.nzChangetextarea();
      }
    });
  }

  // 飞书消息示例
  getFeiShuDemo() {
    let type = "PRIVATE";
    if (this.codetype == "one") {
      type = "PRIVATE";
    }
    if (this.codetype == "prov") {
      type = "ORGANIZATION_CAPTCHA";
    }
    if (this.codetype == "single") {
      type = "CAPTCHA";
    }
    if (this.codetype == "pub") {
      type = "PUBLIC";
    }
    this.api.showFeiShuMsgDemo(type, this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.formData.content = res.data.content;
        this.formData.title = res.data.title || {};
        this.nzChangetextarea();
      }
    });
  }

  // 下载模板
  downLoad() {
    this.buttonload = true;
    this.api.exportprismaPerson(this.projectId).subscribe((res) => {
      // var blob = new Blob([res], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      // var objectUrl = URL.createObjectURL(blob);
      // window.open(objectUrl);
      this.downUtil.downFile(res);
      this.buttonload = false;
    });
  }

  /**
   * preview 预览上传的文件
   * @param file
   */
  preview(file) {
    window.open(window.URL.createObjectURL(file.originFileObj));
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    this.fileList = [
      {
        uid: item.file.uid,
        name: item.file.name,
        status: "uploading",
        originFileObj: item.file,
      },
    ];
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let params = {
      fileType: "." + item.file.name.split(".")[1],
    };
    this.uploadExcel(formData, params, item);
  };

  fileChange(e) {
    const arr = e.fileList;

    this.fileListAttachment = [...arr];
    if ((e.type = "removed")) {
      let filids = _.cloneDeep(this.fileIds);
      this.fileIds = [];
      filids.map((item) => {
        const index = this.fileListAttachment.findIndex(
          (itm) => itm.uid === item.uid
        );
        if (index !== -1) {
          this.fileIds.push(item);
        }
      });
      this.getMailTotal();
    }
  }

  /**
   * uploadAttachment 上传附件
   * @param item
   */
  uploadAttachment = (item: UploadXHRArgs) => {
    const formData = new FormData();
    let type = [
      ".txt",
      ".doc",
      ".docx",
      ".xls",
      ".xlsx",
      ".pdf",
      ".ppt",
      ".pptx",
      ".zip",
      ".rar",
      ".jpg",
      ".jpeg",
      ".png",
    ];
    formData.append("file", item.file as any);
    formData.append("isPublic", "false");
    formData.append("effectiveFileTypes", type as any);
    formData.append("businessType", "DEFAULT");
    this.uploadAttachmentUrl(formData, item);
  };
  /**
   * 获取文件名后缀
   * @param item
   */
  getFileType(file: any): string {
    let type = "PDF";
    if (file && file.name) {
      let name: string = file.name;
      let names: string[] = name.split(".");
      if (names.length > 0) {
        type = names[names.length - 1];
      }
    }
    return `.${type}`;
  }

  /**
   * uploadAttachmentUrl 上传pdf报告文件
   */
  uploadAttachmentUrl(formData, item) {
    const url = `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`;
    return this.http.post(url, formData).subscribe(
      (res: any) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.msg.success("上传文件成功");
          this.fileIds.push({
            id: res.data.id,
            uid: item.file.uid,
            size: this.api.getFileSize(item.file.size),
          });
          this.getMailTotal();
        } else {
          item.onError!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, params, item) {
    return this.api.getprismaPerson(formData, this.projectId).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.api.getLongestPersonCaptcha(this.projectId).subscribe((res) => {
            if (res.result.code === 0) {
              this.longCodeNum = res.data;
            }
          });
          item.onSuccess!();
          const sourceData = res.data;
          sourceData.map((perdata) => {
            this.addObserver(perdata);
          });
          this.fileList[0].status = "done";
        } else {
          this.fileList = [
            {
              uid: item.file.uid,
              name: item.file.name,
              status: "error",
              originFileObj: item.file,
            },
          ];
        }
      },
      (err) => {
          if(err.status === 500){
        // HTTP错误
        const errorMsg = '导入失败，请检查文件内容';
        this.updateFileStatus(item.file.uid, "error", errorMsg);
          //  item.onError!(err, item.file!);
        // this.cdr.detectChanges();
      }else{
        item.onError!(err, item.file!);   
      }
      
      }
    );
  }   

  // 辅助方法：更新文件状态和消息
private updateFileStatus(uid: string, status: string, message: string = '') {
  this.fileList = this.fileList.map(file => 
    file.uid === uid ? { 
      ...file, 
      status,
      response: message 
    } : file
  );
}

  /**
   * 编辑时,根据id获取数据并回显
   * @param investigatorId 调查者id
   */
  public editSurvey(investigatorId: string): void {
    this.http
      .request(
        "get",
        `${this.tenantUrl}/survey/person/getSurveyInvestigator/${investigatorId}`
      )
      .toPromise()
      .then((res: HttpResponseWrapper) => {
        if (res.result.code === 0) {
        }
      })
      .catch((err) => {
        throw new Error(err);
      });
  }

  /**
   * 重新发送邮件时,需要将原来的数据回显,只有邮箱可以编辑,其余不可编辑
   * @param investigatorId 调查者id
   * @param personId 测评者id
   */
  public showDataForReSendEmail(
    investigatorId: string,
    personId: string
  ): void {
    this.http
      .request(
        "get",
        `${this.tenantUrl}/survey/person/getInvestigatorPerson/${investigatorId}/${personId}`
      )
      .toPromise()
      .then((res: HttpResponseWrapper) => {
        if (res.result.code === 0) {
          const { data } = res;
          let sourceData: Array<any> = [];
          if (Array.isArray(data)) {
            sourceData = data;
          } else {
            sourceData.push(data);
          }
          sourceData.map((perdata) => {
            this.addObserver(perdata);
          });
        }
      })
      .catch((err) => {});
  }

  /**
   * 编辑完成时保存回调
   */
  public saveSurvey(): void {}

  // 对邀请失败的评测者重新发送邮件
  public reSendEmail(): void {}

  /**
   * 保存之后跳转到第二步
   */
  afterSaveToStep2(): void {
    this.router.navigate(["project-manage/invite360"], {
      queryParams: { projectId: this.projectId, step: 2 },
    });
  }
  doCopyurl() {
    const input = document.querySelector(".copytext");
    // const range = document.createRange();
    // range.selectNode(input);
    // window.getSelection().removeAllRanges();
    // window.getSelection().addRange(range);
    // document.execCommand('Copy');
    copy(input.value);
    this.msg.success("复制成功");
  }
  doCopy() {
    const div = document.getElementById("text");
    // const range = document.createRange();
    // range.selectNode(div);
    // window.getSelection().removeAllRanges();
    // window.getSelection().addRange(range);
    // document.execCommand('Copy');
    copy(div.value);
    this.msg.success("复制成功");
  }
  downloadImg() {
    const img = document.querySelector(".qrcode img");
    const src = img.getAttribute("src");

    let aLink = document.createElement("a");
    let blob = this.base64ToBlob(src);
    let evt = document.createEvent("HTMLEvents");
    let imageName = "";
    let prefix = this.tenantName + "_"+this.projectCode +":"+this.projectname+"_";
    if (this.showType === 'onecode') {
      imageName = prefix+"公共邀请码【二维码图片】";
    } else if (this.showType === 'orgcode') { 
      imageName = prefix+"组织验证码邀请码【二维码图片】";
    } else if (this.showType === 'singlecode') {
      imageName = prefix+"个人验证码邀请码【二维码图片】";
    }
    let timestamp = new Date().getTime();
    imageName = imageName + "_" + timestamp;
    evt.initEvent("click", true, true);
    aLink.download = imageName + ".png";
    aLink.href = URL.createObjectURL(blob);
    aLink.click();
  }
  moreload() {
    let urllist = [];
    const morecode = document.getElementById("moreqrcode").children;
    for (let index = 0; index < morecode.length; index++) {
      urllist.push({
        img: morecode[index].querySelector(".qrcode img").getAttribute("src"),
      });
    }
    this.downloadlist.forEach((item, index) => {
      urllist.forEach((res, inde) => {
        if (index == inde) {
          res.name = item.firstName;
        }
      });
    });

    this.zipClick(urllist);
  }
  zipClick(imgData) {
    const zip = new JSZip();
    imgData.forEach((res, index) => {
      zip.file(res.name + ".png", res.img.substring(22), { base64: true });
    });
    let imageName = "";
    let prefix = this.tenantName + "_"+this.projectCode +":"+this.projectname+"_";
    imageName = prefix+"个人邀请码【二维码图片】";
    let timestamp = new Date().getTime();
    imageName = imageName + "_" + timestamp;
    zip.generateAsync({ type: "blob" }).then(function(content) {
      let aLink = document.createElement("a");

      aLink.setAttribute("href", URL.createObjectURL(content));
      aLink.setAttribute("download", imageName+".zip");
      aLink.click();
    });
  }
  private excelData;
  updateFile($event) {
    const target: DataTransfer = $event.target as DataTransfer;
    if (target.files.length !== 0) {
      if (target.files.length !== 1) {
        throw new Error("Cannot use multiple files");
      }
    }

    const reader: FileReader = new FileReader();

    reader.onload = (e: any) => {
      /* read workbook */
      const bstr: string = e.target.result;
      const wb: XLSX.WorkBook = XLSX.read(bstr, { type: "binary" });

      /* grab first sheet */
      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];

      this.excelData = XLSX.utils.sheet_to_json(ws);
      let emailList = [];
      if (this.showcCode === "mail") {
        for (let i in this.excelData) {
          emailList.push(this.excelData[i]["邮箱"]);
        }
      } else {
        for (let i in this.excelData) {
          emailList.push(this.excelData[i]["手机号"]);
        }
      }

      this.formData.email = emailList.join("\n");
    };
    reader.readAsBinaryString(target.files[0]);
  }
  base64ToBlob(code) {
    let parts = code.split(";base64,");
    let contentType = parts[0].split(":")[1];
    let raw = window.atob(parts[1]);
    let rawLength = raw.length;

    let uInt8Array = new Uint8Array(rawLength);

    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array], { type: contentType });
  }

  // isVisible : boolean = false;

  // handleInviteCancel() {
  //   this.isVisible = false;
  // }

  // handleInviteOk() {
  //   this.isVisible = false;
  // }

  // 指定人弹窗
  showSpecifiedModal() {
    const modal = this.modalService.create({
      nzContent: ChooseInvitorComponent,
      nzTitle: "指定邀请人",
      nzWidth: 800,
      nzComponentParams: {
        isFetchData: true,
        list: this.allNoInvitedUser,
        projectId: this.projectId,
        type: this.sendtype,
        currentpage: this.currentpage,
        totalpage: this.totalpage,
        totalnumber: this.totalnumber,
        listtype: this.listtype,
        showcCode: this.showcCode,
        tabsetIndex: this.tabsetIndex,
        multipleValue:
          this.tabsetIndex === 1 && this.showcCode === "mail"
            ? this.multipleValue
            : [],
      },

      nzFooter: [
        {
          label: "确认",
          shape: "round",
          type: "primary",
          onClick: () => {
            let child: ChooseInvitorComponent = modal.getContentComponent();
            const list = child.selectkeys;

            let emailList = [];
            if (this.tabsetIndex == 0) {
              list.forEach((item) => {
                emailList.push({
                  id: item.key,
                  captcha: item.captcha,
                  firstName: item.firstName,
                });
              });
            } else {
              list.forEach((item) => {
                emailList.push({
                  email: item.email,
                  id: item.key,
                  captcha: item.captcha,
                  firstName: item.firstName,
                  phone: item.phone,
                  wxWorkUserId: item.wxWorkUserId,
                  dingAccount: item.dingAccount,
                  feishuAccount: item.feishuAccount,
                });
              });
            }
            let num: string = null;
            let obj;
            if (this.showType !== "singlecode") {
              this.formData.emailList = emailList;
              obj = this.api.countMoney(
                this.formData.emailList.length,
                this.strNum
              );
            } else {
              this.formData1.emailList = emailList;
              obj = this.api.countMoney(
                this.formData1.emailList.length,
                this.strNum
              );
            }
            this.countMoney = obj.countMoney;
            this.singleYuan = obj.yuan;
            this.peopleNum = obj.numPeople;
            this.getMailTotal();
            modal.close();
          },
        },
      ],
    });
  }

  plusAdd() {
    this.addtyceshow = true;
    this.getEmaillist();
  }
  getEmaillist() {
    let templateType = this.templateTypeMap[this.showcCode];
    this.api.getEmaillist(templateType).subscribe((res) => {
      if (res.result.code == 0) {
        this.eamilTemplate = res.data;
        console.log("eamilTemplate res.data", res.data);
        this.eamilTemplate.forEach((res) => {
          res.valueId = res.id;
          res.editors = true;
        });
        let choosed = this.eamilTemplate.filter((res) => {
          return res.isStandard;
        });
        if (choosed.length > 0) {
          this.chooseformData = choosed[0];
        }
        let templateType = this.templateTypeMap[this.showcCode];
        console.log("templateType", templateType);
        this.api.getEmailDefault(templateType).subscribe((res) => {
          console.log("getEmailDefault", templateType, res);
          // 若有默认数据，赋值当前编辑
          if (res.data) {
            this.radioValue = res.data.id;
            this.chooseformData = res.data;
            this.chooseformData.valueId = this.chooseformData.id;
            this.chooseformData.editors = true;
            if (this.showcCode === "message") {
              this.chooseformData.content = this.removeMessagePrefixAndSuffix(
                this.chooseformData.content
              );
            }
          } else {
            // 若无默认数据，用模板数据赋值当前编辑
            const chooseformDataDeep = _.cloneDeep(this.chooseformDataTemplate);
            if (this.showcCode === "message") {
              chooseformDataDeep.theme = null;
            }
            chooseformDataDeep.type = templateType;
            this.chooseformData = chooseformDataDeep;
            // this.chooseformData.editors = false;
          }
        });
      }
    });
  }
  addtyceCancel() {
    this.addtyceshow = false;
  }
  addtyceOk() {
    if (!this.chooseformData.id) {
      // this.msg.warning("请先保存该条模板！");
      this.customMsg.open("warning", "请先保存该条模板");
    } else {
      let editors = false;
      this.eamilTemplate.forEach((res) => {
        if (!res.editors) {
          editors = true;
        }
      });
      if (editors) {
        // this.msg.warning("请先保存编辑内容！");
        this.customMsg.open("warning", "请先保存编辑内容");
      } else {
        this.api.useTemplate(this.chooseformData.valueId).subscribe((res) => {
          if (res.result.code == 0) {
            this.addtyceshow = false;
            if (this.showcCode == 'message') {
              this.formData.content = this.removeMessagePrefixAndSuffix(res.data.content);
            } else {
              this.formData.content = res.data.content; // 正文
            }
            this.formData.title = res.data.theme; // 主题
            this.getMailTotal();
          }
        });
      }
    }
  }
  chooseemail() {
    let choosed = this.eamilTemplate.filter((res) => {
      return res.valueId == this.radioValue;
    });
    const choosedData = choosed[0];
    let content = choosedData.content;
    if (this.showcCode === "message") {
      content = this.removeMessagePrefixAndSuffix(choosedData.content);
    }
    this.chooseformData = {
      ...choosedData,
      editors: !!choosed[0].id,
      content: content,
    };
  }
  addDefaultemail() {
    // var timestamp = Date.parse(new Date())
    let list = this.eamilTemplate.filter((res) => {
      // return res.type == "add";
      return !res.id;
    });
    if (list.length == 0) {
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      let monthnew;
      let daynew;
      if (month < 10) {
        monthnew = "0" + month;
      } else {
        monthnew = month;
      }
      if (day < 10) {
        daynew = "0" + day;
      } else {
        daynew = day;
      }
      var nowDate = year + "-" + monthnew + "-" + daynew;
      const template = _.cloneDeep(this.chooseformDataTemplate);
      template.name = "新增模板";
      template.editors = false;
      template.createTime = nowDate;
      template.valueId = date.getMilliseconds().toString();
      template.type = this.templateTypeMap[this.showcCode];
      this.eamilTemplate.unshift(template);
      this.radioValue = date.getMilliseconds().toString();
      this.chooseformData = this.eamilTemplate[0];
    } else {
      // this.msg.warning("请逐一完成新增模板编辑！");
      this.customMsg.open("warning", "请逐一完成新增模板编辑");
    }
  }
  deleteEmail(index) {
    if (!this.eamilTemplate[index].id) {
      this.eamilTemplate.splice(index, 1);
      this.radioValue = this.eamilTemplate[0].id;
      this.chooseformData = this.eamilTemplate[0];
    } else {
      let isDefault = this.eamilTemplate[index].isDefault;
      if (isDefault) {
        // this.msg.warning("默认邮件模板不可删除！");
        const typeStr = this.templateTypeNameMap[this.showcCode];
        this.customMsg.open("error", `默认${typeStr}模板不可删除！`);
      } else {
        let id = this.eamilTemplate[index].id;
        this.api.getDeletelist(id).subscribe((res) => {
          if (res.result.code == 0) {
            this.getEmaillist();
          }
        });
      }
    }
  }
  cancel() {}
  // 设置默认
  setcustom() {
    if (!this.chooseformData.id) {
      this.eamilTemplate.forEach((res) => {
        res.isDefault = false;
      });
    } else {
      let id = this.chooseformData.valueId;
      let isDefault = !this.chooseformData.isDefault;
      let templateType = this.templateTypeMap[this.showcCode];
      this.api.settingdeffault(id, isDefault, templateType).subscribe((res) => {
        if (res.result.code == 0) {
          this.getEmaillist();
        } else {
          if (res.result.code == 210048) this.chooseformData.isDefault = true;
        }
      });
    }
  }
  getdefaultcontant() {
    this.api.ResetProject(this.chooseformData.valueId).subscribe((res) => {
      if (res.result.code == 0) {
        this.getEmaillist();
      }
    });
  }
  saveInfolist() {
    console.log("this.chooseformData", this.chooseformData);
    let json = this.chooseformData;
    if (this.chooseformData.name.length > 50) {
      // this.msg.error("模板名称最多50个字符！");
      this.customMsg.open("error", "模板名称最多50个字符！");
      return;
    }
    if (this.chooseformData.name == "") {
      // this.msg.error("请输入模板名称！");
      this.customMsg.open("error", "请输入模板名称！");
      return;
    }
    if (this.chooseformData.theme.zh_CN == "" && this.showcCode !== "message") {
      const typeStr = this.templateTypeNameMap[this.showcCode];
      this.customMsg.open("error", `请输入${typeStr}主题中文！`);
      return;
    }
    // if (this.chooseformData.theme.en_US == "") {
    //   this.customMsg.open("error", "请输入邮件主题英文！");
    //   return;
    // }
    // 邮件验证-内容至少填写一种
    // const contents = Object.values(this.chooseformData.content);
    // if (contents.filter(val => !!val).length === 0) {
    //   this.msg.error('请输入至少一种邮件正文！')
    //   return
    // }
    if (
      !(
        isString(this.chooseformData.content.zh_CN) &&
        this.chooseformData.content.zh_CN.trim() !== ""
      )
    ) {
      // this.msg.error("请输入中文邮件正文！");
      const typeStr = this.templateTypeNameMap[this.showcCode];
      this.customMsg.open("error", `请输入中文${typeStr}正文！`);
      return;
    }
    if (this.showcCode === "message") {
      json.content = this.addMessagePrefixAndSuffix(json.content);
    }
    if (!this.chooseformData.id) {
      this.api.createlist(json).subscribe((res) => {
        if (res.result.code == 0) {
          this.getEmaillist();
          this.chooseformData.editors = true;
          this.msg.success("新增成功！");
        }
      });
    } else {
      this.api.updatelist(json).subscribe((res) => {
        if (res.result.code == 0) {
          this.getEmaillist();
          this.chooseformData.editors = true;
          this.msg.success("更新成功！");
        }
      });
    }
  }
  editorInfolist() {
    this.chooseformData.editors = false;
  }
  radioChange() {
    this.eamilTemplate.forEach((res) => {
      if (!res.editors) {
        res.editors = true;
      }
    });
  }

  HTMLDecode(text) {
    // html 反转义
    let temp = document.createElement("div");
    temp.innerHTML = text;
    let output = temp.innerText || temp.textContent;
    temp = null;
    return output;
  }
  // 自定义模板-富文本插入字段
  getPushWordTemplate(val, type) {
    if (type) {
      let text = "";
      let dom = document.querySelector("#formula-tinymce-template").children[0]
        .children[0].children[1].children;
      if (dom[0].contentWindow.getSelection().type != "None") {
        let demo = dom[0].contentWindow.getSelection().getRangeAt(0);

        //IE浏览器
        if (document.selection) {
        } else {
          //Chrome等浏览器
          if (val == "name") {
            text = "{{姓名}} ";
          }
          if (val == "start") {
            text = "{{开始日期}} ";
          }
          if (val == "end") {
            text = "{{结束日期}}";
          }
          if (val == "url") {
            text = "{{链接}}";
          }
          if (val == "code") {
            text = "{{验证码}}";
          }
          if (val == "qrcode") {
            text = "{{登录二维码}}";
          }
          let newNode = document.createElement("span");
          newNode.innerText = text;
          demo.insertNode(newNode);
          let newContent =
            dom[0].contentWindow.document.activeElement.innerHTML;
          this.chooseformData.content[this.lan] = newContent;
        }
      }
    }
  }
  // 自定义模板-文本域插入字段
  getPushWordTextareaTemplate(val, type) {
    if (type) {
      let text = "";
      // 编辑中才生效
      if (this.chooseformData.editors) return;
      let textarea = document.querySelector("#formula-textarea-template");
      // 获取当前光标位置
      const startPos = textarea.selectionStart;
      const endPos = textarea.selectionEnd;
      console.log("获取当前光标位置", startPos, endPos);
      // 获取当前 textarea 的值
      const currentValue = textarea.value;
      //IE浏览器
      if (document.selection) {
      } else {
        //Chrome等浏览器
        if (val == "name") {
          text = "{{姓名}} ";
        }
        if (val == "start") {
          text = "{{开始日期}} ";
        }
        if (val == "end") {
          text = "{{结束日期}}";
        }
        if (val == "url") {
          text = "{{链接}}";
        }
        if (val == "code") {
          text = "{{验证码}}";
        }
        if (val == "qrcode") {
          text = "{{登录二维码}}";
        }
        // 构造新的值：在光标处插入文字
        const newValue =
          currentValue.slice(0, startPos) + text + currentValue.slice(endPos);
        // 更新 textarea 的值
        textarea.value = newValue;

        // 将光标移动到插入文字之后的位置
        textarea.selectionStart = startPos + text.length;
        textarea.selectionEnd = textarea.selectionStart;
        // 重新聚焦到 textarea
        textarea.focus();
        this.chooseformData.content[this.lan] = newValue;
      }
    }
  }
  getMerge() {
    this.showcards = true;
    if (this.listSimilar.length == 0) {
      this.getlistSame();
    }
  }
  getMerges() {
    this.showcards = false;
  }
  MergeClear() {
    this.ismergeproject = false;
    this.multipleValue = [];
  }
  MergeSunmmit() {
    if (this.multipleValue.length != 0) {
      this.ismergeproject = true;
    } else {
      this.ismergeproject = false;
    }
    this.showcards = false;
    this.listSimilar.forEach((item) => {
      item.checked = false;
      this.multipleValue.forEach((val) => {
        if (item.id == val) item.checked = true;
      });
    });
    this.changeSendUserType("all");
  }
  getShowcards(e) {
    this.showprojectcard = e;
    this.getMailTotal();
  }

  getlistSame() {
    this.api.getlistSimilar(this.projectId).subscribe((res) => {
      this.listSimilar = res.data;
      this.listSimilar.forEach((item) => {
        item.checked = false;
        this.multipleValue.forEach((val) => {
          if (item.id == val) item.checked = true;
        });
      });
    });
  }
  getlistmodalMail(type?, isGetUser?) {
    let ptype = "";
    if (type) ptype = type;
    let templateType = this.templateTypeMap[this.showcCode];
    this.api.getlistMail(this.projectId, ptype, templateType, "ANSWER").subscribe((res) => {
      if (res.data) {
        this.multipleValue = res.data.mergeProjectIds
          ? res.data.mergeProjectIds
          : [];
          
        if (isGetUser) {
          this.changeSendUserType("all");
        }
        this.formData.title = res.data.subject || {};
        this.formData.content = res.data.content || {};

        if (res.data.answerCodeType == "PRIVATE") {
          this.formData.codeType = "one";
        }
        if (res.data.answerCodeType == "ORGANIZATION_CAPTCHA") {
          this.formData.codeType = "prov";
        }
        if (res.data.answerCodeType == "CAPTCHA") {
          this.formData.codeType = "single";
        }
        if (res.data.answerCodeType == "PUBLIC") {
          this.formData.codeType = "pub";
        }
        this.codetype = this.formData.codeType;
        if (
          this.formData.codeType === "prov" ||
          this.formData.codeType === "single"
        ) {
          this.codemenu = true;
        } else {
          this.codemenu = false;
        }
        if (this.multipleValue.length != 0) {
          this.ismergeproject = true;
        } else {
          this.ismergeproject = false;
        }
        this.getlistSame();
        if (this.showcCode == 'mail') {
          this.getMailTotal();
        } else {
          if (this.showcCode == 'message') {
            console.log(res.data, "res.data")
            this.formData.content = this.removeMessagePrefixAndSuffix(res.data.content);
          }
          this.nzChangetextarea();
        }
      } else {
        if (this.showcCode == 'mail') {
          this.getEmailDemo();
        } else if (this.showcCode == 'message') {
          this.getSmsDemo();
        } else if (this.showcCode == 'vchart') {
          this.getVxDemo();
        } else if (this.showcCode == 'dingcode') {
          this.getDingDemo();
        } else {
          this.getFeiShuDemo();
        }
        this.multipleValue = []
        
        if (isGetUser) {
          this.changeSendUserType("all");
        }
      }
    });
  }
  getMailTotal() {
    // if (this.showcCode !== "mail") {
    //   return;
    // }
    if (this.showcCode !== "mail") {
      return;
    }
    let dom: any = null;
    let htmlData = document.querySelector("#formula-tinymce");
    if (
      htmlData &&
      htmlData.children &&
      htmlData.children[0].children[0] &&
      htmlData.children[0].children[0].children[1] &&
      htmlData.children[0].children[0].children[1].children
    ) {
      dom = htmlData.children[0].children[0].children[1].children;
    }
    let text = null;
    if (dom && dom[0]) {
      text = dom[0].contentWindow.document.activeElement.innerHTML;
    } else {
      text = this.formData.content[this.lan];
    }

    let num1 = this.api.getStringSizeInBytes(text);
    let mailStrNum = this.api.getMailTotalSize(num1, this.fileIds);
    let peopleNum = this.peopleNum || 0;
    // console.log(this.peopleNum, "--");
    this.mailTotal = {
      mailStrNum,
      peopleNum,
      countMoney: Math.round(0.05 * mailStrNum * peopleNum * 100) / 100,
    };
  }
  // 国际化
  lan = "zh_CN";
  onChangeLan(e) {
    this.lan = e;
  }
  getLans(projectId) {
    this.api.getProjectSetting(projectId).subscribe((res) => {
      if (res.result.code === 0) {
        sessionStorage.setItem(
          "projectLanguages",
          JSON.stringify(res.data.availableLanguages)
        );
        sessionStorage.setItem("language", res.data.language);
        this.availableLanguages = res.data.availableLanguages;
      }
    });
  }

  /**
   * 合并发送邀请相关是否展示以及默认值
   * @author:sidwang
   * @Date:2024/12/31
   */
  async getShowTypeCode() {
    this.permission = this.permissionService.isPermission();
    this.isEnableWxWorkMsg = JSON.parse(
      sessionStorage.getItem("isEnableWxWorkMsg")
    );
    this.isEnableDingMsg = JSON.parse(
      sessionStorage.getItem("isEnableDingMsg")
    );
    this.isEnableFeishuMsg = JSON.parse(
      sessionStorage.getItem("isEnableFeishuMsg")
    );
    // 获取邮件功能是否展示
    const res1 = await this.api.isOpenMailSend().toPromise();
    if (res1.result.code === 0) {
      this.isDisplayMail = res1.data || false;
    }
    // 获取短信功能是否展示
    const res2 = await this.api.isOpenMessageSend().toPromise();
    if (res2.result.code === 0) {
      this.isDisplaySMS = res2.data || false;
    }
    const typeArr = [
      { code: "mail", isShow: this.isDisplayMail || false }, // 发送邮件
      { code: "message", isShow: this.isDisplaySMS || false }, // 短信邀请
      { code: "vchart", isShow: this.isEnableWxWorkMsg || false }, // 企业微信
      { code: "dingcode", isShow: this.isEnableDingMsg || false }, // 钉钉
      { code: "fscode", isShow: this.isEnableFeishuMsg || false }, // 飞书
    ];
    const showType = typeArr.filter((val) => val.isShow);
    if (showType && showType.length > 0) {
      this.showcCode = showType[0].code;
    } else {
      this.showcCode = "";
    }
    // this.formData.userType = "all";
    // this.formData.userType_code = "all";
    // this.changeSendUserType("all");
    // this.getmessage(this.showcCode);
    // if (this.showcCode == "mail") {
    //   let type = "PRIVATE";
    //   if (this.codetype == "one") {
    //     type = "PRIVATE";
    //   }
    //   if (this.codetype == "prov") {
    //     type = "ORGANIZATION_CAPTCHA";
    //   }
    //   if (this.codetype == "single") {
    //     type = "CAPTCHA";
    //   }
    //   if (this.codetype == "pub") {
    //     type = "PUBLIC";
    //   }
    //   this.getlistmodalMail(type);
    // }
    // if (this.showcCode == "message") {
    //   this.getSmsDemo();
    // }
    // if (this.showcCode == "vchart") {
    //   this.getVxDemo();
    // }
    // // 钉钉
    // if (this.showcCode == "dingcode") {
    //   this.getDingDemo();
    // }
    // // 飞书
    // if (this.showcCode == "fscode") {
    //   this.getFeiShuDemo();
    // }
  }
  getshowType() {
    const typeArr = [
      { code: "mail", isShow: this.isDisplayMail || false }, // 发送邮件
      { code: "message", isShow: this.isDisplaySMS || false }, // 短信邀请
      { code: "vchart", isShow: this.isEnableWxWorkMsg || false }, // 企业微信
      { code: "dingcode", isShow: this.isEnableDingMsg || false }, // 钉钉
      { code: "fscode", isShow: this.isEnableFeishuMsg || false }, // 飞书
    ];
    const showType = typeArr.filter((val) => val.isShow);
    if (showType && showType.length > 0) {
      this.showcCode = showType[0].code;
    } else {
      this.showcCode = "";
    }
    if (this.showcCode == "mail") {
      this.getlistmodalMail();
    }
    if (this.showcCode == "message") {
      this.getSmsDemo();
    }
    if (this.showcCode == "vchart") {
      this.getVxDemo();
    }
    // 钉钉
    if (this.showcCode == "dingcode") {
      this.getDingDemo();
    }
    // 飞书
    if (this.showcCode == "fscode") {
      this.getFeiShuDemo();
    }
  }
  /**
   * 剔除文本中的前后缀
   * @param text 需要处理的文本
   * @returns 剔除前后缀后的文本
   */
  removeMessagePrefixAndSuffix(content: { [key: string]: string }) {
    const content_ = _.cloneDeep(content);
    Object.keys(content_).forEach((key) => {
      let result = content_[key];
      const prefix = this.messagePrefix[key] || "";
      const suffix = this.messageSuffix[key] || "";
      // 剔除前缀
      if (prefix && result.startsWith(prefix)) {
        result = result.substring(prefix.length);
      }
      // 剔除后缀
      if (suffix && result.endsWith(suffix)) {
        result = result.substring(0, result.length - suffix.length);
      }
      content_[key] = result;
    });
    return content_;
  }
  /**
   * 增加文本中的前后缀
   * @param text 需要处理的文本
   * @returns 增加前后缀后的文本
   */
  addMessagePrefixAndSuffix(content: { [key: string]: string }) {
    const content_ = _.cloneDeep(content);
    Object.keys(content_).forEach((key) => {
      let result = content_[key];
      const prefix = this.messagePrefix[key] || "";
      const suffix = this.messageSuffix[key] || "";
      // 增加前缀
      if (prefix && !result.startsWith(prefix)) {
        result = prefix + result;
      }
      // 增加后缀
      if (suffix && !result.endsWith(suffix)) {
        result = result + suffix;
      }
      content_[key] = result;
    });
    return content_;
  }
}
