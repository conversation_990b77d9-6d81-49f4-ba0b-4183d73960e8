import { Injectable, Inject } from "@angular/core";
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  CanActivateChild,
} from "@angular/router";

import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { ReuseTabService } from "@knz/assembly";

@Injectable({
  providedIn: "root"
})
export class LoginGuardService implements CanActivate, CanActivateChild {
  constructor(
    private router: Router,
    @Inject(ReuseTabService)
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
  ) {
  }

  canActivate(_route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    return this.checkLogin();
  };

  canActivateChild(_route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    return this.checkLogin();
  };

  checkLogin(): boolean {
    // 判断本地有没有token
    // const type = this.routeInfo.snapshot.queryParams.type;
    let tokenObj : any = this.tokenService.get();
    if(tokenObj && tokenObj.token) {
      return true;
    } else {
      this.router.navigate(["/user/login"]);
      return false;
    }
  }
}
