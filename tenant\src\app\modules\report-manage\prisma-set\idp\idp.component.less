.title {
    padding: 10px;
    height: 40px;
    display: flex;
    justify-content: flex-start;

    .col1 {
        width: 255px;
        margin-right: 42px;
        display: flex;
        justify-content: space-between;
    }

    .col2 {
        flex: 1;
        display: flex;
        justify-content: space-between;
    }

    .label {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #17314C;
        line-height: 20px;
    }
}

.con {
    height: 500px;
    overflow-y: auto;
    overflow-x: auto;

    .idp {
        width: 100%;
        min-height: 95px;
        padding: 10px;
        margin-bottom: 15px;
        background: #F5F6FA;
    
        display: flex;
        justify-content: flex-start;
    
        .col1 {
            width: 255px;
            margin-right: 42px;
        }
    
        .col2 {
            flex: 1;
        }
    
    }
}



:host .idp ::ng-deep {
    .anticon-delete {
        &:hover {
            color: sandybrown;
        }
    }
}