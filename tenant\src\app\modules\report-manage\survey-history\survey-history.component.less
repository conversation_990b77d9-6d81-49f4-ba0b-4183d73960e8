.container {
    margin-top: 20px;
    width: 100%;
    height: auto;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;

        .text {
            width: 102px;
            height: 33px;
            font-size: 24px;
            font-family: PingFangSC-Thin, PingFang SC;
            font-weight: 100;
            color: #17314C;
            line-height: 33px;
        }

        .input {
            .search {
                width: 186px;
                height: 30px;
                background: rgba(255, 255, 255, 1);
                border-radius: 15px;

                .ant-input {
                    border-radius: 15px;
                    border: none;
                    border: solid 1px gainsboro;
                }
            }
        }
    }

    .body {
        margin: 10px 0px;
    }
}

.maxW {
    max-width: 350px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
}

.nowrap {
    white-space: nowrap;
}

button {
    padding: 0;
}

.inprogress {
    white-space: nowrap;
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #45BFD9;
    line-height: 20px;
}

.iconbtn {
    width: 20px;
    height: 24px;
    margin-left: 10px;
}

td {
    padding: 10px 16px!important;
}