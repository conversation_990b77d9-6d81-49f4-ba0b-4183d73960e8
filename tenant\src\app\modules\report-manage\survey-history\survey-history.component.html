<div class="container">

    <div class="header">
      <div class="text">
        组织报告
      </div>
      <div class="input">
        <nz-input-group [nzPrefix]="suffixIconSearch" class="search">
          <input type="text" nz-input placeholder="请输入关键词" [(ngModel)]="searchName" (keyup.enter)="loadData()" />
        </nz-input-group>
        <ng-template #suffixIconSearch>
          <i nz-icon nzType="search" style="color: #409EFF" (click)="loadData()"></i>
        </ng-template>
      </div>
    </div>
  
    <div class="body">
  
      <nz-table #basicTable [nzData]="dataSet" [nzFrontPagination]="false">
  
        <thead>
          <tr>
            <th class="nowrap">序号</th>
            <th class="nowrap">活动</th>
            <th class="nowrap">问卷</th>
            <th class="nowrap">调研说明</th>
            <th class="nowrap">生成时间</th>
            <!-- <th class="nowrap">报告类型</th> -->
            <th class="nowrap">操作</th>
          </tr>
        </thead>
  
        <tbody>
          <tr *ngFor="let data of basicTable.data; let i = index;">
            <td>{{i+1 + (currentPage -1) * pageSize }}</td>
            <td class="nowrap"> 
                {{data.projectName}}
            </td>
            <td class="nowrap"> 
                {{data.questionnaireName}}
            </td>
            <td>
              <div style="display: flex; align-items: center; justify-content: space-between;">
                
                <div class="maxW" [id]="'content' + data.id">
                  <div>
                    组织：
                    <ng-container *ngFor="let item of data.organizations; let ind = index;">
                      <span *ngIf="ind > 0">,&nbsp;</span>
                      <span>
                        {{item.name?.zh_CN}}
                      </span>
                    </ng-container>
                  </div>
                  
                  <div *ngFor="let demo of data.demographics">
                    {{demo.name.zh_CN}}：
                    <ng-container *ngFor="let item of demo.children; let ind = index;">
                      <span *ngIf="ind > 0">,&nbsp;</span>
                      <span>
                        {{item.name.zh_CN}}
                      </span>
                    </ng-container>
                  </div>

                  <div>
                    常模：
                    <ng-container *ngFor="let item of data.norms; let ind = index;">
                      <span *ngIf="ind > 0">,&nbsp;</span>
                      <span>
                        {{item.name.zh_CN}}
                      </span>
                    </ng-container>
                  </div>
                </div>

                <button class="iconbtn" nz-button nzType="default" nzValue="small" (click)="copy(data.id)" 
                  nzTooltipTitle="复制内容" nzTooltipPlacement="topLeft" nz-tooltip >
                  <i nz-icon nzType="copy" nzTheme="outline" ></i>
                </button>
              </div>
            </td>
            <td>{{data.createTime | date:'yyyy/MM/dd HH:mm'}}</td>
            <!-- <td>
              {{data.reportStyle === "EMPTY" ? '手出报告':'机出报告'}}
            </td> -->

            <td>
              <div style="display: flex; flex-direction: column; justify-content: flex-start; align-items: center;">
                
                <ng-container *ngFor="let lan of data.reportLanguages">
                    
                    <span style="background-color: cornflowerblue; padding: 2px 10px;white-space: nowrap;">
                        {{lan === 'zh_CN' ? '中文' :'英文'}}报告
                    </span>

                    <div (click)="tdClick(data.id, data.fileMap[lan], lan)" style="display: flex; justify-content: flex-start; align-items: center;">
                        
                        <ng-container *ngIf="data.fileMap[lan] as file">
                            <ng-container *ngIf="file.fileUrl || file.url; else elseTemplate">
                              <a *ngIf="file.url && data.status !== 'UPLOAD_REPORT'" [href]="file.url +'&token=' + getToken()" target="_blank">
                                <span style="word-break: keep-all;">查看&nbsp;</span>
                              </a>
                              <button *ngIf="file.fileUrl" nz-button nzType="link" (click)="startDownlod(data.reportFiles, file, lan)" [nzLoading]="file.isDownloading">下载</button>
                            </ng-container>
                            <ng-template #elseTemplate>
                              <span class="inprogress">{{'生成中'}}</span>
                              &nbsp;
                            </ng-template>
                            <button nz-button nzType="link" (click)="exportPrismaDimensions(data)" [nzLoading]="data.dimDownloading">维度得分&nbsp;</button>
                        </ng-container>
    
                        <div style="white-space: nowrap;" *ngIf="permission">
                            <nz-upload [nzCustomRequest]="customReq" [nzShowUploadList]="false" >
                              <button nz-button nzType="link" [nzLoading]="data.fileMap[lan].isUploading">
                                <span>上传</span>
                              </button>
                              &nbsp;
                            </nz-upload>
              
                            <button nz-button *ngIf="data.reportStyle !== 'EMPTY'" nzType="link" (click)="redoPdf(data.id, data)"  [nzLoading]="data.isCreating">
                              <span>重新生成</span>
                            </button>
                        </div>
                    </div>
                </ng-container>
              </div>
            </td>
          </tr>
        </tbody>
  
      </nz-table>

      <!-- 分页控件 -->
        <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
            <nz-pagination nzShowQuickJumper
                [(nzPageIndex)]="currentPage" 
                [(nzPageSize)]="pageSize" 
                [nzTotal]="totalCount" 
                [nzSize]="'small'" 
                (nzPageIndexChange)="loadData()" >
            </nz-pagination>
        </div>
  
    </div>
  </div>