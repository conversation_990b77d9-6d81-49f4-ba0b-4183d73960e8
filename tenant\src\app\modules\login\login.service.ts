import { Injectable, OnInit } from "@angular/core";
import { Subject } from "rxjs/Subject";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";
import { ActivatedRoute } from "@angular/router";
import { NzDrawerRef } from "ng-zorro-antd";

@Injectable({
  providedIn: "root",
})
export class LoginService implements OnInit {
  tenantUrl: string = "/tenant-api";

  public drawerRef: NzDrawerRef;

  private userSource = new Subject();

  //获得一个Observable
  userObservable = this.userSource.asObservable();

  constructor(private http: HttpClient, private routeInfo: ActivatedRoute) {}

  emitUser(title: string) {
    this.userSource.next(title);
  }

  ngOnInit() {}

  login(): Observable<any> {
    const api = `${this.tenantUrl}/userAccount/user/getSelfInfo`;
    return this.http.post(api, {});
  }

  createTenantRegister(params): Observable<any> {
    const api = `${this.tenantUrl}/knx/tenant/tenantRegister/create?_allow_anonymous=true`;
    return this.http.post(api, params);
  }

  checkoutCardPassword(psd): Observable<any> {
    const api = `${this.tenantUrl}/knx/bean/card/checkoutCardPassword?password=${psd}&_allow_anonymous=true`;
    return this.http.get(api);
  }

  getPermisionCode(code: string): Observable<any> {
    const api =
      `${this.tenantUrl}/userAccount/admin/menu/getUserPermissions/` + code;
    return this.http.get(api);
  }
}
