<div class="online-report">
  <div class="content client-width">
    <!-- header -->
    <div class="online-report-head">
      <!-- 标题 -->
      <div class="title">我的定制报告</div>
      <!-- 面包屑 -->
      <div class="break-crumb">
        <app-break-crumb [Breadcrumbs]="breadcrumbs"></app-break-crumb>
      </div>
    </div>
    <!-- 搜索 -->
    <div class="online-report-search" nz-row nzGutter="10">
      <div nz-col nzSpan="3">
        <button
          nz-button
          [nzSize]="size"
          style="width: 100%;"
          nzType="primary"
          class="add-btn"
          (click)="onOpenDrawer()"
        >
          <i nz-icon nzType="plus" nzTheme="outline"></i>新建报告
        </button>
      </div>
      <div nz-col nzSpan="4">
        <input
          nz-input
          [nzSize]="size"
          placeholder="请输入关键词"
          [(ngModel)]="searchValue"
        />
      </div>
      <div nz-col nzSpan="4">
        <nz-select
          [nzSize]="size"
          style="width: 100%;"
          [(ngModel)]="searchStatus"
          nzAllowClear
          nzPlaceHolder="请选择状态"
        >
          <nz-option nzValue="jack" nzLabel="Jack"></nz-option>
          <nz-option nzValue="lucy" nzLabel="Lucy"></nz-option>
          <nz-option
            nzValue="disabled"
            nzLabel="Disabled"
            nzDisabled
          ></nz-option>
        </nz-select>
      </div>
      <div nz-col nzSpan="4">
        <nz-select
          [nzSize]="size"
          style="width: 100%;"
          [(ngModel)]="searchReportType"
          nzAllowClear
          nzPlaceHolder="请选择工具"
        >
          <nz-option nzValue="jack" nzLabel="Jack"></nz-option>
          <nz-option nzValue="lucy" nzLabel="Lucy"></nz-option>
          <nz-option
            nzValue="disabled"
            nzLabel="Disabled"
            nzDisabled
          ></nz-option>
        </nz-select>
      </div>
      <div nz-col nzSpan="7">
        <nz-range-picker
          [nzSize]="size"
          style="width: 100%;"
          [(ngModel)]="searchDateRange"
          [nzShowTime]="{ nzMinuteStep: '30', nzFormat: 'HH:mm' }"
          [nzFormat]="'YYYY-MM-DD'"
        ></nz-range-picker>
        <!-- (ngModelChange)="onChange($event)" -->
      </div>
      <div nz-col nzSpan="2">
        <button
          nz-button
          [nzSize]="size"
          style="width: 100%;"
          nzType="primary"
          nzGhost
          (click)="onSrearch()"
        >
          查询
        </button>
      </div>
      <span class="clear" (click)="onClear()">清空条件</span>
    </div>
    <!-- 筛选 -->
    <div nz-row class="online-report-filter">
      <div nz-col nzSpan="3">
        <span class="title">活动列表</span>
        <!-- 筛选-正序倒序 -->
        <div
          class="order"
          [ngClass]="orderType ? '' : 'spin'"
          (click)="onChangeOrderType()"
        >
          <div class="order-top"></div>
          <div class="order-bottom"></div>
        </div>
      </div>
      <!-- 筛选-7天内到期活动 -->
      <div nz-col nzSpan="4">
        <label
          nz-checkbox
          [(ngModel)]="isSevenDays"
          (ngModelChange)="onChangeIsSevenDays($event)"
          >7天内到期活动</label
        >
      </div>
      <!-- 筛选-模式切换 -->
      <div nz-col nzOffset="9" nzSpan="5" class="models">
        <div
          [ngClass]="modelType ? 'active' : ''"
          (click)="onChangeModelType(true)"
        >
          <i class="iconfont icon-card-model"></i>卡片模式
        </div>
        <div class="divider"></div>
        <div
          [ngClass]="!modelType ? 'active' : ''"
          (click)="onChangeModelType(false)"
        >
          <i class="iconfont icon-list-model"></i>列表模式
        </div>
      </div>
      <!-- 翻页切换 -->
      <div nz-col nzSpan="2" nzOffset="1" class="pages">
        <button nz-button nzType="link" (click)="onChangePage(true)">
          <i nz-icon nzType="left" nzTheme="outline"></i>
        </button>
        <span class="pages-text">
          <span>{{ pageCurrent }}</span>
          <span> / </span>
          <span>{{ pageTotal }}</span>
        </span>
        <button nz-button nzType="link" (click)="onChangePage(false)">
          <i nz-icon nzType="right" nzTheme="outline"></i>
        </button>
      </div>
    </div>
    <!-- 数据呈现 -->
    <nz-spin [nzSize]="'large'" [nzSpinning]="isLoading">
      <div class="minheight">
        <div class="online-report-list">
          <!-- 卡片模式 -->
          <ng-container *ngIf="modelType">
            <ng-container *ngFor="let item of data">
              <div class="data-card">
                <div class="data-card-top">
                  <div>
                    <span
                      class="container"
                      [nzTitle]="item.name"
                      nzPlacement="topLeft"
                      nz-tooltip
                      >{{ item.name }}</span
                    >
                    <nz-switch [(ngModel)]="item.isOpen"></nz-switch>
                  </div>
                  <div>
                    <span>有效期：2022-02-03 00:00至2022-02-03 10:00</span>
                    <span class="danger" *ngIf="item.isDue">7天内到期活动</span>
                    <span>ID：{{ item.id }}</span>
                  </div>
                  <div>
                    <span
                      >正在使用：<b>{{ item.number }}</b> 人</span
                    >
                    <span>重新生成</span>
                  </div>
                  <div>
                    <span
                      class="container"
                      [nzTitle]="item.reportType"
                      nz-tooltip
                      >报告工具：{{ item.reportType }}{{ item.reportType
                      }}{{ item.reportType }}</span
                    >
                    <span>创建时间：{{ item.createTime }}</span>
                  </div>
                </div>
                <div class="data-card-bottom">
                  <div class="data-card-bottom-left">
                    <div class="item">
                      <i nz-icon nzType="user-add" nzTheme="outline"></i>
                    </div>
                    <div class="item">
                      <i class="iconfont icon-edit_ic"></i>
                    </div>
                    <nz-dropdown>
                      <div class="item" nz-dropdown>
                        <i
                          class="iconfont icon-a-more1x"
                          style="transform: rotate(90deg);"
                        ></i>
                      </div>
                      <ul nz-menu nzSelectable>
                        <li nz-menu-item>
                          <a>删除</a>
                        </li>
                      </ul>
                    </nz-dropdown>
                  </div>
                  <span class="data-card-bottom-right">
                    {{ item.creator }}
                  </span>
                </div>
                <!-- 蒙板 -->
                <div class="shade" *ngIf="item.isInFormation">
                  <div class="shade-spin rotate">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                  <span class="shade-text">生成中</span>
                </div>
              </div>
            </ng-container>
          </ng-container>
          <!-- 列表模式 -->
          <ng-container *ngIf="!modelType">
            <ng-container *ngFor="let item of data">
              <div class="data-list">
                <div nz-row class="data-list-content">
                  <div nz-col nzSpan="11" class="col">
                    <div
                      class="col-title container"
                      [nzTitle]="item.name"
                      nzPlacement="topLeft"
                      nz-tooltip
                    >
                      {{ item.name }}
                    </div>
                    <div class="col-leftBottom">
                      <span
                        >正在使用： <b>{{ item.number }}</b
                        >人</span
                      >
                      <div class="container">
                        <!-- nzPlacement="topLeft" -->
                        <span [nzTitle]="item.reportType" nz-tooltip
                          >报告工具：{{ item.reportType }}</span
                        >
                      </div>
                    </div>
                  </div>
                  <div nz-col nzSpan="11" nzOffset="2" class="col">
                    <div nz-row nzGutter="8" class="col-rightTop">
                      <div nz-col nzSpan="3">
                        <span>ID：{{ item.id }}</span>
                      </div>
                      <div nz-col nzSpan="7" nzOffset="2">
                        <span class="container"
                          >创建人：{{ item.creator }}</span
                        >
                      </div>
                      <div nz-col nzSpan="6" class="operation" nzOffset="1">
                        <div class="operation-item">
                          <i nz-icon nzType="user-add" nzTheme="outline"></i>
                        </div>
                        <div class="operation-item">
                          <i class="iconfont icon-edit_ic"></i>
                        </div>
                        <nz-dropdown>
                          <div class="operation-item" nz-dropdown>
                            <i
                              class="iconfont icon-a-more1x"
                              style="transform: rotate(90deg);"
                            ></i>
                          </div>
                          <ul nz-menu nzSelectable>
                            <li nz-menu-item>
                              <a>删除</a>
                            </li>
                          </ul>
                        </nz-dropdown>
                      </div>
                      <div nz-col nzSpan="3" nzOffset="2" class="switch">
                        <nz-switch [(ngModel)]="item.isOpen"></nz-switch>
                      </div>
                    </div>
                    <div class="col-rightbottom">
                      <span>有效期：2022-02-03 00:00至2022-02-03 10:00</span>
                      <span class="danger" *ngIf="item.isDue"
                        >7天内到期活动</span
                      >
                      <span>重新生成</span>
                    </div>
                  </div>
                </div>
                <!-- 蒙板 -->
                <div class="shade" *ngIf="item.isInFormation">
                  <div class="shade-spin rotate">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                  <span class="shade-text">生成中</span>
                </div>
              </div>
            </ng-container>
          </ng-container>
          <div class="empty" *ngIf="!isLoading && !data.length">
            <img
              src="assets/images/empty.png"
              alt="add"
              (click)="onOpenDrawer()"
            />
            <p>暂无定制报告，快去新建一个吧～</p>
          </div>
        </div>
      </div>
    </nz-spin>
  </div>
  <!-- 新增报告-侧边栏 -->
  <nz-drawer
    [nzVisible]="addVisible"
    nzPlacement="right"
    nzTitle="新建报告"
    (nzOnClose)="onCloseDrawer()"
    [nzWidth]="584"
    nzWrapClassName="round-right-drawer11"
  >
    <div class="drawer-content">
      <div nz-row nzGutter="8" class="drawer-content-head">
        <div nz-col nzSpan="9">
          <b>请选择您中意的报告模版</b>
        </div>
        <div nz-col nzSpan="8" nzOffset="3">
          <nz-input-group [nzSuffix]="suffixIconSearch">
            <input
              type="text"
              nz-input
              placeholder="请输入关键词"
              [nzSize]="size"
              [(ngModel)]="searchReportTemplateKeyword"
              (keydown.enter)="onFilterReportTemplate()"
            />
          </nz-input-group>
          <ng-template #suffixIconSearch>
            <i
              class="iconfont icon-search_o drawer-content-head-search"
              (click)="onFilterReportTemplate()"
            ></i>
          </ng-template>
        </div>
        <div nz-col nzSpan="4">
          <nz-select
            [nzSize]="size"
            style="width: 100%;"
            [(ngModel)]="searchReportTemplateClassify"
            nzAllowClear
            nzPlaceHolder="分类"
            (ngModelChange)="onFilterReportTemplate()"
          >
            <nz-option nzValue="0" nzLabel="分类1"></nz-option>
            <nz-option nzValue="1" nzLabel="分类2"></nz-option>
          </nz-select>
        </div>
      </div>
      <div class="drawer-content-types">
        <ng-container *ngIf="reportTemplateFilter.length">
          <div class="drawer-content-types-box" id="sliderContainer">
            <!-- <ng-container *ngFor="let item of reportTemplateFilter; let idx = index"> -->
            <ng-container *ngFor="let item of reportTemplateFilter">
              <div
                [ngClass]="checkedReportTemplate === item.type ? 'checked' : ''"
                (click)="onSelectReportTemplate(item)"
              >
                <img [src]="item.img" [alt]="item.name" />
                <p nz-tooltip [nzTitle]="item.name">{{ item.name }}</p>
              </div>
            </ng-container>
          </div></ng-container
        >
        <ng-container *ngIf="reportTemplateFilter.length === 0">
          <app-empty></app-empty>
        </ng-container>
      </div>
      <div class="drawer-content-form">
        <p>请输入报告名称</p>
        <input
          type="text"
          nz-input
          placeholder="请输入报告名称"
          [nzSize]="size"
          [(ngModel)]="onlineReportName"
        />
      </div>
      <div class="drawer-content-form">
        <p>请选择你需要的活动，绑定数据</p>
        <nz-select
          [nzSize]="size"
          style="width: 100%;"
          [(ngModel)]="onlineReportBindProject"
          nzAllowClear
          nzPlaceHolder="输入活动名称模糊搜索"
        >
          <nz-option nzValue="0" nzLabel="活动1"></nz-option>
          <nz-option nzValue="1" nzLabel="活动2"></nz-option>
        </nz-select>
      </div>
      <div class="drawer-content-shortcut">
        <p>猜你需要绑定以下活动</p>
        <div>
          <ng-container *ngFor="let item of shortcutList">
            <span
              [ngClass]="checkedShortcut === item.id ? 'checked' : ''"
              (click)="onSelectShortcut(item)"
              [nzTitle]="shortcutTemplate"
              nzPlacement="topLeft"
              nz-tooltip
            >
              {{ item.name }}/{{ item.id }}
            </span>
            <ng-template #shortcutTemplate>
              <p>活动名称：{{ item.name }}</p>
              <p>活动工具：{{ item.type }}</p>
              <p>活动ID：{{ item.id }}</p>
            </ng-template>
          </ng-container>
        </div>
      </div>
    </div>
    <div class="drawer-footer">
      <button nz-button nzType="primary" (click)="onConfirm()">
        确认
      </button>
    </div>
  </nz-drawer>
</div>
