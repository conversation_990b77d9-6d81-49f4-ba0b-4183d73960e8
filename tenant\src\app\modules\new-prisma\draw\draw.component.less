:host {
  scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #f1f1f1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #c1c1c1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

.bg-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(
    180deg,
    #6552e2 0%,
    #a17de1 10%,
    #f8eef6 28%,
    #bacbff 100%
  );
}
div {
  box-sizing: border-box;
}
.mask {
  // 中奖预览遮罩
  position: absolute;
  left: 12px;
  top: 54px;
  width: 202px;
  height: 398px;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10;
  .icon-icon-close {
    display: block;
    font-size: 12px;
    margin: 58px 95px 10px;
    color: #ffffff;
  }
  .mask-bg-img {
    height: 262px;
    background: url(../../../../assets/images/prisma/winning-bg-mobile.png)
      no-repeat;
    padding: 0 40px;
    padding-top: 9px;
    .mask-title {
      display: flex;
      justify-content: center;
      font-size: 12px;
      transform: scale(0.83);
      font-weight: normal;
      color: #261458;
      line-height: 14px;
      overflow-x: hidden;
      max-height: 30px;
    }
    > p.desc {
      // width: 136px;
      // height: 33px;
      font-size: 12px;
      transform: scale(0.67);
      font-weight: normal;
      color: #707070;
      text-align: center;
      line-height: 16px;
      overflow: hidden;
      word-wrap: break-word;
      margin: 0 auto;
      text-overflow: ellipsis;
    }
    .view-prize {
      margin: 20px auto 0;
      width: 86px;
      height: 24px;
      cursor: pointer;
    }
    div.prize-picture {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 106px;
      height: 86px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      margin: 4px auto 0;
    }
  }
}
.box {
  position: relative;
  width: 100%;
  // overflow-y: auto;

  .my-prize {
    position: absolute;
    right: -7px;
    top: 225px;
    width: 35px;
    height: 35px;
    cursor: pointer;
  }
  .title {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    // height: 15%;
    height: 74px;
    margin-bottom: 2px;
    background-size: 202px !important;
    // background: url(../../../../assets/images/prisma/image-title-PC.png) no-repeat;
  }
  .show-box {
    margin: 0 auto 10px;
    width: 184px;
    height: 220px;
    box-sizing: border-box;
    padding: 14px;
    border-radius: 8px;
    background: #9877e0;
    .lottery-result-box {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .jiugongge {
      margin: 0 auto 10px;
      width: 157px;
      height: 157px;
      padding: 2px;
      border-radius: 4px;
      box-sizing: border-box;
      background: #4d29b1;
      img {
        max-width: 100%;
      }
      li {
        display: flex;
        flex-wrap: wrap;
      }
      .prize-box {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 47px;
        height: 47px;
        padding: 2px;
        margin: 2px;
        box-sizing: border-box;
        border: 1px solid transparent;
        border-radius: 5px 5px 5px 5px;
        .prize-detail-box {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
          height: 100%;
          background: #f3eff7;
          border-radius: 4px 4px 4px 4px;
          // background: url(assets/images/prisma/lottery-btn-mobile.png) no-repeat;
          img {
            margin: 5px 10px 2px;
            // width: 22px;
            height: 22px;
          }

          span {
            display: inline-block;
            text-align: center;
            width: 100%;
            height: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            transform: scale(0.5);
            font-size: 12px;
            font-weight: normal;
            color: #707070;
            line-height: 12px;
          }
        }
      }
      .prize-box-active {
        border: 1px solid #bca7eb;
      }
      li:first-child {
        > div:first-child {
          border: 1px solid #9877e1;
          > div {
            background: #9877e1;
            span {
              color: #f4f0f7;
            }
          }
        }
      }
    }
    .time-box {
      width: 100%;
      height: calc(100% - 164px);
      background: #4d29b1;
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      .time-detail-box {
        width: 246px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: normal;
        color: #f4f0f7;
        transform: scale(0.5);
        .time-block {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 30px;
          height: 26px;
          color: #4d29b1;
          background: #ffffff;
          border-radius: 2px 2px 2px 2px;
          margin: 0 4px;
        }
        span {
          display: inline-block;
          white-space: nowrap;
          // transform: scaleX(0.5);
        }
      }
    }
  }
  .rules-box {
    margin: 10px auto 10px;
    width: 184px;
    position: relative;
    padding-bottom: 20px;
    > div {
      width: 100%;
      // background: linear-gradient(135deg, rgba(255, 255, 255, 0.10000000149011612) 0%, #FFFFFF 100%);
      border-radius: 16px 16px 16px 16px;
      border: 1px solid #ffffff;
      min-height: 60px;
    }
    .rules-title {
      position: absolute;
      top: -4px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 20px;
      margin-bottom: 16px;
      img {
        width: 50%;
      }
    }
    .rules-list {
      margin: 20px auto 10px;
      width: 180px;
      overflow-y: auto;
      height: 160px;

      > div {
        word-break: break-all;
        margin: 0 20px;
        font-size: 12px;
        font-weight: normal;
        // color: #3C2383;
        // background-color: rgba(215, 215, 215, 1);
      }
    }
  }
}
.pc .bg-box {
  height: 286px;
}
.pc {
  .mask {
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    .icon-icon-close {
      margin-top: 0;
    }
    .mask-bg-img {
      width: 204px;
      height: 220px;
      background-size: 204px 220px;
      .prize-picture {
        width: 80px;
        height: 65px;
      }
      .view-prize {
        margin: 12px auto 0;
      }
    }
  }
}

.pc .box {
  width: 456px;
  height: 286px;
  display: flex;
  justify-content: center;
  overflow-y: auto;
  // align-items: center;

  .my-prize {
    position: absolute;
    top: 160px;
    right: 30px;
    width: 30px;
    height: 30px;
  }
  .title {
    width: 165px;
    height: 50px;
    background-size: 165px !important;
  }
  .left-box {
    margin-top: 20px;
  }
  .show-box {
    width: 160px;
    height: 190px;
    padding: 12px;
    .lottery-result-box {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .jiugongge {
      margin: 0 auto 8px;
      width: 136px;
      height: 136px;
      img {
        max-width: 100%;
      }
      .prize-box {
        width: 40px;
        height: 40px;
        .prize-detail-box {
          background-size: 34px !important;
          img {
            margin: 5px 9px 1px;
            // width: 18px;
            height: 18px;
          }
          span {
            transform: scale(0.4);
            line-height: 8px;
          }
        }
      }
    }
    .time-box {
      height: 20px;
      border-radius: 10px;
    }
  }
  .rules-box {
    margin: 71px 20px 40px;
    position: relative;
    width: 142px;
    min-height: 40px;
    .rules-title {
      height: 14px;
      margin-bottom: 0;
    }
    .rules-list {
      width: 135px;
      overflow-y: auto;
      height: 160px;
    }
  }
}
