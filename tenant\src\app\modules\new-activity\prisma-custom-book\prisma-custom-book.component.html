<div style="background:#F5F6FA; height: 100%;" id="orgDiv">
  <div class="content client-width">
    <div class="body" #deliveryTable>
      <ul style="display: flex;justify-content: flex-end;">
        <app-break-crumb
          [Breadcrumbs]="Breadcrumbs"
          [queryParams]="queryParams"
        ></app-break-crumb>
      </ul>
      <div
        style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px;"
      >
        <div class="title">
          <h2>自定义题本管理</h2>
        </div>
        <div class="searchDiv">
          <nz-input-group [nzPrefix]="IconSearch">
            <input
              class="input-search"
              type="text"
              nz-input
              placeholder="请输入关键词"
              [(ngModel)]="keyWord"
              (keydown.enter)="search()"
            />
          </nz-input-group>
          <ng-template #IconSearch>
            <i
              nz-icon
              nzType="search"
              class="icon-search"
              (click)="search()"
            ></i>
          </ng-template>
        </div>
      </div>

      <div class="mb-12 mt-12">
        <app-i18n-select
          [active]="lan"
          (selectChange)="onSelectI18n($event)"
        ></app-i18n-select>
      </div>

      <div class="action" *ngIf="uploadshow || exportshow">
        <div class="flex items-center justify-center">
          <button
            class="primary-btn mr-16"
            *ngIf="listChecked !== 'checked'"
            (click)="showModal()"
          >
            <i nz-icon nzType="plus-circle" nzTheme="fill"></i>新增
          </button>

          <nz-upload
            *ngIf="uploadshow"
            [nzCustomRequest]="customReq"
            [nzShowUploadList]="false"
          >
            <button class="default-btn">
              <i class="iconfont icon-icon_export"></i>批量导入
            </button>
          </nz-upload>
          <div class="download_template">
            <button
              *ngIf="uploadshow && downLoadShow"
              nz-button
              nzType="link"
              [nzLoading]="buttonload"
              (click)="exportQusBook()"
            >
              下载模板
            </button>
          </div>
        </div>
        <div class="right-btn">
          <app-btn
            *ngIf="exportshow && !downLoadShow"
            [text]="'导出'"
            [image]="'./assets/images/org/export.png'"
            [hoverColor]="'#B483D6'"
            [hoverImage]="'./assets/images/org/export_hover.png'"
            (btnclick)="exportQusBook()"
          >
          </app-btn>

          <multi-view
            *ngIf="reportType !== 'CULTURE_INVESTIGATION_RESEARCH'"
            [questionnaireId]="questionnaireId"
            [lan]="lan"
            (loadData)="loadData()"
          ></multi-view>
          <pentrate-modal
            *ngIf="uploadshow"
            [questionnaireId]="questionnaireId"
            [lan]="lan"
            (loadData)="loadData()"
          ></pentrate-modal>

          <div
            class="delete-box"
            *ngIf="uploadshow"
            nz-popconfirm
            nzPopconfirmTitle="要删除选中的题目吗？"
            (nzOnConfirm)="onDeleteQuestion()"
          >
            <div class="penetration-questions">
              <i class="iconfont icon-icon_delete"></i>
              <span>批量删除</span>
            </div>
          </div>
        </div>
      </div>

      <div class="scroll topiclist">
        <nz-spin [nzSpinning]="isSpinning">
          <nz-table
            #basicTable
            [nzData]="topicList"
            [nzScroll]="{ y: tableTop + '' }"
            [nzFrontPagination]="false"
          >
            <thead>
              <tr>
                <th nzWidth="60px" *ngIf="projectType != 'ANSWERING'"></th>
                <th
                  *ngIf="uploadshow"
                  nzWidth="60px"
                  nzShowCheckbox
                  [(nzChecked)]="isAllChecked"
                  (nzCheckedChange)="checkAll($event)"
                ></th>
                <th nzWidth="60px">序号</th>
                <th nzWidth="130px">维度名称</th>
                <th nzWidth="100px"></th>
                <th nzWidth="220px">题干</th>
                <th nzWidth="220px">选项</th>
                <!-- <th nzWidth="120px">计算方式</th> -->
                <th nzWidth="80px">状态</th>
                <th *ngIf="uploadshow" nzWidth="100px">操作</th>
              </tr>
            </thead>
            <tbody
              *ngIf="projectType != 'ANSWERING'"
              dragula="VAMPIRESNEW"
              [(dragulaModel)]="basicTable.data"
              (dragulaModelChange)="dragnumber($event)"
            >
              <ng-container *ngFor="let data of basicTable.data; let i = index">
                <tr
                  *ngIf="!data.isPierceThrough"
                  [ngClass]="data.isPierceThrough ? 'through-tr' : ''"
                >
                  <td>
                    <i
                      class="iconfont icon-caidan"
                      style="cursor: pointer;"
                    ></i>
                  </td>
                  <td
                    *ngIf="uploadshow"
                    nzShowCheckbox
                    [nzDisabled]="data.isForceSelect"
                    [(nzChecked)]="data.isDeleted"
                    (nzCheckedChange)="checkOne()"
                  ></td>
                  <td>{{ i + 1 }}</td>
                  <td>
                    {{
                      data.dimensionName && data.dimensionName[lan]
                        ? data.dimensionName[lan]
                        : "--"
                    }}
                    <span
                      *ngIf="
                        data.childDimensionName && data.childDimensionName[lan]
                      "
                      >/{{
                        data.childDimensionName && data.childDimensionName[lan]
                          ? data.childDimensionName[lan]
                          : "--"
                      }}</span
                    >
                  </td>
                  <td>
                    <span
                      *ngIf="!data.isPierceThrough"
                      class="tip-box"
                      [ngStyle]="{
                        color: tips[data.type].color,
                        background: tips[data.type].bg
                      }"
                    >
                      <!-- todo: 非中文多语言默认展示中文 -->
                      {{
                        tips[data.type].name[lan] || tips[data.type].name.zh_CN
                      }}
                    </span>
                    <span
                      *ngIf="data.isPierceThrough"
                      class="tip-box"
                      [ngStyle]="{
                        color: tips.isPierceThrough.color,
                        background: tips.isPierceThrough.bg
                      }"
                      >{{
                        tips.isPierceThrough.name[lan] ||
                          tips.isPierceThrough.name.zh_CN
                      }}</span
                    >
                  </td>
                  <td nzBreakWord>
                    <span
                      *ngIf="data.replaceName"
                      [innerHTML]="data.replaceName[lan] | html"
                    ></span>
                    <span
                      *ngIf="data.isRevisionName"
                      class="block"
                      nz-tooltip
                      nzTooltipTitle="已修订"
                    ></span>
                    <span
                      class="block"
                      style="background: #9833FF;"
                      nz-tooltip
                      *ngIf="
                        data.questionDimensionMappings &&
                        data.questionDimensionMappings.length > 1
                      "
                      [nzTooltipTitle]="titleTemplate"
                    ></span>
                    <ng-template #titleTemplate>
                      <span
                        style="display: block;"
                        *ngFor="let item of data.questionDimensionMappings"
                        >{{
                          item.labelName && item.labelName[lan]
                            ? item.labelName[lan]
                            : ""
                        }}{{
                          item.oneRankDimensionName &&
                          item.oneRankDimensionName[lan]
                            ? "-" + item.oneRankDimensionName[lan]
                            : ""
                        }}{{
                          item.twoRankDimensionName &&
                          item.twoRankDimensionName[lan]
                            ? "-" + item.twoRankDimensionName[lan]
                            : ""
                        }}{{
                          item.threeRankDimensionName &&
                          item.threeRankDimensionName[lan]
                            ? "-" + item.threeRankDimensionName[lan]
                            : ""
                        }}
                      </span>
                    </ng-template>
                  </td>
                  <td nzBreakWord>
                    <span *ngFor="let option of data.options.options"
                      >{{ option.name[lan] }}/</span
                    >
                    <span
                      *ngIf="data.isRevisionOption"
                      class="block"
                      nz-tooltip
                      nzTooltipTitle="已修订"
                    ></span>
                  </td>
                  <!-- <td nzBreakWord>{{data.options?.prismaCalType === 'APPROVAL_RATE' ? '赞成百分比' : ''}}</td> -->
                  <td>
                    {{
                      data.isRequire
                        ? requireMap[lan] || requireMap["zh_CN"]
                        : noRequireMap[lan] || noRequireMap["zh_CN"]
                    }}
                  </td>
                  <td *ngIf="uploadshow">
                    <ng-container *ngIf="data.isEditable">
                      <app-btn
                        [text]="''"
                        *ngIf="!isNewQuestionBook()"
                        [image]="'./assets/images/org/edit.png'"
                        [hoverColor]="'#409EFF'"
                        [hoverImage]="'./assets/images/org/edit_hover.png'"
                        (btnclick)="onEditQuestion(data.id, data.ciasLabel)"
                      >
                      </app-btn>
                      <app-btn
                        *ngIf="isNewQuestionBook()"
                        [text]="''"
                        [image]="'./assets/images/org/edit.png'"
                        [hoverColor]="'#409EFF'"
                        [hoverImage]="'./assets/images/org/edit_hover.png'"
                        (btnclick)="goSubCustom(data)"
                      ></app-btn>
                      <app-btn
                        nz-popconfirm
                        nzPopconfirmTitle="要删除选中的题目吗？"
                        (nzOnConfirm)="delete(data.id)"
                        [text]="''"
                        [image]="'./assets/images/org/del.png'"
                        [hoverColor]="'#409EFF'"
                        [hoverImage]="'./assets/images/org/del_hover.png'"
                      >
                      </app-btn>
                    </ng-container>
                  </td>
                </tr>
                <tr *ngIf="data.isPierceThrough">
                  <td [attr.colspan]="colSpan" style="padding: 0;">
                    <nz-table [nzShowPagination]="false" [nzData]="[{}]">
                      <thead>
                        <th style="padding: 16px; width: 60px;">
                          <i
                            class="iconfont icon-caidan"
                            style="cursor: pointer;"
                          ></i>
                        </th>
                        <th
                          style="padding: 16px; width: 60px; padding-left: 24px;"
                          *ngIf="uploadshow"
                          nzShowCheckbox
                          [(nzChecked)]="data.isDeleted"
                          (nzCheckedChange)="checkOne()"
                        ></th>
                        <th style="padding: 16px; width: 60px;">{{ i + 1 }}</th>
                        <th style="padding: 16px; width: 130px;">
                          <p class="word-wrap" style="width: 98px;">
                            {{
                              data.dimensionName && data.dimensionName[lan]
                                ? data.dimensionName[lan]
                                : "--"
                            }}
                          </p>
                          <span
                            *ngIf="
                              data.childDimensionName &&
                              data.childDimensionName[lan]
                            "
                            >/{{
                              data.childDimensionName &&
                              data.childDimensionName[lan]
                                ? data.childDimensionName[lan]
                                : "--"
                            }}</span
                          >
                        </th>
                        <th style="padding: 16px; width: 100px;">
                          <span
                            *ngIf="!data.isPierceThrough"
                            class="tip-box"
                            [ngStyle]="{
                              color: tips[data.type].color,
                              background: tips[data.type].bg
                            }"
                          >
                            <!-- todo: 非中文多语言默认展示中文 -->
                            {{
                              tips[data.type].name[lan] ||
                                tips[data.type].name.zh_CN
                            }}
                          </span>
                          <span
                            *ngIf="data.isPierceThrough"
                            class="tip-box"
                            [ngStyle]="{
                              color: tips.isPierceThrough.color,
                              background: tips.isPierceThrough.bg
                            }"
                            >{{
                              tips.isPierceThrough.name[lan] ||
                                tips.isPierceThrough.name.zh_CN
                            }}</span
                          >
                        </th>
                        <th style="padding: 16px;width: 220px;" nzBreakWord>
                          <p
                            class="word-wrap"
                            style="width: 188px;"
                            *ngIf="data.replaceName"
                            [innerHTML]="data.replaceName[lan] | html"
                          ></p>
                          <span
                            *ngIf="data.isRevisionName"
                            class="block"
                            nz-tooltip
                            nzTooltipTitle="已修订"
                          ></span>

                          <span
                            class="block"
                            style="background: #9833FF;"
                            nz-tooltip
                            *ngIf="
                              data.questionDimensionMappings &&
                              data.questionDimensionMappings.length > 1
                            "
                            [nzTooltipTitle]="titleTemplate"
                          ></span>
                          <ng-template #titleTemplate>
                            <span
                              style="display: block;"
                              *ngFor="
                                let item of data.questionDimensionMappings
                              "
                              >{{
                                item.labelName && item.labelName[lan]
                                  ? item.labelName[lan]
                                  : ""
                              }}{{
                                item.oneRankDimensionName &&
                                item.oneRankDimensionName[lan]
                                  ? "-" + item.oneRankDimensionName[lan]
                                  : ""
                              }}{{
                                item.twoRankDimensionName &&
                                item.twoRankDimensionName[lan]
                                  ? "-" + item.twoRankDimensionName[lan]
                                  : ""
                              }}{{
                                item.threeRankDimensionName &&
                                item.threeRankDimensionName[lan]
                                  ? "-" + item.threeRankDimensionName[lan]
                                  : ""
                              }}
                            </span>
                          </ng-template>
                        </th>
                        <th style="padding: 16px;width: 220px;" nzBreakWord>
                          <div
                            class="word-wrap"
                            style="width: 188px;display: flex;flex-wrap: wrap;"
                          >
                            <span *ngFor="let option of data.options.options"
                              >{{ option.name[lan] }}/</span
                            >
                            <span
                              *ngIf="data.isRevisionOption"
                              class="block"
                              nz-tooltip
                              nzTooltipTitle="已修订"
                            ></span>
                          </div>
                        </th>
                        <!-- <th style="padding: 16px; width: 120px;" nzBreakWord>{{data.options?.prismaCalType ===
                          'APPROVAL_RATE' ? '赞成百分比' : ''}}</th> -->
                        <th style="padding: 16px; width: 80px;">
                          {{
                            data.isRequire
                              ? requireMap[lan] || requireMap["zh_CN"]
                              : noRequireMap[lan] || noRequireMap["zh_CN"]
                          }}
                        </th>
                        <th
                          style="padding: 16px; width: 100px;"
                          *ngIf="uploadshow"
                        >
                          <ng-container *ngIf="data.isEditable">
                            <app-btn
                              [text]="''"
                              *ngIf="!isNewQuestionBook()"
                              [image]="'./assets/images/org/edit.png'"
                              [hoverColor]="'#409EFF'"
                              [hoverImage]="
                                './assets/images/org/edit_hover.png'
                              "
                              (btnclick)="
                                onEditQuestion(data.id, data.ciasLabel)
                              "
                            >
                            </app-btn>
                            <app-btn
                              *ngIf="isNewQuestionBook()"
                              [text]="''"
                              [image]="'./assets/images/org/edit.png'"
                              [hoverColor]="'#409EFF'"
                              [hoverImage]="
                                './assets/images/org/edit_hover.png'
                              "
                              (btnclick)="goSubCustom(data)"
                            ></app-btn>
                            <app-btn
                              nz-popconfirm
                              nzPopconfirmTitle="要删除选中的题目吗？"
                              (nzOnConfirm)="delete(data.id)"
                              [text]="''"
                              [image]="'./assets/images/org/del.png'"
                              [hoverColor]="'#409EFF'"
                              [hoverImage]="'./assets/images/org/del_hover.png'"
                            >
                            </app-btn>
                          </ng-container>
                        </th>
                      </thead>
                      <tbody *ngIf="data.resultQuestion">
                        <tr>
                          <td></td>
                          <td *ngIf="uploadshow"></td>
                          <!-- 序号 -->
                          <td></td>
                          <!-- 维度名称 -->
                          <td>
                            <p class="word-wrap">
                              {{
                                data.resultQuestion.dimensionName &&
                                data.resultQuestion.dimensionName[lan]
                                  ? data.resultQuestion.dimensionName[lan]
                                  : "--"
                              }}
                            </p>
                          </td>
                          <td></td>
                          <!-- 题干 -->
                          <td>
                            <p
                              class="word-wrap"
                              style="width: 188px;"
                              [innerHTML]="
                                data.resultQuestion.replaceName[lan] | html
                              "
                            ></p>
                            <span
                              *ngIf="data.resultQuestion.isRevisionName"
                              class="block"
                              nz-tooltip
                              nzTooltipTitle="已修订"
                            ></span>

                            <span
                              class="block"
                              style="background: #9833FF;"
                              nz-tooltip
                              *ngIf="
                                data.resultQuestion.questionDimensionMappings &&
                                data.resultQuestion.questionDimensionMappings
                                  .length > 1
                              "
                              [nzTooltipTitle]="titleTemplate"
                            ></span>
                            <ng-template #titleTemplate>
                              <span
                                style="display: block;"
                                *ngFor="
                                  let item of data.resultQuestion
                                    .questionDimensionMappings
                                "
                                >{{
                                  item.labelName && item.labelName[lan]
                                    ? item.labelName[lan]
                                    : ""
                                }}{{
                                  item.oneRankDimensionName &&
                                  item.oneRankDimensionName[lan]
                                    ? "-" + item.oneRankDimensionName[lan]
                                    : ""
                                }}{{
                                  item.twoRankDimensionName &&
                                  item.twoRankDimensionName[lan]
                                    ? "-" + item.twoRankDimensionName[lan]
                                    : ""
                                }}{{
                                  item.threeRankDimensionName &&
                                  item.threeRankDimensionName[lan]
                                    ? "-" + item.threeRankDimensionName[lan]
                                    : ""
                                }}
                              </span>
                            </ng-template>
                          </td>
                          <!-- 选项 -->
                          <td>
                            <!-- <p class="word-wrap" style="width: 188px;" style="width: 188px;display: flex;flex-wrap: wrap;"> -->
                            <span
                              *ngFor="
                                let option of data.resultQuestion.options
                                  .options;
                                let optidx = index
                              "
                              >{{
                                data.resultQuestion.type ===
                                "MULTIPLE_CHOICE_ESSAY_QUESTION"
                                  ? ""
                                  : data.resultQuestion.type ===
                                    "ESSAY_QUESTION"
                                  ? ""
                                  : (optidx === 0 ? "" : "/") + option.name[lan]
                              }}</span
                            >

                            <span
                              *ngIf="data.resultQuestion.isRevisionOption"
                              class="block"
                              nz-tooltip
                              nzTooltipTitle="已修订"
                            ></span>
                            <!-- </p> -->
                          </td>
                          <!-- 计算方式 -->
                          <!-- <td></td> -->
                          <!-- 状态 -->
                          <td>
                            {{
                              data.resultQuestion.isRequire
                                ? requireMap[lan] || requireMap["zh_CN"]
                                : noRequireMap[lan] || noRequireMap["zh_CN"]
                            }}
                          </td>
                          <!-- 操作 -->
                          <td *ngIf="uploadshow">
                            <!-- {{reportType}} -->
                            <ng-container *ngIf="data.isEditable">
                              <app-btn
                                [text]="''"
                                *ngIf="!isNewQuestionBook()"
                                [image]="'./assets/images/org/edit.png'"
                                [hoverColor]="'#409EFF'"
                                [hoverImage]="
                                  './assets/images/org/edit_hover.png'
                                "
                                (btnclick)="
                                  onEditQuestion(data.resultQuestion.id)
                                "
                              >
                              </app-btn>
                              <app-btn
                                *ngIf="isNewQuestionBook()"
                                [text]="''"
                                [image]="'./assets/images/org/edit.png'"
                                [hoverColor]="'#409EFF'"
                                [hoverImage]="
                                  './assets/images/org/edit_hover.png'
                                "
                                (btnclick)="
                                  goSubCustom(data.resultQuestion, data)
                                "
                              ></app-btn>
                            </ng-container>
                          </td>
                        </tr>
                      </tbody>
                    </nz-table>
                  </td>
                </tr>
              </ng-container>
            </tbody>
            <tbody *ngIf="projectType == 'ANSWERING'">
              <ng-container *ngFor="let data of basicTable.data; let i = index">
                <tr [ngClass]="data.isPierceThrough ? 'through-tr' : ''">
                  <td
                    *ngIf="uploadshow"
                    [nzDisabled]="data.isForceSelect"
                    nzShowCheckbox
                    [(nzChecked)]="data.isDeleted"
                    (nzCheckedChange)="checkOne()"
                  ></td>
                  <td>{{ i + 1 }}</td>
                  <td>
                    {{
                      data.dimensionName && data.dimensionName[lan]
                        ? data.dimensionName[lan]
                        : "--"
                    }}<span
                      *ngIf="
                        data.childDimensionName && data.childDimensionName[lan]
                      "
                      >/{{
                        data.childDimensionName && data.childDimensionName[lan]
                          ? data.childDimensionName[lan]
                          : "--"
                      }}</span
                    >
                  </td>
                  <td>
                    <span
                      *ngIf="!data.isPierceThrough"
                      class="tip-box"
                      [ngStyle]="{
                        color: tips[data.type].color,
                        background: tips[data.type].bg
                      }"
                    >
                      <!-- todo: 非中文多语言默认展示中文 -->
                      {{
                        tips[data.type].name[lan] || tips[data.type].name.zh_CN
                      }}
                    </span>
                    <span
                      *ngIf="data.isPierceThrough"
                      class="tip-box"
                      [ngStyle]="{
                        color: tips.isPierceThrough.color,
                        background: tips.isPierceThrough.bg
                      }"
                      >{{
                        tips.isPierceThrough.name[lan] ||
                          tips.isPierceThrough.name.zh_CN
                      }}</span
                    >
                  </td>
                  <td nzBreakWord>
                    <span
                      *ngIf="data.replaceName"
                      [innerHTML]="data.replaceName[lan] | html"
                    ></span>
                    <span
                      *ngIf="data.isRevisionName"
                      class="block"
                      nz-tooltip
                      nzTooltipTitle="已修订"
                    ></span>
                  </td>
                  <td nzBreakWord>
                    <span *ngFor="let option of data.options.options"
                      >{{ option.name[lan] }}/</span
                    >
                    <span
                      *ngIf="data.isRevisionOption"
                      class="block"
                      nz-tooltip
                      nzTooltipTitle="已修订"
                    ></span>
                  </td>
                  <!-- <td nzBreakWord>{{data.options?.prismaCalType === 'APPROVAL_RATE' ? '赞成百分比' : ''}}</td> -->
                  <td>
                    {{
                      data.isRequire
                        ? requireMap[lan] || requireMap["zh_CN"]
                        : noRequireMap[lan] || noRequireMap["zh_CN"]
                    }}
                  </td>
                  <td *ngIf="uploadshow">
                    <ng-container *ngIf="data.isEditable">
                      <app-btn
                        [text]="''"
                        *ngIf="!isNewQuestionBook()"
                        [image]="'./assets/images/org/edit.png'"
                        [hoverColor]="'#409EFF'"
                        [hoverImage]="'./assets/images/org/edit_hover.png'"
                        (btnclick)="onEditQuestion(data.id, data.ciasLabel)"
                      >
                      </app-btn>
                      <app-btn
                        *ngIf="isNewQuestionBook()"
                        [text]="''"
                        [image]="'./assets/images/org/edit.png'"
                        [hoverColor]="'#409EFF'"
                        [hoverImage]="'./assets/images/org/edit_hover.png'"
                        (btnclick)="goSubCustom(data)"
                      ></app-btn>
                      <app-btn
                        nz-popconfirm
                        nzPopconfirmTitle="要删除选中的题目吗？"
                        (nzOnConfirm)="delete(data.id)"
                        [text]="''"
                        [image]="'./assets/images/org/del.png'"
                        [hoverColor]="'#409EFF'"
                        [hoverImage]="'./assets/images/org/del_hover.png'"
                      >
                      </app-btn>
                    </ng-container>
                  </td>
                </tr>
                <tr *ngIf="data.isPierceThrough">
                  <td *ngIf="uploadshow"></td>
                  <!-- 序号 -->
                  <td></td>
                  <!-- 维度名称 -->
                  <td>
                    {{
                      data.resultQuestion.dimensionName &&
                      data.resultQuestion.dimensionName[lan]
                        ? data.resultQuestion.dimensionName[lan]
                        : "--"
                    }}
                  </td>
                  <td></td>
                  <!-- 题干 -->
                  <td>
                    <span
                      [innerHTML]="data.resultQuestion.replaceName[lan] | html"
                    ></span>
                    <span
                      *ngIf="data.resultQuestion.isRevisionName"
                      class="block"
                      nz-tooltip
                      nzTooltipTitle="已修订"
                    ></span>
                  </td>
                  <!-- 选项 -->
                  <td style="word-break: break-all">
                    <span
                      *ngFor="
                        let option of data.resultQuestion.options.options;
                        let optidx = index
                      "
                      >{{
                        data.resultQuestion.type ===
                        "MULTIPLE_CHOICE_ESSAY_QUESTION"
                          ? ""
                          : data.resultQuestion.type === "ESSAY_QUESTION"
                          ? ""
                          : (optidx === 0 ? "" : "/") + option.name[lan]
                      }}</span
                    >
                    <span
                      *ngIf="data.resultQuestion.isRevisionOption"
                      class="block"
                      nz-tooltip
                      nzTooltipTitle="已修订"
                    ></span>
                  </td>
                  <!-- 计算方式 -->
                  <!-- <td></td> -->
                  <!-- 状态 -->
                  <td>
                    {{
                      data.resultQuestion.isRequire
                        ? requireMap[lan] || requireMap["zh_CN"]
                        : noRequireMap[lan] || noRequireMap["zh_CN"]
                    }}
                  </td>
                  <!-- 操作 -->
                  <td *ngIf="uploadshow">
                    <ng-container *ngIf="data.isEditable">
                      <app-btn
                        [text]="''"
                        *ngIf="!isNewQuestionBook()"
                        [image]="'./assets/images/org/edit.png'"
                        [hoverColor]="'#409EFF'"
                        [hoverImage]="'./assets/images/org/edit_hover.png'"
                        (btnclick)="onEditQuestion(data.resultQuestion.id)"
                      >
                      </app-btn>
                      <app-btn
                        *ngIf="isNewQuestionBook()"
                        [text]="''"
                        [image]="'./assets/images/org/edit.png'"
                        [hoverColor]="'#409EFF'"
                        [hoverImage]="'./assets/images/org/edit_hover.png'"
                        (btnclick)="goSubCustom(data)"
                      ></app-btn>
                    </ng-container>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </nz-table>
        </nz-spin>
      </div>
    </div>
  </div>

  <div class="fixd-footer">
    <div>
      <button nz-button class="iptBtn" (click)="bookConfirm()" appDisableTime>
        <span>确认</span>
      </button>
    </div>
  </div>
</div>
