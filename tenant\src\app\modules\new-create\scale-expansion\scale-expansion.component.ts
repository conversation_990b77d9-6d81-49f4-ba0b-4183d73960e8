import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { NewCreateService } from "../new-create.service";
import { NzMessageService } from "ng-zorro-antd";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import _ from "lodash";

@Component({
  selector: "app-scale-expansion",
  templateUrl: "./scale-expansion.component.html",
  styleUrls: ["./scale-expansion.component.less"],
})
export class ScaleExpansionComponent implements OnInit {
  @Input() questionnaireId: string;
  @Input() projectId: string;
  @Output() onSave = new EventEmitter<any>();

  // loading
  isSpinning = false;
  // 上一步下一步
  step = 0;
  // 多选组数-选中项
  gaugeScaleExtendValue = [];
  // 多选组数-可选项
  gaugeScaleExtendList = [];
  // 多选组数-选中项-完整
  gaugeScaleExtendListSelected = [];
  // 每页填答题数
  questionsNumberOptions = [5, 6, 7, 8, 9, 10];
  // 选中相同选项的个数
  itemNumberOptions = [2, 3, 4, 5];
  // 选中后得分
  suitableOptions = [
    {
      label: "最高分：N",
      value: "N",
    },
    {
      label: "最低分：1",
      value: "1",
    },
  ];
  // 弹窗显示隐藏
  isVisible = false;
  dimensionList = [];
  // 查看量表/量表详情
  gaugeOptionList = [];
  // 量表题扩展设置
  gaugeScaleExtend = {
    highestScore: "N",
    highestScoreOption: "最符合",
    lowestScore: "1",
    lowestScoreOption: "最不符合",
  };
  id: null;
  detailsCache: null;
  constructor(
    private http: NewCreateService,
    private message: NzMessageService,
    private customMsg: MessageService
  ) {}
  ngOnInit() {}

  /**
   * 打开量表尺度拓展抽屉
   */
  openModal(): void {
    this.isVisible = true;
    // 获取量表尺度组数列表
    this.getGaugeScaleExtendList();
    // 根据ID获取量表题扩展设置
    this.getGaugeScaleExtend();
  }

  /**
   * 上一页 下一页
   * @param val 1下 0上
   * @returns
   */
  changeStep(val): void {
    if (val == 1) {
      // 下一页
      if (this.gaugeScaleExtendValue.length == 0) {
        // this.message.error('请选择量表尺度');
        this.customMsg.open("error", "请选择量表尺度");
        return;
      }
      for (
        let index = 0;
        index < this.gaugeScaleExtendListSelected.length;
        index++
      ) {
        const element = this.gaugeScaleExtendListSelected[index];
        if (!element.pageSize) {
          // this.message.error(`校验不通过：【${element.name}】 每页填答题数为空`);
          this.customMsg.open(
            "error",
            `校验不通过：【${element.name}】 每页填答题数为空`,
            {}
          );
          return;
        }
        if (!element.selectedCount) {
          // this.message.error(`校验不通过：【${element.name}】 每页选中相同选项的个数为空`);
          this.customMsg.open(
            "error",
            `校验不通过：【${element.name}】 每页选中相同选项的个数为空`,
            {}
          );
          return;
        }
        if (!element.pageCount) {
          // this.message.error(`校验不通过：【${element.name}】 题数为空`);
          this.customMsg.open(
            "error",
            `校验不通过：【${element.name}】 题数为空`,
            {}
          );
          return;
        }
        if (element.pageCount == "#NA") {
          // this.message.error(`校验不通过：【${element.name}】 题数不能整除`);
          this.customMsg.open(
            "error",
            `校验不通过：【${element.name}】 题数不能整除`,
            {}
          );
          return;
        }
      }
      if (!this.gaugeScaleExtend.highestScoreOption.trim()) {
        // this.message.error('请填写扩展规则第1项的选项内容');
        this.customMsg.open("error", "请填写扩展规则第1项的选项内容");
        return;
      }
      if (!this.gaugeScaleExtend.lowestScoreOption.trim()) {
        // this.message.error('请填写扩展规则第N项的选项内容');
        this.customMsg.open("error", "请填写扩展规则第N项的选项内容");
        return;
      }
      if (!this.gaugeScaleExtend.highestScore) {
        // this.message.error('请选择扩展规则第1项的选中后得分');
        this.customMsg.open("error", "请选择扩展规则第1项的选中后得分");
        return;
      }
      if (!this.gaugeScaleExtend.lowestScore) {
        // this.message.error('请选择扩展规则第N项的选中后得分');
        this.customMsg.open("error", "请选择扩展规则第N项的选中后得分");
        return;
      }
      const params = {
        ...this.gaugeScaleExtend,
        projectId: this.projectId, // 活动id
        questionnaireId: this.questionnaireId, // 问卷id
        scaleQuestionGroup: this.gaugeScaleExtendListSelected,
      };
      if (this.id) {
        params["id"] = this.id;
      }
      // if (this.detailsCache) {
      //   const obj1 = _.cloneDeep(params);
      //   const obj2 = _.cloneDeep(this.detailsCache);
      //   delete obj2.id;
      //   if (_.isEqual(obj1, obj2)){
      //     this.step = val;
      //     return
      //   }
      // }
      this.http.saveGaugeScaleExtend(params).subscribe((res) => {
        console.log(res);
        this.step = val;
        const data = res.data.map((val) => ({
          ...val,
          normMapping: val.normMapping
            ? val.normMapping
                .map((item) => `${item.count}:${item.score}`)
                .join("\n")
            : "",
        }));
        this.dimensionList = data;
        this.isSpinning = false;
      });
    } else {
      // 上一页
      this.step = val;
      this.dimensionList = [];
    }
  }

  /**
   * 确认
   * @returns
   */
  handleOk(): void {
    const data = this.dimensionList.map((val) => ({
      dimonsionName: val.dimonsionName,
      dimonsionCode: val.dimonsionCode,
      scoreRange: val.scoreRange,
      normMapping: this.textareaToModel(val.normMapping, val.dimonsionName),
    }));
    for (let index = 0; index < data.length; index++) {
      const val = data[index];
      const [min, max] = val.scoreRange.split("-");
      const sectionFilter = val.normMapping.filter(
        (ele) =>
          Number(ele.count) < Number(min) || Number(ele.count) > Number(max)
      );
      const decimalFilter = val.normMapping.filter(
        (ele) => !this.isInteger(ele.count)
      );
      if (val.normMapping.length == 0) {
        // this.message.error(`校验不通过：【${val.dimonsionName}】 常模必填`);
        this.customMsg.open(
          "error",
          `校验不通过：【${val.dimonsionName}】 常模必填`,
          {}
        );
        return;
      }
      if (sectionFilter.length > 0) {
        // this.message.error(`校验不通过：【${val.dimonsionName}】 常模溢出总分区间`);
        this.customMsg.open(
          "error",
          `校验不通过：【${val.dimonsionName}】 常模溢出总分区间`,
          {}
        );
        return;
      }
      if (decimalFilter.length > 0) {
        return;
      }
    }
    const params = {
      questionnaireId: this.questionnaireId,
      norms: data.map((val) => ({
        dimonsionName: val.dimonsionName,
        dimonsionCode: val.dimonsionCode,
        normMapping: val.normMapping,
      })),
    };
    this.http.saveGaugeScaleExtendNorm(params).subscribe((res) => {
      if (res.data) {
        this.message.success("量表尺度扩展设置成功");
        this.isVisible = false;
        this.clearData();
      }
    });
  }

  /**
   * 恢复默认
   */
  clearData() {
    this.step = 0;
    this.gaugeScaleExtend = {
      highestScore: "N",
      highestScoreOption: "最符合",
      lowestScore: "1",
      lowestScoreOption: "最符不合",
    };
    this.gaugeScaleExtendValue = [];
    this.gaugeScaleExtendListSelected = [];
    this.dimensionList = [];
    this.gaugeOptionList = [];
  }

  /**
   * 取消
   */
  handleCancel(): void {
    this.isVisible = false;
    this.clearData();
  }

  /**
   * 获取量表尺度组数列表 查看量表/量表详情
   */
  getGaugeScaleExtendList() {
    this.http.getGaugeScaleExtendList(this.questionnaireId).subscribe((res) => {
      this.gaugeScaleExtendList = res.data;
    });
  }

  /**
   *  量表内容变更
   * @param e
   */
  changeScaleExtendList(e) {
    if (e.length) {
      this.gaugeScaleExtendListSelected = this.gaugeScaleExtendList
        .filter((val) => e.includes(val.optionCount))
        .map((val) => ({
          // name // 量表组名称
          // optionCount // 量表题选项数
          // questionCount // 题目总数
          ...val,
          pageCount: null, // 总页数
          pageSize: null, // 每页填答题数
          selectedCount: null, // 选中数量
        }));
      this.getGaugeOptionList(e);
    } else {
      this.gaugeOptionList = [];
      this.gaugeScaleExtendListSelected = [];
    }
  }

  /**
   * 根据问卷ID及勾选的选项数获取问卷中量表题选项
   * @param e
   */
  getGaugeOptionList(e) {
    this.http.getGaugeOptionList(this.questionnaireId, e).subscribe((res) => {
      const lists = [];
      for (let index = 0; index < res.data.length; index++) {
        const current = res.data[index];
        const last = res.data[index - 1];
        if (index == 0) {
          lists.push([current]);
        } else {
          if (
            current.code == last.code &&
            lists[lists.length - 1].length !=
              Number(lists[lists.length - 1][0].code)
          ) {
            lists[lists.length - 1].push(current);
          } else {
            lists.push([current]);
          }
        }
      }
      this.gaugeOptionList = lists;
    });
  }

  /**
   * 改变页数
   * @param e
   * @param value
   */
  changePageSize(e, value) {
    value.pageCount =
      value.questionCount % e == 0 ? value.questionCount / e : "#NA";
  }

  /**
   * 扩展规则选项
   * @param e
   * @param type
   */
  chnageScoreOption(e, type) {
    if (type == 1) {
      if (e) {
        this.gaugeScaleExtend.lowestScore = e == "N" ? "1" : "N";
      }
    } else {
      if (e) {
        this.gaugeScaleExtend.highestScore = e == "N" ? "1" : "N";
      }
    }
  }

  /**
   * 根据问卷ID获取第二部分问卷维度集合及维度下总分区间
   * @param questionnaireId
   * @param projectId
   */
  getDimensionList(questionnaireId, projectId) {
    this.isSpinning = true;
    this.http.getDimensionList(questionnaireId, projectId).subscribe((res) => {
      const data = res.data.map((val) => ({
        ...val,
        normMapping: val.normMapping
          ? val.normMapping
              .map((item) => `${item.count}:${item.score}`)
              .join("\n")
          : "",
      }));
      this.dimensionList = data;
      this.isSpinning = false;
    });
  }

  /**
   * 常模校验
   * @param norm normMapping
   * @param name dimonsionName
   * @returns
   */
  private textareaToModel(norm, name): any[] {
    let objs: any[] = [];
    let arr1: string[] = norm.split(/[(\r\n)\r\n;]+/);
    for (let index = 0; index < arr1.length; index++) {
      const element = arr1[index];
      if (element.trim() != "") {
        if (element.indexOf(":") == -1) {
          let msg = `【${name}】常模的第${index +
            1}行数据输入有误,必须含英文分号。`;
          // this.message.error(msg);
          this.customMsg.open("error", msg);
        }
        let arr2: string[] = element.split(":");
        let para1: any = arr2[0].trim();
        let para2: any = arr2[1].trim();
        const valid1 = isNaN(para1);
        const valid2 = isNaN(para2);
        if (valid1) {
          let msg = `【${name}】常模的第${index +
            1}行数据输入有误,  ${para1}  不是数字。`;
          // this.message.error(msg);
          this.customMsg.open("error", msg);
        }
        if (valid2) {
          let msg = `【${name}】常模的第${index +
            1}行数据输入有误,  ${para2}  不是数字。`;
          // this.message.error(msg);
          this.customMsg.open("error", msg);
        }
        if (!this.isInteger(para1)) {
          let msg = `【${name}】常模的第${index +
            1}行数据输入有误,  ${para1}  必须为整数。`;
          // this.message.error(msg);
          this.customMsg.open("error", msg);
        }
        objs.push({ count: para1, score: para2 });
      }
    }
    return objs;
  }

  /**
   * 根据ID获取量表题扩展设置
   */
  getGaugeScaleExtend() {
    this.isSpinning = true;
    this.http
      .getGaugeScaleExtend(this.questionnaireId, this.projectId)
      .subscribe((res) => {
        if (res.data) {
          const {
            highestScore,
            highestScoreOption,
            id,
            lowestScore,
            lowestScoreOption,
            scaleQuestionGroup,
          } = res.data;
          this.detailsCache = _.cloneDeep(res.data);
          this.gaugeScaleExtend = {
            highestScore,
            highestScoreOption,
            lowestScore,
            lowestScoreOption,
          };
          this.id = id;
          this.gaugeScaleExtendListSelected = scaleQuestionGroup;
          this.gaugeScaleExtendValue = scaleQuestionGroup.map(
            (val) => val.optionCount
          );
          this.getGaugeOptionList(this.gaugeScaleExtendValue);
          this.isSpinning = false;
        } else {
          this.isSpinning = false;
        }
      });
  }

  /**
   * 是否为整数 正则
   * @param str
   * @returns
   */
  isInteger(str) {
    return /^\d+$/.test(str);
  }
}
