/**
 *
 *  @author: <PERSON>
 *  @Date: 2023/09/18
 *  @content: i18n tab切换组件
 *
*/
@bgColor: #40a9ff;
@fgColor: white;
@pageBg: #f5f6fa;
@textColor: #595959;
.i18nTabs {
  min-height: 35px;
  width: 100%;
  overflow-x: auto;
  height: auto;
  border-bottom: 1px solid #ececec;
  display: flex;
  > span {
    font-size: 16px;
    font-weight: 400;
    color: @textColor;
    line-height: 22px;
    //   margin: 4px 16px 0 16px;
    //   padding: 8px 0;
    margin: 0 40px 0 0;
    padding: 0 0 8px 0;
    border-bottom: 4px solid transparent;
    border-radius: 2px;
    &:hover {
      color: @bgColor;
    }
    cursor: pointer;
    white-space: nowrap;
  }
  .active {
    color: @bgColor;
    border-color: @bgColor;
  }
}
