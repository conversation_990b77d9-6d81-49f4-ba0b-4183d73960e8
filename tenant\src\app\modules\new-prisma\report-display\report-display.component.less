:root {
  --ifm-scrollbar-size: 7px;
  --ifm-scrollbar-track-background-color: #f1f1f1;
  --ifm-scrollbar-thumb-background-color: silver;
  --ifm-scrollbar-thumb-hover-background-color: #a7a7a7;
  // ::ng-deep .ant-modal-body {
  //   padding: 32px 8px 8px;
  // }
  // ::ng-deep .ant-modal-close-x {
  //   width: 32px;
  //   height: 32px;
  //   line-height: 32px;
  // }
}

.box {
  // padding: 30px;
  padding-top: 0;
  max-height: 610px;
  position: relative;
}

.content {
  width: 100%;
  // height: 340px;
  height: calc(100vh - 150px);
  border-radius: 4px;
  overflow-y: auto;

  &-head {
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;

    p {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #aaaaaa;
      line-height: 20px;
    }

    span {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #409eff;
      line-height: 1;
      padding-right: 10px;
      cursor: pointer;
    }
  }

  .tr_list {
    padding: 0 6px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #495970;
    line-height: 1;

    &.head_tr {
      height: 60px;
      background: #f3f7fb;
      border-radius: 4px;
      font-weight: 600;
    }

    td,
    th {
      text-align: center;
      padding: 16px 8px;

      &:first-child {
        text-align: left;
      }
    }
  }
}

.del_icon,
.edit_icon_close {
  width: 18px;
  height: 18px;
  background-size: 100% 100% !important;
  margin: 0 5px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  // background-color: transparent;
  background-color: #f1f1f1;
  box-shadow: none;
}

::-webkit-scrollbar-thumb {
  // background-color: #e9e9e9;
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  left: 0px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: end;
  .ant-btn-primary {
    width: 128px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 1;
    cursor: pointer;
    border: none;
  }
}
