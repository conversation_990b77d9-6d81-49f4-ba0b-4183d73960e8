.container {
  display: flex;
  justify-content: flex-start;
  align-items: stretch;
  // margin-right: 20px;

  .left {
    width: 280px;
    border-right: #e6e6e6 solid 1px;
    display: flex;
    flex-direction: column;

    .nair {
      height: 110px;
      padding: 16px;
      border-bottom: #e6e6e6 solid 1px;
      border-left: 4px solid #fff;

      div {
        margin-bottom: 8px;
      }

      .line1 {
        height: 21px;
        display: flex;
        justify-content: space-between;

        .text0 {
          height: 20px;
          font-size: 14px;
          font-weight: 600;
          color: #17314c;
          line-height: 20px;
          width: 85%;
          overflow: hidden; /* 超出部分隐藏 */
          white-space: nowrap; /* 不换行 */
          text-overflow: ellipsis; /* 使用省略号代替超出部分 */
          cursor: default;
        }
      }

      .line2 {
        height: 21px;

        .text1 {
          height: 21px;
          border-radius: 4px;
          font-weight: 500;
          padding: 2px 5px;
        }

        .text2 {
          padding: 0 5px;
          margin-left: 5px;
          font-size: 13px;
          font-weight: 500;
          color: #419eff;
          line-height: 17px;
          height: 21px;
          border-radius: 4px;
          border: 1px solid #419eff;
        }
      }

      .line3 {
        height: 21px;

        .text3 {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 500;
          color: #aaaaaa;
        }
      }
    }
  }

  .right {
    width: 720px;
    .table {
      margin-left: 10px;
      max-height: 450px;
    }
  }
}

.linesplit {
  width: 100%;
  height: 10px;
  background: -webkit-linear-gradient(top, #fff, #ddd);
}

.currentNair {
  background-color: #f4faff;
  border-left: 4px solid #409eff !important;
}

.autoTxt {
  font-weight: 500;
  color: #ffffff;
  background-color: #419eff;
}

img {
  width: 22px;
  height: 22px;
  border: 1px solid white;
  left: 0px;
  top: 0px;

  &:hover {
    width: 22px;
    height: 22px;
    border-radius: 100%;
    border-right: 2px solid white;
    border-bottom: 2px solid white;
    border-left: 2px solid white;
    border-top: 2px solid white;
    position: relative;
    left: 1px;
    top: 1px;
  }
}

.scroll {
  height: calc(100vh - 123px);
  .vxscrollbar();
}

//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

::ng-deep {
  .ant-modal-title {
    height: 33px;
    font-size: 24px;
    font-family: PingFangSC-Thin, PingFang SC;
    font-weight: 100;
    color: #17314c;
    line-height: 33px;
  }

  .ant-table-small {
    border: 0px;
  }
}
