import { HttpClient } from "@angular/common/http";
import { Component, Input, OnInit, ViewChild, OnDestroy } from "@angular/core";
import { NzDrawerRef, NzMessageService, NzModalRef } from "ng-zorro-antd";
import { UnionAssessmentComponent } from "./union-assessment/union-assessment.component";
import { UnionSurveyComponent } from "./union-survey/union-survey.component";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import _ from "lodash";
import { Observable, Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";
@Component({
  selector: "app-union-create",
  templateUrl: "./union-create.component.html",
  styleUrls: ["./union-create.component.less"],
})
export class UnionCreateComponent implements OnInit, OnDestroy {
  @Input() paramList: any[];

  isBatchCreate: boolean = false;
  data: any;

  step: number = 1;

  pageCount: number = 0;

  isLoading: boolean = false;

  taskRunning: boolean = false;
  permission;

  tenantBalance: number = 0;

  @ViewChild("app1", { static: false })
  assessmentComponent: UnionAssessmentComponent;

  @ViewChild("app2", { static: false }) surveyComponent: UnionSurveyComponent;
  isAdmin = this.permissionService.isPermission();
  lans: any[] = [];
  tenantUrl: string = "/tenant-api";

  private routerSubscription: Subscription;
  constructor(
    private http: HttpClient,
    private msgServ: NzMessageService,
    // private drawerRef: NzModalRef,
    private drawerRef: NzDrawerRef,
    private msg: NzMessageService,
    private knxFunctionPermissionService: KnxFunctionPermissionService,
    private customMsg: MessageService,
    private router: Router,
    public permissionService: PermissionService
  ) {}

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    this.getSelfInfo();
    this.getServerData();
    this.getBalacne();
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  getServerData() {
    const personUrl =
      this.tenantUrl + `/survey/person/getCreateGroupReportInfo`;

    this.isLoading = true;
    this.http.post(personUrl, this.paramList).subscribe((res: any) => {
      this.isLoading = false;
      if (res.result.code === 0) {
        this.data = res.data;
        if (this.data.assessmentInfos) {
          this.pageCount++;
        }
        if (this.data.employeeInfos) {
          // this.data.employeeInfos[0].organizationTrees = [];
          this.pageCount++;
        }

        if (!this.data.assessmentInfos) {
          this.step = 2;
        }
      }
    });
  }

  getTotal() {
    let total: number = 0;
    if (this.assessmentComponent) {
      total = total + this.assessmentComponent.getTotal();
    }
    if (this.surveyComponent) {
      total = total + this.surveyComponent.getTotal();
    }
    return total;
  }

  /**
   * 获取租户信息
   */
  getSelfInfo() {
    if (!this.isHaveLanPermission()) {
      this.lans = [{ label: "中文", value: "zh_CN", checked: true }];
    } else {
      if (
        this.knxFunctionPermissionService.has(
          "SAG:TENANT:REPORT_MGT:REPORT_LIST:CREATE_GROUP_REPORT_CN"
        )
      ) {
        this.lans.push({ label: "中文", value: "zh_CN", checked: true });
      }

      if (
        this.knxFunctionPermissionService.has(
          "SAG:TENANT:REPORT_MGT:REPORT_LIST:CREATE_GROUP_REPORT_EN"
        )
      ) {
        const infoApi = `${this.tenantUrl}/userAccount/user/getSelfInfo`;
        this.http.post(infoApi, {}).subscribe((res: any) => {
          if (res.result.code === 0) {
            const tenantId = res.data.tenantId;
            this.getIsMultiLanguage(tenantId);
          }
        });
      }
    }
  }

  /**
   * 获取调研团队报告英文报告报表启用权限
   * @param tenantId
   */
  getIsMultiLanguage(tenantId) {
    const beanUrl = `${this.tenantUrl}/survey/standard/tenant/isMultiLanguage?tenantId=${tenantId}`;
    this.http.get(beanUrl).subscribe((res: any) => {
      if (res.result.code === 0) {
        if (res.data) {
          let lans;
          if (this.lans.length === 0) {
            const eng = { label: "ENG", value: "en_US", checked: true };
            lans = [eng];
          } else {
            const eng = { label: "ENG", value: "en_US", checked: false };
            lans = [...this.lans, eng];
          }

          this.lans = lans;
        }
      }
    });
  }
  // 上一步
  pre() {
    this.step = 1;
  }

  // 下一步
  next() {
    this.step = 2;
  }

  choooseBatchCreate(e) {
    this.isBatchCreate = e;
  }

  // 提交操作
  ok() {
    let param: any = {};

    let arr1: any[] = [];
    let arr2: any[] = [];
    if (this.assessmentComponent) {
      arr1 = this.assessmentComponent.buildParam();
      param.assessmentInfos = arr1;
    }

    if (this.surveyComponent) {
      arr2 = this.surveyComponent.buildParam();
      let tmpLans: string[] = [];
      if (arr2 && arr2.length > 0) {
        tmpLans = _.map(
          _.filter(this.lans, function(l) {
            return l.checked;
          }),
          (o) => o.value
        );
        if (tmpLans.length === 0) {
          // this.msg.error("没有选择调研报告语言");
          this.customMsg.open("error", "没有选择调研报告语言");
          return;
        }
      }

      // 设置所有调研报告的语言
      for (let index = 0; index < arr2.length; index++) {
        const p = arr2[index];
        p.reportLanguages = tmpLans;
      }
      param.employeeInfos = arr2;
    }
    if (arr1.length === 0 && arr2.length === 0) {
      // this.msgServ.error("请选择需要创建报告的记录");
      this.customMsg.open("error", "请选择需要创建报告的记录");
      return;
    }
    if (
      arr1.length > 0 &&
      arr1.filter((val) => val.groupStyleType == "TALENT_MAPPING_GROUP")
        .length > 0
    ) {
      const fileName = this.assessmentComponent.fileName;
      const talentMappingReportId = this.assessmentComponent.mappingReportId;
      const sudokuData = this.assessmentComponent.sudokuData;
      if (!talentMappingReportId || sudokuData.length == 0) {
        // this.msgServ.error("请新建九宫格");
        this.customMsg.open("error", "请新建九宫格");
        return;
      }
      if (
        sudokuData.filter(
          (val) => !val.isDisplayComplete || !val.isObjectComplete
        ).length
      ) {
        // this.msgServ.error("九宫格设置未完成");
        this.customMsg.open("error", "九宫格设置未完成");
        return;
      }
      param.fileName = fileName;
      param.talentMappingReportId = talentMappingReportId;
      param.assessmentInfos = param.assessmentInfos.filter(
        (val) => !val.talentMappingNairList
      );
    }
    if (this.getTotal() > this.tenantBalance) {
      // this.msgServ.error("余额不足，请先充值！");
      this.customMsg.open("error", "余额不足，请先充值！");
      return;
    }

    // 调研 组织架构选中大于1个切选中了批量生成 报告名称必填
    let flag = false;
    if (param.employeeInfos) {
      param.employeeInfos.forEach((item) => {
        if (
          !item.isBatchCreate &&
          item.organizationIds &&
          item.organizationIds.length > 1 &&
          !item.fileName.zh_CN
        ) {
          flag = true;
        }
      });
    }
    if (flag) {
      // this.msgServ.error("请先填写报告名称！");
      this.customMsg.open("error", "请先填写报告名称！");
      return;
    }
    this.taskRunning = true;
    this.bgMakeGeneralGroupReport(param).subscribe(
      (res) => {
        this.taskRunning = false;
        if (res.result.code === 0) {
          this.msgServ.success("创建团队报告成功");

          // 返回数据有消息则提醒
          // let msgArray: string[] = res.data;
          // if (msgArray && msgArray.length > 0) {
          //   console.log(msgArray)
          //   this.msgServ.warning(msgArray[0]);
          // }

          let that = this;
          setTimeout(function() {
            // that.drawerRef.triggerCancel();
            that.drawerRef.close();
          }, 1000);
        }
      },
      (err) => {
        this.taskRunning = false;
      }
    );
  }

  getBalacne() {
    const beanUrl =
      this.tenantUrl + `/sagittarius/report/content/getTenantBalance`;
    this.http.get(beanUrl).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.tenantBalance = res.data;
      }
    });
  }

  bgMakeGeneralGroupReport(json): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/batchCreateGroupReport`;
    return this.http.post(api, json);
  }

  /**
   * 是否有语言权限
   */
  isHaveLanPermission() {
    return (
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:CREATE_GROUP_REPORT_CN"
      ) ||
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:CREATE_GROUP_REPORT_EN"
      )
    );
  }
}
