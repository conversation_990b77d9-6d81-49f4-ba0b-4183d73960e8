<div class="con">
    
    <ng-container *ngIf="page === 1, else page2">
        <div class="name">
            <div class="label"><span style="color: red;">*</span>组合名称</div>
            <div class="input">
                <input nz-input placeholder="中文" [(ngModel)]="name.zh_CN" />
                <input nz-input placeholder="English" [(ngModel)]="name.en_US" />
            </div>
        </div>
        
        <div class="title">
            <div class="label">
                <span style="color: red;">*</span>组合维度
            </div>
            <button nz-button [nzSize]="'small'" nzType="link" (click)="clearOption(false)">
                <span>清空选项</span>
            </button>
        </div>
        
        <div class="content">
    
            <ng-container *ngFor="let tab of catList">
            
                <div class="list " [ngClass]="{takeRemain: tab.id === 'question'}">
                    <div class="listSearch" style="display: flex; justify-content: space-between; align-items: center; padding: 0 10px; border-bottom: solid 1px #ccc; margin-bottom: 10px; min-height: 53px;">
                        <div style="margin-right: 10px; display: flex; align-items: center;">
                            <label nz-checkbox
                                [(ngModel)]="tab.allChecked"
                                (ngModelChange)="updateAllChecked(tab, $event)"
                                [nzIndeterminate]="tab.indeterminate">
                                {{tab.name}}全选
                            </label>
                        </div>
                        <div style="flex: 1;" >
                            <nz-input-group [nzPrefix]="suffixIconSearch1">
                                <input style="border-radius:15px;" type="text" nz-input placeholder="请输入" [(ngModel)]="tab.searchText" />
                            </nz-input-group>
                
                            <ng-template #suffixIcon>
                                <i nz-icon nzType="search"></i>
                            </ng-template>
                
                            <ng-template #suffixIconSearch1>
                                <img src="./assets/images/icon_search.png" >
                            </ng-template>
                        </div>
                    </div>
                
                    <div style="padding: 0 10px;" class="listItem treeScroll">
                        <ng-container *ngFor="let itemData of tab.items">
                            <label *ngIf="itemData.isShow && (!tab.searchText || itemData.label.indexOf(tab.searchText)!== -1 )"
                                style="margin-top: 10px;" 
                                nz-checkbox [(ngModel)]="itemData.checked" 
                                (ngModelChange)="updateSingleChecked(tab, itemData, $event)">
                                {{itemData.label}}
                            </label>
                        </ng-container>
                    </div>
                    
                </div>
            
            </ng-container>
            <!-- <ng-container *ngFor="let tab of catList">
            
                <div class="list " [ngClass]="{takeRemain: tab.id === 'question'}">
                    <div class="listSearch" style="display: flex; justify-content: space-between; align-items: center; padding: 0 10px; border-bottom: solid 1px #ccc; margin-bottom: 10px; min-height: 53px;">
                        <div style="margin-right: 10px; display: flex; align-items: center;">
                            <label nz-checkbox
                                [(ngModel)]="tab.allChecked"
                                (ngModelChange)="updateAllChecked(tab, $event)"
                                [nzIndeterminate]="tab.indeterminate">
                                {{tab.name}}全选
                            </label>
                        </div>
                        <div style="flex: 1;" >
                            <nz-input-group [nzPrefix]="suffixIconSearch1">
                                <input style="border-radius:15px;" type="text" nz-input placeholder="请输入" [(ngModel)]="tab.searchText" />
                            </nz-input-group>
                
                            <ng-template #suffixIcon>
                                <i nz-icon nzType="search"></i>
                            </ng-template>
                
                            <ng-template #suffixIconSearch1>
                                <img src="./assets/images/icon_search.png" >
                            </ng-template>
                        </div>
                    </div>
                
                    <div style="padding: 0 10px;" class="listItem treeScroll">
                        <ng-container *ngFor="let itemData of tab.items">
                            <label *ngIf="itemData.isShow && (!tab.searchText || itemData.label.indexOf(tab.searchText)!== -1 )"
                                style="margin-top: 10px;" 
                                nz-checkbox [(ngModel)]="itemData.checked" 
                                (ngModelChange)="updateSingleChecked(tab, itemData, $event)">
                                {{itemData.label}}
                            </label>
                        </ng-container>
                    </div>
                    
                </div>
            
            </ng-container> -->
        </div>
    </ng-container>
    
    <ng-template #page2>
        <div class="title">
            <div class="label">
                <span style="color: red;">*</span>组合维度
            </div>
            <button nz-button [nzSize]="'small'" nzType="link" (click)="clearOption(false)">
                <span>清空选项</span>
            </button>
        </div>
        
        <div class="content">
    
            <ng-container *ngFor="let tab of catOtherList">
            
                <div class="list " [ngClass]="{takeRemain: tab.id === 'question'}">
                
                    <div class="listSearch" style="display: flex; justify-content: space-between; align-items: center; padding: 0 10px; border-bottom: solid 1px #ccc; margin-bottom: 10px; min-height: 53px;">
                        <div style="margin-right: 10px; display: flex; align-items: center;">
                            <label nz-checkbox
                                [(ngModel)]="tab.allChecked"
                                (ngModelChange)="updateAllChecked(tab, $event)"
                                [nzIndeterminate]="tab.indeterminate">
                                {{tab.name}}全选
                            </label>
                        </div>
                        <div style="flex: 1;" >
                            <nz-input-group [nzPrefix]="suffixIconSearch1">
                                <input style="border-radius:15px;" type="text" nz-input placeholder="请输入" [(ngModel)]="tab.searchText" />
                            </nz-input-group>
                
                            <ng-template #suffixIcon>
                                <i nz-icon nzType="search"></i>
                            </ng-template>
                
                            <ng-template #suffixIconSearch1>
                                <img src="./assets/images/icon_search.png" >
                            </ng-template>
                        </div>
                    </div>
                
                    <div style="padding: 0 10px;" class="listItem treeScroll">
                        <ng-container *ngFor="let itemData of tab.items">
                            <label *ngIf="itemData.isShow && (!tab.searchText || itemData.label.indexOf(tab.searchText)!== -1 )"
                                style="margin-top: 10px;" 
                                nz-checkbox [(ngModel)]="itemData.checked" 
                                (ngModelChange)="updateSingleChecked(tab, itemData, $event)">
                                {{itemData.label}}
                            </label>
                        </ng-container>
                    </div>
                    
                </div>
            </ng-container>
        </div>
    </ng-template>

    <div class="action">
        <button class="button2" nz-button [nzSize]="'small'" nzType="primary" (click)="showGroup()">
            <span>已选组合 {{groupList.length}}</span>
        </button>
        <ng-container *ngIf="page === 2">
            <button class="button1" nz-button [nzSize]="'small'" nzType="primary" (click)="makeGroup()">
                <span>组合</span>
            </button>
        
    
            <button class="button3" nz-button [nzSize]="'small'" nzType="primary" (click)="nextPage()">
                <span>上一页</span>
            </button>
        </ng-container>
        <button *ngIf="page === 1" class="button3" nz-button [nzSize]="'small'" nzType="primary" (click)="nextPage(true)">
            <span>下一页</span>
        </button>
    </div>

    <div [hidden]="groupHidden" class="group">
        <div class="gHeader">
            <span class="gTitle">
                组合详情
            </span>
            <button nz-button [nzSize]="'small'" nzType="link" (click)="clearGroup()" >
                <span>清空所有组合</span>
            </button>
        </div>

        <div class="gContent treeScroll">
            <nz-collapse>
                <nz-collapse-panel
                  *ngFor="let group of groupList"
                  [nzHeader]="group.name.zh_CN"
                  [nzActive]="group.active"
                  [nzExtra]="extraTpl"
                >
                 <div *ngFor="let det of group.detailList">
                    {{det.desc}}
                 </div>
                 <div class="background-color" *ngFor="let det of group.analysisDetailList">
                    {{det.desc}}
                 </div>

                 <ng-template #extraTpl>
                     <div style="position: relative; top: -5px;">
                        <button nz-button nzType="default" nzShape="circle" (click)="edit($event, group)">
                            <i nz-icon nzType="edit" nzTheme="outline" ></i>
                        </button>
                        <button nz-button nzType="default" nzShape="circle" (click)="delete($event, group.id)">
                            <i nz-icon nzType="delete" nzTheme="outline"></i>
                        </button>
                     </div>
                 </ng-template>

                </nz-collapse-panel>
              </nz-collapse>

        </div>

        <div class="gAction">
            <button nz-button [nzSize]="'small'" nzType="link" (click)="showGroup()" >
                <span>收起已选组合</span>
            </button>
        </div>

    </div>


</div>

