.iconfont {
  display: flex;
  align-items: center;
  width: 16px;
  height: 16px;
  margin-left: 5px;
}

.hover-icon {
  color: #595959;
}

.hover-icon:hover {
  color: #409eff;
}

.index_xy {
  display: flex;
  justify-content: center;
  padding: 30px 0;
  background-color: #f5f6fa;
  height: 100%;
}

.big_contant {
  .create_p {
    display: flex;
    justify-content: space-between;

    .span_left {
      font-size: 24px;
    }

    .span_right {
      span {
        cursor: pointer;
      }

      > .span_blue {
        color: #53a8fe;
      }
    }
  }

  .create_name {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    font-size: 14px;
    font-weight: 400;
    color: #17314c;

    > li {
      > p {
        margin: 0px 0 30px 0;
        font-weight: bold;
      }

      .pri_name {
        width: 100%;
        height: 50px;
        border-radius: 5px;
        font-size: 16px;
      }
    }
  }

  .setmodal {
    margin-top: 30px;

    .setmodal_top {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;

      > .cur_span {
        cursor: pointer;
        color: #409eff;
      }
    }

    .setmodal_card {
      margin-top: 30px;
      width: 100%;
      min-height: 646px;
      background-color: #fff;
      border-radius: 8px;
      display: flex;

      .card_left {
        width: 146px;
        padding: 20px 0;
        min-height: 646px;
        border-right: 1px solid #e6e6e6;

        .left_div {
          width: 146px;
          height: 48px;
          line-height: 48px;
          text-align: center;
          font-size: 14px;
          font-weight: bold;
          cursor: pointer;

          > span {
            width: 100px;
            display: inline-block;
            overflow: hidden;
            /*超出部分隐藏*/
            white-space: nowrap;
            /*不换行*/
            text-overflow: ellipsis;
            /*超出部分文字以...显示*/
          }
        }

        .showClass {
          background-color: #f5faff;
          color: #409eff;
          border-left: 2px solid #409eff;
          width: 99.99%;
        }
      }

      .card_right {
        flex: 1;
        height: 100%;

        padding: 15px 30px;
      }
    }
  }
}

.submit_xy {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 70px;
  background: #fff;
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: center;

  .center_menu {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .menus_xy {
      display: flex;
      align-items: center;

      .menus_left {
        width: 98px;
        height: 38px;
        border-radius: 19px;
        border: 1px solid #409eff;
        text-align: center;
        line-height: 38px;
        cursor: pointer;
        color: #409eff;

        &:hover {
          box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.15);
        }
      }

      .menus_right {
        width: 98px;
        height: 38px;
        background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        text-align: center;
        line-height: 38px;
        cursor: pointer;
        color: #fff;
        margin-left: 20px;
        border: 0;

        &:hover {
          box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.8);
        }
      }

      .menus_right_new {
        width: 98px;
        height: 38px;
        background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
        box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
        border-radius: 19px;
        text-align: center;
        line-height: 38px;
        cursor: pointer;
        color: #fff;
        border: 0;
        margin-left: 20px;

        &:hover {
          box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.8);
        }
      }
    }
  }
}

.time_picker {
  height: 50px;
  width: 500px;
  border-radius: 5px;

  ::ng-deep input {
    font-size: 16px;
  }
}

:host ::ng-deep {
  .ant-calendar-range-picker-separator {
    line-height: 40px;
  }

  .ant-calendar-picker-input.ant-input {
    height: 50px;
    width: 500px;
    border-radius: 5px;
  }

  .ant-calendar-picker-icon {
    color: #409eff;
    font-size: 20px;
  }

  .ant-calendar-picker-clear,
  .ant-calendar-picker-icon {
    right: 14px;
    margin-top: -10px;
  }
}

.mock_div {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999999999;

  .bg_ul {
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.6;
  }

  .img_ul {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;

    > li {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .btn_div {
      width: 160px;
      line-height: 38px;
      text-align: center;
      color: #fff;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      cursor: pointer;
    }
  }
}

.label_title {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    color: #17314c;
    font-size: 14px;
    font-weight: bold;
  }

  .div_right {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .custom_add_xy {
    color: #495970;
    font-size: 12px;
    display: flex;
    align-items: center;

    &:hover {
      color: #409eff;
    }

    i {
      margin-right: 4px;
      margin-left: 0;
    }

    > span {
      cursor: pointer;
    }
  }

  .border_left_d {
    border-left: 1px solid #e1e1e1;
    margin: 0 10px;
    height: 14px;
  }
}

.top_div {
  margin-top: 8px;
  border-radius: 2px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  width: 100%;
  min-height: 48px;

  .div_left {
    display: flex;
    align-items: center;

    .li_title {
      font-size: 14px;
      color: #17314c;
      font-weight: bold;
      width: 90px;
    }

    .li_list {
      display: flex;
      align-items: center;
      margin-top: -2px;
      padding-bottom: 14px;

      .li_span {
        display: flex;
        align-items: center;
        margin-top: 16px;
        margin-left: 20px;
        margin-right: 12px;
        ::ng-deep .ant-checkbox {
          top: 1px;
        }

        // max-width: 140px;

        // span {
        //   display: inline-block;
        //   max-width: 70px;
        //   overflow: hidden;
        //   /*超出部分隐藏*/
        //   white-space: nowrap;
        //   /*不换行*/
        //   text-overflow: ellipsis;
        //   /*超出部分文字以...显示*/
        // }
      }
    }
  }
}

.nz_modal {
  position: relative;
}

.right_top {
  width: 90%;
}

.new_right {
  .big_div {
    display: flex;
    margin-top: 30px;
    position: relative;

    .all_div {
      .top_tab {
        display: flex;
        border: 1px solid #e6e6e6;
        border-radius: 8px 8px 0 0;

        .top_ul {
          display: flex;
          justify-content: space-between;
          padding: 0 20px;
          flex: 1;

          li {
            padding: 10px 0;
            flex: 1;
            cursor: pointer;
            text-align: center;
          }

          .bot_bod {
            border-bottom: 2px solid #409eff;
          }
        }

        .clear_top {
          flex: 1;
          padding: 10px 20px;
          text-align: right;
          cursor: pointer;
        }
      }

      .no_top {
        border-top: none;
      }

      .radius_bod {
        border-radius: 8px !important;
      }

      .list_ul {
        border: 1px solid #e6e6e6;
        border-radius: 0 0 8px 8px;
        display: flex;

        // min-height: 400px;
        li {
          > div {
            padding: 10px 0;
          }
        }

        .list_left {
          flex: 1;

          .left_div {
            display: flex;

            > span {
              width: 100px;
              text-align: center;
              color: #17314c;
              font-size: 14px;
              font-weight: bold;
            }

            .name_span {
              width: 100px;
              padding-left: 10px;
              display: inline-block;
              overflow: hidden;
              /*超出部分隐藏*/
              white-space: nowrap;
              /*不换行*/
              text-overflow: ellipsis;
              /*超出部分文字以...显示*/
            }

            .son_div {
              display: flex;
              padding-left: 10px;
              flex-wrap: wrap;

              .name_cursor {
                cursor: pointer;
              }

              // > div {
              //   margin: 0 10px;
              // }
            }
          }
        }
      }
    }

    .small_tips {
      margin-left: 30px;
      width: 190px;
      margin-top: -10px;

      .all_tips {
        width: 190px;
        min-height: 173px;
        padding-bottom: 20px;
        background: #f9f9f9;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        .name_mod {
          width: 100%;
          padding: 10px 10px 0 10px;
          font-size: 14px;
          color: #17314c;

          display: flex;
          justify-content: space-between;

          .diss_span {
            font-weight: bold;
          }

          .wei_span {
            cursor: pointer;
          }
        }

        .prev_pos {
          position: absolute;
          right: 10px;
          top: 10px;
          cursor: pointer;
        }

        .tips_mod {
          position: relative;
          width: 160px;
          height: 100px;
          background: #ffffff;
          border-radius: 8px;
          margin-top: 15px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .btn_d {
            padding: 0 15px;
            line-height: 30px;
            background: #ffffff;
            border-radius: 15px;
            border: 1px solid #409eff;
            color: #409eff;
            cursor: pointer;
            margin-top: 10px;
          }
        }

        .choose_name_mod {
          width: 160px;
          height: 200px;
          background: #ffffff;
          border-radius: 8px;
          margin-top: 10px;
          overflow-y: auto;

          .choose_name {
            width: 100%;
            min-height: 100px;
            // background-color: red;
            display: flex;
            flex-wrap: wrap;

            > li {
              padding: 0 8px;
              background-color: #ebf5ff;
              color: #409eff;
              height: 30px;
              border-radius: 15px;
              margin: 3px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
              line-height: 30px;
              max-width: 140px;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
        }
      }
    }
  }

  .upload_img {
    width: 129px;
    line-height: 38px;
    background-color: #ecf5ff;
    border: 1px dashed #419eff;
    text-align: center;
    border-radius: 4px;
    color: #419eff;
    cursor: pointer;
    user-select: none;
  }
}

.title_right_1 {
  margin-left: -100px;
  display: flex;

  .linelin_left {
    width: 71px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 0px 2px 2px 0px;
    border: 1px solid #eee;
    font-weight: bold;
    cursor: pointer;
  }

  .linelin_right {
    width: 71px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 0px 2px 2px 0px;
    border: 1px solid #eee;
    font-weight: bold;
    cursor: pointer;
  }

  .linear {
    color: #fff;
    background: linear-gradient(90deg, #409eff 0%, #26d0f1 100%);
    border: none;
  }
}

.Highlight {
  color: #049fff;
}

.example {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  background: #f5f6fa;
  opacity: 0.5;
}

.footer_left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 128px;
  height: 38px;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  cursor: pointer;
}

.footer_right {
  margin-left: 30px;
  cursor: pointer;
  width: 128px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background: #fafafa;
  border-radius: 19px;
}

// 题本分发/关联设置
.association {
  // 导入导出
  &_head {
    width: 100%;
    margin-bottom: 8px;
    display: flex;
    justify-content: flex-end;
    ::ng-deep {
      button {
        display: flex;
        align-items: center;
        span {
          height: 28px;
          display: flex;
          align-items: center;
          > i {
            margin-right: 4px;
          }
        }
      }
    }
  }
  // 关联设置
  &_content {
    display: flex;
    height: calc(100vh - 186px);
    // 角色&维度
    &_left {
      width: 308px;
      border: 1px solid #efefef;
      background: #ffffff;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      height: 100%;
      &_factors {
        height: calc(100% - 52px);
        margin: 2px;
        padding: 20px;
        overflow-y: auto;
        .vxscrollbar();
        p {
          font-size: 14px;
          font-weight: bold;
        }
        .factors {
          margin-top: 10px;
        }
        .mt-16 {
          margin-top: 16px;
        }
        .labelBox {
          display: flex;
          flex-wrap: wrap;
          label {
            width: 120px;
            margin-left: 0;
            display: flex;
            align-items: center;

            span {
              width: 80px;
              display: inline-block;
              overflow: hidden;
              /*超出部分隐藏*/
              white-space: nowrap;
              /*不换行*/
              text-overflow: ellipsis;
              /*超出部分文字以...显示*/
            }
          }
        }
      }
      // 操作
      &_operate {
        width: 100%;
        border-top: 1px solid #efefef;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 16px;
        ::ng-deep {
          .ant-btn-link {
            padding: 0;
          }
        }
        // span {
        //   width: 72px;
        //   line-height: 30px;
        //   border-radius: 6px;
        //   border: 1px solid #409eff;
        //   color: #409eff;
        //   text-align: center;
        // }
      }
    }
    // 角色维度关联
    &_right {
      width: calc(100% - 328px);
      flex: 1;
      height: 100%;
      margin-left: 16px;
      // display: flex;
      // flex-direction: column;
      // justify-content: flex-start;
      overflow-y: auto;
      &_result {
        // flex: 1;
        // height: 23.5%;
        height: calc((100vh - 234px) / 4);
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #efefef;
        padding: 10px 16px;
        // 角色
        .role {
          display: flex;
          justify-content: space-between;
          span {
            font-size: 14px;
            font-weight: bold;
          }
          ::ng-deep {
            .ant-btn-link {
              padding: 0;
            }
          }
        }
        // 维度
        .items {
          overflow-y: auto;
          max-height: calc(100% - 30px);
          margin-top: 8px;
          display: flex;
          // align-items: center;
          flex-wrap: wrap;
          .vxscrollbar();
          ::ng-deep .ant-tag {
            margin: 4px 4px 4px 0;
            height: 26px;
            padding: 0 8px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #495970;
            border: none;
            background: #f6f6f6;
            border-radius: 2px;
            display: flex;
            align-items: center;
            &:hover {
              background-color: rgba(64, 158, 255, 0.05);
              .anticon {
                color: #62afff;
              }
            }
            .anticon {
              margin-left: 6px;
            }
          }
        }
      }
    }
  }
}

.radioul {
  display: flex;
  flex-wrap: wrap;
}

.modalTitle {
  display: flex;

  .titleName {
    font-size: 16px;
    font-weight: 600;
    color: #495970;
  }
}

.footer {
  width: 144px;
  text-align: center;
  line-height: 45px;
  color: #fff;
  font-size: 16px;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  border-radius: 35px;
  cursor: pointer;
}

.Pretest_dc {
  font-weight: bold;
  font-size: 14px;
  color: #495970;
}

.Pretest_li {
  margin-top: 20px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .Pretest_se {
    width: 270px;
  }

  .Pretest_div {
    margin-left: 20px;
    color: #409eff;
    cursor: pointer;
  }
}

.ca_tip {
  display: flex;
  justify-content: space-between;
  height: 45px;
  border-left: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
  border-top: 1px solid #e6e6e6;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  padding: 0 0 0 20px;

  .ca_disp {
    display: flex;
    align-items: center;
    height: 100%;

    .disp_t {
      color: #ffba3c;
      width: 100px;
      display: flex;
      align-items: center;

      .rantan {
        width: 11px;
        height: 11px;
        background-color: #ffba3c;
        margin-right: 5px;
      }
    }

    .disp_r {
      margin-left: 10px;
      color: #17314c;
      font-weight: 500;
      font-size: 14px;
    }

    .custom_disp {
      color: #409eff;
      font-size: 12px;
      cursor: pointer;
    }

    .line_disp {
      height: 12px;
      border-left: 1px solid #e1e1e1;
      margin: 0 12px;
    }

    .clear_disp {
      font-size: 12px;
      cursor: pointer;
    }
  }

  .disp_8 {
    flex: 8;
    background: linear-gradient(to right, #ffffff 98%, #f4f4f4);
  }

  .disp_2 {
    justify-content: center;
    margin-left: 12px;
    flex: 2;
  }
}

.select_code {
  margin-bottom: 20px;
  display: flex;
  width: 100%;
  height: 87px;
  padding: 10px 20px;
  background-color: #f9f9f9;

  .code_ds {
    width: 42px;
  }

  .codelist {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    margin-top: -5px;

    .dimension {
      padding: 0 8px;
      line-height: 26px;
      height: 26px;
      color: #aaaaaa;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid rgba(140, 140, 140, 0.3);
      margin: 5px 0 0 5px;
    }
  }
}

::ng-deep {
  .round-right-drawer-new {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 108px);
      // height: calc(100% - 106px);
      overflow-y: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer-new-nofooter {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow-y: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
.drawer-footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-track {
    background-color: #F1F1F1;
    box-shadow: none;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

// 人口标签-loading
.factorSpinning {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #eee;
  opacity: 0.4;
  z-index: 999999;
}
//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}
