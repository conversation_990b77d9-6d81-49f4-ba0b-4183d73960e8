.modal-title {
  height: 81px;
  display: flex;
  justify-content: space-between;
  padding: 0 30px;

  .span1 {
    padding-top: 32px;
    font-size: 24px;
    font-family: PingFangSC-Light, PingFang SC;
    font-weight: 300;
    color: #17314C;
    line-height: 33px;
  }

  .span2 {
    padding-top: 46px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #495970;
    line-height: 20px;
  }

}

.tree-node {
  background-color: #bae7ff;
  padding: 0 5px;
  border-radius: 2px;
}

.modal-border {
  display: flex;
  justify-content: center;
  height: 1px;

  >div {
    width: 100%;
    margin: 0 30px;
    border-bottom: 1px solid #E6E6E6;
  }
}

.modal-little-border {
  display: flex;
  justify-content: center;
  height: 1px;

  >div {
    width: 100%;
    border-bottom: 1px solid #E6E6E6;
  }
}

.survey-book-modal-content {
  display: flex;

  .left-content {
    width: 203px;
    height: 473px;
    border-right: 2px solid #E6E6E6;
    padding-left: 28px;
    overflow-y: auto;

    .select {
      margin: 13px 0 20px 30px;
    }
  }

  .right-content {
    flex: 1;
    height: 473px;
    padding: 0 29px 0 19px;

    .right-content-header {
      padding: 0 14px;
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .searchDiv {
        margin-right: 10px;
      }

      .icon-search {
        color: #409EFF;
      }

      .input-search {
        border-radius: 20px;
        width: 140px;
      }
    }

    .questions-box-open {
      height: 412px;
      width: 100%;
      // overflow-y: auto;
      // overflow-x: hidden;
      display: flex;
      padding: 0;

      >div:first-child {
        min-width: 45%;
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        height: 412px;

        >div {
          display: flex;
          min-width: 50%;
          padding: 14px 0 14px 14px;
          border-bottom: 1px solid #E6E6E6;
          font-size: 16px;
          cursor: pointer;

          p {
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #17314C;
            line-height: 22px;
            // text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5);
          }
        }

        .questions_hover {
          background: #F5FAFF;
        }
      }

      >div:last-child {
        // 开放题选项
        width: 50%;
        overflow-y: auto;
        overflow-x: hidden;
        height: 412px;

        .open-que-title {
          height: 46px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #E6E6E6;

          span {
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #17314C;
          }
        }

        .questionsList-options {
          li {
            display: flex;
            padding: 10px;
            border-bottom: 1px solid #E6E6E6;
          }
        }
      }
    }

    .questions-box {
      height: 412px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0;

      li {
        display: flex;
        width: 100%;
        padding: 14px 0 14px 14px;
        border-bottom: 1px solid #E6E6E6;
        font-size: 16px;

        p {
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #17314C;
          line-height: 22px;
          // text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5);
        }
      }

      li:hover {
        background-color: rgba(64, 158, 255, .05);
      }
    }
  }
}



.modal-btn-footer {
  display: flex;
  align-items: center;
  height: 85px;
  padding-left: 34px;

  button {
    color: white;
    width: 128px;
    height: 38px;
    background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    cursor: pointer;
  }
}

.title {
  font-size: 24px;
  font-weight: bold;
}

.title-child {
  overflow-x: hidden;
  overflow-y: auto;
  height: calc(473px - 69px);

  li {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 167px;
    height: 48px;
    padding: 0 10px;
    cursor: pointer;
  }

  li.active {
    background-color: rgba(064, 158, 255, .05);
    color: #409EFF;
  }
}

nz-select {
  margin-right: 8px;
  width: 124px;
}

.flex {
  display: flex;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background-color: #F1F1F1;
  box-shadow: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: #C1C1C1;
  outline: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}