import { Component, Input, OnInit } from "@angular/core";

@Component({
  selector: "app-name-desc-edit",
  templateUrl: "./name-desc-edit.component.html",
  styleUrls: ["./name-desc-edit.component.less"],
})
export class NameDescEditComponent implements OnInit {
  @Input() name: any;

  @Input() replaceName: any;

  lans: any[] = [];

  isVisible: boolean = true;

  constructor() {}

  ngOnInit() {
    this.lans = [
      { lan: "zh_CN", name: "中文", origin: "", after: "" },
      { lan: "en_US", name: "ENG", origin: "", after: "" },
    ];

    for (let index = 0; index < this.lans.length; index++) {
      const element = this.lans[index];
      element.origin = this.replaceName[element.lan];
      element.after = this.replaceName[element.lan];
    }
  }
}
