<ng-container *ngIf="!locwelcomepage && !locengpage">
  <div class="advanced">
    <nz-tabset
      [nzTabBarStyle]="{ padding: 0 }"
      [nzSelectedIndex]="tableIndex"
      (nzSelectedIndexChange)="changeSelectedIndex($event)"
    >
      <nz-tab nzTitle="活动设置">
        <!-- 活动名称 -->
        <div class="mb-24">
          <p class="title mb-16">活动名称</p>
          <div style="width: 99%;">
            <app-i18n-input
              [value]="settingData.name"
              (changeValue)="changeNameValue($event)"
            ></app-i18n-input>
          </div>
        </div>
        <!-- 提示说明 -->
        <div class="mb-24">
          <p class="title mb-16">提示说明</p>
          <div nz-row [nzGutter]="16" class="mb-16">
            <div nz-col [nzSpan]="12">
              <nz-switch
                [(ngModel)]="settingData.isCheckLicenseNotice"
                class="mr-8"
              ></nz-switch>
              <span class="text mr-16">许可声明</span>
              <a routerLink="/permission-statement" target="_blank">预览</a>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-switch
                [(ngModel)]="settingData.isEnableWelcomePage"
                class="mr-8"
              ></nz-switch>
              <span class="text mr-16">欢迎页</span>
              <a
                *ngIf="settingData.isEnableWelcomePage"
                (click)="editorwelcome()"
                >编辑</a
              >
            </div>
          </div>
          <div nz-row [nzGutter]="16" class="mb-24">
            <div nz-col [nzSpan]="12">
              <nz-switch
                [(ngModel)]="settingData.isEnableEndPage"
                class="mr-8"
              ></nz-switch>
              <span class="text mr-16">结束页</span>
              <a *ngIf="settingData.isEnableEndPage" (click)="editorend()"
                >编辑</a
              >
            </div>
          </div>
        </div>
        <!-- 企业LOGO -->
        <div class="mb-24">
          <p class="title mb-16">企业LOGO</p>
          <div class="mb-16">
            <label nz-checkbox [(ngModel)]="settingData.isShowKnxLogo"
              >显示测评调研云logo</label
            >
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-upload
                class="avatar-uploader"
                nzListType="picture-card"
                nzName="avatar"
                [nzShowUploadList]="false"
                [nzBeforeUpload]="beforeUpload1"
                [nzCustomRequest]="customReq"
                (nzChange)="handleChange($event)"
              >
                <img *ngIf="avatarUrl" [attr.src]="avatarUrl" class="avatar" />

                <ng-container>
                  <div class="loading">
                    <img src="./assets/images/upload.png" *ngIf="!avatarUrl" />
                    <div *ngIf="!avatarUrl" class="ant-upload-text">上传</div>
                  </div>
                </ng-container>
              </nz-upload>
              <span class="suggest"
                >建议：800px*800px以上的1:1尺寸，300K以内，PNG格式</span
              >
            </div>
          </div>
        </div>
        <!-- 填答背景 -->
        <div class="mb-24">
          <p class="title mb-16">填答背景</p>
          <div class="mb-16">
            <label nz-checkbox [(ngModel)]="settingData.isShowBackgroundPic"
              >显示填答背景</label
            >
          </div>
          <div nz-row [nzGutter]="40">
            <div nz-col [nzSpan]="10">
              <nz-upload
                class="avatar-uploader"
                nzListType="picture-card"
                nzName="avatar"
                [nzShowUploadList]="false"
                [nzBeforeUpload]="beforeUpload"
                [nzCustomRequest]="customReqPc"
                (nzChange)="handleChangePic($event, 'PC')"
              >
                <img
                  *ngIf="PcavatarUrl"
                  [attr.src]="PcavatarUrl"
                  class="avatar"
                />
                <ng-container>
                  <div class="loading">
                    <img
                      src="./assets/images/upload.png"
                      *ngIf="!PcavatarUrl"
                    />
                    <div *ngIf="!PcavatarUrl" class="ant-upload-text">
                      上传(PC端)
                    </div>
                  </div>
                </ng-container>
              </nz-upload>
              <span class="suggest">建议:1920px*1080px，500K以内，PNG格式</span>
            </div>
            <div nz-col [nzSpan]="10">
              <nz-upload
                class="avatar-uploader"
                nzListType="picture-card"
                nzName="avatar"
                [nzShowUploadList]="false"
                [nzBeforeUpload]="beforeUpload"
                [nzCustomRequest]="customReqMb"
                (nzChange)="handleChangePic($event, 'MB')"
              >
                <img
                  *ngIf="MbavatarUrl"
                  [attr.src]="MbavatarUrl"
                  class="avatar"
                />

                <ng-container>
                  <div class="loading">
                    <img
                      src="./assets/images/upload.png"
                      *ngIf="!MbavatarUrl"
                    />
                    <div *ngIf="!MbavatarUrl" class="ant-upload-text">
                      上传(移动端)
                    </div>
                  </div>
                </ng-container>
              </nz-upload>
              <span class="suggest">建议:160px*78px，500K以内，PNG格式</span>
            </div>
          </div>
        </div>
        <!-- 报告查看权限 -->
        <div
          *ngIf="
            permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PROJECT_SETTING'
            )
          "
        >
          <p class="title mb-16">
            报告查看权限 <span class="suggest">(允许测评者作答完查看报告)</span>
          </p>
          <div nz-row [nzGutter]="8" class="mb-16">
            <div nz-col [nzSpan]="6">
              <span>选择查看方式</span>
            </div>
            <div nz-col [nzSpan]="18">
              <label nz-checkbox [(ngModel)]="settingData.isPublicReport"
                >填答设备</label
              >
              <!-- <label
                nz-checkbox
                [(ngModel)]="settingData.isEmailReport"
                [nzDisabled]="routProjectType!=='OVER' && routProjectType!=='SUSPEND'"
                >邮件</label
              >
              <a *ngIf="settingData.isEmailReport" (click)="sendReport()"
                >发送</a
              > -->
            </div>
          </div>
        </div>
      </nz-tab>
      <nz-tab nzTitle="发布设置">
        <!-- 题数 -->
        <div class="mb-24">
          <p class="title mb-16">问卷</p>
          <div class="mb-16" *ngIf="isNewQuestionBook(); else elseTemplate">
            <nz-radio-group
              [(ngModel)]="settingData.isQuestionCustomSort"
              (ngModelChange)="changeQuestionCustomSort($event)"
            >
              <label nz-radio [nzValue]="false">每页题数</label>
              <nz-input-number
                class="mr-24"
                [(ngModel)]="settingData.questionNumInOnePage"
                [nzPrecision]="precision"
                nzPlaceHolder="题数"
                [nzDisabled]="settingData.isQuestionCustomSort"
              >
              </nz-input-number>
              <label nz-radio [nzValue]="true">自定义</label>
            </nz-radio-group>
          </div>
          <ng-template #elseTemplate>
            <div class="mb-16">
              <span class="mr-8">每页题数</span>
              <nz-input-number
                [nzDisabled]="disabledPagesAndSort"
                class="input_num"
                [(ngModel)]="settingData.questionNumInOnePage"
                [nzPrecision]="precision"
                nzPlaceHolder="题数"
              >
              </nz-input-number>
            </div>
          </ng-template>

          <ng-container *ngIf="isNewQuestionBook360()">
            <div class="mb-16">
              <label
                nz-checkbox
                [nzValue]="true"
                [(ngModel)]="settingData.isAutoPage"
              >
                一页一题时，自动翻页
              </label>
            </div>
          </ng-container>
          <ng-container
            *ngIf="isNewQuestionBook() && settingData.isQuestionCustomSort"
          >
            <div class="mb-16">
              <label
                nz-checkbox
                [nzValue]="true"
                [(ngModel)]="settingData.isAutoFillQuestionBook"
              >
                题本分发致页面题目有缺时，自动补位后续题本
              </label>
            </div>
          </ng-container>
        </div>
        <!-- 排序 -->
        <div class="mb-24">
          <p class="title mb-16">排序</p>
          <div class="mb-16">
            <nz-radio-group
              [nzDisabled]="
                disabledPagesAndSort || settingData.isQuestionCustomSort
              "
              [(ngModel)]="settingData.sequence"
              (ngModelChange)="funsequence($event)"
            >
              <label nz-radio nzValue="QUESTION_TYPE" class="mr-24"
                >按题目类型排序</label
              >
              <label nz-radio nzValue="RANDOM">
                打乱排序
              </label>
            </nz-radio-group>
          </div>
        </div>
        <!-- 普通测评 -->
        <ng-container *ngIf="projecttype != '360'">
          <!-- 语言 -->
          <div class="mb-24">
            <p class="title-between mb-16">
              <span>语言</span>
            </p>
            <div nz-row [nzGutter]="8" class="mb-16">
              <div nz-col [nzSpan]="6">
                <span>填答时可选语言</span>
              </div>
              <div nz-col [nzSpan]="18">
                <nz-checkbox-wrapper (nzOnChange)="optionalLan($event)">
                  <label
                    *ngFor="let item of projectLangOptions; let i = index"
                    nz-checkbox
                    [nzValue]="item.value"
                    [(ngModel)]="item.checked"
                  >
                    {{ item.name }}
                  </label>
                </nz-checkbox-wrapper>
              </div>
            </div>
            <div nz-row [nzGutter]="8" class="mb-16">
              <div nz-col [nzSpan]="6">
                <span>默认填答语言</span>
              </div>
              <div nz-col [nzSpan]="18">
                <nz-radio-group
                  [(ngModel)]="settingData.language"
                  (ngModelChange)="funsequence($event)"
                >
                  <label
                    *ngFor="let item of projectLangOptions; let i = index"
                    nz-radio
                    [nzValue]="item.value"
                    >{{ item.name }}</label
                  >
                </nz-radio-group>
              </div>
            </div>
          </div>
          <ng-container
            *ngIf="
              reportType == 'BLANK_CUSTOM' ||
              reportTypeList.includes('BLANK_CUSTOM')
            "
          >
            <!-- 自定义问卷-关联任务 -->
            <div
              class="mb-24"
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                ) ||
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:SCALE_EXTENSION'
                )
              "
            >
              <p class="title mb-16">关联任务</p>
              <div nz-row [nzGutter]="16" class="mb-16">
                <div
                  nz-col
                  [nzSpan]="12"
                  *ngIf="
                    permissionService.isPermissionOrSag(
                      'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                    )
                  "
                >
                  <nz-switch
                    [(ngModel)]="isEnableQuestionBookDistribute"
                    (ngModelChange)="
                      changeEnable($event, 'isEnableQuestionBookDistribute')
                    "
                  ></nz-switch>
                  <span class="text ml-8">题本分发任务</span>
                </div>
                <div
                  nz-col
                  [nzSpan]="12"
                  *ngIf="
                    permissionService.isPermissionOrSag(
                      'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:SCALE_EXTENSION'
                    )
                  "
                >
                  <nz-switch
                    [(ngModel)]="isEnableScaleExtend"
                    (ngModelChange)="
                      changeEnable($event, 'isEnableScaleExtend')
                    "
                  ></nz-switch>
                  <span class="text ml-8">量表尺度拓展</span>
                </div>
              </div>
            </div>
          </ng-container>
        </ng-container>
        <!-- 360测评 -->
        <ng-container *ngIf="projecttype == '360'">
          <!-- 计算 -->
          <div
            class="mb-24"
            *ngIf="
              permissionService.isPermissionOrSag(
                'SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PUBLISH_SETTING'
              )
            "
          >
            <p class="title-between mb-16">
              <span>计算</span>
            </p>
            <div nz-row [nzGutter]="8">
              <div nz-col [nzSpan]="7">
                <span>被评估人(评分对象)</span>
              </div>
              <div nz-col [nzSpan]="17">
                <nz-radio-group
                  [(ngModel)]="settingData.isCustomRoleWeight"
                  (ngModelChange)="funsequence($event)"
                >
                  <label nz-radio [nzValue]="false">角色权重一致</label>
                  <label nz-radio [nzValue]="true">自定义分配角色权重</label>
                </nz-radio-group>
              </div>
            </div>
            <div style="color: #f5222d;" class="mb-16 mt-6">
              * ( 切换该选项后，角色权重将恢复默认值，请谨慎选择！)
            </div>
          </div>
          <!-- 语言 -->

          <div class="mb-24">
            <p class="title-between mb-16">
              <span>语言</span>
              <a *ngIf="permission && isShowLan()" (click)="showanSetting()"
                ><i nz-icon nzType="setting" nzTheme="outline" class="mr-8"></i
                >语言设置</a
              >
            </p>
            <div
              nz-row
              [nzGutter]="8"
              class="mb-16"
              *ngIf="permission && isShowLan()"
            >
              <div nz-col [nzSpan]="6">
                <div class="mt-6">请选择新增语言</div>
              </div>
              <div nz-col [nzSpan]="18">
                <nz-select
                  style="width: 100%"
                  nzMode="multiple"
                  nzPlaceHolder="请选择"
                  [(ngModel)]="addCodes"
                  [nzDropdownRender]="render"
                  [(nzOpen)]="allLansOpen"
                  nzAllowClear
                  nzPlaceHolder="请选择"
                  (ngModelChange)="changeProjectLan($event)"
                >
                  <!-- <nz-option *ngFor="let option of lansFilter_cnen" [nzLabel]="option.name" [nzValue]="option.value">
                </nz-option> -->
                  <nz-option
                    *ngFor="let option of lansFilter_cnen"
                    [nzLabel]="option.name"
                    [nzValue]="option.value"
                  ></nz-option>
                </nz-select>
                <!-- <ng-template #render>
                  <ng-container *ngIf="lansFilter_cnen.length > 0">
                    <div
                      style="font-size: 12px;color: #FF4F40;line-height: 17px;padding: 8px 12px"
                    >
                      *最多可设置 3 种语言
                    </div> -->
                          <!-- <nz-divider style="margin: 2px 0;"></nz-divider>
                        <div style="display: flex;justify-content: flex-end;padding: 8px 12px">
                          <button nz-button nzType="primary" (click)="showanSetting()">设置</button>
                        </div> -->
                  <!-- </ng-container>
                </ng-template> -->
              </div>
            </div>
            <div nz-row [nzGutter]="8" class="mb-16">
              <div nz-col [nzSpan]="6">
                <span>填答时可选语言</span>
              </div>
              <div nz-col [nzSpan]="18">
                <nz-checkbox-wrapper (nzOnChange)="optionalLan($event)">
                  <label
                    *ngFor="let item of projectLangOptions; let i = index"
                    nz-checkbox
                    [nzValue]="item.value"
                    [(ngModel)]="item.checked"
                  >
                    {{ item.name }}
                  </label>
                </nz-checkbox-wrapper>
              </div>
            </div>
            <div nz-row [nzGutter]="8" class="mb-16">
              <div nz-col [nzSpan]="6">
                <span>默认填答语言</span>
              </div>
              <div nz-col [nzSpan]="18">
                <nz-radio-group
                  [(ngModel)]="settingData.language"
                  (ngModelChange)="funsequence($event)"
                >
                  <label
                    *ngFor="let item of projectLangOptions; let i = index"
                    nz-radio
                    [nzValue]="item.value"
                    >{{ item.name }}</label
                  >
                </nz-radio-group>
              </div>
            </div>
          </div>
          <!-- <div class="mb-24">
            <p class="title-between mb-16">
              <span>语言</span>
            </p>
            <div nz-row [nzGutter]="8" class="mb-16">
              <div nz-col [nzSpan]="6">
                <span>填答时可选语言</span>
              </div>
              <div nz-col [nzSpan]="18">
                <nz-checkbox-wrapper (nzOnChange)="optionalLan($event)">
                  <label
                    *ngFor="let item of projectLangOptions; let i = index"
                    nz-checkbox
                    [nzValue]="item.value"
                    [(ngModel)]="item.checked"
                  >
                    {{ item.name }}
                  </label>
                </nz-checkbox-wrapper>
              </div>
            </div>
            <div nz-row [nzGutter]="8" class="mb-16">
              <div nz-col [nzSpan]="6">
                <span>默认填答语言</span>
              </div>
              <div nz-col [nzSpan]="18">
                <nz-radio-group
                  [(ngModel)]="settingData.language"
                  (ngModelChange)="funsequence($event)"
                >
                  <label
                    *ngFor="let item of projectLangOptions; let i = index"
                    nz-radio
                    [nzValue]="item.value"
                    >{{ item.name }}</label
                  >
                </nz-radio-group>
              </div>
            </div>
          </div> -->

          <!-- 评价关系 -->
          <div
            class="mb-24"
            *ngIf="
              permissionService.isPermissionOrSag(
                'SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PUBLISH_SETTING'
              )
            "
          >
            <p class="title mb-16">评价关系</p>
            <div class="mb-16">
              <nz-switch
                [(ngModel)]="isInviteAnswer"
                (ngModelChange)="changeIsVisitAnswer($event)"
                nzCheckedChildren="启用"
                nzUnCheckedChildren="禁用"
              ></nz-switch>
              <span class="text ml-8">被评估人(评分对象)自行邀请</span>
            </div>
            <div class="mb-16">
              <nz-switch
                [nzDisabled]="!isInviteAnswer"
                [(ngModel)]="isInviteReviewEvaluatee"
                (ngModelChange)="changeIsInviteReviewEvaluatee($event)"
                nzCheckedChildren="启用"
                nzUnCheckedChildren="禁用"
              ></nz-switch>
              <span class="text ml-8">审核被评估人(评分对象)邀请</span>
            </div>
            <div class="mb-16">
              <nz-switch
                 [nzDisabled]="isInviteAnswer"
                [(ngModel)]="settingData.isQuickMutualEvaluation"
                nzCheckedChildren="启用"
                nzUnCheckedChildren="禁用"
              ></nz-switch>
              <span class="text ml-8">快速互评</span>
            </div>
          </div>
          <!-- 有效性 -->
          <div
            class="mb-24"
            *ngIf="
              permissionService.isPermissionOrSag(
                'SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PUBLISH_SETTING'
              )
            "
          >
            <p class="title mb-16">有效性</p>
            <div class="mb-16">
              <div class="mb-16">
                <span class="mr-8">填答有效时间 ≥</span>
                <nz-input-number
                  [(ngModel)]="mm"
                  [nzMin]="0"
                  [nzPrecision]="precision_min"
                  nzPrecisionMode="cut"
                  (nzBlur)="getdefaultTime()"
                  (ngModelChange)="mChange($event)"
                  nzPlaceHolder="分"
                ></nz-input-number>
                <span class="mr-8 ml-8">分</span>
                <nz-input-number
                  [(ngModel)]="ss"
                  [nzMin]="0"
                  [nzPrecision]="precision_min"
                  nzPrecisionMode="cut"
                  (nzBlur)="getdefaultTime()"
                  (ngModelChange)="sChange($event)"
                  nzPlaceHolder="秒"
                ></nz-input-number>
                <span class="ml-8">秒</span>
                <i
                  nz-icon
                  nzType="question-circle"
                  nzTheme="fill"
                  class="tip-iocn ml-8"
                  nz-tooltip
                  [nzTitle]="validTime"
                  [nzOverlayStyle]="{ 'max-width': '340px' }"
                ></i>
                <ng-template #validTime>
                  <p style="margin: 4px 0;font-weight: 600;">
                    有效时间说明
                  </p>
                  <p style="margin: 4px 0;">
                    有效时间计算精确到秒，毫秒直接舍弃。
                  </p>
                  <p style="margin: 4px 0;">
                    举例：
                  </p>
                  <div style="display: flex;">
                    <span style="margin-right: 4px;">●</span>
                    <p>
                      设置有效时间≥2分0秒，即2分钟以内为无效数据，用户填答1分59秒85毫秒记为无效；
                    </p>
                  </div>
                  <div style="display: flex;margin-bottom:4px;">
                    <span style="margin-right: 4px;">●</span>
                    <p>
                      设置有效时间≥2分1秒，即2分钟及以内为无效数据，用户填答2分0秒49毫秒记为无效。
                    </p>
                  </div>
                </ng-template>
              </div>
              <div class="mb-16">
                <span class="mr-8">填答完成率 ≥</span>
                <nz-input-number
                  [(ngModel)]="settingData.answerEffectiveRange"
                  [nzMin]="1"
                  [nzMax]="100"
                  [nzStep]="1"
                  [nzPrecision]="precision"
                  nzPrecisionMode="cut"
                  nzPlaceHolder="百分比"
                ></nz-input-number>
                <span class="ml-8">%</span>
                <i
                  nz-icon
                  nzType="question-circle"
                  nzTheme="fill"
                  class="tip-iocn ml-8"
                  nz-tooltip
                  [nzTitle]="effectiveRange"
                  [nzOverlayStyle]="{ 'max-width': '340px' }"
                ></i>
                <ng-template #effectiveRange>
                  <p style="margin: 4px 0;font-weight: 600;">
                    完成率说明
                  </p>
                  <p style="margin: 4px 0;">
                    每个填答人对每个被评价者的题目的作答比例。
                  </p>
                  <div style="display: flex;">
                    <span style="margin-right: 4px;">●</span>
                    <p>
                      含所有题型，含必填、选填，不含穿透的结果题；
                    </p>
                  </div>
                  <div style="display: flex;margin-bottom:4px;">
                    <span style="margin-right: 4px;">●</span>
                    <p>
                      不同填答人的计算分母因题本分发可能存在差异。
                    </p>
                  </div>
                </ng-template>
              </div>
              <!-- 填答一致性 -->
              <div class="mb-16">
                <span class="mr-8">填答一致性 ≤</span>
                <nz-input-number
                  [(ngModel)]="settingData.answerSameRate"
                  [nzMin]="1"
                  [nzMax]="100"
                  [nzStep]="1"
                  [nzPrecision]="precision"
                  nzPrecisionMode="cut"
                  nzPlaceHolder="百分比"
                ></nz-input-number>
                <span class="mr-8 ml-8">%</span>
                <i
                  nz-icon
                  nzType="question-circle"
                  nzTheme="fill"
                  class="tip-iocn"
                  nz-tooltip
                  [nzTitle]="consistency"
                  [nzOverlayStyle]="{ 'max-width': '370px' }"
                ></i>
                <ng-template #consistency>
                  <p style="margin:4px 0;font-weight: 600;">
                    一致性规则说明
                  </p>
                  <div style="display: flex;">
                    <span style="margin-right: 4px;">●</span>
                    <p>
                      量表题/单选题：填答过程中，填答人在所有题目的选项中选择了相同选项的比例；
                    </p>
                  </div>
                  <div style="display: flex;margin-bottom:4px;">
                    <span style="margin-right: 4px;">●</span>
                    <p>
                      滑块题/多级比重题：填答过程中，填答人在所有题目中评估得分在同一分数区间的比例，分数区间划分可自定义设置（例如：30%，50%，70%，90%，100%）
                    </p>
                  </div>
                </ng-template>
                <button
                  *ngIf="is360Or270()"
                  nz-button
                  nzType="link"
                  nz-popover
                  nzTitle="自定义"
                  nzPlacement="topRight"
                  [(nzVisible)]="answerSameRateVisible"
                  [nzContent]="answerSameRateTemplate"
                  nzTrigger="click"
                  [disabled]="
                    settingData.answerRolePersonNum.rolePersons.length === 0
                  "
                  class="link-btn"
                >
                  <!-- [disabled]="isDisabledCustomValidData()" -->
                  <span nz-icon nzType="plus-circle" nzTheme="outline"></span>
                  自定义
                </button>
                <ng-template #answerSameRateTemplate>
                  <div class="answer-same-rate">
                    <section>
                      <div class="title" style="justify-content: flex-start;">
                        <span>量表/单选题角色</span>
                      </div>
                      <div class="list1">
                        <nz-descriptions
                          [nzColumn]="2"
                          nzSize="small"
                          nzBordered
                        >
                          <ng-container
                            *ngFor="
                              let item of settingData.answerSameRateCustom
                                .scaleCustoms;
                              let itemIdx = index
                            "
                          >
                            <nz-descriptions-item>
                              <span class="mr-8">{{
                                item.roleName.zh_CN + "一致性占比≤"
                              }}</span>
                              <nz-input-number
                                [(ngModel)]="item.rate"
                                [nzMin]="0"
                                [nzMax]="100"
                                [nzStep]="1"
                                [nzPrecision]="precision"
                                nzPlaceHolder="百分比"
                                style="width: 70px;"
                              ></nz-input-number>
                              <span
                                [class]="
                                  itemIdx % 2 === 0 ? 'mr-24 ml-8' : 'ml-8'
                                "
                                >%</span
                              >
                            </nz-descriptions-item>
                          </ng-container>
                        </nz-descriptions>
                      </div>
                      <div class="title" style="justify-content: flex-start;">
                        <span>滑块/多级比重题角色</span>
                      </div>
                      <div class="mb-16 list2">
                        <ng-container
                          *ngFor="
                            let item of settingData.answerSameRateCustom
                              .proportionCustoms
                          "
                        >
                          <div class="list2-item">
                            <span class="mr-8"
                              >{{ item.roleName.zh_CN }}评分/总分，以</span
                            >
                            <nz-input-number
                              [(ngModel)]="item.score1"
                              [nzMin]="0"
                              [nzMax]="100"
                              [nzStep]="1"
                              [nzPrecision]="precision"
                              nzPlaceHolder="百分比"
                              style="width: 70px;"
                            ></nz-input-number>
                            <nz-input-number
                              [(ngModel)]="item.score2"
                              [nzMin]="0"
                              [nzMax]="100"
                              [nzStep]="1"
                              [nzPrecision]="precision"
                              nzPlaceHolder="百分比"
                              style="width: 70px;"
                              class="ml-8"
                            ></nz-input-number>
                            <nz-input-number
                              [(ngModel)]="item.score3"
                              [nzMin]="0"
                              [nzMax]="100"
                              [nzStep]="1"
                              [nzPrecision]="precision"
                              nzPlaceHolder="百分比"
                              style="width: 70px;"
                              class="ml-8"
                            ></nz-input-number>
                            <nz-input-number
                              [(ngModel)]="item.score4"
                              [nzMin]="0"
                              [nzMax]="100"
                              [nzStep]="1"
                              [nzPrecision]="precision"
                              nzPlaceHolder="百分比"
                              style="width: 70px;"
                              class="ml-8"
                            ></nz-input-number>
                            <span class="ml-8 mr-8">% 为区间一致性占比≤</span>
                            <nz-input-number
                              [(ngModel)]="item.rate"
                              [nzMin]="0"
                              [nzMax]="100"
                              [nzStep]="1"
                              [nzPrecision]="precision"
                              nzPlaceHolder="百分比"
                              style="width: 70px;"
                            ></nz-input-number>
                            <span class="ml-8">%</span>
                          </div>
                        </ng-container>
                      </div>
                      <div class="title">
                        <span>自定义有效填数据</span>
                        <div class="title-extra">
                          <nz-upload
                            class="image-wrapper_7"
                            [nzCustomRequest]="import360ValidData"
                            nzAccept=".xlsx,.xls"
                            [nzFilter]=""
                            [nzShowUploadList]="false"
                          >
                            <button
                              nz-button
                              nzType="link"
                              [disabled]="
                                projectStatus !== 'OVER' &&
                                projectStatus !== 'SUSPEND'
                              "
                            >
                              <i class="iconfont icon-import mr-8"></i>导入
                            </button>
                          </nz-upload>
                          <nz-divider
                            nzType="vertical"
                            class="my-16"
                          ></nz-divider>
                          <!-- <a (click)="export360ValidData()" [disabled]="projectStatus !=='OVER'">
                            <i class="iconfont icon-export_ic"></i>
                            <span>导出</span>
                          </a> -->
                          <button
                            nz-button
                            nzType="link"
                            (click)="export360ValidData()"
                            [disabled]="
                              projectStatus !== 'OVER' &&
                              projectStatus !== 'SUSPEND'
                            "
                          >
                            <i class="iconfont icon-export_ic mr-8"></i>
                            导出
                          </button>
                        </div>
                      </div>
                      <p class="desc">
                        请在活动结束后，点击&nbsp;“导出”&nbsp;获取脱敏填答数据，在表格中修改字段&nbsp;“有效性”后&nbsp;，点击&nbsp;“导入”
                      </p>
                    </section>
                    <footer>
                      <button nz-button (click)="onClickAnswerSameRateClear()">
                        清空
                      </button>
                      <button
                        nz-button
                        nzType="primary"
                        (click)="onClickAnswerSameRateConfirm()"
                      >
                        确定
                      </button>
                    </footer>
                  </div>
                </ng-template>
              </div>
             
            <!-- 不满足填答有效性不可提交 -->
             <div class="mb-16">
               <nz-switch
                class="mr-8"
                [(ngModel)]="settingData.isCheckedAnswerValid"
                nzCheckedChildren="开启"
                nzUnCheckedChildren="关闭"
              ></nz-switch>
              <span >不满足填答有效性不可提交</span>
              </div>
              <!-- 任意角色的有效填答人数 -->
              <div class="mb-16">
                <span class="mr-8">任意角色的有效填答人数 ≥</span>
                <nz-input-number
                  [(ngModel)]="settingData.answerRolePersonNum.validNum"
                  [nzMin]="1"
                  [nzMax]="100"
                  [nzStep]="1"
                  [nzPrecision]="precision"
                  nzPrecisionMode="cut"
                  nzPlaceHolder="人"
                ></nz-input-number>
                <span class="ml-8">人</span>
                <button
                  nz-button
                  nzType="link"
                  nz-popover
                  nzPlacement="topRight"
                  nzTitle="角色"
                  [(nzVisible)]="answerRolePersonNumVisible"
                  [nzContent]="answerRolePersonNumTemplate"
                  nzTrigger="click"
                  [disabled]="
                    settingData.answerRolePersonNum.rolePersons.length === 0
                  "
                  class="link-btn"
                >
                  <span nz-icon nzType="plus-circle" nzTheme="outline"></span>
                  自定义
                </button>
                <!-- 角色的有效填答人数 自定义-->
                <ng-template #answerRolePersonNumTemplate>
                  <div class="answer-role-person-num">
                    <section>
                      <!-- <p class="title">角色</p> -->
                      <ng-container
                        *ngFor="
                          let item of settingData.answerRolePersonNum
                            .rolePersons
                        "
                      >
                        <div class="mb-16 list">
                          <span class="mr-8"
                            >{{ item.roleName.zh_CN }}有效填答人数 ≥</span
                          >
                          <nz-input-number
                            [(ngModel)]="item.validNum"
                            [nzMin]="0"
                            [nzStep]="1"
                            [nzPrecision]="precision"
                            nzPlaceHolder="人"
                          ></nz-input-number>
                          <span class="ml-8">人</span>
                        </div>
                      </ng-container>
                    </section>
                    <footer>
                      <button
                        nz-button
                        nzType="primary"
                        (click)="onClickAnswerRolePersonNumConfirm()"
                      >
                        确定
                      </button>
                    </footer>
                  </div>
                </ng-template>
              </div>
             

            </div>
          </div>
          <!-- 分发管理 -->
          <div
            *ngIf="
              permissionService.isPermissionOrSag(
                'SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PUBLISH_SETTING'
              ) && permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                )
            "
          >
            <p class="title-between mb-16">
              <span>分发管理</span>
            </p>

            <div
              class="mb-16"
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                )
              "
            >
              <nz-switch
                [nzDisabled]="isUpdateing"
                [(ngModel)]="settingData.isEnableRoleDimension"
                nzCheckedChildren="启用"
                nzUnCheckedChildren="禁用"
              ></nz-switch>
              <span class="text ml-8">题本分发任务</span>
            </div>
          </div>
        </ng-container>
      </nz-tab>
    </nz-tabset>
  </div>
  <div class="footer" [hidden]="locwelcomepage || locengpage">
    <button nz-button nzType="default" (click)="getDefault()">恢复默认</button>
    <button nz-button nzType="primary" (click)="getSaveSet()">保存设置</button>
  </div>
</ng-container>
<!-- 开始页/结束页 -->
<ng-container *ngIf="locwelcomepage || locengpage">
  <div class="advanced">
    <p class="title mb-16">{{ pagename }}</p>
    <ng-container *ngIf="locengpage">
      <app-i18n-select
        [active]="loceng_locwelcome_lan"
        (selectChange)="onSelectI18n($event)"
      ></app-i18n-select>
      <div style="width: 99%; margin-top: 16px;">
        <tinymce
          [config]="tinyconfig"
          [(ngModel)]="settingData.endPage[loceng_locwelcome_lan]"
          delay="10"
        ></tinymce>
      </div>
    </ng-container>
    <ng-container *ngIf="locwelcomepage">
      <app-i18n-select
        [active]="loceng_locwelcome_lan"
        (selectChange)="onSelectI18n($event)"
      ></app-i18n-select>
      <div style="width: 99%; margin-top: 16px;">
        <tinymce
          [config]="tinyconfig"
          [(ngModel)]="settingData.welcomePage[loceng_locwelcome_lan]"
          delay="10"
        ></tinymce>
      </div>
    </ng-container>
    <div class="footer">
      <button nz-button nzType="default" (click)="wlcomeDefault()">
        返回上一步
      </button>
      <button nz-button nzType="primary" (click)="wlcomeSaveSet()">确认</button>
    </div>
  </div>
</ng-container>
<!-- 语言设置 -->
<app-i18n-setting
  [visible]="lanVisible"
  [selectOptions]="allLans"
  [projectId]="projectId"
  (onClose)="onLanSettingClose()"
  (onSave)="onLanSettingSave($event)"
></app-i18n-setting>
