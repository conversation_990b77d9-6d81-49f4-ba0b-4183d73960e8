import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { TipComponent } from './tip/tip.component';
import { AmaComponent } from './ama/ama.component';
import { AtComponent } from './at/at.component';
import { EpaComponent } from './epa/epa.component';
import { CsiComponent } from './csi/csi.component';
import { CaComponent } from './ca/ca.component';
import { HrComponent } from './hr/hr.component';
import { ArtistComponent } from './artist/artist.component';
import { ProgrammerComponent } from './programmer/programmer.component';
import { SalesComponent } from './sales/sales.component';
import { PmComponent } from './pm/pm.component';
import { PurchaseComponent } from './purchase/purchase.component';
import { MarketComponent } from './market/market.component';
import { FinanceComponent } from './finance/finance.component';
import { PwvoComponent } from './pwvo/pwvo.component';
import { PcaComponent } from './pca/pca.component';
import { PtaComponent } from './pta/pta.component';
import { S360Component } from './s360/s360.component';
import { TouristComponent } from './tourist.component';
import { CsComponent } from './cs/cs.component';
import { RiskControlComponent } from './risk-control/risk-control.component';
import { LegalComponent } from './legal/legal.component';
import { McaComponent } from './mca/mca.component';
import { PrismaComponent } from './prisma/prisma.component';

import { CultureComponent } from './vnew_culture/culture.component'
import { RenComponent } from './vnew_ren/culture.component';
import { SheComponent } from './vnew_she/culture.component';
import { XiaoComponent } from './vnew_xiao/culture.component';

import { TipnewComponent } from './v_Tip_new/tipnew.component'
import { CanewComponent } from './v_Ca_new/canew.component'

import { S360Componentcultrue } from './s360p/s360p.component'
import { S270Component } from './s270/s270.component'

import { PdpComponent } from './pdp/pdp.component'
import { SjtpComponent } from './sjtp/sjtp.component'
import { VideoComponent } from './video/video.component'

import { MokaSuccessComponent } from './moka-success/oauthmoka-success.component'
import { MokaFailComponent } from './moka-fail/oauthmoka-fail.component'


import { PrismaCultureComponent } from './prismaCulture/prismaCulture.component'
import { MhsComponent } from "./mhs/mhs.component";
import { DoublePerspectiveComponent } from './doublePerspective/doublePerspective.component';
import { OrgSurveyComponent } from './orgSurvey/orgSurvey.component';

const routes: Routes = [
  {
    path: "",
    component: null,
    children: [
      { path: "home", component: TouristComponent },
      { path: "tip", component: TipComponent },
      { path: "ama", component: AmaComponent },
      { path: "at", component: AtComponent },
      { path: "epa", component: EpaComponent },
      { path: "csi", component: CsiComponent },
      { path: "ca", component: CaComponent },
      { path: "hr", component: HrComponent },
      { path: "pwvo", component: PwvoComponent },
      { path: "artist", component: ArtistComponent },
      { path: "programmer", component: ProgrammerComponent },
      { path: "sales", component: SalesComponent },
      { path: "pm", component: PmComponent },
      { path: "purchase", component: PurchaseComponent },
      { path: "market", component: MarketComponent },
      { path: "finance", component: FinanceComponent },
      { path: "pca", component: PcaComponent },
      { path: "pta", component: PtaComponent },
      { path: "cs", component: CsComponent },
      { path: "risk-control", component: RiskControlComponent },
      { path: "legal", component: LegalComponent },
      { path: "s360", component: S360Component },
      { path: "mca", component: McaComponent },
      { path: "prisma", component: PrismaComponent },
      { path: "culture", component: CultureComponent },
      { path: "rencai", component: RenComponent },
      { path: "shezhao", component: SheComponent },
      { path: "xiaozhao", component: XiaoComponent },
      { path: "tipnew", component: TipnewComponent },
      { path: "canew", component: CanewComponent },
      { path: "train", component: S360Componentcultrue }, //培养
      { path: "s270", component: S270Component },
      { path: "pdp", component: PdpComponent },
      { path: "sjtp", component: SjtpComponent },
      { path: "video", component: VideoComponent },
      { path: "oauth-moka-success", component: MokaSuccessComponent },
      { path: "oauth-moka-fail", component: MokaFailComponent },

      { path: "prismaCulture", component: PrismaCultureComponent },
      { path: "mhs", component: MhsComponent },
      { path: "doublePerspective", component: DoublePerspectiveComponent },
      { path: "orgSurvey", component: OrgSurveyComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TouristRoutingModule { }
