import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DragulaService } from 'ng2-dragula';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-column-set',
  templateUrl: './column-set.component.html',
  styleUrls: ['./column-set.component.less']
})
export class ColumnSetComponent implements OnInit {

  @Input() columnList: any[];

  @Output() dragged = new EventEmitter<any>();

  subs = new Subscription();

  constructor(private dragulaService: DragulaService,) { }

  ngOnInit() {
    this.subs.add(this.dragulaService.drop("VAMPIRESCOLUMN").subscribe(
      ({ name, el, target, source, sibling }) => {
       
        this.selectChange();
      }
      )
    ); 
  }

  selectChange() {
    this.dragged.emit(this.columnList);
  }
 
}
