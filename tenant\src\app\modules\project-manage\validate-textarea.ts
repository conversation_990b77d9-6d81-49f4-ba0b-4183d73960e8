import { AbstractControl } from "@angular/forms";
import { isEmail } from './validate-util';

//control是我们要验证的表单控件, 
export function areaEmail(control: AbstractControl) {
    let result = true;
    let msg = null;


    if(control.value) {
      let arr: string[] = control.value.split(/[(\r\n)\r\n]+/);
      for (let index = 0; index < arr.length; index++) {
        const emailStr = arr[index];
        if (!isEmail(emailStr)) {
          result = false;
          msg  = emailStr;
          break;
        }
      }
    }
    
    return result ? null : {'areaEmail': {value: msg}}; 
}