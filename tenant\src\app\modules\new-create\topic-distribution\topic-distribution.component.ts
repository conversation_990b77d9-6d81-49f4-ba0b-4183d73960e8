import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { NewCreateService } from "../new-create.service";
import { NzMessageService, UploadXHRArgs, zh_CN } from "ng-zorro-antd";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-topic-distribution",
  templateUrl: "./topic-distribution.component.html",
  styleUrls: ["./topic-distribution.component.less"],
})
export class TopicDistributionComponent implements OnInit {
  @Input() questionnaireId: string;
  @Input() projectId: string;
  @Input() isdisabled: boolean;

  // @Output() onSave = new EventEmitter<any>();

  // @Output() checkItem = new EventEmitter<any>();

  // 国际化
  lan = "zh_CN";
  isVisible = false;
  demographicIdsList = [];
  dimensionCodesList = [];
  mappings = [];
  oldMappings = [];

  // 题本分发导出loading
  isDispenseDownLoadSpinning: boolean = false;
  constructor(
    private http: NewCreateService,
    private message: NzMessageService,
    private customMsg: MessageService
  ) {}
  ngOnInit() {}

  /**
   * 初始化数据
   */
  init() {
    /**
     * 获取二级维度
     */
    this.http
      .getTwoLevelDimensions({ questionnaireId: this.questionnaireId })
      .subscribe((res) => {
        this.dimensionCodesList = res.data.map((item) => {
          item.checked = false;
          item.name = item.name;
          return item;
        });
      });

    /**
     * 获取人口标签
     */
    this.http.getListByProjectId(this.projectId).subscribe((res) => {
      res.data.forEach((item) => {
        if (item.allChildren && item.allChildren.length > 0) {
          item.selectedValue = [];
          this.demographicIdsList.push(item);
        }
      });
    });
    // 获取分发人口信息学分发维度
    this.getDimensionsMappings();
  }

  /**
   * 获取分发人口信息学分发维度
   */

  getDimensionsMappings() {
    this.http
      .getListSelectedDimensionsMappings({
        questionnaireId: this.questionnaireId,
      })
      .subscribe((res) => {
        if (res.data) {
          this.mappings = res.data.map((item) => ({
            demographics: item.demographics,
            dimensions: item.dimensions
          }))
        }
        this.oldMappings = JSON.parse(JSON.stringify(this.mappings));
      });
  }
  /**
   * 打开侧边栏
   */
  openModal(): void {
    this.isVisible = true;
    this.init();
  }

  /**
   * 删除单个数据
   * @param i index
   */
  empty(i) {
    this.mappings.splice(i, 1);
  }

  /**
   * 删除单个tag
   * @param i 关联index
   * @param j 项index
   * @param type 1人口标签项 2二级维度项
   */
  onCloseCard(i, j, type) {
    let spliceList = [
      {
        type: 1,
        delete: () => {
          if (this.mappings[i].demographics.length <= 1) {
            this.empty(i);
            return;
          }
          this.mappings[i].demographics.splice(j, 1);
        },
      },
      {
        type: 2,
        delete: () => {
          if (this.mappings[i].dimensions.length <= 1) {
            this.empty(i);
            return;
          }
          this.mappings[i].dimensions.splice(j, 1);
        },
      },
    ];
    spliceList.forEach((item) => {
      if (item.type == type) {
        item.delete();
      }
    });
  }

  /**
   * 关联数据
   * @returns
   */
  association() {
    let demographics = [];
    this.demographicIdsList.forEach((item) => {
      let data = null;
      if (item.selectedValue && item.selectedValue.length > 0) {
        data = JSON.parse(JSON.stringify(item));
        data.selectedValue.forEach((element) => {
          element.name.zh_CN = item.name.zh_CN + "-" + element.name.zh_CN;
        });
        demographics = demographics.concat(data.selectedValue);
      }
    });
    if (demographics.length <= 0) {
      // this.message.warning("请选择人口标签!");
      this.customMsg.open("warning", "请选择人口标签");
      return;
    }
    let dimensions = [];
    this.dimensionCodesList.forEach((item) => {
      if (item.checked) {
        dimensions.push(item);
      }
    });
    if (dimensions.length <= 0) {
      // this.message.warning("请选择二级维度!");
      this.customMsg.open("warning", "请选择二级维度");
      return;
    }
    this.mappings.push({
      demographics,
      dimensions,
    });
    this.clearSelected();
  }

  /**
   * 清空已选
   */
  clearSelected() {
    this.demographicIdsList.forEach((item) => {
      item.selectedValue = [];
    });
    this.dimensionCodesList.forEach((item) => {
      item.checked = false;
    });
  }

  /**
   * 侧边栏-确定/题本分发保存
   */
  primary() {
    let mappings = [];
    this.mappings.forEach((item) => {
      let demographicIds = [];
      item.demographics.forEach((element) => {
        demographicIds.push(element.id);
      });

      let dimensionCodes = [];
      item.dimensions.forEach((element) => {
        dimensionCodes.push(element.code);
      });
      mappings.push({ demographicIds, dimensionCodes });
    });
    this.http
      .saveSelectedDimensionsMapping({
        questionnaireId: this.questionnaireId,
        mappings,
      })
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.message.success("题本分发保存成功");
          this.handleCancel();
        }
      });
  }

  /**
   * 恢复默认
   */
  default() {
    this.mappings = JSON.parse(JSON.stringify(this.oldMappings));
    this.clearSelected();
  }

  /**
   * 侧边栏-关闭
   */
  handleCancel(): void {
    this.demographicIdsList = [];
    this.dimensionCodesList = [];
    this.mappings = [];
    this.isVisible = false;
  }

  /**
   * 题本分发-导入
   * @param item
   */
  onDispenseImport = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.onDispenseUploadExcel(formData, item);
  };
  /**
   * 题本分发-导入-上传配置
   */
  onDispenseUploadExcel(formData, item) {
    return this.http.importSelectQuestion(formData, this.projectId).subscribe(
      (res) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.message.success("题本分发导入成功");
          this.getDimensionsMappings();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  /**
   * 题本分发-导出
   */
  onDispenseDownLoad() {
    this.isDispenseDownLoadSpinning = true;
    this.http.exportSelectQuestion(this.projectId).subscribe((res) => {
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.isDispenseDownLoadSpinning = false;
    });
  }
}
