<!--
    *@author: <PERSON><PERSON>
    *@Date: 2023/09/11
    *@content: 相关分析页面
-->
<div class="box">
  <header>
    <div>
      <button class="btn3" nz-button nzType="primary" (click)="showGroup()">
        已选关联 ( {{ groupList.length }} )
      </button>
      <ng-container *ngIf="page !== 2; else disabledBtn">
        <button
          [disabled]="true"
          nz-button
          nzType="default"
          (click)="makeGroup()"
        >
          关联
        </button>
      </ng-container>
      <ng-template #disabledBtn>
        <button class="btn4" nz-button nzType="default" (click)="makeGroup()">
          关联
        </button>
      </ng-template>
    </div>
  </header>
  <div class="con">
    <ng-container *ngIf="page === 1; else page2">
      <div class="name">
        <div class="label" *ngIf="coefficientAlgorithm ==='REGRESSION'"><span style="color: red;">*</span>回归系数名称</div>
        <div class="label" *ngIf="coefficientAlgorithm !=='REGRESSION'"><span style="color: red;">*</span>相关系数名称</div>
        <div class="input" nz-row [nzGutter]="16">
          <div nz-col [nzSpan]="12">
            <input nz-input placeholder="中文" [(ngModel)]="name.zh_CN" />
          </div>
          <div nz-col [nzSpan]="12">
            <input nz-input placeholder="English" [(ngModel)]="name.en_US" />
          </div>
        </div>
      </div>

      <div class="title">
        <div class="label"><span style="color: red;">*</span>组合题本</div>
        <button
          nz-button
          [nzSize]="'small'"
          nzType="link"
          (click)="clearOption(false)"
        >
          <span>清空选项</span>
        </button>
      </div>

      <div class="content">
        <ng-container *ngFor="let tab of catList">
          <div class="list " [ngClass]="{ takeRemain: tab.id === 'question' }">
            <div
              class="listSearch"
              style="display: flex; justify-content: space-between; align-items: center; padding: 0 10px; border-bottom: solid 1px #ccc; margin-bottom: 10px; min-height: 53px;"
            >
              <div
                style="margin-right: 10px; display: flex; align-items: center;"
              >
                <label
                  nz-checkbox
                  [(ngModel)]="tab.allChecked"
                  (ngModelChange)="updateAllChecked(tab, $event)"
                  [nzIndeterminate]="tab.indeterminate"
                >
                  {{ tab.name }}全选
                </label>
              </div>
              <div style="flex: 1;">
                <nz-input-group [nzPrefix]="suffixIconSearch1">
                  <input
                    style="border-radius:15px;"
                    type="text"
                    nz-input
                    placeholder="请输入"
                    [(ngModel)]="tab.searchText"
                  />
                </nz-input-group>

                <ng-template #suffixIcon>
                  <i nz-icon nzType="search"></i>
                </ng-template>

                <ng-template #suffixIconSearch1>
                  <img src="./assets/images/icon_search.png" />
                </ng-template>
              </div>
            </div>

            <div style="padding: 0 10px;" class="listItem treeScroll">
              <ng-container *ngFor="let itemData of tab.items">
                <label
                  *ngIf="
                    itemData.isShow &&
                    (!tab.searchText ||
                      itemData.label.indexOf(tab.searchText) !== -1)
                  "
                  style="margin-top: 10px;"
                  nz-checkbox
                  [(ngModel)]="itemData.checked"
                  (ngModelChange)="updateSingleChecked(tab, itemData, $event)"
                >
                  {{ itemData.label }}
                </label>
              </ng-container>
            </div>
          </div>
        </ng-container>
      </div>
    </ng-container>

    <ng-template #page2>
      <div class="title">
        <div class="label"><span style="color: red;">*</span>组合维度</div>
        <button
          nz-button
          [nzSize]="'small'"
          nzType="link"
          (click)="clearOption(false)"
        >
          <span>清空选项</span>
        </button>
      </div>

      <div class="content">
        <ng-container *ngFor="let tab of catOtherList">
          <div class="list" [ngClass]="{ takeRemain: tab.id === 'question' }">
            <div
              class="listSearch"
              style="display: flex; justify-content: space-between; align-items: center; padding: 0 10px; border-bottom: solid 1px #ccc; margin-bottom: 10px; min-height: 53px;"
            >
              <div
                style="margin-right: 10px; display: flex; align-items: center;"
              >
                <label
                  nz-checkbox
                  [(ngModel)]="tab.allChecked"
                  (ngModelChange)="updateAllChecked(tab, $event)"
                  [nzIndeterminate]="tab.indeterminate"
                >
                  {{ tab.name }}全选
                </label>
              </div>
              <div style="flex: 1;">
                <nz-input-group [nzPrefix]="suffixIconSearch1">
                  <input
                    style="border-radius:15px;"
                    type="text"
                    nz-input
                    placeholder="请输入"
                    [(ngModel)]="tab.searchText"
                  />
                </nz-input-group>

                <ng-template #suffixIcon>
                  <i nz-icon nzType="search"></i>
                </ng-template>

                <ng-template #suffixIconSearch1>
                  <img src="./assets/images/icon_search.png" />
                </ng-template>
              </div>
            </div>

            <div style="padding: 0 10px;" class="listItem treeScroll">
              <ng-container *ngFor="let itemData of tab.items">
                <label
                  *ngIf="
                    itemData.isShow &&
                    (!tab.searchText ||
                      itemData.label.indexOf(tab.searchText) !== -1)
                  "
                  style="margin-top: 10px;"
                  nz-checkbox
                  [(ngModel)]="itemData.checked"
                  (ngModelChange)="updateSingleChecked(tab, itemData, $event)"
                >
                  {{ itemData.label }}
                </label>
              </ng-container>
            </div>
          </div>
        </ng-container>
      </div>
    </ng-template>

    <!-- <div class="action">
        <button class="button2" nz-button [nzSize]="'small'" nzType="primary" (click)="showGroup()">
            <span>已选组合 {{groupList.length}}</span>
        </button>
        <ng-container *ngIf="page === 2">
            <button class="button1" nz-button [nzSize]="'small'" nzType="primary" (click)="makeGroup()">
                <span>组合</span>
            </button>
        
    
            <button class="button3" nz-button [nzSize]="'small'" nzType="primary" (click)="nextPage()">
                <span>上一页</span>
            </button>
        </ng-container>
        <button *ngIf="page === 1" class="button3" nz-button [nzSize]="'small'" nzType="primary" (click)="nextPage(true)">
            <span>下一页</span>
        </button>
    </div> -->

    <div [hidden]="groupHidden" class="group">
      <div class="gHeader">
        <span class="gTitle">
          关联详情 <a (click)="bgGetGroupList()">更新数据</a>
        </span>
        <button
          nz-button
          [nzSize]="'small'"
          nzType="link"
          (click)="clearGroup()"
        >
          <span>清空所有关联</span>
        </button>
      </div>

      <div class="gContent treeScroll">
        <nz-collapse>
          <nz-collapse-panel
            *ngFor="let group of groupList"
            [nzHeader]="group.name.zh_CN"
            [nzActive]="group.active"
            [nzExtra]="extraTpl"
          >
            <div *ngFor="let det of group.detailList">
              {{ det.desc }}
            </div>
            <div
              class="background-color"
              *ngFor="let det of group.analysisDetailList"
            >
              {{ det.desc }}
            </div>

            <ng-template #extraTpl>
              <div style="display: flex;">
                <div
                  (click)="edit($event, group)"
                  class="edit_icon_close"
                ></div>
                <div (click)="delete($event, group.id)" class="del_icon"></div>
              </div>
            </ng-template>
          </nz-collapse-panel>
        </nz-collapse>
      </div>

      <div class="gAction">
        <button
          nz-button
          [nzSize]="'small'"
          nzType="link"
          (click)="showGroup()"
        >
          <span>收起已选关联</span>
        </button>
      </div>
    </div>

    <!-- <footer>
      <button class="btn_cancel" nz-button nzType="default" (click)="closeModal()">关闭</button>
      <button *ngIf="page === 2" class="btn_confirm" nz-button nzType="default" (click)="nextPage()">上一页</button>
      <button *ngIf="page === 1" class="btn_confirm" nz-button nzType="default" (click)="nextPage(true)">
        <span>下一页</span>
      </button>
    </footer> -->
  </div>
</div>
<div class="footer">
  <button nz-button nzType="default" (click)="closeModal()">关闭</button>
  <button nz-button nzType="primary" *ngIf="page === 2" (click)="nextPage()">
    上一页
  </button>
  <button
    nz-button
    nzType="primary"
    *ngIf="page === 1"
    (click)="nextPage(true)"
  >
    下一页
  </button>
</div>
