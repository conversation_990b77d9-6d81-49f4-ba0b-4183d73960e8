.warp-bg {
  background: #F6F6F6;
  height: 100%;
}

:host ::ng-deep .ant-select-selection--single,
.ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
  border: 0 !important;
  background-color: #F0F3F8 !important;
}

:host ::ng-deep .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}

.warp {
  min-width: 375px;
  width: 100%;
  height: 281px;
  margin: 0 auto;
  color: #17314C;
  background: url('../../../../assets/images/mobile_login_bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 15% 0 0 0;

  .logo {
    display: block;
    margin: 0 auto 32px;
  }

  .title {
    font-size: 30px;
    display: inline-block;
    text-align: center;
  }

  .title-sub {
    font-size: 20px;
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 5px 0;
    margin-bottom: 22px;

    .add-btn {
      font-size: 14px;
      font-weight: 400;
      color: #409EFF;
    }
  }

  .title:after,
  .title-sub:after {
    content: '';
    display: block;
    width: 79px;
    height: 11px;
    background: linear-gradient(90deg, #71AFF6 0%, #3977FC 100%);
    opacity: 0.1637;
    margin-bottom: 27px;
  }

  .title-sub:after {
    position: absolute;
    top: 20px;
    left: 5px;
  }

  .ant-form-item {
    margin-bottom: 15px;
  }

  .ant-input,
  .ant-select,
  .ant-select-selection--single {
    height: 40px !important;
  }

  .ant-select-selection--single {
    position: relative;
    height: 40px;
    cursor: pointer;
  }

  .tip {
    font-size: 12px;
    color: #B1BCC7;
    line-height: 17px;

  }


  .reg-warp {
    width: 85%;
    margin: 0 auto;
    background: #FFFFFF;
    border-radius: 10px;
    margin-top: 10%;
    padding: 24px;

    .ant-input {
      border: 0;
      background-color: #F0F3F8;
    }

    .select div {
      border: 0 !important;
    }
  }

  .reg-warp2 {
    width: 100%;
    margin: 0 auto;
    background: #F6F6F6;
    padding: 7.4%;

    .ant-input {
      border: 0;
    }
  }

  .submit-btn {
    width: 100%;
    margin: 0 auto;
    height: 40px;
    background: linear-gradient(90deg, #71AFF6 0%, #3977FC 100%);
    box-shadow: 0px 6px 6px 0px rgba(73, 135, 251, 0.28);
    border-radius: 4px;
  }

}