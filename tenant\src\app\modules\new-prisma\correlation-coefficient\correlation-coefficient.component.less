.close-box {
  text-align: right;
  padding: 9px;

  .icon-penetra-close {
    display: inline-block;
    width: 12px;
    height: 12px;
    font-size: 12px;
    cursor: pointer;
  }
}

.box {
  // padding: 30px;
  padding-top: 0;
  max-height: 610px;
}

header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  .title-name {
    font-size: 24px;
    font-family: PingFangSC-Light, PingFang SC;
    font-weight: 300;
    color: #495970;
    line-height: 33px;
  }

  // .btn1 {
  //   width: 72px;
  //   height: 30px;
  //   border-radius: 15px;
  //   border: 1px solid #409eff;
  //   font-size: 14px;
  //   font-family: PingFangSC-Medium, PingFang SC;
  //   font-weight: 500;
  //   color: #409eff;
  //   line-height: 20px;
  // }

  // .btn2 {
  //   width: 115px;
  //   height: 30px;
  //   background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
  //   box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
  //   border-radius: 15px;
  //   font-size: 16px;
  //   font-family: PingFangSC-Medium, PingFang SC;
  //   font-weight: 500;
  //   color: #FFFFFF;
  //   line-height: 22px;
  //   border: none;
  // }
  .btn4 {
    color: #409eff;
    border-color: #409eff;
    border-radius: 6px;
  }
  .btn3 {
    color: #409eff;
    border-radius: 6px;
    background-color: #f5f8ff;
    border-color: #f5f8ff;
    box-shadow: none;
    text-shadow: none;
  }
}
.con {
  position: relative;

  .name {
    height: 70px;

    .input {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
    }
  }

  .title {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .label {
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #17314c;
    line-height: 20px;
  }

  .content {
    width: 100%;
    // height: 316px;
    height: calc(100vh - 300px);
    display: flex;
    justify-content: flex-start;

    border-radius: 4px;
    border: 1px solid #e6e6e6;

    .list {
      width: 255px;
      // min-height: 400px;
      border-right: 1px solid #e6e6e6;

      .listItem {
        // max-height: 246px;
        height: calc(100vh - 370px);
      }
    }
  }

  .action {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    border-bottom: 1px solid #e6e6e6;

    .button1 {
      border-radius: 15px;
      background-color: white;

      span {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #409eff;
        line-height: 20px;
      }
    }

    .button2 {
      background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
      box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
      border-radius: 15px;
      border: none;

      span {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: white;
        line-height: 20px;
      }
    }
    .button3 {
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 15px;
      border: none;

      span {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: white;
        line-height: 20px;
      }
    }
  }

  .group {
    width: 575px;
    height: 488px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 4px rgba(0, 0, 0, 0.04);
    padding: 20px;
    position: absolute;
    top: -8px;
    // right: 0;
    left: 0;
    z-index: 100;

    .gHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      .gTitle {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #17314c;
        line-height: 25px;
        a {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #409eff;
          line-height: 20px;
          margin-left: 22px;
        }
      }
    }

    .gContent {
      height: 390px;
      overflow-y: auto;
    }

    .gAction {
      width: 100%;
      height: 38px;
      line-height: 38px;
      text-align: center;
    }
  }
}

.treeScroll {
  overflow-y: auto;
  overflow-x: auto;
}

.takeRemain {
  flex: 1;
}

.background-color {
  color: #409eff;
}

:host .con ::ng-deep {
  nz-tag {
    margin-top: 10px;
  }

  .ant-collapse-content-box {
    padding-top: 5px;
  }

  .ant-collapse {
    min-width: 290px;
  }

  .ant-checkbox-wrapper {
    display: block;
    margin-left: 0;
  }

  .ant-collapse-extra {
    &:hover {
      color: sandybrown;
    }
  }
}

footer {
  border-top: 1px solid #e6e6e6;
  padding-top: 20px;
  margin-top: 20px;

  .btn_cancel {
    width: 128px;
    height: 38px;
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 22px;
  }

  .btn_confirm {
    width: 128px;
    height: 38px;
    background: #fafafa;
    border-radius: 19px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #aaaaaa;
    line-height: 22px;
  }
}

.del_icon,
.edit_icon_close {
  width: 18px;
  height: 18px;
  background-size: 100% 100% !important;
  margin: 0 5px;
}

.del_icon {
  background: url("../../../../assets/images/org/del.png") no-repeat;
}
.del_icon:hover {
  background: url("../../../../assets/images/org/del_hover.png") no-repeat;
}
.edit_icon_close {
  background: url("../../../../assets/images/org/edit_bg.png") no-repeat;
}
.edit_icon_close:hover {
  background: url("../../../../assets/images/org/edit_bg_hover.png") no-repeat;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  // background-color: transparent;
  background-color: #F1F1F1;
  box-shadow: none;
}
::-webkit-scrollbar-thumb {
  // background-color: #e9e9e9;
  background-color: #C1C1C1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}

:host ::ng-deep {
  .ant-modal-close-x {
    width: 36px !important;
    height: 36px !important;
  }
  .ant-collapse-header {
    background-color: #f5f6fa;
  }
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
