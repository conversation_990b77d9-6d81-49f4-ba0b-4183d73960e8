<div class="objectSetting">
    <div class="header">
        <h2>对象设置</h2>
        <div>
            <nz-upload [nzPreview]="preview" [nzCustomRequest]="customReq" [nzFileList]="fileList" style="display: flex;align-items: center;">
                <button nz-button nzType="link" nzSize="small">
                    <img src="./assets/images/Import.png" alt=""> <span>导入</span>
                </button>
            </nz-upload>
            <nz-divider nzType="vertical"></nz-divider>
            <button nz-button nzType="link" [nzLoading]="isDownLoadSpinning" (click)="downLoad()" nzSize="small">
                <img src="./assets/images/download.png" alt=""> <span>导出</span>
            </button>
        </div>
    </div>
    <div class="content">
        <div class="content_top">
            <div>
                <nz-input-group [nzSuffix]="suffixIconSearch">
                    <input type="text" nz-input placeholder="请输入姓名查找" [(ngModel)]="name" (keyup.enter)="search()" />
                </nz-input-group>
                <ng-template #suffixIconSearch>
                    <span nz-icon nzType="search"></span>
                </ng-template>
            </div>
            <span>匹配人数：{{totalCount || 0}} 人</span>
        </div>
        <nz-table class="table" #basicTable [nzData]="listData" nzBordered [nzSize]="'small'" [nzShowPagination]="false" [nzLoading]="tableLoading">
            <thead>
                <tr>
                    <th nzWidth="60px">序号</th>
                    <th nzWidth="155px">姓名</th>
                    <th>呈现名称</th>
                    <th nzWidth="100px">匹配结果</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let data of basicTable.data; let i = index">
                    <!-- <td>{{i+1+(pages-1)*pageSize}}</td> -->
                    <td>{{i+1}}</td>
                    <td>{{data.name}}</td>
                    <td>{{data.displayNameDesc}}</td>
                    <td>
                        <span nz-icon [nzType]="data.isRefine?'check-circle':'close-circle'" nzTheme="fill" [ngClass]="data.isRefine?'end':'progress'"></span>
                        <span>{{data.isRefine?'已完善':'待完善'}}</span>
                    </td>
                </tr>
            </tbody>
        </nz-table>
        <div class="content_btm">
            <nz-pagination nzShowQuickJumper [(nzPageIndex)]="currentPage" [(nzPageSize)]="pageSize" [nzTotal]="totalCount" [nzSize]="'small'" (nzPageIndexChange)="pageChange(currentPage)">
            </nz-pagination>
        </div>
    </div>
    <div class="footer">
        <span class="warnText" *ngIf="unRefineCount">*有 {{unRefineCount}} 组数据待完善，确定要保存当前设置吗？</span>
        <span *ngIf="!unRefineCount"></span>
        <div class="btns">
            <span (click)="clear()">清空</span>
            <span (click)="confirm()">确认</span>
        </div>
    </div>
</div>