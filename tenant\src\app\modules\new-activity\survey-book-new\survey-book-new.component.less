@bgColor: #40a9ff;
@fgColor: white;

.btn-parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

::ng-deep .survey-book-add-modal .ant-modal-body {
  padding: 0;
}

::ng-deep {
  .through-tr {
    border-bottom: none;

    td {
      border-bottom: none !important;
    }
  }
}

.tip-box {
  display: inline-block;
  // text-align: center;
  background: rgba(51, 114, 255, 0.08);
  border-radius: 2px;
  padding: 2px 5px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 17px;
  margin-right: 10px;
  word-wrap: break-word;
}

.ques-box {
  display: flex;

  p {
    flex: 1;
  }
}

.content {
  background-color: white;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  min-width: 1200px;

  .body {
    flex: 1;
    margin-left: 15px;
    margin-right: 15px;
    padding: 8px;
  }

  .title {
    display: flex;
    align-items: center;
    margin: 20px 30px 20px 0;
    font-size: 24px;
    font-family: PingFangSC-Thin, PingFang SC;
    font-weight: 200;
    color: #17314c;

    > span {
      margin-right: 10px;
    }
  }

  .searchDiv {
    margin-left: 50px;
  }

  .input-search {
    border-radius: 20px;
    width: 200px;
  }

  .icon-search {
    color: #409eff;
  }

  .tab {
    font-size: 24px;
    font-weight: 500;
    color: #17314c;
    line-height: 33px;
  }

  .action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 56px;

    .right-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .projName {
    max-width: 400px;
    white-space: pre-wrap;
    text-align: center;
    font-size: 20px;
    font-weight: 600;
    color: #17314c;
    line-height: 28px;
  }

  .topiclist {
    // height: 600px;
    // height: 100%;

    .topic {
      padding: 5px 2px;
      min-height: 100px;
      border-top: solid 1px #e6e6e6;
      max-width: 1150px;

      .ques {
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
        font-size: 16px;
        font-weight: 400;
        color: #17314c;
        line-height: 28px;

        .quesName {
          display: flex;
        }

        span {
          max-width: 1015px;
        }
      }

      .optionlist {
        display: flex;
        flex-wrap: wrap;
        max-width: 100%;

        div {
          padding-bottom: 10px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #17314c;
          line-height: 22px;
        }

        .marg {
          margin-right: 60px;
        }
      }
    }

    .double {
      // background-color: #CEE7FF;
      background-color: #f5faff;
    }
  }
}

.last_topic {
  border-bottom: solid 1px #e6e6e6;
}

.iptBtn {
  width: 128px;
  height: 38px;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  font-weight: 500;
  color: #ffffff;
  // font-size: 14px;
}

.invite_button {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  font-weight: 500;
  color: #ffffff;
  cursor: pointer;
  padding: 0 20px;
}

.revision-btn {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e6e6e6;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.03);
  border-radius: 19px;
  background-color: white;
  padding: 0 20px;
  cursor: pointer;
  margin-left: 10px;
}

// .revision-btn:hover {
//   color: white;
//   background-color: @bgColor;
//   box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
//   color: #ffffff;
//   border-color: @bgColor;
// }

.to-delete {
  color: #f19672;
}

.svg-ico-delete {
  background-color: #f19672;
}

.delete-box {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-left: 14px;
}

.delete-txt {
  color: #f19672;
  margin-left: 6px;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  box-shadow: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}
