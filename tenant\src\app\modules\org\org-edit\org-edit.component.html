<div class="title">
  组织信息
</div>

<form nz-form [formGroup]="validateForm">
  <!-- <nz-form-item>
    <nz-form-label [nzSpan]="6" nzFor="email" nzRequired>组织名称（中）</nz-form-label>
    <nz-form-control [nzSpan]="17" nzErrorTip="组织名称(中)不能为空！">
      <input nz-input formControlName="zh_CN" [(ngModel)]="orgModel.name.zh_CN">
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSpan]="6" nzFor="email">组织名称（英）</nz-form-label>
    <nz-form-control [nzSpan]="17">
      <input nz-input formControlName="en_US" [(ngModel)]="orgModel.name.en_US">
    </nz-form-control>
  </nz-form-item> -->
  <ng-container *ngFor="let item of i18n">
    <nz-form-item>
      <nz-form-label
        [nzSpan]="8"
        nzFor="email"
        [nzRequired]="item.value === 'zh_CN'"
        >组织名称（{{ item.name }}）</nz-form-label
      >
      <nz-form-control [nzSpan]="15" nzErrorTip="组织名称不能为空！">
        <input
          nz-input
          [formControlName]="item.value"
          [(ngModel)]="orgModel.name[item.value]"
          placeholder="请输入"
        />
      </nz-form-control>
    </nz-form-item>
  </ng-container>
  <nz-form-item>
    <nz-form-label [nzSpan]="6" nzFor="email" nzRequired
      >组织编码&nbsp;&nbsp;</nz-form-label
    >
    <nz-form-control [nzSpan]="17" nzErrorTip="组织编码不能为空！">
      <input
        nz-input
        formControlName="code"
        [(ngModel)]="orgModel.code"
        placeholder="请输入"
        [disabled]="(isUpdate || typeshow) && id !== '0'"
      />
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-label [nzSpan]="6" nzFor="email"
      >组织说明&nbsp;&nbsp;</nz-form-label
    >
    <nz-form-control [nzSpan]="17" nzErrorTip="组织说明不能为空！">
      <input
        nz-input
        formControlName="description"
        [(ngModel)]="orgModel.description"
        placeholder="请输入"
      />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item *ngIf="typeshow && permission && orgModel.distance !== 0">
    <nz-form-label [nzSpan]="6" nzFor="email"
      >组织禁用&nbsp;&nbsp;</nz-form-label
    >
    <nz-form-control [nzSpan]="17">
      <div
        style="display: inline-block;"
        nz-popconfirm
        nzPopconfirmTitle="是否确认要禁用当前组织?"
        [nzCondition]="!orgModel.statusNew"
        (nzOnConfirm)="confirmSwitch()"
      >
        <nz-switch
          formControlName="statusNew"
          [nzLoading]="loading"
          [(ngModel)]="orgModel.statusNew"
          [nzControl]="true"
          (click)="clickIsVisitAnswer($event)"
          nzCheckedChildren="启用"
          nzUnCheckedChildren="禁用"
        ></nz-switch>
      </div>
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label *ngIf="!isUpdate" [nzSpan]="6" nzFor="email" nzRequired
      >上级组织&nbsp;&nbsp;</nz-form-label
    >
    <nz-form-label *ngIf="isUpdate" [nzSpan]="6" nzFor="email"
      >上级组织&nbsp;&nbsp;</nz-form-label
    >
    <nz-form-control [nzSpan]="17" nzErrorTip="上级组织不能为空！">
      <nz-select
        nzShowSearch
        nzAllowClear
        [(ngModel)]="orgModel.parentCode"
        (ngModelChange)="change($event)"
        formControlName="parentCode"
        [nzShowArrow]="true"
        style="width: 100%;"
        nzPlaceHolder="请选择"
        [nzDisabled]="
          !orgModel.isUpdateParentOrganization ||
          (orgModel && orgModel.distance === 0) ||
          fatherCode
        "
      >
        <ng-container *ngFor="let item of parentList">
          <nz-option
            *ngIf="item.id !== id"
            [nzValue]="item.code"
            [nzLabel]="item.name?.zh_CN + '（' + item.code + '）'"
          ></nz-option>
        </ng-container>
      </nz-select>
    </nz-form-control>
  </nz-form-item>
</form>
<div nz-row style="margin-bottom: 20px;" *ngIf="orgModel.distance !== 0">
  <div nz-col nzSpan="7"></div>
  <div nz-col nzSpan="17">
    <label nz-checkbox [(ngModel)]="orgModel.isVirtual"
      >虚拟组织（数据报表/报告不呈现分析）</label
    >
  </div>
</div>

<div class="footer">
  <button nz-button class="iptBtn" (click)="ok()">
    <span>确认</span>
  </button>
</div>
