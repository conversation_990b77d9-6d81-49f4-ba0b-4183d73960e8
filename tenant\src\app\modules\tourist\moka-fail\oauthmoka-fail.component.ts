import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Router } from "@angular/router";
import { NzMessageService } from "ng-zorro-antd";
import { TouristService } from '../tourist.service';


@Component({
  selector: 'app-mokaf',
  templateUrl: './oauthmoka-fail.component.html',
  styleUrls: ['./oauthmoka-fail.component.less']
})
export class MokaFailComponent implements OnInit {
    tourist:boolean=true;
    isNeedLogin:boolean=false;
    token:any;
    _token:any;
    constructor(private http: HttpClient,private router: Router,private message: NzMessageService,private TouristService:TouristService) { }

  ngOnInit() {
    this._token = JSON.parse(localStorage.getItem("_token"));
    if (this._token) {
        this.tourist=false;
        this.isNeedLogin=true;
    }
  }
  comeback(){
    if(this._token){
      this.router.navigate(['/home']);
    }else{
      this.router.navigate(['/tourist/home']);
    }
  }

}
