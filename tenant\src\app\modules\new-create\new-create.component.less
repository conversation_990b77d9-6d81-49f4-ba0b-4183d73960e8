.index_xy {
    display: flex;
    justify-content: center;
    padding: 30px 0;
    background-color: #f5f6fa;
    height: 100%;
  }

  .big_contant {
    .create_p {
      display: flex;
      justify-content: space-between;
      .span_left {
        font-size: 24px;
      }
      .span_right {
        span{
          cursor: pointer;
        }
        > .span_blue {
          color: #53a8fe;
        }
      }
    }
    .create_name {
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
      font-size: 14px;
      font-weight: 400;
      color: #17314c;
      > li {
        > p {
          margin: 0px 0 30px 0;
          font-weight: bold;
        }
        .pri_name {
          width: 100%;
          height: 50px;
          border-radius: 5px;
          font-size: 16px;
        }
      }
    }
    .setmodal {
      margin-top: 30px;
      .setmodal_top {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        > .cur_span {
          cursor: pointer;
          color: #409eff;
        }
      }
      .setmodal_card {
        margin-top: 30px;
        width: 100%;
        min-height: 646px;
        background-color: #fff;
        border-radius: 8px;
        display: flex;
        .card_left {
          width: 146px;
          padding: 20px 0;
          min-height: 646px;
          border-right: 1px solid #e6e6e6;
          .left_div {
            width: 146px;
            height: 48px;
            line-height: 48px;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            >span{
              width: 100px ;
              display: inline-block;
              overflow: hidden; /*超出部分隐藏*/
              white-space: nowrap; /*不换行*/
              text-overflow: ellipsis; /*超出部分文字以...显示*/
            }
          }
          .showClass {
            background-color: #f5faff;
            color: #409eff;
            border-left: 2px solid #409eff;
          }
        }
        .card_right {
          flex: 1;
          height: 100%;
          
          padding: 15px 30px;
          
        }
      }
    }
  }
  .submit_xy {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 70px;
    background: #fff;
    box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);
    display: flex;
    justify-content: center;
    .center_menu {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .menus_xy {
        display: flex;
        align-items: center;
        .menus_left {
          width: 128px;
          height: 38px;
          border-radius: 19px;
          border: 1px solid #409eff;
          text-align: center;
          line-height: 38px;
          cursor: pointer;
          color: #409eff;
          &:hover {
            box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.15);
          }
        }
  
        .menus_right {
          width: 128px;
          height: 38px;
          background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
          box-shadow: 0px 3px 8px 0px rgba(55,175,250,0.5);
          border-radius: 19px;
          text-align: center;
          line-height: 38px;
          cursor: pointer;
          color: #fff;
          margin-left: 20px;
          border: 0;
          &:hover {
            box-shadow: 0px 3px 8px 0px rgba(55,175,250,0.8);
          }
        }
  
        .menus_right_new {
          width: 128px;
          height: 38px;
          background: linear-gradient(90deg, #A1A9FF 0%, #BD97FF 100%);
          box-shadow: 0px 3px 8px 0px rgba(169,163,255,0.5);
          border-radius: 19px;
          text-align: center;
          line-height: 38px;
          cursor: pointer;
          color: #fff;
          margin-left: 20px;
          border: 0;
          &:hover {
            box-shadow: 0px 3px 8px 0px rgba(169,163,255,0.8);
          }
        }
      }
    }
  }

  .time_picker {
    height: 50px;
    width: 500px;
    border-radius: 5px;
    ::ng-deep input {
      font-size: 16px;
    }
  }
  :host ::ng-deep {
    .ant-calendar-range-picker-separator {
      line-height: 40px;
    }
    .ant-calendar-picker-input.ant-input {
      height: 50px;
      width: 500px;
      border-radius: 5px;
    }
    .ant-calendar-picker-icon {
      color: #409eff;
      font-size: 20px;
    }
    .ant-calendar-picker-clear,
    .ant-calendar-picker-icon {
      right: 14px;
      margin-top: -10px;
    }
  }
  .mock_div {
  
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999999999;
    .bg_ul {
      width: 100%;
      height: 100%;
      background-color: #000;
      opacity: 0.6;
    }
  
    .img_ul {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 999;
      display: flex;
      align-items: center;
      justify-content: center;
      > li {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .btn_div {
        width: 160px;
        line-height: 38px;
        text-align: center;
        color: #fff;
        background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        cursor: pointer;
      }
      
  
      
    }
  }

  .top_div {
    margin-top: 20px;
    border: 1px solid #efefef;
    border-radius: 8px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    .div_two {
      display: flex;
      padding:0 20px ;
      align-items: center;
      flex: 1;
      background: linear-gradient(to right, #FFFFFF 98%, #F4F4F4 ) ;
      .li_title {
        font-size: 14px;
        color: #17314c;
        font-weight: bold;
        width: 90px;
      }
      .li_list {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        .li_span {
          padding-left: 20px;
          display: inline-block;
          width: 100px;
          margin-left: 0;
          overflow: hidden; /*超出部分隐藏*/
          white-space: nowrap; /*不换行*/
          text-overflow: ellipsis; /*超出部分文字以...显示*/
        }
      }
    }
  
    .custom_add_xy {
      color: #409eff;
      font-size: 12px;
      > span {
        cursor: pointer;
      }
    }
    .border_left_d{
      border-left: 1px solid #E1E1E1;
      margin: 0 10px;
      height: 14px;
    }
  }
  .nz_modal{
    position: relative;
  }
.right_top{
  width: 90%;
}