<div class="container">
  <div class="type" *ngFor="let item of lans; let ind = index">
    <div>
      <div class="lan">调整前 {{ item.name }}</div>
      <div [innerHTML]="item.origin | html"></div>
    </div>

    <div style="margin-top: 20px;">
      <div class="lan">调整后 {{ item.name }}</div>
      <div style="margin-bottom: 20px;">
        <textarea rows="4" nz-input [(ngModel)]="item.after"></textarea>
      </div>
    </div>
    <!-- <div style="margin-bottom: 20px;">
            <tinymce id="formula-textareanew" *ngIf="isVisible" [(ngModel)]="item.after" delay=100>
            </tinymce>
        </div> -->
  </div>
</div>
