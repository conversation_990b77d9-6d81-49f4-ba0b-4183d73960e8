<div class="tipReportType">
  <nz-card>
    <nz-checkbox-wrapper
      style="width: 100%;"
      (nzOnChange)="changeTypes($event)"
    >
      <div nz-row>
        <div
          class="tipReportType_item"
          nz-col
          nzSpan="8"
          *ngFor="let item of items"
        >
          <label
            [nzDisabled]="isdisabled"
            nz-checkbox
            [nzValue]="item.reportType"
            [ngModel]="checked?.includes(item.reportType)"
            (ngModelChange)="clickType(item.reportType)"
            >{{ item.name.zh_CN }}</label
          >
        </div>
      </div>
    </nz-checkbox-wrapper>
  </nz-card>
</div>
