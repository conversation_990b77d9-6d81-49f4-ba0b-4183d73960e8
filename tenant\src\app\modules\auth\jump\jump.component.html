<div class="jump">
  <ng-container *ngIf="!isSuccess && !isError">
    <div class="jump-spin">
      <div class="jump-spin-load">
        <img src="assets/images/jump/load.png" />
        <div class="jump-spin-load-iocn">
          <ng-template #indicatorTemplate>
            <i nz-icon type="loading" style="font-size: 40px;"></i>
          </ng-template>
          <nz-spin nzSimple [nzIndicator]="indicatorTemplate"> </nz-spin>
        </div>
      </div>
      <!-- <p>The page is currently redirecting.<br /> Please do not close the page and bepatient…</p> -->
      <p>页面当前正在授权中<br />请不要关闭页面，耐心等待…</p>
    </div>
  </ng-container>

  <ng-container *ngIf="isSuccess">
    <div class="jump-result success">
      <i nz-icon nzType="check-circle" nzTheme="fill"></i>
      <h1>授权成功</h1>
      <p>即将带您前往目标页面，请稍候...</p>
      <div class="percent">
        <nz-progress
          [nzPercent]="percent"
          [nzShowInfo]="false"
          [nzStrokeWidth]="4"
          nzStatus="success"
        ></nz-progress>
      </div>
      <!-- <button>返回首页</button> -->
    </div>
  </ng-container>
  <ng-container *ngIf="isError">
    <div class="jump-result error">
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>
      <h1>授权失败</h1>
      <p>{{ errorMsg }}</p>
      <button (click)="gotoLogin()">前往首页</button>
    </div>
  </ng-container>
</div>
