.topic-skip {
  .drawer-footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 10px 16px;
    text-align: right;
    left: 0px;
    background: #fff;
  }
  
  header {
    .topic-skip-close {
      text-align: right;
      margin-bottom: 5px;

      .icon-penetra-close {
        font-size: 11px;
        margin-top: 7px;
        margin-right: 7px;
        cursor: pointer;
      }
    }

    .topic-skip-title {
      position: absolute;
      right: 0;
      top: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 6px 0px;
      margin-right: 16px;
      // margin: 0 30px;
      // padding-bottom: 18px;
      // border-bottom: 1px solid #e6e6e6;
      .title-left {
        display: flex;
        align-items: center;

        span {
          font-size: 24px;
          font-family: PingFangSC-Light, PingFang SC;
          font-weight: 300;
          color: #5e5e5e;
          line-height: 33px;
        }

        .title-icon {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #5e5e5e;
          line-height: 20px;
          cursor: pointer;

          .iconfont {
            font-size: 14px;
          }
        }

        .title-icon:hover {
          color: #409eff;
          .iconfont {
            color: #409eff;
          }
        }
      }

      .associate-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 4px 15px;
        height: 30px;
        background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
        box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
        border-radius: 15px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 22px;
        cursor: pointer;
      }
    }
  }

  .topic-skip-body {
    // background: gold;
    display: flex;
    height: calc(100vh - 102px) !important;
    > div {
      padding: 16px;
    }

    .left {
      flex: 50%;
    }

    .right {
      width: 50%;
      background: #f8f8f8;
    }

    .filter-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
    }

    .tips {
      margin-left: 43px;
      margin-bottom: 10px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #aaaaaa;
      line-height: 17px;
    }
    
    .question-box {
      height: calc(100% - 130px);
      padding-right: 8px;
      overflow-y: auto;

      > li {
        display: flex;

        .label {
          display: flex;
          width: 18px;
          height: 18px;
        }

        > ul {
          margin-left: 14px;
          margin-bottom: 20px;

          li {
            display: flex;
            align-items: center;

            .icon-xiala {
              cursor: pointer;
              transform: rotate(-90deg);
              margin-right: 10px;
            }

            .turn {
              transform: rotate(0deg);
            }

            .icon-xiala,
            .turn {
              transition: all 0.1s linear;
            }
          }
        }
      }
    }
  }
}