<div>
  <ul>
    <li class="table-filter">
      <div>
        <p
          style="min-width: 30px;margin-right: 16px;"
          *ngIf="
            selectedname != '题本落地建议' && selectedname != '报告文字自定义'
          "
        >
          工具
        </p>
        <!--维度解释、维度区间-->
        <nz-select
          style="width: 150px;margin-right: 16px;"
          *ngIf="
            selectedname != '报告自定义' &&
            selectedname != '题本落地建议' &&
            selectedname != '报告文字自定义'
          "
          nzPlaceHolder="工具名称"
          [nzAllowClear]="true"
          [(ngModel)]="listOfTagOptions"
        >
          <nz-option
            *ngFor="let item of selectlist"
            nzCustomContent
            [nzValue]="item.id"
            [nzLabel]="item.name"
            ><span nz-tooltip [nzTooltipTitle]="item.name">{{
              item.name
            }}</span></nz-option
          >
        </nz-select>
        <!--报告自定义-->
        <nz-select
          style="width: 150px;margin-right: 16px;"
          *ngIf="selectedname == '报告自定义' && selectedname != '题本落地建议'"
          nzPlaceHolder="报告风格"
          [nzAllowClear]="true"
          [(ngModel)]="listOfTagOptions"
        >
          <nz-option
            *ngFor="let item of reportTitle"
            nzCustomContent
            [nzValue]="item.code"
            [nzLabel]="item.name"
          >
            <span nz-tooltip [nzTooltipTitle]="item.name">{{ item.name }}</span>
          </nz-option>
        </nz-select>
        <input
          *ngIf="
            selectedname != '自定义' &&
            selectedname != '报告自定义' &&
            selectedname != '题本落地建议' &&
            selectedname != '报告文字自定义'
          "
          nz-input
          style="width: 180px;margin-right: 16px;"
          placeholder="配置类型"
          [(ngModel)]="searchType"
        />
        <!--题本落地建议-->
        <nz-select
          style="width: 150px;margin-right: 16px;"
          *ngIf="
            selectedname === '题本落地建议' || selectedname === '报告文字自定义'
          "
          nzPlaceHolder="语言"
          [nzAllowClear]="true"
          [(ngModel)]="language"
          (ngModelChange)="Searchlist()"
        >
          <nz-option nzValue="zh_CN" nzLabel="中文"></nz-option>
          <nz-option nzValue="en_US" nzLabel="英文"></nz-option>
        </nz-select>
        <input
          nz-input
          style="width: 150px;margin-right: 16px;"
          *ngIf="
            selectedname != '报告自定义' && selectedname != '报告文字自定义'
          "
          [placeholder]="
            selectedname === '题本落地建议' ? '题目编号、题目' : '维度名称'
          "
          [(ngModel)]="searchName"
        />

        <div>
          <button
            nz-button
            nzType="link"
            (click)="Searchlist()"
            style="padding: 0;margin-left: 8px;"
          >
            <i nz-icon type="search"></i>查询
          </button>
          <button
            style="padding: 0;margin-left: 16px;"
            nz-button
            nzType="link"
            (click)="SearchReturn()"
          >
            <i nz-icon nzType="redo" nzTheme="outline"></i>
            重置
          </button>
        </div>
      </div>
      <div>
        <ul class="right">
          <!--维度解释-->
          <ng-container *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '维度解释'">
            <!-- <li class="divider"><nz-divider nzType="vertical"></nz-divider></li> -->
            <li class="btn">
              <nz-upload
                class="upload"
                [nzCustomRequest]="importDimension"
                [nzFilter]=""
                [nzShowUploadList]="false"
              >
                <i class="iconfont icon-icon_import"></i><span>导入</span>
              </nz-upload>
            </li>
          </ng-container>
          <ng-container *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '维度解释'">
            <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
            <li class="btn" (click)="exportDimension()">
              <i class="iconfont icon-icon_export"></i><span>导出</span>
            </li>
          </ng-container>
          <!--维度区间-->
          <ng-container
            style="margin-left: 20px;"
            *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '维度区间'"
          >
            <li class="btn">
              <nz-upload
                class="upload"
                [nzCustomRequest]="importScore"
                [nzFilter]=""
                [nzShowUploadList]="false"
              >
                <i class="iconfont icon-icon_import"></i><span>导入</span>
              </nz-upload>
            </li>
          </ng-container>
          <ng-container
            style="margin-left: 20px;"
            *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '维度区间'"
          >
            <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
            <li class="btn" (click)="exportScore()">
              <i class="iconfont icon-icon_export"></i><span>导出</span>
            </li>
          </ng-container>
          <!--自定义-->
          <ng-container
            style="margin-left: 20px;"
            *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '自定义'"
          >
            <li class="btn">
              <nz-upload
                class="upload"
                [nzCustomRequest]="importScorecus"
                [nzFilter]=""
                [nzShowUploadList]="false"
              >
                <i class="iconfont icon-icon_import"></i><span>导入</span>
              </nz-upload>
            </li>
          </ng-container>
          <ng-container
            style="margin-left: 20px;"
            *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '自定义'"
          >
            <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
            <li class="btn" (click)="exportScorecus()">
              <i class="iconfont icon-icon_export"></i><span>导出</span>
            </li>
          </ng-container>
          <!--题本落地建议-->
          <ng-container
            style="margin-left: 20px;"
            *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '题本落地建议'"
          >
            <!-- <li class="divider"><nz-divider nzType="vertical"></nz-divider></li> -->
            <li class="btn">
              <nz-upload
                class="upload"
                [nzCustomRequest]="importProposal"
                [nzFilter]=""
                [nzShowUploadList]="false"
              >
                <i class="iconfont icon-icon_import"></i><span>导入</span>
              </nz-upload>
            </li>
          </ng-container>
          <ng-container
            style="margin-left: 20px;"
            *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '题本落地建议'"
          >
            <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
            <li class="btn" (click)="exportProposal()">
              <i class="iconfont icon-icon_export"></i><span>导出</span>
            </li>
          </ng-container>
          <!--报告文字自定义-->
          <ng-container
            style="margin-left: 20px;"
            *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '报告文字自定义'"
          >
            <li class="btn">
              <nz-upload
                class="upload"
                [nzCustomRequest]="importCustomText"
                [nzFilter]=""
                [nzShowUploadList]="false"
              >
                <i class="iconfont icon-icon_import"></i><span>导入</span>
              </nz-upload>
            </li>
          </ng-container>
          <ng-container
            style="margin-left: 20px;"
            *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '报告文字自定义'"
          >
            <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
            <li class="btn" (click)="exportCustomText()">
              <i class="iconfont icon-icon_export"></i><span>导出</span>
            </li>
          </ng-container>
          <!-- 报告自定义 -->
          <ng-container
          style="margin-left: 20px;"
          *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '报告自定义'"
        >
          <li class="btn">
            <nz-upload
              class="upload"
              [nzCustomRequest]="importReportConfig"
              [nzFilter]=""
              [nzShowUploadList]="false"
            >
              <i class="iconfont icon-icon_import"></i><span>导入</span>
            </nz-upload>
          </li>
        </ng-container>
        <ng-container
          style="margin-left: 20px;"
          *ngIf="permissionService.isPermissionOrSag(
              'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
            ) && selectedname == '报告自定义'"
        >
          <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
          <li class="btn" (click)="exportReportConfig()">
            <i class="iconfont icon-icon_export"></i><span>导出</span>
          </li>
        </ng-container>
        </ul>
      </div>
    </li>
  </ul>
  <nz-table
    #basicTable
    [nzData]="listOfData"
    [nzScroll]="{ x: '1000px', y: 'calc(100vh - 315px)' }"
    [nzShowPagination]="
      selectedname == '报告自定义' || selectedname == '报告文字自定义'
    "
    [nzLoading]="nzLoading"
  >
    <!-- -->
    <thead>
      <tr>
        <th
          *ngFor="let item of tabletitle; let i = index"
          [nzLeft]="item.Left"
          [nzRight]="item.Right"
          [nzWidth]="item.nzWidth"
          [nzAlign]="item.nzAlign"
          [ngClass]="{ bl: i === tabletitle.length - 1 }"
        >
          {{ item.name }}
        </th>
      </tr>
    </thead>
    <tbody *ngIf="selectedname == '维度解释'">
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td nzAlign="center">{{ i + 1 }}</td>
        <td>
          {{ data.configType }}
        </td>
        <td>
          {{ data.code }}
        </td>
        <td nzLeft="0px">
          <nz-select [(ngModel)]="data.lang">
            <nz-option nzValue="zh_CN" nzLabel="中文"></nz-option>
            <nz-option nzValue="en_US" nzLabel="英文"></nz-option>
          </nz-select>
        </td>
        <td nzLeft="100px">
          {{ data.lang == "zh_CN" ? data.name.zh_CN : data.name.en_US }}
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.description.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.description.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeLeftDescription.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeLeftDescription.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeLeftDescribeAdvantage.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeLeftDescribeAdvantage.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeLeftDescribeDisadvantage.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeLeftDescribeDisadvantage.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeRightDescription.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeRightDescription.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeRightDescribeAdvantage.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeRightDescribeAdvantage.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeRightDescribeDisadvantage.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.closeRightDescribeDisadvantage.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.reflectiveQuestion.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.reflectiveQuestion.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.recommendedBooks.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.recommendedBooks.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.interviewQuestions.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.interviewQuestions.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.taskTraining.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.taskTraining.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.recommendedCourses.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.recommendedCourses.en_US"
          ></textarea>
        </td>
        <td nzRight="0px" class="bl">
          <ul class="table_action">
            <li *ngIf="!data.editor" (click)="geteditor(data)">编辑</li>
            <li *ngIf="data.editor" (click)="saveditor(data)">保存</li>
            <li
              *ngIf="data.editor"
              style="margin-left: 20px;"
              (click)="canceleditor(data)"
            >
              取消
            </li>
          </ul>
        </td>
      </tr>
    </tbody>
    <tbody *ngIf="selectedname == '维度区间'">
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td nzAlign="center">{{ i + 1 }}</td>
        <td>
          {{ data.type }}
        </td>
        <td>
          {{ data.code }}
        </td>
        <td nzLeft="0px">
          <nz-select [(ngModel)]="data.lang">
            <nz-option nzValue="zh_CN" nzLabel="中文"></nz-option>
            <nz-option nzValue="en_US" nzLabel="英文"></nz-option>
          </nz-select>
        </td>
        <td nzLeft="100px">
          {{ data.lang == "zh_CN" ? data.name.zh_CN : data.name.en_US }}
        </td>
        <td>
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.highScore"
          ></textarea>
        </td>
        <td>
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.lowScore"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.description.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.description.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.comment.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.comment.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.advantage.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.advantage.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.development.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.development.en_US"
          ></textarea>
        </td>
        <td nzRight="0px" class="bl">
          <ul class="table_action">
            <li *ngIf="!data.editor" (click)="geteditor(data)">编辑</li>
            <li *ngIf="data.editor" (click)="saveditor(data)">保存</li>
            <li
              *ngIf="data.editor"
              style="margin-left: 20px;"
              (click)="canceleditor(data)"
            >
              取消
            </li>
          </ul>
        </td>
      </tr>
    </tbody>

    <tbody *ngIf="selectedname == '自定义'">
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td nzAlign="center">{{ i + 1 }}</td>
        <td>
          <nz-select [(ngModel)]="data.lang">
            <nz-option nzValue="zh_CN" nzLabel="中文"></nz-option>
            <nz-option nzValue="en_US" nzLabel="英文"></nz-option>
          </nz-select>
        </td>
        <td nzLeft="0px">
          {{ data.name.zh_CN }}
        </td>

        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.description.zh_CN"
          ></textarea>

          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.description.en_US"
          ></textarea>
        </td>

        <td>
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.reflectiveQuestion[data.lang]"
          >
          </textarea>
        </td>

        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.recommendedBooks.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.recommendedBooks.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.trainCourse.zh_CN"
          ></textarea>
          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.trainCourse.en_US"
          ></textarea>
        </td>

        <td nzRight="0px" class="bl">
          <ul class="table_action">
            <li *ngIf="!data.editor" (click)="geteditor(data)">编辑</li>
            <li *ngIf="data.editor" (click)="saveditor(data)">保存</li>
            <li
              *ngIf="data.editor"
              style="margin-left: 20px;"
              (click)="canceleditor(data)"
            >
              取消
            </li>
          </ul>
        </td>
      </tr>
    </tbody>
    <tbody *ngIf="selectedname == '报告自定义'">
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td nzAlign="center"  nzLeft="0px">{{ i + 1 }}</td>
        <td nzBreakWord   nzLeft="65px">
          <nz-select [(ngModel)]="data.lang">
            <nz-option nzValue="zh_CN" nzLabel="中文"></nz-option>
            <nz-option nzValue="en_US" nzLabel="英文"></nz-option>
          </nz-select>
        </td>
        <td nzBreakWord  nzLeft="165px">
          {{ reportStyle[data.reportStyle] }}
        </td>
        <td nzBreakWord>
          {{ data.reportConfig }}
        </td>
        <td nzBreakWord>
          {{ reportCodeName[data.reportConfig] && reportCodeName[data.reportConfig][data.lang] ? reportCodeName[data.reportConfig][data.lang] : '' }}
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.name.zh_CN == null ? reportCustomizationTitle[5].name : ''
            "
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.name.zh_CN"
          ></textarea>

          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.name.en_US == null ? reportCustomizationTitle[5].name : ''
            "
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.name.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.description.zh_CN == null ? reportCustomizationTitle[6].name : ''
            "
            [nzAutosize]="{ minRows: 3, maxRows: 6 }"
            [(ngModel)]="data.description.zh_CN"
          ></textarea>

          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.description.en_US == null ? reportCustomizationTitle[6].name : ''
            "
            [nzAutosize]="{ minRows: 3, maxRows: 6 }"
            [(ngModel)]="data.description.en_US"
          ></textarea>
        </td>
        <td nzRight="0px" class="bl">
          <ul class="table_action">
            <li *ngIf="!data.editor" (click)="geteditor(data)">编辑</li>
            <li *ngIf="data.editor" (click)="saveditor(data)">保存</li>
            <li
              *ngIf="data.editor"
              style="margin-left: 16px;"
              (click)="canceleditor(data)"
            >
              取消
            </li>
          </ul>
        </td>
      </tr>
    </tbody>
    <tbody *ngIf="selectedname == '题本落地建议'">
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td nzLeft="0px">{{ data.code }}</td>
        <td nzBreakWord nzLeft="100px" *ngIf="language === 'zh_CN'">
          {{ data.name }}
        </td>
        <td nzBreakWord nzLeft="100px" *ngIf="language === 'en_US'">
          {{ data.nameEn }}
        </td>
        <td *ngIf="language === 'zh_CN'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="data.suggestion1 == null ? proposaltle[2].name : ''"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion1"
          ></textarea>
        </td>
        <td *ngIf="language === 'zh_CN'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="data.suggestion2 == null ? proposaltle[3].name : ''"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion2"
          ></textarea>
        </td>
        <td *ngIf="language === 'zh_CN'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="data.suggestion3 == null ? proposaltle[4].name : ''"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion3"
          ></textarea>
        </td>
        <td *ngIf="language === 'zh_CN'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="data.suggestion4 == null ? proposaltle[5].name : ''"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion4"
          ></textarea>
        </td>
        <td *ngIf="language === 'zh_CN'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="data.suggestion5 == null ? proposaltle[6].name : ''"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion5"
          ></textarea>
        </td>
        <td *ngIf="language === 'zh_CN'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="data.suggestion6 == null ? proposaltle[7].name : ''"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion6"
          ></textarea>
        </td>
        <td *ngIf="language === 'en_US'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.suggestion1En == null ? proposaltle[2].name : ''
            "
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion1En"
          ></textarea>
        </td>
        <td *ngIf="language === 'en_US'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.suggestion2En == null ? proposaltle[3].name : ''
            "
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion2En"
          ></textarea>
        </td>
        <td *ngIf="language === 'en_US'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.suggestion3En == null ? proposaltle[4].name : ''
            "
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion3En"
          ></textarea>
        </td>
        <td *ngIf="language === 'en_US'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.suggestion4En == null ? proposaltle[5].name : ''
            "
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion4En"
          ></textarea>
        </td>
        <td *ngIf="language === 'en_US'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.suggestion5En == null ? proposaltle[6].name : ''
            "
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion5En"
          ></textarea>
        </td>
        <td *ngIf="language === 'en_US'">
          <textarea
            [(disabled)]="!data.editor"
            nz-input
            [placeholder]="
              data.suggestion6En == null ? proposaltle[7].name : ''
            "
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.suggestion6En"
          ></textarea>
        </td>
        <td nzRight="0px" class="bl">
          <ul class="table_action">
            <li *ngIf="!data.editor" (click)="geteditor(data)">编辑</li>
            <li *ngIf="data.editor" (click)="saveditor(data)">保存</li>
          </ul>
        </td>
      </tr>
    </tbody>
    <tbody *ngIf="selectedname === '报告文字自定义'">
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td>{{ i + 1 }}</td>
        <td nzBreakWord>
          <nz-select [(ngModel)]="data.lang">
            <nz-option nzValue="zh_CN" nzLabel="中文"></nz-option>
            <nz-option nzValue="en_US" nzLabel="英文"></nz-option>
          </nz-select>
        </td>
        <td nzBreakWord>
          {{ data.reportTextConfig }}
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [disabled]="true"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.name.zh_CN"
          ></textarea>

          <textarea
            *ngIf="data.lang == 'en_US'"
            [disabled]="true"
            nz-input
            placeholder="文字说明"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.name.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="自定义文字说明(中文)"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.customName.zh_CN"
          ></textarea>

          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="自定义文字说明(英文)"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.customName.en_US"
          ></textarea>
        </td>
        <td>
          <textarea
            *ngIf="data.lang == 'zh_CN'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="描述文字(中文)"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.describe.zh_CN"
          ></textarea>

          <textarea
            *ngIf="data.lang == 'en_US'"
            [(disabled)]="!data.editor"
            nz-input
            placeholder="描述文字(英文)"
            [nzAutosize]="{ minRows: 1, maxRows: 6 }"
            [(ngModel)]="data.describe.en_US"
          ></textarea>
        </td>
        <td nzRight="0px" class="bl">
          <ul class="table_action">
            <li *ngIf="!data.editor" (click)="geteditor(data)">编辑</li>&nbsp;&nbsp;
            <li nz-popconfirm
            nzPopconfirmTitle="确定要删除该配置吗？"
            (nzOnConfirm)="removeReportText(data)">删除</li>&nbsp;&nbsp;
            <li *ngIf="data.editor" (click)="saveditor(data)">保存</li>&nbsp;&nbsp;
            <li
              *ngIf="data.editor"
              
              (click)="canceleditor(data)"
            >
              取消
            </li>
          </ul>
        </td>
      </tr>
    </tbody>
  </nz-table>

  <div
    *ngIf="selectedname !== '报告自定义' && selectedname !== '报告文字自定义'"
    style="display: flex;justify-content: flex-end;margin-top: 16px;"
  >
    <nz-pagination
      [nzTotal]="Pagetotal"
      [nzPageSize]="10"
      [(nzPageIndex)]="PageIndex"
      (nzPageIndexChange)="PageSizeChange()"
    ></nz-pagination>
  </div>
</div>
