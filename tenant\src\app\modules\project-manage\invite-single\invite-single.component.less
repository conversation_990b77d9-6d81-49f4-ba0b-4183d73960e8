* {
  margin: 0;
  padding: 0;
}

.content {
  width: 100%;
  .xy_tab{
    margin-top: -30px;
    padding-bottom: 30px;
    display: flex;
    justify-content: flex-end;
    .xy_tab_ul{
      display: flex;
      .tab_ul_li{
        width: 80px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        background-color: #fff;
        cursor: pointer;
      }
      .xy_ch_li{
        background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
        color: #fff;
      }
    }
  }
}

.left {
  width: 428px;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin-right: 150px;
  justify-content: space-between;
}

.title {
  margin-bottom: 20px;
  height: 20px;
  font-size: 14px;
  font-weight: bold;
  color: rgba(23, 49, 76, 1);
  line-height: 20px;
}

.label {
  margin-top: 16px;
  margin-bottom: 10px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(23, 49, 76, 1);
  line-height: 20px;
}

.left>div {
  width: auto;
  //   background-color: yellow;
}

.text {
  width: 428px;
  height: 40px;
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
  border: 1px solid rgba(230, 230, 230, 1);
  padding: 0 10px;
  // margin-bottom: 16px;
}
.xy_Refresh{
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.textarea {
  width: 428px;
  height: 112px;
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
  border: 1px solid rgba(230, 230, 230, 1);
  padding: 0 10px;
  // margin-bottom: 16px;
}

.right {
  width: 300px;
  height: auto;
//   background-color: wheat;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.tip {
  margin-bottom: 20px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(64, 158, 255, 1);
  line-height: 20px;
}

.error_tip {
  color: red;
  padding: 0 20px 0 0;
  margin-top: 5px;
  font-size: 12px;
  line-height: 20px;
}
.copy-btn{
  width: 72px;
  margin-top: 23px;
}
.download-btn{
  width: 102px;
  margin-top: 13px;
}

