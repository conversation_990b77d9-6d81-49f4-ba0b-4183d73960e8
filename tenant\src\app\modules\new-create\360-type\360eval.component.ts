import { Compo<PERSON>, OnIni<PERSON>, Inject, ViewChild } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import {
  NzModalService,
  NzMessageService,
  NzTreeComponent,
  NzFormatEmitEvent,
  NzTreeNode,
  NzModalRef,
  UploadXHRArgs,
} from "ng-zorro-antd";
import { NzDrawerService } from "ng-zorro-antd/drawer";
import { NewCreateService } from "../new-create.service";
import { NewPrismaService } from "../../new-prisma/new-prisma.service";
import * as differenceInCalendarDays from "date-fns/difference_in_calendar_days";
import { AdvancedMoreSetting } from "../adv_set/advsetting.component";
import { timer } from "rxjs";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { ModalContentComponent } from "../../../shared/tip-modal/modal-content/modal-content.component";
import _ from "lodash";
import { HttpEvent } from "@angular/common/http";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import { PermissionService } from "@src/modules/service/permission-service.service";
// 关联设置
import { TopicDistribution360Component } from "../topic-distribution-360/topic-distribution-360.component";

@Component({
  selector: "app-new-360",
  templateUrl: "./360eval.component.html",
  styleUrls: ["./360eval.component.less"],
})
export class EvalComponent implements OnInit {
  // 关联设置
  @ViewChild(TopicDistribution360Component, { static: false })
  topicDistribution360: TopicDistribution360Component;
  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/new-activity",
      name: "新建活动",
      Highlight: false,
    },
    {
      path: "",
      name: "活动设置",
      Highlight: true,
    },
  ];
  //
  tenantApi: string = "/tenant-api";
  shownumber = 0;

  //重构之后的数据
  showname = false;
  changeeditorName: any = {};
  changeNumber = 0;
  modelImageUrl = "";
  showIndex = 0;
  showmock = false;
  noviceGuidance = false;
  today = new Date();
  isNzOkLoading = false;
  isNzPreLoading = false;
  isNzRELoading = false;
  tooltype = "360";
  factorTable: any;
  factorTableCache: any;
  chooseIndex = 0;
  tabledata = [];
  toolsName = [];
  dateRange = []; // 活动周期
  addfactorshow = false; //自定义弹窗
  analysisFactorTitle;
  showAnalysisFactor;
  nzBodyStyle: any = { padding: "0" };
  factorisSpinning = false;
  echartData = [];
  isVisiblemodal = false;
  factorshow = false;
  limitcode = "";
  question_book = [];
  modaltip = [];
  relationship = [];
  relationrole = [];
  descTip = [];
  isSpinning = false;
  standardQuestionnaireId;
  typeedit = "";
  projectId = "";
  projectType = "";
  questionnaireId = "";
  visibleDesc = false;
  tinyConfig = {};
  Isactivelan = true;
  standardReportType;
  Association = false;
  Associationlist: any;
  isdisabled = false;
  isUpdate = false;
  isUpdateing = false;
  projectlist;
  confirmModal;
  oIsEmailReport = false;
  prismaData: any = {
    name: {
      zh_CN: "",
      en_US: "",
    },
    startTime: null,
    endTime: null,
    analysisFactorDto: [], //选中的人口标签
    questionNumInOnePage: "5", // 默认时间
    isCheckLicenseNotice: true, // 许可声明
    isCheckedAnswerValid: false, // 校验答案
    isEnableRoleDimension: false,
    isEnableWelcomePage: false,
    welcomePage: {
      zh_CN: "",
      en_US: "",
    },
    endPage: {
      zh_CN: "",
      en_US: "",
    },
    isInviteAnswer: false,
    inviteAnswerSetting: {},
    isEnableEndPage: false,
    isCheckAnswerInstructions: true, // 作答说明
    isPublicReport: false, // 允许测评者查看报告
    isEmailReport: false, //查看报告
    sequence: "QUESTION_TYPE",
    isCustomRoleWeight: false,
    isShowDemographicQuestion: true, // 是否显示人口信息学题
    isShowPreAnswer: true, //
    answerMode: "MULTI",
    isHideRole: false,
    relationPermissions: [],
    isShowRank: true,
    isShowScore: true,
    isShowUnderstand: true,
    language: "zh_CN",
    optionalLanguages: ["zh_CN"],
    availableLanguages: ["zh_CN", "en_US"], // 新增语言
    isShowKnxLogo: true, // 显示射手座logo
    isShowBackgroundPic: false,

    logoFile: "", // 企业客户logo
    pcBackgroundPic: "",
    mobileBackgroundPic: "",
    answerEffectiveRange: "1",
    answerSameRate: "100", // 一致性
    // 任意角色的有效填答人数
    answerRolePersonNum: {
      validNum: 0,
      rolePersons: [],
    },
    // 填答一致性-自定义设置
    answerSameRateCustom: {
      scaleCustoms: [],
      proportionCustoms: [],
    },
    answerEffectiveTime: "1", //默认时间
    standardDemographicDTO: {
      standardDemographicIds: [],
      type: "DEMOGRAPHIC_DEFAULT", // 人口学 默认
    }, // 除了敬业度都要传这个

    surveyType: "ASSESSMENT",
    projectReportDimensions: [],
    standardQuestionnaireDTO: {
      code: "",
      standardQuestionnaireDimensionDTO: [],
    },
    projectReportParentDimensions: [],
    answerDescription: {
      zh_CN: "",
      en_US: "",
    },
    isQuestionCustomSort: false,
    isAutoPage: true, //  一页一题时，自动翻页
    isAutoFillQuestionBook: false, // 题本分发致页面题目有缺时，自动补位后续题本
    // #12084 快速互评
    isQuickMutualEvaluation: false, // 是否开启快速互评
  };
  factorNameShow = false;
  factorName = {
    zh_CN: "",
    en_US: "",
  };
  factorindex = null;
  reportType = "";
  backUrl: string = "/new-activity";
  backUrlName: string = "新建活动";
  TipJobtip = [];
  visibleJob = false;
  reportTemplateId = "";
  radioValue;
  radioCode;
  radioCodelist = [];
  checkedvalue = false;
  radiolist = [];
  createtipid = "";
  Tipjobslist = [];
  jobcodeshow = false;
  ismodalclosed = false;
  PretestList = [];
  selecteddimensionList = [];
  codemaplist = [];
  PretestValue = null;
  PretestValueShow = false;

  nzSimple = false;
  lan = "zh_CN";
  i18n = [];
  // 题本分发导出loading
  isDispenseDownLoadSpinning: boolean = false;
  permission: boolean = false; // 超管权限
  isShowAll = true; //一键显示/隐藏人口学标签
  constructor(
    private routerInfo: ActivatedRoute,
    private router: Router,
    private modalService: NzModalService,
    private drawerService: NzDrawerService,
    private msg: NzMessageService,
    private http: NewCreateService,
    private prismaApi: NewPrismaService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private HttpClient: HttpClient,
    private modal: NzModalService,
    private customMsg: MessageService,
    public knxFunctionPermissionService: KnxFunctionPermissionService,
    public permissionService: PermissionService
  ) {}
  /**
   * 初始化声明周期
   */
  ngOnInit(): void {
    this.backUrl = localStorage.getItem("backurl");
    const url = localStorage.getItem("backurl").split("?")[0];
    this.permission = this.permissionService.isPermission();

    switch (url) {
      case "/project-manage/home-detail":
        this.backUrlName = "活动详情";
        break;
      case "/project-manage/home":
        this.backUrlName = "活动管理";
        break;
      case "/new-activity":
        this.backUrlName = "新建活动";
        break;

      default:
        this.backUrlName = "新建活动";
        break;
    }
    this.Breadcrumbs[1].name = this.backUrlName;
    this.Breadcrumbs[1].path = this.backUrl;
    // this.backUrlName
    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    const _this = this;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.tinyConfig = {
      height: 300,
      fontsize_formats:
        "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 32pt 36pt",
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      images_upload_url: `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`, // 配置你图片上传的url
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.HttpClient.post(
          `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`,
          formData,
          { headers: headers }
        ).subscribe(
          (response: any) => {
            if (response) {
              this.HttpClient.get(
                `${this.tenantApi}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                { headers: headers }
              ).subscribe((imgurl: any) => {
                let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                success(url);
              });
            } else {
              if (response && response.rtnMsg) {
                failure(response.rtnMsg);
              } else {
                failure("上传失败：未知错误");
              }
            }
          },
          (error1) => {
            failure("上传失败：未知错误");
          }
        );
      },
    };
    this.tabledata = JSON.parse(localStorage.getItem("noprismadata"));
    this.projectId = this.routerInfo.snapshot.queryParams.projectId; // 修改用的
    this.questionnaireId = this.routerInfo.snapshot.queryParams.questionnaireId;

    if (this.routerInfo.snapshot.queryParams.projectType) {
      this.projectType = this.routerInfo.snapshot.queryParams.projectType;
    }
    this.standardQuestionnaireId = this.routerInfo.snapshot.queryParams
      .standardQuestionnaireId
      ? this.routerInfo.snapshot.queryParams.standardQuestionnaireId
      : this.tabledata[0].id;
    if (this.projectId) {
      this.getProjectSetting();
      if (this.projectType == "ANNOUNCED" || this.projectType == "PREVIEW") {
        this.isUpdate = true; // 未发布状态下的更新
      }
      if (
        this.projectType != "PREVIEW" &&
        this.projectType != "ANNOUNCED" &&
        this.projectType != undefined &&
        this.projectType != ""
      ) {
        this.isUpdateing = true; // 发布状态下的更新
        this.isdisabled = true;
      }
    } else {
      this.setLanOptions();
      this.newCreate();
    }
  }

  /**
   * 路径跳转
   * @param item 跳转地址
   */
  backU(item) {
    this.router.navigateByUrl(item.path);
  }

  /**
   * 流程指导-打开
   */
  getGuideContent() {
    this.showmock = true;
    this.noviceGuidance = true;
  }

  /**
   * 流程指导-打开
   */
  closed() {
    this.showmock = false;
    this.noviceGuidance = false;
  }

  /**
   * 路径跳转-首页/活动创建（暂未使用）
   * @param type
   */
  goHome(type: string) {
    if (type == "home") {
      this.router.navigateByUrl("/home");
    }
    if (type == "create") {
      this.router.navigateByUrl("/new-activity");
    }
  }

  /**
   * 活动-新建
   */
  newCreate() {
    this.tabledata.forEach((res) => {
      res.styles = [];
      res.reportProjects.forEach((val) => {
        if (val.checked) {
          res.styles.push(val.style);
        }
      });

      this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO.push(
        {
          name: res.name.zh_CN,
          isSelected: false,
          questionnaireId: res.id,
          standardQuestionnaireDimensionIds: [],
          styles: res.styles,
          standardReportTemplateId: "",
        }
      );
    });
    this.getSetInfo(this.projectId, this.standardQuestionnaireId);
  }

  /**
   * 活动-创建后，获取保存的活动信息，如：活动名称、活动时间、高级设置等
   */
  getProjectSetting() {
    this.prismaApi.getProjectSetting(this.projectId).subscribe((item) => {
      sessionStorage.setItem(
        "projectLanguages",
        JSON.stringify(item.data.availableLanguages)
      );
      sessionStorage.setItem("language", item.data.language);
      // 获取当前活动语言
      this.getLanOptions();
      this.projectlist = item.data;
      this.prismaData.answerMode = item.data.answerMode;
      this.prismaData.name = item.data.questionnaires[0].name.zh_CN;
      this.prismaData.description = item.data.questionnaires[0].description;

      this.projectType = item.data.status;
      this.questionnaireId = item.data.questionnaires[0].id;
      this.reportTemplateId = item.data.questionnaires[0].reportTemplateId;

      this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO = [];
      item.data.questionnaires.forEach((val) => {
        this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO.push(
          {
            name: val.name.zh_CN,
            questionnaireId: item.data.standardQuestionnaireIds[0],
            standardQuestionnaireDimensionIds: [],
            styles: val.styles,
          }
        );
      });
      (this.prismaData.isInviteAnswer = item.data.isInviteAnswer),
        (this.prismaData.isInviteReviewEvaluatee =
          item.data.isInviteReviewEvaluatee), // 是否邀请审核被评估人
        (this.prismaData.inviteAnswerSetting = item.data.inviteAnswerSetting),
        (this.prismaData.name = item.data.projectName);
      this.prismaData.startTime = item.data.startTime;
      this.prismaData.endTime = item.data.endTime;
      this.dateRange = [item.data.startTime, item.data.endTime];
      this.prismaData.analysisFactorDto = item.data.analysisFactorDtos;
      // 高级设置属性
      this.prismaData.isShowKnxLogo = item.data.isShowKnxLogo;
      this.prismaData.isShowBackgroundPic = item.data.isShowBackgroundPic;
      this.prismaData.isCheckLicenseNotice = item.data.isCheckLicenseNotice;
      this.prismaData.isCheckedAnswerValid = item.data.isCheckedAnswerValid;
      this.prismaData.isEnableRoleDimension = item.data.isEnableRoleDimension;
      this.prismaData.isEnableWelcomePage = item.data.isEnableWelcomePage;
      this.prismaData.isEnableEndPage = item.data.isEnableEndPage;
      this.prismaData.isShowPreAnswer = item.data.isShowPreAnswer;
      this.prismaData.isPublicReport = item.data.isPublicReport;
      this.prismaData.isEmailReport = item.data.isEmailReport;
      this.oIsEmailReport = item.data.isEmailReport;
      this.prismaData.welcomePage = item.data.welcomePage
        ? item.data.welcomePage
        : {};
      this.prismaData.endPage = item.data.endPage ? item.data.endPage : {};

      this.prismaData.isCheckAnswerInstructions =
        item.data.isCheckAnswerInstructions;
      this.prismaData.isShowDemographicQuestion =
        item.data.isShowDemographicQuestion;
      this.prismaData.language = item.data.language;
      this.prismaData.optionalLanguages = item.data.optionalLanguages;
      this.prismaData.availableLanguages = item.data.availableLanguages.length
        ? item.data.availableLanguages
        : ["zh_CN", "en_US"];

      this.prismaData.logoFile = item.data.logoFile
        ? item.data.logoFile.split("/")[3]
        : null;
      this.prismaData.pcBackgroundPic = item.data.pcBackgroundPic
        ? item.data.pcBackgroundPic.split("/")[3]
        : null;
      this.prismaData.mobileBackgroundPic = item.data.mobileBackgroundPic
        ? item.data.mobileBackgroundPic.split("/")[3]
        : null;
      this.prismaData.answerEffectiveRange = item.data.answerEffectiveRange
        ? item.data.answerEffectiveRange
        : "1";
      this.prismaData.answerSameRate = item.data.answerSameRate
        ? item.data.answerSameRate
        : "100";
      // 角色的有效填答人数
      this.prismaData.answerRolePersonNum = item.data.answerRolePersonNum || {
        validNum: 0,
        rolePersons: [],
      };
      // 填答一致性-自定义设置
      this.prismaData.answerSameRateCustom = item.data.answerSameRateCustom;
      this.prismaData.answerEffectiveTime = item.data.answerEffectiveTime
        ? item.data.answerEffectiveTime
        : "1";

      this.prismaData.questionNumInOnePage = item.data.questionNumInOnePage;
      this.prismaData.sequence = item.data.sequence;
      this.prismaData.isCustomRoleWeight = item.data.isCustomRoleWeight;
      this.prismaData.isShow = item.data.isShow;
      this.prismaData.isQuestionCustomSort =
        item.data.isQuestionCustomSort || false; //每页题数是否自定义
      this.prismaData.isAutoPage =
        item.data.isAutoPage !== undefined ? item.data.isAutoPage : true; // 一页一题是否自动翻页
      this.prismaData.isAutoFillQuestionBook =
        item.data.isAutoFillQuestionBook !== undefined
          ? item.data.isAutoFillQuestionBook
          : false; // 题本分发致页面题目有缺时，自动补位后续题本

      this.getSetInfo(this.projectId, this.standardQuestionnaireId);
      this.prismaData.isQuickMutualEvaluation =
        item.data.isQuickMutualEvaluation;
    });
  }

  /**
   * 活动-获取活动信息，如：选中维度，人口标签等
   * @param projectId 活动id
   * @param standardQuestionnaireId
   */
  getSetInfo(projectId, standardQuestionnaireId) {
    const noPrismaData = JSON.parse(localStorage.getItem("noprismadata"));
    const currentTabNoPrismaData = noPrismaData
      ? noPrismaData.filter((item) => item.id === standardQuestionnaireId)
      : [];
    const reportStyleSelected =
      currentTabNoPrismaData.length > 0
        ? currentTabNoPrismaData[0].reportProjects
            .filter((item) => item.checked)
            .map((item) => item.style)
        : [];
    const params = {
      projectId: projectId,
      standardQuestionnaireId: standardQuestionnaireId,
      reportStyleList: reportStyleSelected,
      editFlag: true,
    };
    this.prismaApi.listByQuestionnaireCodeCreate(params).subscribe((item) => {
      // 获取保存过，或者发布过的维度 驱动因素 人口标签 等
      if (item.result.code == 0) {
        this.factorTable = _.cloneDeep(item.data);
        this.factorTableCache = _.cloneDeep(item.data);
        this.processing(this.factorTable);
      }
    });
  }

  /**
   * 活动-从接口中获取数据后，处理数据
   * @param data 接口返回的factorTable
   */
  processing(data) {
    this.prismaData.analysisFactorDto = this.factorTable.standardAnalysisFactorVOS;
    let checkedCount = this.prismaData.analysisFactorDto.filter((item) => {
      return item.isChecked;
    }).length;
    let hiddenCount = this.prismaData.analysisFactorDto.filter((item) => {
      return item.isChecked && item.isHidden;
    }).length;
    let showCount = this.prismaData.analysisFactorDto.filter((item) => {
      return item.isChecked && !item.isHidden;
    }).length;
    if (hiddenCount == checkedCount) {
      this.isShowAll = true;
    }

    if (showCount == checkedCount) {
      this.isShowAll = false;
    }
    this.standardReportType = this.routerInfo.snapshot.queryParams
      .standardReportType
      ? this.routerInfo.snapshot.queryParams.standardReportType
      : data.surveyStandardSagReportTemplate.standardReportType;
    if (
      this.standardReportType == "_360_DEGREES" ||
      this.standardReportType == "_270_DEGREES"
    ) {
      this.PretestValueShow = true;
    }
    data.dimensionLevelEnumsZHnew = [];
    data.dimensionLevelEnums.forEach((res) => {
      if (res != "NONE") {
        data.dimensionLevelEnumsZHnew.push({
          code: res,
        });
      }
    });
    data.dimensionLevelEnumsZHnew.forEach((item) => {
      if (item.code == "HIGH") {
        item.name = "高层";
      }
      if (item.code == "MIDDLE") {
        item.name = "中层";
      }
      if (item.code == "PRIMARY") {
        item.name = "基层";
      }
      if (item.code == "PERSONAL") {
        item.name = "个人贡献者";
      }
    });

    this.TipJobtip = this.factorTable.relationPermissions.filter((res) => {
      return res.type == "JOB";
    });
    this.modaltip = data.relationPermissions.filter((res) => {
      return res.type == "MODEL";
    });
    this.question_book = data.relationPermissions.filter((res) => {
      return res.type == "QUESTION_BOOK";
    });
    this.relationship = data.relationPermissions.filter((res) => {
      return res.type == "EVALUATION_RELATIONSHIP";
    });
    this.relationrole = data.relationPermissions.filter((res) => {
      return res.type == "ROLE_DIMENSION";
    });
    this.descTip = data.relationPermissions.filter((res) => {
      return res.type == "ANSWER_DESCRIPTION";
    });
    this.prismaData.relationPermissions = data.relationPermissions;
    this.prismaData.answerDescription = data.answerDescription
      ? data.answerDescription
      : data.surveyStandardQuestionnaire.answerDescription;
    let reportType = data.surveyStandardQuestionnaire.reportType;
    this.reportType = reportType;
    if (reportType.indexOf("CUSTOMIZE") != -1) {
      this.typeedit = "CUSTOMIZE";
    } else {
      this.typeedit = "STAND";
    }
    this.toolsName = [];
    if (this.projectlist) {
      this.toolsName.push({
        title: this.projectlist.questionnaires[0].name,
        description: this.projectlist.questionnaires[0].description,
      });
    } else {
      this.toolsName.push({
        title: data.surveyStandardQuestionnaire.name,
        description: data.surveyStandardQuestionnaire.description,
      });
    }

    if (this.projectId) {
      this.prismaData.projectReportDimensions = [];
      data.detailedScoreConfigs.forEach((item) => {
        if (item.isSelect) {
          item.detailedScoreChildDimensions.forEach((res) => {
            if (res.isSelected) {
              this.prismaData.projectReportDimensions.push({
                code: res.code,
                name: res.name.zh_CN,
                parentName: item.name.zh_CN,
                isSelect: true,
                standardQuestionnaireId: this.standardQuestionnaireId,
                standardReportTemplateId: this.factorTable
                  .surveyStandardSagReportTemplate.id,
              });
            }
          });
        }
      }); //交互给后台的子维度选择
    }
    this.get360jobs();
  }

  /**
   * 360对标岗位数据  --走ca逻辑
   */
  get360jobs() {
    this.http.listCajobs().subscribe((res) => {
      if (res.result.code == 0) {
        this.Tipjobslist = res.data;
        this.getSelectJob();
      }
    });
  }
  /**
   * 活动-活动周期选中回调
   * @param result 日期范围
   */
  onChangeActivityDateRange(result: Date): void {
    // result[0] 活动开始时间
    if (result[0]) {
      if (!this.prismaData.startTime) {
        this.dateRange[0].setHours(0);
        this.dateRange[0].setMinutes(0);
      } else {
        const startTime_ = new Date(this.prismaData.startTime);
        if (
          startTime_.getHours() !== this.dateRange[0].getHours() &&
          startTime_.getMinutes() !== this.dateRange[0].getMinutes()
        ) {
          this.dateRange[0].setHours(startTime_.getHours());
          this.dateRange[0].setMinutes(startTime_.getMinutes());
        }
      }
      this.prismaData.startTime = `${this.formatDate(
        this.dateRange[0],
        "date"
      )}T${this.formatDate(this.dateRange[0], "time")}`;
    } else {
      this.prismaData.startTime = null;
    }

    // result[1] 活动结束时间
    if (result[1]) {
      if (!this.prismaData.endTime) {
        this.dateRange[1].setHours(23);
        this.dateRange[1].setMinutes(30);
      } else {
        const endTime_ = new Date(this.prismaData.endTime);
        if (
          endTime_.getHours() !== this.dateRange[1].getHours() &&
          endTime_.getMinutes() !== this.dateRange[1].getMinutes()
        ) {
          this.dateRange[1].setHours(endTime_.getHours());
          this.dateRange[1].setMinutes(endTime_.getMinutes());
        }
      }
      this.prismaData.endTime = `${this.formatDate(
        this.dateRange[1],
        "date"
      )}T${this.formatDate(this.dateRange[1], "time")}`;
    } else {
      this.prismaData.endTime = null;
    }
    this.prismaData.dateRange = this.dateRange;
  }

  /**
   * 活动周期--今日之前不可选择
   * @param current 当前选中时间
   * @returns
   */
  disabledDate = (current: Date): boolean => {
    return differenceInCalendarDays(current, this.today) < 0;
  };

  /**
   * 时间格式化-时间处理,将0-9数字补全
   * @param m 时间值
   * @returns 处理后的时间值
   */
  isZero(m) {
    return m < 10 ? "0" + m : m;
  }

  /**
   * 时间格式化-处理date与time
   * @param timestamp 时间戳
   * @param type 格式化类型
   * @returns
   */
  formatDate(timestamp, type: "date" | "time") {
    const time = new Date(timestamp); // 需要使用Date格式进行日期转化，若是时间戳、字符串时间，需要通过new Date(..)转化

    const y = time.getFullYear();

    const m = time.getMonth() + 1;

    const d = time.getDate();

    const h = time.getHours();

    const mm = time.getMinutes();

    const s = time.getSeconds();

    if (type === "date") {
      // date类型
      const dateResult = y + "-" + this.isZero(m) + "-" + this.isZero(d);
      return dateResult;
    } else {
      // time类型
      const TimeResult =
        this.isZero(h) + ":" + this.isZero(mm) + ":" + this.isZero(s);
      return TimeResult;
    }
  }

  /**
   * 人口标签-选中回调
   */
  ngModelChange(e) {
    this.prismaData.analysisFactorDto = this.factorTable.standardAnalysisFactorVOS;
  }

  /**
   * 人口标签-操作
   * @param status false自定义 true查看
   */
  addFactor(status: boolean) {
    this.showAnalysisFactor = status;
    if (status === false) {
      this.analysisFactorTitle = "自定义";
    } else {
      this.analysisFactorTitle = "查看";
    }
    // 打开人口标签抽屉
    this.addfactorshow = true;
  }

  /**
   * 人口标签-关闭回调
   */
  closeModal(type) {
    this.addfactorshow = type.addfactorshow;
    this.factorTable.standardAnalysisFactorVOS =
      type.factorlist.standardAnalysisFactorVOS;
    this.prismaData.analysisFactorDto = this.factorTable.standardAnalysisFactorVOS;
  }

  /**
   * 人口标签-恢复默认
   */
  getdefaultlist() {
    this.factorisSpinning = true;
    this.prismaApi
      .listByQuestionnaireCode(this.standardQuestionnaireId)
      .subscribe((item) => {
        if (item.result.code == 0) {
          this.factorTable.standardAnalysisFactorVOS =
            item.data.standardAnalysisFactorVOS;
          this.prismaData.analysisFactorDto = this.factorTable.standardAnalysisFactorVOS;
          this.changeNumber += 1;
          this.factorisSpinning = false;
        } else {
          this.factorisSpinning = false;
        }
      });
  }

  /**
   * 人口标签-清空选中
   */
  clearCheck() {
    this.factorTable.standardAnalysisFactorVOS.forEach((res) => {
      if (res.isChecked && !res.isRequire) {
        res.isChecked = false;
      }
    });
    this.prismaData.analysisFactorDto = this.factorTable.standardAnalysisFactorVOS;
  }

  //全选的人口标签
  checkAll() {
    this.factorTable.standardAnalysisFactorVOS.forEach((res) => {
      if (!res.isChecked && !res.isRequire) {
        res.isChecked = true;
      }
    });
    this.prismaData.analysisFactorDto = this.factorTable.standardAnalysisFactorVOS;
  }

  /**
   * 人口标签-隐藏or显示人口标签
   * @param e 选中标签
   * @param item 数据
   * @param index 下标
   */
  changeHidden(e, item, index) {
    e.stopPropagation();
    item.isHidden = !item.isHidden;
  }

  /**
   * 人口标签-自定义导入后触发
   * @param data 返回导入的人口标签
   */
  loadDataMap(data) {
    let tmpAttr: any[] = _.filter(
      this.factorTable.standardAnalysisFactorVOS,
      function(item) {
        return item.type !== "PULL_DOWN_BOX";
      }
    );
    tmpAttr = tmpAttr.concat(data);

    this.factorTable.standardAnalysisFactorVOS = tmpAttr;
    this.prismaData.analysisFactorDto = tmpAttr;
    this.isNzOkLoading = false;
    this.isNzRELoading = false;
    this.isNzPreLoading = false;
    this.isSpinning = false;
    this.shownumber++;
  }

  /**
   * 人口标签-因子-名称修改-未输入活动名称时点击人口标签修改
   * @param item item
   * @param i index
   */
  spaneditor(e, item, i) {
    e.stopPropagation();
    // item.isChecked = !item.isChecked;
    if (this.prismaData.name.zh_CN) {
      this.factorNameShow = true;
      this.factorindex = i;
      this.factorName = JSON.parse(JSON.stringify(item.name));
      localStorage.setItem("oldname", JSON.stringify(item.name));
    } else {
      // this.msg.warning("请填写活动名称！");
      this.customMsg.open("warning", "请填写活动名称");
    }
  }

  /**
   * 人口标签-因子-名称修改-确认
   */
  spanOk() {
    if (this.factorName.zh_CN) {
      this.factorTable.standardAnalysisFactorVOS[
        this.factorindex
      ].name = this.factorName;
      this.nzSimple = true;
      let submitData = this.synthesisTime();
      if (!this.projectId) {
        if (submitData) {
          this.create360project(submitData, "FactorPage", "");
        } else {
          this.nzSimple = false;
        }
      } else {
        if (submitData) {
          this.ActiveUpdataship(submitData, "FactorPage", "");
        } else {
          this.nzSimple = false;
        }
      }
    } else {
      // this.msg.warning("中文名称不可为空！");
      this.customMsg.open("warning", "中文名称不可为空");
    }
  }
  /**
   * 人口标签-因子-名称修改-取消/关闭
   */
  spanCancel() {
    this.factorNameShow = false;
    this.factorName = JSON.parse(localStorage.getItem("oldname"));
  }

  /**
   * 人口标签-弹窗取消
   */
  handleCancel() {
    this.addfactorshow = false;
    this.factorTable.standardAnalysisFactorVOS = this.factorTable.standardAnalysisFactorVOS.filter(
      (item) => {
        return item.standardDemographicId || item.saveisAdd || item.id;
      }
    );
  }

  /**
   * 维度指标-单个选中的维度code
   * @param e $event
   * @param type val.isSelected
   * @param item item
   * @param code val.code
   */
  onChangeChildDimensions(e, type, item, code) {
    let factorNum = this.prismaData.projectReportDimensions.length;
    let limitNum = this.factorTable.surveyStandardSagReportTemplate
      .maxDimensionNum;
    if (factorNum + 1 > limitNum) {
      this.limitcode = code;
    }
  }

  /**
   * 维度指标-选中所有的维度code
   * @param e $event
   * @param type val.isSelected
   * @param item item
   * @param code val.code
   */
  onChangeAllDimensions(e) {
    this.prismaData.projectReportDimensions = [];
    e.forEach((item) => {
      this.prismaData.projectReportDimensions.push({
        code: item,
      });
    });
    let factorNum = this.prismaData.projectReportDimensions.length;
    let limitNum = this.factorTable.surveyStandardSagReportTemplate
      .maxDimensionNum;
    if (factorNum > limitNum) {
      this.factorshow = true;
      let deletindex;
      this.prismaData.projectReportDimensions.forEach((item, index) => {
        if (item.code == this.limitcode) {
          deletindex = index;
        }
      });
      this.prismaData.projectReportDimensions.splice(deletindex, 1);
    }

    this.getCheckedName(
      this.factorTable.detailedScoreConfigs,
      this.prismaData.projectReportDimensions
    );
  }

  /**
   * 维度指标-清除条件
   */
  clearCode() {
    this.factorTable.detailedScoreConfigs.forEach((item) => {
      item.detailedScoreChildDimensions.forEach((val) => {
        val.isSelected = false;
      });
    });
    this.prismaData.projectReportDimensions = [];

    this.getCheckedName(
      this.factorTable.detailedScoreConfigs,
      this.prismaData.projectReportDimensions
    );
  }

  /**
   * 维度指标-推荐指标，显示黄色
   */
  mapCodeList() {
    if (this.PretestValueShow) {
      let valuecode = this.checkedvalue
        ? null
        : (this.radioValue ? this.radioValue : "") +
          "-" +
          (this.radioCode ? this.radioCode : "");
      this.factorTable.detailedScoreConfigs.forEach((item) => {
        item.detailedScoreChildDimensions.forEach((val) => {
          val.jobLevel = [];
          val.jobLevelMappings.forEach((res) => {
            val.jobLevel.push(res.jobId + "-" + res.level);
          });
          val.color = "";
          val.jobLevel.forEach((element) => {
            if (valuecode != "-" && element.search(valuecode) != -1) {
              val.color = "#FFBA3C";
            }
          });
          val.related = false;
        });
      });
      this.getCheckedName(
        this.factorTable.detailedScoreConfigs,
        this.prismaData.projectReportDimensions
      );
    } else {
      this.prismaData.projectReportDimensions = [];
      this.factorTable.detailedScoreConfigs.forEach((item) => {
        item.detailedScoreChildDimensions.forEach((res) => {
          res.isSelected = false;
          res.related = true;
          res.color = "";
          this.codemaplist.forEach((val) => {
            if (res.code == val) {
              res.isSelected = true;
              res.color = "#FFBA3C";
              this.prismaData.projectReportDimensions.push({
                code: res.code,
              });
            }
          });
        });
      });
      this.getCheckedName(
        this.factorTable.detailedScoreConfigs,
        this.prismaData.projectReportDimensions
      );
    }
  }

  /**
   * 维度指标-projectReportDimensions填充内容
   * @param detailedScoreConfigs this.factorTable.detailedScoreConfigs
   * @param projectReportDimensions this.prismaData.projectReportDimensions
   */
  getCheckedName(detailedScoreConfigs, projectReportDimensions) {
    detailedScoreConfigs.forEach((item) => {
      item.detailedScoreChildDimensions.forEach((res) => {
        projectReportDimensions.forEach((val) => {
          if (res.code == val.code) {
            val.name = res.name.zh_CN;
            val.parentName = item.name.zh_CN;
            val.isSelect = true;
            val.standardQuestionnaireId = this.standardQuestionnaireId; //standardQuestionnaireId
            val.standardReportTemplateId = this.factorTable.surveyStandardSagReportTemplate.id;
          }
        });
      });
    });
    // 维度变更获取作答说明文本替换显示
    this.setDescDefault(false);
  }

  /**
   *  维度指标-根据限制条件取消多余选择
   */
  factorOk() {
    this.factorTable.detailedScoreConfigs.forEach((res) => {
      if (res.isSelect) {
        res.detailedScoreChildDimensions.forEach((val) => {
          if (val.code == this.limitcode) {
            val.isSelected = false;
          }
        });
      }
    });
    this.factorshow = false;
  }

  /**
   * 高级设置-弹窗
   */
  showRoleModal() {
    const {
      startTime,
      endTime,
      name: { zh_CN },
    } = this.prismaData;
    if (!zh_CN) {
      // this.msg.warning("请填写活动名称!");
      this.customMsg.open("warning", "请填写活动名称");
      return;
    }
    if (!endTime || !startTime) {
      // this.msg.warning("请填写活动周期!");
      this.customMsg.open("warning", "请填写活动周期");
      return;
    }

    const modal = this.drawerService.create({
      nzContent: AdvancedMoreSetting,
      nzTitle: "高级设置",
      nzWidth: 475,
      nzContentParams: {
        listdata: this.prismaData,
        projecttype: this.tooltype,
        projectStatus: this.projectType,
        reportType: this.standardReportType,
        oIsEmailReport: this.oIsEmailReport,
        isUpdateing: this.isUpdateing,
      },
      nzWrapClassName: "round-right-drawer-new",
    });
    // this.modalService.afterAllClose.subscribe(() => {
    //   const child: AdvancedMoreSetting = modal.getContentComponent();
    //   this.prismaData = child.settingData
    // })
    modal.afterClose.subscribe((settingData) => {
      // 高级设置保存调用
      if (settingData) {
        this.prismaData = settingData;
        // this.submitSave(true);
        let submitData = this.synthesisTime();
        sessionStorage.setItem("savefactors", null);
        if (!this.projectId) {
          if (submitData) {
            this.create360project(submitData, "SeniorPage", "");
          }
        } else {
          this.isSpinning = true;
          this.isNzRELoading = true;
          this.isNzPreLoading = true;
          this.isNzOkLoading = true;
          if (submitData) {
            this.ActiveUpdataship(submitData, "SeniorPage", "");
          } else {
            this.isSpinning = false;
            this.isNzRELoading = false;
            this.isNzPreLoading = false;
            this.isNzOkLoading = false;
          }
        }
      }
    });
  }

  getModalPie() {
    if (this.echartData) {
      this.echartData = [];
      this.factorTable.detailedScoreConfigs.forEach((res) => {
        if (res.isSelect) {
          res.detailedScoreChildDimensions.forEach((val) => {
            if (val.isSelected) {
              this.echartData.push({
                name: val.name.zh_CN,
                value: Math.ceil(Math.random() * 10),
                parentName: res.name.zh_CN,
              });
            }
          });
        }
        if (!res.isSelect) {
          res.detailedScoreChildDimensions.forEach((val) => {
            this.echartData.push({
              name: val.name.zh_CN,
              value: Math.ceil(Math.random() * 10),
              parentName: res.name ? res.name.zh_CN : "",
            });
          });
        }
      });
      this.echartData = this.classification(this.echartData);
    }
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }

    this.modelImageUrl = `${baseUrl}api/file/www/${this.factorTable.surveyStandardSagReportTemplate.modelImageUrl}`;
    this.isVisiblemodal = true;
  }

  /**
   * 模型查看-图表数据归类
   * @param arr  this.echartData
   * @returns
   */
  classification(arr) {
    var map = {},
      dest = [];
    for (var i = 0; i < arr.length; i++) {
      var ai = arr[i];
      if (!map[ai.parentName]) {
        //key 依赖字段 可自行更改
        dest.push({
          name: ai.parentName,
          data: [ai],
        });
        map[ai.parentName] = ai;
      } else {
        for (var j = 0; j < dest.length; j++) {
          var dj = dest[j];
          if (dj.name == ai.parentName) {
            //key 依赖字段 可自行更改
            dj.data.push(ai);
            break;
          }
        }
      }
    }
    return dest;
  }

  /**
   * 模型弹窗-取消
   */
  modalCancel() {
    this.isVisiblemodal = false;
  }

  /**
   * 修改工具名称-打开
   */
  onEditName() {
    this.showname = true;
    this.changeeditorName = JSON.parse(JSON.stringify(this.toolsName[0].title));
  }

  /**
   * 修改工具名称-关闭
   */
  namehandleCancel() {
    this.showname = false;
  }

  /**
   * 修改工具名称-确认
   */
  namehandleOk() {
    if (this.changeeditorName.zh_CN.trim() == "") {
      // this.msg.error("工具名称不能为空！");
      this.customMsg.open("error", "工具名称不能为空！");
      return;
    }
    this.isSpinning = true;
    if (!this.projectId) {
      let submitData = this.synthesisTime();
      if (submitData) {
        this.prismaApi.create(submitData).subscribe((res) => {
          if (res.result.code == 0) {
            this.projectId = res.data[0].projectId;
            this.questionnaireId = res.data[0].id;
            this.reportTemplateId = res.data[0].reportTemplateId;
            this.updateName();
          }
        });
      } else {
        this.isSpinning = false;
      }
    } else {
      let submitData = this.synthesisTime();
      if (this.isUpdateing) {
        //发布的活动，
        if (submitData) {
          submitData.projectId = this.projectId;
          this.prismaApi.updateProjectSetting(submitData).subscribe((res) => {
            if (res.result.code == 0) {
              this.isSpinning = false;
              this.updateName();
            }
          });
        } else {
          this.isSpinning = false;
        }
      } else {
        //未发布的活动，
        if (submitData) {
          if (this.projectType == "PREVIEW") {
            submitData.status = "PREVIEW"; //(待发布)
          } else {
            submitData.status = "ANNOUNCED"; //(待发布)
          }

          submitData.id = this.projectId;
          this.prismaApi.updateAnnouncedProject(submitData).subscribe((res) => {
            if (res.result.code == 0) {
              this.isSpinning = false;
              this.updateName();
            }
          });
        } else {
          this.isSpinning = false;
        }
      }
    }
  }

  /**
   * 修改工具名称-修改保存
   */
  updateName() {
    let params = {
      name: this.changeeditorName,
      questionnaireId: this.questionnaireId,
    };
    this.prismaApi.updateAnswer(params).subscribe((res) => {
      if (res.result.code == 0) {
        this.showname = false;
        this.isSpinning = false;
        this.msg.success("更新成功！");
        this.getProjectSetting();
      } else {
        this.isSpinning = false;
      }
    });
  }

  /**
   * 定制题本
   */
  onClickBook() {
    if (this.TipJobtip.length != 0 && !this.TipJobtip[0].isConfirmed) {
      // this.msg.warning("请先完成岗位选择！");
      this.customMsg.open("warning", "请先完成岗位选择");
      return;
    }
    if (
      this.TipJobtip.length != 0 &&
      this.prismaData.projectReportDimensions.length == 0
    ) {
      // this.msg.warning("请先选择题本模型！");
      this.customMsg.open("warning", "请先选择题本模型");
      return;
    }
    if (
      this.relationrole.length != 0 &&
      this.prismaData.isEnableRoleDimension
    ) {
      this.prismaData.answerMode = "SINGLE";
    }
    if (this.question_book[0].isNeedSaveProject) {
      let submitData = this.synthesisTime();
      if (submitData) {
        if (this.projectId) {
          this.ActiveUpdata(submitData, "BookPage", "custom-book");
        } else {
          this.create360project(submitData, "BookPage", "custom-book");
        }
      }
    }
  }

  /**
   *  评价关系
   */
  onClickRelationEvaluation() {
    if (
      this.relationrole.length != 0 &&
      this.prismaData.isEnableRoleDimension
    ) {
      this.prismaData.answerMode = "SINGLE";
    }
    if (this.factorTable.detailedScoreConfigs.length != 0) {
      if (this.relationship[0].isNeedSaveProject) {
        let submitData = this.synthesisTime();
        if (submitData) {
          if (this.projectId) {
            this.ActiveUpdata(
              submitData,
              "ShipPage",
              "project-manage/invite360"
            );
          } else {
            this.create360project(
              submitData,
              "ShipPage",
              "project-manage/invite360"
            );
          }
        }
      }
    } else {
      // this.msg.error("请先导入题本");
      this.customMsg.open("error", "请先导入题本");
    }
  }

  /**
   * 作答说明-打开
   */
  showModalDesc() {
    let submitData = this.synthesisTime();
    if (submitData) {
      const { codes: dimensionCodes, isSelect } = this.getDimensionCodes();
      if (isSelect && dimensionCodes.length === 0) {
        // this.msg.warning("至少选择2个模型维度！");
        this.customMsg.open("warning", "至少选择2个模型维度");
        return;
      }
      this.visibleDesc = true;
      window.document.documentElement.scrollTop = 0;
    }
  }

  /**
   * 作答说明-确认
   */
  okModalDesc() {
    if (this.factorTable.detailedScoreConfigs.length != 0) {
      this.nzSimple = true;
      let submitData = this.synthesisTime();
      if (!this.projectId) {
        if (submitData) {
          this.create360project(submitData, "AnswerPage", "");
        } else {
          this.nzSimple = false;
        }
      } else {
        if (submitData) {
          if (this.isUpdateing) {
            this.ActiveUpdataship(submitData, "AnswerPage", "");
          } else {
            this.UpdateAnswer();
          }
        } else {
          this.nzSimple = false;
        }
      }
    } else {
      // this.msg.error("请先导入题本");
      this.customMsg.open("error", "请先导入题本");
    }
  }

  /**
   * 作答说明-更新
   */
  UpdateAnswer() {
    let params = {
      answerDescription: this.prismaData.answerDescription,
      questionnaireId: this.questionnaireId,
    };
    this.http.updateAnswer(params).subscribe((res) => {
      if (res.result.code == 0) {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isNzOkLoading = false;
        this.confirmRelation("ANSWER_DESCRIPTION", true);
      } else {
        this.nzSimple = false;
      }
    });
  }

  /**
   * 作答说明-关闭
   */
  cancelModalDesc() {
    this.visibleDesc = false;
  }

  /**
   * 作答说明-国际化切换
   * @param e
   */
  onSelectI18n(e) {
    this.lan = e;
  }

  /**
   * 作答说明-恢复默认or更新显示
   */
  setDescDefault(recoverFlag) {
    // this.http.getDimensions(this.standardQuestionnaireId).subscribe((res) => {
    //   let tmpDesc = res.data.answerDescription;
    //   this.prismaData.answerDescription.zh_CN = tmpDesc.zh_CN;
    //   this.prismaData.answerDescription.en_US = tmpDesc.en_US;
    // });
    const params = {
      projectId: this.projectId,
      standardQuestionnaireId: this.standardQuestionnaireId,
      reportType: this.factorTable.surveyStandardQuestionnaire.reportType,
      questionnaireId: this.questionnaireId,
      recoverFlag,
    };
    const { codes: dimensionCodes, isSelect } = this.getDimensionCodes();
    if (isSelect) {
      params["dimensionCodes"] = dimensionCodes;
    }
    this.http.getQuestionnaireAnswerDescription(params).subscribe((res) => {
      if (res.result.code === 0) {
        let tmpDesc = res.data;
        // this.prismaData.answerDescription.zh_CN = tmpDesc.zh_CN;
        // this.prismaData.answerDescription.en_US = tmpDesc.en_US;
        this.prismaData.answerDescription = tmpDesc;
      }
    });
  }

  /**
   * 获取二级维度code
   */
  getDimensionCodes() {
    const codes = [];
    let isSelect = false;
    this.factorTable.detailedScoreConfigs.forEach((res) => {
      isSelect = res.isSelect;
      if (res.isSelect) {
        res.detailedScoreChildDimensions.forEach((val) => {
          if (val.isSelected) {
            codes.push(val.code);
          }
        });
      }
    });
    return { codes, isSelect };
  }

  /**
   * 数据提交-数据校验
   */
  synthesisTime() {
    if (!this.prismaData.name.zh_CN) {
      // this.msg.error("请填写活动名称");
      this.customMsg.open("error", "请填写活动名称");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      this.nzSimple = false;
      return;
    }

    if (this.prismaData.name.zh_CN.length > 50) {
      // this.msg.error("活动名称不能超过50个字符！");
      this.customMsg.open("error", "活动名称不能超过50个字符！");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      return;
    }

    if (
      this.prismaData.name.zh_CN.indexOf("/") != -1 ||
      this.prismaData.name.zh_CN.indexOf("\\") != -1
    ) {
      // this.msg.error("活动名称包含非法字符'/''\\'！");
      this.customMsg.open("error", "活动名称包含非法字符'/''\\'！");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      return;
    }

    if (!this.dateRange) {
      // this.msg.error("请选择活动周期");
      this.customMsg.open("error", "请选择活动周期");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      return;
    }
    if (this.dateRange.length === 0) {
      // this.msg.error("请选择活动周期");
      this.customMsg.open("error", "请选择活动周期");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isSpinning = false;
      return;
    }
    if (this.prismaData.optionalLanguages.length == 0) {
      // this.msg.error("填答时可选语言至少选一个！");
      this.customMsg.open("error", "填答时可选语言至少选一个！");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      return;
    }
    if (this.prismaData.isEnableEndPage) {
      if (this.prismaData.endPage.zh_CN == "") {
        // this.msg.error("结束页内容不能为空！");
        this.customMsg.open("error", "结束页内容不能为空！");
        this.isNzOkLoading = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isSpinning = false;
        return;
      }
    }
    if (this.prismaData.isEnableWelcomePage) {
      if (this.prismaData.welcomePage.zh_CN == "") {
        // this.msg.error("欢迎页内容不能为空！");
        this.customMsg.open("error", "欢迎页内容不能为空！");
        this.isNzOkLoading = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isSpinning = false;
        return;
      }
    }

    this.prismaData.startTime = `${this.formatDate(
      this.dateRange[0],
      "date"
    )}T${this.formatDate(this.dateRange[0], "time")}`;
    this.prismaData.endTime = `${this.formatDate(
      this.dateRange[1],
      "date"
    )}T${this.formatDate(this.dateRange[1], "time")}`;

    const submitData = JSON.parse(JSON.stringify(this.prismaData));
    submitData.projectName = submitData.name;

    return submitData;
  }

  /**
   * 活动-创建360活动
   * @param data 活动数据
   * @param type 保存类型
   * BookPage题本
   * ShipPage评价关系
   * AnswerPage作答说明
   * FactorPage修改人口标签
   * JobPage对标岗位
   * SeniorPage高级设置
   * @param url 保存后需要跳转的地址
   */
  create360project(data, type, url) {
    this.isSpinning = true;
    this.isNzRELoading = true;
    this.isNzPreLoading = true;
    this.isNzOkLoading = true;
    this.ismodalclosed = true;
    this.prismaApi.create(data).subscribe((res) => {
      if (res.result.code == 0) {
        this.projectId = res.data[0].projectId;
        this.questionnaireId = res.data[0].id;
        this.reportTemplateId = res.data[0].reportTemplateId;
        this.standardQuestionnaireId = res.data[0].standardQuestionnaireId;
        if (
          type == "BookPage" ||
          type == "ShipPage" ||
          type == "AnswerPage" ||
          type == "FactorPage" ||
          type == "JobPage" ||
          type == "SeniorPage"
        ) {
          //需要跳转到其他页面，如：定制题本、评价关系等
          this.ismodalclosed = false;
          if (type == "BookPage" || type == "ShipPage") {
            if (type == "BookPage") {
              this.router.navigate([url], {
                queryParams: {
                  questionnaireId: this.questionnaireId,
                  projectId: this.projectId,
                  standardQuestionnaireId: this.standardQuestionnaireId,
                  type: this.projectType,
                  edittype: this.typeedit,
                  standardReportType: this.standardReportType,
                },
              });
            } else {
              this.router.navigate([url], {
                queryParams: {
                  projectId: this.projectId,
                  step: 1,
                  type: "create",
                  projectType: this.projectType,
                  action: "",
                  standardQuestionnaireId: this.standardQuestionnaireId,
                  standardReportType: this.standardReportType,
                  isCustomRoleWeight: this.prismaData.isCustomRoleWeight,
                },
              });
            }
            this.isSpinning = false;
            this.isNzRELoading = false;
            this.isNzPreLoading = false;
            this.isNzOkLoading = false;
          } else {
            if (type == "AnswerPage") {
              this.UpdateAnswer();
            }
            if (type == "FactorPage" || type == "SeniorPage") {
              this.nzSimple = false;
              this.isSpinning = false;
              this.isNzRELoading = false;
              this.isNzPreLoading = false;
              this.isNzOkLoading = false;
              this.factorNameShow = false;
              this.getProjectSetting();
            }
            if (type == "JobPage") {
              this.getCustomList();
            }
          }
        } else {
          //保存活动，回到活动列表页面
          this.msg.success("创建成功！");
          this.router.navigateByUrl(url);
        }
      } else {
        this.ismodalclosed = false;
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isNzOkLoading = false;
        this.factorNameShow = false;
      }
    });
  }

  /**
   * 活动-活动数据更新
   * @param submitData 活动数据
   * @param type 保存类型
   * BookPage题本
   * ShipPage评价关系
   * AnswerPage作答说明
   * FactorPage修改人口标签
   * JobPage对标岗位
   * SeniorPage高级设置
   * SavePage保存/预发布 RelasePage发布 （主要看submitData.status的状态）
   * MorePage题本分发/关联设置
   * @param url 保存后需要跳转的地址
   */
  ActiveUpdataship(submitData, type, url) {
    if (this.isUpdateing) {
      //是否已发布
      submitData.projectId = this.projectId;
      this.prismaApi.updateProjectSetting(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          if (
            type == "AnswerPage" ||
            type == "MorePage" ||
            type == "FactorPage" ||
            type == "SeniorPage"
          ) {
            if (type == "AnswerPage") {
              this.UpdateAnswer();
            }
            if (type == "MorePage") {
              this.SaveRoleDimensions();
            }
            if (type == "FactorPage" || type == "SeniorPage") {
              this.nzSimple = false;
              this.isSpinning = false;
              this.isNzRELoading = false;
              this.isNzPreLoading = false;
              this.isNzOkLoading = false;
              this.factorNameShow = false;
              this.getProjectSetting();
            }
          } else {
            //更新活动，回到活动列表页面
            this.msg.success("保存成功！");
            this.router.navigateByUrl(url);
          }
        } else {
          this.nzSimple = false;
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzPreLoading = false;
          this.isNzOkLoading = false;
          this.factorNameShow = false;
        }
      });
    } else {
      submitData.id = this.projectId;
      this.prismaApi.updateAnnouncedProject(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          if (
            type == "MorePage" ||
            type == "FactorPage" ||
            type == "SeniorPage"
          ) {
            if (type == "MorePage") {
              this.SaveRoleDimensions();
            }
            if (type == "FactorPage" || type == "SeniorPage") {
              this.nzSimple = false;
              this.isSpinning = false;
              this.isNzRELoading = false;
              this.isNzPreLoading = false;
              this.isNzOkLoading = false;
              this.factorNameShow = false;
              this.getProjectSetting();
            }
          } else {
            //更新活动，回到活动列表页面
            this.msg.success("保存成功！");
            this.router.navigateByUrl(url);
          }
        } else {
          this.nzSimple = false;
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzPreLoading = false;
          this.isNzOkLoading = false;
          this.factorNameShow = false;
        }
      });
    }
  }

  /**
   * 活动-未发布的情况下调用的更新活动
   * @param submitData 活动数据
   * @param type 保存类型
   * @param url 保存后需要跳转的地址
   */
  ActiveUpdata(submitData, type, url) {
    this.isSpinning = true;
    submitData.id = this.projectId;
    if (type == "BookPage" || type == "ShipPage") {
      if (type == "BookPage") {
        this.bookUpdate(submitData, url);
      } else {
        this.shipUpdate(submitData, url);
      }
    } else {
      this.prismaApi.updateAnnouncedProject(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          if (type == "Association") {
            this.isSpinning = false;
            this.getProjectSetting();
            this.topicDistribution360.openModal();
          } else {
            this.msg.success("保存成功！");
            this.router.navigateByUrl(url);
          }
        } else {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzPreLoading = false;
          this.isNzOkLoading = false;
        }
      });
    }
    // this.UpdateProject(submitData,type, url)
  }

  /**
   * 活动-保存活动
   */
  submitSave() {
    let submitData = this.synthesisTime();

    sessionStorage.setItem("savefactors", null);
    if (!this.projectId) {
      if (submitData) {
        this.create360project(submitData, "SavePage", "/project-manage/home");
      }
    } else {
      this.isSpinning = true;
      this.isNzRELoading = true;
      this.isNzPreLoading = true;
      this.isNzOkLoading = true;
      if (submitData) {
        this.ActiveUpdataship(submitData, "SavePage", "/project-manage/home");
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isNzOkLoading = false;
      }
    }
  }

  /**
   * 活动-预发布
   */
  submitPreviewSave() {
    const submitData = this.synthesisTime();
    sessionStorage.setItem("savefactors", null);

    if (!submitData.relationPermissions) {
      // this.msg.error("请先完成关联任务");
      this.customMsg.open("error", "请先完成关联任务");
      return;
    }

    const booquestion = submitData.relationPermissions.filter((res) => {
      return res.type == "QUESTION_BOOK";
    });

    const relationship = submitData.relationPermissions.filter((res) => {
      return res.type == "EVALUATION_RELATIONSHIP";
    });

    const tipjob = submitData.relationPermissions.filter((res) => {
      return res.type == "JOB";
    });

    const relationrole = submitData.relationPermissions.filter((res) => {
      return res.type == "ROLE_DIMENSION";
    });

    const answer = submitData.relationPermissions.filter((res) => {
      return res.type == "ANSWER_DESCRIPTION";
    });

    if (tipjob.length != 0) {
      if (!tipjob[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--对标岗位");
        this.customMsg.open("error", "请先完成关联任务--对标岗位");
        return;
      }
    }
    if (booquestion.length != 0) {
      if (!booquestion[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--题本");
        this.customMsg.open("error", "请先完成关联任务--题本");
        return;
      }
    }
    if (relationship.length != 0) {
      if (!relationship[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--评价关系");
        this.customMsg.open("error", "请先完成关联任务--评价关系");
        return;
      }
    }
    if (relationrole.length != 0 && this.prismaData.isEnableRoleDimension) {
      if (!relationrole[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--多维问卷");
        this.customMsg.open("error", "请先完成关联任务--多维问卷");
        return;
      }
    }

    if (answer.length != 0) {
      if (!answer[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--作答说明");
        this.customMsg.open("error", "请先完成关联任务--作答说明");
        return;
      }
    }
    this.isNzPreLoading = true;
    if (!this.projectId) {
      if (submitData) {
        submitData.status = "PREVIEW"; // (进行中) ----发布
        this.create360project(submitData, "SavePage", "/project-manage/home");
        // this.ActiveCreate(submitData)
      }
    } else {
      if (submitData) {
        this.isSpinning = true;
        this.isNzRELoading = true;
        this.isNzPreLoading = true;
        this.isNzOkLoading = true;
        if (submitData) {
          submitData.status = "PREVIEW";
          this.ActiveUpdataship(submitData, "SavePage", "/project-manage/home");
        } else {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzPreLoading = false;
          this.isNzOkLoading = false;
        }

        // this.ActiveUpdata(submitData)// 修改活动
      }
    }
  }

  /**
   * 发布活动
   */
  submitRelase() {
    const submitData = this.synthesisTime();
    sessionStorage.setItem("savefactors", null);

    // return

    if (!submitData.relationPermissions) {
      // this.msg.error("请先完成关联任务");
      this.customMsg.open("error", "请先完成关联任务");
      return;
    }

    const booquestion = submitData.relationPermissions.filter((res) => {
      return res.type == "QUESTION_BOOK";
    });

    const relationship = submitData.relationPermissions.filter((res) => {
      return res.type == "EVALUATION_RELATIONSHIP";
    });

    const tipjob = submitData.relationPermissions.filter((res) => {
      return res.type == "JOB";
    });

    const relationrole = submitData.relationPermissions.filter((res) => {
      return res.type == "ROLE_DIMENSION";
    });

    const answer = submitData.relationPermissions.filter((res) => {
      return res.type == "ANSWER_DESCRIPTION";
    });

    if (tipjob.length != 0) {
      if (!tipjob[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--对标岗位");
        this.customMsg.open("error", "请先完成关联任务--对标岗位");
        return;
      }
    }
    if (booquestion.length != 0) {
      if (!booquestion[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--题本");
        this.customMsg.open("error", "请先完成关联任务--题本");
        return;
      }
    }
    if (relationship.length != 0) {
      if (!relationship[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--评价关系");
        this.customMsg.open("error", "请先完成关联任务--评价关系");
        return;
      }
    }
    if (relationrole.length != 0 && this.prismaData.isEnableRoleDimension) {
      if (!relationrole[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--多维问卷");
        this.customMsg.open("error", "请先完成关联任务--多维问卷");
        return;
      }
    }

    if (answer.length != 0) {
      if (!answer[0].isConfirmed) {
        // this.msg.error("请先完成关联任务--作答说明");
        this.customMsg.open("error", "请先完成关联任务--作答说明");
        return;
      }
    }

    if (!this.projectId) {
      this.isNzRELoading = true;
      if (submitData) {
        submitData.status = "ANSWERING"; // (进行中) ----发布
        this.create360project(submitData, "RelasePage", "/project-manage/home");
        // this.ActiveCreate(submitData)
      }
    } else {
      if (submitData) {
        if (this.projectType == "PREVIEW") {
          const that = this;
          this.confirmModal = this.modal.create({
            nzTitle: null,
            nzContent: ModalContentComponent,
            nzFooter: null,
            nzComponentParams: {
              father: that,
              submitData: submitData,
            },
            nzClosable: false,
            nzMaskClosable: false,
          });
        } else {
          this.isSpinning = true;
          this.isNzRELoading = true;
          this.isNzPreLoading = true;
          this.isNzOkLoading = true;
          submitData.status = "ANSWERING";
          this.ActiveUpdataship(
            submitData,
            "RelasePage",
            "/project-manage/home"
          );
        }
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isNzOkLoading = false;
      }
    }
  }

  /**
   * 预览活动-（暂未使用）
   */
  getpreview() {
    if (this.projectId) {
      const submitData = this.synthesisTime();
      const booquestion = submitData.relationPermissions.filter((res) => {
        return res.type == "QUESTION_BOOK";
      });

      const relationship = submitData.relationPermissions.filter((res) => {
        return res.type == "EVALUATION_RELATIONSHIP";
      });

      const tipjob = submitData.relationPermissions.filter((res) => {
        return res.type == "JOB";
      });

      const relationrole = submitData.relationPermissions.filter((res) => {
        return res.type == "ROLE_DIMENSION";
      });

      const answer = submitData.relationPermissions.filter((res) => {
        return res.type == "ANSWER_DESCRIPTION";
      });

      if (tipjob.length != 0) {
        if (!tipjob[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--对标岗位");
          this.customMsg.open("error", "请先完成关联任务--对标岗位");
          return;
        }
      }
      if (booquestion.length != 0) {
        if (!booquestion[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--题本");
          this.customMsg.open("error", "请先完成关联任务--题本");
          return;
        }
      }
      if (relationship.length != 0) {
        if (!relationship[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--评价关系");
          this.customMsg.open("error", "请先完成关联任务--评价关系");
          return;
        }
      }
      if (relationrole.length != 0 && this.prismaData.isEnableRoleDimension) {
        if (!relationrole[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--多维问卷");
          this.customMsg.open("error", "请先完成关联任务--多维问卷");
          return;
        }
      }

      if (answer.length != 0) {
        if (!answer[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--作答说明");
          this.customMsg.open("error", "请先完成关联任务--作答说明");
          return;
        }
      }
      if (submitData) {
        this.preview(submitData);
      }
    } else {
      // this.msg.warning("请先保存活动！");
      this.customMsg.open("warning", "请先保存活动");
    }
  }

  /**
   * 预览填答-(暂未使用)
   * @param submitData
   */
  preview(submitData) {
    this.isSpinning = true;
    submitData.id = this.projectId;
    this.prismaApi.updateAnnouncedProject(submitData).subscribe((res) => {
      if (res.result.code == 0) {
        this.http.PreviewUrl(this.projectId).subscribe((res) => {
          if (res.result.code == 0) {
            window.open(res.data, "_blank");
          }
        });
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isNzOkLoading = false;
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isNzOkLoading = false;
      }
    });
  }

  /**
   * 关联任务-确认
   * @param type 任务类型
   * @param isConfirmed  true确认 false取消
   */
  confirmRelation(type, isConfirmed, showMsg = true) {
    let data = {
      projectId: this.projectId,
      questionnaireId: this.questionnaireId,
      type: type,
      isConfirmed: isConfirmed,
    };
    this.prismaApi.confirmRelation(data).subscribe((item) => {
      if (item.result.code == 0) {
        if (showMsg) {
          this.msg.success("保存成功！");
        }
        this.visibleDesc = false;
        this.nzSimple = false;
        this.Association = false;

        if (type == "ANSWER_DESCRIPTION" || type == "JOB") {
          if (type == "ANSWER_DESCRIPTION") {
            this.descTip[0].isConfirmed = true;
          }
          if (type == "JOB") {
            this.TipJobtip[0].isConfirmed = true;
          }
        } else {
          this.getProjectSetting();
        }
      }
    });
  }

  /**
   * 定制题本跳转
   * @param submitData 活动数据
   * @param url 保存后需要跳转的地址
   */
  bookUpdate(submitData, url) {
    let queryParams = {};
    if (this.isUpdateing) {
      //进行中的活动修改
      queryParams = {
        questionnaireId: this.questionnaireId,
        standardQuestionnaireId: this.standardQuestionnaireId,
        projectId: this.projectId,
        type: this.projectType,
        edittype: this.typeedit,
        listChecked: "checked",
        standardReportType: this.standardReportType,
      };

      submitData.projectId = this.projectId;
      this.prismaApi.updateProjectSetting(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          this.router.navigate([url], {
            queryParams: queryParams,
          });
        } else {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzPreLoading = false;
          this.isNzOkLoading = false;
        }
      });
    } else {
      //保存的活动修改
      queryParams = {
        questionnaireId: this.questionnaireId,
        standardQuestionnaireId: this.standardQuestionnaireId,
        projectId: this.projectId,
        type: this.projectType,
        edittype: this.typeedit,
        standardReportType: this.standardReportType,
      };
      this.prismaApi.updateAnnouncedProject(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          this.router.navigate([url], {
            queryParams: queryParams,
          });
        } else {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzPreLoading = false;
          this.isNzOkLoading = false;
        }
      });
    }
  }

  /**
   * 评价关系跳转
   * @param submitData 活动数据
   * @param url 保存后需要跳转的地址
   */
  shipUpdate(submitData, url) {
    let queryParams = {};
    if (this.relationship[0].isConfirmed) {
      //确认过的评价关系
      if (this.isUpdateing) {
        //进行中的活动修改
        queryParams = {
          projectId: this.projectId,
          step: 1,
          type: "create",
          projectType: this.projectType,
          action: "edit",
          listChecked: "checked",
          standardQuestionnaireId: this.standardQuestionnaireId,
          standardReportType: this.standardReportType,
          isCustomRoleWeight: this.prismaData.isCustomRoleWeight,
        };
        submitData.projectId = this.projectId;
        this.prismaApi.updateProjectSetting(submitData).subscribe((res) => {
          if (res.result.code == 0) {
            this.router.navigate([url], {
              queryParams: queryParams,
            });
          } else {
            this.isSpinning = false;
            this.isNzRELoading = false;
            this.isNzPreLoading = false;
            this.isNzOkLoading = false;
          }
        });
      } else {
        // this.msg.warning("请在活动管理列表页面修改或查看！");
        this.customMsg.open("warning", "请在活动管理列表页面修改或查看");
        this.isSpinning = false;
      }
    } else {
      //没确认过的
      queryParams = {
        projectId: this.projectId,
        step: 1,
        type: "create",
        projectType: this.projectType,
        action: "",
        standardQuestionnaireId: this.standardQuestionnaireId,
        standardReportType: this.standardReportType,
        isCustomRoleWeight: this.prismaData.isCustomRoleWeight,
      };
      this.prismaApi.updateAnnouncedProject(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          this.router.navigate([url], {
            queryParams: queryParams,
          });
        } else {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzPreLoading = false;
          this.isNzOkLoading = false;
        }
      });
    }
  }

  /**
   * loading关闭（暂未使用）
   */
  hideProcess() {
    this.isNzOkLoading = false;
    this.isNzRELoading = false;
    this.isNzPreLoading = false;
    this.isSpinning = false;
  }

  /**
   * 岗位/层级-过滤出关联的前测code
   * @param e this.PretestValue,
   * @param type  'rester' || undefind
   */
  getmappings(e, type?) {
    let dimensionList = this.PretestList.filter((item) => {
      return item.projectId == e;
    });
    if (dimensionList.length != 0)
      this.selecteddimensionList = dimensionList[0].dimensionList;
    let mappinglist = {};
    this.http.getCaMapping().subscribe((item) => {
      mappinglist = item.data;
      this.codemaplist = this.selecteddimensionList.map((res) => {
        return mappinglist[res.code];
      });
      if (type == "rester") {
        this.mapCodeList();
      }
    });
  }

  /**
   * 岗位/层级-选中前侧数据
   * @param e
   */
  pretestLog(e) {
    if (e) {
      this.PretestValueShow = false;
      this.checkedvalue = false;
      this.radioValue = null;
      this.radioCode = null;
      this.getmappings(e);
    } else {
      this.selecteddimensionList = [];
    }
  }

  /**
   * 岗位/层级-无前测模型，立即建模
   */
  getValueShow() {
    this.PretestValueShow = true;
    this.PretestValue = null;
    this.selecteddimensionList = [];
  }

  /**
   *  岗位/层级-打开对标岗位弹窗
   */
  getModalJob() {
    let submitData = this.synthesisTime();
    if (submitData) {
      this.getSelectJob();
      this.visibleJob = true;
    }
  }

  /**
   * 岗位/层级-前侧数据
   */
  get360PretestProject() {
    this.http.getCaProject().subscribe((res) => {
      this.PretestList = res.data;
      this.getmappings(this.PretestValue, "rester");
    });
  } //

  /**
   * 岗位/层级-获取数据
   */
  getSelectJob() {
    if (this.reportTemplateId) {
      this.http.querylistTipjobs(this.reportTemplateId).subscribe((res) => {
        if (res.result.code == 0) {
          if (res.data) {
            this.PretestValue = res.data.relationProjectId;
            this.radioValue = res.data.standardReportTemplateJobId;
            this.radioCode = res.data.dimensionLevel;
            this.checkedvalue = res.data.isSelectedModel;
            this.createtipid = res.data.id;

            if (res.data.relationProjectId) {
              this.PretestValueShow = false;
            } else {
              if (this.standardReportType == "_360_TRAIN") {
                this.PretestValueShow = true;
              }
              this.radiolist = this.Tipjobslist.filter((res) => {
                return this.radioValue == res.id;
              });
              this.radioCodelist = this.factorTable.dimensionLevelEnumsZHnew.filter(
                (item) => {
                  return this.radioCode == item.code;
                }
              );
            }
            this.jobcodeshow = true;
            this.get360PretestProject();
          } else {
            this.get360PretestProject();
          }
        }
      });
    } else {
      this.get360PretestProject();
    }
  }

  /**
   * 岗位/层级-自选模型
   * @param e true/false
   */
  ngModelSelect(e) {
    if (e) {
      this.radioValue = null;
      this.radioCode = null;
    }
  }

  /**
   * 岗位/层级-选择岗位
   */
  nzOnJobsType() {
    this.jobcodeshow = false;
    this.checkedvalue = false;
  }

  /**
   * 岗位/层级-选择层级
   */
  nzOnCodesType() {
    this.jobcodeshow = false;
    this.checkedvalue = false;
  }

  /**
   * 选择层级（暂位使用）
   * @param i
   * @param name
   */
  getchoose(i, name) {
    this.chooseIndex = i;
  }

  /**
   * 岗位/层级-关闭
   */
  handleJobClose() {
    if (!this.ismodalclosed) {
      this.jobcodeshow = true;
      this.visibleJob = false;
    }
  }

  /**
   * 岗位/层级-确认
   */
  handleJobOk() {
    if (this.PretestValueShow) {
      if (!this.radioValue && !this.radioCode && !this.checkedvalue) {
        // this.msg.warning("请选择岗位和层级或者自选模型！");
        this.customMsg.open("warning", "请选择岗位和层级或者自选模型");
        return;
      }
    } else {
      if (!this.PretestValue) {
        // this.msg.warning("请选择前测岗位模型！");
        this.customMsg.open("warning", "请选择前测岗位模型");
        return;
      }
    }
    const submitData = this.synthesisTime();
    if (this.projectId) {
      if (this.projectType == "ANSWERING" || this.projectType == "OVER") {
        this.visibleJob = false;
        this.ismodalclosed = false;
      } else {
        this.isSpinning = true;
        this.isNzRELoading = true;
        this.isNzOkLoading = true;
        this.isNzPreLoading = true;
        if (submitData) {
          if (this.isUpdateing) {
            this.ActiveUpdataship(submitData, "JobPage", "");
          } else {
            this.getCustomList();
          }
        } else {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPreLoading = false;
        }
      }
    } else {
      if (submitData) {
        this.create360project(submitData, "JobPage", "");
      }
    }
  }

  /**
   * 获取对标岗位信息
   */
  getCustomList() {
    this.radioCodelist = this.factorTable.dimensionLevelEnumsZHnew.filter(
      (item) => {
        return this.radioCode == item.code;
      }
    );
    this.radiolist = this.Tipjobslist.filter((res) => {
      return this.radioValue == res.id;
    });

    this.customized(this.radiolist);
  }

  /**
   * 确定对标岗位
   * @param list  this.radiolist
   */
  customized(list) {
    // this.http.querylistTipjobs(this.reportTemplateId).subscribe((res) => {
    //   if(res.data) {
    //     this.createtipid = res.data.id
    //   }
    // })
    let data;
    data = {
      name: list.length != 0 ? list[0].name : null,
      projectId: this.projectId,
      standardQuestionnaireId: this.standardQuestionnaireId,
      standardReportTemplateJobId: list.length != 0 ? list[0].id : null,
      dimensionLevel: this.radioCode,
      isSelectedModel: this.checkedvalue,
      relationProjectId: !this.PretestValueShow ? this.PretestValue : null,
    };
    if (this.createtipid) {
      data.id = this.createtipid;
    }
    this.prismaData.isSpinning = true;
    this.http.createlistTipjobs(data).subscribe((res) => {
      if (res.result.code == 0) {
        this.isSpinning = false;
        this.visibleJob = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
        this.jobcodeshow = true;
        this.mapCodeList();
        this.confirmRelation("JOB", true);
      }
    });
  }

  // 多语言-默认为中英文-新增情况下题本及评价关系需要
  setLanOptions() {
    if (!this.routerInfo.snapshot.queryParams.projectId) {
      sessionStorage.setItem(
        "projectLanguages",
        JSON.stringify(["zh_CN", "en_US"])
      );
      sessionStorage.setItem("language", "zh_CN");
      this.i18n = [{ name: "中文", value: "zh_CN" }];
      return;
    }
  }

  //题本分发/关联设置--获取关联关系
  onAssociation() {
    if (this.relationship[0].isConfirmed && this.question_book[0].isConfirmed) {
      // 新题本分发
      // 保存过的人口标签
      const beforeFactorDtos = this.factorTable.projectAnalysisFactorDtos
        .map((val) => val.id)
        .sort()
        .join();
      const currentFactorDtos = this.prismaData.analysisFactorDto
        .filter((val) => val.isChecked)
        .map((val) => val.id)
        .sort()
        .join();
      // 维度
      // const beforeDimensionsArr = [];
      // this.factorTableCache.detailedScoreConfigs.forEach((val) => {
      //   if (val.isSelect) {
      //     val.detailedScoreChildDimensions.forEach((item) => {
      //       if (item.isSelected) {
      //         beforeDimensionsArr.push(item.code);
      //       }
      //     });
      //   }
      // });
      // const beforeDimensions = beforeDimensionsArr.sort().join();
      // const currentDimensions = this.prismaData.projectReportDimensions
      //   .map((val) => val.code)
      //   .sort()
      //   .join();
      if (
        beforeFactorDtos === currentFactorDtos
      ) {
        this.topicDistribution360.openModal();
      } else {
        let submitData = this.synthesisTime();
        if (submitData) {
          this.ActiveUpdata(submitData, "Association", "");
        }
      }
      return;

      this.http.RoleAndDimensionInfo(this.projectId).subscribe((res) => {
        this.Associationlist = res.data;
        this.Associationlist.NewcustomDimensions = [];
        this.Associationlist.dimensions.forEach((item) => {
          this.Associationlist.NewcustomDimensions.push({
            name: item.dimensionName,
            code: item.dimensionCode,
            checked: false,
          });
        });
        this.Associationlist.surveyRoles.forEach((item) => {
          item.checked = false;
          item.Dimensions = [];
          if (item.questionnaireRoleDimensionMappings) {
            item.questionnaireRoleDimensionMappings.forEach((val) => {
              item.Dimensions.push({
                code: val.dimensionCode,
                name: val.dimensionName,
              });
            });
          }
        });
        this.Association = true;
      });
    } else {
      // this.msg.warning("请先将题本与评价关系确认完成！");
      this.customMsg.open("warning", "请先将题本与评价关系确认完成");
    }
  }

  /**
   * 题本分发/关联设置-清空关联
   */
  associationClear() {
    this.Associationlist.NewcustomDimensions.forEach((res) => {
      res.checked = false;
    });
    this.Associationlist.surveyRoles.forEach((res) => {
      res.checked = false;
    });
  }

  /**
   * 题本分发/关联设置-关联关系
   */
  associationName() {
    this.Associationlist.surveyRoles.forEach((item) => {
      if (item.checked) {
        this.Associationlist.NewcustomDimensions.forEach((val) => {
          if (val.checked) {
            item.Dimensions.push({
              code: val.code,
              name: val.name,
            });
            item.Dimensions = this.removalselected(item.Dimensions);
          }
        });
      }
    });

    this.Associationlist.NewcustomDimensions.forEach((res) => {
      res.checked = false;
    });
    this.Associationlist.surveyRoles.forEach((res) => {
      res.checked = false;
    });
  }

  /**
   * 题本分发/关联设置-多选选中
   * @param arr
   * @returns
   */
  removalselected(arr) {
    let result = [];
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      if (!obj[arr[i].code]) {
        result.push(arr[i]);
        obj[arr[i].code] = true;
      }
    }
    return result;
  }

  //题本分发/关联设置--删除关联-具体维度
  onCloseCard(i, j) {
    this.Associationlist.surveyRoles[i].Dimensions.splice(j, 1);
  }

  //题本分发/关联设置--删除关联-所有角色
  onCloseCardAll(i) {
    this.Associationlist.surveyRoles[i].Dimensions = [];
  }

  //题本分发/关联设置--关闭弹窗
  AssociationCancel() {
    this.Association = false;
  }

  //题本分发/关联设置-确认保存
  AssociationOk() {
    if (this.isdisabled) {
      this.Association = false;
    } else {
      let submitData = this.synthesisTime();
      if (submitData) {
        this.ActiveUpdataship(submitData, "MorePage", "");
      }
    }
  }

  //题本分发/关联设置--关联关系上传
  SaveRoleDimensions() {
    let parmas = {
      projectId: this.projectId,
      roleDimensions: [],
    };
    this.Associationlist.surveyRoles.forEach((res) => {
      if (res.Dimensions.length != 0) {
        res.Dimensions.forEach((val) => {
          parmas.roleDimensions.push({
            roleId: res.id,
            dimensionCode: val.code,
          });
        });
      }
    });

    this.http.SaveRoleDimensions(parmas).subscribe((item) => {
      if (item.result.code == 0) {
        this.confirmRelation("ROLE_DIMENSION", true);
      }
    });
  }

  /**
   *
   *  @author: Sid Wang
   *  @todo: 题本分发-关联设置-上传
   *  @Date: 2023/11/01
   *  @code: #7775
   *  @param item
   */
  onDispenseImport = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadAssociationExcel(formData, item);
  };

  /**
   * uploadExcel 题本分发-关联设置-上传配置
   */
  /**
   *
   *  @author: Sid Wang
   *  @todo: uploadAssociationExcel 题本分发-关联设置-上传配置
   *  @Date: 2023/11/01
   *  @code: #7775
   *  @param item
   */
  uploadAssociationExcel(formData, item) {
    return this.http.importSelectQuestion(formData, this.projectId).subscribe(
      (res) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.msg.success("题本分发导入成功");
          // 刷新关联设置内容
          this.http.RoleAndDimensionInfo(this.projectId).subscribe((res) => {
            this.Associationlist = res.data;
            this.Associationlist.NewcustomDimensions = [];
            this.Associationlist.dimensions.forEach((item) => {
              this.Associationlist.NewcustomDimensions.push({
                name: item.dimensionName,
                code: item.dimensionCode,
                checked: false,
              });
            });
            this.Associationlist.surveyRoles.forEach((item) => {
              item.checked = false;
              item.Dimensions = [];
              if (item.questionnaireRoleDimensionMappings) {
                item.questionnaireRoleDimensionMappings.forEach((val) => {
                  item.Dimensions.push({
                    code: val.dimensionCode,
                    name: val.dimensionName,
                  });
                });
              }
            });
          });
          // if (this.isdisabled) {
          //   this.Association = false;
          // } else {
          // 确认-题本分发-关联任务
          // this.confirmRelation("ROLE_DIMENSION", true);
          // }
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }
  /**
   *
   *  @author: Sid Wang
   *  @todo: 题本分发-关联设置-导出
   *  @Date: 2023/11/01
   *  @code: #7775
   *  @param item
   */
  onDispenseDownLoad() {
    this.isDispenseDownLoadSpinning = true;
    this.http.exportSelectQuestion(this.projectId).subscribe((res) => {
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.isDispenseDownLoadSpinning = false;
    });
  }
  async getLanOptions() {
    const currentLansRes = await this.prismaApi.getLanguages().toPromise();
    const defaultCode = ["zh_CN", "en_US"];
    const projectLanguages =
      JSON.parse(sessionStorage.getItem("projectLanguages")) || defaultCode;
    this.i18n = currentLansRes.data
      .filter((val) => projectLanguages.includes(val.value))
      .sort((a, b) => {
        if (a.value === "zh_CN") return -1;
        if (b.value === "zh_CN") return 1;
        if (a.value === "en_US") return -1;
        if (b.value === "en_US") return 1;
        if (a.value === "jp") return -1;
        if (b.value === "jp") return 1;
        if (a.value === "ko") return -1;
        if (b.value === "ko") return 1;
        if (a.value === "cs_1") return -1;
        if (b.value === "cs_1") return 1;
        if (a.value === "cs_2") return -1;
        if (b.value === "cs_2") return 1;
        if (a.value === "cs_3") return -1;
        if (b.value === "cs_3") return 1;
        return 0;
      });
  }
  changeNameValue(name) {
    this.factorName = name;
  }

  //一键隐藏人口学标签
  hiddenAllcheck() {
    this.isShowAll = !this.isShowAll;
    this.prismaData.analysisFactorDto.forEach((res) => {
      if (res.isChecked && !res.isHidden) {
        res.isHidden = true;
      }
    });
  }

  //一键显示人口标签
  showAllCheck() {
    this.isShowAll = !this.isShowAll;
    this.prismaData.analysisFactorDto.forEach((res) => {
      if (res.isChecked && res.isHidden) {
        res.isHidden = false;
      }
    });
  }

  onConfirm360Distribution(event) {
    console.log("题本分发", event);
    if (event) {
      this.confirmRelation("ROLE_DIMENSION", true, false);
    }
  }
}
