import { Component, ViewChild, TemplateRef, AfterViewInit, Input } from '@angular/core';
import { MessageService } from './message-service.service';
@Component({
  selector: 'app-custom-message',
  template: `
    <ng-template #msgCustomTemplate>
      <app-msg-content  style="display: flex;" ></app-msg-content>
    </ng-template>
  `,
})
export class CustomMessageComponent implements AfterViewInit {
  @ViewChild('msgCustomTemplate', { static: true }) msgCustomTemplate!: TemplateRef<void>;

  constructor(private messageService: MessageService) { }

  ngAfterViewInit() {
    this.messageService.msgCustomTemplate = this.msgCustomTemplate;
  }
}

