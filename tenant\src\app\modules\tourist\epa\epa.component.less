@text-color: #17314C;
.content{width:100%;}
.s1{
    width:100%;background-color: #ffffff;
    &>div{margin: 0 auto;padding: 60px 0;display: flex;}
    .s1-l{flex:2;padding-right: 180px;}
    .s1-r{flex:1;}
    h5{font-size:30px;line-height:42px;margin-bottom:25px; span{font-size:12px;color: #409EFF; border-radius:14px;padding:2px 10px;margin-left:30px;background-color: rgba(64,158,255,0.1);}}
    p{font-size:14px;line-height: 24px;margin-bottom: 80px;}
    .btn{width:160px;height:38px;line-height: 38px; background:linear-gradient(90deg,rgba(38,208,241,1) 0%,rgba(64,158,255,1) 100%);box-shadow:0px 3px 8px 0px rgba(55,175,250,0.5);border-radius:19px;font-size:16px;font-weight:bold;color: #ffffff;text-align: center;}
}
.s2{
   width:100%;background-color: #F5F6FA;
    &>div{ margin: 0 auto;padding: 60px 0;}
    ul{display: flex;flex-direction: row;flex-wrap: wrap;justify-content: space-between;
        li{width: 33%;padding: 25px 70px;img{margin-right: 10px;}}
        h3{font-size:20px;font-weight: bold;}
    }
    h5{font-size:24px;font-weight: bold;text-align: center;margin: 40px 0 50px;}
}
.s3{
    width:100%;background-color: #ffffff;padding-top:40px;
     .s3-main{ margin: 0 auto;padding: 60px 0;display: flex;}
     h5{font-size:24px;font-weight: bold;text-align: center;padding: 60px 0 60px;background: url(../../../../assets/images/q.png) no-repeat center;}
     .s3-l{flex: 1;font-size:16px;
        li{position: relative;line-height: 96px;
            div{width: 180px;display: inline-block;}
            span{font-size:16px;color: #409EFF;background-color: #F1F8FF;border-radius:46px;padding: 10px 25px;}
            &:not(:last-child)::after{content: '';position: absolute;bottom: 0;left: 0; height: 1px;width: 60%;background: linear-gradient(to right,#ffffff, #ECECEC,#ffffff)}}
    }
     .s3-r{flex: 1;img{margin-top: 30px;}}
 }
 .s4{
    width:100%;background-color: #F5F6FA;padding-bottom:98px;
    &>div{margin: 0 auto;}
    h5{font-size:24px;font-weight: bold;text-align: center;padding: 76px 0 56px;}
    .des{width: 100%;padding-top: 39px;margin: 0 auto;}
    .card{ width: 400px;float: left;
        &:nth-child(1), &:nth-child(3){transform: scale(0.8);opacity: 0.5;}
        div{width: 297px;height: 350px;background-color: #ffffff;border-radius:8px; text-align: center;padding-top:56px;margin:0 auto;
        img{margin: 0 auto;}
        h5{font-size:20px;font-weight: bold;padding: 54px 0 27px;}}}
        .carousel{position: relative; display: inline-block;}
        #carousel{width: 200%;}
        .carousel span{position: absolute;bottom: 50%; left:-100px; display: inline-block;width: 50px;text-align: center;cursor: pointer;z-index: 90;color: #fff;}
        .carousel .next{right: -100px; left:auto;}
    }
 .s5{
    width:100%;background-color: #ffffff;
    .s5-main{ margin: 0 auto;padding: 60px 0;display: flex;}
    h5{font-size:24px;font-weight: bold;padding: 50px 0 40px;}
    .s5-r{flex: 1;font-size:16px;
        p{position: relative; font-size: 16px;line-height: 55px;padding-left:20px; &::before{position: absolute;top:20px;left: 0; content: '';display: block;width: 0;height: 0;border: 8px solid;border-color: transparent transparent transparent #409EFF;}}
    }
    .s5-l{width:60%; img{margin-top: 30px;}text-align: center;}
    .btn{ margin:20px 0;button{width:160px;height:38px;line-height: 38px; background:linear-gradient(90deg,rgba(38,208,241,1) 0%,rgba(64,158,255,1) 100%);box-shadow:0px 3px 8px 0px rgba(55,175,250,0.5);border-radius:19px;font-size:16px;font-weight:bold;color: #ffffff;}}
 }
