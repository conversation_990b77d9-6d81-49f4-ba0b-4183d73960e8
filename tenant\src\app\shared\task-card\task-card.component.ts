import { Component, EventEmitter, Input, OnInit, Output, } from '@angular/core';

@Component({
  selector: 'app-task-card',
  templateUrl: './task-card.component.html',
  styleUrls: ['./task-card.component.less']
})
export class TaskCardComponent implements OnInit {

  @Input() text: string = '标题';
  @Input() btnText: string = '按钮';
  @Input() isConfirmed: boolean = true;
  @Input() showConfirmed?: boolean = true;
  @Output() onClick = new EventEmitter<void>();
  constructor() { }
  
  ngOnInit() {}

  handClick(){
    this.onClick.emit()
  }
}
