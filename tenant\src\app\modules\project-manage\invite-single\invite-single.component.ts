import { Component, OnInit, Input } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd";
import { Validators, FormBuilder, FormGroup } from "@angular/forms";
import { validateRex } from "../validate-project";
import { areaEmail } from "../validate-textarea";
import { ProjectManageService } from "../../service/project-manage.service";
import { copy } from "clipboard";
import { MessageService } from "@src/shared/custom-message/message-service.service";

declare const document: any;
@Component({
  selector: "app-invite-single",
  templateUrl: "./invite-single.component.html",
  styleUrls: ["./invite-single.component.less"],
})
export class InviteSingleComponent implements OnInit {
  @Input() model: any;
  @Input() projectId: string;
  @Input() projectCode: string;
  @Input() projectName: string;
  @Input() questionnaireIds: any[];

  validateForm: FormGroup;
  tenantName: string = "";
  validationMessage = {
    email: {
      email: "不是有效邮箱地址",
      required: "邮箱地址能为空",
      // 'notdown': '用户名不能以下划线开头',
      // 'only': '用户名只能包含数字、字母、下划线'
    },
    title: {
      required: "请填写邮件标题",
    },
    content: {
      required: "请填写邮件内容",
    },
  };
  changeCode = true;
  changeName: string = "公共码";
  formErrors = {
    email: "",
    title: "",
    content: "",
  };

  constructor(
    private msg: NzMessageService,
    private projectManageService: ProjectManageService,
    private fb: FormBuilder,
    private customMsg: MessageService,
  ) {}

  ngOnInit(): void {
    this.buildForm();
    this.createQrcode("PUBLIC");
    // 获取sessionStorage中的用户信息
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
    this.tenantName = userInfo.data.name;
  }

  // 构建表单方法
  buildForm(): void {
    this.validateForm = this.fb.group({
      email: [
        null,
        [
          Validators.email,
          Validators.required,
          // validateRex('notdown', /^(?!_)/),
          // validateRex('only', /^[1-9a-zA-Z_]+$/)
        ],
      ],
      title: [null, [Validators.required]],
      content: [null, [Validators.required]],
    });

    // 每次表单数据发生变化的时候更新错误信息
    this.validateForm.valueChanges.subscribe((data) =>
      this.onValueChanged(data)
    );

    // 初始化错误信息
    this.onValueChanged();
  }

  // 每次数据发生改变时触发此方法
  onValueChanged(data?: any) {
    // 如果表单不存在则返回
    if (!this.validateForm) return;
    // 获取当前的表单
    const form = this.validateForm;

    // 遍历错误消息对象
    for (const field in this.formErrors) {
      // 清空当前的错误消息
      this.formErrors[field] = "";
      // 获取当前表单的控件
      const control = form.get(field);

      // 当前表单存在此空间控件 && 此控件没有被修改 && 此控件验证不通过
      if (control && control.dirty && !control.valid) {
        // 获取验证不通过的控件名，为了获取更详细的不通过信息
        const messages = this.validationMessage[field];
        // 遍历当前控件的错误对象，获取到验证不通过的属性
        for (const key in control.errors) {
          // 把所有验证不通过项的说明文字拼接成错误消息
          this.formErrors[field] += messages[key] + "<br>";
        }
      }
    }
  }

  createQrcode(type) {
    let json = {
      questionnaireIds: this.questionnaireIds,
    };
    this.projectManageService
      .generateQrCode(this.projectId, type, json)
      .subscribe((res) => {
        if (res.result.code == 0) {
          // update user list display
          this.model.qrCodeContent = res.data;
        } else {
          // this.msg.error(res.result.message);
          this.customMsg.open("error", res.result.message);
          this.model.qrCodeContent = undefined;
        }
      });
  }
  selectCode(type) {
    if (type == "public") {
      this.changeCode = true;
      this.changeName = "公共码";
      this.createQrcode("PUBLIC");
    } else {
      this.changeCode = false;
      this.changeName = "一人一码";
      this.createQrcode("PRIVATE");
    }
    //rng
  }
  doCopy() {
    const input = document.querySelector(".copytext");
    // input.select()
    // input.setSelectionRange(0, input.value.length);
    // document.execCommand('copy');
    copy(input.value);
    this.msg.success("复制成功");
  }

  downloadImg() {
    const img = document.querySelector(".qrcode img");
    const src = img.getAttribute("src");

    let aLink = document.createElement("a");
    let blob = this.base64ToBlob(src);
    let evt = document.createEvent("HTMLEvents");
    let imageName = ""; 
    let prefix = this.tenantName + "_"+this.projectCode +":"+this.projectName+"_";
    if (this.changeCode) {
      imageName = prefix + "公共邀请码【二维码图片】";
    } else {
      imageName = prefix + "个人邀请码【二维码图片】";
    }
    let timestamp = new Date().getTime();
    imageName = imageName + "_" + timestamp;
    aLink.download = imageName + ".png";
    aLink.href = URL.createObjectURL(blob);
    aLink.click();
  }

  base64ToBlob(code) {
    let parts = code.split(";base64,");
    let contentType = parts[0].split(":")[1];
    let raw = window.atob(parts[1]);
    let rawLength = raw.length;

    let uInt8Array = new Uint8Array(rawLength);

    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array], { type: contentType });
  }
}
