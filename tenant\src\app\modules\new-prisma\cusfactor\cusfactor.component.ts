import {
  Component,
  OnInit,
  Input,
  SimpleChanges,
  Output,
  EventEmitter,
} from "@angular/core";
import _ from "lodash";
import { UploadFile, UploadXHRArgs } from "ng-zorro-antd/upload";
import { NewPrismaService } from "../new-prisma.service";
import { NzModalRef, NzMessageService } from "ng-zorro-antd";
import { HttpEvent } from "@angular/common/http";
import { DragulaService } from "ng2-dragula";
import { Subscription } from "rxjs";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";
@Component({
  selector: "app-factors",
  templateUrl: "./cusfactor.component.html",
  styleUrls: ["./cusfactor.component.less"],
})
export class CusFactorComponent implements OnInit {
  @Input() shownumber;
  @Input() projectId;
  @Input() factorlist;
  @Input() addfactorshow;
  @Input() showAnalysisFactor;

  @Input() surveyType;
  @Input() reportType;
  @Input() changeNumber;

  @Output() closemodal = new EventEmitter<any>();
  @Output() cancelmodal = new EventEmitter<any>();
  @Output() loadDataMap = new EventEmitter<any>();
  @Output() getdefaultlist = new EventEmitter<any>();

  subs = new Subscription();

  isChildDrag: boolean = false;

  analysisFactorTitle;
  confirmvisible;
  Modalcheck = 0; //人口标签高亮
  IsAdd;
  isRelationLabel: boolean = false;
  relationLabelList = [];
  currentRelationLabelList = [];

  prismaData = {
    standardAnalysisFactorVOS: [],
    name_cn: "",
    name_eng: "",
    code: "",
    name: null,
  };

  checklist = [];
  permission = null;
  codelistlen = 0;
  optionReport = [
    {
      name: "RESEARCHERS_DISTRIBUTION",
      type: "调研人员分布(人数/占比)",
      checked: false,
    },
    {
      name: "POP_LABELS_CROSS_ANALYSIS",
      type: "人口标签交叉分析(四窗)",
      checked: false,
    },
    {
      name: "GROUP_ANALYSIS",
      type: "群体分析(详细列表)",
      checked: false,
    },
    {
      name: "KEY_POPULATION_ANALYSIS",
      type: "重点人群分析(定制报告专用)",
      checked: false,
    },
    {
      name: "POP_LABELS_CROSS_ANALYSIS_OVERVIEW",
      type: "双低人群概览(定制报告专用)",
      checked: false,
    },
    {
      name: "APPENDIX",
      type: "附录",
      checked: false,
    },
  ];
  lan: string = "zh_CN";
  i18n: any = [];
  nameObj: {};
  isLoadingOne = false;
  /**
   * 标签弹框定位是否定位在上方 isPositionTop
   *@author:wangxiangxin
   *@Date:2023/09/12
   */
  isPositionTop = false;
  constructor(
    private msg: NzMessageService,
    private Http: NewPrismaService,
    private dragulaService: DragulaService,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {
    dragulaService.createGroup("OPTIONS", {
      moves: (el, container, handle) => {
        return handle.className === "handle iconfont icon-caidan";
      },
    });
    this.subs.add();
  }

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    this.getLanOptions();
    if (this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM") {
      this.Http.getRelationLabel().subscribe((res) => {
        if (res.data) {
          this.relationLabelList = res.data;
          this.filterLabelData(0);
        }
      });

      this.checklist.forEach((item) => {
        if (
          item.child.filter(
            (ite) => ite.demographicMapId != null && ite.demographicMapId != ""
          ).length > 0
        ) {
          item.isRelationLabel = true;
        }
      });
    }
  }

  async getRelationLabel() {
    const res = await this.Http.getRelationLabel().toPromise();
    this.relationLabelList = res.data;
  }

  async getLanOptions() {
    const currentLansRes = await this.Http.getLanguages().toPromise();
    this.i18n = currentLansRes.data;
    this.nameObj = {};
    this.i18n.forEach((e) => {
      this.nameObj[e.value] = "";
    });
    this.prismaData.name = _.cloneDeep(this.nameObj);
  }
  saveSort() {
    if (true) {
      this.settingSave();
      return;
    }
    let editor = [];
    let editorson = [];
    this.checklist.forEach((item) => {
      item.child.forEach((res) => {
        if (!res.editor) editor.push(res);
        if (res.child && res.child.length != 0) {
          res.child.forEach((val) => {
            if (!val.editor) editorson.push(res);
          });
        }
      });
    });
    if (editor.length != 0 || editorson.length != 0) {
      // this.msg.warning("请先保存修改！");
      this.customMsg.open("warning", "请先保存修改");
      return;
    }
    let arr = [...this.prismaData.standardAnalysisFactorVOS, ...this.checklist];
    arr = this.removaldata(arr);

    this.Http.resort({
      projectId: this.projectId,
      analysisFactorDto: arr,
    }).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("排序保存成功");
        this.prismaData.standardAnalysisFactorVOS = arr;
        this.addfactorshow = false;
        let data = {
          addfactorshow: this.addfactorshow,
          factorlist: this.prismaData,
        };

        this.closemodal.emit(data);
      }
    });
  }

  ngOnDestroy() {
    this.dragulaService.destroy("OPTIONS");
    this.subs.unsubscribe();
  }

  isDragChange(e, type: number) {
    if (type === 1) {
      this.isChildDrag = false;
    } else {
      this.isChildDrag = true;
    }
  }

  ngOnChanges(changesQuestion: SimpleChanges) {
    this.prismaData.standardAnalysisFactorVOS = JSON.parse(
      JSON.stringify(this.factorlist.standardAnalysisFactorVOS)
    );
    this.checklist = this.prismaData.standardAnalysisFactorVOS.filter(
      (item) => {
        return item.type == "PULL_DOWN_BOX" && !item.isRequire;
      }
    );
    if (this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM") {
      this.getRelationLabel();
    }
    this.checklist.forEach((item) => {
      item.statusNew = item.status === "ENABLE";
      //关联标签
      if (
        this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM" &&
        item.isRelationLabel === undefined
      ) {
        item.isRelationLabel = false;
      }

      item.child.forEach((val) => {
        val.editor = true;
        val.statusNew = val.status === "ENABLE";
        if (!val.child) val.child = [];
        val.child.forEach((res) => {
          res.editor = true;
          res.statusNew = res.status === "ENABLE";
        });
      });
    });
    this.codelistlen =
      this.prismaData.standardAnalysisFactorVOS.length - this.checklist.length;
  }
  getanswered(list, type) {
    if (list.editor) {
      list.editor = !list.editor;
    }
    if (type == "save") {
      this.savemodifyName(list, "editor");
    }
  }
  savemodifyName(list, change) {
    if (list.name.zh_CN.trim() == "") {
      // this.msg.warning("选项名称为空不能保存！");
      this.customMsg.open("warning", "选项名称为空不能保存");
      return;
    }
    let params = {
      id: list.id,
      name: list.name,
    };
    this.Http.modifyName(params).subscribe((item) => {
      if (item.result.code == 0) {
        this.msg.success("保存成功");
        if (change == "editor") list.editor = true;
      }
    });
  }

  savemodifyReport(list) {
    let params = {
      id: list.id,
      labelShowReportPage: list.labelShowReportPage,
    };
    this.Http.modifyReport(params).subscribe((item) => {
      if (item.result.code == 0) {
        this.msg.success("保存成功");
      }
    });
  }
  addNewlist() {
    let numbers = 13;
    if (this.permission) {
      numbers = 25;
    } else {
      numbers = 13;
    }

    if (this.checklist.length + this.codelistlen >= numbers) {
      // this.msg.error(`人口标签最多${numbers}个！`);
      this.customMsg.open("error", `人口标签最多${numbers}个！`);
    } else {
      // this.prismaData.name_cn = ""
      // this.prismaData.name_eng = ""
      this.prismaData.name = _.cloneDeep(this.nameObj);
      this.prismaData.code = "";
      this.IsAdd = true;
      this.confirmvisible = true;
    }
  } //新增人口标签

  // 编辑新增人口标签的小tip弹窗 确定按钮
  getnzOnConfirm(i) {
    if (this.IsAdd) {
      // if (this.prismaData.name_cn.replace("", "") == "") {
      //   this.msg.error('请填写中文标签名称！')
      //   return
      // }
      // if (this.prismaData.name_cn && this.prismaData.name_cn.length > 50) {
      //   this.msg.error('中文名字最多输入50个中文字符!')
      //   return
      // }
      // if (this.prismaData.name_cn && this.prismaData.name_eng.length > 50) {
      //   this.msg.error('英文名字最多输入50个中文字符!')
      //   return
      // }
      if (this.prismaData.name.zh_CN.replace("", "") == "") {
        // this.msg.error("请填写中文标签名称！");
        this.customMsg.open("error", "请填写中文标签名称！");
        return;
      }
      if (
        this.prismaData.name.zh_CN &&
        this.prismaData.name.zh_CN.length > 50
      ) {
        // this.msg.error("中文名字最多输入50个中文字符!");
        this.customMsg.open("error", "中文名字最多输入50个中文字符!");
        return;
      }
      if (
        this.prismaData.name.en_US &&
        this.prismaData.name.en_US.length > 50
      ) {
        // this.msg.error("英文名字最多输入50个中文字符!");
        this.customMsg.open("error", "英文名字最多输入50个中文字符!");
        return;
      }
      if (this.prismaData.code.replace("", "") == "") {
        // this.msg.error("请填写编码！");
        this.customMsg.open("error", "请填写编码！");
        return;
      }
      if (this.prismaData.code && this.prismaData.code.length > 50) {
        // this.msg.error("编码最多输入50个中文字符!");
        this.customMsg.open("error", "编码最多输入50个中文字符!");
        return;
      }
      // 特殊字符要验证
      if (!this.validaNameValue(this.prismaData.name)) return;
      this.confirmvisible = true;
      let obj = {
        code: this.prismaData.code,
        dimensionScope: "ALL",
        type: "PULL_DOWN_BOX",
        isAdd: true,
        // name: {
        //   en_US: this.prismaData.name_eng,
        //   zh_CN: this.prismaData.name_cn
        // },
        name: this.prismaData.name,
        statusNew: true,
        child: [
          {
            dimensionScope: "ALL",
            // name: {
            //   en_US: "",
            //   zh_CN: ""
            // },
            name: _.cloneDeep(this.nameObj),
            statusNew: true,
            code: "",
            child: [],
          },
        ],
      };
      // if (this.reportType.indexOf('INVESTIGATION_RESEARCH_CUSTOM') !== -1 || this.reportType == 'CULTURE_INVESTIGATION_RESEARCH') {
      obj["labelShowReportPage"] = this.optionReport.map((item) => {
        item.checked = true;
        return item;
      });
      // }
      this.checklist.push(JSON.parse(JSON.stringify(obj)));

      this.confirmvisible = false;
      this.Modalcheck = this.checklist.length - 1;
    } else {
      // if (this.prismaData.name_cn.toString().replace("", "") == "") {
      //   this.msg.error('请填写中文标签名称！')
      //   return
      // }
      // if (this.prismaData.name_eng && this.prismaData.name_cn.length > 50) {
      //   this.msg.error('中文名字最多输入50个中文字符!')
      //   return
      // }
      // if (this.prismaData.name_eng && this.prismaData.name_eng.length > 50) {
      //   this.msg.error('英文名字最多输入50个中文字符!')
      //   return
      // }
      if (this.prismaData.name.zh_CN.toString().replace("", "") == "") {
        // this.msg.error("请填写中文标签名称！");
        this.customMsg.open("error", "请填写中文标签名称！");
        return;
      }
      if (
        this.prismaData.name.en_US &&
        this.prismaData.name.zh_CN.length > 50
      ) {
        // this.msg.error("中文名字最多输入50个中文字符!");
        this.customMsg.open("error", "中文名字最多输入50个中文字符!");
        return;
      }
      if (
        this.prismaData.name.en_US &&
        this.prismaData.name.en_US.length > 50
      ) {
        // this.msg.error("英文名字最多输入50个中文字符!");
        this.customMsg.open("error", "英文名字最多输入50个中文字符!");
        return;
      }
      if (this.prismaData.code.replace("", "") == "") {
        // this.msg.error("请填写编码！");
        this.customMsg.open("error", "请填写编码！");
        return;
      }
      if (this.prismaData.code && this.prismaData.code.length > 50) {
        // this.msg.error("编码最多输入50个中文字符!");
        this.customMsg.open("error", "编码最多输入50个中文字符!");
        return;
      }
      // 特殊字符要验证
      if (!this.validaNameValue(this.prismaData.name)) return;
      // this.checklist[i].name.zh_CN = this.prismaData.name_cn
      // this.checklist[i].name.en_US = this.prismaData.name_eng
      this.checklist[i].name = this.prismaData.name;
      this.checklist[i].code = this.prismaData.code;
      this.checklist[i].showble = false;
      this.checklist[i].showReport = false;
    }
    // if (this.showAnalysisFactor) {
    //   this.savemodifyName(this.checklist[i], 'editor')
    // }
  }

  // 编辑新增人口标签的小tip弹窗 取消按钮
  getnzOnCancel(i) {
    // this.prismaData.name_cn = ""
    // this.prismaData.name_eng = ""
    this.prismaData.name = _.cloneDeep(this.nameObj);
    this.prismaData.code = "";
    this.confirmvisible = false;
    if (i != "-1") {
      this.checklist[i].showble = false;
      this.checklist[i].showReport = false;
      //   this.prismaData.name_cn =
    }
  }

  getModalcheck(i) {
    this.Modalcheck = i;
    this.filterLabelData(i);
  }

  // 编辑名称
  editName(i) {
    // this.prismaData.name_cn = this.checklist[i].name.zh_CN
    // this.prismaData.name_eng = this.checklist[i].name.en_US
    this.prismaData.name = this.checklist[i].name;
    this.prismaData.code = this.checklist[i].code;
    this.checklist[i].showble = true;
    this.checklist[i].showReport = false;
    this.Modalcheck = i;
    this.IsAdd = false;
  }
  // 报告显示的小tip弹窗 确定按钮
  getRepotrOnConfirm(i) {
    this.checklist[i].labelShowReportPage = [];
    this.prismaData["labelShowReportPage"].forEach((item) => {
      if (item.checked) {
        this.checklist[i].labelShowReportPage.push(item.name);
      }
    });
    this.checklist[i].showble = false;
    this.checklist[i].showReport = false;
    // 活动是进行中之后的状态的话 直接单个保存
    // if (this.showAnalysisFactor) {
    //   this.savemodifyReport(this.checklist[i])
    // }
  }
  // 报告显示
  editReport(i) {
    this.prismaData["labelShowReportPage"] = [];
    //处理老数据 如果没有默认全选
    if (!this.checklist[i].labelShowReportPage) {
      this.prismaData["labelShowReportPage"] = this.optionReport.map((item) => {
        item.checked = true;
        return item;
      });
    } else {
      // 有返回值的话 根据返回的选项显示复选框
      this.prismaData["labelShowReportPage"] = JSON.parse(
        JSON.stringify(this.optionReport)
      );
      this.checklist[i].labelShowReportPage.forEach((res) => {
        this.prismaData["labelShowReportPage"].forEach((val) => {
          if (res == val.name) {
            val.checked = true;
          }
        });
      });
    }
    this.checklist[i].showReport = true;
    this.checklist[i].showble = false;
    this.Modalcheck = i;
    this.IsAdd = false;
  }

  // 新增人口标签选项
  addlist(i) {
    // if (!this.checklist[i].statusNew) {
    //   return
    // }
    let numbers = 20;
    if (this.permission) {
      numbers = 280;
    } else {
      numbers = 20;
    }
    if (this.checklist[i].child.length < numbers) {
      this.Modalcheck = i;
      this.checklist[i].child.push({
        dimensionScope: "ALL",
        name: {
          en_US: "",
          zh_CN: "",
        },
        child: [],
        statusNew: true,
      });
    } else {
      // this.msg.error(`最多添加${numbers}个选项！`);
      this.customMsg.open("error", `最多添加${numbers}个选项！`);
    }
    this.checklist[i].visible = false;
  }

  deleteFacName(i) {
    // this.Modalcheck = i
    // this.checklist.splice(i, 1)
    // this.checklist[i].visible = true
  }

  deleteFacNameok(i) {
    if (this.checklist[i].id) {
      // 有id的人口标签需要先判断一下是否可以删除
      this.Http.isCanDelete(this.checklist[i].id).subscribe((res) => {
        if (res.result.code === 0) {
          if (res.data) {
            this.Modalcheck = 0;
            this.checklist[i].visible = false;
            this.prismaData.standardAnalysisFactorVOS.forEach((item, index) => {
              if (item.name.zh_CN == this.checklist[i].name.zh_CN) {
                this.prismaData.standardAnalysisFactorVOS.splice(index, 1);
              }
            });
            this.checklist.splice(i, 1);
          } else {
            // this.msg.error(`该人口已绑定题本分发或题本修订，无法删除`);
            this.customMsg.open(
              "error",
              `该人口已绑定题本分发或题本修订，无法删除`
            );
          }
        }
      });
    } else {
      this.Modalcheck = 0;
      this.checklist[i].visible = false;
      this.prismaData.standardAnalysisFactorVOS.forEach((item, index) => {
        if (item.name.zh_CN == this.checklist[i].name.zh_CN) {
          this.prismaData.standardAnalysisFactorVOS.splice(index, 1);
        }
      });
      this.checklist.splice(i, 1);
    }
  }

  //删除人口标签中某个选项
  confirm(i, j) {
    if (this.checklist[i].child[j].id) {
      // 有id的人口标签需要先判断一下是否可以删除
      this.Http.isCanDelete(this.checklist[i].child[j].id).subscribe((res) => {
        if (res.result.code === 0) {
          if (res.data) {
            this.checklist[i].child.splice(j, 1);
          } else {
            // this.msg.error(`该人口已绑定题本分发或题本修订，无法删除`);
            this.customMsg.open(
              "error",
              `该人口已绑定题本分发或题本修订，无法删除`
            );
          }
        }
      });
    } else {
      this.checklist[i].child.splice(j, 1);
    }
  }
  confirmchild(i, j, data) {
    if (data[i].child[j].id) {
      // 有id的人口标签需要先判断一下是否可以删除
      this.Http.isCanDelete(data[i].child[j].id).subscribe((res) => {
        if (res.result.code === 0) {
          if (res.data) {
            data[i].child.splice(j, 1);
          } else {
            // this.msg.error(`该人口已绑定题本分发或题本修订，无法删除`);
            this.customMsg.open(
              "error",
              `该人口已绑定题本分发或题本修订，无法删除`
            );
          }
        }
      });
    } else {
      data[i].child.splice(j, 1);
    }
  }
  verifyDuplicate(data) {
    //判断是否有重复的code
    let arr = [];
    let tip = "";
    try {
      //找到重复编码跳出整个循环
      data.forEach((item) => {
        if (arr.indexOf(item.code) == -1) {
          arr.push(item.code);
        } else {
          tip = `人口标签${item.name.zh_CN}编码${item.code}重复`;
          throw new Error("finish");
        }
        item.child.forEach((element, index) => {
          if (arr.indexOf(element.code) == -1) {
            arr.push(element.code);
          } else {
            tip = `人口标签${item.name.zh_CN}第${index + 1}个选项编码${
              element.code
            }重复`;
            throw new Error("finish");
          }
          element.child.forEach((val, i) => {
            if (arr.indexOf(val.code) == -1) {
              arr.push(val.code);
            } else {
              tip = `人口标签${item.name.zh_CN}选项${
                element.name.zh_CN
              }的第${i + 1}个子选项编码${val.code}重复`;
              throw new Error("finish");
            }
          });
        });
      });
    } catch (e) {}
    return tip;
  }
  // 保存设置
  settingSave() {
    // falg=true 时 代表是已发布活动调用
    let zh_children = [];
    let en_children = [];
    let code_children = [];
    this.checklist.forEach((item) => {
      zh_children.push(
        _.filter(item.child, {
          name: {
            zh_CN: "",
          },
        }).length
      );
      code_children.push(
        _.filter(item.child, {
          code: "",
        }).length
      );
      item.child.forEach((val) => {
        zh_children.push(
          _.filter(item.child, {
            name: {
              zh_CN: "",
            },
          }).length
        );
        code_children.push(
          _.filter(item.child, {
            code: "",
          }).length
        );
        val.child.forEach((res) => {
          if (!res.standardDemographicId) {
            zh_children.push(
              _.filter(val.child, {
                name: {
                  zh_CN: "",
                },
              }).length
            );
            code_children.push(
              _.filter(val.child, {
                code: "",
              }).length
            );
          }
        });
      });
    });
    // 校验人口学标签是否有重复设置
    // if(this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM"){
    //   for (var i = 0; i < this.checklist.length; i++){
    //     let mapIds = [];
    //     this.checklist[i].child.forEach((item) => {
    //       if(mapIds.indexOf(item.demographicMapId) !== -1){
    //         this.msg.create("error", "不能重复关联同一人口标签");
    //           return ;
    //       } else if(item.demographicMapId != undefined){
    //         mapIds.push(item.demographicMapId);
    //       }
    //     })

    //   }
    // }
    if (zh_children.filter((item) => item >= 1).length > 0) {
      // this.msg.create("error", "请完成填写新增人口标签的中文选项");
      this.customMsg.open("error", "请完成填写新增人口标签的中文选项");
      this.addfactorshow = true;
      return;
    }
    if (code_children.filter((item) => item >= 1).length > 0) {
      // this.msg.create("error", "请完成填写新增人口标签的编码选项");
      this.customMsg.open("error", "请完成填写新增人口标签的编码选项");
      this.addfactorshow = true;
      return;
    }
    this.checklist.forEach((item) => {
      item.saveisAdd = true;
    });
    if (this.verifyDuplicate(this.checklist)) {
      // this.msg.create("error", this.verifyDuplicate(this.checklist));
      this.customMsg.open("error", this.verifyDuplicate(this.checklist));
      this.addfactorshow = true;
      return;
    }
    this.prismaData.standardAnalysisFactorVOS.forEach((item) => {
      this.checklist.forEach((res) => {
        if (item.code == res.code) {
          // if (item.name.zh_CN == res.name.zh_CN) {
          item.child = res.child;
        }
      });
    });
    this.checklist.forEach((res) => {
      if (res.isAdd) {
        this.prismaData.standardAnalysisFactorVOS.forEach((item, index) => {
          // if (item.name.zh_CN == res.name.zh_CN) {
          if (item.code == res.code) {
            item = res;
          } else {
            this.prismaData.standardAnalysisFactorVOS.push(res);
          }
        });
      }
    });
    let arr = [...this.prismaData.standardAnalysisFactorVOS, ...this.checklist];
    arr = _.cloneDeep(this.removaldata(arr));
    arr.forEach((item) => {
      if (this.showAnalysisFactor && !item.id) {
        item.isHidden = true;
        item.isChecked = true;
      }
      item.status = item.statusNew
        ? "ENABLE"
        : item.statusNew !== undefined
        ? "DISABLE"
        : item.status;

      item.child.forEach((res) => {
        res.status = res.statusNew
          ? "ENABLE"
          : res.statusNew !== undefined
          ? "DISABLE"
          : res.status;
        if (res.child && res.child.length != 0) {
          res.child.forEach((val) => {
            val.status = val.statusNew
              ? "ENABLE"
              : val.statusNew !== undefined
              ? "DISABLE"
              : val.status;
          });
        }
      });
    });
    this.prismaData.standardAnalysisFactorVOS = arr;
    this.addfactorshow = false;
    let data = {
      addfactorshow: this.addfactorshow,
      factorlist: this.prismaData,
    };
    if (this.projectId) {
      this.isLoadingOne = true;
      this.Http.saveOrUpdate({
        projectId: this.projectId,
        analysisFactorDto: arr,
      }).subscribe((res) => {
        if (res.result.code === 0) {
          this.msg.success("保存成功");
          data.factorlist.standardAnalysisFactorVOS = res.data;
          this.closemodal.emit(data);
        }
        this.isLoadingOne = false;
      });
    } else {
      this.closemodal.emit(data);
    }
  }
  goback() {
    this.cancelmodal.emit();
  }
  //恢复默认
  settingBack() {
    this.getdefaultlist.emit();
    this.Modalcheck = 0;
  }

  removaldata(arr) {
    arr.reverse();
    let result = [];
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      // if (!obj[arr[i].name.zh_CN]) {
      if (!obj[arr[i].code]) {
        result.push(arr[i]);
        // obj[arr[i].name.zh_CN] = true;
        obj[arr[i].code] = true;
      }
    }
    result.reverse();
    return result;
  } //去重

  getcuston(i) {
    this.checklist[i].showble = false;
    this.checklist[i].showReport = false;
    this.positionIsTop(i);
  }

  cancel() {}

  // 导出
  exportQusBook() {
    let projectId = "";
    if (this.projectId) projectId = this.projectId;
    this.Http.exportPrismaAnalysisFactor(
      projectId,
      this.surveyType,
      this.reportType
    ).subscribe((res) => {
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }

  /**
   * customReq 导入
   * @param item
   */
  customReqAnalysisFactor = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    let projectId = "";
    if (this.projectId) projectId = this.projectId;
    return this.Http.uploadPrismaAnalysisFactor(
      formData,
      projectId,
      this.surveyType,
      this.reportType
    ).subscribe(
      (event: HttpEvent<any>) => {
        item.onSuccess!();
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入文件成功");
          // res.data.map(item => {
          //     item.child = item.analysisFactorChildren
          // })
          res.data.forEach((obj) => {
            obj.saveisAdd = true;
            if (this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM") {
              if (
                obj.child.filter(
                  (ite) =>
                    ite.demographicMapId != null && ite.demographicMapId != ""
                ).length > 0
              ) {
                obj.isRelationLabel = true;
              }
            }
          });
          this.loadDataMap.emit(res.data);
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }
  getaddchild(data) {
    let numbers = 20;
    if (this.permission) {
      numbers = 280;
    } else {
      numbers = 20;
    }
    if (data.length < numbers) {
      data.push({
        dimensionScope: "ALL",
        name: {
          en_US: "",
          zh_CN: "",
        },
        code: "",
        statusNew: true,
      });
    } else {
      // this.msg.error(`子选项最多添加${numbers}个选项！`);
      this.customMsg.open("error", `子选项最多添加${numbers}个选项！`);
    }
  }
  //禁用人口学标签
  clickIsVisitAnswer(e, index, k) {
    //如果是false 则提示用户是否禁用
    if (!this.checklist[index].child[k].statusNew) {
      e.stopPropagation();
      this.checklist[index].child[k].statusNew = true;
      this.checklist[index].child[k].showRelationLabel = false;
      if (this.checklist[index].child[k].child.length != 0) {
        this.checklist[index].child[k].child.forEach((res) => {
          res.statusNew = true;
        });
      }
    }
  }

  confirmSwitch(index, k): void {
    this.checklist[index].child[k].statusNew = false;
    // this.nzMessageService.info('click confirm');
    if (this.checklist[index].child[k].child.length != 0) {
      this.checklist[index].child[k].child.forEach((res) => {
        res.statusNew = false;
      });
    }
  }

  //禁用人口学标签
  clickIsVisitAnswerChild(e, index, k, l) {
    //如果是false 则提示用户是否禁用
    if (!this.checklist[index].child[k].child[l].statusNew) {
      e.stopPropagation();
      this.checklist[index].child[k].child[l].statusNew = true;
    } else {
      // this.checklist[index].child[k].statusNew = false
    }
  }

  confirmSwitchChild(index, k, l): void {
    this.checklist[index].child[k].child[l].statusNew = false;
    // this.nzMessageService.info('click confirm');
  }

  //禁用人口学标签
  clickIsVisitAnswerParent(e, index) {
    //如果是false 则提示用户是否禁用
    if (!this.checklist[index].statusNew) {
      e.stopPropagation();
      this.checklist[index].statusNew = true;
      this.checklist[index].child.forEach((res) => {
        res.statusNew = true;
        if (res.child && res.child.length != 0) {
          res.child.forEach((val) => {
            val.statusNew = true;
          });
        }
      });
    } else {
      // this.checklist[index].Parent[k].statusNew = false
    }
  }
  confirmSwitchParent(index): void {
    this.checklist[index].statusNew = false;
    this.checklist[index].child.forEach((res) => {
      res.statusNew = false;
      if (res.child && res.child.length != 0) {
        res.child.forEach((val) => {
          val.statusNew = false;
        });
      }
    });
  }
  onSelectI18n(e) {
    this.lan = e;
  }
  changeNameValue(name) {
    this.validaNameValue(name);
    this.prismaData.name = name;
  }
  //校验名称中不能含有非法字符
  validaNameValue(name) {
    if (this.reportType.indexOf("INVESTIGATION_RESEARCH") > -1) {
      var characterList = new Array(
        ":",
        "：",
        "/",
        "?",
        "？",
        "\\",
        "*",
        "[",
        "]"
      );
      for (let index = 0; index < characterList.length; index++) {
        const char = characterList[index];
        if (name.zh_CN && name.zh_CN.indexOf(char) > -1) {
          this.customMsg.open("error", "中文名称不能包含" + char);
          return false;
        }
        if (name.en_US && char !== "/" && name.en_US.indexOf(char) > -1) {
          this.customMsg.open("error", "英文名称不能包含" + char);
          return false;
        }
      }
      return true;
    }
    return true;
  }
  /**
   * 计算元标签弹框定位是否应该定位在上方
   *@author:wangxiangxin
   *@Date:2023/09/12
   */
  positionIsTop(i) {
    // 可视区域的高度
    let h = document.body.clientHeight;
    // 内容距离顶部的距离
    var scrollHeight = document
      .getElementById(`scroll` + i)
      .getBoundingClientRect().top; // 获取元素
    this.isPositionTop = Math.ceil(h / 2) + 27 < scrollHeight;
  }

  checkRelationLabel(i) {
    this.Modalcheck = i;
    this.checklist[i].isRelationLabel = !this.checklist[i].isRelationLabel;
    this.checklist[i].visible = false;
    this.filterLabelData(i);
    if (!this.checklist[i].isRelationLabel) {
      this.checklist[i].child.forEach((item) => {
        item.demographicMapId = "";
      });
    }
  }
  relationLabel(data, item) {
    item.child.forEach((ite) => {
      if (ite.showRelationLabel) {
        ite.showRelationLabel = false;
      }
    });
    data.showRelationLabel = !data.showRelationLabel;
  }

  //过滤关联标签数据
  filterLabelData(i) {
    if (this.reportType === "DP_INVESTIGATION_RESEARCH_CUSTOM") {
      this.currentRelationLabelList = [];
      if (
        this.checklist[i].basicLabel === "" ||
        this.checklist[i].basicLabel === undefined ||
        this.checklist[i].basicLabel === "NONE"
      ) {
        this.currentRelationLabelList = _.cloneDeep(
          this.relationLabelList.filter((item) => {
            return item.parentId != "0";
          })
        );
        this.currentRelationLabelList.forEach((item) => {
          let parent = this.relationLabelList.find(
            (obj) => obj.id === item.parentId && obj.parentId === "0"
          );
          if (parent) {
            item.name.zh_CN = parent.name.zh_CN + "/" + item.name.zh_CN;
          }
        });
      } else {
        this.currentRelationLabelList = this.relationLabelList.filter(
          (item) => {
            return (
              item.basicLabel == this.checklist[i].basicLabel &&
              item.parentId != "0"
            );
          }
        );
      }
    }
  }

  // 人口标签关联的小tip弹窗 确定按钮
  relationLabelOnConfirm(item, i) {
    item.showRelationLabel = false;
  }

  // 人口标签关联的小tip弹窗 取消按钮
  relationLabelOnCancel(item, i) {
    item.showRelationLabel = false;
  }
}
