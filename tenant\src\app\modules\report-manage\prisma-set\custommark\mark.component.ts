import { HttpClient } from "@angular/common/http";
import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd";
import _ from "lodash";
import { Observable } from "rxjs";
import { NzFormatEmitEvent } from "ng-zorro-antd/core";
import { NzTreeComponent } from "ng-zorro-antd/tree";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-custom-mark",
  templateUrl: "./mark.component.html",
  styleUrls: ["./mark.component.less"],
})
export class MarkComponent implements OnInit {
  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;
  @ViewChild("nzTreeOrgComponent", { static: false })
  nzTreeOrgComponent: NzTreeComponent;
  @Input() prismaReportDataId: string;
  @Input() projectId: string;

  updateId: string;

  name: any = { zh_CN: "", en_US: "" };

  catList = [];
  catOtherList = [];

  defaultCheckedKeys: any[] = [];
  defaultCheckedRenkouKeys: any[] = [];
  allOrgChecked: any[] = [];
  treeNodes = [];
  allTreeNode = [];
  treeOrg = [];
  searchNode1: string = "";
  searchNode2: string = "";
  language: string = "zh_CN";
  organizationName: any = {
    zh_CN: "",
    en_US: "",
  };
  treeOrgAll: boolean = true;
  renkouCheckAll: boolean = false;

  catMap = {};
  catOtherMap = {};

  groupHidden: boolean = true;

  groupList: any[] = [];

  dimQuesList: any[] = [];
  demographicContents: any[] = [];

  twoDash: string = "__";

  showcard = false;

  page: number = 1; // 页码
  tabIndex: number = 1; // tabIdx

  namelist = [];
  provinceDatacus = [];
  provinceDataone = [];
  provinceDatatwo = [];
  provinceDatathree: any[] = [];

  selectedProvincecus = null;
  selectedProvinceone = null;
  selectedProvincetwo = null;
  selectedProvincethree = null;
  selectedProvince: any = null;

  PrismaReportData = [];
  oldPrismaReportData = [];
  expandedOrgNodes = [];
  expandedRenNodes = [];

  oldlist = [];

  typelist = [
    {
      value: "INDEX",
      label: "指数",
    },
    {
      value: "ONE_RANK",
      label: "一级维度",
    },
    {
      value: "TWO_RANK",
      label: "二级维度",
    },
    {
      value: "THREE_RANK",
      label: "三级维度",
    },
  ];

  valuewords = "";

  tenantUrl: string = "/tenant-api";

  constructor(
    private http: HttpClient,
    private msgServ: NzMessageService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    this.bgGetGroupList();
    this.bgGetDimList();
    this.getcustomlist();
    this.listDemographicdata();
    this.listOrganizationByPrismaReportDataId();
  }

  nzEvent(event: NzFormatEmitEvent): void {
    // console.log("aa");
  }
  nzCheck(event: NzFormatEmitEvent): void {
    this.defaultCheckedKeys = event.keys;
    console.log(event.keys, this.allOrgChecked, "-------");
    this.treeOrgAll = true;
    this.allOrgChecked.forEach((item) => {
      if (!this.defaultCheckedKeys.includes(item)) {
        this.treeOrgAll = false;
      }
    });
    // if (this.defaultCheckedKeys.length === this.allOrgChecked.length) {
    //   this.treeOrgAll = true;
    // } else {
    //   this.treeOrgAll = false;
    // }
  }

  CheckRenKou(event: NzFormatEmitEvent) {
    let flag = 1;

    this.treeNodes.forEach((item) => {
      if (!item.checked) flag = 0;
      if (item.children.length !== 0) {
        item.children.forEach((element) => {
          if (!element.checked) flag = 0;
          if (element.children.length !== 0) {
            element.children.forEach((ele) => {
              if (!ele.checked) flag = 0;
            });
          }
        });
      }
      if (flag === 1) {
        this.renkouCheckAll = true;
      } else {
        this.renkouCheckAll = false;
      }
    });
  }

  mapDemographicContents() {
    this.demographicContents = [];
    this.treeNodes.forEach((item) => {
      let demographicRootId;
      let demographicIds = [];
      if (item.children.length !== 0) {
        item.children.forEach((item1) => {
          if (item1.children.length !== 0) {
            item1.children.forEach((item2) => {
              if (item2.checked) {
                demographicRootId = item.id;
                demographicIds.push(item2.id);
              }
            });
          } else {
            if (item1.checked) {
              demographicRootId = item.id;
              demographicIds.push(item1.id);
            }
          }
        });
      }
      if (demographicRootId) {
        this.demographicContents.push({
          demographicRootId: demographicRootId,
          demographicIds: demographicIds,
        });
      }
    });
  }

  CheckRenKouAll(e) {
    this.treeNodes.forEach((item) => {
      if (e) {
        this.defaultCheckedRenkouKeys = [
          ...this.defaultCheckedRenkouKeys,
          item.key,
        ];
        if (item.children.length !== 0) {
          item.children.forEach((element) => {
            element.checked = true;
            this.defaultCheckedRenkouKeys = [
              ...this.defaultCheckedRenkouKeys,
              element.key,
            ];
            if (element.children.length !== 0) {
              element.children.forEach((ele) => {
                ele.checked = true;
                this.defaultCheckedRenkouKeys = [
                  ...this.defaultCheckedRenkouKeys,
                  ele.key,
                ];
              });
            } else {
              this.defaultCheckedRenkouKeys = [
                ...this.defaultCheckedRenkouKeys,
                element.key,
              ];
            }
          });
        }
      } else {
        this.defaultCheckedRenkouKeys = [];
      }
    });
  }

  treeOrgChangeAll(e) {
    if (e) {
      this.defaultCheckedKeys = this.allOrgChecked;
    } else {
      this.defaultCheckedKeys = [];
    }
  }

  intData() {
    // 大维度
    let tmp = [];
    let list: any[] = this.getSelection1();
    let obj = {
      id: "dimension",
      name: "大维度",
      allChecked: false,
      indeterminate: false,
      items: list[0],
      type: 1,
      checkedValues: [],
      searchText: "",
    };
    let obj1 = this.cloneDeepJson(obj);
    this.catList.push(obj);
    this.catOtherList.push(obj1);
    this.catMap[obj.type] = obj;
    this.catOtherMap[obj.type] = obj1;

    // 子维度
    tmp = [];
    obj = {
      id: "subDimension",
      name: "子维度",
      allChecked: false,
      indeterminate: false,
      items: list[1],
      type: 2,
      checkedValues: [],
      searchText: "",
    };
    obj1 = this.cloneDeepJson(obj);
    this.catList.push(obj);
    this.catOtherList.push(obj1);
    this.catMap[obj.type] = obj;
    this.catOtherMap[obj.type] = obj1;

    // 题本
    tmp = [];
    obj = {
      id: "question",
      name: "题本",
      allChecked: false,
      indeterminate: false,
      items: list[2],
      type: 3,
      checkedValues: [],
      searchText: "",
    };
    obj1 = this.cloneDeepJson(obj);
    this.catList.push(obj);
    this.catOtherList.push(obj1);
    this.catMap[obj.type] = obj;
    this.catOtherMap[obj.type] = obj1;
  }

  cloneDeepJson(obj) {
    return JSON.parse(JSON.stringify(obj));
  }

  getSelection1(): any[] {
    let tmp1: any[] = [];
    let tmp2: any[] = [];
    let tmp3: any[] = [];

    for (let index = 0; index < this.dimQuesList.length; index++) {
      const element = this.dimQuesList[index];
      let val = element.type;
      tmp1.push({
        label: element.name.zh_CN,
        value: val,
        checked: false,
        isShow: true,
      });
      this.getSelection2(element.child, tmp2, tmp3, val);
    }
    return [tmp1, tmp2, tmp3];
  }

  getSelection2(
    dataList: any[],
    itemList: any[],
    subItemList: any[],
    parentId: string
  ) {
    for (let index = 0; index < dataList.length; index++) {
      const element = dataList[index];
      // let val = element.type + '-' + element.dimensionCode;
      let val = element.type + this.twoDash + element.dimensionCode;
      itemList.push({
        label: element.name.zh_CN,
        value: val,
        checked: false,
        isShow: false,
        pId: parentId,
      });
      this.getSelection3(
        element.child,
        subItemList,
        val,
        element.dimensionCode
      );
    }
  }

  getSelection3(
    dataList: any[],
    itemList: any[],
    parentId: string,
    dimCode: string
  ) {
    for (let index = 0; index < dataList.length; index++) {
      const element = dataList[index];
      // let val = element.type + '-' + element.questionId;
      let val =
        element.type +
        this.twoDash +
        dimCode +
        this.twoDash +
        element.questionId;
      itemList.push({
        label: element.name.zh_CN,
        value: val,
        checked: false,
        isShow: false,
        pId: parentId,
      });
    }
  }

  updateAllChecked(tabData, e) {
    tabData.indeterminate = false;
    tabData.items.forEach((item) => {
      if (item.isShow) {
        item.checked = e;
      }
    });

    this.changeCheckModel(tabData);
  }

  updateSingleChecked(tabData, itemData, e) {
    if (tabData.items.every((item) => !item.checked)) {
      tabData.allChecked = false;
      tabData.indeterminate = false;
    } else if (tabData.items.every((item) => item.checked)) {
      tabData.allChecked = true;
      tabData.indeterminate = false;
    } else {
      tabData.indeterminate = true;
    }

    this.changeCheckModel(tabData);
  }

  changeCheckModel(tabData) {
    let type = tabData.type;
    if (type === 3) {
      return;
    }

    let checkArr: string[] = [];
    let items: any[] = tabData.items;
    for (let index = 0; index < items.length; index++) {
      const element = items[index];
      if (element.checked && element.isShow) {
        checkArr.push(element.value);
      }
    }
    // 下一级是否显示
    let children: any[];
    children = this.catMap[type + 1].items;

    if (children && children.length > 0) {
      for (let j = 0; j < children.length; j++) {
        const ch = children[j];
        if (_.includes(checkArr, ch.pId)) {
          ch.isShow = true;
        } else {
          ch.isShow = false;
        }
      }
    }

    // 如果是大维度，检查问题是否显示
    if (type === 1) {
      this.checkQuestion();
    }
  }

  checkQuestion() {
    // 子维度选择
    let checkArr: string[] = [];
    let items: any[];
    items = this.catMap[2].items;

    for (let index = 0; index < items.length; index++) {
      const element = items[index];
      if (element.checked && element.isShow) {
        checkArr.push(element.value);
      }
    }

    // 问题是否显示
    let children: any[];
    children = this.catMap[3].items;

    if (children && children.length > 0) {
      for (let j = 0; j < children.length; j++) {
        const ch = children[j];
        if (_.includes(checkArr, ch.pId)) {
          ch.isShow = true;
        } else {
          ch.isShow = false;
        }
      }
    }
  }

  getSingleParam(str: string): any {
    let arr: string[] = _.split(str, this.twoDash);
    let tmp: any = {};
    if (arr.length > 0) {
      tmp.type = arr[0];
    }
    if (arr.length > 1) {
      tmp.dimensionCode = arr[1];
    }
    if (arr.length > 2) {
      tmp.questionId = arr[2];
    }
    return tmp;
  }

  next() {
    if (!this.selectedProvincecus) {
      // this.msgServ.error("指数必选");
      this.customMsg.open("error", "指数必选");
      return;
    }
    this.page = 2;
  }

  makeGroup() {
    if (!this.selectedProvincecus) {
      // this.msgServ.error("指数必选");
      this.customMsg.open("error", "指数必选");
      return;
    }

    let tmpList: any[] = [];

    for (let index = 0; index < this.catList.length; index++) {
      let cat: any = this.catList[index];
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const it = items[j];
        if (it.isShow && it.checked) {
          tmpList.push(this.getSingleParam(it.value));
        }
      }
    }

    if (tmpList.length == 0) {
      // this.msgServ.error("组合维度必选");
      this.customMsg.open("error", "组合维度必选");
      return;
    }

    let newdemographicContents = [];
    this.mapDemographicContents();

    let param: any = {
      prismaReportDataId: this.prismaReportDataId,
      indexId: this.selectedProvincecus,
      detailList: tmpList,

      //可选可不选
      demographicContents: this.demographicContents,
      parentDimensionId: this.selectedProvinceone,
      dimensionId: this.selectedProvincetwo,
      threeDimensionId: this.selectedProvincethree,
      // organizationName: this.organizationName, // 组织名称
      organizationIds: this.defaultCheckedKeys, // 组织Id
    };
    if (!this.organizationName.zh_CN && !this.organizationName.en_US) {
      param.organizationName = null;
    } else {
      param.organizationName = this.organizationName;
    }

    let sub = null;
    if (this.updateId) {
      param.id = this.updateId;
      sub = this.bgUpdate(param);
    } else {
      sub = this.bgCreate(param);
    }

    sub.subscribe((res: any) => {
      if (res.result.code === 0) {
        this.msgServ.success("保存成功");
        this.clearOption(true);
        this.bgGetGroupList();
      }
    });
  }

  delete(e, id) {
    e.stopPropagation();
    this.bgDelete(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msgServ.success("删除成功");
        if (id === this.updateId) {
          this.updateId = undefined;
        }
        this.bgGetGroupList();
      }
    });
  }

  edit(e, groupData: any) {
    this.page = 1;
    this.clearOption(true);
    e.stopPropagation();
    this.clearOption(true);
    let arr1: string[] = [];
    let arr2: string[] = [];
    let arr3: string[] = [];
    let arr4: string[] = [];
    let arr5: string[] = [];
    let arr6: string[] = [];

    let selList = groupData.selectedList;
    for (let index = 0; index < selList.length; index++) {
      const selItem = selList[index];
      let type: string = selItem.type;
      let dimensionCode: string = selItem.dimensionCode;
      let questionId: string = selItem.questionId;
      let val: string = "";
      if (questionId && dimensionCode && type) {
        val = type + this.twoDash + dimensionCode + this.twoDash + questionId;
        arr3.push(val);
      } else if (dimensionCode && type) {
        val = type + this.twoDash + dimensionCode;
        arr2.push(val);
      } else {
        val = type;
        arr1.push(val);
      }
    }

    this.restoreCheckState(1, arr1, []);
    this.restoreCheckState(2, arr2, arr1);
    this.restoreCheckState(3, arr3, arr2);
    this.restoreCheckState2(1, arr4, []);
    this.restoreCheckState2(2, arr5, arr4);
    this.restoreCheckState2(3, arr6, arr5);
    this.showGroup();

    this.selectedProvincecus = groupData.indexId;
    this.selectedProvinceone = groupData.parentDimensionId;
    this.selectedProvincetwo = groupData.dimensionId;
    this.selectedProvincethree = groupData.threeDimensionId;
    this.organizationName = groupData.organizationName
      ? groupData.organizationName
      : { zh_CN: "", en_US: "" };
    let selectedProvince = [];
    // this.defaultCheckedRenkouKeys = []
    // groupData.demographicContents.forEach(item => {
    //     this.defaultCheckedRenkouKeys = [this.defaultCheckedRenkouKeys, ...item.demographicIds, item.demographicRootId]
    // })

    this.selectedProvince = selectedProvince;
    this.updateId = groupData.id;
    // 回显组织架构
    this.echoOrg(groupData);
    // 回显人口标签
    this.echoPopulation(groupData);
  }

  echoOrg(groupData) {
    const trees = this.nzTreeOrgComponent.getTreeNodes();
    trees.forEach((node1) => {
      this.echoOrgMap(node1, groupData);
    });
  }

  echoOrgMap(node, groupData) {
    // let flag = false
    groupData.organizationIds.map((item) => {
      if (node.key === item) {
        // flag = true
        node.isChecked = true;
        // this.tabIndex = 2
      }
    });
    // node.isChecked = flag
    if (node.children.length !== 0) {
      node.children.forEach((element) => {
        this.echoOrgMap(element, groupData);
      });
    }

    setTimeout(() => {
      // this.tabIndex = 1
      this.treeNodes.forEach((item) => {
        if (item.children.length != 0) {
          item.checked = item.children.every((ele) => {
            return ele.checked;
          });
        }
      });
      this.renkouCheckAll = this.treeNodes.every((item) => {
        return item.checked;
      });
      console.log(this.renkouCheckAll);
      // 此行注释掉 要不然 用户没有全选的时候 标签选中无法回显 具体代码逻辑未缕清
      // this.CheckRenKouAll(this.renkouCheckAll)
    }, 100);
  }
  checkEcho() {
    this.defaultCheckedRenkouKeys = [];
    this.treeNodes.map((item) => {
      if (item.checked) {
        this.defaultCheckedRenkouKeys = [
          ...this.defaultCheckedRenkouKeys,
          item.id,
        ];
        if (item.children.length !== 0) {
          item.children.forEach((element) => {
            if (element.checked) {
              this.defaultCheckedRenkouKeys = [
                ...this.defaultCheckedRenkouKeys,
                element.key,
              ];
            }
            if (element.children.length !== 0) {
              element.children.forEach((ele) => {
                if (ele.checked) {
                  this.defaultCheckedRenkouKeys = [
                    ...this.defaultCheckedRenkouKeys,
                    ele.key,
                  ];
                }
              });
            }
          });
        }
      }
    });
  }

  echoPopulation(groupData) {
    const trees = this.nzTreeComponent.getTreeNodes();
    trees.forEach((nodes1) => {
      if (nodes1.children.length !== 0) {
        let isNode1Checked = true;
        nodes1.isHalfChecked = false;
        nodes1.children.forEach((nodes2) => {
          if (nodes2.children.length !== 0) {
            let isNode2Checked = true;
            nodes2.isHalfChecked = false;
            nodes2.children.forEach((nodes3) => {
              // 用来记录是否已经查询到nodes2.key存在
              let isActive = false;
              nodes3.isChecked = false;
              groupData.demographicContents.map((item1) => {
                // item1.demographicIds.map((item2) => {
                if (
                  item1.demographicIds.includes(nodes3.key) &&
                  item1.demographicRootId === nodes1.key
                ) {
                  nodes3.isChecked = true;
                  // 用来记录是否已经查询到nodes2.key存在
                  isActive = true;
                  nodes2.isHalfChecked = true;
                  nodes1.isHalfChecked = true;
                  if (isNode2Checked) {
                    isNode2Checked = true;
                  }
                }
                // });
              });
              if (isNode2Checked) {
                isNode2Checked = isActive;
                if (isNode1Checked) {
                  isNode1Checked = isActive;
                }
              }
            });
            nodes2.isChecked = isNode2Checked;
            nodes2.isHalfChecked = isNode2Checked
              ? false
              : nodes2.isHalfChecked;
          } else {
            // 用来记录是否已经查询到nodes2.key存在
            let isActive = false;
            nodes2.isChecked = false;
            groupData.demographicContents.map((item1) => {
              // item1.demographicIds.map((item2) => {
              if (
                item1.demographicIds.includes(nodes2.key) &&
                item1.demographicRootId === nodes1.key
              ) {
                nodes2.isChecked = true;
                isActive = true;
                if (isNode1Checked) {
                  isNode1Checked = true;
                }
                nodes1.isHalfChecked = true;
              }
              // });
            });
            if (isNode1Checked) {
              isNode1Checked = isActive;
            }
          }
        });
        nodes1.isChecked = isNode1Checked;
        nodes1.isHalfChecked = isNode1Checked ? false : nodes1.isHalfChecked;
      }
    });
  }

  restoreCheckState(
    type: number,
    checkedValues: string[],
    checkParentValues: string[]
  ) {
    let items: any[];
    items = this.catMap[type].items;
    if (items && items.length > 0) {
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        // set show state
        if (type === 1 || _.includes(checkParentValues, item.pId)) {
          item.isShow = true;
        } else {
          item.isShow = false;
        }
        // set check state
        item.checked = _.includes(checkedValues, item.value);
      }
    }
  }
  restoreCheckState2(
    type: number,
    checkedValues: string[],
    checkParentValues: string[]
  ) {
    let items: any[];
    items = this.catOtherMap[type].items;
    if (items && items.length > 0) {
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        // set show state
        if (type === 1 || _.includes(checkParentValues, item.pId)) {
          item.isShow = true;
        } else {
          item.isShow = false;
        }
        // set check state
        item.checked = _.includes(checkedValues, item.value);
      }
    }
  }

  clearOption(clearName?: boolean) {
    if (clearName) {
      this.selectedProvincecus = null;
      this.selectedProvinceone = null;
      this.selectedProvincetwo = null;
      this.selectedProvincethree = null;
      this.selectedProvince = null;
      this.organizationName = {
        zh_CN: "",
        en_US: "",
      };
    }
    this.updateId = undefined;

    // clear select
    for (let index = 0; index < this.catList.length; index++) {
      const cat = this.catList[index];
      cat.searchText = "";
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        item.checked = false;
        item.isShow = cat.type === 1;
      }
      cat.allChecked = false;
      cat.indeterminate = false;
    }
    for (let index = 0; index < this.catOtherList.length; index++) {
      const cat = this.catOtherList[index];
      cat.searchText = "";
      let items: any[] = cat.items;
      for (let j = 0; j < items.length; j++) {
        const item = items[j];
        item.checked = false;
        item.isShow = cat.type === 1;
      }
      cat.allChecked = false;
      cat.indeterminate = false;
    }
  }

  showGroup() {
    this.groupHidden = !this.groupHidden;
  }

  clearGroup() {
    this.bgEmptyGroup().subscribe((res: any) => {
      if (res.result.code === 0) {
        this.msgServ.success("清空所有组合成功");
        this.updateId = undefined;
        this.bgGetGroupList();
      }
    });
  }

  getTips() {
    this.showcard = true;
    this.getcustomlist();
  }
  test() {
    // this.namelist = this.oldlist.filter(item =>{
    //     return item.name.zh_CN.indexOf(this.valuewords) > -1
    // })
    this.namelist.forEach((item) => {
      if (item.name.zh_CN.indexOf(this.valuewords) > -1) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
    this.namelist.sort(function(a, b) {
      return b.checked - a.checked;
    });
  }
  addlist() {
    this.namelist.unshift({
      type: null,
      name: {
        en_US: "",
        zh_CN: "",
      },
      id: null,
      checked: true,
    });
    this.namelist.sort(function(a, b) {
      return b.checked - a.checked;
    });
  }

  deleteTips(e, id, i) {
    e.stopPropagation();
    this.namelist.splice(i, 1);
    this.namelist.sort(function(a, b) {
      return b.checked - a.checked;
    });
  }

  provincechoose(e) {
    this.PrismaReportData.forEach((item) => {
      item.checked = false;
      item.children.forEach((res) => {
        res.checked = false;
        e.forEach((val) => {
          if (res.id == val) res.checked = true;
        });
        if (res.checked) item.checked = true;
      });
    });
    this.oldPrismaReportData = JSON.parse(
      JSON.stringify(this.PrismaReportData)
    );
  }

  cannelcard() {
    this.showcard = false;
    this.valuewords = "";
  }
  commitcard() {
    let list = this.namelist.every((item) => {
      return item.type;
    });

    if (list) {
      let param = {
        list: this.namelist,
        projectId: this.projectId,
      };
      this.batchCreateOrUpdate(param).subscribe((res: any) => {
        if (res.result.code == 0) {
          this.showcard = false;
          this.valuewords = "";
          this.getcustomlist();
        }
      });
    } else {
      // this.msgServ.warning("指数类别必选！");
      this.customMsg.open("warning", "指数类别必选");
      return;
    }
  }

  bgGetGroupList() {
    let api = `${this.tenantUrl}/survey/prisma/custom/index/combination/listByPrismaReportDataId/${this.prismaReportDataId}`;
    this.http.get(api).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.groupList = res.data;
        for (let index = 0; index < this.groupList.length; index++) {
          const element = this.groupList[index];
          element.active = true;
          element.factor = "";
        }
        let newsname = [];
        this.groupList.forEach((item) => {
          item.groupname = [];
          item.demographics.forEach((val) => {
            val.factorname = "";
            val.children.forEach((res, index) => {
              val.factorname +=
                res.name.zh_CN + (index == val.children.length - 1 ? "" : "、");
            });
            val.factor = val.name.zh_CN + "：" + val.factorname;
            item.factor += val.factor;
          });
          if (item.indexName && item.indexName.zh_CN) {
            item.indextype = item.indexName.zh_CN;
          } else {
            item.indextype = "";
          }
          if (item.parentDimensionName && item.parentDimensionName.zh_CN) {
            item.parenttype = item.parentDimensionName.zh_CN;
          } else {
            item.parenttype = "";
          }
          if (item.dimensionName && item.dimensionName.zh_CN) {
            item.dimensiontype = item.dimensionName.zh_CN;
          } else {
            item.dimensiontype = "";
          }
          item.groupname.push(
            item.indextype,
            item.parenttype,
            item.dimensiontype,
            item.factor
          );
          item.groupnames = item.groupname
            .filter((item) => {
              return item;
            })
            .join("/");
        });
      }
    });
  }

  bgGetDimList() {
    let api = `${this.tenantUrl}/survey/prisma/correlation/coefficient/combination/listQuestionDimensionByPrismaReportDataId/${this.prismaReportDataId}`;
    this.http.get(api).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.dimQuesList = res.data;
        this.intData();
      }
    });
  }

  getcustomlist() {
    let api = `${this.tenantUrl}/survey/prisma/custom/index/dimension/listByProjectId/${this.projectId}`;
    this.http.get(api).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.namelist = res.data;
        this.namelist.forEach((item) => {
          item.checked = true;
        });
        this.oldlist = JSON.parse(JSON.stringify(res.data));

        this.provinceDatacus = this.namelist.filter((item) => {
          return item.type == "INDEX";
        });
        this.provinceDataone = this.namelist.filter((item) => {
          return item.type == "ONE_RANK";
        });
        this.provinceDatatwo = this.namelist.filter((item) => {
          return item.type == "TWO_RANK";
        });
        this.provinceDatathree = this.namelist.filter((item) => {
          return item.type == "THREE_RANK";
        });
      }
    });
  }

  listOrganizationByPrismaReportDataId() {
    let api = `${this.tenantUrl}/survey/prisma/custom/index/listOrganizationByPrismaReportDataId/${this.prismaReportDataId}`;
    this.http.get(api).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.formatterOrg(res.data);
        this.treeOrg = res.data;
        if (this.treeOrg && this.treeOrg.length > 0) {
          this.expandedOrgNodes = [this.treeOrg[0].key];
        }
      }
    });
  }
  listDemographicdata() {
    let api = `${this.tenantUrl}/survey/prisma/custom/index/listDemographicByPrismaReportDataId/${this.prismaReportDataId}`;
    this.http.get(api).subscribe((res: any) => {
      if (res.result.code === 0) {
        // this.PrismaReportData = res.data
        this.treeNodes = res.data;
        this.formatter(this.treeNodes);
        if (this.treeNodes && this.treeNodes.length > 0) {
          this.treeNodes.map((item) => {
            this.expandedRenNodes = [...this.expandedRenNodes, item.id];
          });
        }
      }
    });
  }

  formatter(data) {
    this.allTreeNode = [];
    data.forEach((item) => {
      this.allTreeNode.push(item.key);
      item.title = item.name.zh_CN;
      item.key = item.id;
      item.checked = false;
      if (item.children.length !== 0) {
        item.isLeaf = false;
        this.formatter(item.children);
      } else {
        item.isLeaf = true;
      }
    });
  }
  formatterOrg(data) {
    data.map((item) => {
      if (item.selected && !item.disabled) {
        this.defaultCheckedKeys = [...this.defaultCheckedKeys, item.key];
        this.allOrgChecked = [...this.allOrgChecked, item.key];
      }
      if (item.children.length !== 0) {
        this.formatterOrg(item.children);
      }
    });
  }

  batchCreateOrUpdate(param) {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/dimension/batchCreateOrUpdate`;
    return this.http.post(api, param);
  }

  bgCreate(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/combination/create`;
    return this.http.post(api, param);
  }

  bgUpdate(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/combination/update`;
    return this.http.post(api, param);
  }

  bgDelete(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/combination/delete/${id}`;
    return this.http.post(api, {});
  }

  bgEmptyGroup(): Observable<any> {
    let api = `${this.tenantUrl}/survey/prisma/custom/index/combination/deleteAll/${this.prismaReportDataId}`;
    return this.http.post(api, {});
  }
}
