<ng-container *ngIf="oldDemoshow === 1">
  <div style="position: relative">
    <div
      *ngIf="nametip == '新增'"
      style="position: absolute;display: flex;justify-content: flex-end;top: -70px;right: 60px;"
    >
      <img src="./assets/images/addfactor.png" alt="" />
      <div class="word_sel" (click)="addNewlist()">
        新增题目
      </div>
    </div>
  </div>
  <div class="over-flow-box">
    <ng-container *ngFor="let questionData of batchquestionData; let i = index">
      <div
        style="border: 1px solid #eee;padding: 10px 0 10px 20px;border-radius: 10px;min-height: 530px;"
        [ngClass]="i != 0 ? 'top_div' : ''"
      >
        <div
          style="display: flex;justify-content: space-between;padding-right: 20px;"
        >
          <nz-radio-group
            [(ngModel)]="questionData.isRequire"
            (ngModelChange)="changeStatus($event, i)"
          >
            <label nz-radio [nzValue]="true">必填</label>
            <label nz-radio [nzValue]="false">选填</label>
          </nz-radio-group>

          <i
            nz-icon
            nzType="delete"
            *ngIf="batchquestionData?.length > 1"
            style="font-size: 20px;cursor: pointer;"
            nzTheme="twotone"
            (click)="deletelist(i)"
          ></i>
        </div>
        <form nz-form [formGroup]="questionData.validateForm">
          <div class="question">
            <div class="list1">
              <div class="title">题目编码</div>
              <div>
                <nz-form-item nz-row>
                  <nz-form-control nz-col [nzSpan]="12">
                    <input
                      nz-input
                      placeholder="请输入题目编码"
                      name="code"
                      [disabled]="codeDis"
                      [(ngModel)]="questionData.code"
                      formControlName="code"
                    />
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>
          <div class="question">
            <div class="list1">
              <div class="title">
                题干
                <span class="add-btn" *ngIf="!!id" (click)="toRevision()"
                  ><img src="./assets/images/vew_editor.png" alt="" />
                  题本修订
                </span>
                <!-- <span class="add-btn" nz-tooltip [nzTooltipTitle]="RevisionTemp" [nzTooltipTrigger]="'click'" [nzOverlayClassName]="'revision-temp-box'" [nzTooltipPlacement]="'bottom'"><img src="./assets/images/vew_editor.png" alt="">
                    题本修订
                  </span> -->
              </div>
              <div>
                <nz-form-item nz-row>
                  <nz-form-control nz-col [nzSpan]="12">
                    <input
                      nz-input
                      placeholder="请输入中文题干内容"
                      name="name"
                      [(ngModel)]="questionData.name"
                      formControlName="name"
                    />
                  </nz-form-control>
                  <nz-form-control nz-col [nzSpan]="11" [nzOffset]="1">
                    <input
                      nz-input
                      placeholder="请输入英文题干内容"
                      name="name"
                      [(ngModel)]="questionData.nameEn"
                      formControlName="nameEn"
                    />
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <div class="list2">
              <div class="title">题型</div>
              <div>
                <nz-form-item>
                  <nz-form-control>
                    <nz-select
                      *ngIf="questionData.label !== 'VALID'"
                      class="select"
                      name="type"
                      formControlName="type"
                      nzAllowClear
                      nzPlaceHolder="请选择"
                      [(ngModel)]="questionData.type"
                      (ngModelChange)="typeChange($event, i)"
                    >
                      <nz-option
                        *ngFor="let item of questionTypeList"
                        [nzValue]="item.id"
                        [nzLabel]="item.name.zh_CN"
                        required
                      >
                      </nz-option>
                    </nz-select>
                    <nz-select
                      *ngIf="questionData.label === 'VALID'"
                      class="select"
                      name="type"
                      formControlName="type"
                      nzAllowClear
                      nzPlaceHolder="请选择"
                      [(ngModel)]="questionData.type"
                      (ngModelChange)="typeChange($event, i)"
                    >
                      <nz-option
                        *ngFor="let item of questionTypeList2"
                        [nzValue]="item.id"
                        [nzLabel]="item.name.zh_CN"
                        required
                      >
                      </nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <div
              class="list2"
              *ngIf="reportType != 'CULTURE_INVESTIGATION_RESEARCH'"
            >
              <div class="title" style="position: relative;">
                标签
                <span class="add-btn" (click)="ManageLabel()"
                  ><img src="./assets/images/vew_editor.png" alt="" />
                  标签管理
                </span>
                <div class="mark_label" *ngIf="markshow">
                  <div>
                    <p>标签管理</p>
                    <ul class="mark_title top_title">
                      <li style="width: 190px;padding-left: 13px;">类别</li>
                      <li style="flex: 1;margin-left: 10px;">标签</li>
                      <li style="flex: 2;margin-left: 10px;">更改后名称(中)</li>
                      <li style="flex: 2;margin-left: 10px;">
                        更改后名称(ENG)
                      </li>
                    </ul>
                    <ul
                      class="mark_title top_mark"
                      *ngFor="let item of labelList; let i = index"
                    >
                      <li style="flex: 1;display: flex;align-items: center;">
                        <span>{{ i + 1 }}</span>
                        <nz-select
                          [(ngModel)]="item.group"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <nz-option nzValue="指数" nzLabel="指数"></nz-option>
                          <nz-option nzValue="其它" nzLabel="其它"></nz-option>
                        </nz-select>
                      </li>
                      <li style="flex: 1;margin-left: 10px;">
                        <input
                          [ngModelOptions]="{ standalone: true }"
                          nz-input
                          [disabled]="true"
                          [(ngModel)]="item.labelName"
                        />
                      </li>
                      <li style="flex: 2;margin-left: 10px;">
                        <input
                          [ngModelOptions]="{ standalone: true }"
                          nz-input
                          placeholder="请输入"
                          [(ngModel)]="item.name.zh_CN"
                        />
                      </li>
                      <li style="flex: 2;margin-left: 10px;">
                        <input
                          [ngModelOptions]="{ standalone: true }"
                          nz-input
                          placeholder="ENG"
                          [(ngModel)]="item.name.en_US"
                        />
                      </li>
                    </ul>
                  </div>
                  <div class="but_fotter">
                    <button
                      nz-button
                      [nzSize]="size"
                      nzType="default"
                      nzShape="round"
                      (click)="markClear()"
                    >
                      恢复默认
                    </button>
                    <button
                      nz-button
                      nzType="primary"
                      [nzSize]="size"
                      nzShape="round"
                      (click)="markCommit()"
                    >
                      确认
                    </button>
                  </div>
                </div>
              </div>
              <div>
                <nz-form-item>
                  <nz-form-control>
                    <nz-select
                      class="select"
                      name="label"
                      formControlName="label"
                      nzAllowClear
                      nzPlaceHolder="请选择标签"
                      [(ngModel)]="questionData.label"
                      (ngModelChange)="typeLabelChange($event)"
                      [nzDisabled]="
                        batchquestionData[i].type === 'ESSAY_QUESTION' ||
                        batchquestionData[i].type ===
                          'MULTIPLE_CHOICE_ESSAY_QUESTION'
                      "
                    >
                      <nz-option
                        *ngFor="let item of labelList"
                        [nzValue]="item.parentDimensionCode"
                        [nzLabel]="item.name.zh_CN"
                        required
                      >
                      </nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>

          <div class="question">
            <div class="list1">
              <div class="title">
                维度
                <span class="add-btn" (click)="ManageOption()"
                  ><img src="./assets/images/vew_editor.png" alt="" />
                  维度管理
                </span>
              </div>
              <!-- <div> -->
              <nz-form-item [nzType]="'flex'" [nzJustify]="'space-between'">
                <nz-form-control [nzSpan]="7">
                  <nz-select
                    class="select"
                    name="parentCustomDimension"
                    nzPlaceHolder="一级维度"
                    formControlName="parentCustomDimension"
                    nzAllowClear
                    [(ngModel)]="questionData.parentCustomDimension"
                  >
                    <nz-option
                      *ngFor="let item of ClassAfactor"
                      [nzValue]="item.name.zh_CN"
                      [nzLabel]="item.name.zh_CN"
                      required
                    >
                    </nz-option>
                  </nz-select>
                </nz-form-control>
                <nz-form-control [nzSpan]="7">
                  <nz-select
                    class="select"
                    name="customDimension"
                    formControlName="customDimension"
                    nzAllowClear
                    nzPlaceHolder="二级维度"
                    [(ngModel)]="questionData.customDimension"
                  >
                    <nz-option
                      *ngFor="let item of ClassBfactor"
                      [nzValue]="item.name.zh_CN"
                      [nzLabel]="item.name.zh_CN"
                      required
                    >
                    </nz-option>
                  </nz-select>
                </nz-form-control>
                <nz-form-control [nzSpan]="7">
                  <nz-select
                    class="select"
                    name="childCustomDimension"
                    formControlName="childCustomDimension"
                    nzAllowClear
                    nzPlaceHolder="三级维度"
                    [(ngModel)]="questionData.childCustomDimension"
                  >
                    <nz-option
                      *ngFor="let item of ClassCfactor"
                      [nzValue]="item.name.zh_CN"
                      [nzLabel]="item.name.zh_CN"
                      required
                    >
                    </nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div
              class="list2"
              *ngIf="
                batchquestionData[i].type === 'SCALE' ||
                batchquestionData[i].type === 'SINGLE'
              "
            >
              <div class="title">计算</div>
              <div>
                <nz-form-item>
                  <nz-form-control>
                    <nz-select
                      class="select"
                      name="scope"
                      formControlName="scope"
                      nzAllowClear
                      nzPlaceHolder="请选择计算方式"
                      [(ngModel)]="questionData.scope"
                    >
                      <nz-option
                        *ngFor="let item of scopeList"
                        [nzValue]="item.id"
                        [nzLabel]="item.name"
                        required
                      >
                      </nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>
        </form>

        <div class="question" *ngIf="questionData.type !== 'ESSAY_QUESTION'">
          <div class="list1" *ngIf="isedited">
            <div class="title">
              选项
              <span
                *ngIf="
                  batchquestionData[i].type === 'MULTIPLE_CHOICE_ESSAY_QUESTION'
                "
                >&nbsp;&nbsp;&nbsp;&nbsp;多选个数&nbsp;&nbsp;
                <nz-input-number
                  [nzDisabled]="!this.batchquestionData[i].isRequire"
                  style="width: 60px;"
                  [(ngModel)]="batchquestionData[i].multiSelectMin"
                  [nzMin]="1"
                  [nzStep]="1"
                ></nz-input-number>
                &nbsp;-&nbsp;<nz-input-number
                  style="width: 60px;"
                  [(ngModel)]="batchquestionData[i].multiSelectMax"
                  [nzMin]="batchquestionData[i].multiSelectMin"
                  [nzStep]="1"
                ></nz-input-number>
              </span>
              <span class="add-btn" (click)="onAddOption(i)"
                ><img src="./assets/images/add.png" alt="" /> 添加选项</span
              >
            </div>
          </div>
          <div class="list2" *ngIf="questionData.optionList.length > 0">
            <div
              class="title"
              *ngIf="questionData.type !== 'MULTIPLE_CHOICE_ESSAY_QUESTION'"
            >
              选项分布
              <label
                nz-checkbox
                [(ngModel)]="questionData.totalScoreStatus"
                *ngIf="questionData.type == 'PROPORTION_MULTIPLE'"
                >设置总分</label
              >
            </div>
          </div>
        </div>

        <ng-container *ngIf="questionData.type !== 'ESSAY_QUESTION'">
          <div dragula="VAMPIRESADDPRISMACUSTOMBOOK" [(dragulaModel)]="questionData.optionList">
            <div
              class="question"
              *ngFor="let option of questionData.optionList; let j = index"
              style="margin: 0;"
            >
              <div class="list1" *ngIf="isedited" nz-row>
                <div class="option" nz-col nzSpan="12">
                  <nz-input-group
                    class="option-input"
                    [nzPrefix]="prefixTemplateUser"
                  >
                    <input
                      class="option-input"
                      nz-input
                      placeholder="请输入选项中文内容"
                      [(ngModel)]="option.name.zh_CN"
                      [placeholder]="'请输入选项中文'"
                    />
                  </nz-input-group>
                  <ng-template #prefixTemplateUser>
                    <span class="option-index">{{ j + 1 }}</span></ng-template
                  >
                </div>
                <div class="option" nz-col nzSpan="12">
                  <input
                    class="option-input"
                    nz-input
                    placeholder="请输入选项英文内容"
                    [(ngModel)]="option.name.en_US"
                    [placeholder]="'请输入选项英文'"
                  />
                  <span
                    class="delete-btn"
                    style="margin-left: 5px;"
                    (click)="onDeleteOption(i, j)"
                    >-</span
                  >
                </div>
              </div>
              <ng-container *ngIf="questionData.label !== 'VALID'">
                <div
                  class="list2"
                  *ngIf="
                    !(
                      questionData.type == 'PROPORTION' ||
                      questionData.type == 'PROPORTION_MULTIPLE'
                    )
                  "
                >
                  <ng-container
                    *ngIf="
                      questionData.type === 'MULTIPLE_CHOICE_ESSAY_QUESTION'
                    "
                  >
                    <div
                      style="display: flex; align-items: center; height: 32px;"
                    >
                      <label nz-checkbox [(ngModel)]="option.isEnableOpenAnswer"
                        >开放式回答</label
                      >
                      <nz-radio-group
                        [(ngModel)]="option.isRequireOpenAnswer"
                        *ngIf="option.isEnableOpenAnswer"
                      >
                        <label nz-radio [nzValue]="true">必填</label>
                        <label nz-radio [nzValue]="false">选填</label>
                      </nz-radio-group>
                    </div>
                  </ng-container>
                  <ng-container
                    *ngIf="
                      questionData.type !== 'MULTIPLE_CHOICE_ESSAY_QUESTION'
                    "
                  >
                    <div class="option">
                      <nz-select
                        class="option-select"
                        nzAllowClear
                        nzPlaceHolder="请选择"
                        [(ngModel)]="option.scope"
                        (ngModelChange)="
                          ngModelChangeitem(option, questionData)
                        "
                      >
                        <nz-option
                          *ngFor="let item of option.optionScopeList"
                          [nzValue]="item.id"
                          [nzLabel]="item.name"
                          [(nzDisabled)]="item.disabled"
                          required
                        >
                        </nz-option>
                      </nz-select>
                      <nz-input-number
                        class="weight-input"
                        placeholder="分值"
                        [(ngModel)]="option.value"
                        [(disabled)]="option.inputshow"
                      >
                      </nz-input-number>
                    </div>
                  </ng-container>
                </div>
                <div
                  class="list3"
                  *ngIf="
                    questionData.type == 'PROPORTION' ||
                    questionData.type == 'PROPORTION_MULTIPLE'
                  "
                >
                  <ng-container>
                    <div class="option">
                      <nz-select
                        class="option-select"
                        [(ngModel)]="option.isScoring"
                        nzAllowClear
                        nzPlaceHolder="请选择"
                      >
                        <nz-option [nzValue]="true" nzLabel="计分"></nz-option>
                        <nz-option
                          [nzValue]="false"
                          nzLabel="不计分"
                        ></nz-option>
                      </nz-select>
                      <nz-input-number
                        class="weight-input-1"
                        [nzPlaceHolder]="'最大值'"
                        [(ngModel)]="option.highestScore"
                        [nzStep]="1"
                      ></nz-input-number>
                      <nz-input-number
                        style="margin: 0 5px;"
                        class="weight-input-1"
                        [nzPlaceHolder]="'最小值'"
                        [(ngModel)]="option.lowestScore"
                        [nzStep]="1"
                      ></nz-input-number>
                      <nz-input-number
                        class="weight-input-1"
                        [nzPlaceHolder]="'刻度值'"
                        [nzMin]="0.1"
                        [(ngModel)]="option.stepScore"
                        [nzStep]="0.1"
                      ></nz-input-number>
                    </div>
                  </ng-container>
                </div>
              </ng-container>
              <div class="list2" *ngIf="questionData.label === 'VALID'">
                <ng-container>
                  <div class="option">
                    <nz-select
                      class="option-select"
                      [(ngModel)]="option.isValid"
                      nzAllowClear
                      nzPlaceHolder="请选择"
                    >
                      <nz-option [nzValue]="true" nzLabel="有效"></nz-option>
                      <nz-option [nzValue]="false" nzLabel="无效"></nz-option>
                    </nz-select>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </ng-container>
  </div>
  <ul class="modal_footer" *nzModalFooter>
    <li class="footer_left" (click)="getcommit()">
      确认
    </li>
    <li class="footer_right" (click)="getrelease()">
      取消
    </li>
  </ul>
</ng-container>

<div *ngIf="oldDemoshow === 2" style="display: flex;flex-direction: column;">
  <ul class="newfactor">
    <li>维度管理</li>
    <li class="add_factor" (click)="addfactor()">
      <img src="./assets/images/add.png" alt="" />添加维度
    </li>
  </ul>

  <div>
    <nz-row>
      <nz-col [nzSpan]="8" class="dimension_div">
        <span>类别</span>
      </nz-col>
      <nz-col [nzSpan]="8" class="dimension_div">
        <span>维度名称（中文）</span>
      </nz-col>
      <nz-col [nzSpan]="7" class="dimension_div">
        <span>维度名称（英文）</span>
      </nz-col>
      <nz-col [nzSpan]="1" class="dimension_div"> </nz-col>
    </nz-row>
  </div>
  <div class="over-flow-box">
    <div *ngFor="let item of factorlist; let i = index">
      <div nz-row style="padding:10px 0;">
        <div nz-col [nzSpan]="8" class="dimension_div">
          <nz-select
            style="width: 100%;"
            [(ngModel)]="item.type"
            [nzDisabled]="item.isDefault"
            nzAllowClear
            nzPlaceHolder="Choose"
          >
            <nz-option [nzValue]="'ONE_RANK'" nzLabel="一级维度"></nz-option>
            <nz-option [nzValue]="'TWO_RANK'" nzLabel="二级维度"></nz-option>
            <nz-option [nzValue]="'THREE_RANK'" nzLabel="三级维度"></nz-option>
          </nz-select>
        </div>

        <div nz-col [nzSpan]="8" class="dimension_div">
          <input
            nz-input
            placeholder="请输入"
            [(ngModel)]="item.name.zh_CN"
            required
            [disabled]="item.id"
          />
        </div>
        <div nz-col [nzSpan]="7" class="dimension_div">
          <input
            nz-input
            placeholder="请输入"
            [(ngModel)]="item.name.en_US"
            required
            [disabled]="item.isDefault"
          />
        </div>
        <div nz-col [nzSpan]="1" class="dimension_div" *ngIf="!item.isDefault">
          <span class="delete-btn" (click)="onDeletefactorlist(i, item.id)"
            >-</span
          >
        </div>
      </div>
    </div>
  </div>

  <ul class="modal_footer" *nzModalFooter>
    <li class="footer_left" (click)="getSaveSet()">
      保存
    </li>
    <li class="footer_right" (click)="getDefault()">
      返回上一步
    </li>
  </ul>
</div>

<div *ngIf="oldDemoshow === 3">
  <ul class="newfactor">
    <li>题本修订</li>
    <li>
      <a
        nz-popconfirm
        nzPopconfirmTitle="确定清空当前修订吗？"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="clear()"
        (nzOnCancel)="cancel()"
        >清空</a
      >
    </li>
  </ul>
  <div class="revision-content">
    <div class="left">
      <div class="top">
        <nz-select
          style="width: 180px;"
          [(ngModel)]="selectConditionVal"
          nzPlaceHolder="请选择"
        >
          <nz-option
            *ngFor="let option of revisionConditionObj"
            [nzValue]="option.value"
            [nzLabel]="option.label"
          ></nz-option>
        </nz-select>
        <div class="create" (click)="createRevision()">
          <img src="./assets/images/add.png" alt="" /><span>创建</span>
        </div>
      </div>
      <div>
        <!-- org -->
        <div [hidden]="selectConditionVal !== 1" class="tree treeScroll">
          <nz-tree
            #nzTreeComponent
            nzCheckable
            [nzCheckStrictly]="true"
            [nzData]="orgList"
            [nzExpandAll]="false"
            [nzExpandedKeys]="expandedNodes"
            [nzSearchValue]="searchValue"
            (nzCheckBoxChange)="nzEvent($event)"
          >
          </nz-tree>
        </div>
        <!-- 人口标签 -->
        <div [hidden]="selectConditionVal !== 2" class="tree treeScroll">
          <nz-tree
            #nzTreeComponentRenkou
            nzCheckable
            [nzData]="demographics"
            [nzExpandAll]="false"
            [nzExpandedKeys]="expandedRenNodes"
            [nzSearchValue]="searchRenValue"
            (nzCheckBoxChange)="nzEventRen($event)"
          >
          </nz-tree>
        </div>
        <div [hidden]="selectConditionVal !== 3" class="tree treeScroll">
          <nz-tree
            #nzTreeComponentQues
            nzCheckable
            [nzData]="revisionTypes"
            [nzExpandAll]="false"
            [nzExpandedKeys]="expandedRevisionTypes"
            [nzSearchValue]="searchRenValue"
            (nzCheckBoxChange)="nzEventQues($event)"
          >
          </nz-tree>
        </div>
      </div>
    </div>
    <div class="right">
      <ul class="res-list">
        <li *ngFor="let res of resultList; let resIndex = index">
          <ng-container *ngIf="res.revisionInfoVos.length > 0">
            <nz-collapse>
              <nz-collapse-panel [nzHeader]="collTemp" [nzActive]="true">
                <div class="res-ques-box">
                  <ul
                    class="res-ques"
                    *ngFor="
                      let que of res.revisionInfoVos;
                      let queIndex = index
                    "
                  >
                    <li>
                      <div style="margin-bottom: 5px;">
                        <div style="height: 100%; margin-right: 10px;">
                          {{ que.type === "QUESTION" ? "题干" : "选项" }}
                        </div>
                        <input
                          nz-input
                          disabled
                          style="width: 370px; height: 30px;"
                          [(ngModel)]="que.name.zh_CN"
                        />
                      </div>
                      <div>
                        <div
                          style="height: 100%; margin-right: 10px;"
                          class="delete-icon-box"
                        >
                          <div
                            class="delete-icon"
                            nz-popconfirm
                            [nzPopconfirmTitle]="
                              '确定删除当前' +
                              (que.type === 'QUESTION' ? '题干吗?' : '选项吗?')
                            "
                            nzPopconfirmPlacement="bottom"
                            (nzOnConfirm)="deleteOne(resIndex, queIndex)"
                            (nzOnCancel)="cancel()"
                          ></div>
                        </div>
                        <input
                          nz-input
                          disabled
                          style="width: 370px; height: 30px;"
                          [(ngModel)]="que.name.en_US"
                        />
                      </div>
                    </li>
                    <li class="center">调整后</li>
                    <li>
                      <div style="margin-bottom: 5px;">
                        <input
                          nz-input
                          style="width: 370px; height: 30px;"
                          placeholder="请输入"
                          [(ngModel)]="que.anotherName.zh_CN"
                        />
                      </div>
                      <div>
                        <input
                          nz-input
                          style="width: 370px; height: 30px;"
                          placeholder="请输入"
                          [(ngModel)]="que.anotherName.en_US"
                        />
                      </div>
                    </li>
                  </ul>
                </div>
              </nz-collapse-panel>
            </nz-collapse>
            <ng-template #collTemp>
              <div class="res-box">
                <span class="type-name">结果{{ resIndex + 1 }}</span>
                <div class="content-text" *ngIf="res.organizationId">
                  {{ res.organizationName.zh_CN }}
                </div>
                <!-- <nz-select [(ngModel)]="selectedValue" nzAllowClear nzPlaceHolder="Choose">
                  <nz-option nzValue="jack" nzLabel="Jack"></nz-option>
                  <nz-option nzValue="lucy" nzLabel="Lucy"></nz-option>
                  <nz-option nzValue="disabled" nzLabel="Disabled" nzDisabled></nz-option>
                </nz-select> -->
                <a
                  nz-dropdown
                  *ngIf="res.organizationId"
                  nzTrigger="click"
                  [nzDropdownMenu]="menu"
                >
                  {{
                    res.organizationScope === "CURRENT_AND_BELOW"
                      ? "及以下"
                      : "仅当前"
                  }}
                  <i nz-icon nzType="down"></i>
                </a>
                <nz-dropdown-menu #menu="nzDropdownMenu">
                  <ul nz-menu>
                    <li
                      nz-menu-item
                      (click)="res.organizationScope = 'ONLY_CURRENT'"
                    >
                      {{ "仅当前" }}
                    </li>
                    <li
                      nz-menu-item
                      (click)="res.organizationScope = 'CURRENT_AND_BELOW'"
                    >
                      {{ "及以下" }}
                    </li>
                  </ul>
                </nz-dropdown-menu>
                <div>
                  <span *ngFor="let demo of res.demographics">
                    <span *ngIf="res.organizationId" style="color: #64A2F7;"
                      >且</span
                    >
                    {{
                      demo.name.zh_CN +
                        (demo.children.length !== 0
                          ? " - " + demo.children[0].name.zh_CN
                          : "")
                    }}
                  </span>
                </div>
              </div>
            </ng-template>
          </ng-container>
        </li>
      </ul>
    </div>
  </div>

  <ul class="modal_footer" *nzModalFooter>
    <li class="footer_left" (click)="getSaveRevision()">
      保存
    </li>
    <li class="footer_right" (click)="getDefault()">
      返回上一步
    </li>
  </ul>
</div>

<ng-template #RevisionTemp>
  <div>
    <ul class="revision-header-box">
      <li class="revision-title">题本修订</li>
      <li class="revision-handler" (click)="createRevision()">
        <img src="./assets/images/prisma/revision-add.png" alt="" />创建
      </li>
    </ul>

    <div class="scro-rev-box">
      <div class="revision-condition">
        <div class="box1">
          <span>条件</span>
          <nz-select
            style="width: 180px; margin-right: 10px;"
            [(ngModel)]="selectedValue"
            nzAllowClear
            nzSize="large"
            nzPlaceHolder="请选择组织"
          >
            <nz-option nzValue="jack" nzLabel="Jack"></nz-option>
            <nz-option nzValue="lucy" nzLabel="Lucy"></nz-option>
            <nz-option
              nzValue="disabled"
              nzLabel="Disabled"
              nzDisabled
            ></nz-option>
          </nz-select>
          <nz-select
            style="width: 180px;"
            [(ngModel)]="selectedValue"
            nzAllowClear
            nzSize="large"
            nzPlaceHolder="请选择人口标签"
          >
            <nz-option nzValue="jack" nzLabel="Jack"></nz-option>
            <nz-option nzValue="lucy" nzLabel="Lucy"></nz-option>
            <nz-option
              nzValue="disabled"
              nzLabel="Disabled"
              nzDisabled
            ></nz-option>
          </nz-select>
        </div>
        <div class="box2">
          <span>对象</span>
          <nz-select
            style="width: 370px;"
            [(ngModel)]="selectedValue"
            nzAllowClear
            nzSize="large"
            nzPlaceHolder="请选择"
          >
            <nz-option nzValue="jack" nzLabel="Jack"></nz-option>
            <nz-option nzValue="lucy" nzLabel="Lucy"></nz-option>
            <nz-option
              nzValue="disabled"
              nzLabel="Disabled"
              nzDisabled
            ></nz-option>
          </nz-select>
        </div>
      </div>
      <ul class="res-list">
        <li>
          <div class="res-box">
            <span class="type-name">结果</span>
            <div class="content-text">{{ "条件" }}</div>
            <a nz-dropdown nzTrigger="click" [nzDropdownMenu]="menu">
              {{ "及以下" }}
              <i nz-icon nzType="down"></i>
            </a>
            <nz-dropdown-menu #menu="nzDropdownMenu">
              <ul nz-menu>
                <li nz-menu-item>{{ "及以下" }}</li>
                <li nz-menu-item>{{ "及以下" }}</li>
              </ul>
            </nz-dropdown-menu>
          </div>
          <div class="res-ques-box">
            <ul class="res-ques">
              <li>
                <div style="margin-bottom: 5px;">
                  <div style="height: 100%; margin-right: 10px;">题本</div>
                  <input
                    nz-input
                    disabled
                    style="width: 370px; height: 30px;"
                    placeholder="Basic usage"
                    [(ngModel)]="value"
                  />
                </div>
                <div>
                  <div
                    style="height: 100%; margin-right: 10px;"
                    class="delete-icon-box"
                  >
                    <div class="delete-icon"></div>
                  </div>
                  <input
                    nz-input
                    disabled
                    style="width: 370px; height: 30px;"
                    placeholder="Basic usage"
                    [(ngModel)]="value"
                  />
                </div>
              </li>
              <li class="center">调整后</li>
              <li>
                <div style="margin-bottom: 5px;">
                  <input
                    nz-input
                    style="width: 370px; height: 30px;"
                    placeholder="请输入"
                    [(ngModel)]="value"
                  />
                </div>
                <div>
                  <input
                    nz-input
                    style="width: 370px; height: 30px;"
                    placeholder="请输入"
                    [(ngModel)]="value"
                  />
                </div>
              </li>
            </ul>
          </div>
        </li>
      </ul>
    </div>
    <ul class="revision-footer">
      <button nz-button nzType="primary" nzGhost>清空</button>
      <button nz-button nzType="primary">确认</button>
    </ul>
  </div>
</ng-template>
