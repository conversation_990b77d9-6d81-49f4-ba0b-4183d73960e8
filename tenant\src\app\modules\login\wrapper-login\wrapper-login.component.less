.content {
  width: 100%;
  height: 700px;
  background: rgba(0, 0, 0, 0.5);
  // background: url("../../../../assets/images/login_back.png") no-repeat; background-size: 100% auto ;
}

::ng-deep .vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}

::ng-deep .vertical-center-modal .ant-modal {
  top: 0;
}

.s1 {
  min-height: 600px;
  background: url(../../../../assets/images/index_bg.png) no-repeat top;
  background-size: cover;
  padding-top: 0.1px;

  .text {
    position: relative;
    margin: 0 auto;
    padding: 100px 0;

    h3 {
      font-size: 32px;
      letter-spacing: 4px;
      font-weight: 500;
      color: #ffffff;
    }

    h5 {
      font-size: 16px;
      letter-spacing: 2px;
      font-weight: bold;
      color: #ffffff;
      margin: 35px 0 93px;
    }

    button {
      width: 160px;
      border-radius: 19px;
      height: 38px;
      background-color: #ffffff;
      color: #409EFF;
      position: absolute;
      top: 45%;
      left: 0;
    }
  }
}