<div class="content client-width">
    <layout-header class="vx-default__header" [showtitle]="showtitle" [tourist]="tourist" [isNeedLogin]="isNeedLogin"></layout-header>

    <section class="s1">
        <div class="client-width">
            <div class="s1-l">
                <h5>盘点101 </h5>
                <p>采用科学建构的测评模型，依托系统线上化的操作流程，全面且有效地评估员工能力水平。提供“自评”和“他评”两种可选测评形式，助力企业解决人才盘点、选拔晋升、组织诊断等多样化测评与调研需求。 </p>
                <button *ngIf="!tourist" class="btn" (click)="downloadReport()">下载报告样例</button>
                <button *ngIf="tourist" class="btn" routerLink="/new-activity">即刻体验</button>
            </div>
            <div class="s1-r">
                <img src="assets/images/v_ren_0.png" alt="">
            </div>
        </div>
    </section>

    <section class="s2">
        <div class="client-width">
            <h5>年年做盘点，人才状况还是雾里看花。</h5>
            <img src="assets/images/v_ren_1.png" alt="">
        </div>
    </section>

    <section class="s3" style="display: flex;justify-content: center">
        <div class="client-width">
            <h5>1套标准流程 快速敏捷0耗时</h5>
            <img src="assets/images/v_ren_2.png" alt="">
        </div>
    </section>

    <section class="s4" style="display: flex;justify-content: center;padding-top: 30px;margin-top: 30px;padding-top: 100px;">
        <div class="client-width" style="display: flex;justify-content: center;">
            <img src="assets/images/v_ren_3.png" alt="">
        </div>
    </section>

    <section class="s2">
        <div class="client-width" style="display: flex;justify-content: center;">
            <div style="width: 160px;
            line-height: 38px;color: #fff;
            background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 19px;cursor: pointer;" (click)="gotoHome('create')">
                即刻体验
            </div>
        </div>
    </section>

    <app-footer></app-footer>
</div>