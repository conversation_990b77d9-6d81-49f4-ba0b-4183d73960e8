<!-- 设置自邀请人数 -->
<nz-drawer nzWrapClassName="self-invite-drawer" [nzMaskClosable]="false" [nzVisible]="isVisible" nzPlacement="right" [nzWidth]="1000" nzTitle="设置自邀请人数" (nzOnClose)="handleCancel()" [nzBodyStyle]="{ height: 'calc(100% - 55px)', overflow: 'auto', 'padding-bottom': '53px' }">
    <div class="selfInvite">
        <nz-table #basicTable [nzData]="listOfData" [nzShowPagination]="false">
            <thead>
                <tr>
                    <th><b>角色名称</b></th>
                    <th><b>角色名称 (英)</b></th>
                    <th><b>自邀请人数</b></th>
                    <th><b>不设上限</b></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let data of basicTable.data">
                    <td>
                        <nz-select class="choose" [(ngModel)]="data.roleId" placeholder="请选择" [nzDisabled]="true" style="width: 260px;">
                            <nz-option [nzValue]="option.id" [nzLabel]="option.name?.zh_CN" *ngFor="let option of rolelistfilter">
                            </nz-option>
                        </nz-select>
                    </td>
                    <td>
                        <nz-select class="choose" [(ngModel)]="data.roleId" placeholder="请选择" [nzDisabled]="true" style="width: 260px;">
                            <nz-option [nzValue]="option.id" [nzLabel]="option.name?.en_US" *ngFor="let option of rolelistfilter">
                            </nz-option>
                        </nz-select>
                        <td>
                            <nz-input-number [(ngModel)]="data.min" [nzMin]="0" [nzStep]="1" (ngModelChange)="changeMin($event, data.roleId)"></nz-input-number>
                            <span class="ml-8 mr-8">-</span>
                            <nz-input-number [(ngModel)]="data.max" [nzMin]="0" [nzStep]="1" (ngModelChange)="changeMax($event, data.roleId)" [nzDisabled]="data.disableLimit"></nz-input-number>
                        </td>
                        <td>
                            <label class="ml-20" nz-checkbox [(ngModel)]="data.disableLimit" (ngModelChange)="changeDisableLimit($event, data.roleId)"></label>
                        </td>
                </tr>
            </tbody>
        </nz-table>
        <div class="selfInvite_footer">
            <button type="button" (click)="handleOk()" class="ant-btn ant-btn-primary"><span>确认</span></button>
        </div>
    </div>
</nz-drawer>
