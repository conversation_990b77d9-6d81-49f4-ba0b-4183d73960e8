import { Injectable } from "@angular/core";
import { HttpHeaders, HttpClient } from "@angular/common/http";
import { Observable } from "rxjs";
import { filter, map } from "rxjs/operators";

import { HttpResponseWrapper } from "@shared/interfaces/http-common";
import { SurveyInvestigatorPerson } from "@src/modules/project-manage/project-manage";

@Injectable({
  providedIn: "root",
})
export class ProjectManageService {
  tenantUrl: string;
  private httpOptions = {};
  private httpOptionsload = {};
  questionTypeList: any[];
  tipsTypelist: any[];

  constructor(private http: HttpClient) {
    this.tenantUrl = "/tenant-api";
    this.httpOptions = {
      headers: new HttpHeaders({ "Content-Type": "application/json" }),
    };
    this.httpOptionsload = {
      headers: new HttpHeaders({ "Content-Type": "application/json" }),
    };

    this.questionTypeList = [
      { id: "SINGLE", name: { zh_CN: "单选" }, value: "0" },
      // { id: 'JUDGE', name: { zh_CN: '判断' }, value: '0' },
      { id: "SCALE", name: { zh_CN: "量表" }, value: "0" },
      // { id: 'FORCED_ELECTION', name: { zh_CN: '迫选' }, value: '0' },
      // { id: 'FOUR_FORCED_ELECTION', name: { zh_CN: '四项迫选题' }, value: '0' },
      { id: "ESSAY_QUESTION", name: { zh_CN: "开放题" }, value: "0" },
      { id: "PROPORTION", name: { zh_CN: "滑块" }, value: "0" },
      { id: "PROPORTION_MULTIPLE", name: { zh_CN: "多级比重" }, value: "0" },
      {
        id: "MULTIPLE_CHOICE_ESSAY_QUESTION",
        name: { zh_CN: "多选开放题" },
        value: "0",
      },
    ];
    this.tipsTypelist = [
      {
        id: "SINGLE",
        name: { zh_CN: "单选", en_US: "Single Choice" },
        value: "0",
      },
      // { id: 'JUDGE', name: { zh_CN: '判断', en_US: 'True or False' }, value: '0' },
      { id: "SCALE", name: { zh_CN: "量表", en_US: "Scale" }, value: "0" },
      // { id: 'FORCED_ELECTION', name: { zh_CN: '迫选', en_US: 'Forced Choice' }, value: '0' },
      // { id: 'FOUR_FORCED_ELECTION', name: { zh_CN: '四项迫选题', en_US: '' }, value: '0' },
      {
        id: "ESSAY_QUESTION",
        name: { zh_CN: "开放", en_US: "Open Question" },
        value: "0",
      },
      {
        id: "PROPORTION",
        name: { zh_CN: "滑块", en_US: "Single-level Weighting Question" },
        value: "0",
      },
      {
        id: "PROPORTION_MULTIPLE",
        name: { zh_CN: "多级比重", en_US: "Multi-level Weighting Question" },
        value: "0",
      },
      {
        id: "MULTIPLE_CHOICE_ESSAY_QUESTION",
        name: { zh_CN: "多选开放", en_US: "Multiple Choice Open Question" },
        value: "0",
      },
    ];
  }

  public calculateNum(data: string, num, type?) {
    let allNum = data.length;
    console.log(allNum, data);

    let startNum = data.split("{{开始日期}}").join().length !== allNum; // 5
    let endNum = data.split("{{结束日期}}").join().length !== allNum; // 5
    let linkNum = data.split("{{链接}}").join().length !== allNum; //
    let codeNum = data.split("{{验证码}}").join().length !== allNum;
    let strAll = data
      .split("{{开始日期}}")
      .join("")
      .split("{{结束日期}}")
      .join("")
      .split("{{链接}}")
      .join("")
      .split("{{验证码}}")
      .join("");
    let hrefNum = 14;
    if (location.origin === "https://sagittarius-stg.vxhcm.com") {
      hrefNum = hrefNum + "https://cs-stg.vxhcm.com".length;
    } else if (location.origin === "https://sagittarius.vxhcm.com") {
      hrefNum = hrefNum + "https://cs.vxhcm.com".length;
    } else {
      hrefNum = hrefNum + location.origin.length;
    }
    let strAllNum = strAll.length;
    if (startNum) strAllNum = strAllNum + 5;
    if (endNum) strAllNum = strAllNum + 5;
    if (linkNum) strAllNum = strAllNum + hrefNum;
    // "【肯耐珂萨】诚邀您参与测评调研。请于 12-08和12-08期间点击http://sag-qa.knxdevelop.com/s/1Uf9ZFpwjTS 开始测评。回复TD退订"
    if (codeNum) strAllNum = strAllNum + (type === "prov" ? 5 : num);
    return strAllNum;
  }
  public countMoney(numPeople: any, strNum: any) {
    let strCoefficient = Math.ceil(strNum / 50);
    let yuan: any = (strCoefficient * 0.1).toFixed(1);
    const countMoney = (numPeople * yuan).toFixed(1);
    numPeople = numPeople.toFixed(0);
    return {
      countMoney,
      yuan,
      numPeople,
    };
  }

  public getProjectList(): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listProject`;
    return this.http.get(url);
  }

  public getPagedProjectList(
    name?: string,
    status?: string,
    pageIndex?: number,
    pageSize?: number
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listProjectByPage`;
    let param = {
      name: name,
      page: {
        current: pageIndex,
        pages: 0,
        searchCount: true,
        size: pageSize,
      },
      status: status,
    };
    return this.http.post(url, param);
  }
  public getPagedProjectList1(params: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listProjectByPage`;
    return this.http.post(url, params);
  }
  public projectSummary(): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/projectSummary`;
    return this.http.get(url);
  }

  // 穿透题相关
  // 获取穿透/跳过题目
  public listByTypesAndName(params: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/listByTypesAndName`;
    return this.http.post(url, params);
  }

  public exportThrough(questionnaireId: string): Observable<any> {
    // 导出
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/question/pierceThrough/export?questionnaireId=${questionnaireId}`;
    return this.http.get(url, this.httpOptionsload);
  }

  public importThrough(
    formData: any,
    questionnaireId: string
  ): Observable<any> {
    // 导入
    const url = `${this.tenantUrl}/survey/question/pierceThrough/import?questionnaireId=${questionnaireId}`;
    return this.http.post(url, formData);
  }

  public deleteSurveyPierceThrough(questionnaireId): Observable<any> {
    // 删除所有穿透题
    const url = `${this.tenantUrl}/survey/question/deleteAllSurveyPierceThrough?questionnaireId=${questionnaireId}`;
    return this.http.get(url);
  }
  public deleteOneSurveyPierceThrough(params): Observable<any> {
    // 删除某个穿透题
    const url = `${this.tenantUrl}/survey/question/deleteSurveyPierceThrough`;
    return this.http.post(url, params);
  }
  public showPierceThroughCombination(questionnaireId): Observable<any> {
    // 查询穿透题
    const url = `${this.tenantUrl}/survey/question/showPierceThroughCombination?questionnaireId=${questionnaireId}`;
    return this.http.get(url);
  }

  public getQuestionsByProjId(projectId: string): Observable<any> {
    //获取穿透状态
    const url = `${this.tenantUrl}/survey/questionnaire/listSurveyQuestionnaireAndQuestion/${projectId}`;
    return this.http.get(url);
  }

  public createSurveyStandardPierceThrough(params: {
    parentQuestionId: string;
    questionId: string;
    surveyStandardOptions?: any[];
  }): Observable<any> {
    // 创建穿透题
    const url = `${this.tenantUrl}/survey/question/createSurveyStandardPierceThrough`;
    return this.http.post(url, params);
  }

  // 选择项目后，获取项目人员列表
  public getUserList(projectId: string): Observable<any> {
    const url =
      `${this.tenantUrl}/survey/person/listByProjectId?projectId=` + projectId;
    return this.http.post(url, this.httpOptions);
  }

  // public getPagedUserList(projectId: string, pageIndex: number, pageSize: number, sort?: string, searchField?: string, conditions?: any): Observable<any> {
  //   const url = `${this.tenantUrl}/survey/person/listByPage";
  // let param = {
  //   "answerStatus": conditions.answerStatus,
  //   "orderBy": conditions.orderBy,
  //   "searchField": searchField,
  //   "page": {
  //     "current": pageIndex,
  //     "pages": 0,
  //     "searchCount": true,
  //     "size": pageSize
  //   },
  //   "projectId": projectId
  // };
  //   return this.http.post(url, param);
  // }
  public getPagedUserList(params: {
    answerStatus: any[];
    orderBy: string;
    searchField: string;
    page: any;
    projectId: string;
  }): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listByPage`;
    return this.http.post(url, params);
  }

  // 如果选择的是360类型项目，获取360项目人员列表
  public get360ProjectUserList(
    projectId: string
  ): Observable<SurveyInvestigatorPerson[]> {
    const url = `${this.tenantUrl}/survey/person/listSurveyInvestigatorPerson/${projectId}`;
    return this.http.get(url).pipe(
      filter((res: HttpResponseWrapper) => res.result.code === 0),
      map((res) => res.data)
    );
  }

  // 360人员列表 分页接口
  public get360ProjectPagedUserList(
    projectId: string,
    pageIndex: number,
    pageSize: number,
    searchField?: string,
    orderBy?: string
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listSurveyInvestigatorPersonByPage`;
    let param = {
      searchField: searchField,
      page: {
        current: pageIndex,
        pages: 0,
        searchCount: true,
        size: pageSize,
      },
      orderBy: orderBy,
      projectId: projectId,
    };
    return this.http.post(url, param);
  }

  public listByProjectId(projectId: string): Observable<any> {
    const url =
      `${this.tenantUrl}/survey/questionnaire/listByProjectId/` + projectId;
    return this.http.get(url);
  }

  public listAllUsedQuestionnaireNames(): Observable<any> {
    // 查询工具名称
    const url = `${this.tenantUrl}/survey/questionnaire/listAllUsedQuestionnaireNames`;
    return this.http.get(url);
  }

  // 修改成post
  public updateProjectStatus(params: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/updateStatus`;
    return this.http.post(url, params, this.httpOptions);
  }

  // 标准发送邮件
  public sendEmail(emailJson: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/sendEmail`;
    return this.http.post(url, emailJson, this.httpOptions);
  }

  public sendMessage(messageJson: any): Observable<any> {
    let api = this.tenantUrl + "/survey/project/sendSMS";
    return this.http.post(api, messageJson, this.httpOptions);
  } //360短信发送 httpOptions 是个空对象

  public sendEmail360(emailJson: any): Observable<any> {
    let api = this.tenantUrl + "/survey/project/sendBehaviorEmail";
    return this.http.post(api, emailJson, this.httpOptions);
  } //360邮件发送

  public sendMessage360(messageJson: any): Observable<any> {
    let api = this.tenantUrl + "/survey/project/sendBehaviorSMS";

    return this.http.post(api, messageJson, this.httpOptions);
  } //360短信发送 httpOptions 是个空对象

  public sendBehaviorWxWorkMsg(messageJson: any): Observable<any> {
    let api = this.tenantUrl + "/survey/project/sendBehaviorWxWorkMsg";
    return this.http.post(api, messageJson, this.httpOptions);
  } //360短信发送 httpOptions 是个空对象

  // 钉钉消息发送
  public sendDingMsg(messageJson: any): Observable<any> {
    let api = this.tenantUrl + "/survey/project/sendDingMsg";
    return this.http.post(api, messageJson, this.httpOptions);
  }

  // 飞书消息发送
  public sendFeiShuMsg(messageJson: any): Observable<any> {
    let api = this.tenantUrl + "/survey/project/sendFeishuMsg";
    return this.http.post(api, messageJson, this.httpOptions);
  }

  public generateQrCode(
    projectId: string,
    answerCodeTypeEnum: any,
    json: any
  ): Observable<any> {
    let api =
      this.tenantUrl +
      `/survey/project/generateCode/${projectId}/${answerCodeTypeEnum}`;
    return this.http.post(api, json);
  }

  // public generateQrCode(projectId: string, json: any): Observable<any> {
  //   const url = `${this.tenantUrl}/survey/project/generateCode/" + projectId;
  //   return this.http.post(url, json);
  // }

  public getTenantConfigList(json: {
    dimensionName: string;
    projectId: any;
    questionnaireId: string;
  }): Observable<any> {
    const url = this.tenantUrl + "/sagittarius/report/package/list";
    return this.http.post(url, json);
  }

  public updateTenantConfigList(json: any): Observable<any> {
    const url = this.tenantUrl + "/sagittarius/report/package/updateBatch";
    return this.http.post(url, json, this.httpOptions);
  } //修改成post

  //360邮件
  public showEmailDemo(type: string, purpose?: string): Observable<any> {
    let url = `${this.tenantUrl}/survey/emailTemplate/getDefault?type=${type}`;
    if (purpose) url = url + "&purpose=" + purpose;
    return this.http.get(url);
  }

  //360短信
  public showMessDemo(
    answerCodeType: string,
    projectId: string,
    purpose?: string
  ): Observable<any> {
    let url = `${this.tenantUrl}/survey/project/showSMSDemo?answerCodeType=${answerCodeType}&projectId=${projectId}`;
    if (purpose) url = url + `&purpose=${purpose}`;
    return this.http.get(url);
  }
  //360Vx
  public showWxWorkMsgDemo(
    answerCodeType: string,
    projectId: string,
    purpose?: string
  ): Observable<any> {
    let url = `${this.tenantUrl}/survey/project/showWxWorkMsgDemo?answerCodeType=${answerCodeType}&projectId=${projectId}`;
    if (purpose) url = url + "&purpose=" + purpose;
    return this.http.get(url);
  }

  // 钉钉消息示例
  public showDingMsgDemo(
    answerCodeType: string,
    projectId: string,
    purpose?: string
  ): Observable<any> {
    let url = `${this.tenantUrl}/survey/project/showDingMsgDemo?answerCodeType=${answerCodeType}&projectId=${projectId}`;
    if (purpose) url = url + "&purpose=" + purpose;
    return this.http.get(url);
  }
  // 飞书消息示例
  public showFeiShuMsgDemo(
    answerCodeType: string,
    projectId: string,
    purpose?: string
  ): Observable<any> {
    let url = `${this.tenantUrl}/survey/project/showFeishuMsgDemo?answerCodeType=${answerCodeType}&projectId=${projectId}`;
    if (purpose) url = url + "&purpose=" + purpose;
    return this.http.get(url);
  }

  //prisma 邮件
  public prismashowEmailDemo(
    answerCodeType: string,
    projectId: string,
    surveyType?: any
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/showEmailDemo?surveyType=${surveyType}&answerCodeType=${answerCodeType}&projectId=${projectId}`;
    return this.http.get(url);
  }
  //prisma 短信
  public prismashowMessDemo(
    answerCodeType: string,
    projectId: string,
    surveyType?: string
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/showSMSDemo?surveyType=${surveyType}&answerCodeType=${answerCodeType}&projectId=${projectId}`;
    return this.http.get(url);
  }

  public exportmail(type: any): Observable<any> {
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/project/exportmail?type=${type}`;
    return this.http.post(url, {}, this.httpOptionsload);
  }

  public createSurveyInvestigator(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/createSurveyInvestigator`;
    return this.http.post(url, json);
  }

  public createSurveyRole(json: {
    projectId: any;
    surveyRoles: any[];
  }): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/createSurveyRole`;
    return this.http.post(url, json);
  }

  public listNotInvitedList(projectId: any, type: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listNotInvitedByProjectId/${projectId}/${type}`;
    return this.http.get(url);
  }

  public captchacodeurl(
    projectId: string,
    answerCodeTypeEnum: string
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/generateCode/${projectId}/${answerCodeTypeEnum}`;
    return this.http.post(url, {});
  } //lxy 360新增邀请，测评者分组

  public listInvestigatorGroup(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listAllByProjectId/${projectId}`;
    return this.http.get(url);
  } //lxy 360新增验证码的邀请码接口

  public listInvestigatorGroupnew(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listAllByPage`;
    return this.http.post(url, json);
  }

  public listAllInvestigators(
    projectId: string,
    type: string
  ): Observable<any> {
    let url = `${this.tenantUrl}/survey/person/listAllInvestigators/${projectId}`;
    if (type) {
      url = url + `?type=` + type;
    }
    return this.http.get(url);
  } //ldx 360评价关系邀请

  // 360邀请测评者上传excel数据
  public getPerson(
    json: any,
    id: string,
    isCustomRoleWeight: boolean
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/read?projectId=${id}&isCustomRoleWeight=${isCustomRoleWeight}`;
    return this.http.post(url, json);
  }

  // 360邀请测评者上传excel数据
  public getMutualEvaluationPerson(
      json: any,
      id: string,
      isCustomRoleWeight: boolean
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/importMutualEvaluation?projectId=${id}&isCustomRoleWeight=${isCustomRoleWeight}`;
    return this.http.post(url, json);
  }

  // 360下载邀请模板
  public exportPerson(
    projectId: any,
    isCustomRoleWeight: any
  ): Observable<any> {
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/person/export?projectId=${projectId}&isCustomRoleWeight=${isCustomRoleWeight}`;
    return this.http.post(url, {}, this.httpOptionsload);
  }

  // 360下载评价关系
  public Relationship(
    projectId: string,
    isCustomRoleWeight: boolean
  ): Observable<any> {
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/person/exportEvaluationRelationship/${projectId}?isCustomRoleWeight=${isCustomRoleWeight}`;
    return this.http.get(url, this.httpOptionsload);
  }

  // 360下载评价关系
  public MutualEvaluationRelationship(
      projectId: string,
      isCustomRoleWeight: boolean
  ): Observable<any> {
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/person/exportMutualEvaluationTemplate?projectId=${projectId}&isCustomRoleWeight=${isCustomRoleWeight}`;
    return this.http.get(url, this.httpOptionsload);
  }

  // 360角色列表
  public listRole(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listSurveyRole/${projectId}`;
    return this.http.get(url);
  }

  // 更新活动是否显示
  public updateProject(json: { id: string; isShow: boolean }): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/updateProject`;
    return this.http.post(url, json);
  } //修改成post

  // 获取活动设置内容
  public getProjectSetting(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/getProjectSetting/${projectId}`;
    return this.http.get(url);
  }

  // 修改活动高级设置
  public updateProjectSetting(json: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/updateProjectSetting`;
    return this.http.post(url, json);
  } //修改成post

  // 获取题本列表
  public listByQuestionnaireId(
    id: string,
    condition?: string,
    desc?: number
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/listByQuestionnaireId/${id}?desc=${desc}&condition=${condition}`;
    return this.http.get(url);
  }

  // 新增题本
  public createQuestion(json: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/batchCreate`;
    return this.http.post(url, json);
  }

  // 更新题本
  public updateQuestion(json: { questionnaireId: string }): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/update`;

    return this.http.post(url, json);
  }

  // 删除题本
  public deleteQuestion(id: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/delete/${id}`;
    return this.http.post(url, this.httpOptions);
  } //修改成post

  // 获取题本
  public getQuestion(id: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/get/${id}`;
    return this.http.get(url);
  }

  // 批量删除题本
  public batchDeleteQuestion(json: any[]): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/batchDelete`;
    return this.http.post(url, json);
  }
  // 自定义题本导出模板
  public customExport(edittype: any, questionnaireId: any): Observable<any> {
    this.httpOptions = { responseType: "Blob", observe: "response" };
    let url =
      `${this.tenantUrl}/survey/question/customExport?questionnaireId=` +
      questionnaireId;
    if (edittype === "STAND") {
      url =
        `${this.tenantUrl}/survey/question/behaviorStandardExport?questionnaireId=` +
        questionnaireId;
    }
    return this.http.get(url, this.httpOptions);
  }
  // 导出题目列表
  public exportQuestionList(questionnaireId: any): Observable<any> {
    this.httpOptions = { responseType: "Blob", observe: "response" };
    const url =
      `${this.tenantUrl}/survey/question/exportList?questionnaireId=` +
      questionnaireId;
    return this.http.get(url, this.httpOptions);
  }

  // 批量上传题目
  public customImport(json: any, questionnaireId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/customImport?questionnaireId=${questionnaireId}`;
    return this.http.post(url, json);
  }

  public standardImport(json: any, questionnaireId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/behaviorStandardImport?questionnaireId=${questionnaireId}`;
    return this.http.post(url, json);
  }

  //敬业度发布活动
  public PublishPrisma(projectId: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/publishProject/${projectId}`;
    return this.http.get(url);
  }

  // 敬业度下载邀请模板
  public exportprismaPerson(projectId: string): Observable<any> {
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/project/exportPrismaPersonExcel?projectId=${projectId}`;
    return this.http.post(url, {}, this.httpOptionsload);
  }

  // 360要请评估人
  public exportInvestigatorInviteExcel(projectId: string): Observable<any> {
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/project/exportInvestigatorInviteExcel?projectId=${projectId}`;
    return this.http.post(url, {}, this.httpOptionsload);
  }

  // 敬业度邀请测评者上传excel数据
  public getprismaPerson(json: any, id: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/readPrismaPersonExcel?projectId=${id}`;
    return this.http.post(url, json);
  }

  // 360评估人
  public readInvestigatorInviteExcel(json: any, id: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/readInvestigatorInviteExcel?projectId=${id}`;
    return this.http.post(url, json);
  }

  // 短信、邮件 、企业微信 获取所有未邀请人
  public listNotInvitedprisma(json: string[], type: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listPrismaNotInvitedByProjectId/${type}`;
    return this.http.post(url, json);
  }

  public listNotInvitedprismanew(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listPrismaNotInvitedByPage`;
    return this.http.post(url, json);
  }
  //邀请码获取所有未邀请人
  public listNotInvitedprismacode(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listSurveyOrganizationCaptcha/${projectId}`;
    return this.http.get(url);
  }

  //复制活动
  public copyproject(projectId: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/copyProject?projectId=${projectId}`;
    return this.http.get(url);
  }

  //更新问卷
  updatePaper(param: any): Observable<any> {
    return this.http.request(
      "get",
      `${this.tenantUrl}/survey/project/updateProjectReportSetting?projectId=${param}`
    );
  }

  //确认关联任务
  confirmRelation(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/confirmRelationPermission`;
    return this.http.post(url, json);
  }

  //360评价关系新接口
  newconfirmRelation(json: {
    projectId: any;
    page:
      | {
          // 批量上传题目
          size: number;
          current: number;
        }
      | { size: number; current: number }
      | { size: number; current: number };
  }): Observable<any> {
    const url = `${this.tenantUrl}/survey/person/listContainsPersonByProjectId`;
    return this.http.post(url, json);
  }

  //tip对标岗位
  listTipjobs(id: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/report/template/sag/listJob?questionnaireId=${id}`;
    return this.http.get(api);
  }
  //tip对标岗位-reportTemplateId
  listTipjobsReportTemplateId(id: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/report/template/sag/listJob?reportTemplateId=${id}`;
    return this.http.get(api);
  }

  //修改tip对标岗位
  createlistTipjobs(json: {
    id: string;
    name: any;
    projectId: any;
    standardQuestionnaireId: any;
    standardReportTemplateJobId: any;
  }): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/saveOrUpdateReportTemplateJob`;
    return this.http.post(api, json);
  }

  querylistTipjobs(id: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getByReportTemplateId?reportTemplateId=${id}`;
    return this.http.get(api);
  }

  //保存新建维度
  SaveSurveyCustomDimension(json: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/custom/dimension/batchSaveOrUpdate`;
    return this.http.post(api, json);
  }
  //保存新建维度
  batchSaveSurveyCustomDimension(json: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/batchSaveSurveyCustomDimension`;
    return this.http.post(api, json);
  }
  listSurveyCustomDimension(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/listSurveyCustomDimension?questionnaireId=${id}`;
    return this.http.get(api);
  }

  listdeletefactor(id: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/isUsed?id=${id}`;
    return this.http.get(api);
  }

  //维度解释
  customizelist(json: {
    configType: any;
    dimensionNameOrCode: any;
    page:
      | { current: any; searchCount: boolean; size: number }
      | { current: any; searchCount: boolean; size: number };
    projectId: any;
    questionnaireId: any;
  }): Observable<any> {
    const api = `${this.tenantUrl}/survey/report/dimension/listReportDimensionByPage`;
    return this.http.post(api, json);
  }

  //维度区间
  scorelist(json: {
    type: any;
    dimensionNameOrCode: any;
    page:
      | { current: any; searchCount: boolean; size: number }
      | { current: any; searchCount: boolean; size: number };
    projectId: any;
    questionnaireId: any;
  }): Observable<any> {
    const api = `${this.tenantUrl}/survey/report/dimension/listReportScoreByPage`;
    return this.http.post(api, json);
  }

  //自定义
  Customlist(json: {
    dimensionName: any;
    page:
      | { current: any; searchCount: boolean; size: number }
      | { current: any; searchCount: boolean; size: number };
    projectId: any;
    questionnaireId: any;
  }): Observable<any> {
    const api = `${this.tenantUrl}/survey/report/dimension/listCustomDimensionByPage`;
    return this.http.post(api, json);
  }

  //配置表下拉框数据
  listquestionnaire(projectId: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/listByProjectId/${projectId}`;
    return this.http.get(api);
  }

  //更新维度解释
  updatecustomizelist(json: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/report/dimension/updateReportDimension`;
    return this.http.post(api, json);
  }

  //更新维度区间
  updatescorelist(json: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/report/dimension/updateReportScore`;
    return this.http.post(api, json);
  }

  //更新自定义
  updateCustomlist(json: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/report/dimension/updateCustomDimension`;
    return this.http.post(api, json);
  }
  //获取邮件模板列表
  getEmaillist(type: string, purpose?: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/emailTemplate/list?type=${type}`;
    if (purpose) api = api + "&purpose=" + purpose;
    return this.http.get(api);
  }

  //获取默认邮件模板
  getEmailDefault(type: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/emailTemplate/getDefault?type=${type}`;
    return this.http.get(api);
  }

  //删除邮件模板
  getDeletelist(id: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/emailTemplate/delete/${id}`;
    return this.http.post(api, {});
  }

  //设置默认邮件模板
  settingdeffault(
    id: string,
    isDefault: boolean,
    type: string,
    purpose?: string
  ): Observable<any> {
    let api = `${this.tenantUrl}/survey/emailTemplate/setDefault/${id}/${isDefault}?type=${type}`;
    if (purpose) api = api + "&purpose=" + purpose;
    return this.http.post(api, {});
  }

  //创建模板
  createlist(json: {
    content: {
      [key: string]: string;
    };
    name: string;
    valueId: string;
    isDefault: boolean;
    type: string;
    isStandard: boolean;
    id: string;
    editors: boolean;
  }): Observable<any> {
    const api = `${this.tenantUrl}/survey/emailTemplate/create`;
    return this.http.post(api, json);
  }

  //修改邮件模板
  updatelist(json: {
    content: {
      [key: string]: string;
    };
    name: string;
    valueId: string;
    isDefault: boolean;
    type: string;
    isStandard: boolean;
    id: string;
    editors: boolean;
  }): Observable<any> {
    const api = `${this.tenantUrl}/survey/emailTemplate/update`;
    return this.http.post(api, json);
  }

  //获取邮件模板详情
  useTemplate(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/emailTemplate/get/${id}`;
    return this.http.get(api);
  }

  //矫正维度获取活动维度
  PublishedProject(id: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/listPublishedProjected?projectId=${id}`;
    return this.http.get(api);
  }

  //修改活动子维度
  UpdateProject(json: any[]): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/updateProjectReportDimension`;
    return this.http.post(api, json);
  }

  //恢复模板默认
  ResetProject(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/emailTemplate/reset/${id}`;
    return this.http.post(api, {});
  }

  // prisma历史对比数据 > 创建历史对比数据
  createPrismaHistoryData(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/create`;
    return this.http.post(api, params);
  }
  // prisma历史对比数据 > 修改历史对比数据
  updatePrismaHistoryData(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/update`;
    return this.http.post(api, params);
  }

  // prisma历史对比数据 > 删除历史对比数据
  deletePrismaHistoryData(id: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/delete/${id}`;
    return this.http.post(api, {});
  }

  // prisma历史对比数据 > 返回活动下的历史对比数据列表
  getPrismaHistoryData(projectId: null): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/listByProjectId/${projectId}`;
    return this.http.get(api);
  }
  // prisma历史对比数据 > 查询历史对比数据
  getDetailById(projectId: string | null): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/getDetailById/${projectId}`;
    return this.http.get(api);
  }

  // prisma历史对比数据 > 返回匹配结果
  getMatchMappingResult(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/getMatchMappingResult`;
    return this.http.post(api, params);
  }

  // prisma历史对比数据 > 清空匹配结果
  clearMatchMappingResult(id: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/clearMatchMappingResult/${id}`;
    return this.http.post(api, {});
  }

  // prisma历史对比数据 > 导出数据表
  exportMatchMappingResult(id: any): Observable<any> {
    this.httpOptions = { responseType: "Blob", observe: "response" };
    const api = `${this.tenantUrl}/survey/prisma/history/data/exportMatchMappingResult?id=${id}`;
    return this.http.get(api, this.httpOptions);
  }

  // prisma历史对比数据 > 保存匹配结果
  saveMatchMappingResult(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/saveMatchMappingResult`;
    return this.http.post(api, params);
  }

  // prisma历史对比数据 > 计算匹配度
  calculateMatchRate(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/calculateMatchRate`;
    return this.http.post(api, params);
  }

  // prisma历史对比数据 > 切换历史对比数据状态
  switchPrismaHistoryStatus(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/history/data/toggleStatus/${id}`;
    return this.http.post(api, {});
  }

  // prisma历史对比数据 > 导出切换历史对比模板
  exportPrismaHistoryData(id: string): Observable<any> {
    this.httpOptions = { responseType: "Blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/prisma/history/data/export?id=` + id;
    return this.http.get(url, this.httpOptions);
  }

  // prisma历史对比数据 > 导入切换历史对比模板
  uploadPrismaHistoryData(formData: any, id: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/prisma/history/data/importHistoryData?id=${id}`;
    return this.http.post(api, formData);
  }
  // prisma历史对比数据 > 导入匹配结果
  importMatchMappingResult(formData: any, id: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/prisma/history/data/importMatchMappingResult?id=${id}`;
    return this.http.post(api, formData);
  }

  // 获取活动详情
  getDetail(id: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/getDetail/${id}`;
    return this.http.get(api);
  }

  syncMessageStatus(id: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/syncMessageStatus?projectId=${id}`;
    return this.http.get(api);
  }

  public exportMessageLog(json): Observable<any> {
    this.httpOptions = { responseType: "Blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/messageLog/export`;
    return this.http.post(url, json, this.httpOptions);
  }

  getOrgList(params): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/pageOrganization`;
    return this.http.post(url, params);
  }

  updateOneQuestionnaireReportSetting(questionnaireId): Observable<any> {
    // 矫正维度恢复默认
    const url = `${this.tenantUrl}/survey/project/updateOneQuestionnaireReportSetting?questionnaireId=${questionnaireId}`;
    return this.http.get(url);
  }

  // 题本拖拽排序
  reSort(ids, questionnaireId): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/reSort/${questionnaireId}`;
    return this.http.post(api, ids);
  }

  // 清除填答数据
  clearAnswerData(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/clearAnswerData`;
    return this.http.post(api, params);
  }

  public simpleTemplate(): Observable<any> {
    this.httpOptions = { responseType: "Blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/person/download/simpleTemplate`;
    return this.http.post(url, {}, this.httpOptions);
  }
  // 调研 填答数据 获取填答人数据
  getRespondentData(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/pagePersonInfo`;
    return this.http.post(api, params);
  }
  // 调研 填答数据 导出填答人数据
  exportRespondentData(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/exportAllPerson`;
    return this.http.post(api, params, {
      responseType: "blob",
      observe: "response",
    });
  }

  // 调研 填答数据 删除填答人数据
  batchDeletePersonInfo(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/batchDeletePersonInfo`;
    return this.http.post(api, params);
  }
  // 调研 填答数据 清除填答人数据
  batchClearPersonInfo(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/batchClearPersonInfo`;
    return this.http.post(api, params);
  }

  // 获取活动下最新的邮件主题
  getLatestMailSubject(params: {
    projectId: string;
    type: string;
    purpose: string;
  }): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/getLatestMailSubject?projectId=${params.projectId}&type=${params.type}&purpose=${params.purpose}`;
    return this.http.get(api);
  }

  //获取相同类型活动
  getlistSimilar(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/listSimilarProjects?projectId=${projectId}`;
    return this.http.get(api);
  }

  //获取最新一次消息模板
  getlistMail(
    projectId: string,
    answerCodeType: string,
    type: string,
    purpose: string
  ): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/getLatestMail?projectId=${projectId}&answerCodeType=${answerCodeType}&type=${type}&purpose=${purpose}`;
    return this.http.get(api);
  }

  // 活动管理更多操作显示权限
  getIsSystemInterface(): Observable<any> {
    let api = `${this.tenantUrl}/survey/standard/tenant/isSystemInterface`;
    return this.http.get(api);
  }
  // 活动管理更多操作显示权限
  getIsPushThird(params: {
    projectId: string;
    isPushThird: boolean;
  }): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/isPushThird?projectId=${params.projectId}&isPushThird=${params.isPushThird}`;
    return this.http.post(api, params);
  }

  //展示调研名称
  getIlistPrismaLabel(projectId): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/listPrismaLabel?projectId=${projectId}`;
    return this.http.get(api);
  }

  //更新调研名称
  getIupdatePrismaLabel(params: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/updatePrismaLabel`;
    return this.http.post(api, params);
  }

  //恢复调研名称
  getIrestorePrismaLabel(projectId): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/restorePrismaLabel?projectId=${projectId}`;
    return this.http.get(api);
  }

  // 题本修订 接口
  // 获取题本修订信息接口
  getCreateRevisionInfo(questionId): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/getCreateRevisionInfo/${questionId}`;
    return this.http.get(api);
  }

  // 获取或创建题本修订结果接口
  getOrCreateRevisionResult(params): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/getOrCreateRevisionResult`;
    return this.http.post(api, params);
  }
  // 保存题本修订结果结果接口
  saveRevisionResult(params): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/saveRevisionResult`;
    return this.http.post(api, params);
  }
  // 清空结果接口
  clearRevisionResult(questionId): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/clearRevisionResult/${questionId}`;
    return this.http.post(api, {});
  }

  // 权限 /survey/user/list
  getPermissionUser(username?): Observable<any> {
    let api = `${this.tenantUrl}/survey/user/list?username=${username}`;
    return this.http.post(api, {});
  }
  // 权限 /survey/user/list
  getPermissionList(name?): Observable<any> {
    let api = `${this.tenantUrl}/survey/permission/list?name=${name}`;
    return this.http.post(api, {});
  }
  // 添加权限 /survey/user/list
  addPermissions(json): Observable<any> {
    let api = `${this.tenantUrl}/survey/permission/save`;
    return this.http.post(api, json);
  }
  // 修改权限 /survey/user/list
  editPermissions(json): Observable<any> {
    let api = `${this.tenantUrl}/survey/permission/update`;
    return this.http.post(api, json);
  }
  // 删除权限 /survey/user/list
  delPermissions(id): Observable<any> {
    let api = `${this.tenantUrl}/survey/permission/delete/${id}`;
    return this.http.post(api, {});
  }
  // 设置用户权限 /survey/user/list
  setPermissions(json): Observable<any> {
    let api = `${this.tenantUrl}/survey/permission/setUserPermission`;
    return this.http.post(api, json);
  }

  // 是否超管
  isAdmin(): Observable<any> {
    let api = `${this.tenantUrl}/survey/user/isAdmin`;
    return this.http.get(api);
  }

  // 发送短信二维码数量
  getLongestPersonCaptcha(projectId): Observable<any> {
    let api = `${this.tenantUrl}/survey/person/getLongestPersonCaptcha?projectId=${projectId}`;
    return this.http.get(api);
  }

  getQuestionnaireInformation(json): Observable<any> {
    let api = `${this.tenantUrl}/survey/questionnaire/getQuestionnaireInformation`;
    return this.http.post(api, json);
  }
  // 分页获取被评估人常模设置列表接口
  getPagedListByPage(params: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/investigator/norm/setting/listByPage`;
    return this.http.post(url, params);
  }
  // 导入被评估人常模设置列表接口
  public importNorm(formData: any, projectId: string): Observable<any> {
    // 导入
    const url = `${this.tenantUrl}/survey/investigator/norm/setting/import?projectId=${projectId}`;
    return this.http.post(url, formData);
  }
  public exportNorm(projectId: string): Observable<any> {
    // 导出
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/investigator/norm/setting/export?projectId=${projectId}`;
    return this.http.get(url, this.httpOptionsload);
  }
  // 修改被评估人常模设置接口
  public updateNorm(formData: any): Observable<any> {
    // 导入
    const url = `${this.tenantUrl}/survey/investigator/norm/setting/update`;
    return this.http.post(url, formData);
  }
  // 修改被评估人常模设置均分名称接口
  public updateAverageScoreName(formData: any): Observable<any> {
    // 导入
    const url = `${this.tenantUrl}/survey/investigator/norm/setting/updateAverageScoreName`;
    return this.http.post(url, formData);
  }
  // 被评估人常模一键删除接口
  public clearDeleteAll(projectId: string): Observable<any> {
    // 导入
    const url = `${this.tenantUrl}/survey/investigator/norm/setting/deleteAll/${projectId}`;
    return this.http.post(url, {});
  }
  //报告配置表 报告自定义列表table
  listReportConfig(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/listReportConfig`;
    return this.http.get(api, { params });
  }
  //报告文字配置表 报告自定义列表table
  listReportTextConfig(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/listReportTextConfig`;
    return this.http.get(api, { params });
  }

  //报告配置表 报告自定义列表table 修改数据
  public saveReportConfig(formData: any): Observable<any> {
    // 导入
    const url = `${this.tenantUrl}/survey/project/saveReportConfig`;
    return this.http.post(url, formData);
  }
  //报告配置表 报告文字自定义列表table 修改数据
  public saveReportTextConfig(formData: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/saveReportTextConfig`;
    return this.http.post(url, formData);
  }
  //报告配置表 报告文字自定义列表table 删除数据
  public removeReportTextConfig(id: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/deleteReportTextConfig?id=${id}`;
    return this.http.get(url);
  }

  // 查询多视角列表根据维度分类
  public listQuestionDimensionByRank(questionnaireId): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/listQuestionDimensionByRank?questionnaireId=${questionnaireId}`;
    return this.http.get(url);
  }

  //查询多视角列表根据选中的题目
  public listQuestionDimensionByQuestion(formData: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/listQuestionDimensionByQuestion`;
    return this.http.post(url, formData);
  }
  // 获取维度管理列表
  public getlistSurveyCustomDimension(questionnaireId): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/listSurveyCustomDimension?questionnaireId=${questionnaireId}`;
    return this.http.get(url);
  }

  // 获取指数列表
  public getlistPrismaLabel(id): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listPrismaLabel?projectId=${id}`;
    return this.http.get(url);
  }
  //关联多视角
  public saveQuestionDimension(formData: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/saveQuestionDimension`;
    return this.http.post(url, formData);
  }

  // 删除单条多视角数据
  public delQuestionDimension(id): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/delQuestionDimension?id=${id}`;
    return this.http.get(url);
  }

  // 一键清除多视角
  public batchDelQuestionDimension(questionnaireId): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/batchDelQuestionDimension?questionnaireId=${questionnaireId}`;
    return this.http.get(url);
  }
  // 导入多视角
  public questionDimensionImport(
    formData: any,
    questionnaireId: string
  ): Observable<any> {
    // 导入
    const url = `${this.tenantUrl}/survey/question/questionDimensionImport?questionnaireId=${questionnaireId}`;
    return this.http.post(url, formData);
  }
  // 导出多视角
  public questionDimensionExport(questionnaireId: string): Observable<any> {
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/question/questionDimensionExport?questionnaireId=${questionnaireId}`;
    return this.http.get(url, this.httpOptionsload);
  }
  // 短信功能是否展示
  public isOpenMessageSend(): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/isOpenMessageSend`;
    return this.http.get(url);
  }

  //题本落地建议
  proposalList(id): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listIasDimensionConfig?projectId=${id}`;
    return this.http.get(url);
  }

  //报告配置表 题本落地建议列表table 修改数据
  public saveProposalConfig(formData: any, language: string): Observable<any> {
    console.log(formData);
    const url = `${this.tenantUrl}/survey/project/updateIasDimensionConfig`;
    let data = {};
    if (formData != null) {
      let suggestionData =
        (formData.suggestion1 != null ? formData.suggestion1 : "null") + "\n";
      suggestionData +=
        (formData.suggestion2 != null ? formData.suggestion2 : "null") + "\n";
      suggestionData +=
        (formData.suggestion3 != null ? formData.suggestion3 : "null") + "\n";
      suggestionData +=
        (formData.suggestion4 != null ? formData.suggestion4 : "null") + "\n";
      suggestionData +=
        (formData.suggestion5 != null ? formData.suggestion5 : "null") + "\n";
      suggestionData +=
        formData.suggestion6 != null ? formData.suggestion6 : "null";

      let suggestionDataEn =
        (formData.suggestion1En != null ? formData.suggestion1En : "null") +
        "\n";
      suggestionDataEn +=
        (formData.suggestion2En != null ? formData.suggestion2En : "null") +
        "\n";
      suggestionDataEn +=
        (formData.suggestion3En != null ? formData.suggestion3En : "null") +
        "\n";
      suggestionDataEn +=
        (formData.suggestion4En != null ? formData.suggestion4En : "null") +
        "\n";
      suggestionDataEn +=
        (formData.suggestion5En != null ? formData.suggestion5En : "null") +
        "\n";
      suggestionDataEn +=
        formData.suggestion6En != null ? formData.suggestion6En : "null";

      data = {
        id: formData.id,
        name: null,
        suggestion: { zh_CN: suggestionData, en_US: suggestionDataEn },
      };
      console.log(data);
      return this.http.post(url, data);
    }
    return null;
  }
  // 使用UTF-8编码计算字符串的字节长度
  public getStringSizeInBytes(str) {
    let totalBytes = new Blob([str]).size;
    // 将字节长度转换为兆字节（MB）
    let sizeInMB = totalBytes / (1024 * 1024);
    let num = Math.round(sizeInMB * 100) / 100;
    return num == 0 && str ? 0.01 : num;
  }

  //计算邮件整体大小
  public getMailTotalSize(size, fileList) {
    let size1 = 0;
    fileList.forEach((element) => {
      size1 += element.size;
    });
    return Math.ceil(size + size1);
  }

  //计算附件大小
  getFileSize(size) {
    return Math.round((size / (1024 * 1024)) * 100) / 100;
  }
  // 邮件功能是否展示
  public isOpenMailSend(): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/isOpenMailSend`;
    return this.http.get(url);
  }

  // 跳过问题

  // 租户-导出跳过题模板
  public exportSkip(questionnaireId: string): Observable<any> {
    this.httpOptionsload = { responseType: "blob", observe: "response" };
    const url = `${this.tenantUrl}/survey/question/skip/export?questionnaireId=${questionnaireId}`;
    return this.http.get(url, this.httpOptionsload);
  }
  // 租户-导入跳过题模板
  public importSkip(formData: any, questionnaireId: string): Observable<any> {
    // 导入
    const url = `${this.tenantUrl}/survey/question/skip/import?questionnaireId=${questionnaireId}`;
    return this.http.post(url, formData);
  }
  // 删除所有跳过题
  public deleteAllSurveySkip(questionnaireId): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/deleteAllSurveySkip?questionnaireId=${questionnaireId}`;
    return this.http.get(url);
  }
  // 删除某个跳过题
  public deleteSurveySkip(params): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/deleteSurveySkip`;
    return this.http.post(url, params);
  }
  // 查询跳过题
  public showSkipCombination(questionnaireId): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/showSkipCombination?questionnaireId=${questionnaireId}`;
    return this.http.get(url);
  }
  // 租户-创建跳过题
  public createSurveyStandardSkip(params: {
    parentQuestionId: string;
    skipQuestionVos: any[];
    surveyStandardOptions?: any[];
    answerType: string;
  }): Observable<any> {
    const url = `${this.tenantUrl}/survey/question/createSurveyStandardSkip`;
    return this.http.post(url, params);
  }
  public getTenantNormConfigList(json: {
    name: string;
    projectId: any;
  }): Observable<any> {
    const url = this.tenantUrl + "/survey/norm/prisma/normListByProjectId";
    return this.http.post(url, json);
  }
  public saveTenantNormConfigList(params: any): Observable<any> {
    const url = this.tenantUrl + "/survey/norm/prisma/savePrismaNorm";
    return this.http.post(url, params, this.httpOptions);
  } 
    //更新问卷
  updateProjectNorm(projectId: string): Observable<any> {
    return this.http.request(
      "get",
      `${this.tenantUrl}/survey/norm/prisma/updateProjectNorm?projectId=${projectId}`
    );
  }
}
