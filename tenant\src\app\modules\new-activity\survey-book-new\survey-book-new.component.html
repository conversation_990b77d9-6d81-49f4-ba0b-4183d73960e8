<div style="background:#F5F6FA; height: 100%;" id="orgDiv">
  <div class="content client-width">
    <div class="body" #deliveryTable>
      <div
        style="display: flex; justify-content: space-between; align-items: center;"
      >
        <div class="title">
          <span>定制题本</span>
          <nz-radio-group
            [(ngModel)]="lanIndex"
            (ngModelChange)="changeLan($event)"
            [nzButtonStyle]="'solid'"
            [nzSize]="'small'"
          >
            <label nz-radio-button [nzValue]="0">中文</label>
            <label nz-radio-button [nzValue]="1">ENG</label>
          </nz-radio-group>
        </div>

        <div class="searchDiv">
          <nz-input-group [nzPrefix]="IconSearch">
            <input
              class="input-search"
              type="text"
              nz-input
              placeholder="请输入关键词"
              [(ngModel)]="keyWord"
              (keydown.enter)="search()"
            />
          </nz-input-group>
          <ng-template #IconSearch>
            <i
              nz-icon
              nzType="search"
              class="icon-search"
              (click)="search()"
            ></i>
          </ng-template>
        </div>
      </div>

      <div class="action" *ngIf="!typeshow || !exportshow">
        <div class="flex align-middle justify-center">
          <button
            *ngIf="!typeshow && reportType !== 'OC_INVESTIGATION_RESEARCH'"
            class="invite_button"
            (click)="add()"
          >
            <i
              nz-icon
              nzType="plus-circle"
              nzTheme="fill"
              style="color: white; font-size: 22px;"
            ></i>
            <span style="color: white;margin-left: 8px;">新增题本</span>
          </button>
          <button *ngIf="!typeshow" class="revision-btn" (click)="batchEdit()">
            批量修订
          </button>
        </div>
        <div class="right-btn">
          <nz-upload
            *ngIf="uploadshow"
            [nzCustomRequest]="customReq"
            [nzShowUploadList]="false"
          >
            <app-btn
              style="margin-left: -30px;"
              [text]="'导入'"
              [image]="'./assets/images/org/import.png'"
              [hoverColor]="'#27C091'"
              [hoverImage]="'./assets/images/org/import_hover.png'"
            >
            </app-btn>
          </nz-upload>
          <app-btn
            style="margin-left: -30px;"
            *ngIf="!exportshow"
            [text]="'导出'"
            [image]="'./assets/images/org/export.png'"
            [hoverColor]="'#B483D6'"
            [hoverImage]="'./assets/images/org/export_hover.png'"
            (btnclick)="exportQusBook()"
          >
          </app-btn>
          <pentrate-modal
            *ngIf="!typeshow"
            [questionnaireId]="questionnaireId"
            [lan]="lan"
            (loadData)="loadData()"
          ></pentrate-modal>
          <div
            class="delete-box"
            *ngIf="!typeshow"
            nz-popconfirm
            nzPopconfirmTitle="要删除选中的题目吗？"
            (nzOnConfirm)="onDeleteQuestion()"
          >
            <div class="svg-ico-18 svg-ico-delete" title="删除"></div>
            <div class="delete-txt">批量删除</div>
          </div>
        </div>
      </div>

      <div class="scroll topiclist">
        <nz-table
          #basicTable
          [nzData]="topicList"
          [nzScroll]="{ y: tableTop }"
          [nzFrontPagination]="false"
        >
          <thead>
            <tr>
              <th nzWidth="60px" *ngIf="projectType != 'ANSWERING'"></th>
              <th
                *ngIf="!typeshow"
                nzWidth="60px"
                [nzDisabled]="disabledAllChecked"
                nzShowCheckbox
                [(nzChecked)]="isAllChecked"
                (nzCheckedChange)="checkAll($event)"
              ></th>
              <th nzWidth="60px">序号</th>
              <th nzWidth="150px">维度名称</th>
              <th nzWidth="100px"></th>
              <th nzWidth="220px">题干</th>
              <th nzWidth="220px">选项</th>
              <th nzWidth="80px">状态</th>
              <th *ngIf="!typeshow" nzWidth="100px">操作</th>
            </tr>
          </thead>
          <tbody
            *ngIf="projectType != 'ANSWERING'"
            dragula="SURVEYBOOK"
            [(dragulaModel)]="basicTable.data"
            (dragulaModelChange)="dragnumber($event)"
          >
            <ng-container *ngFor="let data of basicTable.data; let i = index">
              <tr [ngClass]="data.isPierceThrough ? 'through-tr' : ''">
                <td>
                  <i class="iconfont icon-caidan" style="cursor: pointer;"></i>
                </td>
                <td
                  *ngIf="!typeshow"
                  [nzDisabled]="data.isForceSelect"
                  nzShowCheckbox
                  [(nzChecked)]="data.isDeleted"
                  (nzCheckedChange)="checkOne()"
                ></td>
                <td>
                  {{ i + 1 }}
                </td>
                <td>
                  {{
                    data.dimensionName && data.dimensionName[lan]
                      ? data.dimensionName[lan]
                      : "--"
                  }}
                </td>
                <td>
                  <span
                    *ngIf="!data.isPierceThrough"
                    class="tip-box"
                    [ngStyle]="{
                      color: tips[data.type].color,
                      background: tips[data.type].bg
                    }"
                    >{{ tips[data.type].name[lan] }}</span
                  >
                  <span
                    *ngIf="data.isPierceThrough"
                    class="tip-box"
                    [ngStyle]="{
                      color: tips.isPierceThrough.color,
                      background: tips.isPierceThrough.bg
                    }"
                    >{{ tips.isPierceThrough.name[lan] }}</span
                  >
                </td>
                <td>
                  <p
                    *ngIf="data.replaceName"
                    [innerHTML]="data.replaceName[lan] | html"
                  ></p>
                </td>
                <td>
                  <span
                    *ngFor="
                      let option of data.options.options;
                      let optidx = index
                    "
                    >{{
                      data.type === "ESSAY_QUESTION"
                        ? ""
                        : data.type === ""
                        ? "MULTIPLE_CHOICE_ESSAY_QUESTION"
                        : (optidx === 0 ? "" : "/") + option.name[lan]
                    }}</span
                  >
                </td>
                <td>
                  <div>
                    {{
                      lan === "zh_CN"
                        ? data.isRequire
                          ? "必填"
                          : "非必填"
                        : data.isRequire
                        ? "Required"
                        : "Not required"
                    }}
                  </div>
                </td>
                <td *ngIf="!typeshow">
                  <div>
                    <app-btn
                      *ngIf="data.isEditable"
                      [text]="''"
                      [image]="'./assets/images/org/edit.png'"
                      [hoverColor]="'#409EFF'"
                      [hoverImage]="'./assets/images/org/edit_hover.png'"
                      (btnclick)="edit(data)"
                    >
                    </app-btn>
                    <app-btn
                      nz-popconfirm
                      nzPopconfirmTitle="要删除选中的题目吗？"
                      (nzOnConfirm)="delete(data.id)"
                      *ngIf="
                        !data.isForceSelect && canEdit() && data.isEditable
                      "
                      [text]="''"
                      [image]="'./assets/images/org/del.png'"
                      [hoverColor]="'#409EFF'"
                      [hoverImage]="'./assets/images/org/del_hover.png'"
                    >
                    </app-btn>
                  </div>
                </td>
              </tr>
              <tr *ngIf="data.isPierceThrough">
                <td>
                  <i class="iconfont icon-caidan" style="cursor: pointer;"></i>
                </td>
                <td *ngIf="!typeshow"></td>
                <!-- 序号 -->
                <td></td>
                <!-- 维度名称 -->
                <td>
                  {{
                    data.resultQuestion.dimensionName &&
                    data.resultQuestion.dimensionName[lan]
                      ? data.resultQuestion.dimensionName[lan]
                      : "--"
                  }}
                </td>
                <td></td>
                <!-- 题干 -->
                <td>
                  <p
                    [innerHTML]="data.resultQuestion.replaceName[lan] | html"
                  ></p>
                </td>
                <!-- 选项 -->
                <td>
                  <span
                    *ngFor="
                      let option of data.resultQuestion.options.options;
                      let optidx = index
                    "
                    >{{
                      data.type === "MULTIPLE_CHOICE_ESSAY_QUESTION"
                        ? ""
                        : data.type === "ESSAY_QUESTION"
                        ? ""
                        : (optidx === 0 ? "" : "/") + option.name[lan]
                    }}</span
                  >
                </td>
                <!-- 状态 -->
                <td>
                  <div>
                    {{
                      lan === "zh_CN"
                        ? data.resultQuestion.isRequire
                          ? "必填"
                          : "非必填"
                        : data.resultQuestion.isRequire
                        ? "Required"
                        : "Not required"
                    }}
                  </div>
                </td>
                <!-- 操作 -->
                <td *ngIf="!typeshow">
                  <app-btn
                    [text]="''"
                    [image]="'./assets/images/org/edit.png'"
                    [hoverColor]="'#409EFF'"
                    [hoverImage]="'./assets/images/org/edit_hover.png'"
                    (btnclick)="edit(data.resultQuestion)"
                  >
                  </app-btn>
                </td>
              </tr>
            </ng-container>
          </tbody>
          <tbody *ngIf="projectType == 'ANSWERING'">
            <ng-container *ngFor="let data of basicTable.data; let i = index">
              <tr [ngClass]="data.isPierceThrough ? 'through-tr' : ''">
                <td
                  *ngIf="!typeshow"
                  [nzDisabled]="data.isForceSelect"
                  nzShowCheckbox
                  [(nzChecked)]="data.isDeleted"
                  (nzCheckedChange)="checkOne()"
                ></td>
                <td>
                  {{ i + 1 }}
                </td>
                <td>
                  {{
                    data.dimensionName && data.dimensionName[lan]
                      ? data.dimensionName[lan]
                      : "--"
                  }}
                </td>
                <td>
                  <span
                    *ngIf="!data.isPierceThrough"
                    class="tip-box"
                    [ngStyle]="{
                      color: tips[data.type].color,
                      background: tips[data.type].bg
                    }"
                    >{{ tips[data.type].name[lan] }}</span
                  >
                  <span
                    *ngIf="data.isPierceThrough"
                    class="tip-box"
                    [ngStyle]="{
                      color: tips.isPierceThrough.color,
                      background: tips.isPierceThrough.bg
                    }"
                    >{{ tips.isPierceThrough.name[lan] }}</span
                  >
                </td>
                <td>
                  <p
                    *ngIf="data.replaceName"
                    [innerHTML]="data.replaceName[lan] | html"
                  ></p>
                </td>
                <td>
                  <span
                    *ngFor="
                      let option of data.options.options;
                      let optidx = index
                    "
                    >{{
                      data.type === "ESSAY_QUESTION"
                        ? ""
                        : data.type === ""
                        ? "MULTIPLE_CHOICE_ESSAY_QUESTION"
                        : (optidx === 0 ? "" : "/") + option.name[lan]
                    }}</span
                  >
                </td>
                <td>
                  <div>
                    {{
                      lan === "zh_CN"
                        ? data.isRequire
                          ? "必填"
                          : "非必填"
                        : data.isRequire
                        ? "Required"
                        : "Not required"
                    }}
                  </div>
                </td>
                <td *ngIf="!typeshow">
                  <div>
                    <app-btn
                      [text]="''"
                      [image]="'./assets/images/org/edit.png'"
                      [hoverColor]="'#409EFF'"
                      [hoverImage]="'./assets/images/org/edit_hover.png'"
                      (btnclick)="edit(data)"
                    >
                    </app-btn>
                    <app-btn
                      nz-popconfirm
                      nzPopconfirmTitle="要删除选中的题目吗？"
                      (nzOnConfirm)="delete(data.id)"
                      *ngIf="!data.isForceSelect && canEdit()"
                      [text]="''"
                      [image]="'./assets/images/org/del.png'"
                      [hoverColor]="'#409EFF'"
                      [hoverImage]="'./assets/images/org/del_hover.png'"
                    >
                    </app-btn>
                  </div>
                </td>
              </tr>
              <tr *ngIf="data.isPierceThrough">
                <td *ngIf="!typeshow"></td>
                <!-- 序号 -->
                <td></td>
                <!-- 维度名称 -->
                <td>
                  {{
                    data.resultQuestion.dimensionName &&
                    data.resultQuestion.dimensionName[lan]
                      ? data.resultQuestion.dimensionName[lan]
                      : "--"
                  }}
                </td>
                <td></td>
                <!-- 题干 -->
                <td>
                  <p
                    [innerHTML]="data.resultQuestion.replaceName[lan] | html"
                  ></p>
                </td>
                <!-- 选项 -->
                <td>
                  <span
                    *ngFor="
                      let option of data.resultQuestion.options.options;
                      let optidx = index
                    "
                    >{{
                      data.type === "MULTIPLE_CHOICE_ESSAY_QUESTION"
                        ? ""
                        : data.type === "ESSAY_QUESTION"
                        ? ""
                        : (optidx === 0 ? "" : "/") + option.name[lan]
                    }}</span
                  >
                </td>
                <!-- 状态 -->
                <td>
                  <div>
                    {{
                      lan === "zh_CN"
                        ? data.resultQuestion.isRequire
                          ? "必填"
                          : "非必填"
                        : data.resultQuestion.isRequire
                        ? "Required"
                        : "Not required"
                    }}
                  </div>
                </td>
                <!-- 操作 -->
                <td *ngIf="!typeshow">
                  <app-btn
                    [text]="''"
                    [image]="'./assets/images/org/edit.png'"
                    [hoverColor]="'#409EFF'"
                    [hoverImage]="'./assets/images/org/edit_hover.png'"
                    (btnclick)="edit(data.resultQuestion)"
                  >
                  </app-btn>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </nz-table>
      </div>
    </div>
  </div>

  <div
    style="width:100%; position: fixed; bottom: 0px; margin-top: 15px; background-color: white; text-align: center; height:60px; box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);"
  >
    <div
      style="max-width: 1200px; display: flex; justify-content: flex-end; margin:auto; height:100%; align-items: center;"
    >
      <button nz-button class="iptBtn" (click)="bookConfirm()" appDisableTime>
        <span>确认</span>
      </button>
    </div>
  </div>
</div>
