import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { isTemplateRef, NzMessageService } from "ng-zorro-antd";
import { ProjectManageService } from "../../../service/project-manage.service";

@Component({
  selector: "app-compare-config",
  templateUrl: "./compare.component.html",
  styleUrls: ["./compare.component.less"],
})
export class compareconfig implements OnInit {
  constructor(private http: HttpClient, private msg: NzMessageService) {}
  @Input() isVisible;
  @Input() chartId;
  @Output() getClose = new EventEmitter<any>();
  showclass = true;
  showtitle = true;
  parentDimensions: any = [];
  childDimensions: any = [];
  showcheckbox: any = [];
  personlist: any = [];
  lineChartX = null;
  lineChartY = null;
  charttype = "basic";
  showcharts = false;
  projectName = null;
  questionnaireType = null;
  colors = ["#409EFF", "#FFBA3B", "#FF7676", "#4FCF83", "#A06BFF"];
  tenantUrl: string = "/tenant-api";
  chartcolor = [];
  ngOnInit(): void {
    this.getcompare(this.chartId);
  }
  getbigcode(type) {
    this.showclass = type;
    this.showcheckbox = this.showclass
      ? this.parentDimensions
      : this.childDimensions;
    this.lineChartX = [];
    this.chartlinechecked();
    this.getselectedX(this.showcheckbox);
  }
  getcharts(type) {
    this.showtitle = type;
    this.showcharts = false;
    setTimeout(() => {
      if (type) {
        this.charttype = "basic";
        this.chartlinechecked();
        this.getselectedX(this.showcheckbox);
      } else {
        this.charttype = "line";
        this.chartlinechecked();
        this.getselectedX(this.showcheckbox);
      }
    }, 100);
  }
  chooselog(e) {
    this.showcharts = false;
    setTimeout(() => {
      this.chartlinechecked();
      this.getselectedX(this.showcheckbox);
    }, 100);
  }
  choosetitle(e, t) {
    this.showcharts = false;
    setTimeout(() => {
      this.chartlinechecked();
      this.getselectedX(this.showcheckbox);
    }, 100);
  }
  getselectedY(data) {
    this.lineChartY = [];
    data.forEach((res) => {
      if (res.checked) {
        res.newscores = [];
        res.scores.forEach((val) => {
          if (val.checked) {
            res.newscores.push({
              score: val.score,
              color: val.color,
            });
          }
        });
        this.lineChartY.push({
          scores: res.newscores,
        });
      }
    });
    this.showcharts = true;
  }
  getselectedX(data) {
    this.lineChartX = [];
    data.forEach((res) => {
      if (res.checked) {
        this.lineChartX.push(res.dimensionName.zh_CN);
      }
    });
    this.showcharts = true;
  }
  changelist(data, type) {
    this.lineChartX = [];
    this.lineChartY = [];
    data.forEach((res) => {
      res.checked = true;
    });
  }
  getcompare(id) {
    let api = `${this.tenantUrl}/sagittarius/report/content/getCompareReport/${id}`;
    this.http.get(api).subscribe((res: any) => {
      this.projectName = res.data.projectName.zh_CN;
      this.questionnaireType = res.data.questionnaireType;
      this.personlist = res.data.personOrganizationIdNames;
      this.colorslist(this.personlist);
      this.changelist(this.personlist, false);
      if (this.showtitle) {
        this.parentDimensions = res.data.parentDimensions;
        this.childDimensions = res.data.childDimensions;
      } else {
        this.parentDimensions = res.data.personOrganizationParentDimensions;
        this.childDimensions = res.data.personOrganizationChildDimensions;
      }

      this.changelist(this.parentDimensions, true);
      this.changelist(this.childDimensions, true);
      if (this.parentDimensions.length == 0) {
        this.showclass = false;
      } else {
        this.showclass = true;
      }

      this.showcheckbox = this.showclass
        ? this.parentDimensions
        : this.childDimensions;
      this.chartlinechecked();
      this.getselectedX(this.showcheckbox);
    });
  }
  chartlinechecked() {
    this.showcheckbox.forEach((item) => {
      item.scores.forEach((val) => {
        this.personlist.forEach((res) => {
          if (res.id == val.personOrganizationId) {
            val.checked = res.checked;
            val.color = res.color;
          }
        });
      });
    });
    this.getselectedY(this.showcheckbox);
  }
  colorslist(data) {
    data.forEach((res, index) => {
      res.color = this.colors[index];
    });
  }
  handleCancel() {
    this.isVisible = false;
    this.getClose.emit(false);
  }
}
