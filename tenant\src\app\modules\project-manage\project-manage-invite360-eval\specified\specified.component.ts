import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-specified-eval',
  templateUrl: './specified.component.html',
  styleUrls: ['./specified.component.less']
})
export class SpecifiedEvalComponent implements OnInit {

  @Input() list;
  constructor() { }

  ngOnInit() {
    console.log(this.list);
    
    this.list = this.list.map(item => {

      let name : string = item.name && item.name.zh_CN ? item.name.zh_CN : item.name;

      return {
        key: item.id,
        title: name ? name : item.name,
        description: name ? name : item.name,
        direction: 'left',
        email: item.email,
        phone:item.phone,
        firstName:name ?  name : item.name, //人员名称
        captcha:item.inviteCaptcha,//验证码
        wxWorkUserId:item.wxWorkUserId,//企业微信
        dingAccount: item.dingAccount // 钉钉用户账号
      }
    })
  }

  filterOption(inputValue: string, item: any): boolean {
    return item.description.indexOf(inputValue) > -1;
  }

  search(ret: {}): void {
   
  }

  select(ret: {}): void {

  }

  change(ret: {}): void {
   
  }

}
