<!-- <header>
  <div class="icon-box">
    <span style="font-size: 11px;" class="iconfont icon-penetra-close" (click)="close()"></span>
  </div>
  <div class="title">
    添加子管理员
  </div>
</header> -->

<div *ngIf="permission && transType === 3" class="option_btn">
  <ul class="left_buttton">
    <ng-container>
      <li class="btn" nz-tooltip>
        <nz-upload
          class="upload"
          [nzCustomRequest]="importSubManager"
          nzAccept=".xlsx,.xls"
          [nzFilter]=""
          [nzShowUploadList]="false"
        >
          <i class="iconfont icon-icon_import"></i><span>导入</span>
        </nz-upload>
      </li>
    </ng-container>
    <ng-container>
      <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
      <li class="btn" (click)="exportSubManager()" nz-tooltip>
        <i class="iconfont icon-icon_export"></i><span>导出</span>
      </li>
    </ng-container>
  </ul>
</div>
<div class="sub-admin">
  <div class="content">
    <div class="left">
      <div class="header">
        <label
          nz-checkbox
          [(ngModel)]="checkAlluser"
          [nzIndeterminate]="userCheckedIndeterminate"
          (ngModelChange)="subUserAllChange($event)"
          >全选</label
        >
        <nz-input-group
          style="width:  calc(100% - 68px);"
          [nzSuffix]="suffixIconSearch"
        >
          <input
            nz-input
            placeholder="请输入关键词"
            [(ngModel)]="searchValue"
            (keydown.enter)="searchUser()"
          />
        </nz-input-group>
        <ng-template #suffixIconSearch>
          <i
            class="iconfont icon-search_o"
            style="cursor: pointer;"
            (click)="searchUser()"
          ></i>
        </ng-template>
      </div>
      <ul class="left-content">
        <ng-container *ngFor="let subData of subList; let index = index">
          <li
            *ngIf="subData.isShow"
            [ngClass]="{ active: clickSunManagerId === subData.userId }"
            (click)="clickSubManager(subData, index)"
          >
            <label
              nz-checkbox
              [(ngModel)]="subData.isSubManager"
              (ngModelChange)="SubManagerChange($event, subData, index)"
            ></label>
            <div>{{ subData.name }}</div>
          </li>
        </ng-container>
      </ul>
    </div>
    <div class="middle">
      <ng-container *ngIf="clickSunManagerId; else emptyTips">
        <div class="header">
          <nz-select
            style="width: 100%"
            [(ngModel)]="selectedId"
            nzPlaceHolder="请选择"
          >
            <ng-container *ngFor="let option of panels">
              <nz-option
                *ngIf="option.isShow"
                [nzValue]="option.id"
                [nzLabel]="option.title"
              ></nz-option>
            </ng-container>
          </nz-select>
        </div>
        <div class="middle-content">
          <div
            *ngIf="
              selectedId === 'org' ||
              selectedId === 'orgView' ||
              selectedId === 'demographics' ||
              selectedId === 'demographicsView' ||
              selectedId === 'viewReportFactors'
            "
            class="content-header"
          >
            <!-- 报告查看部门 -->
            <nz-input-group
              [hidden]="selectedId !== 'org'"
              [nzSuffix]="suffixIconSearch1"
            >
              <input
                type="text"
                nz-input
                placeholder="请输入关键词"
                [(ngModel)]="searchOrgValue"
              />
            </nz-input-group>
            <!-- 可查看填答进度的部门 -->
            <nz-input-group
              [hidden]="selectedId !== 'orgView'"
              [nzSuffix]="suffixIconSearch1"
            >
              <input
                type="text"
                nz-input
                placeholder="请输入关键词"
                [(ngModel)]="searchOrgViewValue"
              />
            </nz-input-group>
            <!-- 报告分析因子 -->
            <div [hidden]="selectedId !== 'demographics'" class="space-between">
              <label
                nz-checkbox
                [ngModel]="demoCheckedAll"
                [nzIndeterminate]="demoCheckedIndeterminate"
                (ngModelChange)="demoChangeAll($event)"
              >
                <span>全选</span>
              </label>
              <nz-input-group
                [nzSuffix]="suffixIconSearch1"
                style="width:  calc(100% - 60px);"
              >
                <input
                  type="text"
                  nz-input
                  placeholder="请输入关键词"
                  [(ngModel)]="searchDemographicValue"
                />
              </nz-input-group>
            </div>
            <!-- 可查看填答进度的分析因子 -->
            <div
              [hidden]="selectedId !== 'demographicsView'"
              class="space-between"
            >
              <label
                nz-checkbox
                [ngModel]="demoViewCheckedAll"
                [nzIndeterminate]="demoViewCheckedIndeterminate"
                (ngModelChange)="demoChangeAll($event)"
              >
                <span>全选</span>
              </label>

              <nz-input-group
                [nzSuffix]="suffixIconSearch1"
                style="width:  calc(100% - 60px);"
              >
                <input
                  type="text"
                  nz-input
                  placeholder="请输入关键词"
                  [(ngModel)]="searchDemographicsViewValue"
                />
              </nz-input-group>
            </div>
            <!-- <nz-input-group [hidden]="selectedId !=='demographics'" [nzSuffix]="suffixIconSearch1">
              <input  type="text" nz-input placeholder="请输入关键词" [(ngModel)]="searchRenValue" />
            </nz-input-group> -->
            <ng-template #suffixIconSearch1>
              <!-- <img src="./assets/images/icon_search.png"> -->
              <i class="iconfont icon-search_o"></i>
            </ng-template>
            <!-- 报告查看部门 -->
            <nz-select
              [hidden]="selectedId !== 'org'"
              style="width: 100%; margin-top: 8px;"
              [(ngModel)]="selectedLevel"
              (ngModelChange)="checkDepartment($event)"
              nzAllowClear
              nzPlaceHolder="筛选条件"
            >
              <nz-option
                *ngFor="let option of data.organizationDistanceList"
                [nzValue]="option.distance"
                [nzLabel]="option.name.zh_CN"
              ></nz-option>
            </nz-select>
            <!-- 可查看填答进度的部门 -->
            <nz-select
              [hidden]="selectedId !== 'orgView'"
              style="width: 100%; margin-top: 8px;"
              [(ngModel)]="selectedViewLevel"
              (ngModelChange)="checkDepartment($event)"
              nzAllowClear
              nzPlaceHolder="筛选条件"
            >
              <nz-option
                *ngFor="let option of data.organizationDistanceList"
                [nzValue]="option.distance"
                [nzLabel]="option.name.zh_CN"
              ></nz-option>
            </nz-select>
            <!-- 360测评 可查看/下载报告 -->
            <div
              [hidden]="selectedId !== 'viewReportFactors'"
              class="space-between"
            >
              <label
                nz-checkbox
                [ngModel]="viewReportFactorsCheckedAll"
                [nzIndeterminate]="viewReportFactorsCheckedIndeterminate"
                (ngModelChange)="demoChangeAll($event)"
              >
                <span>全选</span>
              </label>
              <nz-input-group
                [nzSuffix]="suffixIconSearch1"
                style="width:  calc(100% - 60px);"
              >
                <input
                  type="text"
                  nz-input
                  placeholder="请输入关键词"
                  [(ngModel)]="searchViewReportFactorsValue"
                />
              </nz-input-group>
            </div>
          </div>
          <!-- 报告查看部门 -->
          <div
            [hidden]="selectedId !== 'org'"
            class="scroll-list  content-list scroll-height-1"
          >
            <nz-tree
              #nzOrgTreeComponent
              nzCheckable
              [nzData]="data.organizationTrees"
              [nzExpandAll]="false"
              [nzExpandedKeys]="expandedNodes"
              [nzSearchValue]="searchOrgValue"
              (nzCheckBoxChange)="nzEvent($event)"
              (nzClick)="expanded($event)"
              [nzCheckedKeys]="checkedKeys"
            >
            </nz-tree>
          </div>
          <!-- 可查看填答进度的部门 -->
          <div
            [hidden]="selectedId !== 'orgView'"
            class="scroll-list  content-list scroll-height-1"
          >
            <nz-tree
              #nzOrgViewTreeComponent
              nzCheckable
              [nzData]="data.organizationTreesView"
              [nzExpandAll]="false"
              [nzExpandedKeys]="expandedNodesView"
              [nzSearchValue]="searchOrgViewValue"
              (nzCheckBoxChange)="nzEvent($event)"
              (nzClick)="expanded($event)"
              [nzCheckedKeys]="checkedKeysView"
            >
            </nz-tree>
          </div>
          <!-- 报告分析因子 -->
          <div
            [hidden]="selectedId !== 'demographics'"
            class="scroll-list content-list scroll-height-2"
          >
            <nz-tree
              #nzFactorsComponent
              nzCheckable
              [nzData]="data.demographicTrees"
              [nzExpandAll]="false"
              [nzExpandedKeys]="expandendFactorsNodes"
              [nzSearchValue]="searchDemographicValue"
              (nzCheckBoxChange)="nzEventDemographic($event)"
              (nzClick)="expanded($event)"
              [nzCheckedKeys]="checkedFactorsKeys"
            >
            </nz-tree>
          </div>
          <!-- 可查看填答进度的分析因子 -->
          <div
            [hidden]="selectedId !== 'demographicsView'"
            class="scroll-list content-list scroll-height-2"
          >
            <nz-tree
              #nzAnswerFactorsComponent
              nzCheckable
              [nzData]="data.demographicsView"
              [nzExpandAll]="false"
              [nzExpandedKeys]="expandendAnswerFactorsNodes"
              [nzSearchValue]="searchDemographicsViewValue"
              (nzCheckBoxChange)="nzEventDemographic($event)"
              (nzClick)="expanded($event)"
              [nzCheckedKeys]="checkedAnswerFactorsKeys"
            >
            </nz-tree>
          </div>
          <!-- 报告内外部常模 -->
          <div
            [hidden]="selectedId !== 'norm'"
            class="scroll-list content-list scroll-height-3"
          >
            <ul
              *ngIf="
                data.internalNorms.length !== 0 ||
                data.prismaNorms.length !== 0 ||
                data.orgaztionSinkingAnalysiss?.length !== 0
              "
            >
              <li>
                <label
                  nz-checkbox
                  [(ngModel)]="normCheckedAll"
                  [nzIndeterminate]="normCheckedIndeterminate"
                  (ngModelChange)="normChangeAll($event)"
                >
                  <span>全选</span>
                </label>
              </li>
              <li *ngFor="let item of data.internalNorms">
                <label
                  nz-checkbox
                  [(ngModel)]="item.isChecked"
                  (ngModelChange)="normChange($event)"
                >
                  <span>{{ item.name.zh_CN }}</span>
                </label>
              </li>
              <li *ngFor="let item of data.orgaztionSinkingAnalysiss">
                <label
                  nz-checkbox
                  [(ngModel)]="item.isChecked"
                  (ngModelChange)="normChange($event)"
                >
                  <span>{{ item.name.zh_CN }}</span>
                </label>
              </li>
              <li *ngFor="let item of data.prismaNorms">
                <label
                  nz-checkbox
                  [(ngModel)]="item.isChecked"
                  (ngModelChange)="normChange($event)"
                >
                  <span>{{ item.name.zh_CN }}</span>
                </label>
              </li>
            </ul>
          </div>
          <!-- 报告历史对比 -->
          <div
            [hidden]="selectedId !== 'history'"
            class="scroll-list content-list scroll-height-3"
          >
            <ul
              *ngIf="
                data.internalNorms.length !== 0 || data.prismaNorms.length !== 0
              "
            >
              <li>
                <label
                  nz-checkbox
                  [(ngModel)]="historyCheckedAll"
                  [nzIndeterminate]="historyCheckedIndeterminate"
                  (ngModelChange)="historyChangeAll($event)"
                >
                  <span>全选</span>
                </label>
              </li>
              <li *ngFor="let item of data.historyDataList">
                <label
                  nz-checkbox
                  [(ngModel)]="item.isChecked"
                  (ngModelChange)="historyChange($event)"
                >
                  <span>{{ item.name.zh_CN }}</span>
                </label>
              </li>
            </ul>
          </div>
          <!-- 360测评 可查看/下载报告 -->
          <div
            [hidden]="selectedId !== 'viewReportFactors'"
            class="scroll-list content-list scroll-height-2"
          >
            <nz-tree
              #nzViewReportFactorsComponent
              nzCheckable
              [nzData]="data.viewReportFactors"
              [nzExpandAll]="false"
              [nzExpandedKeys]="expandendViewReportFactorsNodes"
              [nzSearchValue]="searchViewReportFactorsValue"
              (nzCheckBoxChange)="nzEventDemographic($event)"
              (nzClick)="expanded($event)"
              [nzCheckedKeys]="checkedViewReportFactorsKeys"
            >
            </nz-tree>
          </div>
        </div>
      </ng-container>
      <ng-template #emptyTips>
        <div class="empty-text">
          <img src="./assets/images/org/group_empty.png" />
          <div>请先选择左侧人员后进行配置</div>
        </div>
      </ng-template>
    </div>
    <div class="right">
      <nz-collapse
        [nzBordered]="false"
        *ngIf="clickSunManagerId && showTips; else emptyTipsRight"
      >
        <ng-container *ngFor="let panel of panels; let panIndex = index">
          <nz-collapse-panel
            #p
            *ngIf="panel.list.length !== 0"
            [nzHeader]="tempHeader"
            [nzActive]="panel.active"
            [ngStyle]="panel.customStyle"
            [nzExpandedIcon]="expandedIcon"
          >
            <ul class="panel-content">
              <li *ngFor="let panelData of panel.list; let dataIndex = index">
                <span class="text">{{ panelData.name }}</span>
                <span
                  class="iconfont icon-penetra-close"
                  (click)="delete(panel.id, panelData.id, panIndex, dataIndex)"
                ></span>
              </li>
            </ul>
            <ng-template #expandedIcon let-active>
              <i
                nz-icon
                *ngIf="!p.nzActive"
                nzType="plus"
                nzTheme="outline"
                class="expandedIcon"
              ></i>
              <i
                nz-icon
                *ngIf="p.nzActive"
                nzType="minus"
                nzTheme="outline"
                class="expandedIcon"
              ></i>
              <!-- <img *ngIf="!p.nzActive" width="19px" height="19px"
                src="./assets/images/event-management/home/<USER>" alt="">
              <img *ngIf="p.nzActive" width="19px" height="19px"
                src="./assets/images/event-management/home/<USER>" alt=""> -->
            </ng-template>
            <ng-template #tempHeader>
              <div style="margin-left: 8px;">{{ panel.title }}</div>
            </ng-template>
          </nz-collapse-panel>
        </ng-container>
      </nz-collapse>
      <ng-template #emptyTipsRight>
        <div class="empty-text">
          <img src="./assets/images/org/group_empty.png" />
          <div>
            请在左侧“{{ transType === 3 ? "组织架构" : "人口标签" }}”选择需要{{
              transType === 3 ? "调研" : "测评"
            }}的因子，点击后可展开不同因子
          </div>
        </div>
      </ng-template>
    </div>
    <div class="loading-div" *ngIf="isExporting || isImporting">
      <nz-spin nzTip="Loading..." class="loading" nzSimple> </nz-spin>
    </div>
  </div>
  <!-- 侧边栏-操作 -->
  <footer>
    <button nz-button nzType="default" (click)="close()">取消</button>
    <button nz-button nzType="primary" (click)="save()" [nzLoading]="isLoading">
      保存
    </button>
  </footer>
</div>
