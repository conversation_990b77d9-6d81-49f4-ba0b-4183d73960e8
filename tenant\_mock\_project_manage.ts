import { MockRequest } from '@knz/mock';

export const PROJECT_MANAGE = {
  'GET /survey/project/listProject': 
  {
    result:{
      code: 0, 
      message: "test mock prolist"
    } ,
    data : [
      {
        "id": "92288830-fb47-491a-9fc0-b076cbbfb689",
        "name": "test111",
        "status": "ANSWERING",
        "startTime": "2020-06-13T00:00:00.000+0000",
        "endTime": "2020-06-26T20:00:00.000+0000",
        "completedCount": 0,
        "inCompletedCount": 3
      },
      {
        "id": "17f369e6-928c-4592-b9b5-286abec7ba1e",
        "name": "test222",
        "status": "ANSWERING",
        "startTime": "2020-06-13T00:00:00.000+0000",
        "endTime": "2020-07-25T05:00:00.000+0000",
        "completedCount": 0,
        "inCompletedCount": 1
      },
      {
        "id": "3ee25810-fc54-46a1-88d2-7112fba14b9d",
        "name": "string333",
        "status": "ANSWERING",
        "startTime": "2020-06-11T17:18:00.000+0000",
        "endTime": "2020-06-14T05:24:00.000+0000",
        "completedCount": 0,
        "inCompletedCount": 1
      },
      {
        "id": "93cf9117-7dd9-4f33-95d8-0349ccdbf86b",
        "name": "yyyyy444",
        "status": "OVER",
        "startTime": "2020-06-13T00:00:00.000+0000",
        "endTime": "2020-07-15T00:00:00.000+0000",
        "completedCount": 0,
        "inCompletedCount": 1
      }
    ], 
  },

  'POST /survey/person/listByProjectId': (req: MockRequest) => {
    console.log( "mock req projectId== "  + req.queryString.projectId);
    return {
      result:{
        code: 0, 
        message: "test mock usrlist"
      } ,
      data: [
        {
          seq: 2,
          firstName: "Tayer Swift",
          lastName:"sdfdsf",
          startTime: "2009-09-25 18:20",
          endTime: "2009-09-25 18:20",
          answerStatus: "ANSWERED",
        },
        {
          seq: 1,
          firstName: "bbb 老师可抵扣",
          lastName:"21233",
          startTime: "2009-05-04 18:20",
          endTime: "2009-05-04 18:20",
          answerStatus: "UNANSWERED",
        },
        {
          seq: 3,
          firstName: "aaa 未看考试",
          lastName:"kjsrj",
          startTime: "2009-12-05 18:20",
          endTime: "2009-12-05 18:20",
          answerStatus: "ANSWERED",
        }
      ],

    };
  },

  'PUT /survey/project/updateStatus': 
  {
    result:{
      code: 0, 
      message: "test mock updateStatus"
    } ,
    data : "mock success"
  },

  'POST /sagittarius/project/sendEmail': 
  {
    result:{
      code: 0, 
      message: "test mock sendEmail"
    } ,
    data : "mock success"
  },

  'GET /survey/project/generateCode/:projectId': (req: MockRequest) =>{
    console.log("== generateCode projectId = " + req.params.projectId);
    return {
      result:{
        code: 0, 
        message: "test mock generateCode"
      } ,
      data : "mock success"
    }
  }

}; 
