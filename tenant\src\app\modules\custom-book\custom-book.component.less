// @import '../../../styles/variable.less';

.search-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 14px;
  padding-top: 22px;
}

.bread {
  color: #17314c;
  font-size: 24px;
  margin-right: 10px;
}

.custom-page {
  background: #f5f6fa;
  height: calc(100vh - 170px);
}

.block {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #fbb853;
  margin-left: 5px;
  cursor: pointer;
}

.invite_button {
  width: 140px;
  height: 55px;
  background-image: url("../../../assets/images/invite_button.png");
  background-position: center center;
  background-color: transparent;
  border-style: none;
  border-radius: 20px;
  // padding-left: 18px;
  // padding-bottom: 8px;
  margin-left: -8px;
  margin-top: 5px;
  cursor: pointer;
}

.invite_button > span,
.export_button > span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.upload_button_text {
  padding-bottom: 4px;
  display: block;
}

.upload_button {
  width: 123px;
  height: 38px;
  font-size: 16px;
  font-weight: 500;
  color: #419eff;
  line-height: 38px;
  padding-left: 30px;
  margin-left: 10px;
  background: url("../../../assets/images/upload_bg.png") no-repeat;
  border: 0;
}

.upload_button_text {
  font-size: 14px;
  font-weight: 500;
  padding-left: 8px;
}

.svg-ico-delete {
  background-color: #f19672;
}

.delete-box {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-left: 30px;
}

.delete-txt {
  color: #f19672;
  margin-left: 6px;
}

.btn-box {
  display: flex;
  align-items: center;
}

.btn-parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  margin-top: 16px;
}

.ant-table-tbody {
  background-color: #fff;
}

.next_button {
  cursor: pointer;
  width: 128px;
  height: 38px;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
}

.fixed {
  height: 70px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.score {
  color: #17314c;
  font-size: 18px;
  margin-right: 48px;
}

.fixed-box {
  background-color: #fff;
}

.to-delete {
  color: #f19672;
}

.round-btn {
  padding: 2px 10px;
  border-radius: 30px;
  border: 1px solid #419eff;
  color: #419eff;
  background-color: #ffffff;
  margin-top: 10px;
  cursor: pointer;
}

.icon-search {
  color: #409eff;
}

.input-search {
  border-radius: 20px;
  width: 418px;
  // padding-top: 18px;
  // padding-bottom: 18px;
}

:host ::ng-deep .ant-upload-list-item {
  display: none;
}

.through_ul {
  display: flex;
  height: calc(100vh - 240px);

  > li {
    flex: 1;
    padding: 20px 10px;
    border-top: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;

    p {
      margin: 10px 0;
      padding-left: 40px;
      color: #aaaaaa;
    }
  }

  .th_li_left {
    // height: 400px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .th_li_right {
    // height: 400px;
    height: 100%;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
  }

  .question-box {
    // height: calc(100% - 80px);
    flex: 1;
    overflow-y: auto;

    > li {
      display: flex;

      .label {
        display: flex;
        width: 18px;
        height: 18px;
      }

      > ul {
        margin-left: 14px;
        margin-bottom: 20px;
        width: 91%;

        li {
          display: flex;
          // align-items: center;

          .icon-xiala {
            cursor: pointer;
            transform: rotate(0deg);
            margin-right: 10px;
            // margin-top: -20px;
          }

          .turn {
            transform: rotate(90deg);
          }

          .icon-xiala,
          .turn {
            transition: all 0.1s linear;
          }
        }
        .secondOpt {
          flex-direction: column;
          align-items: flex-start;
          &-opt {
            display: flex;
            align-items: center;
          }
          &-card {
            background-color: #f8f8f8;
            border-radius: 6px;
            width: 100%;
            padding: 4px 8px;
            margin: 4px 0;
          }
        }
      }
    }
  }
}

.button_ul {
  margin-top: 30px;
  display: flex;

  li {
    cursor: pointer;
  }

  .btnok {
    width: 128px;
    line-height: 38px;
    text-align: center;
    color: #fff;
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
  }

  .btndef {
    margin-left: 20px;
    color: #aaaaaa;
    width: 128px;
    line-height: 38px;
    text-align: center;
    background: #fafafa;
    border-radius: 19px;
  }
}

.all_open {
  // position: absolute;
  // right: 70px;
  // top: 15px;
  width: 115px;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  color: #fff;
  background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
  box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
  border-radius: 15px;
}

.cardFit {
  padding: 0 10px;
  // text-align: center;
  color: #fff;
  line-height: 25px;
  font-size: 12px;
  border-radius: 2px;
}

.card_bg1 {
  color: #9833ff;
  background: #f7efff;
}

.card_bg2 {
  color: #3372ff;
  background: #eff4ff;
}

.card_bg3 {
  color: #24cc9e;
  background: #eefbf8;
}

.card_bg4 {
  color: #238fff;
  background: #e4f1ff;
}

.card_bg5 {
  color: #12c6f9;
  background: #ecfbff;
}

.card_bg6 {
  color: #ff8d33;
  background: #fff6ef;
}

.card_bg7 {
  color: #ff58a6;
  background: #f8ebf1;
}

// Cardcolors = ['#FF7A7A', '#FFB03D', '#409EFF', '#6EC699', '#59BFD0', '#6377FE', '#D47CFF', '#74B2FF', '#76C227', '#82A0D5', '#FF64BC', '#4CA7BF']

::ng-deep {
  .popover-header {
    // 关联详情浮层
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 575px;
    height: 55px;

    span {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #17314c;
      line-height: 25px;
    }
    a {
      font-size: 14px;
      font-weight: normal;
      line-height: 25px;
    }
  }

  .popover-body {
    ul {
      width: 575px;
      height: 384px;
      overflow-y: auto;

      li {
        margin-bottom: 5px;
        background: #f5f6fa;
        padding: 0 15px;

        .condition-box {
          display: flex;
          justify-content: space-between;
          border-bottom: 1px solid #e6e6e6;
          padding: 14px 15px;

          .popover-tips {
            text-align: center;
            width: 34px;
            height: 21px;
            line-height: 21px;
            background: rgba(51, 114, 255, 0.08);
            border-radius: 2px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #3372ff;
            margin-right: 10px;
          }
        }

        .popover-text {
          flex: 1;
          p {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
          }
        }

        .result-box {
          display: flex;
          justify-content: space-between;
          padding: 14px 15px;

          .popover-res-tips {
            text-align: center;
            width: 34px;
            height: 21px;
            line-height: 21px;
            background: rgba(36, 204, 158, 0.08);
            border-radius: 2px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #24cc9e;
            margin-right: 10px;
          }
        }

        .popover-del {
          width: 18px;
          height: 18px;
          cursor: pointer;
          background: url(../../../assets/images/org/del.png) no-repeat;
        }

        .popover-del:hover {
          background: url(../../../assets/images/org/del_hover.png);
        }
      }
    }

    .close-association {
      width: 100%;
      height: 38px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .penetra-modal {
    .ant-modal-footer {
      margin: 0 30px;
      padding: 20px 0;
      text-align: left;

      button {
        width: 128px;
        height: 38px;
        background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 22px;
      }

      .cancel-btn {
        background: #fafafa;
        color: #aaaaaa;
        border: none;
        box-shadow: none;
      }
    }

    .ant-modal-header {
      padding: 0;
      border: none;

      header {
        .penetra-modal-close {
          text-align: right;
          margin-bottom: 5px;

          .icon-penetra-close {
            font-size: 11px;
            margin-top: 7px;
            margin-right: 7px;
            cursor: pointer;
          }
        }

        .penetra-modal-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin: 0 30px;
          padding-bottom: 18px;
          border-bottom: 1px solid #e6e6e6;

          span {
            font-size: 24px;
            font-family: PingFangSC-Light, PingFang SC;
            font-weight: 300;
            color: #17314c;
            line-height: 33px;
          }

          .associate-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px 15px;
            height: 30px;
            background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
            box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
            border-radius: 15px;
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 22px;
            cursor: pointer;
          }
        }
      }
    }

    .ant-modal-body {
      padding: 0;
    }

    .penetra-modal-body {
      display: flex;

      > div {
        padding: 20px 30px;
        height: 474px;
      }

      .left {
        flex: 1;
      }

      .right {
        width: 480px;
        background: #f8f8f8;
      }

      .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }

      .tips {
        margin-left: 43px;
        margin-bottom: 10px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 17px;
      }

      .question-box {
        height: calc(100% - 80px);
        overflow-y: auto;

        > li {
          display: flex;

          .label {
            display: flex;
            width: 18px;
            height: 18px;
          }

          > ul {
            margin-left: 14px;
            margin-bottom: 20px;

            li {
              display: flex;
              align-items: center;

              .icon-xiala {
                cursor: pointer;
                transform: rotate(-90deg);
                margin-right: 10px;
              }

              .turn {
                transform: rotate(0deg);
              }

              .icon-xiala,
              .turn {
                transition: all 0.1s linear;
              }
            }
          }
        }
      }
    }
  }
}

.tip-box {
  display: inline-block;
  background: rgba(51, 114, 255, 0.08);
  border-radius: 2px;
  padding: 2px 5px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 17px;
  // text-align: center;
  word-wrap: break-word;
}

::ng-deep {
  .through-tr {
    border-bottom: none;

    td {
      border-bottom: none !important;
    }
  }
}

.title-icon {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #495970;
  line-height: 20px;
  cursor: pointer;

  .iconfont {
    font-size: 19px;
  }
}

.title-icon:hover {
  color: #409eff;

  .iconfont {
    color: #409eff;
  }
}

.bottom {
  display: flex;
  align-items: center;
  background: #ffffff;
  box-shadow: 0px -7px 7px 0px rgba(0, 0, 0, 0.03);
  // padding: 30px;
  padding: 24px 0 0 0;
}

.popup-method {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 33px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #495970;
  line-height: 17px;
}

::ng-deep {
  .through-box {
    .ant-modal-header {
      padding: 0;
      border-bottom: transparent;
    }
  }
}

// .load_bt{
//   position: absolute;
//   left: 200px;
//   top: 15px;
// }
::ng-deep {
  .round-right-drawer1 {
    .ant-drawer-body {
      padding: 16px;
      // height: calc(100% - 55px);
      // padding-bottom: 66px;
      height: calc(100% - 108px);
      overflow: auto;
      scrollbar-color: auto;
      scrollbar-width: auto;

      // .vxscrollbar();
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      // 滑块背景
      &::-webkit-scrollbar-track {
        // background-color: transparent;
        background-color: #f1f1f1;
        box-shadow: none;
      }

      // 滑块
      &::-webkit-scrollbar-thumb {
        // background-color: #e9e9e9;
        background-color: #c1c1c1;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
        outline: none;
      }
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }

    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}

.primary-btn {
  height: 38px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  padding: 0 16px 0 12px;
  color: #fff;

  > i {
    font-size: 24px;
    margin-right: 9px;
    color: #fff;
  }

  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  line-height: 22px;
  cursor: pointer;
}

.default-btn {
  height: 36px;
  background: transparent;
  border: 1px solid #409eff;
  color: #409eff;
  border-radius: 24px;
  padding: 0 16px 0 12px;
  display: flex;
  justify-content: center;
  align-items: center;

  > i {
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    font-size: 12px;
    margin-right: 9px;
    color: #fff;
    border-radius: 50%;
    background-color: #409eff;
  }

  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  cursor: pointer;
}

.mb-12 {
  margin-bottom: 12px;
}

.mt-12 {
  margin-top: 12px;
}

.mr-16 {
  margin-right: 16px;
}
