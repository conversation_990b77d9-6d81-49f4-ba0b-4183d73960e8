<!-- 新建活动的工具列表， “我的专属”列表复用这块-->
<div class="tool_cards">
  <ul class="cards_ul " [ngClass]="'after_' + fl_num">
    <li
      *ngFor="let item of toolslist; let i = index"
      style="display: flex; flex-direction: column;"
    >
      <div class="title_top" [ngStyle]="{ background: arrColor[i % 11] }">
        <!-- 问卷名称  -->
        <span>{{ item.name.zh_CN }}</span>
        <!-- 角标 -->
        <div class="title_tips" *ngIf="item.cornerMark != ''">
          {{ item.cornerMark.slice(0, 5) }}
        </div>
      </div>
      <div
        style="padding: 0 20px; display: flex; flex-direction: column; flex: 1;"
      >
        <div class="tips_w" style="display: flex; align-items: flex-end;">
          <div style="display: flex; align-items: flex-end;">
            <span>售价：</span>
            <!-- 报告风格总和的原始金额，如果有折扣，原始金额不呈现 -->
            <div style="position: relative" *ngIf="!item.isDiscount">
              <span style="font-size: 20px">
                {{ item.totalprice }}
              </span>
              <span>K米/账号</span>
              <!-- 有折扣的时候，原始金额加删除线呈现。 20230615迭代 US#7053 废弃此逻辑-->
              <hr *ngIf="item.isDiscount" class="line_throw" />
            </div>
          </div>
          <!-- 如果有折扣，显示折扣金额且为红色 -->
          <ng-container *ngIf="item.isDiscount">
            <span style="font-size: 20px; margin-left: 0px;color: #F84444;">
              {{ item.currentPrice }}
            </span>
            <span style="color: #F84444;">K米/账号</span>
          </ng-container>
        </div>

        <!-- 营销中的包年模式 -->
        <div
          *ngIf="item.isPackageYear"
          class="tips_w"
          style="display: flex; flex-wrap: wrap; align-items: center;"
        >
          <span>模式：</span>
          <div class="tools_use_1">
            <div class="use_d use_c">包年(不限数量)</div>
          </div>
        </div>

        <!-- 报告风格 -->
        <div style="display: flex; flex-wrap: wrap; flex: 1;">
          <span style="margin-top: 10px;">报告：</span>
          <div class="tools_use">
            <ng-container *ngFor="let val of item.reportProjects">
              <div
                class="use_d"
                [ngClass]="val.checked ? 'use_c' : ''"
                (click)="
                  getcards(
                    toolslist,
                    item,
                    val,
                    item.id,
                    val.style,
                    item.isStandalone
                  )
                "
              >
                <span>{{ val.name.zh_CN }}</span>
              </div>
            </ng-container>
          </div>
        </div>
        <!-- TIP2.0 人才画像类问卷，需要多个工具模块 -->
        <div
          *ngIf="item.reportType == 'TIP_NEW_2'"
          style="display: flex; flex-wrap: wrap; flex: 1; margin-bottom: 15px;"
        >
          <span style="margin-top: 10px;">工具：</span>
          <div class="tools_use">
            <ng-container
              *ngFor="
                let val of item.standardDimensionResultVOList;
                let j = index
              "
            >
              <ng-container
                *ngIf="
                  val.reportType !== 'CSI' && val.reportType !== 'CSI_DT';
                  else mhs
                "
              >
                <!-- TIP2.0问卷绑定的报告的详细得分模块种开启了显示的各个大维度名称 -->
                <div
                  class="use_d"
                  *ngIf="val.showcard"
                  [ngClass]="val.checked ? 'use_c' : ''"
                  (click)="
                    getcards(
                      toolslist,
                      item,
                      val,
                      item.id,
                      val.style,
                      item.isStandalone,
                      val.name
                    )
                  "
                >
                  <span>{{ val.name.zh_CN }}</span>
                </div>
                <!-- 胜任力，领导力，情景领导力 三类工具互斥，如果选择了其中一个，其他两个呈现不可选状态 -->
                <div class="use_d un_choose" *ngIf="!val.showcard">
                  <span>{{ val.name.zh_CN }}</span>
                </div>
              </ng-container>
              <ng-template #mhs>
                <!-- TIP2.0问卷绑定的报告的详细得分模块种开启了显示的各个大维度名称 -->
                <div
                  class="use_d"
                  *ngIf="val.showcardMhs"
                  [ngClass]="val.checked ? 'use_c' : ''"
                  (click)="
                    getcards(
                      toolslist,
                      item,
                      val,
                      item.id,
                      val.style,
                      item.isStandalone,
                      val.name
                    )
                  "
                >
                  <span>{{ val.name.zh_CN }}</span>
                </div>
                <!-- ，情景领导力 工具互斥，如果选择了其中一个，其他两个呈现不可选状态 -->
                <div class="use_d un_choose" *ngIf="!val.showcardMhs">
                  <span>{{ val.name.zh_CN }}</span>
                </div>
              </ng-template>
            </ng-container>
          </div>
        </div>
        <div
          style="display: flex; justify-content: flex-end;"
          *ngIf="
          item.reportType.indexOf('DP_INVESTIGATION_RESEARCH_CUSTOM') != -1 || item.reportType.indexOf('_INVESTIGATION_RESEARCH_CUSTOM') == -1
          "
        >
          <span
            style="cursor: pointer; color: #409EFF; font-size: 12px;"
            (click)="Jumplist(item)"
          >
            了解详情
          </span>
        </div>
      </div>
    </li>
  </ul>
</div>
