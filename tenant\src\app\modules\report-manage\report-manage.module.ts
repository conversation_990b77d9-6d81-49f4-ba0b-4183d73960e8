import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ReportManageRoutingModule } from "./report-manage-routing.module";
import { SharedModule } from "../../shared/shared.module";
import { UserinfoComponent } from "./userinfo/userinfo.component";
import { ReportTitlePipe } from "./report-title.pipe";
import { DownloadHistoryComponent } from "./download-history/download-history.component";
import { GroupCreateComponent } from "./group-create/group-create.component";
import { SurveyCreateComponent } from "./survey-create/survey-create.component";
import { SurveyHistoryComponent } from "./survey-history/survey-history.component";
import { ReportHomeComponent } from "./report-home/report-home.component";
import { FileDownloadComponent } from "./file-download/file-download.component";
import { UnionCreateComponent } from "./union-create/union-create.component";
import { UnionSurveyComponent } from "./union-create/union-survey/union-survey.component";
import { UnionAssessmentComponent } from "./union-create/union-assessment/union-assessment.component";
import { SingleSurveyComponent } from "./union-create/single-survey/single-survey.component";
import { compareconfig } from "./report-home/compare/compare.component";
import { lineComponent } from "./report-home/lineChart/lineChart.component";

import { ColumnSetComponent } from "./column-set/column-set.component";
import { PrismaSetComponent } from "./prisma-set/prisma-set.component";
import { XiShuComponent } from "./prisma-set/xi-shu/xi-shu.component";
import { IdpComponent } from "./prisma-set/idp/idp.component";
import { DownloadListComponent } from "./report-home/download-list/download-list.component";
import { MarkComponent } from "./prisma-set/custommark/mark.component";

import { ObjectSettingComponent } from "./union-create/object-setting/object-setting.component";
import { RenderSettingComponent } from "./union-create/render-setting/render-setting.component";
import { IndexTipComponent } from "./union-create/index-tip/index-tip.component";
import { BatchSendComponent } from "./batch-send/batch-send.component";
import { AdvancedFiltersComponent } from "./union-create/advancedFilters/advanced-filters.component";

// 在线看板
import { OnlineReportComponent } from "./online-report/online-report.component";

@NgModule({
  declarations: [
    UserinfoComponent,
    ReportTitlePipe,
    DownloadHistoryComponent,
    GroupCreateComponent,
    SurveyCreateComponent,
    SurveyHistoryComponent,
    ReportHomeComponent,
    FileDownloadComponent,
    UnionCreateComponent,
    UnionSurveyComponent,
    UnionAssessmentComponent,
    SingleSurveyComponent,
    compareconfig,
    lineComponent,
    ColumnSetComponent,
    PrismaSetComponent,
    XiShuComponent,
    IdpComponent,
    DownloadListComponent,
    MarkComponent,
    ObjectSettingComponent,
    RenderSettingComponent,
    IndexTipComponent,
    BatchSendComponent,
    AdvancedFiltersComponent,
    OnlineReportComponent,
  ],
  imports: [CommonModule, ReportManageRoutingModule, SharedModule],
  exports: [AdvancedFiltersComponent],
  entryComponents: [
    DownloadHistoryComponent,
    GroupCreateComponent,
    SurveyCreateComponent,
    SurveyHistoryComponent,
    FileDownloadComponent,
    UserinfoComponent,
    UnionCreateComponent,
    ColumnSetComponent,
    PrismaSetComponent,
    DownloadListComponent,
    AdvancedFiltersComponent,
  ],
})
export class ReportManageModule {}
