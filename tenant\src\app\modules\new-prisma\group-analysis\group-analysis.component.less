@primaryColor: #40a9ff;
@minorColor: #f5f8ff;
@delColor: #f19672;
@fgColor: #fff;
@pageBg: #e6e6e6;
@textColor_1: #17314c;
@textColor_2: #495970;
@textColor_3: #e6e6e6;
@textColor_4: #aaaaaa;
@textColor_5: #c4c4c4;
@textColor_6: #efefef;
@itemBgColor: #f5f6fa;
@tagBgColor: #f6f6f6;

:root {
  --ifm-scrollbar-size: 7px;
  --ifm-scrollbar-track-background-color: #f1f1f1;
  --ifm-scrollbar-thumb-background-color: silver;
  --ifm-scrollbar-thumb-hover-background-color: #a7a7a7;
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
// 滑块背景
::-webkit-scrollbar-track {
  // background-color: transparent;
  background-color: #F1F1F1;
  box-shadow: none;
}
// 滑块
::-webkit-scrollbar-thumb {
  // background-color: #e9e9e9;
  background-color: #C1C1C1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}
.box {
  .row {
    border-radius: 4px;
    height: calc(100vh - 140px);
    border: 1px solid @textColor_3;
    > div {
      border-left: 1px solid @textColor_3;
      height: 100%;
      &:nth-child(1) {
        border-left: none;
      }
      .title {
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid @textColor_3;
        > div {
          span {
            &:nth-child(1) {
              font-size: 16px;
              font-weight: 400;
              color: @textColor_1;
              line-height: 22px;
              margin-right: 10px;
            }
            &:nth-child(2) {
              font-size: 14px;
              font-weight: 400;
              color: @textColor_4;
              line-height: 20px;
            }
          }
        }
        button {
          padding: 0;
        }
      }
      .scroll {
        &-inside {
          margin: 4px;
          padding: 11px;
          height: calc(100vh - 212px);
          overflow-y: auto;
          .right {
            border-bottom: 1px solid #e6e6e6;
            padding: 16px 0;
            &:nth-child(1) {
              padding-top: 0;
            }
            &:nth-last-child(1) {
              border-bottom: none;
            }
            .pointer {
              cursor: pointer;
            }
            &-title {
              width: 100%;
              padding: 0 12px 8px 6px;
              display: flex;
              justify-content: space-between;
              &-text {
                width: 90%;
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                b {
                  font-size: 16px;
                  font-weight: 500;
                  color: @textColor_2;
                  margin-right: 10px;
                }
                span {
                  margin-right: 8px;
                  font-size: 14px;
                  color: @textColor_2;
                }
                .cross {
                  font-size: 14px;
                  font-weight: 400;
                  // color:  @textColor_2;
                  color: @primaryColor;
                }
              }
              i {
                width: 20px;
                height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                color: @fgColor;
                background-color: @textColor_5;
                border-radius: 50%;
                font-size: 12px;
                cursor: pointer;
                &:hover {
                  background-color: @delColor;
                }
              }
            }
            &-card {
              width: 100%;
              border-radius: 4px;
              background-color: #f5f6fa;
              padding: 16px 12px;
              margin-top: 8px;
              display: flex;
              justify-content: space-between;
              &-info {
                width: 90%;
                font-size: 14px;
                span {
                  margin-right: 8px;
                }
                .tag {
                  font-weight: 400;
                  color: #495970;
                }
                .val {
                  font-weight: 500;
                  color: #409eff;
                }
                .also {
                  font-weight: 400;
                  color: #409eff;
                }
              }
              i {
                width: 20px;
                height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                color: @fgColor;
                background-color: @textColor_5;
                border-radius: 50%;
                font-size: 12px;
                cursor: pointer;
                &:hover {
                  background-color: @delColor;
                }
              }
            }
          }
        }
      }
      .hasBtns {
        height: calc(100vh - 260px);
      }
      .btns {
        height: 48px;
        border-top: 1px solid @textColor_3;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        button {
          margin-right: 9px;
          color: @primaryColor;
          border: 1px solid @primaryColor;
          &:hover {
            background: linear-gradient(270deg, #74cdff 0%, #409eff 100%);
            border: none;
            color: @fgColor;
          }
        }
      }
    }
  }
}
.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
