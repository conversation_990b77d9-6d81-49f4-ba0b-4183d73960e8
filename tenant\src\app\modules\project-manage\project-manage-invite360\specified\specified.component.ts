import { Component, Input, OnInit } from '@angular/core';
import { ProjectManageService } from '../../../service/project-manage.service';
import { TransferItem,TransferChange } from 'ng-zorro-antd/transfer';
@Component({
  selector: 'app-specified',
  templateUrl: './specified.component.html',
  styleUrls: ['./specified.component.less']
})

export class SpecifiedComponent implements OnInit {

  @Input() list:TransferItem[] = [];
  @Input() projectId;
  @Input() type
  @Input() currentpage 
  @Input() totalpage 
  @Input() totalnumber 
  @Input() listtype
  searchFiled = ''
  rightlist = []
  selectkeys = []
  constructor(  private api: ProjectManageService) { }

  ngOnInit() {
    this.listMap()
  }

  filterOption(inputValue: string, item: any): boolean {
    return item.description.indexOf(inputValue) > -1;
  }
  listMap(){
    this.list = this.list.map(item => {
      let name : string = item.name && item.name.zh_CN ? item.name.zh_CN : item.name;
      return {
        key: item.id,
        title: name ? name : item.firstName,
        description: name ? name : item.firstName,
        direction: 'left',
        email: item.email,
        phone:item.phone,
        firstName:name ?  name : item.firstName, //人员名称
        captcha:item.captcha,//验证码
        wxWorkUserId:item.wxWorkUserId,//企业微信
      }
    })
    this.list.unshift(...this.selectkeys)
    this.list = this.removalchildren(this.list)

  }
  search(ret): void {
    
    this.searchFiled = ret.value
    if(this.listtype === 'orgcode') {
      let params = {
        projectId: this.projectId,
        searchContent: this.searchFiled,
        pageRequest: {
          current: 1,
          size: 100
        }
      }
      this.api.getOrgList(params).subscribe(res => {
        if (res.result.code === 0) {
          this.list = res.data
          this.currentpage = res.page.current
          this.totalpage = res.page.pages
          this.totalnumber = res.page.total
          this.listMap()
        }
      })
    }
    if(this.listtype === 'code') {
      let params = {
        projectId: this.projectId,
        searchFiled: this.searchFiled,
        page: {
          current: 1,
          size: 100
        }
      }
      
      this.api.listInvestigatorGroupnew(params).subscribe(res =>{
        if (res.result.code === 0) {
          this.list = res.data
          this.currentpage = res.page.current
          this.totalpage = res.page.pages
          this.totalnumber = res.page.total
          this.listMap()
        }
      })
    }
    if(this.listtype === 1 || this.listtype == 'message'){
      let params = {
        projectId: this.projectId,
        type: this.type,
        searchFiled: this.searchFiled,
        page: {
          current: 1,
          size: 100
        }
      }
      this.api.listNotInvitedprismanew(params).subscribe(res =>{
        if (res.result.code === 0) {
          this.list = res.data
          this.currentpage = res.page.current
          this.totalpage = res.page.pages
          this.totalnumber = res.page.total
          this.listMap()
        }
      })
    }
    if(this.listtype === 0 || this.listtype === 'mail'){
      let params = {
        projectId: this.projectId,
        searchFiled: this.searchFiled,
        page: {
          current: 1,
          size: 100
        }
      }
      this.api.listInvestigatorGroupnew(params).subscribe(res =>{
        if (res.result.code === 0) {
          this.list = res.data
          this.currentpage = res.page.current
          this.totalpage = res.page.pages
          this.totalnumber = res.page.total
          this.listMap()
        }
      })
    }
    
  }

  select(ret: {}): void {
  }

  change(ret:TransferChange): void {
   
   this.rightlist =  this.list.filter(item =>{
     return item.direction == "right"
   })

   this.rightlist.forEach(item =>{
    this.selectkeys.push(item)
   })
   this.selectkeys = this.removalchildren(this.selectkeys)
   if(ret.from == 'right'){
     for (let i = 0; i < ret.list.length; i++) {
      this.selectkeys.forEach((item,j) =>{
        if(ret.list[i].key == item.key){
          this.selectkeys.splice(j,1)
        }
      })
     }
   }
  }
  removalchildren(arr) {
    let result = []
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      if (!obj[arr[i].key]) {
        result.push(arr[i]);
        obj[arr[i].key] = true;
      }
    }
    return result
  }//去重

  nzPageIndexChange(e){
    if(this.listtype == 'message'){
      let params = {
        projectId: this.projectId,
        type: this.type,
        searchFiled:this.searchFiled,
        page: {
          current: this.currentpage,
          size: 100
        }
      }
      this.api.listNotInvitedprismanew(params).subscribe(res =>{
        if (res.result.code === 0) {
          this.list = res.data
          this.listMap()
          this.currentpage = res.page.current
        }
      })
    }
    if(this.listtype == 'code'){
      let params = {
        projectId: this.projectId,
        searchFiled:this.searchFiled,
        page: {
          current: this.currentpage,
          size: 100
        }
      }
      
      this.api.listInvestigatorGroupnew(params).subscribe(res =>{
        if (res.result.code === 0) {
          this.list = res.data
          this.listMap()
          this.currentpage = res.page.current
        }
      })
    }
    
  }

}
