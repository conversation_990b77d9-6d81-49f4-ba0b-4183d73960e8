import { Component, OnInit, Input } from "@angular/core";

import { NzMessageService, NzModalService, NzModalRef } from "ng-zorro-antd";
import { NzFormatEmitEvent } from "ng-zorro-antd/core";
import _ from "lodash";

import { NewActivityService } from "@src/modules/new-activity/new-activity.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { OpenSubjectComponent } from "../../open-subject/open-subject.component";
import { TouristModule } from "@src/modules/tourist/tourist.module";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-survey-book-modal",
  templateUrl: "./survey-book-modal.component.html",
  styleUrls: ["./survey-book-modal.component.less"],
})
export class SurveyBookModalComponent implements OnInit {
  @Input() projectId: string;
  @Input() questionnaireId: string;

  constructor(
    private api: NewActivityService,
    private surveySerivce: SurveyApiService,
    private msg: NzMessageService,
    private modalService: NzModalService,
    private modalRef: NzModalRef,
    private customMsg: MessageService
  ) {}

  selectedValue: number = 0;
  dimensionDetailData: any = [];
  dimensionList: any = [];
  questionsList: any = [];
  isAllChecked: boolean = false; // 全选
  isAllDisable: boolean = false; // 全选禁用
  questionsIndex: number = 0;
  chooseQuestionsId: any = [];
  isLoading: boolean = false;
  dimensionNum: number = 0;
  keyWord: string = "";

  openQuestionIndex: number = 0; // 题目列表
  openQuestionIndexClass: number = 0; // 题目列表
  openQuestionNumClass: number = 0; // 添加列表
  createOpenQuestion: any[] = [];
  showOption: boolean = false; // 展示选项
  showAdd: boolean = false; // 新增开放题

  openQuestion = {
    // 标准开放题
    dimensionCode: "",
    name: {
      zh_CN: "",
      en_US: "",
    },
    isRequire: false,
    questionnaireId: "",
    isDeleted: true,
    type: "ESSAY_QUESTION",
  };
  manyOpenQuertion = {
    // 多选式开放题
    dimensionCode: "",
    name: {
      zh_CN: "",
      en_US: "",
    },
    questionnaireId: "",
    isDeleted: true,
    isRequire: false,
    type: "MULTIPLE_CHOICE_ESSAY_QUESTION",
    options: {
      multiSelectMin: null,
      multiSelectMax: null,
      options: [
        {
          id: 1,
          isEnableOpenAnswer: true,
          name: {
            zh_CN: "",
            en_US: "",
          },
        },
      ],
    },
  };
  addOptionObj = {
    id: 1,
    isEnableOpenAnswer: true,
    name: {
      zh_CN: "",
      en_US: "",
    },
  };

  // 树节点相关变量
  defaultCheckedKeys: any[] = [];
  defaultSelectedKey: string = "";
  defaultExpandedKeys: any[] = [];

  ngOnInit() {
    this.loadData();
  }

  loadData() {
    this.api.getTopicsBasedOnClassification(this.projectId).subscribe((res) => {
      res.data.forEach((element, index) => {
        if (index === 0)
          this.defaultSelectedKey =
            res.data[this.selectedValue].dimensionQuestionVos[0].dimensionCode;

        if (element.type === "EEI") {
          element.title = "敬业度" + element.type;
          this.defaultExpandedKeys = [element.type];
        }
        if (element.type === "ESI") element.title = "满意度" + element.type;
        if (element.type === "OTHERS") element.title = "开放题";
        element.key = element.type;
        element.checked = false;
        element.children = element.dimensionQuestionVos;
        element.children.forEach((item) => {
          item.title = item.dimensionName["zh_CN"];
          item.key = item.dimensionCode;
          item.isLeaf = true;
        });
      });
      this.dimensionDetailData = res.data;

      this.switchDimension();
      this.chooseDimension(0);
      this.dimensionCount();
    });
  }

  /**
   *
   * 左侧题目
   */
  nzEvent(event: NzFormatEmitEvent): void {
    console.log(event);
  }

  /** 左侧题目 end **/

  // 切换维度大分类
  switchDimension(e?) {
    // if(this.dimensionList.length !== 0) this.dimensionDetailData[this.selectedValue] = this.dimensionList
    // 切换维度大分类
    this.dimensionList = this.dimensionDetailData[this.selectedValue];
    this.questionsList = [];
    this.isAllChecked = false;
    this.isAllDisable = true;
    this.chooseDimension(0);
  }

  // 选择题目类型
  chooseDimension(key, origin?) {
    if (origin) {
      if (origin.key === "EEI") this.selectedValue = 0;
      if (origin.key === "ESI") this.selectedValue = 1;
      if (origin.key === "OTHERS") this.selectedValue = 2;
      this.switchDimension();
    }

    let index = key;
    if (isNaN(key)) {
      this.defaultSelectedKey = key;
      console.log(
        origin.dimensionQuestionVos.findIndex(
          (item) => item.dimensionCode === key
        )
      );

      index = origin.dimensionQuestionVos.findIndex(
        (item) => item.dimensionCode === key
      );

      console.log(this.dimensionList.dimensionQuestionVos);
    }
    this.questionsIndex = index;
    this.dimensionList.dimensionQuestionVos[index].dimensionCode ===
    "KFSHD-prisma076-077"
      ? (this.showAdd = true)
      : (this.showAdd = false);
    this.questionsList = this.dimensionList.dimensionQuestionVos[
      index
    ].surveyQuestions;
    this.checkStatus();
  }

  // 判断选中状态
  checkStatus() {
    let flag = this.questionsList.every((item) => {
      return item.isDeleted === false;
    });
    let allFlag = this.questionsList.every((item) => {
      return item.isForceSelect === true;
    });
    flag === true ? (this.isAllChecked = true) : (this.isAllChecked = false);
    allFlag === true ? (this.isAllDisable = true) : (this.isAllDisable = false);
  }

  // 全选
  checkAll(e) {
    let flag = this.dimensionDetailData[
      this.selectedValue
    ].dimensionQuestionVos[this.questionsIndex].surveyQuestions.every(
      (item) => {
        return item.isDeleted === false;
      }
    );
    if (e === true && flag !== true) {
      this.dimensionDetailData[this.selectedValue].dimensionQuestionVos[
        this.questionsIndex
      ].surveyQuestions.map((item) => {
        item.isDeleted = false;
      });
    }
    if (e === false && flag === true) {
      this.dimensionDetailData[this.selectedValue].dimensionQuestionVos[
        this.questionsIndex
      ].surveyQuestions.map((item) => {
        if (item.isForceSelect !== true) {
          item.isDeleted = true;
        }
      });
    }
    this.dimensionDetailData[this.selectedValue].dimensionQuestionVos[
      this.questionsIndex
    ].surveyQuestions.map((item, index) => {
      if (item.isForceSelect !== true) {
        if (item.isDeleted === false) {
          let queIndex = this.chooseQuestionsId.findIndex((itm) => {
            return itm === item.id;
          });
          if (queIndex === -1) {
            this.chooseQuestionsId.push(item.id);
          }
        }
      }
    });
    this.dimensionCount();
  }
  // 单选
  checkOne(e, index) {
    this.dimensionDetailData[this.selectedValue].dimensionQuestionVos[
      this.questionsIndex
    ].surveyQuestions[index].isDeleted = !e;
    let question = this.dimensionDetailData[this.selectedValue]
      .dimensionQuestionVos[this.questionsIndex].surveyQuestions[index];
    this.checkStatus();
    let quesIndex = this.chooseQuestionsId.findIndex((itm) => {
      return itm === question.id;
    });
    if (!e === false && quesIndex === -1)
      this.chooseQuestionsId.push(question.id);
    if (!e === true && quesIndex !== -1)
      this.chooseQuestionsId.splice(quesIndex, 1);
    this.dimensionCount();
  }

  getSelectedQuestions() {
    let params: any[] = [];
    for (let index = 0; index < this.dimensionDetailData.length; index++) {
      const ele = this.dimensionDetailData[index].dimensionQuestionVos;
      for (let i = 0; i < ele.length; i++) {
        const element = ele[i].surveyQuestions;
        element.map((itm) => {
          if (itm.isDeleted === false) {
            params.push(itm.id);
          }
        });
      }
    }
    return params;
  }

  dimensionCount() {
    let dims: number = 0;
    this.dimensionDetailData.forEach((element) => {
      const ele = element.dimensionQuestionVos;
      ele.forEach((item) => {
        const ques = item.surveyQuestions;
        let flag = _.find(item.surveyQuestions, { isDeleted: false });
        if (flag) dims++;
      });
    });
    this.dimensionNum = dims;
  }

  chooseOpenQuestion(i, type) {
    this.openQuestionIndex = i;
    if (type) {
      this.showOption = false;
      this.openQuestionNumClass = i;
      this.openQuestionIndexClass = null;
    } else {
      this.showOption = true;
      this.openQuestionNumClass = null;
      this.openQuestionIndexClass = i;
    }
  }

  ok() {
    if (this.createOpenQuestion.length > 0) {
      // return this.msg.warning("请先保存创建的题本");
      return this.customMsg.open("warning", "请先保存创建的题本");
    }
    this.isLoading = true;
    let selList: any[] = this.getSelectedQuestions();
    if (selList.length === 0) {
      // this.msg.error(`没有选择任何题目`);
      this.customMsg.open("error", "没有选择任何题目");
      return;
    }
    this.modalRef.triggerOk();
  }

  search() {
    if (this.keyWord === "") {
      this.questionsList = this.dimensionList.dimensionQuestionVos[
        this.questionsIndex
      ].surveyQuestions;
    } else {
      let that = this;
      this.questionsList = _.filter(this.questionsList, function(o) {
        let nameCn: string = o.name.zh_CN;
        let nameEn: string = o.name.en_US;
        return (
          nameCn.indexOf(that.keyWord) >= 0 || nameEn.indexOf(that.keyWord) >= 0
        );
      });
    }
  }

  addOpenSubject(type) {
    // type: 0 新建开放题 1 多选开放题\
    this.showOption = false;
    this.openQuestionNumClass = 0;
    this.openQuestionIndexClass = null;
    let dom = document.getElementsByClassName("questions_scroll_box");
    dom[0].scrollTop = 0;
    if (this.createOpenQuestion.length !== 0) {
      // return this.msg.warning("请先保存当前问题");
      return this.customMsg.open("warning", "请先保存当前问题");
    }
    this.createOpenQuestion = [];
    if (type === 0) {
      this.createOpenQuestion.push(
        JSON.parse(JSON.stringify(this.openQuestion))
      );
    } else {
      this.createOpenQuestion.push(
        JSON.parse(JSON.stringify(this.manyOpenQuertion))
      );
    }
  }

  changeStatus(e) {
    if (this.createOpenQuestion[0].type === "MULTIPLE_CHOICE_ESSAY_QUESTION") {
      this.createOpenQuestion[0].options.multiSelectMin = 1;
    }
  }

  formatterNum(e, type) {
    let flag = Number.isInteger(e);
    let num = null;
    if (!flag) {
      if (e >= 1) {
        num = Math.floor(e);
      } else {
        num = 0;
      }
    } else {
      num = e;
    }
    if (type == 0) {
      this.createOpenQuestion[0].options.multiSelectMin = num;
    } else {
      this.createOpenQuestion[0].options.multiSelectMax = num;
    }
  }

  save() {
    this.createOpenQuestion[0].dimensionCode = this.dimensionList.dimensionQuestionVos[
      this.questionsIndex
    ].dimensionCode;
    this.createOpenQuestion[0].questionnaireId = this.questionnaireId;
    if (this.createOpenQuestion[0].options) {
      this.createOpenQuestion[0].options.options.forEach((item, index) => {
        item.id = index + 1;
      });
    }
    this.surveySerivce
      .addOpenQuestion(this.createOpenQuestion[0])
      .subscribe((res) => {
        if (res.result.code === 0) {
          // this.createOpenQuestion[0].id = res.data.id || res.data;
          this.dimensionList.dimensionQuestionVos[
            this.questionsIndex
          ].surveyQuestions.unshift(res.data);
          this.createOpenQuestion = [];
        }
      });
  }

  delete() {
    // 删除未保存的新增题目
    this.createOpenQuestion = [];
  }

  deleteOption(idx) {
    // 删除未保存的新增题目
    if (this.createOpenQuestion[0].options.options.length <= 1) {
      // return this.msg.warning("多选式开放题最少有一个选项");
      return this.customMsg.open("warning", "多选式开放题最少有一个选项");
    }
    this.createOpenQuestion[0].options.options.splice(idx, 1);
  }

  addOptions() {
    if (this.createOpenQuestion[0].options.options.length >= 10) {
      // return this.msg.error("选项最多为10个");
      this.customMsg.open("error", "选项最多为10个");
      return;
    } else {
      // let id = this.recursionId(1)

      // this.addOptionObj.id = id
      this.createOpenQuestion[0].options.options.push(
        JSON.parse(JSON.stringify(this.addOptionObj))
      );
      // addOptionObj
    }
  }

  recursionId(id) {
    let idx = this.createOpenQuestion[0].options.options.findIndex(
      (item) => item.id === id
    );
    if (idx !== -1) {
      id = id + 1;
      this.recursionId(id);
    } else {
      return id;
    }
  }
}
