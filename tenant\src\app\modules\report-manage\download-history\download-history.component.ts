import { HttpClient } from "@angular/common/http";
import { Component, Inject, OnInit } from "@angular/core";
import { DA_SERVICE_TOKEN, ITokenService } from "@knz/auth";
import { NzMessageService, UploadXHRArgs } from "ng-zorro-antd";
import { Observable } from "rxjs";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-download-history",
  templateUrl: "./download-history.component.html",
  styleUrls: ["./download-history.component.less"],
})
export class DownloadHistoryComponent implements OnInit {
  reportStyleMap = {
    _360_DEGREES_GROUP: "360团队报告",
    PTA_STANDARD_GROUP: "PTA团队报告",
    EMPTY: "手出报告",
  };
  tenantUrl: string = "/tenant-api";

  dataSet = [];

  searchName: string = "";

  currentGroupId: string;

  currentGroupData: any;

  permission: boolean;

  // 分页控制
  totalCount: number = 1;
  currentPage: number = 1;
  pageSize: number = 10;

  constructor(
    private http: HttpClient,
    private msgServ: NzMessageService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private customMsg: MessageService,
        public permissionService: PermissionService,
  ) {}

  token: string;

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    this.token = this.tokenService.get().token;
    this.loadData();
  }

  loadData() {
    this.dataSet = [];

    this.getGroupReportList().subscribe((res) => {
      if (res.result.code === 0) {
        this.totalCount = res.page.total;
        this.dataSet = res.data;
      }
    });
  }

  search() {
    this.currentPage = 1;
    this.loadData();
  }

  startDownlod(fileId: string, lineData: any) {
    const api = `${this.tenantUrl}/survey/standard/file/download/${fileId}`;
    lineData.isDownloading = true;
    this.http.get(api, { responseType: "blob", observe: "response" }).subscribe(
      (data) => {
        lineData.isDownloading = false;
        this.downFile(data);
      },
      (error1) => {
        lineData.isDownloading = false;
      }
    );
  }

  getGroupReportList(): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/getGroupReportsByPage`;
    let param = {
      name: this.searchName,
      page: {
        current: this.currentPage,
        size: this.pageSize,
      },
    };
    return this.http.post(api, param);
  }

  downFile(data) {
    this.showBlobErrorMessage(data);
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/pdf" });
    let content_desposition: string = data.headers.get("content-disposition");
    let name = content_desposition.split(";")[1];

    let fileName: string;
    let fileNameUnicode: string;
    let type: number = 0;

    if (name.indexOf("fileName=") >= 0) {
      fileName = name.replace("fileName=", "");
      type = 1;
    } else if (name.indexOf("fileName*=") >= 0) {
      fileNameUnicode = name.replace("fileName*=", "");
      type = 2;
    }

    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (type === 1) {
      fileName = decodeURIComponent(fileName);
    } else if (type === 2) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    } else {
      fileName = new Date().toLocaleDateString();
    }

    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  showBlobErrorMessage(data: any) {
    let body = data.body;
    if (body.type === "application/json") {
      let that = this;
      const reader = new FileReader();
      reader.readAsText(body, "utf-8");
      reader.onload = () => {
        // 处理报错信息
        // JSON.parse(reader.result) 拿到报错信息
        let resp: any = JSON.parse(reader.result + "");
        let code: number = resp.result.code;
        let errMsg: string = resp.result.message;

        if (code !== 0) {
          // that.msgServ.error(`${errMsg}，请联系管理员。`);
          this.customMsg.open("error", `${errMsg}，请联系管理员。`);
        }
      };
    }
  }

  tdClick(gId: string, data) {
    this.currentGroupId = gId;
    this.currentGroupData = data;
  }

  redoPdf(gId: string, data) {
    this.currentGroupId = gId;
    this.currentGroupData = data;

    data.isCreating = true;
    const api = `${this.tenantUrl}/sagittarius/report/content/createGroupReport?groupId=${gId}`;
    this.http.get(api).subscribe(
      (res: any) => {
        data.isCreating = false;
        if (res.result.code === 0) {
          this.msgServ.success("重新生成已提交");
          data.fileId = "";
          data.url = "";
          data.reportStyle === "EMPTY";
          //重新生成提交后 不可立即查看跟下载，直接显示生成中
        }
      },
      (error1) => {
        data.isCreating = false;
      }
    );
  }

  /**
   * customReq上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = ".pdf";

    formData.append("file", item.file as any);
    formData.append("isPublic", "false");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.uploadPdf(formData);
  };

  /**
   * uploadPdf 上传pdf报告文件
   */
  uploadPdf(formData) {
    this.currentGroupData.isUploading = true;
    const url = `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`;
    return this.http.post(url, formData).subscribe(
      (res: any) => {
        if (res.result.code === 0) {
          const retData = res.data;

          this.updateGroupFile(retData.id);
        }
      },
      (err) => {
        this.currentGroupData.isUploading = false;
      }
    );
  }

  /**
   * 更新group的fileId
   */
  updateGroupFile(fileId: string) {
    const url = `${this.tenantUrl}/sagittarius/report/content/updateGroupReportFile?groupId=${this.currentGroupId}&fileId=${fileId}`;
    return this.http.post(url, {}).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.currentGroupData.isUploading = false;
        this.currentGroupData.fileId = fileId;
        this.currentGroupData.status = "2";
        this.currentGroupData.url = "";
        this.msgServ.success("上传成功");
      }
    });
  }
}
