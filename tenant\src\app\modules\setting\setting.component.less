@text-color: #17314c;

.setting {
  width: 100%;
  height: calc(100vh - 134px);
  overflow-y: auto;
  // background: rgb(245, 246, 250);
  padding-top: 0.1px;
}

.bg-color {
  padding: 20px 20px 0;
  background: #f9fafc;
  margin-bottom: 10px;
}

.index-title {
  font-size: 24px;
  font-family: PingFangSC-Thin, PingFang SC;
  font-weight: 500;
  color: @text-color;
  line-height: 33px;
  margin: 20px 0;
}

.act-tit-box {
  align-items: center;
  margin-bottom: 20px;
}

.page-footer {
  padding-bottom: 13px;
  text-align: right;
}

.act-tit {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  // font-weight: 500;
  font-weight: bold;
  color: #17314c;
  line-height: 20px;
  // margin-bottom: 20px;
  margin-right: 29px;
  &:before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 15px;
    background: #409eff;
    margin-right: 10px;
  }
}

.set_con {
  // flex-wrap: wrap;
  .title {
    font-size: 14px;
    line-height: 20px;
    color: @text-color;
    margin-bottom: 19px;
  }
}

.not-allowed {
  cursor: not-allowed;

  &:hover {
    cursor: not-allowed;
  }
}

.input_item {
  width: 100%;
  height: 48px;
  border-radius: 4px;
  border: 1px solid rgba(230, 230, 230, 1);
  font-size: 16px;
}

.psd_text {
  color: #409eff;
  cursor: pointer;
}

.input_width {
  width: 100%;
}

.input_div {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.25);
  padding-left: 8px;
}

.peroid {
  margin-top: 20px;
  margin-bottom: 10px;
  padding: 10px;
  background-color: white;
  width: 285px;

  .head {
    height: 50px;
    position: relative;

    .remain {
      top: -20px;
      width: 56px;
      height: 32px;
      position: absolute;
      padding-right: 5px;
      padding-top: 3px;
      text-align: center;
      background-image: url("../../../assets/images/org/remain_tip.png");

      span {
        // width: 45px;
        height: 26px;
        font-size: 19px;
        font-family: YouSheBiaoTiHei;
        color: #ffffff;
        line-height: 26px;
      }
    }

    .tip1 {
      position: absolute;
      top: 15px;
      color: #aaa;
    }

    .tip2 {
      position: absolute;
      color: #aaa;
      // left: 100px;
      right: 0px;
    }
  }
}

.tipText {
  width: 38px;
  height: 26px;
  position: absolute;
  background-image: url("../../../assets/images/org/progress_tip.png");
  text-align: center;

  span {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 22px;
  }
}

footer {
  width: 100%;
  height: 70px;
  line-height: 70px;
  background: #ffffff;
  position: fixed;
  bottom: 0;
  text-align: right;
  background: rgb(245, 246, 250);
  ::ng-deep {
    .ant-btn-primary {
      border: none;
      padding: 1px 41px;
      background: linear-gradient(
        90deg,
        rgba(38, 208, 241, 1) 0%,
        rgba(64, 158, 255, 1) 100%
      );
      box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.4);
      font-size: 16px;
      &:hover {
        box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.8);
      }
    }
    .ant-btn-default {
      padding: 0 40px;
      color: #aaa;
      font-size: 16px;
      &:hover {
        color: #409eff;
      }
    }
  }
}

.line-32 {
  min-height: 33px;
  line-height: 33px;
}

.tableForm {
  ::ng-deep {
    .ant-form-item-control {
      line-height: 33px;
    }
    .ant-form-item {
      margin-bottom: 0;
    }
    // .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 8px 16px;
    }
  }
}
