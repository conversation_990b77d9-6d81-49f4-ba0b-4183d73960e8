<div class="header" [ngClass]="{'add':!background}">
    <div class="content client-width">
        <a routerLink="/home" (click)="gotoHome()">
            <img src="assets/images/logo.png" class="logo" *ngIf="background" />
            <img src="assets/images/logo.png" class="logo" *ngIf="!background" />
        </a>
        <div class="new_add" *ngIf="showtitle">
            <div (click)="getchoose('0')" style="cursor: pointer;" routerLink="/tourist/home">首页</div>
            <div (click)="getchoose('1')" style="position: relative;cursor: pointer;"
                [ngClass]=" showindex == 1 && showdiv ?'bg_div':''">产品
                <img *ngIf="showindex == 1  && showdiv" src="assets/images/load_up.png" />
                <img *ngIf=" showindex != 1 || !showdiv" src="assets/images/load_down.png" />
                <ul class="product" *ngIf="showindex == 1 && showdiv">
                    <li class="left_li" style="width: 180px;">
                        <div class="title_d">
                            <img src="assets/images/head_dy.png" alt=""><span>调研</span>
                        </div>
                        <div class="point_name" routerLink="/tourist/prisma">
                            <p></p> <span>5G敬业度调研</span>
                        </div>
                        <div class="point_name" routerLink="/tourist/s360">
                            <p></p> <span>360°行为反馈</span>
                        </div>
                        <div class="point_name" routerLink="/tourist/train">
                            <p></p> <span>360°培养反馈</span>
                        </div>
                        <div class="point_name" routerLink="/tourist/s270">
                            <p></p> <span>270°行为反馈</span>
                        </div>
                        <div class="point_name" routerLink="/tourist/prismaCulture">
                            <p></p> <span>企业文化调研</span>
                        </div>
                    </li>
                    <li class="right_li" style="flex: 1;">
                        <div class="title_d">
                            <img src="assets/images/head_cp.png" alt=""><span>通用测评</span>
                        </div>
                        <div style="display: flex;">
                            <ul class="ul_names">
                                <li class="point_name" routerLink="/tourist/epa">
                                    <p></p> <span>EPA职业性格测评</span>
                                </li>
                                <li class="point_name" routerLink="/tourist/ama">
                                    <p></p> <span>AMA工作成就动机测评</span>
                                </li>
                                
                                <li class="point_name" routerLink="/tourist/mhs">
                                    <p></p> <span>MHS心理健康筛查测评</span>
                                </li>
                                <li class="point_name" routerLink="/tourist/csi">
                                    <p></p> <span>CSI危机筛查测评</span>
                                </li>
                                <li class="point_name" routerLink="/tourist/mca">
                                    <p></p> <span>MCA“领航”测评</span>
                                </li>
                            </ul>
                            <ul class="ul_names">
                                <li class="point_name" routerLink="/tourist/pwvo">
                                    <p></p> <span>PWVO价值观取向测评</span>
                                </li>
                                <li class="point_name" routerLink="/tourist/at">
                                    <p></p> <span>AT认知能力测评</span>
                                </li>
                                <li class="point_name" routerLink="/tourist/ca">
                                    <p></p> <span>CA胜任力测评</span>
                                </li>
                                <li class="point_name" routerLink="/tourist/pca">
                                    <p></p> <span>PCA性格与职业测评</span>
                                </li>
                                <li class="point_name" routerLink="/tourist/pta">
                                    <p></p> <span>PTA性格类型测评</span>
                                </li>
                            </ul>
                            <ul class="ul_names">
                                <li class="point_name" routerLink="/tourist/tip">
                                    <p></p> <span>TIP人才画像测评</span>
                                </li>
                                <li class="point_name" routerLink="/tourist/sjtp">
                                    <p></p> <span>SJTP潜能情境测评</span>
                                </li>
                                <li class="point_name" routerLink="/tourist/pdp">
                                    <p></p> <span>PDP特质动态测评</span>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
            <div (click)="getchoose('2')" style="position: relative;cursor: pointer;"
            [ngClass]="showindex == 2 && showdiv2?'bg_div':''">解决方案
                <img *ngIf="showindex == 2 && showdiv2" src="assets/images/load_up.png" />
                <img *ngIf="showindex != 2 || !showdiv2" src="assets/images/load_down.png" />
                <ul class="product" style="justify-content: space-between;" *ngIf="showindex == 2 && showdiv2 ">
                    <li class="left_li" style="flex: 1;">
                        <div class="title_d">
                            <img src="assets/images/head_dy.png" alt=""><span>人才招聘</span>
                        </div>
                        <div class="point_name" routerLink="/tourist/xiaozhao">
                            <p></p> <span>校招1+1</span>
                        </div>
                        <div class="point_name" routerLink="/tourist/shezhao">
                            <p></p> <span>社招1x</span>
                        </div>
                        <div class="point_name" routerLink="/tourist/tipnew">
                            <p></p> <span>岗位绩优人群画像构建</span>
                        </div>
                    </li>
                    <li class="right_li" style="flex: 1;">
                        <div class="title_d"  >
                            <img src="assets/images/head_cp.png" alt=""><span>人才盘点</span>
                        </div>
                        <ul class="ul_names" routerLink="/tourist/rencai">
                            <li class="point_name">
                                <p></p> <span>盘点101</span>
                            </li>
                            <li class="point_name" routerLink="/tourist/canew">
                                <p></p> <span>挖掘高潜人才胜任力</span>
                            </li>

                        </ul>
                    </li>
                    <li class="right_li" style="flex: 1;">
                        <div class="title_d">
                            <img src="assets/images/head_cp.png" alt=""><span>人才培养</span>
                        </div>
                        <ul class="ul_names" routerLink="/tourist/culture">
                            <li class="point_name">
                                <p></p> <span>培养1v1</span>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
        <div *ngIf="showtitle">
            <a routerLink="/user/login" *ngIf="!background || tourist"><span class="login"
                    [ngClass]="{'add':tourist}">登录</span></a>
            <a routerLink="/user/register" *ngIf="!background || tourist">
                <div class="register" [ngClass]="{'add':tourist}">注册</div>
            </a>
            <div *ngIf="background && !tourist">
                <i class="user"></i>
                <a nz-dropdown [nzDropdownMenu]="menu">
                    {{user}}
                    <i nz-icon nzType="down"></i>
                </a>
                <nz-dropdown-menu #menu="nzDropdownMenu">
                    <ul nz-menu nzSelectable>
                        <li nz-menu-item><a routerLink="/setting">个人中心</a></li>
                        <!-- <li nz-menu-item><a routerLink="/setting/update" [queryParams]="{type:'password'}">修改密码 </a>
                        </li> -->
                        <li nz-menu-item (click)="logOut()">退出登录</li>
                    </ul>
                </nz-dropdown-menu>
            </div>
            <!--<div>注册</div>-->
        </div>
        <div *ngIf="!showtitle">
            <div *ngIf="background && !tourist">
                <i class="user"></i>
                <a nz-dropdown [nzDropdownMenu]="menu">
                    {{user}}
                    <i nz-icon nzType="down"></i>
                </a>
                <nz-dropdown-menu #menu="nzDropdownMenu">
                    <ul nz-menu nzSelectable>
                        <li nz-menu-item *knxFunctionPermission="'SAG:TENANT:USER_CENTER'" ><a routerLink="/setting">个人中心</a></li>
                        <li nz-menu-item *knxFunctionPermission="'SAG:TENANT:DOC_MGT'"><a routerLink="/setting/document" >共享中心</a></li>
                        <li nz-menu-item *knxFunctionPermission="'SAG:TENANT:MSG_SEND_LOG'"><a routerLink="/setting/sendmsg" >发送日志</a></li>
                        <li nz-menu-item (click)="logOut()">退出登录</li>
                    </ul>
                </nz-dropdown-menu>
            </div>
        </div>
    </div>
</div>
