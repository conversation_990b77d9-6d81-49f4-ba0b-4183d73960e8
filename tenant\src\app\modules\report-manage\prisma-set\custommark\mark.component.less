.con {
  position: relative;

  .news_ul {
    display: flex;
    justify-content: space-between;
    min-height: 70px;
    .name {
      height: 70px;
      flex: 1;

      .input {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;

        .select {
          flex: 1;

          &:nth-child(2) {
            margin: 0 10px;
          }
          &:last-child {
            margin: 0 0 0 10px;
          }
        }
      }

      .new_title {
        display: flex;
        justify-content: space-between;

        // .second_lab {
        //     color: #409EFF;
        //     cursor: pointer;
        //     position: relative;

        //     .tipscard {
        //         position: absolute !important;
        //         top: 30px;
        //         right: -200px;
        //         width: 650px;
        //         z-index: 999;
        //         background-color: #FFFFFF;
        //         box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1000);
        //         padding: 30px;

        //         .list_ul {
        //             li {
        //                 display: flex;
        //                 justify-content: space-between;
        //                 align-items: center;
        //             }
        //         }

        //         .table_ul {

        //             li {
        //                 display: flex;

        //                 div {
        //                     &:nth-child(1) {
        //                         flex: 2;
        //                     }

        //                     &:nth-child(2) {
        //                         flex: 4;
        //                         padding: 0 8px;
        //                     }

        //                     &:nth-child(3) {
        //                         flex: 1;
        //                         display: flex;
        //                         align-items: center;
        //                         justify-content: center;
        //                     }
        //                 }

        //                 &:nth-child(1) {}

        //             }
        //             .line_div{
        //                 height: 300px;
        //                 overflow-y: auto;
        //             }
        //             .line_list {
        //                 padding: 10px 0;
        //                 div {
        //                     &:nth-child(1) {
        //                         display: flex;
        //                         align-items: center;
        //                     }

        //                     &:nth-child(2) {}

        //                     &:nth-child(3) {}
        //                 }
        //             }
        //         }
        //     }
        // }
        .second_lab {
          color: #409eff;
          cursor: pointer;
          position: relative;
          z-index: 99;

          .tipscard {
            width: 582px;
            height: 474px;
            position: absolute !important;
            top: 25px;
            right: -10px;
            z-index: 99;
            background-color: #ffffff;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
            padding: 20px;

            .card-title {
              text-align: left;
              font-size: 16px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #495970;
              line-height: 24px;
              border-bottom: 1px solid #ececec;
              padding-bottom: 16px;
              margin-bottom: 10px;
            }

            .list_ul {
              display: flex;
              align-items: center;
              margin-bottom: 20px;

              li {
                display: flex;
                justify-content: space-between;
                align-items: center;
              }
            }

            .table_ul {
              li {
                display: flex;
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #17314c !important;
                line-height: 20px;

                div {
                  text-align: left;

                  &:nth-child(1) {
                    flex: 1;
                  }

                  &:nth-child(2) {
                    flex: 4;
                    padding: 0 12px;
                  }
                }
              }

              .line_div {
                height: 240px;
                overflow-y: auto;
              }

              .line_list {
                padding: 10px 0;

                div {
                  &:nth-child(1) {
                    display: flex;
                    align-items: center;
                  }
                }
              }

              .li_footer {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                margin-top: 20px;
                padding: 20px 0;
                border-top: 1px solid #ececec;

                .btn_cancel {
                  width: 69px;
                  height: 30px;
                  background: #ffffff;
                  border-radius: 15px;
                  color: #409eff;
                  border: 1px solid #409eff;
                }

                .btn_confirm {
                  width: 69px;
                  height: 30px;
                  background: #409eff;
                  border-radius: 15px;
                  color: #ffffff;
                  border: 1px solid #409eff;
                }
              }
            }
          }
        }
      }
    }

    .name_2 {
      height: 70px;
      flex: 1;
      margin-left: 30px;

      .input {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        .select {
          flex: 1;
          height: 70px !important;
          overflow-y: auto !important;
        }
      }
    }
  }

  .title {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-top: 40px;
  }

  .vi-tab {
    height: 320px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    display: flex;
    ul {
      height: 100%;
      border-right: 1px solid #e6e6e6;
      li {
        cursor: pointer;
        margin-top: 21px;
        text-align: center;
        line-height: 48px;
        box-sizing: border-box;
        width: 148px;
        height: 48px;
        border-left: 2px solid transparent;
      }
      li.active {
        background: rgba(64, 160, 255, 0.05);
        border-left: 2px solid #409eff;
      }
    }
    .vi-tab-content {
      flex: 1;
      .vi-tab-content_top {
        padding-left: 15px;
        padding-right: 9px;
        height: 53px;
        border-bottom: 1px solid #e6e6e6;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .vi-tab-content_title {
          display: flex;
          align-items: center;
        }
      }
      .vi-tab-content_bottom {
        padding-left: 15px;
        max-height: 266px;
        overflow-y: auto;
      }
    }
  }

  .label {
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #17314c;
    line-height: 20px;
  }

  .content {
    width: 100%;
    height: 450px;
    display: flex;
    justify-content: flex-start;

    border-radius: 4px;
    border: 1px solid #e6e6e6;

    .list {
      width: 255px;
      min-height: 400px;
      border-right: 1px solid #e6e6e6;

      .listItem {
        height: 380px;
      }
    }
  }

  .action {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    border-bottom: 1px solid #e6e6e6;

    .button1 {
      border-radius: 15px;
      background-color: white;

      span {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #409eff;
        line-height: 20px;
      }
    }

    .button2 {
      background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
      box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
      border-radius: 15px;
      border: none;

      span {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: white;
        line-height: 20px;
      }
    }

    .button3 {
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 15px;
      border: none;

      span {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: white;
        line-height: 20px;
      }
    }
  }

  .group {
    width: 700px;
    height: 610px;
    background: #e8e8e8;
    box-shadow: 0px 0px 10px 4px rgba(0, 0, 0, 0.04);
    padding: 20px;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 100;

    .gHeader {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;

      .gTitle {
        height: 25px;
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #17314c;
        line-height: 25px;
      }
    }

    .gContent {
      height: 495px;
    }

    .gAction {
      margin-top: 10px;
      text-align: center;
    }
  }
}

.treeScroll {
  overflow-y: auto;
  overflow-x: auto;
}

.takeRemain {
  flex: 1;
}

.background-color {
  background-color: rgba(142, 255, 142, 0.3);
}

:host .con ::ng-deep {
  nz-tag {
    margin-top: 10px;
  }

  .ant-collapse-content-box {
    padding-top: 5px;
  }

  .ant-collapse {
    min-width: 290px;
  }

  .ant-checkbox-wrapper {
    display: block;
    margin-left: 0;
  }

  .ant-collapse-extra {
    &:hover {
      color: sandybrown;
    }
  }
}

::ng-deep {
  .ant-modal-close-x {
    width: 36px !important;
    height: 36px !important;
  }
}

.header_tit {
  display: inline-block;
  width: 250px !important;
  overflow: hidden;
  text-overflow: ellipsis; //文本溢出显示省略号
  white-space: nowrap; //文本不会换行
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
// 滑块背景
::-webkit-scrollbar-track {
  // background-color: transparent;
  background-color: #f1f1f1;
  box-shadow: none;
}
// 滑块
::-webkit-scrollbar-thumb {
  // background-color: #e9e9e9;
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}
