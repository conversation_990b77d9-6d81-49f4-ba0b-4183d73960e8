<div class="container">
  <!-- <div class="header">
        <div class="text">
            填答进度
        </div>
        
    </div> -->
  <div class="subject">
    <div class="text">
      <!-- 批量导出  -->
      <span class="message"
        >已选择 {{ this.conditions.length }} 个条件筛选</span
      >
    </div>
  </div>
  <div class="condition_main">
    <span>满足下列</span>
    <nz-select
      [(ngModel)]="params.match"
      nzAllowClear
      nzPlaceHolder="请选择"
      style="width: 100px; margin: 0 5px 0 5px;"
    >
      <nz-option nzValue="MATCH_ALL" nzLabel="所有"></nz-option>
    </nz-select>
    <span>条件，按</span>
    <nz-select
      [(ngModel)]="params.type"
      nzAllowClear
      nzPlaceHolder="请选择"
      style="width: 100px; margin: 0 5px 0 5px;"
    >
      <nz-option nzValue="ORGANIZATION" nzLabel="部门"></nz-option>
      <nz-option nzValue="DEMOGRAPHIC" nzLabel="人口学"></nz-option>
    </nz-select>
    <span>为主体</span>
    <nz-select
      [(ngModel)]="params.actionType"
      nzAllowClear
      nzPlaceHolder="请选择"
      style="width: 100px; margin: 0 5px 0 5px;"
    >
      <nz-option nzValue="MERGE" nzLabel="合并"></nz-option>
      <nz-option nzValue="SPLIT" nzLabel="分批"></nz-option>
    </nz-select>
    <span> 导出</span>
  </div>
  <div class="condition">
    <div class="add_button" (click)="addCondition()">
      <i class="iconfont icon-plus-circle"></i> 添加条件
    </div>
    <div
      class="condition_detail"
      *ngFor="let item of conditions; let i = index"
    >
      <nz-select
        [(ngModel)]="item.type"
        nzAllowClear
        nzPlaceHolder="请选择"
        style="width: 150px; margin-left: 5px;"
        (ngModelChange)="subjectTypeChange($event, i)"
      >
        <nz-option nzValue="ORGANIZATION" nzLabel="按部门级别"></nz-option>
        <nz-option nzValue="DEMOGRAPHIC" nzLabel="按人口标签"></nz-option>
      </nz-select>
      <nz-select
        [(ngModel)]="item.scope"
        nzPlaceHolder="请选择"
        style="width: 150px; margin-right: 5px;margin-left: 5px;"
        (ngModelChange)="scopeChange($event, i)"
      >
        <nz-option
          *ngFor="let p of item.levelTypeList"
          [nzValue]="p.id"
          [nzLabel]="p.name?.zh_CN"
        ></nz-option>
      </nz-select>

      <nz-select
        [(ngModel)]="item.labelIds"
        [nzSize]="size"
        nzMode="multiple"
        nzPlaceHolder="请选择"
        (ngModelChange)="labelChange($event, i)"
        [nzMaxTagCount]="0"
        [nzMaxTagPlaceholder]="tagPlaceHolder"
        style="width: 150px;"
      >
        <nz-option
          *ngFor="let option of item.labelList"
          [nzLabel]="option.name?.zh_CN"
          [nzValue]="option.id"
          style=" margin-right: 5px;"
        ></nz-option>
      </nz-select>
      <ng-template #tagPlaceHolder let-selectedList>
        已选中{{ item.labelIds.length + (item.isSelectAll ? -1 : 0) }}个
      </ng-template>
      <i
        class="iconfont icon-icon_delete"
        style=" padding-left: 5px;"
        (click)="deleteCodition(i)"
      ></i>
    </div>
  </div>
  <!-- <div class="foot_btn">
        <footer>
          </footer> 
    </div> -->
  <div class="drawer-footer">
    <button (click)="clearChange()" class="default" nz-button>
      清空
    </button>
    <button
      (click)="exportChange()"
      nzType="primary"
      class="confirm"
      [nzLoading]="isLoading"
      nz-button
    >
      保存
    </button>
  </div>
</div>
