.container {
  display: flex;
  justify-content: flex-start;
  align-items: stretch;
  // margin-right: 24px;
  .left {
    width: 280px;
    border-right: #e6e6e6 solid 1px;
    display: flex;
    flex-direction: column;
    // padding-right: 20px;

    .nair {
      width: 100%;
      height: 120px;
      // border-radius: 5px;
      padding: 20px 10px;
      border-bottom: #e6e6e6 solid 1px;
      border-left: 4px solid #fff;

      .line1 {
        display: flex;
        justify-content: space-between;
        padding-right: 10px;
        .text0 {
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          font-size: 14px;
          color: #495970;
        }
        margin-bottom: 10px;
      }

      .line2 {
        .text1 {
          border-radius: 4px;
          font-weight: 500;
          padding: 2px 5px;
          margin-right: 5px;
        }
        .text2 {
          padding: 2px 6px;
          font-size: 13px;
          font-weight: 500;
          color: #419eff;
          line-height: 17px;
          height: 21px;
          border-radius: 4px;
          border: 1px solid #419eff;
        }
        margin-bottom: 10px;
      }

      .line3 {
        .text3 {
          font-size: 14px;
          font-weight: 500;
          color: #aaaaaa;
          line-height: 20px;
        }
      }
    }
  }

  .right {
    width: 720px;
    .file-name {
      margin: 0 16px 16px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .title {
      margin: 16px 16px 8px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #495970;
        line-height: 20px;
      }

      .input {
        .search {
          width: 310px;
        }
      }
    }

    .table {
      margin-left: 16px;
      max-height: calc(100vh - 232px);
    }
  }
}

.linesplit {
  width: 100%;
  height: 10px;
  background: -webkit-linear-gradient(top, #fff, #ddd);
  // background: -webkit-linear-gradient(top, #fff, #0000ff);
}

.bottom {
  margin-top: 20px;
  padding: 0 20px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .pre {
    button {
      width: 128px;
      height: 38px;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      text-align: center;

      span {
        width: 32px;
        height: 22px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        line-height: 22px;
      }
    }

    label {
      margin-left: 50px;
    }

    span {
      margin-left: 10px;
    }
  }

  .after {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .line1 {
      height: 17px;
      font-size: 13px;
      font-weight: 500;
      color: #495970;
      line-height: 17px;

      .total {
        margin-right: 10px;
        height: 28px;
        font-size: 20px;
        font-weight: 400;
        color: #e1251b;
        line-height: 28px;
      }
    }

    .line2 {
      height: 17px;
      font-size: 13px;
      font-weight: 500;
      color: #17314c;
      line-height: 17px;
      margin-top: 15px;
    }
  }
}

.currentNair {
  // background-color: #F0F0F0;
  background-color: #f4faff;
  border-left: 4px solid #409eff !important;
  // border:dashed #E0E0E0 1px !important;
  // background: -webkit-linear-gradient(top, #fff, #0000ff);
}

.autoTxt {
  font-weight: 500;
  color: #ffffff;
  background-color: #419eff;
}

.handTxt {
  font-weight: 500;
  color: #ffffff;
  background-color: #45bfd9;
}
.singlText {
  font-weight: 500;
  color: #ffffff;
  background-color: #a597ff;
}
.multiTxt {
  font-weight: 500;
  color: #ffffff;
  background-color: #efbc30;
}

.thead {
  background-color: #f3f7fb !important;
}

.th {
  width: 24px;
  height: 17px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 600;
  color: #aaaaaa;
  line-height: 17px;
  background-color: #f3f7fb !important;
}

.td {
  height: 20px;
  font-size: 15px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 500;
  color: #17314c;
  line-height: 20px;
}

.col1 {
  max-width: 130px;
}

.col2 {
  max-width: 260px;
}

.btnIcon {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: url("../../../../../assets/images/msg_deleted.png");
  background-size: 100% 100%;
  &:hover {
    background: url("../../../../../assets/images/delete-ico-hover.png");
    background-size: 100% 100%;
  }
  cursor: pointer;
}

.scroll {
  height: calc(100vh - 123px);
  .vxscrollbar();
}

//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

::ng-deep {
  .ant-modal-title {
    height: 33px;
    font-size: 24px;
    font-family: PingFangSC-Thin, PingFang SC;
    font-weight: 100;
    color: #17314c;
    line-height: 33px;
  }

  .ant-table-small {
    border: 0px;
  }
}

.addSudoku {
  background-color: #f4faff;
  border: 1px dashed #409eff;
  font-size: 14px;
  font-weight: 500;
  color: #419eff;
  padding: 6px 10px;
  border-radius: 2px;
  cursor: pointer;
  > span {
    font-size: 14px;
    font-weight: 500;
    color: #419eff !important;
    margin-right: 8px;
  }
}
.overflowBox {
  overflow-y: auto;
  max-height: calc(100vh - 232px);
  .multitool {
    height: 83px;
    background: #f9f9f9;
    padding: 10px;
    margin-left: 20px;
    margin-bottom: 10px;
    .warnInput {
      border: 1px solid #f84444;
    }
    &_btm {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .warnText {
        font-size: 12px;
        font-weight: 400;
        color: #f84444;
      }
      &_btns {
        display: flex;
        justify-content: space-between;
        align-items: center;
        span {
          font-size: 12px;
          font-weight: 500;
          color: #409eff;
          cursor: pointer;
        }
        .del {
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: url("../../../../../assets/images/msg_deleted.png");
          background-size: 100% 100%;
          &:hover {
            background: url("../../../../../assets/images/delete-ico-hover.png");
            background-size: 100% 100%;
          }
          cursor: pointer;
        }
      }
    }
  }
}
::ng-deep {
  .popoverCard {
    max-width: 100%;
    .ant-popover-inner-content {
      padding: 0;
    }
  }
}
.empty-box {
  height: calc(100vh - 232px);
  overflow-y: auto;
}
