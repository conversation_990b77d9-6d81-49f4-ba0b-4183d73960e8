import { Component, OnInit } from "@angular/core";

import { Router, ActivatedRoute } from "@angular/router";
import { Observable } from "rxjs";
import _ from "lodash";

import {
  NzDrawerService,
  NzMessageService,
  NzModalRef,
  NzModalService,
  NzNotificationService,
  UploadXHRArgs,
} from "ng-zorro-antd";
import { HttpClient, HttpEvent } from "@angular/common/http";
import { ProjectManageService } from "../../service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { SurveyInvestigatorPerson } from "../project-manage";
import { HttpResponseWrapper } from "../../../shared/interfaces/http-common";
import { DatePipe } from "@angular/common";
import { LoginService } from "@src/modules/login/login.service";

import { ProjectDelayComponent } from "../project-delay/project-delay.component";
import { ProjectReopenComponent } from "../project-reopen/project-reopen.component";
import { PersonListComponent } from "../person-list/person-list.component";
import { PrismaDrawerComponent } from "./prisma-drawer/prisma-drawer.component";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";

import { AdministratorModalComponent } from "./administrator-modal/administrator-modal.component";
import { SubAdministratorModalComponent } from "./sub-administrator-modal/sub-administrator-modal.component";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-project-manage-detail",
  templateUrl: "./project-manage-detail.component.html",
  styleUrls: ["./project-manage-detail.component.less"],
  providers: [DatePipe],
})
export class ProjectManageDetailComponent implements OnInit {
  // SurveyInvestigatorPerson.SurveyPeople.answerStatus 项目完成状态
  tenantUrl: string = "/tenant-api";

  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/project-manage/home",
      name: "活动管理",
      Highlight: false,
    },
    {
      path: "",
      name: "活动详情",
      Highlight: true,
    },
  ];
  public readonly allAnswerStatus = [
    { text: "未开始", value: "NOT_INVITED" },
    { text: "进行中", value: "ANSWERING" },
    { text: "已完成", value: "ANSWERED" },
  ];

  // SurveyInvestigatorPerson.SurveyPeople.status 邀请状态
  public readonly inviteStatus = {
    NEW: "未发送",
    SENDING: "发送中",
    SENT: "邀请成功",
    FAIL: "邀请失败",
  };

  status: any = {
    NOT_INVITED: "未开始",
    WAITING_ANSWER: "未开始",
    ANSWERING: "进行中",
    ANSWERED: "已完成",
  };
  projectData: any = {}; // 活动简介
  thName: string = "全部"; // thName
  questionnaireIds: any[] = [];
  isSpinning: boolean = false;
  tableIsSpinning: boolean = false;

  // 页面loading遮罩状态
  isloading: boolean = false;

  // table 数据
  // ANSWERING 进行中 ANSWERED 已完成
  answerStatus: any[] = [];
  // 按名称升序 FIRST_NAME_ASC 按名称降序 FIRST_NAME_DESC  按邀请时间升序 STARTTIME_ASC 按邀请时间降序 STARTTIME_DESC 按完成时间升序 ENDTIME_ASC 按完成时间降序 ENDTIME_DESC
  orderBy: string = "";
  page: any = {
    current: 1,
    pages: 0,
    searchCount: true,
    size: 5,
  };
  firstPage: any = {
    current: 1,
    pages: 0,
    searchCount: true,
    size: 5,
  };
  // 活动状态 样式
  statusClass: string = "";
  // 活动状态中文
  statusName: string = "";
  // 1：其它测评  2：360测评 3：调研
  transType: number = 1;
  is360Invite: boolean = false;
  isShowOrg = false;

  // 搜索相关
  // 人员名称
  searchField: string = "";
  // 主管理员
  listMainManager: any[] = [];
  // 子管理员
  listSubMainManager: any[] = [];

  // 管理权限
  permission: boolean = false;
  listData: any[] = [];
  // 普通测评
  tableList: any[] = [];
  // 360
  tableListOf360: SurveyInvestigatorPerson[] = [];
  // 调研
  tableListOfresearch: any[] = [];
  projectId: string = "";
  showmock = false;
  setp1 = false;
  setp2 = false;
  setp3 = false;

  mainManagerPermission: boolean = false;
  subManagerPermission: boolean = false;

  isDisplayMail = false;
  constructor(
    private datePipe: DatePipe,
    private api: ProjectManageService,
    private router: Router,
    private route: ActivatedRoute,
    private surveySerivce: SurveyApiService,
    private msg: NzMessageService,
    private http: HttpClient,
    private notification: NzNotificationService,
    private modalService: NzModalService,
    private drawerService: NzDrawerService,
    private loginServ: LoginService,
    public knxFunctionPermissionService: KnxFunctionPermissionService,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {}

  ngOnInit() {
    this.getIsDisplayMail();
    this.showmock = JSON.parse(sessionStorage.getItem("noviceGuidance"));
    if (this.showmock) {
      this.setp1 = true;
    }
    this.permission = this.permissionService.isPermission();

    this.route.queryParams.subscribe((res) => {
      this.projectId = res["id"];
    });
    this.loadManager();
    this.loadSubManager();
    this.getDetail();
    this.getCurrentUserProjectPermission();
  }

  /**
   * 获取主管理员，子管理员权限
   */
  getCurrentUserProjectPermission() {
    this.surveySerivce
      .getCurrentUserProjectPermission(this.projectId)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.mainManagerPermission = res.data.isMainManager;
          this.subManagerPermission = res.data.isSubManager;
        }
      });
  }

  /**
   * 一键删除
   */
  deleteAll() {
    this.surveySerivce.deleteAll(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("操作成功");
        this.getTableData();
      }
    });
  }
  /**
   * 清除人员
   */
  clearPersonAll() {
    this.surveySerivce.clearPersonAll(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("操作成功");
        this.getTableData();
      }
    });
  }

  /**
   * 详情权限  展示活动主管理员
   */
  loadManager() {
    this.surveySerivce.getListMainManager(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.listMainManager = res.data;
      }
    });
  }

  /**
   * 详情权限  展示活动子管理员
   */
  loadSubManager() {
    this.surveySerivce.getListSubManager(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.listSubMainManager = _.filter(res.data, function(o) {
          return o.isSubManager;
        });
      }
    });
  }

  /**
   * 管理权限-添加主管理员
   */
  administratorModal() {
    const drawer = this.drawerService.create({
      nzTitle: "添加主管理员",
      nzContent: AdministratorModalComponent,
      nzClosable: true,
      nzMaskClosable: false,
      nzContentParams: {
        projectId: this.projectId,
      },
      nzWidth: 740,
      nzWrapClassName: "round-right-drawer8",
      nzOnCancel: () =>
        new Promise((resolve) => {
          drawer.close();
        }),
    });
    // 侧边栏确定
    drawer.afterClose.subscribe((isOk) => {
      if (isOk) {
        this.loadManager();
        this.loadSubManager();
      }
    });
  }

  /**
   * 管理权限-添加子管理员
   */
  subAdministratorModal() {
    const drawer = this.drawerService.create({
      nzTitle: "添加子管理员",
      nzContent: SubAdministratorModalComponent,
      nzClosable: true,
      nzMaskClosable: false,
      nzContentParams: {
        projectId: this.projectId,
        standardReportType: this.projectData.standardReportType,
        // 1：其它测评  2：360测评 3：调研
        transType: this.transType,
      },
      nzWrapClassName: "round-right-drawer8-nobody",
      nzWidth: 1000,
      nzOnCancel: () =>
        new Promise((resolve) => {
          drawer.close();
        }),
    });
    // 侧边栏确定
    drawer.afterClose.subscribe((isOk) => {
      if (isOk) {
        this.loadManager();
        this.loadSubManager();
      }
    });
  }

  /**
   * 清除填答
   * @param personId
   * @param investigatorId
   */
  clearHistory(personId: string, investigatorId?: string) {
    let params: any = {
      personId: personId,
      projectId: this.projectId,
    };
    if (investigatorId) params.investigatorId = investigatorId;
    this.api.clearAnswerData(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("操作成功");
        this.getTableData();
      }
    });
  }

  /**
   * 评价关系
   */
  toEvl() {
    this.router.navigate(["project-manage/invite360"], {
      queryParams: {
        projectId: this.projectData.id,
        step: 1,
        type: "home-detail",
        standardReportType: this.projectData.standardReportType,
        isCustomRoleWeight: this.projectData.isCustomRoleWeight,
      },
    });
  }

  /**
   * 获取活动数据
   * @param refresh
   */
  async getDetail(refresh?: boolean) {
    // this.isSpinning = true
    this.api.getDetail(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.projectData = res.data;
        this.statusClass = this.projectData.status.toLowerCase();
        switch (this.projectData.status) {
          case "ANNOUNCED":
            this.statusName = "未开始";
            break;
          case "WAITING_ANSWER":
            this.statusName = "未开始";
            break;
          case "OVER":
            this.statusName = "已完成";
            break;
          case "ANSWERING":
            this.statusName = "进行中";
            break;
          case "SUSPEND":
            this.statusName = "已完成";
            break;
          case "PREVIEW":
            this.statusName = "预发布";
            break;

          default:
            break;
        }
        // this.isSpinning = false
        if (this.projectData.surveyType === "EMPLOYEE_ENGAGEMENT") {
          this.transType = 3;
          this.isShowOrg = this.projectData.questionnaires[0].isShowOrganization;
        } else if (this.projectData.is360Project) {
          this.transType = 2;
          this.is360Invite = this.projectData.isInviteAnswer;
        } else {
          this.transType = 1;
        }
        refresh ? this.getTableData(refresh) : this.getTableData();
      }
    });
  }

  /**
   * 邀请被评估人完善评价关系
   * @returns
   */
  inviteEval() {
    localStorage.setItem("backurl", this.router.routerState.snapshot.url);
    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    if (this.projectData.status == "ANNOUNCED") {
      // this.msg.error("活动还未发布，暂不能邀请测评者");
      this.customMsg.open("error", "活动还未发布，暂不能邀请测评者");
      return;
    }
    this.router.navigate(["project-manage/invite360-eval"], {
      queryParams: {
        projectId: this.projectId,
        step: 2,
        name: this.projectData.name,
        projectCode: this.projectData.code,
      },
    });
  }

  /**
   * 搜索
   */
  search() {
    this.page.current = 1;
    this.getTableData();
  }

  /**
   * 获取表格数据
   * @param refresh
   * @param sortName
   * @param sortValue
   */
  getTableData(refresh?: boolean, sortName?: string, sortValue?: string) {
    let sortTmp: string = undefined;
    if (sortName && sortValue) {
      let tmpValue = "ASC";
      if (sortValue === "descend") {
        tmpValue = "DESC";
      }
      sortTmp = sortName + "_" + tmpValue;
      this.orderBy = sortTmp.toUpperCase();
    }
    refresh ? (this.searchField = "") : "";
    if (this.transType === 1 || (this.transType === 3 && !this.isShowOrg)) {
      // 普通测评
      this.tableIsSpinning = true;
      let params = {
        answerStatus: refresh ? [] : this.answerStatus,
        orderBy: refresh ? "" : this.orderBy,
        searchField: this.searchField,
        page: refresh ? this.firstPage : this.page,
        projectId: this.projectId,
      };

      this.api.getPagedUserList(params).subscribe((res) => {
        if (res.result.code === 0) {
          this.page = res.page;
          if (+res.result.code !== 0) return;
          // update user list display
          this.tableList = res.data;
          this.tableList.forEach((item, index) => {
            item.startTime = this.transDate(item.startTime);
            item.endTime = this.transDate(item.endTime);
            item.seq = this.page.size * (this.page.current - 1) + index + 1;
          });
          this.tableIsSpinning = false;
        }
      });
    } else if (this.transType === 2) {
      // 360测评
      this.tableIsSpinning = true;
      this.api
        .get360ProjectPagedUserList(
          this.projectId,
          this.page.current,
          this.page.size,
          this.searchField,
          this.orderBy
        )
        .subscribe((res) => {
          if (res.result.code === 0) {
            this.page = res.page;
            this.tableListOf360 = this.addKeys(res.data);
            this.tableIsSpinning = false;
          }
        });
    } else if (this.transType === 3 && this.isShowOrg) {
      // 获取调研人员列表
      let param = {
        // organizationId: this.orgId,
        page: this.page,
        projectId: this.projectId,
        searchField: this.searchField,
      };
      this.tableIsSpinning = true;
      this.surveySerivce
        .listFirstLevelOrganizationByPage(param)
        .subscribe((res) => {
          if (res.result.code === 0) {
            this.tableListOfresearch = res.data;
            this.tableIsSpinning = false;
            this.page = res.page;
          }
        });
    }
  }

  /**
   * 对邀请成功的测评者再次发送邮件以提醒
   * @param projectId 活动ID
   * @param email 邮箱地址
   */
  public sendEmailForRemind(projectId: string, email: string, data): void {
    if (email) {
      this.http
        .get(
          `${this.tenantUrl}/survey/person/sendEmail/${email}/${projectId}/${data.id}`
        )
        .toPromise()
        .then((res: HttpResponseWrapper) => {
          if (res.result.code === 0) {
            this.msg.success("提醒成功");
          } else {
            this.notification.error("操作失败", `原因：${res.result.message}`);
          }
        })
        .catch((err) => {});
    } else {
      // this.msg.warning("请先配置邮箱！");
      this.customMsg.open("warning", "请先配置邮箱");
    }
  }

  /**
   * 时间处理
   * @param datestr
   * @returns
   */
  transDate(datestr: string): string {
    if (datestr == undefined || typeof datestr !== "string") {
      return "";
    }
    return datestr.replace("T", " ").substring(0, 19);
  }

  /**
   * 添加用于渲染数据的字段
   */
  private addKeys<T extends SurveyInvestigatorPerson>(data: T[]): T[] {
    data.forEach((value) => {
      let completed = 0;
      value.surveyPeople.forEach(
        (val) => val.answerStatus === "ANSWERED" && completed++
      );
      value.progress = completed + "/" + value.surveyPeople.length; // 当前项目完成进度
      value.expand = false; // nzTable：当前子 table 展开状态
    });
    return data;
  }

  /**
   * 活动截止
   */
  deadline() {
    this.modalService.confirm({
      nzTitle: "活动截止",
      nzContent: "活动截止后将无法作答，是否确定？",
      nzOkText: "确定",
      nzCancelText: "取消",
      nzOnOk: () => {
        const param = this.getParam("AS_OF", undefined, undefined);
        this.api.updateProjectStatus(param).subscribe((res) => {
          if (res.result.code == 0) {
            this.getDetail();
            // update user list display
            const tmp = this.datePipe.transform(new Date(), "yyyy-MM-dd HH:mm");
            this.updateSelectionModel("OVER", undefined, tmp);
          }
        });
      },

      nzOnCancel: () => console.log("Cancel"),
    });
  }

  /**
   * 活动延期
   */
  public delay() {
    const dateStartStr = this.datePipe.transform(
      this.projectData.startTime,
      "yyyy-MM-dd HH:mm"
    );
    const dateEndStr = this.datePipe.transform(
      this.projectData.endTime,
      "yyyy-MM-dd HH:mm"
    );

    const modal = this.modalService.create({
      nzTitle: "",
      nzContent: ProjectDelayComponent,
      nzComponentParams: {
        id: this.projectData.id,
        startDate: dateStartStr,
        endDate: dateEndStr,
      },
      nzMaskClosable: false,
      nzClosable: false,

      nzFooter: [
        {
          label: "取消",
          shape: "round",
          onClick: () => modal.destroy(),
        },
        {
          label: "确认",
          shape: "round",
          type: "primary",
          onClick: () => {
            const child: ProjectDelayComponent = modal.getContentComponent();
            const endDate = child.endDate;

            if (!endDate) {
              // this.msg.error("日期不能为空");
              this.customMsg.open("error", "日期不能为空");
              return;
            }

            if (
              new Date(this.projectData.startTime).getTime() >=
              new Date(endDate).getTime()
            ) {
              // this.msg.error("结束时间要大于开始时间");
              this.customMsg.open("error", "结束时间要大于开始时间");
              return;
            }
            const param = this.getParam(
              "DELAY",
              this.projectData.startTime,
              endDate
            );
            this.api.updateProjectStatus(param).subscribe((res) => {
              if (res.result.code == 0) {
                this.getDetail();
              }
            });
            modal.destroy();
          },
        },
      ],
    });
  }

  /**
   * 活动重新开启
   */
  public reopen() {
    const dateStartStr = this.datePipe.transform(
      this.projectData.startTime,
      "yyyy-MM-dd HH:mm"
    );
    const dateEndStr = this.datePipe.transform(
      this.projectData.endTime,
      "yyyy-MM-dd HH:mm"
    );

    const dates: string[] = [];
    dates.push(dateStartStr);
    dates.push(dateEndStr);

    const modal = this.modalService.create({
      nzTitle: "活动重新开启",
      nzContent: ProjectReopenComponent,
      nzComponentParams: {
        id: this.projectData.id,
        dates,
      },

      nzOkText: "保存",
      nzCancelText: "取消",

      nzMaskClosable: false,
      nzClosable: false,
      nzFooter: [
        {
          label: "取消",
          shape: "round",
          onClick: () => modal.destroy(),
        },
        {
          label: "确认",
          shape: "round",
          type: "primary",
          onClick: () => {
            const child: ProjectReopenComponent = modal.getContentComponent();
            const data: any[] = child.dates;
            if (!data[0]) {
              // this.msg.error("开始日期不能为空");
              this.customMsg.open("error", "开始日期不能为空");
              return;
            }
            if (!data[1]) {
              // this.msg.error("结束日期不能为空");
              this.customMsg.open("error", "结束日期不能为空");
              return;
            }

            if (new Date(data[0]).getTime() >= new Date(data[1]).getTime()) {
              // this.msg.error("结束日期要大于开始日期");
              this.customMsg.open("error", "结束日期要大于开始日期");
              return;
            }

            const param = this.getParam("REWRITE_OPEN", data[0], data[1]);
            this.api.updateProjectStatus(param).subscribe((res) => {
              if (res.result.code == 0) {
                this.getDetail();
              }
            });
            modal.destroy();
          },
        },
      ],
    });
  }

  /**
   * 获取截止相关参数
   * @param status
   * @param start
   * @param end
   * @returns
   */
  getParam(status: string, start: string, end: string): any {
    const param = {
      id: this.projectData.id,
      projectStatusChange: status,
      startTime: this.getUnixDateFormat(start),
      endTime: this.getUnixDateFormat(end),
    };
    if (!start) {
      delete param.startTime;
    }
    if (!end) {
      delete param.endTime;
    }
    return param;
  }

  /**
   * 活动截止-更新状态及时间
   * @param status
   * @param startDate
   * @param endDate
   */
  updateSelectionModel(status: string, startDate: string, endDate: string) {
    this.projectData.status = status;
    if (startDate) {
      this.projectData.startTime = startDate;
    }
    if (endDate) {
      this.projectData.endTime = endDate;
    }
  }

  /**
   * 时间处理
   * @param sourceDate
   * @returns
   */
  private getUnixDateFormat(sourceDate: string): string {
    if (sourceDate) {
      return this.datePipe.transform(sourceDate, "yyyy-MM-dd HH:mm:ss");
    }
    return undefined;
  }

  /**
   * 普通测评列表 排序
   * @param sort
   */
  public sort(sort: { key: string; value: string }): void {
    this.getTableData(false, sort.key, sort.value);
    // this.search();
  }

  /**
   * 360测评列表 排序
   * @param sort
   */
  public sortChange(sort: { key: string; value: string }): void {
    this.getTableData(false, sort.key, sort.value);
  }

  /**
   * 过滤
   * @param type
   * @param name
   */
  filter(type?: string, name?: string) {
    this.thName = name;
    let arr = [];
    if (type && type === "ANNOUNCED") {
      arr = [type, "NOT_INVITED"];
    } else {
      arr = [type];
    }
    !type ? (this.answerStatus = []) : (this.answerStatus = arr);
    this.getTableData();
  }

  /**
   * 刷新
   */
  refresh() {
    this.thName = "全部";
    this.getDetail(true);
  }

  /**
   * 活动详情（测评，调研，不支持360） -> 进度列表 -> 导入列表
   *
   * 下载中有页面遮罩，不支持用户重复操作
   *
   * 下载完成，导出单个Excel文件
   *
   * 下载失败，文字提示“下载失败”，不需要用户确认。
   *
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const isUpdata =
      item.file.type ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      item.file.type === "application/vnd.ms-excel";

    if (!isUpdata) {
      // return this.msg.error("上传文件格式应为excel");
      return this.customMsg.open("error", "上传文件格式应为excel");
    }

    const formData = new FormData();
    formData.append("excel", item.file as any);

    // 前面有操作在操作中，不能支持此操作，需等前面操作完成后。
    if (this.isloading) {
      return false;
    }
    // 操作开始，设置loading遮罩状态。避免用户进行其他操作，显性提醒用户此操作在进行中，还未结束。
    this.isloading = true;

    this.surveySerivce
      .uploadFilePerson(formData, this.transType, this.projectId)
      .subscribe(
        (event: HttpEvent<any>) => {
          let res: any = event;
          if (res.result.code === 0) {
            // 服务端操作完成，取消置loading遮罩状态。显性让用户知道此操完成了。
            this.isloading = false;

            this.msg.success("导入人员成功");
            this.getTableData();
          } else {
            // 服务端操作失败，取消置loading遮罩状态。
            this.isloading = false;

            // 为什么大于10000的错误不显示？？
            if (res.result.code < 10000) {
              // this.msg.error(res.result.message);
              this.customMsg.open("error", res.result.message);
            }
          }
        },
        (err) => {
          // 服务端操作失败，取消置loading遮罩状态。
          this.isloading = false;

          item.onError!(err, item.file!);
        }
      );
  };

  /**
   * 活动详情（测评，360，调研） -> 进度列表 -> 导出列表
   *
   * 下载中有页面遮罩，不支持用户重复操作
   *
   * 下载完成，导出单个Excel文件
   *
   * 下载失败，文字提示“下载失败”，不需要用户确认。
   *
   * @returns excel
   */
  exportUsers() {
    // 前面有操作在操作中，不能支持此操作，需等前面操作完成后。
    if (this.isloading) {
      return false;
    }
    // 操作开始，设置loading遮罩状态。避免用户进行其他操作，显性提醒用户此操作在进行中，还未结束。
    this.isloading = true;

    let api: string = `${this.tenantUrl}/survey/project/exportPersonExcel?projectId=${this.projectId}`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any> = this.http.post(api, {}, param);

    // 调取后端服务接口
    sub.subscribe(
      (data) => {
        this.downFile(data);
        // 服务端操作完成，取消置loading遮罩状态。显性让用户明白此操完成了。
        this.isloading = false;
      },
      (error1) => {
        // 服务端操作失败，取消置loading遮罩状态。
        this.isloading = false;

        // 提示用户操完失败
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  }

  /**
   * 活动详情（仅调研） -> 进度列表 -> 导出原始填答
   *
   * 下载中有页面遮罩，不支持用户重复操作
   *
   * 下载完成，导出单个Excel文件
   *
   * 下载失败，文字提示“下载失败”，不需要用户确认
   *
   * @returns excel
   */
  exportOriginal() {
    // 前面有操作在操作中，不能支持此操作，需等前面操作完成后。
    if (this.isloading) {
      return false;
    }

    // 操作开始，设置loading遮罩状态。避免用户进行其他操作，显性提醒用户此操作在进行中，还未结束。
    this.isloading = true;

    // 定义接口地址
    let api: string = `${this.tenantUrl}/survey/download/answer/prisma/template?projectId=${this.projectId}`;
    if (this.transType !== 3) {
      api = `${this.tenantUrl}/survey/download/answer/assess/template?projectId=${this.projectId}`;
    }
    // 传参
    let param: any = { responseType: "blob", observe: "response" };
    // 请求方法 POST
    let sub: Observable<any> = this.http.post(api, {}, param);

    sub.subscribe(
      (data) => {
        // 调取后端服务接口
        this.downFile(data);

        // 服务端操作完成，取消置loading遮罩状态。显性让用户明白此操完成了。
        this.isloading = false;
      },
      (error1) => {
        // 服务端操作失败，取消置loading遮罩状态。
        this.isloading = false;

        // 提示用户操完失败
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  }

  /**
   * 活动详情（测评，360，调研） -> 进度列表 -> 导入原始答案操作
   *
   * 下载中有页面遮罩，不支持用户重复操作
   *
   * 下载完成，导出单个Excel文件
   *
   * 下载失败，文字提示“下载失败”，不需要用户确认。
   *
   * @param item
   */
  customReqOriginal = (item: UploadXHRArgs) => {
    const isUpdata =
      item.file.type ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      item.file.type === "application/vnd.ms-excel";

    if (!isUpdata) {
      // return this.msg.error("上传文件格式应为excel");
      return this.customMsg.open("error", "上传文件格式应为excel");
    }

    const formData = new FormData();
    formData.append("excel", item.file as any);

    // 前面有操作在操作中，不能支持此操作，需等前面操作完成后。
    if (this.isloading) {
      return false;
    }
    // 操作开始，设置loading遮罩状态。避免用户进行其他操作，显性提醒用户此操作在进行中，还未结束。
    this.isloading = true;

    // 掉service进行导入
    return this.surveySerivce.importAnswer(formData, this.projectId).subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          // 服务端操作完成，取消置loading遮罩状态。显性让用户知道此操完成了。
          this.isloading = false;

          this.msg.success("导入人员成功");
          this.getTableData();
        } else {
          // 服务端操作失败，取消置loading遮罩状态。
          this.isloading = false;

          // 为什么大于10000的错误不显示？？
          if (res.result.code < 10000) {
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      },
      (err) => {
        // 服务端操作失败，取消置loading遮罩状态。
        this.isloading = false;

        item.onError!(err, item.file!);
      }
    );
  };

  /**
   * 导出填答进度 （废弃）
   */
  exportOrgDetail() {
    let api: string = `${this.tenantUrl}/survey/project/exportOrganizationAnswerRate?projectId=${this.projectId}`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any> = this.http.post(api, {}, param);

    sub.subscribe(
      (data) => {
        this.downFile(data);
      },
      (error1) => {
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  }

  /**
   * 通用下载函数
   *
   * @param data
   */
  downFile(data) {
    this.showBlobErrorMessage(data);

    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];
    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * 文件报错提示
   * @param data
   */
  showBlobErrorMessage(data: any) {
    let body = data.body;
    if (body.type === "application/json") {
      let that = this;
      const reader = new FileReader();
      reader.readAsText(body, "utf-8");
      reader.onload = () => {
        // 处理报错信息
        // JSON.parse(reader.result) 拿到报错信息
        let resp: any = JSON.parse(reader.result + "");
        let code: number = resp.result.code;
        let errMsg: string = resp.result.message;

        if (code !== 0) {
          // that.msg.error(`${errMsg}，请联系管理员。`);
          this.customMsg.open("error", `${errMsg}，请联系管理员。`);
        }
      };
    }
  }

  /**
   * 删除/隐藏
   */
  deleteFacNameok() {
    this.api
      .updateProject({ id: this.projectId, isShow: false })
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.router.navigate(["/project-manage/home"]);
        }
      });
  }

  /**
   * 编辑
   */
  edit() {
    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    if (this.projectData.surveyType === "EMPLOYEE_ENGAGEMENT") {
      localStorage.setItem("backurl", this.router.routerState.snapshot.url);
      this.router.navigate(["new-prisma"], {
        queryParams: {
          projectId: this.projectData.id,
          type: this.projectData.status,
        },
      });
    } else {
      if (!this.projectData.is360Project) {
        this.projectData.standardReportType = this.projectData
          .standardReportType
          ? this.projectData.standardReportType
          : "other";
      }
      localStorage.setItem("backurl", this.router.routerState.snapshot.url);
      this.router.navigate(["new-create"], {
        queryParams: {
          projectId: this.projectData.id,
          projectCode: this.projectData.code,
          projectType: this.projectData.status,
          standardQuestionnaireId: this.projectData.questionnaires[0]
            .standardQuestionnaireId,
          questionnaireId: this.projectData.questionnaires[0].id,
          standardReportType: this.projectData.standardReportType,
        },
      });
      if (!this.projectData.is360Project) {
        sessionStorage.setItem(
          "standardQuestionnaireIds",
          JSON.stringify(this.projectData.questionnaires)
        );
      }
      localStorage.setItem("noprismadata", null);
    }
  }

  /**
   * 邀请
   * @returns
   */
  invite() {
    localStorage.setItem("backurl", this.router.routerState.snapshot.url);
    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    if (this.projectData.status == "ANNOUNCED") {
      // this.msg.error("活动还未发布，暂不能邀请测评者");
      this.customMsg.open("error", "活动还未发布，暂不能邀请测评者");
      return;
    }
    this.api.listByProjectId(this.projectId).subscribe((res) => {
      res.data.map((item) => this.questionnaireIds.push(item.id));
    });
    if (this.projectData.surveyType === "ASSESSMENT") {
      if (!this.projectData.is360Project) {
        // 普通邀请
        this.router.navigate(["project-manage/invite"], {
          queryParams: {
            projectId: this.projectId,
            questionnaireIds: this.questionnaireIds,
            step: 2,
            name: this.projectData.name,
            projectCode: this.projectData.code,
          },
        });
      } else {
        // 360
        this.router.navigate(["project-manage/invite360"], {
          queryParams: {
            projectId: this.projectId,
            step: 2,
            name: this.projectData.name,
            projectCode: this.projectData.code,
          },
        });
      }
    } else {
      if (this.projectData.questionnaires[0].isShowOrganization) {
        // 是否显示组织架构  调研邀请
        this.router.navigate(["project-manage/inviteprisma"], {
          queryParams: {
            projectId: this.projectId,
            questionnaireIds: this.questionnaireIds,
            step: 2,
            name: this.projectData.name,
            projectCode: this.projectData.code,
          },
        });
      }
    }
  }

  /**
   * 分页相关-页码改变
   * @param e
   * @returns
   */
  pageIndexChange(e) {
    if (e === 0 || e > this.page.pages) return;
    this.page.current = e;
    this.getDetail();
  }

  /**
   * 分页相关-每页条数改变
   * @param e
   */
  pageSizeChange(e) {
    this.page.current = 1;
    this.page.size = e;
    this.getDetail();
  }

  /////////////////////////////////360表格相关
  private expandIds = [];
  /**
   * table展开
   * @param type
   * @param e
   */
  nzExpandChange(type, e) {
    if (type) {
      this.expandIds.push(e);
      this.expandIds = this.removaldata(this.expandIds);
    } else {
      this.expandIds.forEach((res, index) => {
        if (res == e) {
          delete this.expandIds[index];
        }
      });
    }
  }

  /**
   * 去重
   * @param arr
   * @returns
   */
  removaldata(arr) {
    let result = [];
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      if (!obj[arr[i]]) {
        result.push(arr[i]);
        obj[arr[i]] = true;
      }
    }
    return result;
  }

  /**
   * 邀请-360
   * @param projectId
   * @param step
   * @param email
   * @param id
   * @returns
   */
  goAndInvite360(projectId: string, step: number, email: string, id: string) {
    if (this.projectData.status == "ANNOUNCED") {
      // this.msg.error("活动还未发布，暂不能邀请测评者");
      this.customMsg.open("error", "活动还未发布，暂不能邀请测评者");
      return;
    }
    this.router.navigate(["project-manage/invite360"], {
      queryParams: {
        projectId,
        step,
        email,
        id,
      },
    });
  }

  goInvite360(
    investigatorId: string,
    personId: string,
    projectId: string,
    action: string,
    step: number,
  ) {
    this.router.navigate(["project-manage/invite360"], {
      queryParams: {
        investigatorId,
        personId,
        projectId,
        action,
        step,
      },
    });
  }

  /**
   * 根据如下ID删除某一项
   * @param surveyId 调查者ID
   * @param personId 测评者ID
   */
  public deleteListById(surveyId: string, personId?: string): void {
    this.http
      .get(
        `${this.tenantUrl}/survey/person/deleteEvaluator/${surveyId}${
          personId ? "/" + personId : ""
        }`
      )
      .toPromise()
      .then((res: HttpResponseWrapper) => {
        if (res.result.code === 0) {
          this.getTableData();
        } else {
          this.tableIsSpinning = false;
          this.notification.error("删除失败", `原因：${res.result.message}`);
        }
      });
  }

  /**
   * 根据如下ID删除某一项
   * @param id
   */
  public deleteById(id: string): void {
    this.http
      .get(`${this.tenantUrl}/survey/person/deletePerson/${id}`)
      .toPromise()
      .then((res: HttpResponseWrapper) => {
        if (res.result.code === 0) {
          this.getTableData();
        } else {
          this.tableIsSpinning = false;
          this.notification.error("删除失败", `原因：${res.result.message}`);
        }
      });
  }

  /**
   * 编辑
   * @param data
   */
  geteditors(data) {
    this.router.navigate(["project-manage/invite360"], {
      queryParams: {
        projectId: data.surveyInvestigator.projectId,
        investigatorId: data.surveyInvestigator.id,
        step: 1,
        type: "home-detail",
        action: "edit",
        standardReportType: this.projectData.standardReportType,
        isCustomRoleWeight: this.projectData.isCustomRoleWeight,
        projectCode: this.projectData.code,
      },
    });
  }

  /**
   * 跳转到查看报告页
   * @param personId 测评者id
   */
  public gotoReport(personId: string, type?: string): void {
    if (type === "360") {
      this.router.navigateByUrl(`/report-manage?investigatorId=${personId}`);
    } else {
      this.router.navigateByUrl(`/report-manage?personId=${personId}`);
    }
  }

  ///////////////////////  调研
  /**
   * 填答进度
   */
  fillInProgress() {
    const drawer = this.drawerService.create({
      nzContent: PrismaDrawerComponent,
      nzContentParams: {
        projectId: this.projectId,
        // orgId: orgId,
        // orgName: orgName
      },
      nzTitle: "填答进度",
      nzWrapClassName: "round-right-drawer8-nofooter",
      nzWidth: 650,
      nzMaskClosable: true,
      nzClosable: true,
    });

    this.loginServ.drawerRef = drawer;
  }

  /**
   * 查看人员（弃用）
   * @param orgId
   * @param orgName
   */
  openPersonList(orgId: string, orgName: string) {
    const modal = this.drawerService.create({
      nzContent: PersonListComponent,
      nzContentParams: {
        projectId: this.projectId,
        orgId: orgId,
        orgName: orgName,
      },
      nzTitle: `组织名称：${orgName}`,
      nzWidth: 530,
      nzWrapClassName: "round-right-drawer8",
      nzMaskClosable: true,
      nzClosable: true,
    });
    this.loginServ.drawerRef = modal;
  }

  /**
   * 操作指南-跳过
   */
  jumprun() {
    this.setp1 = false;
    this.setp2 = false;
    this.setp3 = false;
    this.showmock = false;
  }

  /**
   * 操作指南-下一步1
   */
  next1() {
    this.setp1 = false;
    this.setp2 = true;
  }

  /**
   * 操作指南-下一步2
   */
  next2() {
    this.setp2 = false;
    this.setp3 = true;
  }

  /**
   * 操作指南-下一步3
   */
  next3() {
    this.setp3 = false;
    this.showmock = false;
  }

  /**
   * 操作指南-打开
   */
  getnewlead() {
    this.showmock = true;
    this.setp1 = true;
  }

  /**
   * 获取邮件功能是否展示
   *@author:longh
   *@Date:2024/12/31
   */
  getIsDisplayMail() {
    const url = `${this.tenantUrl}/survey/project/isOpenMailSend`;

    this.http.get(url).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.isDisplayMail = res.data;
      }
    });
  }
}
