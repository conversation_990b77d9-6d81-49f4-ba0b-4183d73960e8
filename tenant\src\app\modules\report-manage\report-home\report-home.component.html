<!-- 遮罩层,用于关闭弹窗 -->
<div class="mark" *ngIf="showOneBox || showVideoBox" (click)="closeBox()"></div>

<!-- 报告管理主容器 -->
<div class="report-manage">
  <div class="content client-width" style="height: 100%;">
    <!-- 解释说明区域 -->
    <div class="interpretation">
      <!-- 下载列表组件 -->
      <app-download-list
        [downloadnum]="downloadnum"
        (backclick)="backclick($event)"
      ></app-download-list>
    </div>

    <!-- 面包屑导航 -->
    <div style="height: 15px; width: 100%;"></div>
    <div style="display: flex;justify-content: flex-end;margin-bottom: 16px;">
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </div>

    <!-- banner区域 -->
    <div class="banner" nz-row nzGutter="10" *ngIf="isShowBanner()">
      <!-- 报告解释banner -->
      <div nz-col class="banner-item" nzSpan="12" *ngIf="isShowReportExplain()">
        <div class="banner-item-img">
          <img
            src="assets/images/report-home/onlineVideos.png"
            alt="在线视频"
          />
        </div>
        <div class="left-operate">
          <!-- 立即观看按钮 -->
          <span
            class="left-operate-video"
            (click)="showVideo()"
            *ngIf="
              permissionService.isPermissionOrSag(
                'SAG:TENANT:REPORT_MGT:REPORT_LIST:VIDEO'
              )
            "
            >立即观看</span
          >
          <span
            *ngIf="
              !permissionService.isPermissionOrSag(
                'SAG:TENANT:REPORT_MGT:REPORT_LIST:VIDEO'
              )
            "
          ></span>
          <!-- 预约专家按钮 -->
          <span
            class="left-operate-expert"
            (click)="showOneOnOne()"
            *ngIf="
              permissionService.isPermissionOrSag(
                'SAG:TENANT:REPORT_MGT:REPORT_LIST:EXPERT_INTERPRETATION'
              )
            "
            >不，预约专家1V1解读</span
          >
          <span
            *ngIf="
              !permissionService.isPermissionOrSag(
                'SAG:TENANT:REPORT_MGT:REPORT_LIST:EXPERT_INTERPRETATION'
              )
            "
          ></span>
        </div>
      </div>

      <!-- 在线看板banner -->
      <div
        nz-col
        class="banner-item"
        nzSpan="12"
        *knxFunctionPermission="
          'SAG:TENANT:REPORT_MGT:REPORT_LIST:ONLINE_REPORT'
        "
      >
        <div class="banner-item-img">
          <img
            src="assets/images/report-home/customReports.png"
            alt="自定义报告"
          />
        </div>
        <div class="right-operate">
          <!-- 立即体验按钮 -->
          <button
            nz-button
            nzType="primary"
            class="right-btn"
            (click)="goToOnlineReportList()"
          >
            立即体验
          </button>
        </div>
      </div>
    </div>

    <!-- 报告类型切换 -->
    <div class="header">
      <div
        *ngFor="let item of headerList"
        class="item"
        [ngClass]="{ selected: item.isSelected }"
        (click)="switchHeader(item)"
      >
        <img [attr.src]="item.imgPath" alt="报告类型图标" />
        <span style="margin-left: 20px;">{{ item.name }}</span>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search">
      <div class="condition">
        <!-- 模糊搜索下拉框 -->
        <div class="fuzzySearch">
          <nz-input-group nzCompact>
            <nz-select
              [(ngModel)]="filterType"
              nzPlaceHolder="请选择"
              [nzDisabled]="childNum !== 0"
              style="width: 120px;"
            >
              <nz-option nzValue="projectName" nzLabel="活动名称"></nz-option>
              <nz-option
                *ngIf="selectHeader.id === 1"
                nzValue="name"
                nzLabel="人员姓名"
              ></nz-option>
              <nz-option
                *ngIf="selectHeader.id !== 1"
                nzValue="reportName"
                nzLabel="报告名称"
              ></nz-option>
              <nz-option
                nzValue="questionnaireType"
                nzLabel="产品类型"
              ></nz-option>
              <nz-option
                *ngIf="selectHeader.id === 1"
                nzValue="demographic"
                nzLabel="人口学信息"
              ></nz-option>
              <nz-option
                *ngIf="selectHeader.id === 2"
                nzValue="groupDetailName"
                nzLabel="团队明细"
              ></nz-option>
              <nz-option
                *ngIf="selectHeader.id === 3"
                nzValue="groupDetailName"
                nzLabel="对比明细"
              ></nz-option>
            </nz-select>

            <!-- 搜索输入框 -->
            <nz-input-group [nzSuffix]="suffixIconSearch">
              <input
                nz-input
                [disabled]="childNum !== 0"
                placeholder="请输入"
                [(ngModel)]="filterValue"
                (keydown.enter)="search()"
              />
            </nz-input-group>
            <ng-template #suffixIconSearch>
              <i
                class="iconfont icon-search_o searchIcon"
                (click)="search()"
              ></i>
            </ng-template>
          </nz-input-group>
        </div>

        <!-- 高级筛选组件 -->
        <app-advanced-model
          #advancedChild
          [father]="this"
          [type]="selectHeader.id"
        ></app-advanced-model>

        <!-- 分类下拉框 -->
        <nz-select
          [(ngModel)]="selectedValue"
          (ngModelChange)="search()"
          nzPlaceHolder="请选择"
          style="width: 100px;"
        >
          <nz-option nzValue="" nzLabel="全部分类"></nz-option>
          <nz-option nzValue="ASSESSMENT" nzLabel="测评"></nz-option>
          <nz-option nzValue="EMPLOYEE_ENGAGEMENT" nzLabel="调研"></nz-option>
        </nz-select>
        <!-- 时间范围选择器 -->
        <!-- (ngModelChange)="search()" -->
        <nz-range-picker
          [nzFormat]="dateFormat"
          [(ngModel)]="dateRange"
          (nzOnOk)="search()"
          (ngModelChange)="dateChange($event)"
          [nzShowTime]="{ nzFormat: 'HH:mm:ss' }"
          (nzOnCalendarChange)="onCalendarChange($event)"
        ></nz-range-picker>
        <!-- 账户下拉框 -->
        <!-- <nz-select
          [(ngModel)]="selectedUserValue"
          nzPlaceHolder="账户筛选"
          nzAllowClear
          nzMode="multiple"
          style="width: 100px;margin-left: 8px;"
          [nzDropdownMatchSelectWidth]="false"
        >
          <ng-container *ngFor="let item of userList">
            <nz-option
              [nzValue]="item.id"
              nzCustomContent
              [nzLabel]="item.username"
            >
              <span
                nz-tooltip
                [nzTooltipTitle]="item.username + '-' + item.id"
                nzOverlayClassName="create-tooltip"
                style="background-color: gold; width: 100%;display: flex;"
                >{{ item.username }}</span
              >
            </nz-option>
          </ng-container>
        </nz-select> -->
        <div
          nz-popover
          nzPlacement="bottomRight"
          [nzContent]="createPopoverTemplate"
          nzTrigger="click"
          [(nzVisible)]="userFilterVisible"
          (nzVisibleChange)="onUserFilterPopoverShow($event)"
          nzTrigger="click"
          *ngIf="selectHeader.id !== 1"
          [class]="childNum !== 0 ? 'btn-disabled create-btn' : 'create-btn'"
        >
          <span [class]="userFilterVisible ? 'text-blue' : ''">账户筛选</span>
          <i
            nz-icon
            nzType="down"
            nzTheme="outline"
            [class]="userFilterVisible ? 'arrow arrow-open' : 'arrow'"
          ></i>
        </div>

        <ng-template #createPopoverTemplate>
          <div style="display: flex;align-items: center;">
            <nz-select
              [(ngModel)]="selectedUserValue"
              nzPlaceHolder="账户筛选"
              nzAllowClear
              nzMode="multiple"
              nzShowSearch
              style="width: 300px;"
              [nzDropdownMatchSelectWidth]="false"
            >
              <ng-container *ngFor="let item of userList">
                <nz-option
                  [nzValue]="item.id"
                  nzCustomContent
                  [nzLabel]="item.username"
                >
                  <!-- <span
                    nz-tooltip
                    [nzTooltipTitle]="item.username + '-' + item.id"
                    nzOverlayClassName="create-tooltip"
                    style="width: 100%;display: flex;"
                    >{{ item.username }}</span
                  > -->
                  <span style="width: 100%;display: flex;">{{
                    item.username
                  }}</span>
                </nz-option>
              </ng-container>
            </nz-select>
            <!-- <button nz-button nzType="link" (click)="onUserFilterBtn()" style="padding: 0;margin-left: 8px;">
              查询
            </button>
            <button nz-button nzType="link" nzGhost (click)="onUserClearBtn()" style="padding: 0;color: #595959;">
              清空
            </button> -->
          </div>
        </ng-template>

        <!-- 生成状态复选框 -->
        <div class="build" *ngIf="selectHeader.id !== 3">
          <nz-checkbox-wrapper (nzOnChange)="changeBuild($event)">
            <label nz-checkbox [(ngModel)]="buildSucceed" nzValue="SUCCESS"
              >生成成功</label
            >
            <label nz-checkbox [(ngModel)]="buildFail" nzValue="FAIL"
              >生成失败</label
            >
          </nz-checkbox-wrapper>
        </div>

        <!-- 帮助指引按钮 -->
        <i
          nz-icon
          nzType="question-circle"
          nzTheme="fill"
          (click)="getnewlead()"
          class="newlead"
        ></i>
      </div>

      <!-- 清空条件按钮 -->
      <div (click)="switchHeader()" class="clearBtn">
        清空条件
      </div>
    </div>

    <!-- 数据列表区域 -->
    <div class="data">
      <!-- 加载中状态 -->
      <div
        style="text-align: center; height: 460px; display: flex; align-items: center; justify-content: center;"
        *ngIf="isLoading"
      >
        <ng-template #indicatorTemplate>
          <i nz-icon nzType="loading" style="font-size: 24px;"></i>
        </ng-template>
        <nz-spin nzSimple [nzIndicator]="indicatorTemplate"> </nz-spin>
      </div>

      <!-- 标准报告表格 -->
      <nz-table
        *ngIf="!isLoading && selectHeader.id === 1"
        class="report-table"
        #basicTable
        [nzData]="listOfData"
        nzBordered
        [nzFrontPagination]="false"
        [nzScroll]="{ x: '1200px' }"
        [ngClass]="{ 'table-empty': !(listOfData && listOfData.length) }"
      >
        <!-- 表头 -->
        <thead>
          <tr>
            <!-- 全选列 -->
            <th
              *ngIf="selectHeader?.id === 1"
              nzWidth="70px"
              nzLeft="0px"
              nzAlign="center"
            >
              <div>
                <button
                  *ngIf="allFlag"
                  style="padding:0;"
                  nz-button
                  nzType="link"
                  nzSize="small"
                  (click)="btnSelectAll()"
                >
                  全选
                </button>
                <button
                  *ngIf="!allFlag"
                  style="padding:0;"
                  nz-button
                  nzType="link"
                  nzSize="small"
                  (click)="btnCancelAll()"
                >
                  全不选
                </button>
              </div>
            </th>

            <!-- 动态列 -->
            <ng-container *ngFor="let colDto of columnList">
              <th
                *ngIf="colDto.isSelected"
                [nzWidth]="colDto.width"
                [nzLeft]="colDto.left"
                [nzRight]="colDto.right"
              >
                <span *ngIf="colDto.attrName !== 'personId'">
                  {{ colDto.title }}
                </span>

                <!-- 姓名列设置 -->
                <div *ngIf="colDto.attrName === 'personId'" class="thSetting">
                  <div>
                    {{ colDto.title }}
                  </div>
                </div>
              </th>
            </ng-container>

            <!-- 列设置模板 -->
            <ng-template #contentTemplateCol>
              <div style="margin:0; padding: 0; ">
                <app-column-set
                  (dragged)="columnSortChanged($event)"
                  [columnList]="columnList"
                ></app-column-set>
              </div>
            </ng-template>

            <!-- 操作列 -->
            <th [nzWidth]="tdWidth" nzRight="0px">
              <div
                style="width:100%; display: flex; justify-content: flex-start;"
              >
                <div
                  style="margin-left: 4px; display: flex; align-items: center;"
                  class="personNum"
                  nz-popover
                  nzPopoverPlacement="bottom"
                  nzPopoverTitle="可配置列"
                  [nzPopoverContent]="contentTemplateCol"
                >
                  <i nz-icon nzType="setting" nzTheme="outline"></i>
                </div>

                <span>&nbsp;&nbsp;操作</span>
              </div>
            </th>
          </tr>
        </thead>

        <!-- 表格内容 -->
        <tbody>
          <tr *ngFor="let data of basicTable.data">
            <!-- 选择框列 -->
            <td
              *ngIf="selectHeader?.id === 1"
              nzWidth="70px"
              nzLeft="0px"
              nzAlign="center"
            >
              <label
                nz-checkbox
                [(ngModel)]="data.isChecked"
                (ngModelChange)="toggoleItem(data, $event)"
              ></label>
            </td>

            <!-- 动态数据列 -->
            <ng-container *ngFor="let col of columnList">
              <td
                *ngIf="col.isSelected"
                [nzLeft]="col.left"
                [nzRight]="col.right"
              >
                <!-- 日期类型 -->
                <p *ngIf="col.type === 'date'">
                  {{ data[col.attrName] | date: "yyyy-MM-dd HH:mm:ss" }}
                </p>

                <!-- 人员ID列 -->
                <p
                  *ngIf="col.attrName === 'personId'"
                  (click)="userInfoShow(data.personId || data.investigatorId)"
                  class="userInfoShow"
                >
                  {{ data.name }}
                </p>

                <!-- 人口学信息列 -->
                <div
                  class="maxW"
                  *ngIf="col.attrName === 'demographic'"
                  nz-tooltip
                  [nzTooltipTitle]="demoConent"
                >
                  <div *ngFor="let dItem of data[col.attrName]">
                    {{ dItem }}
                  </div>
                  <ng-template #demoConent>
                    <div *ngFor="let dItem of data[col.attrName]">
                      {{ dItem }}
                    </div>
                  </ng-template>
                </div>

                <!-- 项目名称列 -->
                <div *ngIf="col.attrName == 'projectName'" class="projectName">
                  <div
                    class="projectName_text1"
                    [ngClass]="{ showTag: isValid(data) || isFail(data) }"
                  >
                    <ng-container *ngIf="col.attrName1; else attrName">
                      <span
                        nz-tooltip
                        nzPlacement="topLeft"
                        [nzTooltipTitle]="data[col.attrName][col.attrName1]"
                        >{{ data[col.attrName][col.attrName1] }}</span
                      >
                    </ng-container>
                    <ng-template #attrName>
                      <span
                        nz-tooltip
                        nzPlacement="topLeft"
                        [nzTooltipTitle]="data[col.attrName]"
                        >{{ data[col.attrName] }}</span
                      >
                    </ng-template>
                  </div>
                  <!-- 状态标签 -->
                  <span *ngIf="isFail(data)" class="tag_fail">失败</span>
                  <span *ngIf="isValid(data)" class="tag_invalid">无效</span>
                </div>

                <!-- 其他普通列 -->
                <p
                  *ngIf="
                    col.type !== 'date' &&
                    col.attrName !== 'personId' &&
                    col.attrName !== 'demographic' &&
                    col.attrName !== 'projectName'
                  "
                >
                  {{
                    col.attrName1
                      ? data[col.attrName][col.attrName1]
                      : data[col.attrName]
                  }}
                </p>
              </td>
            </ng-container>

            <!-- 操作列 -->
            <td [width]="tdWidth" nzRight="0px">
              <div style="margin-left: 4px;">
                <!-- 有效报告操作 -->
                <ng-container *ngIf="!isValid(data)">
                  <span
                    style="padding:0"
                    *ngIf="data.isShowReport && data.isShowViewReport"
                  >
                    <!-- 查看报告下拉菜单 -->
                    <a nz-dropdown [nzDropdownMenu]="menu">
                      查看报告
                      <i nz-icon nzType="down"></i>
                    </a>
                    <nz-dropdown-menu #menu="nzDropdownMenu">
                      <ul nz-menu nzSelectable>
                        <ng-container *ngFor="let val2 of data.style">
                          <li nz-menu-item>
                            <a (click)="goReport(val2.url)" target="_blank">
                              <img
                                *ngIf="val2.flag === true"
                                src="./assets/images/warn.png"
                              />
                              <span
                                *ngIf="val2.reportStatus === 'REPORT_GENERATED'"
                                >{{ val2.name }}</span
                              >
                              <span
                                *ngIf="
                                  val2.reportStatus === 'REPORT_GENERATING' ||
                                  (val2.reportStatus !== 'REPORT_GENERATED' &&
                                    !permission)
                                "
                                >生成中</span
                              >
                              <span
                                *ngIf="
                                  val2.reportStatus === 'REPORT_FAIL' &&
                                  permission
                                "
                                >创建报告失败</span
                              >
                              <span
                                *ngIf="
                                  val2.reportStatus === 'SCORE_FAIL' &&
                                  permission
                                "
                                >生成分数失败</span
                              >
                            </a>
                          </li>
                        </ng-container>
                      </ul>
                    </nz-dropdown-menu>
                    &nbsp;
                  </span>
                </ng-container>

                <!-- 权限验证操作 -->
                <ng-container *ngIf="!isPermisionValid(data)">
                  <!-- 创建对比报告 -->
                  <a (click)="showToolBox(data)">
                    <a
                      [nzTooltipTitle]="TempC"
                      nzTooltipPlacement="left"
                      [nzTooltipTrigger]="'click'"
                      nz-tooltip
                      nzOverlayClassName="tool-box"
                      (nzVisibleChange)="showVisible($event, data)"
                      [nzVisible]="data.isShowToolBox"
                    ></a>
                    创建对比报告
                  </a>
                  &nbsp;

                  <!-- 对比报告工具箱模板 -->
                  <ng-template #TempC>
                    <div class="tempCC" *ngIf="toolType === 1">
                      <!-- 工具箱头部 -->
                      <div class="tooltip-header">
                        <img
                          src="./assets/images/report-home/icon/icon-bar.png"
                          alt=""
                        />
                        <h1>测评对比报告</h1>
                        <span>请选择≤5位人员进行对比</span>
                        <img
                          class="close"
                          src="./assets/images/report-home/icon/icon-close.png"
                          (click)="close(data)"
                          alt=""
                        />
                      </div>

                      <!-- 搜索框 -->
                      <div class="search-box">
                        <nz-input-group
                          [nzPrefix]="suffixIconSearch"
                          class="search-tool"
                        >
                          <input
                            type="text"
                            nz-input
                            placeholder="请输入关键词"
                            [(ngModel)]="searchText"
                            (keyup.enter)="searchData()"
                          />
                        </nz-input-group>
                        <ng-template #suffixIconSearch>
                          <i
                            nz-icon
                            nzType="search"
                            style="color: #409EFF"
                            (click)="searchData()"
                          ></i>
                        </ng-template>
                      </div>

                      <!-- 数据列表 -->
                      <ul class="tool-scroll">
                        <li nz-row *ngFor="let item of getDataList()">
                          <div nz-col nzSpan="6">
                            <label
                              nz-checkbox
                              [(ngModel)]="item.isChecked"
                            ></label>
                          </div>
                          <div nz-col nzSpan="6">{{ item.name }}</div>
                          <div nz-col nzSpan="12">
                            {{ item.description ? item.description : "-" }}
                          </div>
                        </li>
                      </ul>

                      <!-- 底部按钮 -->
                      <div class="footer">
                        <button
                          nz-button
                          class="toolBtn"
                          [nzLoading]="isToolBtnLoading"
                          (click)="toolOk(data)"
                        >
                          <span>确认</span>
                        </button>
                      </div>
                    </div>

                    <!-- 调研报告工具箱 -->
                    <div class="tempCDiaoyan" *ngIf="toolType === 2">
                      <!-- 工具箱头部 -->
                      <div class="tooltip-header">
                        <div>
                          <img
                            src="./assets/images/org/compare_org.png"
                            alt=""
                          />
                          <h1>调研对比报告</h1>
                          <span>请选择≤5个部门进行对比</span>
                        </div>
                        <img
                          class="close"
                          src="./assets/images/report-home/icon/icon-close.png"
                          (click)="close(data)"
                          alt=""
                        />
                      </div>

                      <!-- 部门选择器 -->
                      <div class="tool-content">
                        <nz-transfer
                          [nzDataSource]="list"
                          [nzShowSelectAll]="false"
                          [nzRenderList]="[leftRenderList, null]"
                          (nzChange)="change($event, items)"
                        >
                          <ng-template
                            #leftRenderList
                            let-items
                            let-onItemSelectAll="onItemSelectAll"
                            let-onItemSelect="onItemSelect"
                          >
                            <!-- 搜索框 -->
                            <nz-input-group
                              [nzSuffix]="suffixIcon"
                              style="padding: 0"
                            >
                              <input
                                type="text"
                                nz-input
                                placeholder="请输入关键词"
                                [(ngModel)]="searchTreeValue"
                              />
                            </nz-input-group>
                            <ng-template #suffixIcon>
                              <i nz-icon nzType="search"></i>
                            </ng-template>

                            <!-- 部门树 -->
                            <nz-tree
                              #tree
                              [nzData]="treeData"
                              [nzExpandedKeys]="nzExpandedKeys"
                              [nzCheckedKeys]="checkedNodeList"
                              [nzSearchValue]="searchTreeValue"
                              nzMultiple
                            >
                              <ng-template #nzTreeTemplate let-node>
                                <span
                                  class="ant-tree-checkbox"
                                  [class.ant-tree-checkbox-disabled]="
                                    node.isDisabled
                                  "
                                  [class.ant-tree-checkbox-checked]="
                                    node.isChecked
                                  "
                                  (click)="checkBoxChange(node, onItemSelect)"
                                >
                                  <span class="ant-tree-checkbox-inner"></span>
                                </span>
                                <span
                                  (click)="checkBoxChange(node, onItemSelect)"
                                  class="ant-tree-node-content-wrapper ant-tree-node-content-wrapper-open"
                                  [ngStyle]="{
                                    color: isSearchStyle(
                                      node.title,
                                      searchTreeValue
                                    )
                                  }"
                                  >{{ node.title }}</span
                                >
                              </ng-template>
                            </nz-tree>
                          </ng-template>
                        </nz-transfer>
                      </div>

                      <!-- 底部按钮 -->
                      <div class="footer">
                        <button
                          nz-button
                          class="toolBtn"
                          nzType="primary"
                          [nzLoading]="isToolBtnLoading"
                          (click)="toolOkPrisma(data)"
                        >
                          <span>确认</span>
                        </button>
                      </div>
                    </div>
                  </ng-template>
                </ng-container>

                <!-- 本地上传 -->
                <nz-upload
                  *ngIf="
                    permissionService.isPermissionOrSag(
                      'SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:UPLOAD'
                    )
                  "
                  [nzCustomRequest]="customReq"
                  [nzShowUploadList]="false"
                >
                  <a (click)="normalClick(data)">
                    <span>本地上传</span>
                  </a>
                  &nbsp;
                </nz-upload>

                <!-- 报告状态图标 -->
                <span
                  style="padding:0; margin-left:15px;"
                  *ngIf="data.isShowReport && data.prismaReportDataId"
                >
                  <a *ngFor="let val2 of data.style">
                    <i
                      nz-icon
                      nzType="info-circle"
                      nzTheme="twotone"
                      [nzTwotoneColor]="'#ff0000'"
                      *ngIf="
                        (val2.reportStatus === 'REPORT_FAIL' ||
                          val2.reportStatus === 'SCORE_FAIL') &&
                        permission
                      "
                    ></i>
                  </a>
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </nz-table>

      <!-- 修改报告名称弹窗 -->
      <nz-modal
        [(nzVisible)]="isBoxVisible"
        nzTitle="报告名称修改"
        (nzOnCancel)="handleBoxCancel()"
        (nzOnOk)="handleBoxOk()"
      >
        <!-- 中文名称 -->
        <div nz-row style="margin-bottom: 20px;">
          <div style="line-height: 32px; text-align: right;" nz-col nzSpan="4">
            中文：
          </div>
          <div nz-col nzSpan="18">
            <input
              nz-input
              placeholder="报告名称"
              [(ngModel)]="fileName.zh_CN"
              nzSize="default"
            />
          </div>
        </div>
        <!-- 英文名称 -->
        <div nz-row>
          <div style="line-height: 32px; text-align: right;" nz-col nzSpan="4">
            英文：
          </div>
          <div nz-col nzSpan="18">
            <input
              nz-input
              placeholder="报告名称"
              [(ngModel)]="fileName.en_US"
              nzSize="default"
            />
          </div>
        </div>
      </nz-modal>

      <!-- 团队报告表格 -->
      <nz-table
        *ngIf="!isLoading && selectHeader.id === 2"
        class="report-table"
        #basicTable
        [nzData]="listOfData"
        [nzFrontPagination]="false"
        nzBordered
        [nzScroll]="{ x: '1200px' }"
        [ngClass]="{ 'table-empty': !(listOfData && listOfData.length) }"
      >
        <!-- 表头 -->
        <thead>
          <tr>
            <!-- 全选列 -->
            <th nzWidth="70px" nzLeft="0px" nzAlign="center">
              <div>
                <button
                  *ngIf="allFlag"
                  style="padding:0;"
                  nz-button
                  nzType="link"
                  nzSize="small"
                  (click)="btnSelectAll('team')"
                >
                  全选
                </button>
                <button
                  *ngIf="!allFlag"
                  style="padding:0;"
                  nz-button
                  nzType="link"
                  nzSize="small"
                  (click)="btnCancelAll('team')"
                >
                  全不选
                </button>
              </div>
            </th>
            <!-- 动态列 -->
            <ng-container *ngFor="let colDto of teamColumnList">
              <th
                *ngIf="colDto.isSelected"
                [nzWidth]="colDto.width"
                [nzLeft]="colDto.left"
                [nzRight]="colDto.right"
              >
                {{ colDto.title }}
              </th>
            </ng-container>
            <!-- 操作 -->
            <th nzWidth="220px" nzRight="0px">
              <!-- 列设置模板 -->
              <ng-template #contentTemplateColTeam>
                <div style="margin:0; padding: 0; ">
                  <app-column-set
                    (dragged)="teamColumnSortChanged($event)"
                    [columnList]="teamColumnList"
                  ></app-column-set>
                </div>
              </ng-template>
              <div
                style="width:100%; display: flex; justify-content: flex-start;"
              >
                <div
                  style="margin-left: 4px; display: flex; align-items: center;"
                  class="personNum"
                  nz-popover
                  nzPopoverPlacement="bottom"
                  nzPopoverTitle="可配置列"
                  [nzPopoverContent]="contentTemplateColTeam"
                >
                  <i nz-icon nzType="setting" nzTheme="outline"></i>
                </div>
                <span>&nbsp;&nbsp;操作</span>
              </div>
            </th>
          </tr>
        </thead>

        <!-- 表格内容 -->
        <tbody>
          <tr *ngFor="let data of basicTable.data">
            <!-- 选择框 -->
            <td
              nzWidth="70px"
              nzLeft="0px"
              nzAlign="center"
              *ngIf="permission ? permission: data.isShow"
            >
              <label
                nz-checkbox
                [(ngModel)]="data.isChecked"
                (ngModelChange)="toggoleItem(data, $event, 'team')"
              ></label>
            </td>

            <!-- 数据列 -->
            <ng-container
            *ngIf="permission ? permission: data.isShow"
            >
              <!-- 动态数据列 -->
              <ng-container *ngFor="let col of teamColumnList">
                <td
                  *ngIf="col.isSelected"
                  [nzLeft]="col.left"
                  [nzRight]="col.right"
                >
                  <!-- 活动名称 -->
                  <div
                    *ngIf="col.attrName == 'projectName'"
                    class="projectName"
                  >
                    <div
                      class="projectName_text2"
                      nz-tooltip
                      nzPlacement="topLeft"
                      [nzTooltipTitle]="data.projectName?.zh_CN"
                    >
                      {{ data.projectName?.zh_CN }}
                    </div>
                    <!-- 失败标签 -->
                    <span
                      class="tag_fail"
                      nz-popover
                      [nzPopoverTitle]="null"
                      [nzPopoverContent]="data.failReason"
                      *ngIf="data.status === 'CREATE_REPORT_FAIL'"
                      >失败</span
                    >
                  </div>
                  <!-- 日期类型 -->
                  <p *ngIf="col.type === 'date'">
                    {{ data[col.attrName] | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                  <!-- 报告名称 -->
                  <div class="td-box" *ngIf="col.attrName == 'reportName'">
                    <p>{{ data.reportName?.zh_CN }}</p>
                    <i
                      class="iconfont icon-icon_edit"
                      (click)="showBox(data)"
                    ></i>
                  </div>
                  <!-- 团队明细 -->
                  <div *ngIf="col.attrName == 'generalComment'">
                    <!-- 团队报告 -->
                    <div
                      *ngIf="data.surveyType !== 'EMPLOYEE_ENGAGEMENT'"
                      style="display: flex; align-items: center; justify-content: flex-start;"
                    >
                      <div
                        class="maxW"
                        [id]="'content' + data.id"
                        nz-tooltip
                        [nzTooltipTitle]="groupConent"
                      >
                        <ng-template #groupConent>
                          <ng-container
                            *ngFor="
                              let name of data.generalComment.names;
                              let ind = index
                            "
                          >
                            <span *ngIf="ind > 0">
                              ,
                            </span>
                            <span>
                              {{ name }}
                            </span>
                          </ng-container>
                        </ng-template>

                        <ng-container
                          *ngFor="
                            let name of data.generalComment.names;
                            let ind = index
                          "
                        >
                          <span *ngIf="ind > 0">
                            ,
                          </span>
                          <span>
                            {{ name }}
                          </span>
                        </ng-container>
                      </div>

                      <button
                        class="iconbtn"
                        nz-button
                        nzType="default"
                        nzValue="small"
                        (click)="copy(data.id)"
                        nzTooltipTitle="复制内容"
                        nzTooltipPlacement="topLeft"
                        nz-tooltip
                      >
                        <i nz-icon nzType="copy" nzTheme="outline"></i>
                      </button>
                    </div>

                    <!-- 组织报告 -->
                    <div
                      *ngIf="
                        data.reportType === 'PRISMA' ||
                        data.surveyType === 'EMPLOYEE_ENGAGEMENT'
                      "
                      style="display: flex; align-items: center; justify-content: flex-start;"
                    >
                      <div
                        class="maxW"
                        [id]="'content' + data.id"
                        nz-tooltip
                        [nzTooltipTitle]="tipConent"
                      >
                        <ng-template #tipConent>
                          <div class="tooltipMargin">
                            组织：
                            <ng-container
                              *ngFor="
                                let item of data.generalComment.organizations;
                                let ind = index
                              "
                            >
                              <span *ngIf="ind > 0">,</span>
                              <span>
                                {{ item?.name?.zh_CN }}
                              </span>
                            </ng-container>
                          </div>

                          <div
                            class="tooltipMargin"
                            *ngFor="
                              let demo of data.generalComment.demographics
                            "
                          >
                            {{ demo?.name?.zh_CN }}：
                            <ng-container
                              *ngFor="
                                let item of demo.children;
                                let ind = index
                              "
                            >
                              <span *ngIf="ind > 0">,</span>
                              <span>
                                {{ item?.name?.zh_CN }}
                              </span>
                            </ng-container>
                          </div>

                          <div
                            class="tooltipMargin"
                            *ngIf="data.generalComment.norms.length != 0"
                          >
                            外部常模：
                            <ng-container
                              *ngFor="
                                let item of data.generalComment.norms;
                                let ind = index
                              "
                            >
                              <span *ngIf="ind > 0">,</span>
                              <span>
                                {{ item?.name?.zh_CN }}
                              </span>
                            </ng-container>
                          </div>

                          <div
                            *ngIf="
                              data.generalComment.internalNormTypes.length > 0
                            "
                            class="tooltipMargin"
                          >
                            内部常模：
                            <ng-container
                              *ngFor="
                                let item of data.generalComment
                                  .internalNormTypes;
                                let ind = index
                              "
                            >
                              <span *ngIf="ind > 0">,</span>
                              <span>
                                {{ item?.name?.zh_CN }}
                              </span>
                            </ng-container>
                          </div>
                          <div
                            *ngIf="
                              data.generalComment.orgaztionSinkingAnalysis
                                ?.length > 0
                            "
                            class="tooltipMargin"
                          >
                            组织下沉分析对比：
                            <ng-container
                              *ngFor="
                                let item of data.generalComment
                                  .orgaztionSinkingAnalysis;
                                let ind = index
                              "
                            >
                              <span *ngIf="ind > 0">,</span>
                              <span>
                                {{ item?.name?.zh_CN }}
                              </span>
                            </ng-container>
                          </div>
                          <div
                            *ngIf="
                              data.generalComment.prismaHistoryDatas.length > 0
                            "
                            class="tooltipMargin"
                          >
                            历史对比：
                            <ng-container
                              *ngFor="
                                let item of data.generalComment
                                  .prismaHistoryDatas;
                                let ind = index
                              "
                            >
                              <span *ngIf="ind > 0">,</span>
                              <span>
                                {{ item?.name?.zh_CN }}
                              </span>
                            </ng-container>
                          </div>
                        </ng-template>

                        <div>
                          组织：
                          <ng-container
                            *ngFor="
                              let item of data.generalComment.organizations;
                              let ind = index
                            "
                          >
                            <span *ngIf="ind > 0">,</span>
                            <span>
                              {{ item?.name?.zh_CN }}
                            </span>
                          </ng-container>
                        </div>

                        <div
                          *ngFor="let demo of data.generalComment.demographics"
                        >
                          {{ demo?.name?.zh_CN }}：
                          <ng-container
                            *ngFor="let item of demo.children; let ind = index"
                          >
                            <span *ngIf="ind > 0">,</span>
                            <span>
                              {{ item?.name?.zh_CN }}
                            </span>
                          </ng-container>
                        </div>

                        <div *ngIf="data.generalComment.norms?.length > 0">
                          外部常模：
                          <ng-container
                            *ngFor="
                              let item of data.generalComment.norms;
                              let ind = index
                            "
                          >
                            <span *ngIf="ind > 0">,</span>
                            <span>
                              {{ item?.name?.zh_CN }}
                            </span>
                          </ng-container>
                        </div>

                        <div
                          *ngIf="
                            data.generalComment.internalNormTypes.length > 0
                          "
                        >
                          内部常模：
                          <ng-container
                            *ngFor="
                              let item of data.generalComment.internalNormTypes;
                              let ind = index
                            "
                          >
                            <span *ngIf="ind > 0">,</span>
                            <span>
                              {{ item?.name?.zh_CN }}
                            </span>
                          </ng-container>
                        </div>
                        <div
                          *ngIf="
                            data.generalComment.orgaztionSinkingAnalysis
                              ?.length > 0
                          "
                        >
                          组织下沉分析对比：
                          <ng-container
                            *ngFor="
                              let item of data.generalComment
                                .orgaztionSinkingAnalysis;
                              let ind = index
                            "
                          >
                            <span *ngIf="ind > 0">,</span>
                            <span>
                              {{ item?.name?.zh_CN }}
                            </span>
                          </ng-container>
                        </div>
                        <div
                          *ngIf="
                            data.generalComment.prismaHistoryDatas.length > 0
                          "
                        >
                          历史对比：
                          <ng-container
                            *ngFor="
                              let item of data.generalComment
                                .prismaHistoryDatas;
                              let ind = index
                            "
                          >
                            <span *ngIf="ind > 0">,</span>
                            <span>
                              {{ item?.name?.zh_CN }}
                            </span>
                          </ng-container>
                        </div>
                      </div>

                      <button
                        class="iconbtn"
                        nz-button
                        nzType="default"
                        nzValue="small"
                        (click)="copy(data.id)"
                        nzTooltipTitle="复制内容"
                        nzTooltipPlacement="topLeft"
                        nz-tooltip
                      >
                        <i nz-icon nzType="copy" nzTheme="outline"></i>
                      </button>
                    </div>
                  </div>
                  <!-- 其他 -->
                  <p
                    *ngIf="
                      col.type !== 'date' &&
                      col.attrName !== 'projectName' &&
                      col.attrName !== 'reportName' &&
                      col.attrName !== 'generalComment'
                    "
                  >
                    {{
                      col.attrName1
                        ? data[col.attrName][col.attrName1]
                        : data[col.attrName]
                    }}
                  </p>
                </td>
              </ng-container>
              <!-- 操作 -->
              <td nzWidth="220px" nzRight="0px">
                <div class="groupFile">
                  <div class="view-report">
                    <ng-container
                      *ngFor="let lan of data.reportListFileVo.reportLanguages"
                    >
                      <!-- <span class="title">
                        {{lan === 'zh_CN' ? '中文报告' :'英文报告'}}
                      </span> -->

                      <div
                        (click)="tdClick(data.id, data, data.fileMap[lan], lan)"
                        style="display: flex; flex-direction: column; justify-content: flex-start; align-items: flex-start;"
                      >
                        <div
                          *ngIf="data.fileMap[lan] as file"
                          style="white-space: nowrap; display: flex; align-items: center;"
                        >
                          <ng-container
                            *ngIf="file.fileUrl || file.url; else elseTemplate"
                          >
                            <!-- 查看 prisma不显示 -->
                            <a
                              *ngIf="
                                file.url &&
                                data.status !== 'UPLOAD_REPORT' &&
                                file.fileType == 'PDF' &&
                                lan === 'zh_CN'
                              "
                              [href]="file.url + '&token=' + getToken()"
                              target="_blank"
                            >
                              <span style="word-break: keep-all;"
                                >查看报告&nbsp;</span
                              >
                            </a>
                          </ng-container>
                          <ng-template #elseTemplate>
                            <span
                              *ngIf="data.status === 'CREATE_REPORT_SUCCESS'"
                              class="inprogress"
                              >{{ "生成成功" }}</span
                            >
                            <span
                              *ngIf="data.status === 'INIT'"
                              class="inprogress"
                              >{{ "生成中" }}</span
                            >
                            <span
                              *ngIf="data.status === 'CREATE_REPORT_FAIL'"
                              class="inprogress"
                              >{{ "生成失败" }}</span
                            >
                            &nbsp;
                          </ng-template>
                          <nz-upload
                            *ngIf="
                              permissionService.isPermissionOrSag(
                                'SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:UPLOAD'
                              )
                            "
                            [nzCustomRequest]="customReq"
                            [nzShowUploadList]="false"
                          >
                            <button
                              nz-button
                              nzSize="small"
                              nzType="link"
                              [nzLoading]="data.fileMap[lan]?.isUploading"
                            >
                              <span
                                >上传({{ lan === "zh_CN" ? "中" : "英" }})</span
                              >
                            </button>
                            &nbsp;
                          </nz-upload>
                        </div>

                        <!-- *ngIf="permission==='true'" -->
                        <div style="white-space: nowrap;">
                          <!-- {{data.fileMap[lan] | json}} -->

                          <!-- <button nz-button nzSize="small" nzType="link" (click)="redoGroupPdf(data.id, data)"
                            [nzLoading]="data.isCreating">
                            <span>重新生成</span>
                          </button> -->

                          <!-- <button nz-button nzSize="small" nzType="link" (click)="exportGroupAnswerDatas(data)"
                            [nzLoading]="data.answerDownloading">
                            <span>原始填答</span>
                          </button> -->
                        </div>
                      </div>
                    </ng-container>
                  </div>

                  <div style="width: 100%; ">
                    <button
                      *ngIf="
                        permissionService.isPermissionOrSag(
                          'SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:SETTING'
                        ) &&
                        data.surveyType === 'EMPLOYEE_ENGAGEMENT' &&
                        data.reportType !== 'CULTURE_INVESTIGATION_RESEARCH' &&
                        data.reportType !== 'VIVO_INVESTIGATION_RESEARCH_CUSTOM'
                      "
                      nz-button
                      nzSize="small"
                      nzType="link"
                      (click)="openPrismaSet(data)"
                    >
                      <span>报告设置</span>
                    </button>
                    <ng-container
                      *ngIf="
                        !permission &&
                        knxFunctionPermissionService.has(
                          'SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:HIDE'
                        )
                      "
                    >
                      <a
                        nz-popconfirm
                        nzPopconfirmTitle="确定删除这份报告?"
                        nzPopconfirmPlacement="bottom"
                        (nzOnConfirm)="closedreport(data)"
                        (nzOnCancel)="cancelreport()"
                      >
                        删除&nbsp;
                      </a>
                    </ng-container>
                    <button
                      *ngIf="permission && data.isShow"
                      nz-button
                      nzSize="small"
                      nzType="link"
                      (click)="closedreport(data)"
                    >
                      隐藏&nbsp;
                    </button>
                    <button
                      *ngIf="permission && !data.isShow"
                      nz-button
                      nzSize="small"
                      nzType="link"
                      (click)="closedreport(data)"
                    >
                      恢复显示&nbsp;
                    </button>
                    <nz-upload
                      *ngIf="
                        permissionService.isPermissionOrSag(
                          'SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:IMPORT_WORD'
                        ) && data.prismaDataId
                      "
                      [nzCustomRequest]="customReqAnalysisFactor"
                      [nzShowUploadList]="false"
                    >
                      <button
                        nz-button
                        nzSize="small"
                        nzType="link"
                        (click)="nzCustomRequestChange($event, data)"
                      >
                        导入词云&nbsp;
                      </button>
                    </nz-upload>
                    <button
                      *ngIf="
                        permissionService.isPermissionOrSag(
                          'SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:EXPORT_WORD'
                        ) && data.prismaDataId
                      "
                      nz-button
                      nzSize="small"
                      nzType="link"
                      (click)="exportWord(data)"
                    >
                      导出词云&nbsp;
                    </button>
                    <button
                      class="operator-btn"
                      nz-tooltip
                      nzTooltipPlacement="topRight"
                      nzOverlayClassName="create-tooltip"
                      [nzTooltipTitle]="'创建人：' + data.createBy"
                    >
                      {{ data.createBy }}
                    </button>
                    <!-- <i *ngIf="data.status === 'CREATE_REPORT_FAIL'" nz-icon nz-popover [nzPopoverTitle]="null" [nzPopoverContent]="data.failReason" nzType="exclamation-circle" style="margin-left: 4px; cursor: pointer; color: red;" nzTheme="outline"></i> -->
                  </div>
                </div>
              </td>
            </ng-container>
          </tr>
        </tbody>
      </nz-table>

      <!-- 对比报告 table -->
      <nz-table
        *ngIf="!isLoading && selectHeader.id === 3"
        class="report-table"
        #basicTable
        [nzData]="listOfData"
        [nzFrontPagination]="false"
        nzBordered
        [nzScroll]="{ x: '1200px' }"
        [ngClass]="{ 'table-empty': !(listOfData && listOfData.length) }"
      >
        <thead>
          <tr>
            <!-- 动态列 -->
            <ng-container *ngFor="let colDto of comparisonColumnList">
              <th
                *ngIf="colDto.isSelected"
                [nzWidth]="colDto.width"
                [nzLeft]="colDto.left"
                [nzRight]="colDto.right"
              >
                {{ colDto.title }}
              </th>
            </ng-container>
            <!-- 操作 -->
            <th nzWidth="240px" nzRight="0px">
              <!-- 列设置模板 -->
              <ng-template #contentTemplateColComparison>
                <div style="margin:0; padding: 0; ">
                  <app-column-set
                    (dragged)="comparisonColumnSortChanged($event)"
                    [columnList]="comparisonColumnList"
                  ></app-column-set>
                </div>
              </ng-template>
              <div
                style="width:100%; display: flex; justify-content: flex-start;"
              >
                <div
                  style="margin-left: 4px; display: flex; align-items: center;"
                  class="personNum"
                  nz-popover
                  nzPopoverPlacement="bottom"
                  nzPopoverTitle="可配置列"
                  [nzPopoverContent]="contentTemplateColComparison"
                >
                  <i nz-icon nzType="setting" nzTheme="outline"></i>
                </div>
                <span>&nbsp;&nbsp;操作</span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of basicTable.data">
            <ng-container *ngFor="let col of comparisonColumnList">
              <td
                *ngIf="col.isSelected"
                [nzLeft]="col.left"
                [nzRight]="col.right"
              >
                <!-- 活动名称 -->
                <div
                  *ngIf="col.attrName == 'projectName'"
                  nz-tooltip
                  nzPlacement="topLeft"
                  [nzTooltipTitle]="data.projectName?.zh_CN"
                >
                  {{ data.projectName?.zh_CN }}
                </div>

                <!-- 日期类型 -->
                <p *ngIf="col.type === 'date'">
                  {{ data[col.attrName] | date: "yyyy-MM-dd HH:mm:ss" }}
                </p>
                <!-- 报告名称 -->
                <div
                  *ngIf="col.attrName == 'reportName'"
                  nz-tooltip
                  nzPlacement="topLeft"
                  [nzTooltipTitle]="data.reportName?.zh_CN"
                >
                  {{ data.reportName?.zh_CN }}
                </div>
                <!-- 对比明细 -->
                <div
                  style="display: flex; flex-wrap: wrap;max-width: 400px;"
                  *ngIf="col.attrName == 'personOrganizationIdNames'"
                >
                  <div
                    *ngFor="
                      let item of data.personOrganizationIdNames;
                      last as isLast
                    "
                  >
                    {{ item.name }}<span *ngIf="!isLast">，</span>
                  </div>
                </div>
                <!-- 其他 -->
                <p
                  *ngIf="
                    col.type !== 'date' &&
                    col.attrName !== 'projectName' &&
                    col.attrName !== 'reportName' &&
                    col.attrName !== 'personOrganizationIdNames'
                  "
                >
                  {{
                    col.attrName1
                      ? data[col.attrName][col.attrName1]
                      : data[col.attrName]
                  }}
                </p>
              </td>
            </ng-container>
            <!-- 操作栏 -->
            <td nzWidth="240px" nzRight="0px" class="comparison-operation">
              <button
                *ngIf="data.status === 'SUCCESS'"
                nz-button
                nzSize="small"
                nzType="link"
                (click)="showCompareDialog(data)"
              >
                <span>对比报告</span>
              </button>
              <span
                style="padding-left: 10px;"
                *ngIf="data.status !== 'SUCCESS'"
              >
                生成中
              </span>
              <ng-container *ngIf="!permission">
                <a
                  nz-popconfirm
                  nzPopconfirmTitle="确定删除这份报告?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="closedreport(data, true)"
                  (nzOnCancel)="cancelreport()"
                >
                  删除&nbsp;
                </a>
              </ng-container>
              <button
                *ngIf="permission && data.isShow"
                nz-button
                nzSize="small"
                nzType="link"
                (click)="closedreport(data, true)"
              >
                隐藏&nbsp;
              </button>
              <button
                *ngIf="permission && !data.isShow"
                nz-button
                nzSize="small"
                nzType="link"
                (click)="closedreport(data, true)"
              >
                恢复显示&nbsp;
              </button>
              <button
                class="operator-btn"
                nz-tooltip
                nzTooltipPlacement="topRight"
                nzOverlayClassName="create-tooltip"
                [nzTooltipTitle]="'创建人：' + data.createBy"
                style="margin-left: 8px;"
              >
                {{ data.createBy }}
              </button>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>

    <!-- 分页控件 -->
    <div class="page">
      <ng-container>
        <div class="action" *ngIf="selectHeader?.id !== 3">
          <span style="white-space: nowrap;">已选择</span>
          <span
            class="personNum"
            nz-popover
            nzPopoverTitle="已选择人员"
            [nzPopoverContent]="contentTemplate"
          >
            {{ getSelectCount() }}
          </span>
          <ng-template #contentTemplate>
            <div style="max-height: 600px;" class="scroll">
              <div style="height: 30px;" *ngFor="let val3 of getSelectNames()">
                <span style="color: #f56a00;">&#8730;</span>
                <span style="margin-left: 10px;">{{ val3.name }}</span>
              </div>
            </div>
          </ng-template>
          <button
            nz-button
            nzSize="small"
            nzType="link"
            (click)="openDownload()"
            *ngIf="isShowDownLoad()"
          >
            下载
          </button>
          <button
            nz-button
            nzSize="small"
            nzType="link"
            [nzLoading]="sendLoading"
            (click)="jumpToSend()"
            *ngIf="isShowBatchSend() && isDisplayMail"
          >
            批量发送
          </button>
          <button
            nz-button
            nzSize="small"
            nzType="link"
            (click)="openDownload()"
            *ngIf="isShowGroupDownLoad()"
          >
            下载
          </button>
          <button
            nz-button
            nzSize="small"
            nzType="link"
            (click)="jumpToSend()"
            *ngIf="isShowGroupBatchSend() && isDisplayMail"
            [nzLoading]="sendLoading"
          >
            批量发送
          </button>
          <!-- 重新生成-标准 -->
          <ng-container
            *ngIf="
              permissionService.isPermissionOrSag(
                'SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:REGENERATE'
              ) && selectHeader.id === 1
            "
          >
            <button
              nz-button
              nzSize="small"
              nzType="link"
              [nzLoading]="loading.recreate"
              nz-popconfirm
              nzPopconfirmTitle="您确定要重新生成标准报告吗?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="confirmReport()"
              (nzOnCancel)="cancelReport()"
            >
              重新生成
            </button>
          </ng-container>

          <!-- 重新生成-团队 -->
          <ng-container
            *ngIf="
              permissionService.isPermissionOrSag(
                'SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:REGENERATE'
              ) && selectHeader.id === 2
            "
          >
            <button
              nz-button
              nzSize="small"
              nzType="link"
              [nzLoading]="loading.recreate"
              nz-popconfirm
              nzPopconfirmTitle="您确定要重新生成团队报告吗?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="confirmReport()"
              (nzOnCancel)="cancelReport()"
            >
              重新生成
            </button>
          </ng-container>

          <ng-container *ngIf="selectHeader?.id === 1">
            <button
              nz-button
              nzSize="small"
              nzType="link"
              (click)="openUnionCreate()"
              *knxFunctionPermission="
                'SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:CREATE_GROUP_REPORT'
              "
            >
              创建团队报告
            </button>
          </ng-container>
        </div>
      </ng-container>

      <nz-pagination
        nzShowQuickJumper
        [(nzPageIndex)]="currentPage"
        [(nzPageSize)]="pageSize"
        [nzTotal]="totalCount"
        (nzPageIndexChange)="loadData(currentPage)"
        style="margin-right: 0;"
      >
      </nz-pagination>
    </div>
  </div>
  <div *ngIf="isVisible">
    <app-compare-config
      [isVisible]="isVisible"
      [chartId]="chartId"
      (getClose)="getClose($event)"
    ></app-compare-config>
  </div>
</div>

<!-- 余额不足提示弹窗 -->
<nz-modal
  [(nzVisible)]="tips_vib"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  (nzOnCancel)="handleCancel()"
  class="tips2"
  [nzStyle]="{ top: '300px', 'border-radius': '8px' }"
  nzWidth="480"
>
  <!-- 弹窗内容模板 -->
  <ng-template #modalContent>
    <div class="info">
      <div class="tips_icon">
        <img src="./assets/images/tips_err.png" alt="错误提示图标" />
      </div>
      <div class="tips_title">余额不足</div>
      <div class="tips_text">
        抱歉，因为您的K米余额不足，所以最新测评报告不在列表中，
        <br />请联系销售顾问进行充值，即可查看。
      </div>
    </div>
  </ng-template>

  <!-- 弹窗底部按钮模板 -->
  <ng-template #modalFooter>
    <div style="display: flex; justify-content: center;align-items: center;">
      <button nz-button class="iptBtn" (click)="handleCancel()">
        <span>确定</span>
      </button>
    </div>
  </ng-template>
</nz-modal>

<!-- 新手引导蒙层 -->
<div class="mock_div" *ngIf="showmock">
  <!-- 背景遮罩 -->
  <div class="bg_ul" *ngIf="showmock"></div>

  <!-- 第一步引导 - 批量下载 -->
  <ul class="img_ul" *ngIf="step1">
    <img src="./assets/images/report_1.png" alt="批量下载引导图" />
    <div style="display: flex;justify-content: center;">
      <div>
        <p>勾选人员或组织，批量下载</p>
        <div class="btn_div">
          <div class="left_d" (click)="jumprun()">跳过</div>
          <div class="right_d" (click)="next1()">下一步</div>
        </div>
      </div>
    </div>
  </ul>

  <!-- 第二步引导 - 已选查看 -->
  <ul class="img_ul_1" *ngIf="step2">
    <div>
      <div class="btn_div">
        <div class="left_d" (click)="jumprun()">跳过</div>
        <div class="right_d" (click)="next2()">下一步</div>
      </div>
      <p style="margin-top: 20px;">点击已选，查看已选人员或组织</p>
    </div>
    <img src="./assets/images/report_2.png" alt="已选查看引导图" />
  </ul>

  <!-- 第三步引导 - 创建团队报告 -->
  <ul class="img_ul_2" *ngIf="step3">
    <div style="display: flex;justify-content: center;">
      <div>
        <div class="btn_div">
          <div class="left_d" (click)="jumprun()">跳过</div>
          <div class="right_d" (click)="next3()">下一步</div>
        </div>
        <p style="margin-top: 20px;">点击创建团队报告，进行团队比较</p>
      </div>
    </div>
    <img src="./assets/images/report_3.png" alt="创建团队报告引导图" />
  </ul>

  <!-- 第四步引导 - 查看报告 -->
  <ul class="img_ul_3" *ngIf="step4">
    <img src="./assets/images/report_4.png" alt="查看报告引导图" />
    <div style="display: flex;justify-content: flex-end;width: 80%;">
      <div>
        <p>点击查看报告，进行在线浏览</p>
        <div class="btn_div">
          <div class="left_d" (click)="jumprun()">跳过</div>
          <div class="right_d" (click)="next4()">下一步</div>
        </div>
      </div>
    </div>
  </ul>

  <!-- 第五步引导 - 创建对比报告 -->
  <ul class="img_ul_4" *ngIf="step5">
    <img src="./assets/images/report_5.png" alt="创建对比报告引导图" />
    <div style="display: flex;justify-content:flex-end;margin-top: -280px;">
      <div>
        <p>点击创建对比报告，选择要比较的对象</p>
        <div class="btn_div">
          <div class="left_d" (click)="jumprun()">跳过</div>
          <div class="right_d" (click)="next5()">我知道了</div>
        </div>
      </div>
    </div>
  </ul>
</div>

<!-- 生成失败提示模板 -->
<ng-template #customNotification let-tip="data">
  <div class="custom-top">
    <div>
      <i nz-icon nzType="exclamation-circle" nzTheme="fill"></i>
    </div>
    <div>
      <h3>提示</h3>
      <p>
        您有生成失败的{{ tip.name }}报告，点击
        <a (click)="getFilterFail(tip.type)">查看详情</a>
      </p>
    </div>
  </div>
  <div class="custom-bottom">
    <button
      nz-button
      nzType="default"
      nzSize="small"
      (click)="closeBuildFailNotification(false, tip.type)"
    >
      不再提示
    </button>
    <button
      nz-button
      nzType="primary"
      nzSize="small"
      (click)="closeBuildFailNotification(true, tip.type)"
    >
      我知道了
    </button>
  </div>
</ng-template>

<!-- 侧边栏-在线视频教学 -->
<nz-drawer
  [nzVisible]="showVideoBox"
  nzPlacement="right"
  nzTitle="在线视频教学"
  (nzOnClose)="closeModal()"
  [nzWidth]="440"
  nzWrapClassName="report-home-right-drawer"
>
  <section class="videoBox">
    <p class="videoBox-text">请选择需要观看的报告类型</p>

    <!-- 报告类型选择 -->
    <nz-select
      class="videoBox-select"
      [(ngModel)]="isGroup"
      nzPlaceHolder="请选择"
    >
      <nz-option [nzValue]="false" nzLabel="个人报告"></nz-option>
      <nz-option [nzValue]="true" nzLabel="团队报告"></nz-option>
    </nz-select>

    <!-- 报告分类选择 -->
    <nz-radio-group [(ngModel)]="reportType" class="videoBox-group">
      <label nz-radio *ngFor="let data of reportTypeList" [nzValue]="data.name">
        <span
          nz-tooltip
          nzTooltipPlacement="topLeft"
          [nzTooltipTitle]="data.description"
        >
          {{ data.description }}
        </span>
      </label>
    </nz-radio-group>

    <!-- 底部按钮 -->
    <footer class="videoBox-footer">
      <button nz-button (click)="clearBox()">
        清空
      </button>
      <button nz-button nzType="primary" (click)="confirm()">
        确认
      </button>
    </footer>
  </section>
</nz-drawer>

<!-- 侧边栏-专家1V1解读 -->
<nz-drawer
  [nzVisible]="showOneBox"
  nzPlacement="right"
  nzTitle="专家1V1解读"
  (nzOnClose)="closeModal()"
  [nzWidth]="585"
  nzWrapClassName="report-home-right-drawer"
>
  <section class="oneBox">
    <!-- 预约表单 -->
    <ng-container *ngIf="!showSuccess">
      <p class="oneBox-text">请选择需要观看的报告类型</p>

      <!-- 报告类型和解读方式选择 -->
      <div nz-row nzGutter="16">
        <div nz-col class="gutter-row" nzSpan="12">
          <nz-select
            class="oneBox-select"
            [(ngModel)]="isGroup"
            nzPlaceHolder="请选择"
          >
            <nz-option [nzValue]="false" nzLabel="个人报告"></nz-option>
            <nz-option [nzValue]="true" nzLabel="团队报告"></nz-option>
          </nz-select>
        </div>
        <div nz-col class="gutter-row" nzSpan="12">
          <nz-select
            class="oneBox-select"
            [(ngModel)]="reportHumanInterpretation"
            (ngModelChange)="changePer($event)"
            nzPlaceHolder="请选择"
          >
            <nz-option nzValue="TEL" nzLabel="电话沟通"></nz-option>
            <nz-option nzValue="SCENE" nzLabel="现场解读"></nz-option>
          </nz-select>
        </div>
      </div>

      <!-- 报告分类选择 -->
      <nz-radio-group [(ngModel)]="reportType" class="oneBox-group">
        <label
          nz-radio
          *ngFor="let data of reportTypeList"
          [nzValue]="data.name"
        >
          <span
            nz-tooltip
            nzTooltipPlacement="topLeft"
            [nzTooltipTitle]="data.description"
          >
            {{ data.description }}
          </span>
        </label>
      </nz-radio-group>

      <!-- 联系方式输入 -->
      <p class="oneBox-text">请留下您的联系方式</p>
      <input
        class="oneBox-input"
        nz-input
        placeholder="请输入手机号"
        [(ngModel)]="telNum"
      />
      <p class="oneBox-tips">
        预约成功后，我们将在2个工作日内与您联系，请保持电话畅通
      </p>

      <!-- 底部按钮和费用展示 -->
      <footer class="oneBox-footer">
        <div class="oneBox-footer-left">
          共计: <span>{{ balance }}</span
          >K米，K米将自动从您的账户中扣除
        </div>
        <div class="oneBox-footer-right">
          <button nz-button (click)="clearBox()">
            清空
          </button>
          <button
            nz-button
            nzType="primary"
            (click)="confirmOne()"
            [nzLoading]="modalLoading"
          >
            确认
          </button>
        </div>
      </footer>
    </ng-container>

    <!-- 预约成功提示 -->
    <ng-container *ngIf="showSuccess">
      <div class="oneBox-empty">
        <app-empty
          text="预约成功"
          imgUrl="assets/images/report-home/success.png"
        ></app-empty>
      </div>
    </ng-container>
  </section>
</nz-drawer>
