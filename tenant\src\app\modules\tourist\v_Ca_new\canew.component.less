@text-color: #17314c;
.content {
  width: 100%;
}
.s1 {
  width: 100%;
  background-color: #ffffff;
  & > div {
    margin: 0 auto;
    padding: 60px 0;
    display: flex;
  }
  .s1-l {
    flex: 2;
    padding-right: 180px;
  }
  .s1-r {
    flex: 1;
  }
  .bg_img{
    height: 433px;
    background: url(../../../../assets/images/v_newtip_0.png) no-repeat center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  h5 {
    font-size: 30px;
    line-height: 42px;
    margin-bottom: 25px;
    span {
      font-size: 12px;
      color: #409eff;
      border-radius: 14px;
      padding: 2px 10px;
      margin-left: 30px;
      background-color: rgba(64, 158, 255, 0.1);
    }
  }
  p {
    font-size: 14px;
    line-height: 24px;
    margin-bottom: 80px;
  }
  .btn {
    width: 160px;
    height: 38px;
    line-height: 38px;
    background: linear-gradient(
      90deg,
      rgba(38, 208, 241, 1) 0%,
      rgba(64, 158, 255, 1) 100%
    );
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
  }
}
.s2 {
  width: 100%;
  background-color: #f5f6fa;
  text-align: center;
  & > div {
    margin: 0 auto;
    padding: 60px 0;
  }
  h5 {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin: 40px 0 50px;
  }
}
.s3 {
  width: 100%;
  background-color: #ffffff;
  padding-top: 40px;
  .s3-main {
    margin: 0 auto;
    padding: 60px 0;
    display: flex;
  }
  h5 {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 60px 0 60px;
  }
  .s3-l {
    flex: 1;
    font-size: 16px;
    li {
      position: relative;
      line-height: 96px;
      div {
        width: 180px;
        display: inline-block;
      }
      span {
        font-size: 16px;
        color: #409eff;
        background-color: #f1f8ff;
        border-radius: 46px;
        padding: 10px 25px;
      }
      &:not(:last-child)::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        height: 1px;
        width: 60%;
        background: linear-gradient(to right, #ffffff, #ececec, #ffffff);
      }
    }
  }
  .s3-r {
    flex: 1;
    img {
      margin-top: 30px;
    }
  }
}
.s4 {
  width: 100%;
  background-color: #f5f6fa;
  padding-bottom: 98px;
  .s4-main {
    display: flex;
    flex-direction: column;
    // li{flex: 1;margin-right:17px;
    //     &:last-child{margin-right:0;}
    //     &:nth-child(1){background: url(../../../../assets/images/t_mca_s4-1.png) no-repeat top center;padding:260px 50px 30px 60px;}
    //     &:nth-child(2){background: url(../../../../assets/images/t_mca_s4-2.png) no-repeat top center;padding:260px 50px 30px 60px;}
    //     &:nth-child(3){background: url(../../../../assets/images/t_mca_s4-3.png) no-repeat top center;padding:260px 50px 30px 60px;}
    // }
  }
  h5 {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 76px 0 56px;
  }
  h3 {
    font-size: 20px;
    font-weight: 500;
    color: #409eff;
  }
  .mbt30 {
    margin-bottom: 30px;
  }
}
.s5 {
  width: 100%;
  background-color: #ffffff;
  .s5-main {
    margin: 0 auto;
    padding: 60px 0;
    display: flex;
  }
  h5 {
    font-size: 24px;
    font-weight: bold;
    padding: 50px 0 40px;
    text-align: center;
  }
  .s5-r {
    flex: 1;
    font-size: 16px;
    padding: 10%;
    p {
      position: relative;
      font-size: 16px;
      line-height: 55px;
      padding-left: 20px;
    }
  }
  .s5-l {
    width: 60%;
    img {
      margin-top: 30px;
    }
    text-align: center;
  }
  .btn {
    margin: 20px 0;
    button {
      width: 160px;
      height: 38px;
      line-height: 38px;
      background: linear-gradient(
        90deg,
        rgba(38, 208, 241, 1) 0%,
        rgba(64, 158, 255, 1) 100%
      );
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
    }
  }
}
