import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { NzMessageService, UploadXHRArgs } from "ng-zorro-antd";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { ActivatedRoute } from "@angular/router";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { HttpEvent } from "@angular/common/http";

@Component({
  selector: "app-topic-skip",
  templateUrl: "./topic-skip.component.html",
  styleUrls: ["./topic-skip.component.less"],
})
export class TopicSkipComponent implements OnInit {
  @Output() loadData = new EventEmitter<string>();
  @Input() questionnaireId: string; //问卷id
  @Input() lan: string; //中英文

  questionType: string = null; // 题型
  questionStr: string = ""; // 模糊搜索 val
  resultType: string = null; // 结果 题型
  resultStr: string = ""; // 结果 模糊搜索 val
  questionList: any[] = [];
  resultList: any[] = [];
  parentQuestionId: ""; // 条件题id
  questionId: ""; // 结果题id
  tplModalButtonLoading: boolean = false; // 结果题id
  surveyStandardOptions: any[] = []; // 条件提选项id
  popoverVisible: boolean = false; // 条件提选项id
  penetrationList: any[] = []; // 关联列表
  answerType: string = "FINAL";
  reportType: string = null; //判断类型
  questionTypeList = [
    { label: "单选", value: "SINGLE" },
    { label: "量表", value: "SCALE" },
    { label: "评价", value: "EVALUATION" },
    // { label: "开放", value: "ESSAY_QUESTION" },
    { label: "多选式开放", value: "MULTIPLE_CHOICE_ESSAY_QUESTION" },
    // { label: "滑块", value: "PROPORTION" },
    // { label: "多级比重", value: "PROPORTION_MULTIPLE" },
  ];
  resultTypeList = [
    { label: "单选", value: "SINGLE" },
    { label: "量表", value: "SCALE" },
    { label: "评价", value: "EVALUATION" },
    { label: "开放", value: "ESSAY_QUESTION" },
    { label: "多选式开放", value: "MULTIPLE_CHOICE_ESSAY_QUESTION" },
    { label: "滑块", value: "PROPORTION" },
    { label: "多级比重", value: "PROPORTION_MULTIPLE" },
  ];
  isSpinningQuestion = false;
  isSpinningResult = false;
  constructor(
    private api: ProjectManageService,
    private msg: NzMessageService,
    private routeInfo: ActivatedRoute,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    console.log(111111111111, this.questionnaireId, this.lan);
    this.reportType = this.routeInfo.snapshot.queryParams.reportType;
    // if (this.reportType !== "INVESTIGATION_RESEARCH") {
    //   this.resultTypeList = [
    //     ...this.resultTypeList,
    //     { label: "滑块", value: "PROPORTION" },
    //     { label: "多级比重", value: "PROPORTION_MULTIPLE" },
    //   ];
    // }
    this.getData();
  }

  // 清空所有关联
  confirmDelAllSkip() {
    this.api.deleteAllSurveySkip(this.questionnaireId).subscribe((res) => {
      // 删除所有关联
      if (res.result.code === 0) {
        this.getShowSkipCombination();
        this.getlistByTypesAndName();
        this.getlistByTypesAndNameRes();
        this.loadData.next();
        this.msg.success("删除成功");
        this.popoverVisible = false;
      }
    });
  }

  delOne(ques) {
    console.log(ques);
    // 删除某一关联
    const surveyStandardOptions = ques.surveyStandardOptions.map((val) => ({
      id: val.id,
    }));
    const skipQuestionVos = ques.skipQuestionVos.map((val) => ({
      questionId: val.questionId,
    }));
    // let arr = [];
    // if (ques.surveyStandardOptions.length !== 0) {
    //   ques.surveyStandardOptions.forEach((item) => {
    //     arr.push({ id: item.id });
    //   });
    // }
    const params = {
      parentQuestionId: ques.parentQuestionId,
      skipQuestionVos: skipQuestionVos,
      surveyStandardOptions: surveyStandardOptions,
      answerType: ques.answerType,
    };
    console.log(params);
    this.api.deleteSurveySkip(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.getShowSkipCombination();
        this.loadData.next();
        this.msg.success("删除成功");
      }
    });
  }

  closeAssociation() {
    this.popoverVisible = false;
  }

  getShowSkipCombination() {
    console.log("获取结果题列表");
    // 获取结果题列表
    this.api.showSkipCombination(this.questionnaireId).subscribe((res) => {
      if (res.result.code === 0) {
        this.penetrationList = res.data;
      }
    });
  }

  getlistByTypesAndName() {
    this.isSpinningQuestion = true;
    console.log("获取条件列表");
    // 获取条件列表
    this.parentQuestionId = "";
    this.surveyStandardOptions = [];
    let params = {
      questionTypes: !this.questionType
        ? this.questionTypeList.map((item) => item.value)
        : [this.questionType],
      searchField: this.questionStr,
      questionnaireId: this.questionnaireId,
      questionOperationType: "SKIP",
      questionAssociationType: 'CONDITION', // 穿透题类型-条件题
    };
    this.api.listByTypesAndName(params).subscribe((res) => {
      if (res.result.code === 0) {
        res.data.forEach((item) => {
          item.checked = false;
          item.selectFlag = false;
          item.indeterminate = false;
          item.options.options.checked = false;
        });
        this.questionList = res.data;
      }
      this.isSpinningQuestion = false;
    });
  }
  getlistByTypesAndNameRes() {
    this.isSpinningResult = true;
    console.log("获取结果列表", this.resultType);
    // 获取结果列表
    this.questionId = "";
    let params = {
      questionTypes: !this.resultType
        ? this.resultTypeList.map((item) => item.value)
        : [this.resultType],
      searchField: this.resultStr,
      questionnaireId: this.questionnaireId,
      questionOperationType: "SKIP",
      questionAssociationType: 'RESULT', // 穿透题类型-结果题
    };
    this.api.listByTypesAndName(params).subscribe((res) => {
      if (res.result.code === 0) {
        if (res.data.length !== 0) {
          res.data.forEach((item) => {
            item.checked = false;
          });
        }
        this.resultList = res.data;
      }
      this.isSpinningResult = false;
    });
  }

  getData() {
    this.getlistByTypesAndName();
    this.getlistByTypesAndNameRes();
    this.getShowSkipCombination();
  }
  getInitData() {
    this.questionType = null;
    this.questionStr = "";
    this.resultType = null;
    this.resultStr = "";
    this.getData();
  }

  chooseQues(e, id) {
    if (e) {
      this.questionList.forEach((item) => {
        // 全选子选项
        if (item.id === this.parentQuestionId) {
          // 取消其他题目子选项
          this.surveyStandardOptions = [];
          item.checked = false;
          item.indeterminate = false;
          item.options.options.forEach((itm) => {
            itm.checked = false;
          });
        }
        if (item.id === id) {
          item.indeterminate = false;
          item.options.options.forEach((itm) => {
            itm.checked = true;
            this.surveyStandardOptions.push({ id: itm.id });
          });
        }
      });
      this.parentQuestionId = id;
      // 需处理结果题中的选中，不能为当前的条件题
      this.resultList.forEach((item) => {
        if (item.id === id) {
          item.checked = false;
        }
      });
    } else {
      this.parentQuestionId = "";
      this.surveyStandardOptions = [];
      this.questionList.forEach((item) => {
        item.options.options.forEach((itm) => {
          itm.checked = false;
        });
      });
    }
  }

  chooseOpt(e, quesId, optId) {
    if (e && (!this.parentQuestionId || this.parentQuestionId === quesId))
      this.parentQuestionId = quesId;
    if (e && this.parentQuestionId !== quesId) {
      this.questionList.forEach((item) => {
        if (quesId === item.id) {
          item.options.options.forEach((itm) => {
            if (itm === quesId) {
              itm.checked = true;
            }
          });
        } else {
          item.checked = false;
          item.indeterminate = false;
          item.options.options.forEach((itm) => {
            itm.checked = false;
          });
        }
      });

      this.parentQuestionId = quesId;
    } // 判断选中并且id为当前条件提

    this.questionList.forEach((item) => {
      if (item.id === quesId) {
        if (item.options.options.every((itm) => !itm.checked)) {
          item.checked = false;
          item.indeterminate = false;
        } else if (item.options.options.every((item) => item.checked)) {
          item.checked = true;
          item.indeterminate = false;
        } else {
          item.indeterminate = true;
        }
      }
    });

    if (!e) {
      this.surveyStandardOptions.map((item, index) => {
        if (item.id === optId) this.surveyStandardOptions.splice(index, 1);
      });
      if (this.surveyStandardOptions.length === 0) this.parentQuestionId = "";

      return;
    }

    this.surveyStandardOptions.push({ id: optId });
  }

  chooseRes(e, id, idx) {
    console.log("结果", e, id, this.resultList);
    // this.questionId = id;
    // if (this.resultList.length > 1) {
    //   this.resultList.forEach((item, index) => {
    //     if (idx !== index) item.checked = false;
    //   });
    // }
  }

  reset() {
    this.parentQuestionId = "";
    this.questionId = "";
    this.surveyStandardOptions = [];
    this.answerType = "FINAL";
    this.questionList.forEach((item) => {
      item.checked = false;
      item.indeterminate = false;
      item.options.options.forEach((itm) => {
        itm.checked = false;
      });
    });
    this.resultList.forEach((item) => {
      item.checked = false;
    });
  }

  changeQuestionType(e) {
    // 条件类型
    this.getlistByTypesAndName();
  }
  changeResultType(e) {
    // 结果类型
    this.getlistByTypesAndNameRes();
  }

  searchQuestion() {
    // 条件模糊搜索
    this.getlistByTypesAndName();
  }
  searchResult() {
    // 结果模糊搜索
    this.getlistByTypesAndNameRes();
  }

  selectQuestion(idx) {
    // 下拉Question
    // this.selectId = id
    this.questionList[idx].selectFlag = !this.questionList[idx].selectFlag;
  }

  createQues() {
    // 关联穿透题
    if (!this.parentQuestionId || this.surveyStandardOptions.length === 0) {
      // this.msg.error("请选择条件题选项");
      this.customMsg.open("error", "请选择条件题选项");
      return;
    }
    const resultList = this.resultList.filter((item) => item.checked) || [];
    const resultIds =
      resultList.map((item) => ({
        questionId: item.id,
        // type: item.type,
        // questionName: item.name,
      })) || [];
    if (!resultIds.length) {
      // this.msg.error("请选择结果题");
      this.customMsg.open("error", "请选择结果题");
      return;
    }
    let params = {
      parentQuestionId: this.parentQuestionId,
      skipQuestionVos: resultIds,
      surveyStandardOptions: this.surveyStandardOptions,
      answerType: this.answerType,
    };
    this.tplModalButtonLoading = true;
    this.api.createSurveyStandardSkip(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("关联成功");
        this.parentQuestionId = "";
        this.questionId = "";
        this.surveyStandardOptions = [];
        this.getData();
        this.loadData.next();
      }
      this.tplModalButtonLoading = false;
    });
  }

  /**
   * 穿透题本弹窗
   * 
    tplTitle: TemplateRef<{}>,
    tplContent: TemplateRef<{}>,
    tplFooter: TemplateRef<{}>
   */
  createPenetrationQuestions(): void {
    this.getData();
  }

  closePenetraModal(): void {
    this.questionType = null; // 题型
    this.questionStr = ""; // 模糊搜索 val
    this.resultType = null; // 结果 题型
    this.resultStr = ""; // 结果 模糊搜索 val
    this.parentQuestionId = "";
    this.questionId = "";
    this.surveyStandardOptions = [];
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.api.importSkip(formData, this.questionnaireId).subscribe(
      (event: HttpEvent<any>) => {
        item.onSuccess!();
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入文件成功");
          this.getData();
          this.loadData.next();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  exportR() {
    this.api.exportSkip(this.questionnaireId).subscribe((res) => {
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }

  // 穿透题相关 end
}
