import { Component, OnInit } from '@angular/core';
import * as echarts from 'echarts';
import { HttpClient } from '@angular/common/http';
import "echarts-wordcloud";

@Component(
    {selector: 'app-word-cloud', templateUrl: './word-cloud.component.html', styleUrls: ['./word-cloud.component.less']}
)
export class WordCloudComponent implements OnInit {

    options: any = {};
    myChart2
    colorList 
    constructor(
        private http: HttpClient

    ) {}
    ngOnInit() {
        // this.getBase64('../../../../assets/images/immmg.jpeg')
        this.initWordCloud()
    }

    // async getBase64(imgUrl) {
    //     let self = this
    //     window.URL = window.URL || window.webkitURL
    //     var xhr = new XMLHttpRequest();
    //     xhr.open("get", imgUrl, true);
    //     xhr.responseType = "blob";
    //     xhr.onload = await function(){
    //         if(this.status == 200){
    //             //得到一个blob对象
    //             var blob = this.response;
    //             console.log("blob", blob)
    //             // 至关重要
    //             let oFileReader = new FileReader();
    //             oFileReader.onloadend = function(e){
    //                 // 此处拿到的已经是base64的图片了,可以赋值做相应的处理
    //                 console.log(e.target.result)
    //             }
    //             oFileReader.readAsDataURL(blob);
    //         }
    //     }
    //     xhr.send();
    // }

    initWordCloud() {
        
        const selt = this
        let data = {
            value: [
                {name: '体系薄弱'	,value:2},
                {name: '还需要谨慎抉择'	,value:5},
                {name: '来成长'	,value:2},
                {name: '跨部门协作'	,value:10},
                {name: '积累太少'	,value:2},
                {name: '都亟待加强'	,value:2},
                {name: '过长时导致降低'	,value:8},
                {name: '无规范'	,value:2},
                {name: '加班适量减少'	,value:2},
                {name: '业务开始趋于聚焦'	,value:8},
                {name: '很难招'	,value:2},
                {name: '做是一样'	,value:2},
                {name: '不止行业'	,value:2},
                {name: '招人难'	,value:2},
                {name: '危机感不足'	,value:8},
                {name: '打响知名度'	,value:2},
                {name: '没有沉淀'	,value:2},
                {name: '底层员工缺少驱动剂'	,value:16},
                {name: '能力有待提升'	,value:9},
                {name: '人才储备'	,value:9},
                {name: '公司分享'	,value:9},
                {name: '在来看'	,value:9},
                {name: '资源浪费'	,value:9},
                {name: '容易降低信任度'	,value:7},
                {name: '完善制度'	,value:9},
                {name: '战略清晰'	,value:9},
                {name: '饮食在'	,value:9},
                {name: '锦上添花'	,value:6},
                {name: '在塑造'	,value:9},
                {name: '更注重'	,value:9},
                {name: '还是产牌打造'	,value:1},
                {name: '避免传播'	,value:9},
                {name: '根因值得拿出探讨'	,value:9},
                {name: '过半年'	,value:8},
                {name: '以提升实力目标'	,value:8},
                {name: '发行利润'	,value:8},
                {name: '人才机制不太行'	,value:8},
                {name: '团队还掌话语权'	,value:8},
                {name: '组织划分权责'	,value:8},
                {name: '需要出来'	,value:8},
                {name: '度极低'	,value:8},
                {name: '内部分享流程'	,value:8},
                {name: '没有发现'	,value:8},
                {name: '增加标准'	,value:8},
                {name: '还是提炼'	,value:8},
                {name: '提升是靠'	,value:8},
                {name: '导致薄弱'	,value:8},
                {name: '简而精'	,value:8},
                {name: '未证明是一员'	,value:8},
                {name: '感觉放心'	,value:8},
                {name: '流程太长'	,value:8},
                {name: '并没有践行战略'	,value:8},
                {name: '规模算公司'	,value:8},
                {name: '没有免费零食'	,value:15},
                {name: '这是问题'	,value:15},
                {name: '体系机制偏薄弱'	,value:15},
                {name: '没有职级'	,value:15},
                {name: '人才因为变得浮躁'	,value:15},
                {name: '一直活跃'	,value:2},
                {name: '不得不挤出时间轮子'	,value:2},
                {name: '是优势'	,value:2},
                {name: '产品研发实力'	,value:2},
                {name: '游戏都有私服'	,value:2},
                {name: '重复轮'	,value:2},
                {name: '财务资源'	,value:2},
                {name: '开放包容'	,value:2},
                {name: '措施不行'	,value:2},
                {name: '很多时候靠人'	,value:2},
                {name: '人才培育'	,value:2},
                {name: '公司未能激发效率'	,value:2},
                {name: '往往起大早集'	,value:2},
                {name: '还需要合理性'	,value:2},
                {name: '流程方面'	,value:2},
                {name: '造成资源资源'	,value:2},
                {name: '有效规避'	,value:2},
                {name: '项目说做做'	,value:2},
                {name: '核心价值观清晰'	,value:2},
                {name: '环境不同'	,value:4},
                {name: '自研IP'	,value:4},
                {name: '员工对于'	,value:4},
                {name: '很薄弱'	,value:4},
                {name: '感觉良好'	,value:10},
                {name: '行为但是是背道而驰'	,value:10},
                {name: '更容易会带来突破'	,value:10},
                {name: '存在加班'	,value:10},
                {name: '避免出现'	,value:10},
                {name: '猜到是谁'	,value:10},
                {name: '流失率'	,value:10},
                {name: '核心产品缺少积累'	,value:10},
                {name: '研运挑战'	,value:10},
                {name: '品牌尽快加强'	,value:10},
                {name: '明晰路径'	,value:10},
                {name: '快速迭代'	,value:10},
                {name: '不能产品'	,value:10},
                {name: '适当授权'	,value:1},
                {name: '工作室各自独立'	,value:1},
                {name: '妄图搞定事'	,value:1},
                {name: '流程不清晰'	,value:1},
                {name: '应区别对待'	,value:1},
                {name: '摊子铺'	,value:1},
                {name: '能够积淀'	,value:1},
                {name: '时间过少'	,value:1},
                {name: '人比获得更多'	,value:1},
                {name: '但是太快'	,value:1},
                {name: '不能雪中送炭搞'	,value:1},
                {name: '在精简'	,value:1},
                {name: '目标瞄准'	,value:8},
                {name: '选是一样'	,value:8},
                {name: '在破釜沉舟'	,value:8},
                {name: '空间环节'	,value:8},
                {name: '导致有时间提升'	,value:1},
                {name: '各自为政'	,value:1},
                {name: '先跑起来'	,value:1},
                {name: '想要多谢提高'	,value:1},
                {name: '岗位职责不够明确'	,value:1},
                {name: '员工流动'	,value:1},
                {name: '激活再突破动力计划'	,value:1},
                {name: '很注重开发'	,value:1},
                {name: '或许是期待太高'	,value:1},
                {name: '应该考虑划分'	,value:1},
                {name: '音频方面'	,value:1},
                {name: '在变成员'	,value:1},
                {name: '尽量考虑到感受'	,value:1},
                {name: '决策有点随意'	,value:1},
                {name: '团队身上找到原因'	,value:1},
                {name: '最主要是放大'	,value:1},
                {name: '内部培训'	,value:1},
                {name: '人放到岗位'	,value:1},
                {name: '新人容易增加带成本'	,value:1},
                {name: '各项目组'	,value:1},
                {name: '勇敢面对困难'	,value:1},
                {name: '给予认可'	,value:1},
                {name: '共创太多'	,value:1},
                {name: '持续带来较少'	,value:1},
                {name: '牵引力没有制度'	,value:1},
                {name: 'OEM制造商'	,value:1},
                {name: '不论是品牌'	,value:4},
                {name: '处于卷上海'	,value:4},
                {name: '难以提前预见可能'	,value:4},
                {name: '的声音'	,value:4},
                {name: '缺乏能力'	,value:4},
                {name: '而不是方面'	,value:4},
                {name: '加速交流'	,value:4},
                {name: '管理混乱'	,value:4},
                {name: '过于拍脑袋'	,value:4},
                {name: '乐趣上产品'	,value:4},
                {name: '点决定'	,value:18},
                {name: '可以接受'	,value:18},
                {name: '预见性不够'	,value:18},
                {name: '进而增加负担'	,value:2},
                {name: '没有想定'	,value:2},
                {name: '形象提升'	,value:2},
                {name: '工业化'	,value:2},
                {name: '环境较差'	,value:2},
                {name: '宁缺毋滥'	,value:2},
                {name: '能力不够'	,value:2},
                {name: '向心力欠缺'	,value:2},
                {name: '没关注'	,value:2},
                {name: '加上因素'	,value:2},
                {name: '建议开展技能'	,value:2},
                {name: '无法做岗位重'	,value:2},
                {name: '能锦上添花'	,value:2},
                {name: '去跟进'	,value:2},
                {name: '公司不够强大'	,value:2},
                {name: '游戏国外上线'	,value:2},
                {name: '公司在'	,value:2},
                {name: '制度机制'	,value:2},
                {name: '没有游戏'	,value:2},
                {name: '将管理工少'	,value:2},
                {name: '处理不够果断'	,value:2},
                {name: '我们手上除了项目'	,value:2},
                {name: '浪费但是还是厉害'	,value:2},
                {name: '还是需要做事'	,value:2},
                {name: '需要成为游戏'	,value:2},
                {name: '人力体系薄弱'	,value:2},
                {name: 'betterlaterthannever'	,value:2},
                {name: '初代创始人'	,value:2},
                {name: '动作也需要做梳理'	,value:2},
                {name: '东西全流失'	,value:2},
                {name: '还没有在品质'	,value:2},
                {name: '公司好'	,value:2},
                {name: '习惯性知识整理'	,value:2},
                {name: '激活流程'	,value:2},
                {name: '上层一句话决定生死'	,value:2},
                {name: '一方面影响发挥出'	,value:2},
                {name: '不够了解市场'	,value:2},
                {name: '低估对手'	,value:2},
                {name: '没有标准'	,value:2},
                {name: '再进行有备案'	,value:2},
                {name: '缺少信息洞察'	,value:2},
                {name: '我不知道'	,value:2},
                {name: '人才密度还不够'	,value:7},
                {name: '人才培养'	,value:7},
                {name: '做好调研'	,value:7},
                {name: '要求制度'	,value:7},
                {name: '具做到'	,value:7},
                {name: '知名度低'	,value:7},
            ],
            //小鸟图片
            image: "data:image/png;base64,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",
        }

        //温馨提示：image 选取有严格要求不可过大；，否则firefox不兼容  iconfont上面的图标可以
        // let maskImage = new Image();
        // maskImage.src = data.image
        // maskImage.onload = function () {
            selt.myChart2 = echarts.init(document.querySelector('.echarts')); 
            selt.myChart2
            .setOption({
                backgroundColor: '#fff',
                color: selt.colorList,
                tooltip: {
                    show: false
                },
                series: [
                    {
                        type: 'wordCloud',
                        size: [
                            '30%', '100%'
                        ],
                        sizeRange: [
                            5, 15
                        ],
                        // textRotation: [
                        //     0, 45, 90, -45
                        // ],
                        // rotationRange: [
                        //     -45, 90
                        // ],
                        // rotationRange: [
                        //     15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 315, 330, 345, 360
                        // ],
                        rotationRange: [
                            -80, 80
                        ],
                        gridSize: 10,
                        shape: 'circle',
                        drawOutOfBound: false,
                        autoSize: {
                            enable: true,
                            minSize: 1,
                            maxSize: 20
                        },
                        // maskImage: maskImage,
                        textStyle: {
                            normal: {
                                color: () => {
                                    return 'rgb(' + [
                                        Math.round(Math.random() * 160),
                                        Math.round(Math.random() * 160),
                                        Math.round(Math.random() * 160)
                                    ].join(',') + ')';
                                }
                            },
                            emphasis: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.15)'
                            }
                        },
                        data: data.value
                    }
                ]
            })
        // }
    }
}
