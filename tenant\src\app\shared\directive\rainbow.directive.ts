import { Directive, HostBinding, HostListener } from '@angular/core';

@Directive({
  selector: '[appRainbow]'
})
export class RainbowDirective {
  
  possibleColors = [
    'darksalmon', 'hotpink', 'lightskyblue', 'goldenrod', 'peachpuff',
    'mediumspringgreen', 'cornflowerblue', 'blanchedalmond', 'lightslategrey',
    'DarkMagenta','Purple','MediumOrchid','DarkViolet','DarkOrchid',
    'LightSlateGray','SlateGray','DodgerBlue',
  ];
  
  @HostBinding('style.color') color: string;
  @HostBinding('style.borderColor') borderColor: string;
  @HostBinding('style.backgroundColor') bgColor: string;

  lastIndex : number = 0;

  @HostListener('keydown') onKeydown() {
    let ranIndex : number = Math.floor(Math.random() * this.possibleColors.length);
    if(ranIndex === this.lastIndex) {
      ranIndex = this.lastIndex + 1;
      if(ranIndex >= this.possibleColors.length ) {
        ranIndex = 0;
      }
    }
    const colorPick = ranIndex;
    this.lastIndex = colorPick;
    console.log('Jerry colorPick: ' + colorPick);
    this.color = 'white';
    this.bgColor = this.possibleColors[colorPick];
  }

  @HostListener('focus') focus() {
    this.borderColor = 'red';
  }

  @HostListener('blur') blur() {
    this.borderColor = 'gray';
  }

}