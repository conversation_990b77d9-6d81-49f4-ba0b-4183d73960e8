* {
  margin: 0;
  padding: 0;
}

img {
  display: inline-block;
}

.batchSendBox {
  width: 100%;
  height: 100%;
  background-color: #F5F6FA;
  overflow-y: auto;
  padding-bottom: 30px;

  .container {
    // 容器
    margin: 0px auto;

    .headContent {
      padding-top: 20px;
      display: flex;
      justify-content: space-between;
      padding-bottom: 30px;

      h1 {
        font-size: 24px;
        font-family: PingFangSC-Thin, PingFang SC;
        font-weight: 100;
        color: #17314C;
        line-height: 33px;
        margin-top: 6px;
      }
    }

    .main {
      width: 100%;
      min-height: 680px;
      background: #FFFFFF;
      border-radius: 8px;
      display: flex;
      padding: 30px 30px 16px;
      justify-content: space-between;

      .main_L {
        width: 555px;
        height: 100%;
        margin-right: 30px;

        .radio_type {
          margin-bottom: 38px;

          h5 {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #17314C;
            line-height: 20px;
            margin-bottom: 31px;
          }

          .invite-user {
            .ant-radio-group {
              .ant-radio-wrapper {
                margin-right: 57px;
              }
            }
          }
        }

        .send_contet {
          .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            min-height: 30px;

            h5 {
              font-size: 14px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #17314C;
              line-height: 20px;
            }

            div {
              display: flex;
              justify-content: end;
              align-items: center;

              ::ng-deep .ant-btn {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 5px 14px;
                min-width: 56px;
                height: 30px;
                background: #FFFFFF;
                border-radius: 15px;
                border-color: #409EFF;
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #409EFF;
                text-shadow: none;
                box-shadow: none;
                margin-left: 8px;
                cursor: pointer;

                .anticon-loading {
                  margin-left: 0px;
                }
              }

              ::ng-deep .ant-upload.ant-upload-disabled {
                cursor: pointer;
              }
            }
          }

          .content {
            width: 100%;
            height: 440px;
            padding: 14px;
            background: #F9F9F9;
            border-radius: 8px;
            overflow-y: auto;
            box-sizing: border-box;
            color: rgba(0, 0, 0, 0.65);
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #17314C;
            line-height: 22px;
          }
        }
      }

      .main_R {
        flex: 1;

        .email_subject {
          h5 {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #17314C;
            line-height: 20px;
            margin-bottom: 20px;
          }

          .input-user {
            height: 40px;

            .title_email {
              width: 100%;
              height: 100%;
              padding: 0 15px;
              font-size: 14px;
            }
          }

          padding-bottom: 36px;
        }

        .email_text {
          h5 {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #17314C;
            line-height: 20px;
            margin-bottom: 20px;
          }

          .upload {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #409EFF;
            line-height: 20px;
            cursor: default;
            margin-top: 16px;

            span {
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #AAAAAA;
              line-height: 17px;
            }
          }
        }
      }
    }
  }

  .invite-footer {
    position: fixed;
    width: 100%;
    left: 0;
    bottom: 0;
    height: 70px;
    background: #ffffff;
    box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);
    text-align: right;
    padding-top: 16px;

    .ant-btn-primary {
      width: 128px;
      height: 38px;
      background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
      box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      border: none;
    }
  }
}