<!--
    *@author: <PERSON>
    *@Date: 2023/09/11
    *@content: 交叉分析
-->
<div class="box">
  <nz-spin [nzSpinning]="isSpinning">
    <div nz-row class="row">
      <div nz-col nzSpan="12">
        <!-- 标题 -->
        <div class="title">
          <div>
            <span class="title-text">分割线</span>
            <div class="title-tip">区隔四窗象限的轴</div>
          </div>
          <a (click)="onClearLeft()"
            ><i class="iconfont icon-icon_delete"></i>清空</a
          >
        </div>
        <!-- 内容区域-滑动 -->
        <div class="scroll">
          <div class="scroll-inside hasBtns">
            <!-- 分类 -->
            <div class="left">
              <div class="left-title">分类（一次选一个）</div>
              <div class="left-checkbox">
                <nz-radio-group
                  [(ngModel)]="classifyCheckedKey"
                  (ngModelChange)="onChangeClass($event)"
                  style="width: 100%;"
                >
                  <div nz-row>
                    <ng-container *ngFor="let item of classifyOptions">
                      <div nz-col nzSpan="6">
                        <label nz-radio [nzValue]="item.value">{{
                          item.label
                        }}</label>
                      </div>
                    </ng-container>
                  </div>
                </nz-radio-group>
              </div>
              <div class="left-checkbox" *ngIf="classifyCheckedKey === 'DEPT'">
                <nz-radio-group
                  [(ngModel)]="organizationLevel"
                  style="width: 100%;"
                >
                  <div nz-row>
                    <ng-container *ngFor="let item of organizationLevelOptions">
                      <div nz-col nzSpan="6">
                        <label nz-radio [nzValue]="item.value">{{
                          item.label
                        }}</label>
                      </div>
                    </ng-container>
                  </div>
                </nz-radio-group>
              </div>
            </div>
            <!-- 对比数据 -->
            <div class="left">
              <div class="left-title">对比数据 (一次选一个)</div>
              <div class="left-checkbox">
                <nz-radio-group
                  [(ngModel)]="correlationCheckedKey"
                  style="width: 100%;"
                >
                  <div nz-row>
                    <ng-container *ngFor="let item of correlationOptions">
                      <div nz-col nzSpan="6">
                        <label nz-radio [nzValue]="item.value">{{
                          item.label
                        }}</label>
                      </div>
                    </ng-container>
                  </div>
                </nz-radio-group>
              </div>
            </div>
            <!-- 横纵轴 -->
            <div class="left">
              <div class="left-title">
                横纵轴 (默认第一选项X轴，第二选项Y轴)
              </div>
              <div class="left-checkbox">
                <ng-container *ngFor="let item of indexOptions">
                  <div>
                    <label
                      nz-checkbox
                      [nzDisabled]="item.disabled"
                      [nzValue]="item.value"
                      [(ngModel)]="item.checked"
                      (ngModelChange)="onChangeIndex()"
                      >{{ item.label }}</label
                    >
                    <ng-container
                      *ngIf="item.value === XKey || item.value === YKey"
                    >
                      <nz-tag
                        [nzColor]="item.value === XKey ? 'blue' : 'purple'"
                        >{{ item.value === XKey ? "X" : "Y" }}</nz-tag
                      >
                    </ng-container>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
        <div class="btns">
          <button nz-button nzType="primary" nzGhost (click)="onRelation()">
            关联
          </button>
        </div>
      </div>
      <div nz-col nzSpan="12">
        <!-- 标题 -->
        <div class="title">
          <div>
            <span class="title-text">结果</span>
            <div class="title-tip">
              主分析中，选中相同横纵轴指数的部门和人口为一组，按以下部门的呈现顺序显示(默认数据置顶显示)
            </div>
          </div>
          <a (click)="onClearRight()"
            ><i class="iconfont icon-icon_refresh"></i>恢复默认</a
          >
        </div>
        <!-- 内容区域-滑动 -->
        <div class="scroll">
          <div class="scroll-inside">
            <div class="right">
              <div class="right-title">
                <div></div>
                <b>部门</b>
              </div>
              <!-- 部门 -->
              <div style="height: 200px;" *ngIf="!rightDataSort1.length">
                <app-empty text="暂无数据"></app-empty>
              </div>
              <ng-container *ngIf="rightDataSort1.length">
                <div
                  dragula="CROSS_ANALYSIS_1"
                  [(dragulaModel)]="rightDataSort1"
                  (dragulaModelChange)="dragulaChangeSort1($event)"
                >
                  <div
                    class="right-card"
                    *ngFor="let item of rightDataSort1"
                    [class.no-drag]="item.isDefault"
                  >
                    <div class="right-card-title">
                      <div class="del" (click)="onDel(item)">
                        <i nz-icon nzType="minus-circle" nzTheme="fill"></i>
                      </div>
                      <span>
                        {{ correlationOptionsMap[item.compareDataCode] }}
                        <nz-badge
                          *ngIf="item.isDefault"
                          nzStatus="processing"
                          nz-tooltip
                          nzTitle="默认"
                          class="ml-4"
                          nzPlacement="right"
                        ></nz-badge>
                      </span>
                      <i
                        class="iconfont icon-caidan pointer"
                        *ngIf="!item.isDefault"
                      ></i>
                    </div>
                    <div class="right-card-tags">
                      <span>
                        {{ indexOptionsMap[item.x] }}
                      </span>
                      <span>
                        {{ indexOptionsMap[item.y] }}
                      </span>
                      <nz-tag
                        [nzColor]="'blue'"
                        style="line-height: 23px;"
                        *ngIf="item.organizationLevel"
                      >
                        {{
                          organizationLevelOptionsMap[item.organizationLevel]
                        }}
                      </nz-tag>
                    </div>
                  </div>
                </div>
              </ng-container>
              <div class="right-title">
                <div></div>
                <b>人口标签</b>
              </div>
              <!-- 人口标签 -->
              <div style="height: 200px;" *ngIf="!rightDataSort2.length">
                <app-empty text="暂无数据"></app-empty>
              </div>
              <ng-container *ngIf="rightDataSort2.length">
                <div
                  dragula="CROSS_ANALYSIS_2"
                  [(dragulaModel)]="rightDataSort2"
                  (dragulaModelChange)="dragulaChangeSort2($event)"
                >
                  <div
                    *ngFor="let item of rightDataSort2"
                    class="right-card"
                    [class.no-drag]="item.isDefault"
                  >
                    <div class="right-card-title">
                      <div class="del" (click)="onDel(item)">
                        <i nz-icon nzType="minus-circle" nzTheme="fill"></i>
                      </div>
                      <span>
                        {{ correlationOptionsMap[item.compareDataCode] }}
                        <nz-badge
                          *ngIf="item.isDefault"
                          nzStatus="processing"
                          nz-tooltip
                          nzTitle="默认"
                          class="ml-4"
                          nzPlacement="right"
                        ></nz-badge>
                      </span>
                      <i
                        class="iconfont icon-caidan pointer"
                        *ngIf="!item.isDefault"
                      ></i>
                    </div>
                    <div class="right-card-tags">
                      <span>
                        {{ indexOptionsMap[item.x] }}
                      </span>
                      <span>
                        {{ indexOptionsMap[item.y] }}
                      </span>
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nz-spin>
</div>
<div class="footer">
  <button nz-button nzType="default" (click)="onClose()" [nzLoading]="loading">
    关闭
  </button>
  <button
    nz-button
    nzType="primary"
    (click)="onConfirm()"
    [nzLoading]="loading"
  >
    确认
  </button>
</div>
