.jump {
  font-family: <PERSON><PERSON>ang<PERSON>, <PERSON><PERSON><PERSON> SC;
  font-style: normal;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  &-spin {
    // margin-top: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &-load {
      width: 320px;
      height: 250px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
      &-iocn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, 0%);
      }
    }
    p {
      width: 350px;
      margin-top: 32px;
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      line-height: 22px;
      text-align: center;
    }
    transition: opacity 1s ease-in-out; /* 添加过渡效果，使变化更平滑 */
  }
  &-result {
    width: 560px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    i {
      font-size: 70px;
    }
    h1 {
      margin-top: 30px;
      font-weight: 500;
      font-size: 24px;
      color: #17314c;
      line-height: 32px;
      text-align: center;
    }
    p {
      margin-top: 8px;
      font-weight: 400;
      font-size: 14px;
      color: #495970;
      line-height: 22px;
      text-align: center;
      color: rgba(0, 0, 0, 0.45);
    }
    button {
      margin-top: 40px;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      padding: 8px 32px;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 22px;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
        transition: opacity 0.3s ease-in-out; /* 添加过渡效果，使变化更平滑 */
      }
    }
    transition: opacity 1s ease-in-out; /* 添加过渡效果，使变化更平滑 */
    .percent {
      margin-top: 40px;
      width: 100%;
    }
  }
  .success {
    color: #60cb7f;
  }
  .error {
    color: #fbb853;
  }
}
