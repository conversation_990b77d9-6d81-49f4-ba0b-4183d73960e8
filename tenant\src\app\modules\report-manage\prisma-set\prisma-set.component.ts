import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit} from '@angular/core';
import { NumberToChineseModule } from '@knz/assembly';
import { NzMessageService, NzModalRef } from 'ng-zorro-antd';

@Component({
  selector: 'app-prisma-set',
  templateUrl: './prisma-set.component.html',
  styleUrls: ['./prisma-set.component.less']
})
export class PrismaSetComponent implements OnInit {

  @Input() data: any;

  @Input() prismaReportDataId: string;

  @Input() projectId: string;

  mode: string = '1'

  isCreating = false;

  tenantUrl: string = "/tenant-api";

  constructor(private http: HttpClient, private msgServ: NzMessageService, private ref: NzModalRef, ) { }

  ngOnInit() {
    // console.log(this.data.projectId)
    // console.log(this.projectId)
  }

  getActive(type) {
    this.mode = type;
  }

  redoGroupPdf() {
    let param:any = {isReCalculate: true};
    let key = "prismaReportDataId";;
    param[key] = this.prismaReportDataId;
    const api = `${this.tenantUrl}/sagittarius/report/content/reCreateGroupReport`;
    this.isCreating = true;
    this.http.post(api, param).subscribe((res: any) => {
      this.isCreating = false;
      if (res.result.code === 0) {
        this.msgServ.success("重新生成已提交请求");
        this.ref.triggerOk();
      }
    }, error => {
      this.isCreating = false;
    });
  }

}
