import { HttpClient } from "@angular/common/http";
import {
  Component,
  Inject,
  OnInit,
  ViewChild,
  ChangeDetectorRef,
  ViewChildren,
  TemplateRef,
  ElementRef,
  ViewContainerRef,
  OnDestroy,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { DA_SERVICE_TOKEN, ITokenService } from "@knz/auth";
import {
  NzDrawerService,
  NzMessageService,
  NzModalService,
  NzNotificationDataOptions,
  NzNotificationService,
  UploadXHRArgs,
} from "ng-zorro-antd";
import { NzTreeNode } from "ng-zorro-antd/core";
import { TransferChange, TransferItem } from "ng-zorro-antd/transfer";
import { NzTreeComponent } from "ng-zorro-antd/tree";
import { FileDownloadComponent } from "../file-download/file-download.component";
import { ReportService } from "../report.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { GroupCreateComponent } from "../group-create/group-create.component";
import { SurveyCreateComponent } from "../survey-create/survey-create.component";
import { UserinfoComponent } from "../userinfo/userinfo.component";
import { UnionCreateComponent } from "../union-create/union-create.component";
import { HttpEvent } from "@angular/common/http";
import _ from "lodash";
// import { ComparePersonComponent } from '../compare-person/compare-person.component';
// import { CompareOrgComponent } from '../compare-org/compare-org.component';
import { PrismaSetComponent } from "../prisma-set/prisma-set.component";
import { concat, Observable } from "rxjs";
import { AdvancedModelComponent } from "./advancedFilter/advanced-model.component";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import { OrgService } from "../../org/org.service";
import { env } from "process";
import { environment } from "@env/environment";
import { DownloadUtilService } from "@src/modules/service/download-util.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import * as moment from "moment";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-report-home",
  templateUrl: "./report-home.component.html",
  styleUrls: ["./report-home.component.less"],
})
export class ReportHomeComponent implements OnInit, OnDestroy {
  @ViewChild("advancedChild", { static: false })
  advancedChild: AdvancedModelComponent;

  @ViewChild("tree", { static: false }) tree: NzTreeComponent;
  @ViewChild("customNotification", { static: true })
  customNotification: TemplateRef<any>;

  permission = this.permissionService.isPermission();

  list: TransferItem[] = [];
  treeData = [];
  checkedNodeList: NzTreeNode[] = [];

  toolVisible: boolean = false;

  isBoxVisible: boolean = false;
  fileName: any = {
    zh_CN: null,
    en_US: null,
  };
  changeNameData: any;
  childNum: number = 0;

  headerList: any[] = [
    {
      id: 1,
      name: "标准报告",
      iconText: "标",
      imgPath: "./assets/images/org/img_standard.png",
      isSelected: true,
    },
    {
      id: 2,
      name: "团队报告",
      iconText: "团",
      imgPath: "./assets/images/org/img_group.png",
      isSelected: false,
    },
    {
      id: 3,
      name: "对比报告",
      iconText: "对",
      imgPath: "./assets/images/org/img_compare.png",
      isSelected: false,
    },
  ];

  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "",
      name: "报告管理",
      Highlight: true,
    },
  ];
  // 标准报告
  columnList: any[] = [
    {
      id: 1,
      type: "string",
      title: "活动名称",
      attrName: "projectName",
      attrName1: "zh_CN",
      isSelected: true,
      width: "250px",
      left: "70px",
    },
    {
      id: 11,
      type: "string",
      title: "活动ID",
      attrName: "projectCode",
      isSelected: true,
      width: "70px",
    },
    {
      id: 12,
      type: "date",
      title: "创建时间",
      attrName: "createTime",
      isSelected: true,
      width: "170px",
    },
    {
      id: 2,
      type: "date",
      title: "更新时间",
      attrName: "updateTime",
      isSelected: true,
      width: "170px",
    },
    {
      id: 3,
      type: "string",
      title: "姓名",
      attrName: "personId",
      isSelected: true,
      width: "100px",
    },
    {
      id: 4,
      type: "string",
      title: "产品类型",
      attrName: "questionnaireType",
      isSelected: true,
      width: "120px",
    },
    {
      id: 6,
      type: "string",
      title: "人口学信息",
      attrName: "demographic",
      isSelected: false,
      width: "220px",
    },
    {
      id: 5,
      type: "string",
      title: "总评",
      attrName: "generalComment",
      isSelected: true,
      width: "70px",
      right: this.permission ? "300px" : "230px",
    },
  ];
  // 团队报告
  teamColumnList: any[] = [
    {
      id: 1,
      type: "string",
      title: "活动名称",
      attrName: "projectName",
      attrName1: "zh_CN",
      isSelected: true,
      width: "220px",
      left: "70px",
    },
    {
      id: 2,
      type: "string",
      title: "活动ID",
      attrName: "projectCode",
      isSelected: true,
      width: "70px",
    },
    {
      id: 3,
      type: "string",
      title: "报告名称",
      attrName: "reportName",
      isSelected: true,
      width: "220px",
    },
    {
      id: 4,
      type: "date",
      title: "创建时间",
      attrName: "createTime",
      isSelected: true,
      width: "170px",
    },
    {
      id: 5,
      type: "date",
      title: "更新时间",
      attrName: "updateTime",
      isSelected: true,
      width: "170px",
    },
    {
      id: 6,
      type: "string",
      title: "产品类型",
      attrName: "questionnaireType",
      isSelected: true,
      width: "100px",
    },
    {
      id: 7,
      type: "string",
      title: "团队明细",
      attrName: "generalComment",
      isSelected: true,
      width: "300px",
    },
  ];
  // 对比报告
  comparisonColumnList: any[] = [
    {
      id: 1,
      type: "string",
      title: "活动名称",
      attrName: "projectName",
      attrName1: "zh_CN",
      isSelected: true,
      width: "220px",
      left: "0px",
    },
    {
      id: 2,
      type: "string",
      title: "活动ID",
      attrName: "projectCode",
      isSelected: true,
      width: "70px",
    },
    {
      id: 3,
      type: "string",
      title: "报告名称",
      attrName: "reportName",
      isSelected: true,
      width: "220px",
    },
    {
      id: 4,
      type: "date",
      title: "创建时间",
      attrName: "createTime",
      isSelected: true,
      width: "170px",
    },
    {
      id: 5,
      type: "date",
      title: "更新时间",
      attrName: "updateTime",
      isSelected: true,
      width: "170px",
    },
    {
      id: 6,
      type: "string",
      title: "产品类型",
      attrName: "questionnaireType",
      isSelected: true,
      width: "100px",
    },
    {
      id: 7,
      type: "string",
      title: "对比明细",
      attrName: "personOrganizationIdNames",
      isSelected: true,
      width: "220px",
    },
  ];

  selectHeader = this.headerList[0];

  dateFormat = "YYYY/MM/DD HH:mm:ss";
  filterType: string = "projectName";
  filterValue: string = "";
  // selectedValue:string = 'ASSESSMENT';
  selectedValue: string = "";
  dateRange = [];

  // 分页控制
  totalCount: number = 1;
  currentPage: number = 1;
  pageSize: number = 10;

  listOfData = [];
  selectionMap: any = {};
  allFlag: boolean = true;
  isLoading: boolean = false;

  // 团队报告 上传使用变量
  currentGroupId: string;
  currentGroupKey: string;
  currentGroupData: any;
  currentLanguage: any;

  // 标准报告 上传变量
  currentNormalData: any;

  // 跳转查询参数
  personId: any;
  investigatorId: any;

  prismaDataId;

  // 显示对比报告参数
  isVisible = false;
  chartId = null;

  // 弹窗
  tips_vib = false;
  showErrorFlag: boolean = true;
  tdWidth = "230px";
  showmock = false;
  step1 = false;
  step2 = false;
  step3 = false;
  step4 = false;
  step5 = false;
  interval$: NodeJS.Timer;
  interval2$: NodeJS.Timer;
  interval3$: NodeJS.Timer;
  interval4$: NodeJS.Timer;

  // 视频解读
  balance: string | number = 6000; //
  showVideoBox: boolean = false;
  showOneBox: boolean = false;
  isGroup: boolean = false;
  reportType: string;
  showSuccess: boolean = false;
  modalLoading: boolean = false;
  telNum: string = "";
  reportTypeList: any[] = [];
  reportHumanInterpretation: string = "TEL";
  downloadnum = 0;
  reportTypedetail = "";

  // 对比报告
  searchTreeValue: string = "";
  paramList: any[] = [];
  pid: string = "";
  isDataMode: boolean = true;
  searchList: any[] = [];
  searchText: string = "";
  projId: string = "";
  nairId: string = "";
  isToolBtnLoading: boolean = false;
  toolType: number = 1; // 1 测评对比 2 调研对比

  // 调研报告
  selectedNodes: any = {};
  expandedKeys: string[] = [];
  transferKeys: any[] = [];
  nzExpandedKeys: any[] = [];

  tenantUrl: string = "/tenant-api";
  sendLoading: boolean;
  btnSelectAllLoading: boolean = false;

  // 生成失败
  buildFail: boolean = false;
  // 生成成功
  buildSucceed: boolean = false;
  viewContainerRef: any;

  tenantId: string = "";
  personReportFailShoe = true;
  groupReportFailShoe = true;

  isDisplayMail = true;

  userList: any[] = [];
  selectedUserValue: any[] = [];
  userFilterVisible: boolean;
  constructor(
    private cdr: ChangeDetectorRef,
    private router: Router,
    private http: HttpClient,
    private msgServ: NzMessageService,
    private orgService: OrgService,
    private routeInfo: ActivatedRoute,
    private downUtil: DownloadUtilService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private modalService: NzModalService,
    private drawerService: NzDrawerService,
    private rptService: ReportService,
    private surveySerivce: SurveyApiService,
    public knxFunctionPermissionService: KnxFunctionPermissionService,
    private notification: NzNotificationService,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {}

  showBox(data: any) {
    this.changeNameData = data;
    this.isBoxVisible = true;
    if (this.changeNameData.fileName) {
      this.fileName.zh_CN = this.changeNameData.fileName.zh_CN;
      this.fileName.en_US = this.changeNameData.fileName.en_US;
    }
  }
  handleBoxCancel() {
    this.fileName = {
      zh_CN: null,
      en_US: null,
    };
    this.isBoxVisible = false;
  }
  handleBoxOk() {
    let params: any = {
      fileName: this.fileName,
    };
    this.changeNameData.prismaDataId
      ? (params.prismaReportDataId = this.changeNameData.prismaDataId)
      : "";
    this.changeNameData.groupId
      ? (params.groupId = this.changeNameData.groupId)
      : "";
    this.rptService.updateGroupReportFileName(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.msgServ.success("修改成功");
        this.isBoxVisible = false;
        this.loadData();
        this.fileName = {
          zh_CN: null,
          en_US: null,
        };
      }
    });
  }

  columnSortChanged(event) {
    this.columnList = [...event];
    sessionStorage.setItem("columnSetting", JSON.stringify(this.columnList));
  }
  teamColumnSortChanged(event) {
    this.teamColumnList = [...event];
    sessionStorage.setItem(
      "teamColumnSetting",
      JSON.stringify(this.teamColumnList)
    );
  }
  comparisonColumnSortChanged(event) {
    this.comparisonColumnList = [...event];
    sessionStorage.setItem(
      "comparisonColumnSetting",
      JSON.stringify(this.comparisonColumnList)
    );
  }

  /**
   * customReq 导入
   * @param item
   */
  customReqAnalysisFactor = (item: UploadXHRArgs, data) => {
    console.log(item, "data");
    console.log(data);
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  nzCustomRequestChange(e, data) {
    this.prismaDataId = data.prismaDataId;
  }

  exportWord(data) {
    const api = `${this.tenantUrl}/sagittarius/report/keyword/export?prismaReportDataId=${data.prismaDataId}`;

    this.loading.dimScore = true;
    this.http
      .get(api, {
        responseType: "blob",
        observe: "response",
      })
      .subscribe((res) => {
        this.loading.dimScore = false;
        this.rptService.downFile(res);
      });
  }

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.orgService
      .importWordCloud(formData, this.prismaDataId)
      .subscribe(
        (event: HttpEvent<any>) => {
          item.onSuccess!();
          let res: any = event;
          if (res.result.code === 0) {
            this.msgServ.success("导入文件成功");
          }
        },
        (err) => {
          item.onError!(err, item.file!);
        }
      );
  }

  // 视频解读
  showVideo() {
    // 视频
    this.showVideoBox = true;
  }

  showOneOnOne() {
    // 1v1
    this.showOneBox = true;
  }

  changePer(e) {
    if (e === "TEL") {
      this.balance = 6000;
    } else {
      this.balance = 25000;
    }
  }

  closeModal() {
    setTimeout(() => {
      this.showOneBox = false;
      this.showVideoBox = false;
      this.clearBox();
    });
  }

  closeBox() {
    this.showVideoBox = false;
    this.showOneBox = false;
    this.clearBox();
  }

  clearBox() {
    this.balance = 6000;
    this.isGroup = false;
    this.telNum = "";
    this.reportType = null;
    this.reportHumanInterpretation = "TEL";
  }

  confirm() {
    if (!this.reportType) {
      // this.msgServ.error("请选择工具");
      this.customMsg.open("error", "请选择工具");
    } else {
      const url = `${this.tenantUrl}/survey/standard/report/view/getByReportTypeAndGroup?reportType=${this.reportType}&isGroup=${this.isGroup}`;
      this.http.get(url).subscribe((res: any) => {
        if (res.result.code === 0) {
          if (res.data.tenantUrl) window.open(res.data.tenantUrl);
        }
      });
    }
  } // 视频解读
  confirmOne() {
    if (!this.reportType) {
      // this.msgServ.error("请选择工具");
      this.customMsg.open("error", "请选择工具");
      return;
    }
    let flag = /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/.test(
      this.telNum
    );
    if (!flag) {
      // this.msgServ.error("请输入正确的手机号码");
      this.customMsg.open("error", "请输入正确的手机号码");
      return;
    }
    const params = {
      isGroup: this.isGroup,
      reportHumanInterpretation: this.reportHumanInterpretation,
      reportType: this.reportType,
      telNum: this.telNum,
    };
    this.modalLoading = true;
    this.rptService.createOne(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.showSuccess = true;
        this.modalLoading = false;
        this.msgServ.success("预约成功");
        setTimeout(() => {
          this.showSuccess = false;
          this.closeBox();
        }, 3000);
      } else {
        this.modalLoading = false;
      }
    });
  } // 视频解读

  ngOnInit() {
    // 标准报告
    let columnSetting = sessionStorage.getItem("columnSetting");
    if (columnSetting) {
      this.columnList = JSON.parse(columnSetting);
    }
    // 团队
    let teamColumnSetting = sessionStorage.getItem("teamColumnSetting");
    if (teamColumnSetting) {
      this.teamColumnList = JSON.parse(teamColumnSetting);
    }
    // 对比
    let comparisonColumnSetting = sessionStorage.getItem(
      "comparisonColumnSetting"
    );
    if (comparisonColumnSetting) {
      this.comparisonColumnList = JSON.parse(comparisonColumnSetting);
    }

    this.getSelfInfo();
    this.getUserData();
    this.loadVideoData();
    this.getIsDisplayMail();
    // this.getAnglemark()
    this.showmock = JSON.parse(sessionStorage.getItem("noviceGuidance"));
    if (this.showmock) {
      this.step1 = true;
    }

    this.permission = this.permissionService.isPermission();
    if (this.permission) {
      this.tdWidth = "300px";
    }

    this.personId = this.routeInfo.snapshot.queryParams.personId;
    this.investigatorId = this.routeInfo.snapshot.queryParams.investigatorId;
    this.loadData();
  }
  ngOnDestroy() {
    this.notification.remove();
  }
  loadVideoData() {
    this.rptService.getReportTypeList().subscribe((res) => {
      if (res.result.code === 0) {
        this.reportTypeList = res.data;
      }
    });
  }

  // filterHandle() { //高级筛选  操作（展示弹窗）
  //   this.advancedVisible = !this.advancedVisible
  // }

  getToken(): string {
    return this.tokenService.get().token;
  }

  switchHeader(headerItem?: any) {
    this.notification.remove();
    if (this.childNum !== 0) {
      this.advancedChild.clear();
    }

    if (headerItem) {
      for (let index = 0; index < this.headerList.length; index++) {
        const element = this.headerList[index];
        element.isSelected = element.id === headerItem.id;
        if (element.isSelected) {
          this.selectHeader = element;
        }
      }
    }

    // clear condtions
    this.filterType = "projectName";
    this.filterValue = "";
    // this.selectedValue = 'ASSESSMENT';
    this.selectedValue = "";
    this.dateRange = [];
    this.currentPage = 1;
    this.totalCount = 1;
    // clear selection
    this.selectionMap = {};
    this.allFlag = true;

    // 跳转的参数仅第一次有效
    this.personId = undefined;
    this.investigatorId = undefined;
    this.btnSelectAllLoading = false;

    // 生成成功/失败
    this.buildSucceed = false;
    this.buildFail = false;
    // 账户筛选
    this.selectedUserValue = [];

    this.loadData();
  }

  search() {
    if (this.childNum !== 0) {
      this.advancedChild.submitForm();
    } else {
      // 跳转的参数仅第一次有效
      this.personId = undefined;
      this.investigatorId = undefined;

      this.currentPage = 1;
      this.loadData();
    }
  }

  dateChange(e) {
    if (e.length === 0) {
      this.search();
    }
  }
  onCalendarChange(event): void {
    // 用户选择日期后，动态设置默认时间
    const [start, end] = event;
    start.setHours(0, 0, 0, 0); // 设置默认开始时间为 00:00:00
    end.setHours(23, 59, 59, 999); // 设置默认结束时间为 23:59:59
    this.dateRange = [start, end];
  }

  clearParams(num: number) {
    this.childNum = num;
    if (this.childNum !== 0) {
      this.filterType = "projectName";
      this.filterValue = null;
    }
  }

  getQueryParam(page?: any): any {
    let son = false;
    // 高级筛选处理
    this.currentPage = page ? this.currentPage : 1;
    if (this.childNum !== 0) son = true;
    let param = {
      type: this.selectedValue,
      startTime: this.dateRange[0]
        ? moment(this.dateRange[0]).format("YYYY-MM-DD HH:mm:ss")
        : null,
      endTime: this.dateRange[1]
        ? moment(this.dateRange[1]).format("YYYY-MM-DD HH:mm:ss")
        : null,
      pageRequest: {
        current: this.currentPage,
        size: this.pageSize,
      },
      match: son ? this.advancedChild.getSonMatch() : null,
      moreConditions: son ? this.advancedChild.getSonParams() : null,
      page: {
        current: this.currentPage,
        size: this.pageSize,
      },
      searchType: this.filterType,
      searchValue: this.filterValue,
      personId: this.personId || null,
      investigatorId: this.investigatorId || null,
      createBys:
        this.selectedUserValue && this.selectedUserValue.length
          ? this.selectedUserValue
          : null,
    };
    console.log("param", param);
    // 生成失败/成功
    if (this.selectHeader.id !== 3) {
      if (this.buildSucceed && !this.buildFail) {
        param["reportStatus"] = "SUCCESS";
      } else if (!this.buildSucceed && this.buildFail) {
        param["reportStatus"] = "FAIL";
      } else {
        param["reportStatus"] = "ALL";
      }
    }
    return param;
  }

  changeBuild(e) {
    console.log("changeBuild", e);
    this.loadData();
  }

  getParamsOfTeam() {
    let param = {
      searchType: this.filterType, // 活动名称
      searchValue: this.filterValue, // 活动名称
      type: this.selectedValue, // 分类
      startTime: this.dateRange[0]
        ? this.rptService.formatTime(this.dateRange[0], 0)
        : null,
      endTime: this.dateRange[1]
        ? this.rptService.formatTime(this.dateRange[1], 1)
        : null,
    };
    return param;
  }

  getCompareParam(): any {
    let param = {
      type: this.selectedValue,
      startTime: this.dateRange[0]
        ? this.rptService.formatTime(this.dateRange[0], 0)
        : null,
      endTime: this.dateRange[1]
        ? this.rptService.formatTime(this.dateRange[1], 1)
        : null,
      page: {
        current: this.currentPage,
        size: this.pageSize,
      },
      searchType: this.filterType,
      searchValue: this.filterValue,
      personId: this.personId || null,
      investigatorId: this.investigatorId || null,
    };
    return param;
  }

  loadData(page?: any) {
    let son = false;
    // let params
    // let match
    this.listOfData = [];
    let sub = null;
    if (this.childNum !== 0) son = true;
    if (this.selectHeader.id === 1) {
      sub = this.rptService.getStandardReportList(this.getQueryParam(page));
      if (this.personReportFailShoe) {
        this.getReportFailCount();
      }
    } else if (this.selectHeader.id === 2) {
      sub = this.rptService.getGroupedReportList(this.getQueryParam(page));
      if (this.groupReportFailShoe) {
        this.getReportFailCount();
      }
    } else if (this.selectHeader.id === 3) {
      sub = this.rptService.getCompareReportList(this.getQueryParam(page));
    }

    if (!sub) {
      return;
    }
    this.isLoading = true;
    sub.subscribe(
      (res) => {
        this.isLoading = false;
        if (res.result.code === 0) {
          this.totalCount = res.page.total;

          if (this.selectHeader.id === 1) {
            // 肯豆不够
            if (res.data.bean !== undefined && res.data.bean === false) {
              this.tips_vib = this.showErrorFlag;
            }
            this.listOfData = res.data.reportPersonData;
            console.log(111, this.listOfData);
            for (let index = 0; index < this.listOfData.length; index++) {
              const element = this.listOfData[index];
              element.isShowToolBox = false;
              element.isChecked = this.isItemChecked(element);
            }
          } else if (this.selectHeader.id === 2) {
            this.listOfData = res.data;
            for (let index = 0; index < this.listOfData.length; index++) {
              const element = this.listOfData[index];
              element.isChecked = this.isItemChecked(element);
            }
            this.doTranslate();
          } else if (this.selectHeader.id === 3) {
            this.listOfData = res.data;
          }
        }
      },
      (err) => {
        this.isLoading = false;
        this.totalCount = 0;
        // this.msgServ.error(err.statusText);
        this.customMsg.open("error", err.statusText);
      }
    );
  }

  doTranslate() {
    for (let index = 0; index < this.listOfData.length; index++) {
      const element = this.listOfData[index];
      let tmp: any = {};
      let lanMap = {
        zh_CN: [],
        en_US: [],
      };


      let fileVos: any[] = _.orderBy(
        element.reportListFileVo.reportFileVos,
        ["fileType"],
        ["desc"]
      );
      fileVos.forEach((item) => {
        if (item.fileUrl) {
          let fileIds: string[] = lanMap[item.language];
          if (fileIds) {
            fileIds.push(item.fileUrl);
          }
        }
        tmp[item.language] = JSON.parse(JSON.stringify(item));
      });

      let keys: string[] = Object.keys(tmp);
      for (let i = 0; i < keys.length; i++) {
        const lan = keys[i];
        let fileObj: any = tmp[lan];
        let fIds: string = lanMap[lan];
        if (fileObj && fIds && fIds.length > 0) {
          fileObj.fileUrl = fIds[0];
        }
      }
      element.fileMap = tmp;
    }
  }

  goReport(url: string) {
    if (!url) {
      // this.msgServ.warning("报告生成中");
      this.customMsg.open("warning", "报告生成中");
      return;
    }
    let token = this.tokenService.get().token;
    window.open(`${url}&token=${token}`);
  }

  btnSelectAll(type?: string) {
    // 如果全选/全不选还没有执行完毕 禁止操作
    if (this.btnSelectAllLoading) {
      return;
    }
    this.btnSelectAllLoading = true;
    if (type !== "team") {
      // 标准
      const api = `${this.tenantUrl}/sagittarius/report/content/getSelectReportInfo`;
      this.http.post(api, this.getQueryParam()).subscribe((res: any) => {
        if (res.result.code === 0) {
          for (let index = 0; index < res.data.length; index++) {
            const element = res.data[index];
            this.toggoleItem(element, true);
          }
          this.resetCheckState();
          this.allFlag = false;
        }
      });
    } else {
      // 团队
      const api = `${this.tenantUrl}/sagittarius/report/content/selectGroupReportInfo`;
      this.http.post(api, this.getQueryParam()).subscribe((res: any) => {
        // this.http.post(api, this.getParamsOfTeam()).subscribe((res: any) => {
        if (res.result.code === 0) {
          for (let index = 0; index < res.data.length; index++) {
            const element = res.data[index];
            this.toggoleItem(element, true, "team");
          }
          this.resetCheckState();
          this.allFlag = false;
        }
      });
    }
    this.btnSelectAllLoading = false;
  }

  btnCancelAll() {
    // 如果全选/全不选还没有执行完毕 禁止操作
    if (this.btnSelectAllLoading) {
      return;
    }
    this.btnSelectAllLoading = true;
    this.selectionMap = {};
    this.resetCheckState();
    this.allFlag = true;
    this.btnSelectAllLoading = false;
  }

  resetCheckState() {
    for (let index = 0; index < this.listOfData.length; index++) {
      const element = this.listOfData[index];
      element.isChecked = this.isItemChecked(element);
    }
  }

  toggoleItem(data, isChecked: boolean, type?: string) {
    let propertyName;
    let idKey: string;
    this.reportTypedetail = data.reportType;
    if (type !== "team") {
      let isCommon: boolean = data.personId;
      let is360: boolean = data.investigatorId;
      if (isCommon) {
        idKey = data.personId;
        propertyName = "personId";
      } else if (is360) {
        idKey = data.investigatorId;
        propertyName = "investigatorId";
      }

      if (data.prismaReportDataId || (!is360 && !isCommon)) {
        idKey = data.prismaReportDataId;
        propertyName = "prismaReportDataId";
      }
    } else {
      if (data.prismaDataId) {
        idKey = data.prismaDataId;
        propertyName = "prismaReportDataId";
      }
      if (data.groupId) {
        idKey = data.groupId;
        propertyName = "groupId";
      }
    }
    let selectKey: string;
    if (type !== "team") {
      selectKey = data.questionnaireId + "-" + idKey + "-" + data.reportType;
    } else {
      selectKey = data.id + "-" + idKey;
    }

    if (isChecked) {
      let val: any = {};
      val[propertyName] = idKey;
      val.projectId = data.projectId;
      val.questionnaireId = data.questionnaireId;
      val.name = type !== "team" ? data.name : data.reportName.zh_CN;
      if (data.questionnaireName) {
        val.name = data.questionnaireName.zh_CN + " - " + val.name;
      }
      val.reportType = data.reportType;
      val.isTipDetail = data.isTipDetail;
      val.id = idKey;
      if (idKey) {
        this.selectionMap[selectKey] = val;
      }
    } else {
      delete this.selectionMap[selectKey];
    }
  }

  isItemChecked(data) {
    let tmpId: string;
    let selectKey: string;
    if (this.selectHeader.id === 1) {
      if (!data.prismaReportDataId) {
        tmpId = data.investigatorId || data.personId;
      } else {
        tmpId = data.prismaReportDataId;
      }
      selectKey = data.questionnaireId + "-" + tmpId + "-" + data.reportType;
    } else if (this.selectHeader.id === 2) {
      if (!data.prismaDataId) {
        tmpId = data.groupId;
      } else {
        tmpId = data.prismaDataId;
      }
      selectKey = data.id + "-" + tmpId;
    }

    return this.selectionMap[selectKey];
  }

  getSelectCount() {
    return Object.keys(this.selectionMap).length;
  }

  getSelectNames() {
    let vals = Object.values(this.selectionMap);
    return vals;
  }

  copy(id) {
    const copyEl = document.querySelector("#content" + id);
    const range = document.createRange();
    range.selectNode(copyEl);
    window.getSelection().removeAllRanges();
    window.getSelection().addRange(range);
    document.execCommand("copy");
    this.msgServ.success("复制成功");
  }

  buildSelectParam() {
    let ret: any = Object.values(this.selectionMap);
    return ret;
  }

  loading = {
    dimScore: false,
    answerData: false,
    zh_CN: false,
    en_US: false,
    recreate: false,
    totol: 0,
    done: 0,
  };

  /**
   * 下载(未使用)
   * @returns
   */
  openDownload() {
    let selNum: number = this.getSelectCount();
    if (selNum === 0) {
      // this.msgServ.error("没有选择下载数据");
      this.customMsg.open("error", "没有选择下载数据");
      return;
    }
    let isTeam: boolean;
    let param: any[] = this.buildSelectParam();
    if (this.selectHeader.id === 1) {
      isTeam = false;
    }
    if (this.selectHeader.id === 2) {
      isTeam = true;
    }

    const modal = this.modalService.create({
      nzTitle: null,
      nzFooter: null,
      nzWidth: 450,
      nzContent: FileDownloadComponent,
      nzComponentParams: {
        loading: this.loading,
        isAdmin: this.permission,
        selectParam: param,
        isTeam: isTeam,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child = modal.getContentComponent();
        let actions: string[] = child.getSelectActions(false);
        if (_.includes(actions, "dimScore")) {
          this.downloadDimScore(param);
        }
        if (_.includes(actions, "answerData")) {
          this.downloadAnswerData(param);
        }

        let zhCn: string = "zh_CN";
        let enUs: string = "en_US";

        if (_.includes(actions, zhCn) || _.includes(actions, enUs)) {
          let lans: string[] = [];
          if (_.includes(actions, zhCn)) {
            lans.push(zhCn);
            // 选择下载默认加入英文
            lans.push(enUs);
          }
          if (_.includes(actions, enUs)) {
            lans.push(enUs);
          }
          let newParam: any = { languages: lans, reportPersons: param };
          if (this.selectHeader.id === 1) {
            newParam.type = "COMMON";
          }
          if (this.selectHeader.id === 2) {
            newParam.type = "GROUP";
          }

          this.downloadStandardFiles(newParam);
        }
        this.downloadnum = 1;

        return false;
      },
      nzOnCancel: () => {
        this.loading = {
          dimScore: false,
          answerData: false,
          zh_CN: false,
          en_US: false,
          recreate: false,
          totol: 0,
          done: 0,
        };
      },
    });
  }
  jumpToSend() {
    //跳转到发送页面
    let selNum: number = this.getSelectCount();
    if (selNum === 0) {
      // this.msgServ.error("没有选择批量发送数据");
      this.customMsg.open("error", "没有选择批量发送数据");
      return;
    }

    let isGroup: boolean;
    let param: any[] = this.buildSelectParam();
    if (this.selectHeader.id === 1) {
      isGroup = false;
    }
    if (this.selectHeader.id === 2) {
      isGroup = true;
    }
    // sessionStorage.setItem('sendData', JSON.stringify(data))
    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    let list = {
      isGroup: isGroup,
      reportSendDataVos: param,
    };
    this.sendLoading = true;
    this.rptService.saveBatchReportEmailObjects(list).subscribe((res) => {
      this.sendLoading = false;
      this.router.navigate(["report-manage/batch-send"], {
        queryParams: {
          type: !isGroup
            ? res.data.isGroup
              ? "individualTeam"
              : "individual"
            : "team",
          reportMailContentId: res.data.reportMailContentId,
        },
      });
    });
  }
  downloadDimScore(params) {
    const investigatorIds = [];
    const prismaReportDataIds = [];
    const nairePersonIds = [];
    const groupIds = [];
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < params.length; i++) {
      const param = params[i];
      if (param.prismaReportDataId) {
        prismaReportDataIds.push(param.prismaReportDataId);
      } else if (param.investigatorId) {
        investigatorIds.push(param.investigatorId);
      } else if (param.groupId) {
        groupIds.push(param.groupId);
      } else if (param.personId) {
        const nairePersonIds_ = nairePersonIds.filter((o) => {
          return o.naireId === param.questionnaireId;
        });

        let nairePersonId = {
          naireId: "",
          ids: [],
          tipDetails: [],
        };
        if (nairePersonIds_.length > 0) {
          nairePersonId = nairePersonIds_[0];
        } else {
          nairePersonIds.push(nairePersonId);
          nairePersonId.naireId = param.questionnaireId;
        }

        if (param.isTipDetail) {
          nairePersonId.tipDetails.push({
            personId: param.personId,
            reportType: param.reportType,
          });
        } else {
          nairePersonId.ids.push(param.personId);
        }
      }
    }

    let paramssss: any = {
      investigatorIds,
      prismaReportDataIds,
      nairePersonIds,
      groupIds,
    };
    if (this.selectHeader.id === 1) {
      // 区分报告类型
      paramssss.type = "COMMON";
    } else if (this.selectHeader.id === 2) {
      paramssss.type = "GROUP";
    }
    if (
      params.length > 0 ||
      prismaReportDataIds.length > 0 ||
      groupIds.length > 0
    ) {
      const api = `${this.tenantUrl}/survey/download/dimensionScore/export/async`;
      this.loading.dimScore = true;
      this.http.post(api, paramssss).subscribe((res: any) => {
        if (res.result.code === 0) {
          if (this.selectHeader.id === 1) {
            // 区分报告类型
            this.interval$ = setInterval(() => {
              this.checkDimScoreApi(this.interval$, res.data);
            }, 5000);
          } else if (this.selectHeader.id === 2) {
            this.interval3$ = setInterval(() => {
              this.checkDimScoreApi(this.interval3$, res.data);
            }, 5000);
          }
        } else {
          this.loading.dimScore = false;
        }
      });
    } else {
      const api = `${this.tenantUrl}/survey/download/dimensionScore/export`;
      this.loading.dimScore = true;
      this.http
        .post(api, paramssss, {
          responseType: "blob",
          observe: "response",
        })
        .subscribe((data) => {
          this.loading.dimScore = false;
          this.rptService.downFile(data);
        });
    }
  }

  checkDimScoreApi(interval$, requestId) {
    const api =
      `${this.tenantUrl}/survey/download/dimensionScore/export/check?requestId=` +
      requestId;
    this.http.post(api, {}).subscribe((res: any) => {
      if (res.result.code === 0 && res.data) {
        clearInterval(interval$);
        this.downDimScoreApi(requestId);
      }
    });
  }

  downDimScoreApi(requestId) {
    const api =
      `${this.tenantUrl}/survey/download/dimensionScore/export/cache?requestId=` +
      requestId;
    this.http
      .post(
        api,
        {},
        {
          responseType: "blob",
          observe: "response",
        }
      )
      .subscribe((data) => {
        this.loading.dimScore = false;
        this.rptService.downFile(data);
      });
  }

  downloadAnswerData(params) {
    const investigatorIds = [];
    const prismaReportDataIds = [];
    const nairePersonIds = [];
    const groupIds = [];
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < params.length; i++) {
      const param = params[i];
      if (param.prismaReportDataId) {
        prismaReportDataIds.push(param.prismaReportDataId);
      } else if (param.investigatorId) {
        investigatorIds.push(param.investigatorId);
      } else if (param.groupId) {
        groupIds.push(param.groupId);
      } else if (param.personId) {
        const nairePersonIds_ = nairePersonIds.filter((o) => {
          return o.naireId === param.questionnaireId;
        });

        let nairePersonId = {
          naireId: "",
          ids: [],
          tipDetails: [],
        };
        if (nairePersonIds_.length > 0) {
          nairePersonId = nairePersonIds_[0];
        } else {
          nairePersonIds.push(nairePersonId);
          nairePersonId.naireId = param.questionnaireId;
        }

        if (param.isTipDetail) {
          nairePersonId.tipDetails.push({
            personId: param.personId,
            reportType: param.reportType,
          });
        } else {
          nairePersonId.ids.push(param.personId);
        }
      }
    }

    let paramssss: any = {
      investigatorIds,
      prismaReportDataIds,
      nairePersonIds,
      groupIds,
    };
    if (this.selectHeader.id === 1) {
      // 区分报告类型
      paramssss.type = "COMMON";
    } else if (this.selectHeader.id === 2) {
      paramssss.type = "GROUP";
    }

    if (
      params.length > 0 ||
      prismaReportDataIds.length > 0 ||
      groupIds.length > 0
    ) {
      const api = `${this.tenantUrl}/survey/download/answer/export/async`;
      this.loading.dimScore = true;
      this.http.post(api, paramssss).subscribe((res: any) => {
        if (res.result.code === 0) {
          if (this.selectHeader.id === 1) {
            // 区分报告类型
            this.interval2$ = setInterval(() => {
              this.checkAnswerApi(this.interval2$, res.data);
            }, 5000);
          } else if (this.selectHeader.id === 2) {
            this.interval4$ = setInterval(() => {
              this.checkAnswerApi(this.interval4$, res.data);
            }, 5000);
          }
        } else {
          this.loading.dimScore = false;
        }
      });
    } else {
      const api = `${this.tenantUrl}/survey/download/answer/export`;
      this.loading.answerData = true;
      this.http
        .post(api, paramssss, { responseType: "blob", observe: "response" })
        .subscribe((data) => {
          this.loading.answerData = false;
          this.rptService.downFile(data);
        });
    }
  }

  checkAnswerApi(interval$, requestId) {
    const api =
      `${this.tenantUrl}/survey/download/answer/export/check?requestId=` +
      requestId;
    this.http.post(api, {}).subscribe((res: any) => {
      if (res.result.code === 0 && res.data) {
        clearInterval(interval$);
        this.downAnswerApi(requestId);
      }
    });
  }

  downAnswerApi(requestId) {
    const api =
      `${this.tenantUrl}/survey/download/answer/export/cache?requestId=` +
      requestId;
    this.http
      .post(
        api,
        {},
        {
          responseType: "blob",
          observe: "response",
        }
      )
      .subscribe((data) => {
        this.loading.dimScore = false;
        this.rptService.downFile(data);
      });
  }

  // 导出标准报告
  downloadStandardFiles(param) {
    // 中台文件接口
    const api = `${this.tenantUrl}/file/downloadMultiFile`;

    // `${this.tenantUrl}/sagittarius/report/file/getFileIdsByUserIds`
    let getIdsApi = `${this.tenantUrl}/sagittarius/report/file/getFileIds`;
    this.http.post(getIdsApi, param).subscribe((res: any) => {
      let fileIds = [];
      let zipName = "";
      if (res && res.data) {
        if (res.data.batchs) {
          fileIds = res.data.batchs;
        }
        if (res.data.zipName) {
          zipName = res.data.zipName + ".zip";
        }
      }
      if (fileIds.length === 0) {
        // this.msgServ.error("暂无报告下载");
        this.customMsg.open("error", "暂无报告下载");

        return;
      }
      this.loading.zh_CN = true;
      this.loading.en_US = true;

      // 拆分下载参数
      // let downloadPageSize : number = 100;
      // let groups : any[] = _.chunk(fileIds, downloadPageSize);
      let groups: any[] = fileIds;
      this.loading.totol = groups.length;
      this.loading.done = 0;

      let obs: Observable<any>[] = [];
      for (let index = 0; index < groups.length; index++) {
        const curFileIds = groups[index];
        let sub = this.http.post(api, curFileIds, {
          responseType: "blob",
          observe: "response",
        });
        obs.push(sub);
      }

      concat(...obs).subscribe(
        (data) => {
          this.loading.done = this.loading.done + 1;
          if (this.loading.done === this.loading.totol) {
            this.loading.totol = 0;
            this.loading.zh_CN = false;
            this.loading.en_US = false;
          }
          this.rptService.downFileWithName(data, zipName);
        },
        (error1) => {
          this.loading.done = this.loading.done + 1;
          this.loading.zh_CN = false;
          this.loading.en_US = false;
        }
      );
    });
  }
  confirmReport() {
    this.reCreateReport();
  }
  cancelReport() {}
  // 重新生成报告
  reCreateReport() {
    let params = [];
    params = this.buildSelectParam();

    if (params.length === 0) {
      // this.msgServ.error("没有选择重新生成记录");
      this.customMsg.open("error", "没有选择重新生成记录");
      return;
    }
    let api = `${this.tenantUrl}/sagittarius/report/content/report/batchGenerate`;

    if (this.selectHeader.id === 1)
      api = `${this.tenantUrl}/sagittarius/report/content/report/batchGenerate`;
    if (this.selectHeader.id === 2)
      api = `${this.tenantUrl}/sagittarius/report/content/batchReCreateGroupReport`;
    this.loading.recreate = true;

    this.http.post(api, params).subscribe((res: any) => {
      this.loading.recreate = false;
      if (res.result.code === 0) {
        this.loadData(this.currentPage);
        this.msgServ.create("success", "重新生成报告已提交");
      }
    });
  }

  // 团队 下载pdf
  groupDownload(lineData: any, fileData: any, lan: string) {
    const api = `${this.tenantUrl}/file/downloadMultiFile`;
    fileData.isDownloading = true;
    let fileIds = [];
    lineData.reportListFileVo.reportFileVos.forEach((f) => {
      if (!lan || (lan && f.language === lan)) {
        fileIds.push(f.fileUrl);
      }
    });
    this.http
      .post(api, fileIds, { responseType: "blob", observe: "response" })
      .subscribe(
        (data) => {
          fileData.isDownloading = false;
          this.rptService.downFile(data);
        },
        (error) => {
          fileData.isDownloading = false;
        }
      );
  }

  // 团队 导出维度得分
  exportGroupDimensions(data) {
    let param: any = {};
    let key = data.groupId ? "groupId" : "prismaReportDataId";
    param[key] = data.id;
    let api =
      this.tenantUrl + "/sagittarius/report/dimension/score/export/group";
    data.dimDownloading = true;
    this.http
      .post(api, param, { responseType: "blob", observe: "response" })
      .subscribe(
        (resdata: any) => {
          data.dimDownloading = false;
          this.rptService.downFile(resdata);
        },
        (err) => {
          data.dimDownloading = false;
        }
      );
  }

  // 团队 导出原始答题
  exportGroupAnswerDatas(data) {
    let param: any = {};
    let key = data.groupId ? "groupId" : "prismaReportDataId";
    param[key] = data.id;
    let api = this.tenantUrl + "/survey/question/export/group/answer";
    data.answerDownloading = true;
    this.http
      .post(api, param, { responseType: "blob", observe: "response" })
      .subscribe(
        (resdata: any) => {
          data.answerDownloading = false;
          this.rptService.downFile(resdata);
        },
        (err) => {
          data.answerDownloading = false;
        }
      );
  }

  // 团队 重新生成
  redoGroupPdf(gId: string, data) {
    let param: any = { isReCalculate: true };
    let key = data.groupId ? "groupId" : "prismaReportDataId";
    param[key] = data.id;

    const api = `${this.tenantUrl}/sagittarius/report/content/reCreateGroupReport`;
    data.isCreating = true;
    this.http.post(api, param).subscribe(
      (res: any) => {
        data.isCreating = false;
        if (res.result.code === 0) {
          this.msgServ.success("重新生成已提交请求");
          this.loadData();
        }
      },
      (error) => {
        data.isCreating = false;
      }
    );
  }

  // 团队 上传几个方法
  normalClick(data) {
    this.currentNormalData = data;
  }

  /**
   * 点击上传按钮调用
   * @param gId
   * @param data : 仅用来控制进度条
   * @param lan : 语言
   */
  tdClick(gId: string, lineData: any, data: any, lan?: string) {
    this.currentGroupId = gId;
    this.currentGroupKey = lineData.groupId ? "groupId" : "prismaReportDataId";
    this.currentLanguage = lan;
    this.currentGroupData = data;
  }

  /**
   * customReq上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    let type = this.getFileType(item.file);

    let tmps: string[] = [];
    if (
      (this.selectHeader.id === 2 && this.currentGroupKey !== "groupId") ||
      (this.selectHeader.id === 1 && this.currentNormalData.prismaReportDataId)
    ) {
      tmps.push(".ppt");
      tmps.push(".pptx");
      tmps.push(".pdf");
    } else {
      tmps.push(".pdf");
    }
    if (!_.includes(tmps, type.toLowerCase())) {
      // this.msgServ.error("文件类型不支持。");
      this.customMsg.open("error", "文件类型不支持。");
      return;
    }
    let fileType = "PDF";
    if (".ppt" == type.toLowerCase() || ".pptx" == type.toLowerCase()) {
      fileType = "PPTX";
    }
    let Public = JSON.parse(sessionStorage.getItem("isEnableMoka"));

    formData.append("file", item.file as any);
    formData.append("isPublic", Public);
    formData.append("effectiveFileTypes", type);
    formData.append("businessType", "SAG_REPORT");
    this.uploadPdf(formData, fileType);
  };

  /**
   * 获取文件名后缀
   * @param item
   */
  getFileType(file: any): string {
    let type = "PDF";
    if (file && file.name) {
      let name: string = file.name;
      let names: string[] = name.split(".");
      if (names.length > 0) {
        type = names[names.length - 1];
      }
    }
    return `.${type}`;
  }

  /**
   * uploadPdf 上传pdf报告文件
   */
  uploadPdf(formData, fileType: string) {
    const url = `${this.tenantUrl}/survey/standard/file/uploadWithBusinessType`;
    return this.http.post(url, formData).subscribe(
      (res: any) => {
        if (res.result.code === 0) {
          const retData = res.data;
          if (this.selectHeader.id === 1) {
            this.updateStandardFile(retData.id, fileType);
          } else {
            this.updateGroupFile(retData.id, fileType);
          }
        }
      },
      (err) => {
        this.currentGroupData.isUploading = false;
        this.currentNormalData.isUploading = false;
      }
    );
  }

  /**
   * 更新标准报告的fileId
   */
  updateStandardFile(fileId: string, fileType: string) {
    let param: any = {
      fileId: fileId,
      language: "zh_CN",
      fileType: fileType,
    };

    let data = this.currentNormalData;

    let isCommon: boolean = data.personId;
    let is360: boolean = data.investigatorId;
    let idKey: string;
    let propertyName;
    if (isCommon) {
      idKey = data.personId;
      propertyName = "personId";
    } else if (is360) {
      idKey = data.investigatorId;
      propertyName = "investigatorId";
    }

    if (data.surveyType === "EMPLOYEE_ENGAGEMENT" || (!is360 && !isCommon)) {
      idKey = data.prismaReportDataId;
      propertyName = "prismaReportDataId";
    }
    param[propertyName] = idKey;
    param.projectId = data.projectId;
    param.questionnaireId = data.questionnaireId;
    param.reportType = data.reportType;

    this.currentNormalData.isUploading = true;
    const url = `${this.tenantUrl}/sagittarius/report/content/update/reportFile`;
    this.http.post(url, param).subscribe(
      (res: any) => {
        this.currentNormalData.isUploading = false;
        if (res.result.code === 0) {
          this.loadData();
          this.msgServ.success("上传成功");
        }
      },
      (err) => {
        this.currentNormalData.isUploading = false;
      }
    );
  }

  /**
   * 更新group的fileId
   */
  updateGroupFile(fileId: string, fileType: string) {
    let param: any = {
      fileId: fileId,
      language: this.currentLanguage,
      fileType: fileType,
    };
    param[this.currentGroupKey] = this.currentGroupId;

    this.currentGroupData.isUploading = true;
    const url = `${this.tenantUrl}/sagittarius/report/content/update/group/reportFile`;
    return this.http.post(url, param).subscribe(
      (res: any) => {
        this.currentGroupData.isUploading = false;
        if (res.result.code === 0) {
          this.loadData();
          this.msgServ.success("上传成功");
        }
      },
      (err) => {
        this.currentGroupData.isUploading = false;
      }
    );
  }

  /**
   * 生成团队报告(未使用)
   * @returns
   */
  openCreateDialog() {
    let params = [];
    params = this.buildSelectParam();
    params = _.filter(params, function(o) {
      return o.prismaReportDataId === undefined;
    });
    if (!params || params.length === 0) {
      // this.msgServ.error("没有选择测评人员");
      this.customMsg.open("error", "没有选择测评人员");
      return;
    }

    const modal = this.modalService.create({
      nzContent: GroupCreateComponent,
      nzComponentParams: {
        paramList: this.buildSelectParam(),
      },
      nzTitle: "创建团队报告",
      nzWidth: 1000,
      nzStyle: { padding: 0 },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzMaskClosable: false,
      nzClosable: true,
      nzFooter: null,
    });
  }

  /**
   * 创建组织报告（未使用）
   * @returns
   */
  openCreateSurvey() {
    let params = [];
    params = this.buildSelectParam();
    params = _.filter(params, function(o) {
      return o.prismaReportDataId !== undefined;
    });
    if (!params || params.length !== 1) {
      // this.msgServ.error("只能选择一个调研活动");
      this.customMsg.open("error", "只能选择一个调研活动");
      return;
    }

    const modal = this.modalService.create({
      nzContent: SurveyCreateComponent,
      nzComponentParams: {
        projectId: params[0].projectId,
      },
      nzTitle: null,
      nzFooter: null,
      nzWidth: 1000,
      nzStyle: { padding: 0 },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzMaskClosable: false,
      nzClosable: true,
      nzOnOk: () => {},
    });
  }

  // 生成对比报告

  private generateTree(arr: TransferItem[]): TransferItem[] {
    const tree: TransferItem[] = [];
    // tslint:disable-next-line:no-any
    const mappedArr: any = {};
    let arrElem: TransferItem;
    let mappedElem: TransferItem;
    for (let i = 0, len = arr.length; i < len; i++) {
      arrElem = arr[i];
      arrElem.key = arr[i].id;
      mappedArr[arrElem.id] = { ...arrElem };
      mappedArr[arrElem.id].children = [];
    }

    for (const id in mappedArr) {
      if (mappedArr.hasOwnProperty(id)) {
        mappedElem = mappedArr[id];
        if (mappedElem.parentid) {
          mappedArr[mappedElem.parentid].children.push(mappedElem);
        } else {
          tree.push(mappedElem);
        }
        // if(mappedElem.children && mappedElem.children.length === 0) {
        //   mappedElem.isLeaf = true
        // }
      }
    }

    tree.forEach((item) => {
      this.forEaechTree(item);
    });
    console.log(tree);

    return tree;
  }

  forEaechTree(node) {
    if (node.children.length !== 0) {
      node.children.forEach((item) => {
        this.forEaechTree(item);
      });
    } else {
      node.isLeaf = true;
    }
  }

  checkBoxChange(
    node: NzTreeNode,
    onItemSelect: (item: TransferItem) => void
  ): void {
    if (node.isDisabled) {
      return;
    }
    // 
    node.isChecked = !node.isChecked;
    if (node.isChecked) {
      const idx = this.checkedNodeList.find(
        (n) => n.origin.id === node.origin.id
      );
      if (!idx) {
        this.checkedNodeList.push(node);
      }
    } else {
      const idx = this.checkedNodeList.indexOf(node);
      if (idx !== -1) {
        this.checkedNodeList.splice(idx, 1);
      }
    }
    const item = this.list.find((w) => w.id === node.origin.id);
    onItemSelect(item!);
  }

  change(ret: TransferChange): void {
    // console.log(this.checkedNodeList);

    if (ret.to === "right") {
      this.checkedNodeList.forEach((node) => {
        node.isChecked = true;
        node.isDisabled = true;
      });
    } else {
      const arr = [];
      this.checkedNodeList.forEach((node, index) => {
        ret.list.map((item) => {
          if (item.id === node.origin.id) {
            node.isChecked = false;
            node.isDisabled = false;
            arr.push(index);
          }
        });
      });
      arr.reverse().map((idnex) => {
        this.checkedNodeList.splice(idnex, 1);
      });
    }
  }

  getDataList() {
    return this.isDataMode ? this.paramList : this.searchList;
  }

  showVisible(e, data) {
    console.log(e);
    if (!e) this.close(data);
  }

  showToolBox(data) {
    this.rptService
      .getComparePersonOrg(
        data.projectId,
        data.questionnaireId,
        data.reportType
      )
      .subscribe((res) => {
        if (res.result.code === 0) {
          let retData = res.data;
          let personList = retData.personList;
          let organizationList = retData.organizationList;

          let pId = data.personId || data.investigatorId;
          this.paramList = personList;
          this.pid = pId;
          this.projId = data.projectId;
          this.nairId = data.questionnaireId;
          if (personList && personList.length) {
            this.toolType = 1;
            for (let index = 0; index < this.paramList.length; index++) {
              const element = this.paramList[index];
              if (this.pid && this.pid === element.id) {
                element.isChecked = true;
              } else {
                element.isChecked = false;
              }
            }

            data.isShowToolBox = true;
          } else if (organizationList && organizationList.length) {
            this.toolType = 2;
            this.orgService.getParentList(data.projectId).subscribe((res) => {
              if (res.result.code === 0) {
                res.data.forEach((item) => {
                  item.parentid = item.parentOrganization
                    ? item.parentOrganization.id
                    : 0;
                  item.title = item.name.zh_CN;
                });
                this.list = res.data;
                this.treeData = this.generateTree(this.list);

                this.nzExpandedKeys = [];
                this.treeData.map((node) => {
                  this.nzExpandedKeys.push(node.id);
                });

                data.isShowToolBox = true;
              }
            });
          } else {
            // this.msgServ.error("不能创建对比报告。");
            this.customMsg.open("error", "不能创建对比报告。");
            data.isShowToolBox = false;
          }
        }
      });
  }

  close(data) {
    data.isShowToolBox = false;
    this.searchTreeValue = "";
    this.paramList = [];
    this.checkedNodeList = [];
    this.pid = "";
    this.isDataMode = true;
    this.searchList = [];
    this.searchText = "";
    this.projId = "";
    this.nairId = "";
  }

  isSearchStyle(text, searchTreeValue) {
    if (this.searchTreeValue) {
      console.log(text.indexOf(this.searchTreeValue));

      return text.indexOf(this.searchTreeValue) !== -1 ? "red" : "";
    } else {
      return "";
    }
  }

  toolOk(data) {
    let arrs = this.paramList;
    let ids: string[] = [];
    for (let index = 0; index < arrs.length; index++) {
      const element = arrs[index];
      if (element.isChecked) {
        ids.push(element.id);
      }
    }

    if (ids.length === 0) {
      // this.msgServ.error("没有选择创建记录。");
      this.customMsg.open("error", "没有选择创建记录。");
      return;
    }

    this.isToolBtnLoading = true;
    this.rptService
      .createCompareReport(this.projId, this.nairId, ids, data.reportType)
      .subscribe((res) => {
        this.isToolBtnLoading = false;
        if (res.result.code === 0) {
          this.msgServ.success("创建对比报告成功。");
          data.isShowToolBox = false;
        }
      });
  }

  toolOkPrisma(data) {
    let ids: string[] = [];
    for (let index = 0; index < this.checkedNodeList.length; index++) {
      let element: any = this.checkedNodeList[index];
      ids.push(element.origin.id);
    }
    if (ids.length === 0) {
      // this.msgServ.error("没有选择创建记录。");
      this.customMsg.open("error", "没有选择创建记录。");
      return;
    }

    this.isToolBtnLoading = true;
    this.rptService
      .createCompareReport(this.projId, this.nairId, ids, data.reportType)
      .subscribe((res) => {
        this.isToolBtnLoading = false;
        if (res.result.code === 0) {
          this.msgServ.success("创建对比报告成功。");
          data.isShowToolBox = false;
          this.checkedNodeList = [];
        }
      });
  }

  searchData() {
    let txt: string = this.searchText;
    if (txt.trim() !== "") {
      this.isDataMode = false;
      this.searchList = _.filter(this.paramList, function(item) {
        return (
          item.name && item.name.toLowerCase().indexOf(txt.toLowerCase()) > -1
        );
      });
    } else {
      this.isDataMode = true;
    }
  }

  /**
   * 创建团队报告
   * @returns
   */
  openUnionCreate() {
    let params = [];
    params = this.buildSelectParam();
    if (!params || params.length === 0) {
      // this.msgServ.error("没有选择创建的记录");
      this.customMsg.open("error", "没有选择创建的记录");
      return;
    }
    this.drawerService.create({
      nzContent: UnionCreateComponent,
      nzContentParams: {
        paramList: params,
      },
      nzTitle: "创建团队报告",
      nzWrapClassName: "round-right-drawer-nobody",
      nzWidth: 1000,
      // nzOkText: "保存",
      // nzCancelText: "取消",
      nzMaskClosable: false,
      nzClosable: true,
      // nzOnOk: () => {},
    });
  }

  showCompareDialog(data: any) {
    this.isVisible = true;

    this.chartId = data.id;
  }

  /**
   * 被测人信息
   * @param personId
   * @returns
   */
  userInfoShow(personId: string) {
    if (!personId) {
      return;
    }
    let usrInfo: any = {};
    const personUrl =
      this.tenantUrl +
      `/survey/person/getPersonDemographics?personId=${personId}`;
    this.http.get(personUrl).subscribe((res: any) => {
      if (res.result.code === 0) {
        usrInfo = res.data;
        let name: string = usrInfo.name;
        let usrFirstLetter = name.substring(0, 1);
        usrInfo.usrFirstLetter = usrFirstLetter;

        const modal = this.modalService.create({
          nzContent: UserinfoComponent,
          nzComponentParams: {
            content: usrInfo,
          },
          nzTitle: null,
          nzFooter: null,
          nzWidth: 400,
          nzStyle: { padding: 0 },
          nzOkText: null,
          nzCancelText: "取消",
          nzMaskClosable: false,
          nzClosable: true,
          nzOnCancel: () => {},
        });
      }
    });
  }

  handleCancel() {
    this.tips_vib = false;
    this.showErrorFlag = false;
  }

  getClose(data) {
    this.isVisible = data;
  }

  jumprun() {
    this.step1 = false;
    this.step2 = false;
    this.step3 = false;
    this.step4 = false;
    this.step5 = false;
    this.showmock = false;
    document.body.style.overflow = 'auto';
  }

  next1() {
    this.step1 = false;
    this.step2 = true;
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth'
    });
  }

  next2() {
    this.step2 = false;
    this.step3 = true;
  }

  next3() {
    this.step3 = false;
    this.step4 = true;
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  next4() {
    this.step4 = false;
    this.step5 = true;
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth'
    });
  }

  next5() {
    this.step5 = false;
    this.showmock = false;
    document.body.style.overflow = 'auto';
  }

  getnewlead() {
    this.step1 = true;
    this.showmock = true;
    // window.scrollTo({
    //   top: document.body.scrollHeight,
    //   behavior: 'smooth'
    // });
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
    document.body.style.overflow = 'hidden';
  }

  /**
   * 报告设置
   * @param data
   */
  openPrismaSet(data) {
    const modal = this.modalService.create({
      nzContent: PrismaSetComponent,
      nzComponentParams: {
        data: data,
        prismaReportDataId: data.prismaDataId,
        projectId: data.projectId,
      },
      nzTitle: null,
      nzFooter: null,
      nzWidth: 1000,
      nzStyle: { padding: 0 },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzMaskClosable: false,
      nzClosable: true,
      nzOnOk: () => {
        this.loadData();
      },
    });
  }

  closedreport(data, compared?: boolean) {
    // compared 对比报告修改
    let param: any = {
      prismaDataId: data.prismaDataId ? data.prismaDataId : "",
      groupId: data.groupId ? data.groupId : "",
    };
    if (compared) {
      param = {
        compareReportId: data.id,
      };
    }
    const url = `${this.tenantUrl}/sagittarius/report/content/toggle/report/visibility`;
    this.http.post(url, param).subscribe((res: any) => {
      if (res.result.code == 0) {
        this.loadData();
      }
    });
  }
  cancelreport() {}

  getAnglemark() {
    this.rptService.getReportScheduleInitCount().subscribe((res) => {
      if (res.result.code == 0) this.downloadnum = res.data;
    });
  }
  backclick(data) {
    this.downloadnum = data;
  }

  isShowDownLoad() {
    return (
      (this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_REPORT"
      ) ||
        this.knxFunctionPermissionService.has(
          "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_DIMENSION_SCORE"
        )) &&
      this.selectHeader.id === 1
    );
  }

  isShowBatchSend() {
    return (
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:BATCH_EMAIL_SEND"
      ) && this.selectHeader.id === 1
    );
  }

  isShowGroupDownLoad() {
    return (
      (this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_DIMENSION_SCORE"
      ) ||
        this.knxFunctionPermissionService.has(
          "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_REPORT"
        )) &&
      this.selectHeader.id === 2
    );
  }

  isShowGroupBatchSend() {
    return (
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:BATCH_EMAIL_SEND"
      ) && this.selectHeader.id === 2
    );
  }

  isValid(data) {
    if (data.isValid) {
      return false;
    } else {
      const arr = data.style
        ? data.style.filter(
            (val) =>
              val.url && val.reportStyle == "EMPTY" && val.name == "手出报告"
          )
        : [];
      if (arr.length > 0) return false;
      return true;
    }
  }

  isFail(data) {
    let failReport = data.style.filter(
      (val) => val.reportStatus === "REPORT_FAIL"
    );
    return failReport.length > 0 ? true : false;
  }

  isPermisionValid(data) {
    if (
      !this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:CREATE_COMPARE_REPORT"
      )
    ) {
      return true;
    }

    return this.isValid(data);
  }

  /**
   * 获取租户信息
   */
  getSelfInfo() {
    const infoApi = `${this.tenantUrl}/userAccount/user/getSelfInfo`;
    this.http.post(infoApi, {}).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.tenantId = res.data.tenantId;
      }
    });
  }
  /**
   * 生效失败提示
   */
  getReportFailCount() {
    let sub = null;
    if (this.selectHeader.id === 1) {
      sub = this.rptService.getPersonReportFailCount();
    } else if (this.selectHeader.id === 2) {
      sub = this.rptService.getGroupReportFailCount();
    }
    if (!sub) {
      return;
    }
    sub.subscribe(
      (res) => {
        if (res === 0) return;
        if (this.selectHeader.id === 1) {
          // 当前租户是否设置不在提示
          const isNoPrompt =
            localStorage.getItem(`isNoPrompt-1-${this.tenantId}`) || "false";
          // 标准
          const oldPersonReportFailCount = Number(
            localStorage.getItem(`personReportFailCount-${this.tenantId}`)
          );
          if (
            res > oldPersonReportFailCount ||
            (res === oldPersonReportFailCount && isNoPrompt === "false")
          ) {
            localStorage.setItem(
              `personReportFailCount-${this.tenantId}`,
              String(res)
            );
            localStorage.setItem(
              `isNoPrompt-${this.selectHeader.id}-${this.tenantId}`,
              "false"
            );
            // 生成失败提示
            setTimeout(() => {
              this.openBuildFailNotification(this.selectHeader.id);
            }, 0);
          }
        } else {
          const isNoPrompt =
            localStorage.getItem(`isNoPrompt-2-${this.tenantId}`) || "false";
          // 团队
          const oldGroupReportFailCount = Number(
            localStorage.getItem(`groupReportFailCount-${this.tenantId}`)
          );
          if (
            res > oldGroupReportFailCount ||
            (res === oldGroupReportFailCount && isNoPrompt === "false")
          ) {
            localStorage.setItem(
              `groupReportFailCount-${this.tenantId}`,
              String(res)
            );
            localStorage.setItem(
              `isNoPrompt-${this.selectHeader.id}-${this.tenantId}`,
              "false"
            );
            // 生成失败提示
            setTimeout(() => {
              this.openBuildFailNotification(this.selectHeader.id);
            }, 0);
          }
        }
      },
      (err) => {
        // this.msgServ.error(err.statusText);
        this.customMsg.open("error", err.statusText);
      }
    );
  }
  openBuildFailNotification(type): void {
    const data = {
      type: type,
      name: type === 1 ? "标准" : "团队",
    };
    this.notification.template(this.customNotification, {
      nzDuration: 0,
      nzClass: "buildFail",
      nzData: data,
    });
  }
  closeBuildFailNotification(isAgain, type) {
    if (isAgain) {
      localStorage.setItem(`isNoPrompt-${type}-${this.tenantId}`, "false");
    } else {
      localStorage.setItem(`isNoPrompt-${type}-${this.tenantId}`, "true");
    }
    if (type === 1) {
      this.personReportFailShoe = false;
    } else {
      this.groupReportFailShoe = false;
    }
    this.notification.remove();
  }
  getFilterFail(type) {
    this.buildFail = true;
    if (type === 1) {
      this.personReportFailShoe = false;
    } else {
      this.groupReportFailShoe = false;
    }
    this.notification.remove();
    // 搜索条件清除
    this.childNum = 0;
    this.advancedChild.clear();
    this.filterValue = "";
    this.filterType = "projectName";
    this.selectedValue = "";
    this.dateRange = [];
    this.currentPage = 1;
    this.search();
  }

  viewOnlineRep(data: any) {
    // 在线报告地址
    console.log(data);

    console.log(location, environment);
    const str = location.origin.split(":");
    let url: string = location.origin;
    /** !environment.production ? url = locationData.protocol +  : '' */
    // if(!data.url) {
    //   this.msgServ.warning("报告生成中");
    //   return;
    // }
    let token = this.tokenService.get().token;
    // window.open(`${environment.SERVER_URL}online_report/index?prismaReportDataId=${data.prismaReportDataId}&questionId=${data.questionId}&token=${token}`)
    window.open(
      `http://localhost:4200/online_report/?prismaReportDataId=1599601486153469953&tenantId=1549594714391007234&questionId=1581823844362682370&token=${token}`
    );
  }

  onOnlineReportPermission() {
    // this.msgServ.error("当前账号无看板权限!");
    this.customMsg.open("error", "当前账号无看板权限");
  }
  // 打开在线看板-列表
  goToOnlineReportList() {
    // this.router.navigateByUrl(`/report-manage/online-report`);
    let token = this.tokenService.get().token;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      // baseUrl = "https://sag-qa.knxdevelop.com/";
      baseUrl = "http://localhost:4200/";
    }
    const url = `${baseUrl}online_report/reportList?token=${token}`;
    window.open(url);
  }
  // 打开在线看板-详情
  goToOnlineReportDetail(prismaReportDataId: string) {
    let token = this.tokenService.get().token;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      // baseUrl = "https://sag-qa.knxdevelop.com/";
      baseUrl = "http://localhost:4200/";
    }
    const url = `${baseUrl}online_report/reportList/details?prismaReportDataId=${prismaReportDataId}&token=${token}`;
    // const url = `http://localhost:4200/online_report?prismaReportDataId=1738116031243829249&tenantId=${this.tenantId}&token=${token}`;
    // const url = `${baseUrl}online_report/?token=${token}`
    window.open(url);
  }

  // 是否显示立即观看&预约
  isShowReportExplain() {
    return (
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:VIDEO"
      ) ||
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:EXPERT_INTERPRETATION"
      )
    );
  }
  // 是否显示banner
  isShowBanner() {
    return (
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:VIDEO"
      ) ||
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:EXPERT_INTERPRETATION"
      ) ||
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:ONLINE_REPORT"
      )
    );
  }
  /**
   * 获取邮件功能是否展示
   *@author:longh
   *@Date:2024/12/31
   */
  getIsDisplayMail() {
    const url = `${this.tenantUrl}/survey/project/isOpenMailSend`;

    this.http.get(url).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.isDisplayMail = res.data;
      }
    });
  }

  /**
   * 获取账户信息
   */
  async getUserData() {
    try {
      const res2: any = await this.http
        .post(`${this.tenantUrl}/survey/user/listAll`, {})
        .toPromise();

      if (res2.result.code === 0) {
        this.userList = (res2.data.data || [])
          .filter((val: { username: string; id: number; type: string }) =>
            ["TENANT_ADMIN", "TENANT_USER", "DOMESTIC_CONSUMER"].includes(
              val.type
            )
          )
          .map((val: { username: string; id: number }) => ({
            username: val.username,
            id: val.id,
          }));
      } else {
        console.warn("Failed to fetch user list:", res2.result.message);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  }

  onUserFilterPopoverShow(value: boolean): void {
    if (!value) this.search();
  }
  // onUserFilterBtn(): void {
  //   this.search();
  //   this.userFilterVisible = false;
  // }
  // onUserClearBtn(): void {
  //   this.selectedUserValue = [];
  //   this.search();
  //   this.userFilterVisible = false;
  // }
  // nzOpenChange(value: boolean): void {
  //   console.log("nzOpenChange", value);
  // }
}
