.mark {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 3;
  background-color: black;
  opacity: 0.1;
}

.td-box {
  display: flex;

  p {
    flex: 1;
  }

  .icon-icon_edit {
    margin-left: 8px;
  }

  .icon-icon_edit:hover {
    color: #1890ff;
    cursor: pointer;
  }
}

// 测评对比报告弹窗
::ng-deep {
  .tool-box {
    max-width: 100%;

    .ant-tooltip-inner {
      padding: 0;
    }

    .ant-tooltip-arrow::before {
      background-color: #ffffff;
    }
  }
}

.tempCC {
  width: 367px;
  height: 391px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);

  /** 头部 **/
  .tooltip-header {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 362px;
    height: 56px;
    padding: 0 24px 0 20px;
    border-bottom: 1px solid #ececec;

    img {
      display: inline-block;
      width: 30px;
      height: 30px;
    }

    h1 {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #495970;
      line-height: 24px;
      margin: 0 10px;
    }

    span {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #a5a5a5;
      line-height: 24px;
      margin-right: 19px;
    }

    .close {
      width: 12px;
      height: 12px;
      cursor: pointer;
    }
  }

  .search-box {
    display: flex;
    justify-content: center;
    padding: 11px 0;

    .search-tool {
      width: 319px;
      height: 35px;
    }
  }

  .tool-scroll {
    height: 235px;
    overflow-y: auto;

    li {
      height: 40px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #495970;
      line-height: 20px;

      div {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
      }
    }

    li:nth-child(2n + 1) {
      background: #f9f9f9;
    }
  }

  .footer {
    text-align: right;
    padding: 0 24px;

    .toolBtn {
      width: 80px;
      height: 32px;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      padding-top: 3px;

      span {
        width: 32px;
        height: 22px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 22px;
      }
    }
  }
}

.tempCDiaoyan {
  width: 492px;
  height: 420px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);

  .tooltip-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    width: 485px;
    height: 56px;
    padding: 0 24px 0 20px;
    border-bottom: 1px solid #ececec;

    div {
      display: flex;
      align-items: center;
    }

    img {
      display: inline-block;
      width: 30px;
      height: 30px;
    }

    h1 {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #495970;
      line-height: 24px;
      margin: 0 10px;
    }

    span {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #a5a5a5;
      line-height: 24px;
      margin-right: 19px;
    }

    .close {
      width: 12px;
      height: 12px;
      cursor: pointer;
    }
  }

  ::ng-deep .tool-content {
    padding: 14px 16px 0 27px;

    .ant-transfer-list {
      width: 200px;
      height: 299px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #e6e6e6;
      overflow-x: auto;
      overflow-y: auto;
    }

    .ant-tree-child-tree {
      overflow: visible;
    }

    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      box-shadow: none;
      -webkit-border-radius: 2em;
      -moz-border-radius: 2em;
      border-radius: 2em;
    }

    ::-webkit-scrollbar-thumb {
      background-color: #c1c1c1;
      outline: none;
      -webkit-border-radius: 2em;
      -moz-border-radius: 2em;
      border-radius: 2em;
    }
  }

  .footer {
    text-align: right;
    height: 50px;
    line-height: 50px;
    padding-right: 24px;
  }
}

// 测评对比报告弹窗 ** end **

.report-manage {
  background: #ffffff;
  min-height: 100%;

  .content {
    position: relative;
    .interpretation {
      // 报告解读
      position: absolute;
      bottom: 60px;
      right: -50px;

      > div {
        position: relative;
        width: 35px;
        height: 35px;
        margin-top: 10px;
        cursor: pointer;

        // 视频解读弹窗
        .video-pop {
          position: absolute;
          left: -480px;
          bottom: -70px;
          z-index: 100;
          width: 468px;
          height: 244px;
          background: #ffffff;
          box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
          padding: 16px 20px;
          cursor: auto;
          border-radius: 4px;

          h1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #495970;
            line-height: 24px;
            border-bottom: 1px solid #ececec;
            padding-bottom: 16px;
            margin-bottom: 19px;
          }

          p {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #495970;
            line-height: 20px;
            margin-bottom: 15px;
          }

          .phone {
            margin-top: 30px;

            p {
              margin-bottom: 15px;
            }

            p.tips {
              margin-top: 10px;
              margin-bottom: 20px;
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #aaaaaa;
              line-height: 17px;
            }
          }

          footer {
            height: 68px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #ececec;
            margin-top: 28px;
            // text-align: right;
          }
        }

        // one on one
        .one-pop {
          position: absolute;
          left: -480px;
          bottom: -24px;
          z-index: 100;
          width: 468px;
          height: 364px;
          background: #ffffff;
          box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
          padding: 16px 20px;
          cursor: auto;
          border-radius: 4px;

          .empty {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 100%;
            height: 100%;
          }

          h1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #495970;
            line-height: 24px;
            border-bottom: 1px solid #ececec;
            padding-bottom: 16px;
            margin-bottom: 19px;
          }

          p {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #495970;
            line-height: 20px;
            margin-bottom: 15px;
          }

          .phone {
            margin-top: 30px;

            p {
              margin-bottom: 15px;
            }

            p.tips {
              margin-top: 10px;
              margin-bottom: 20px;
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #aaaaaa;
              line-height: 17px;
            }
          }

          footer {
            height: 68px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #ececec;
            margin-top: 0;

            h2 {
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #495970;
              line-height: 17px;

              span {
                font-size: 24px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #e1251b;
                line-height: 33px;
              }
            }

            p {
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #aaaaaa;
              line-height: 17px;
            }
          }
        }

        .pop-arrow {
          &:before {
            content: "";
            position: absolute;
            right: -16px;
            top: 150px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-left: 8px solid #ffffff;
            border-right: 8px solid transparent;
            border-bottom: 8px solid transparent;
          }
        }

        .pop-arrow-one {
          &:before {
            content: "";
            position: absolute;
            right: -16px;
            top: 315px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-left: 8px solid #ffffff;
            border-right: 8px solid transparent;
            border-bottom: 8px solid transparent;
          }
        }
      }
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    padding: 0 103px 0 109px;
    border-radius: 4px;
    border: 1px solid #e4e4e4;

    .item {
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 500;
      color: #495970;
      line-height: 28px;
      height: 70px;

      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-color: white;

      // &:hover {
      //     // border: solid #409EFF 2px;
      //     // border-radius: 12px;
      // }
      > span {
        white-space: nowrap;
      }
    }

    .selected {
      position: relative;

      &::after {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        content: "";
        width: 281px;
        height: 2px;
        background: #409eff;
        border-radius: 4px;
      }
    }
  }

  .search {
    // margin-top: 20px;
    // padding-right: 15px;
    background-color: white;
    width: 100%;
    // min-height: 70px;
    padding: 16px 0;

    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      margin-left: 10px;
      font-size: 26px;
      font-weight: 600;
    }

    .condition {
      // padding-top: 5px;
      // padding-right: 10px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      ::ng-deep {
        .box {
          width: 100px !important;
        }
      }
      .fuzzySearch {
        margin-right: 8px;
        width: 220px;

        ::ng-deep {
          > .ant-input-group {
            display: flex;
          }

          .ant-select-selection {
            width: 100px;
          }

          .searchIcon {
            color: #409eff;
            cursor: pointer;
            font-size: 20px;
          }

          .ant-input {
            width: 120px;
          }
        }
      }

      .build {
        height: 32px;
        display: flex;
        align-items: center;
        margin-left: 8px;

        ::ng-deep {
          .ant-checkbox-wrapper {
            margin-left: 0;
          }
        }
      }

      .newlead {
        color: #bfbfbf;
        font-size: 14px;
        cursor: pointer;
        margin-left: 8px;

        &:hover {
          color: #409eff;
        }
      }
    }

    .clearBtn {
      // width: 104px;
      // height: 36px;
      padding: 4px 16px;
      white-space: nowrap;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 4px;
      // display: flex;
      // justify-content: center;
      // align-items: center;
      cursor: pointer;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
    }
  }

  .data {
    background-color: white;
    // border: 1px solid #ECECEC;
    border-radius: 2px;
    // border-right: 1px solid #ECECEC;

    .report-table {
      a {
        color: #409eff;
      }

      th {
        color: #495970;
        background: #f3f7fb;
        white-space: nowrap;
        height: 40px;
        padding: 10px;
        font-size: 15px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        line-height: 22px;
        // border-left: 1px solid #ECECEC;

        nz-switch {
          margin-left: 9px;
        }
      }

      .th1 {
        text-align: center;
        width: 70px;
      }

      td {
        padding: 10px;
        // border-left: 1px solid #ECECEC;
      }

      .userInfoShow {
        cursor: pointer;
      }
    }
  }

  .page {
    min-height: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 20px;
    background-color: white;

    .action {
      flex: 1;
      padding-left: 15px;
      display: flex;
      flex-wrap: wrap;
      min-height: 35px;
      align-items: center;
      background: rgba(64, 158, 255, 0.07);
      border-radius: 2px;
      border: 1px solid #b7daff;

      .personNum {
        color: dodgerblue;
        margin-left: 10px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        margin-right: 15px;
        min-width: 30px;
        text-align: right;
      }

      // button:hover {
      //     color: white !important;
      //     background-color: #1890FF;
      //     border-radius: 30px;
      //     padding-left: 20px;
      //     padding-right: 20px;
      // }

      .newBtn:hover {
        color: white;
        background-color: #1890ff;
      }
    }

    nz-pagination {
      margin: 0 15px;
    }
  }
}

.maxW {
  max-width: 280px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  margin-right: 10px;
}

.nowrap {
  white-space: nowrap;
}

.inprogress {
  padding-top: 4px;
}

.groupFile {
  padding-left: 4px;
  // padding-top: 5px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;

  .view-report {
    display: flex;
  }

  .title {
    background-color: #1890ff;
    padding: 2px 15px;
    margin-top: 5px;
    margin-bottom: 5px;
    white-space: nowrap;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    color: white;
  }

  a {
    padding-top: 3px;
    margin-right: 5px;
    //  &:hover {
    //     color: white !important;
    //     background-color: #1890FF;
    //     border-radius: 4px;
    //  }
  }

  button {
    padding: 2px 0px;
    // &:hover {
    //   color: white;
    //   background-color: #1890FF;
    // }
  }
}

.tips_icon {
  text-align: center;
}

.tips_title {
  color: #17314c;
  font-size: 24px;
  line-height: 33px;
  text-align: center;
  margin: 24px auto 19px auto;
}

.tips_text {
  font-size: 14px;
  color: #17314c;
  line-height: 24px;
  text-align: center;
}

.iptBtn {
  width: 128px;
  height: 38px;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  padding-top: 3px;

  span {
    width: 32px;
    height: 22px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 22px;
  }
}

:host ::ng-deep nz-select {
  margin-right: 8px;
  width: 120px;
}

.mock_div {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 9999;

  .bg_ul {
    width: 100%;
    height: 100%;
    background-color: #000000;
    opacity: 0.6;
  }

  .img_ul {
    position: absolute;
    top: 420px;
    left: 340px;
    color: #fff;
  }

  .img_ul_1 {
    position: absolute;
    bottom: 50px;
    left: 335px;
    color: #fff;
  }

  .img_ul_2 {
    position: absolute;
    bottom: 50px;
    left: 335px;
    color: #fff;
  }

  .img_ul_3 {
    position: absolute;
    top: 465px;
    right: 340px;
    color: #fff;

    // img {
    //   width: 80%;
    // }
  }

  .img_ul_4 {
    position: absolute;
    top: 275px;
    right: 340px;
    color: #fff;
  }

  .img_ul,
  .img_ul_1,
  .img_ul_2,
  .img_ul_3,
  .img_ul_4 {
    .btn_div {
      display: flex;
      margin-top: 20px;

      > div {
        cursor: pointer;
      }

      .left_d {
        width: 104px;
        line-height: 36px;
        border-radius: 19px;
        text-align: center;
        border: 1px solid #ffffff;
      }

      .right_d {
        margin-left: 20px;
        width: 104px;
        line-height: 36px;
        border-radius: 19px;
        text-align: center;
        background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      }
    }
  }
}

:host ::ng-deep {
  .ant-calendar-picker-input {
    width: 340px;
  }
}

::ng-deep .ant-popover-title {
  padding: 10px 16px 15px 16px !important;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #495970;
}

.configImg {
  &:hover {
    content: url("../../../../assets/images/org/col_set_active.png");
  }
}

.thSetting {
  // width:100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.tooltipMargin {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: groove 1px gainsboro;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  box-shadow: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

.projectName {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &_text1 {
    width: 200px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &_text2 {
    // width: 190px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
  }

  .showTag {
    width: 140px;
  }
}

.tag {
  &_invalid {
    font-size: 12px;
    font-weight: 400;
    border-radius: 2px;
    padding: 2px 5px;
    color: #8c8c8c;
    white-space: nowrap;
    background: rgba(140, 140, 140, 0.1);
    margin-left: 10px;
  }

  &_fail {
    font-size: 12px;
    font-weight: 400;
    border-radius: 2px;
    padding: 2px 5px;
    color: #f84444;
    white-space: nowrap;
    background: rgba(248, 68, 68, 0.08);
    margin-left: 10px;
  }
}

::ng-deep .buildFail {
  .custom-top {
    display: flex;

    i {
      color: #f84444;
      font-size: 24px;
      margin-right: 16px;
    }

    h3 {
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 8px;
    }
  }

  .custom-bottom {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .ant-notification-notice-close {
    display: none;
  }
}

::ng-deep {
  .round-right-drawer-nobody {
    .ant-drawer-body {
      padding: 0;
      height: calc(100% - 123px);
      overflow: auto;
      .vxscrollbar();
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }

    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}

//滚动条
.vxscrollbar() {
  scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #f1f1f1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #c1c1c1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

.iconbtn {
  padding: 0 8px;
}

.banner {
  margin-bottom: 20px;
  &-item {
    position: relative;
    &-img {
      width: 100%;
      min-height: 168px;
      background: linear-gradient(135deg, #f5faff 0%, #ffebb6 100%);
      img {
        width: 100%;
        background: #fff;
      }
    }
    .right-operate {
      position: absolute;
      width: 100%;
      bottom: 22px;
      display: flex;
      justify-content: center;
      .right-btn {
        background: #ffd665;
        border-color: #ffd665;
        color: #9a7100;
        &:hover {
          opacity: 0.8;
        }
      }
    }
    .left-operate {
      position: absolute;
      width: 100%;
      bottom: 22px;
      display: flex;
      justify-content: flex-start;
      &-video{
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        line-height: 20px;
        padding: 5px 17px;
        background: #806cfe;
        border-radius: 4px;
        margin-left: 40px;
        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
      }
      &-expert{
        font-size: 14px;
        font-weight: 400;
        color: #17314c;
        line-height: 20px;
        padding: 5px 17px;
        cursor: pointer;
        &:hover {
          // opacity: 0.8;
          color: #806cfe;
        }

      }
    }
  }
}

::ng-deep {
  .report-home-right-drawer {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 108px);
      overflow: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
.videoBox {
  &-text {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 22px;
    margin-bottom: 16px;
  }
  &-select {
    width: 100%;
    margin-bottom: 16px;
  }
  &-group {
    width: 100%;
    label {
      width: 100%;
      font-size: 14px;
      font-weight: 400;
      color: #262626;
      line-height: 20px;
      margin-bottom: 12px;
    }
  }
  &-footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 10px 16px;
    text-align: right;
    left: 0px;
    background: #fff;
  }
}
.oneBox {
  &-text {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 22px;
    margin-bottom: 16px;
  }
  &-select {
    width: 100%;
    margin-bottom: 16px;
  }
  &-group {
    width: 100%;
    label {
      width: 100%;
      font-size: 14px;
      font-weight: 400;
      color: #262626;
      line-height: 20px;
      margin-bottom: 12px;
    }
  }
  &-input {
    width: 100%;
    margin-bottom: 8px;
  }
  &-tip {
    font-size: 12px;
    font-weight: 400;
    color: #595959;
    line-height: 17px;
  }
  &-footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 10px 16px;
    text-align: right;
    left: 0px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-left {
      font-size: 14px;
      font-weight: 400;
      color: #262626;
      line-height: 20px;
      span {
        font-size: 20px;
        font-weight: 400;
        color: #ff4f40;
        line-height: 28px;
        margin: 0 8px;
      }
    }
  }
  &-empty {
    height: calc(100vh - 140px);
  }
}

.operator-btn {
  font-family: PingFangSC, PingFang SC;
  font-size: 12px;
  color: #8c8c8c;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  padding: 1px 8px 2px 8px !important;
  max-width: 80px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  background: #f5f5f5;
  border-radius: 10px;
  border: 1px solid #dfdfdf;
}
.comparison-operation {
  .ant-btn {
    padding: 0;
  }
}

.create-btn {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e4e4e4;
  padding: 3px 11px;
  display: flex;
  width: 100px;
  cursor: pointer;
  margin-left: 8px;
  .arrow {
    font-size: 12px;
    color: #bfbfbf;
    transition: transform 0.3s;
    display: flex;
    align-items: center;
  }
  .arrow-open {
    transform: rotate(180deg);
    color: #1890ff;
  }
  .text-blue {
    color: #1890ff;
  }
}
.btn-disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
  opacity: 1;
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}
::ng-deep {
  .create-tooltip {
    max-width: 100%;
    // .ant-tooltip-inner {
    // min-width: 350px;
    // }
  }
}

// 滚动列表无数据显示
::ng-deep {
  .table-empty{
    .ant-table-body{
      overflow-x: hidden !important;
    }
    .ant-table-placeholder{
      border-left: 1px solid #e8e8e8;
      border-right: 1px solid #e8e8e8;
    }
  }
}