import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { LoginService } from "@src/modules/login/login.service";
import { HttpClient } from "@angular/common/http";

@Component({
  selector: 'app-video',
  templateUrl: './video.component.html',
  styleUrls: ['./video.component.less']
})

export class VideoComponent implements OnInit {

  reportType: string
  isGroup: boolean = false
  queryParams: any
  cosurl: string

  /********header*******/
  tourist:boolean=true;
  isNeedLogin:boolean=false;
  token:any;
  _token:any;
  tenantUrl: string = "/tenant-api";

  constructor(private route: ActivatedRoute,private loginService:LoginService,private router: Router,private http: HttpClient
  ) { }

  ngOnInit() {
    this._token = JSON.parse(localStorage.getItem("_token"));
    if (this._token) {
        this.tourist=false;
        this.isNeedLogin=true;
    }
    this.route.queryParams.subscribe(params=>{
      this.reportType = params['reportType']
      this.isGroup = params['isGroup']
          console.log(this.reportType, this.isGroup)
      })
    this.getVideoUrl()
  }

  getVideoUrl() {
    const url = `${this.tenantUrl}/survey/standard/report/view/getByReportTypeAndGroup?reportType=${this.reportType}&isGroup=${this.isGroup}&_allow_anonymous=true`
    this.http.get(url).subscribe((res: any)=> {
      if(res.result.code === 0) {
        this.cosurl = res.data.cosUrl
      }
    })
  }

}