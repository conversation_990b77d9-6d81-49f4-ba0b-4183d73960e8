import { HttpClient } from "@angular/common/http";
import { Component, Input, OnInit, OnDestroy } from "@angular/core";
import { NzMessageService, NzDrawerRef } from "ng-zorro-antd";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-answer-setting",
  templateUrl: "./answer-setting.component.html",
  styleUrls: ["./answer-setting.component.less"],
})
export class AnswerSettingComponent implements OnInit, OnDestroy {
  @Input() projectId: string;
  @Input() standardQuestionnaireId: string;
  @Input() disableSave: boolean;
  @Input() isEnableRoleDimension: boolean;
  @Input() isShowPerson: boolean;
  projectInfo: any = {};
  nairInfo: any = {};
  isLoading = false;
  permission: boolean;
  tenantUrl = "/tenant-api";

  typeList: any[] = [
    { id: "S", name: "S" },
    { id: "A", name: "A" },
    { id: "B", name: "B" },
    { id: "C", name: "C" },
  ];

  personPropertyList: any[] = [];
  showType: string = "";
  scoreSelectedType: string = "";

showTabs = [
  { key: 'rank', label: '显示排名', model: 'isShowRank' },
  { key: 'score', label: '显示得分', model: 'isShowScore' },
  { key: 'understand', label: '显示"不了解"', model: 'isShowUnderstand' },
  { key: 'person', label: '显示人员', model: 'isShowPerson' }
];
  activeShowTab = 'rank'; // 默认激活tab
  commentTabs = [
  { key: 'totalScore', label: '开启总分评语', model: 'isTotalScoreComment' },
  // { key: 'everyEvaluatee', label: '开启每位被评估人评语', model: 'isEveryEvaluateeComment' }
];
  activeCommentTab = 'totalScore';
  scoreTabs = [
  { key: 'mandatoryRank', label: '开启强制排名', model: 'isEnableMandatoryRank' },
  { key: 'scoreDistribution', label: '开启得分分布', model: 'isEnableScoreDistribution' }
];
activeScoreTab = 'mandatoryRank';
  disanswerMode: boolean = false;
  personDemographics: any[] = ["NAME"];
  private routerSubscription: Subscription;
  constructor(
    private drawerRef: NzDrawerRef,
    private http: HttpClient,
    private msg: NzMessageService,
    private customMsg: MessageService,
        public permissionService: PermissionService,
    private router: Router
  ) {}

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    // this.permission = 'true';
    this.bgGetProjInfo();
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });

  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
  add() {
    this.nairInfo.scoreDistribution.push({
      level: "S",
      personPercent: "",
      lowScorePercent: "",
      highScorePercent: "",
      isGuaranteed: false,
    });
  }
  newadd() {
    this.nairInfo.conditions.totalScoreCommentConditionList.push({
      symbol: "GREATER_THAN",
      value: 0,
    });
  }

  delete(index: number) {
    this.nairInfo.scoreDistribution.splice(index, 1);
  }

  newdelete(i) {
    if (this.nairInfo.conditions.totalScoreCommentConditionList.length > 1) {
      this.nairInfo.conditions.totalScoreCommentConditionList.splice(i, 1);
    }
  }

  ok() {
    // check validate info
    if (this.permission  && this.nairInfo.isEnableScoreDistribution) {
      let arrs: any[] = this.nairInfo.scoreDistribution;
      for (let index = 0; index < arrs.length; index++) {
        const obj = arrs[index];
        let prefix = `得分分布设置：`;
        if (
          !obj.lowScorePercent ||
          !obj.highScorePercent ||
          !obj.personPercent
        ) {
          // this.msg.error(prefix + "值不能为空。");
          this.customMsg.open("error", `${prefix}值不能为空。`);
          return;
        }
        if (obj.lowScorePercent > obj.highScorePercent) {
          // this.msg.error(prefix + `低值不能大于高值。`);
          this.customMsg.open("error", `${prefix}低值不能大于高值。`);
          return;
        }
      }
      // 保底值必填
      const isHaveGuaranteed = this.nairInfo.scoreDistribution.filter(val => val.isGuaranteed).length > 0; 
      if (!isHaveGuaranteed) {
        this.customMsg.open("error", "请选择1个保底值！");
        return;
      }
    }
    if (this.permission && this.nairInfo.isTotalScoreComment) {
      let arrs: any[] = this.nairInfo.conditions.totalScoreCommentConditionList;
      for (let index = 0; index < arrs.length; index++) {
        const obj = arrs[index];
        if (obj.value === "") {
          // this.msg.error(`总分设置值不能为空`);
          this.customMsg.open("error", "总分设置值不能为空");
          return;
        }
      }
    }
    if (this.nairInfo.isShowPerson && this.nairInfo.personDemographics) { 
      this.personDemographics = this.personPropertyList.filter(item => item.checked).map(item => item.code);
      let selectedNum = 0;
      this.personPropertyList.forEach(item => {
        if (item.checked && item.name !== "姓名") {
          selectedNum++;
        }
      });
      if (selectedNum > 2) {
        this.customMsg.open("error", "显示人员，除姓名外最多勾选两项");
        return;
      }
    }

    this.bgSaveProjInfo();
  }

  guChanged(e, detail) {
    if (e) {
      let arrs: any[] = this.nairInfo.scoreDistribution;
      for (let index = 0; index < arrs.length; index++) {
        const obj = arrs[index];
        if (obj.level !== detail.level) {
          obj.isGuaranteed = false;
        }
      }
    }
  }

  bgGetProjInfo() {
    const api = `${this.tenantUrl}/survey/questionnaire/getQuestionnaireInformation`;
    let newParam = {
      projectId: this.projectId,
      standardQuestionnaireId: this.standardQuestionnaireId,
      editFlag: true,
    };
    this.http.post(api, newParam).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.projectInfo = res.data;
        this.nairInfo = this.projectInfo.surveyQuestionnaire;
        if (!this.nairInfo.conditionRelation)
          this.nairInfo.conditionRelation = "AND";
        if (!this.nairInfo.conditions) {
          this.nairInfo.conditions = {
            totalScoreCommentConditionList: [
              {
                symbol: "GREATER_THAN",
                value: 0,
              },
            ],
          };
        }
        if(this.nairInfo.isShowRank && !this.nairInfo.rankSort) { 
          this.nairInfo.rankSort = "ASC";//默认得分从大到小正序
        }
        if (this.projectInfo.projectAnalysisFactorDtos) {
          for (let item of this.projectInfo.projectAnalysisFactorDtos) {
            if (item.name.zh_CN === "姓名") {
              this.personPropertyList.push({ code: item.id, name: "姓名", checked: true });
              this.personDemographics.push(item.id);
            } else { 
              this.personPropertyList.push({ code: item.id, name: item.name.zh_CN, checked: false });
            }
          }
        } else { 
          this.personPropertyList = [{ code: "NAME", name: "姓名", checked: true }];
        }
        if(!this.nairInfo.personDemographics) { 
          this.personDemographics  = ["NAME"];
        } else { 
          this.personDemographics  = this.nairInfo.personDemographics;
        }
        this.personPropertyList.forEach(item => {
          if (this.personDemographics.includes(item.code)) {
            item.checked = true;
          }
        });
        let roleDimensions = this.projectInfo.relationPermissions.filter(
          (item) => {
            return item.type == "ROLE_DIMENSION";
          }
        );
        if (roleDimensions.length != 0 && this.isEnableRoleDimension) {
          // this.nairInfo.answerMode = 'SINGLE';
          this.disanswerMode = true;
        }
       
      }
    });
  }

  bgSaveProjInfo() {
    const api = `${this.tenantUrl}/survey/questionnaire/updateQuestionnaireSetting`;
    
    let param = {
      answerMode: this.nairInfo.answerMode,
      isHideRole: this.nairInfo.isHideRole,
      isShowRank: this.nairInfo.isShowRank,
      isShowScore: this.nairInfo.isShowScore,
      isShowUnderstand: this.nairInfo.isShowUnderstand,
      questionnaireId: this.nairInfo.id,
      isEnableScoreDistribution: this.nairInfo.isEnableScoreDistribution,
      scoreDistribution: this.nairInfo.scoreDistribution,
      conditions: this.nairInfo.conditions,
      conditionRelation: this.nairInfo.conditionRelation,
      isTotalScoreComment: this.nairInfo.isTotalScoreComment,
      isShowPerson: this.nairInfo.isShowPerson,
      personDemographics: this.personDemographics,
      rankSort: this.nairInfo.rankSort,
      isEnableMandatoryRank: this.nairInfo.isEnableMandatoryRank,
      mandatoryRankType: this.nairInfo.mandatoryRankType,
      isEveryEvaluateeComment: this.nairInfo.isEveryEvaluateeComment || false,
    };
    this.isLoading = true;
    this.http.post(api, param).subscribe((res: any) => {
      this.isLoading = false;
      if (res.result.code === 0) {
        this.drawerRef.close();
      }
    });
  }


 // 切换 tab 并自动勾选 checkbox
  checkShowTab(key: string, event: boolean) {
    this.activeShowTab = key;
    const tab = this.showTabs.find(t => t.key === key);
    if (tab && !this.nairInfo[tab.model] && event !==null) {
      this.nairInfo[tab.model] = event;
    }
  }

  checkCommentTab(key: string, event: boolean) {
  this.activeCommentTab = key;
  const tab = this.commentTabs.find(t => t.key === key);
  if (tab && !this.nairInfo[tab.model] && event !==null) {
    this.nairInfo[tab.model] = event;
  }
}
  
checkScoreTab(key: string, event: boolean) {
  this.activeScoreTab = key;
  const tab = this.scoreTabs.find(t => t.key === key);
  if (tab && !this.nairInfo[tab.model] && event !==null) {
  
    this.nairInfo[tab.model] = event;
  }
   if( this.activeScoreTab =="scoreDistribution"&& event){
       this.nairInfo['isShowRank'] = true;
    }
}

  checkPersonProperty(code: string, checked: boolean) {
    this.personPropertyList.find(item => item.code === code).checked = checked;
    this.nairInfo.personDemographics = this.personPropertyList.filter(item => item.checked).map(item => item.code);
  }
  checkMandatoryRank(code: string) {
    if(this.nairInfo.mandatoryRankType.indexOf(code) === -1) { 
      this.nairInfo.mandatoryRankType.push(code);
    } else { 
      this.nairInfo.mandatoryRankType = this.nairInfo.mandatoryRankType.filter(item => item !== code);
    }
  }

}
