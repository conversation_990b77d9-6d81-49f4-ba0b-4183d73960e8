.topicDistribution360 {
  * {
    font-family: <PERSON><PERSON>angSC, PingFang SC;
  }
  &-content {
    height: 100%;
    display: flex;
    justify-content: space-between;
    gap: 16px;
    &-card {
      flex: 1;
      height: 100%;
      background-color: #fff;
      border-radius: 8px;
      border: 1px solid #ececec;
      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        height: 54px;
        border-bottom: 1px solid #ececec;
        h3 {
          font-weight: 500;
          font-size: 16px;
          color: #262626;
          line-height: 22px;
          span {
            font-weight: 400;
            font-size: 14px;
            color: #595959;
            line-height: 20px;
            margin-left: 16px;
          }
        }
        .btn-primary {
          padding: 4px 16px;
          border-radius: 6px;
          // border: 1px solid #409eff;
          // color: #409eff;
          font-size: 12px;
        }
        .btn-link {
          font-size: 14px;
          color: #409eff;
          padding: 0;
        }
        button {
          line-height: 20px;
          background: #fff;
          cursor: pointer;
          &:hover {
            opacity: 0.7;
          }
        }
      }
      &-body {
        height: calc(100% - 54px);
        padding: 16px;
        overflow: auto;
        .vxScrollbar();
        .item {
          border: 1px dashed #ececec;
          border-radius: 2px;
          margin-bottom: 16px;
          &-title {
            background: #f8f8f8;
            padding: 10px 16px;
            display: flex;
            align-items: center;
            span {
              font-weight: 600;
              font-size: 14px;
              color: #17314c;
              line-height: 20px;
            }
          }
          &-label {
            padding: 10px 16px;
          }
          &-content {
            padding: 16px 16px 8px 16px;
            border-top: 1px dashed #ececec;
            h1 {
              font-weight: 400;
              font-size: 14px;
              color: #595959;
              line-height: 20px;
              text-align: center;
              margin-bottom: 8px;
            }
            &-checkbox {
              label {
                width: 100%;
                display: flex;
                align-items: center;
                margin: 2px;
                ::ng-deep {
                  span {
                    &:first-child {
                      margin-top: 3px;
                    }
                    &:last-child {
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    }
                  }
                }
              }
            }
          }
        }
        .template-1 {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin-bottom: 16px;
          button {
            margin-left: 24px;
          }
        }
        .template-2 {
          border: 1px solid #ececec;
          border-radius: 2px;
          margin-bottom: 16px;
          &-title {
            background: #f5faff;
            padding: 10px 16px;
            display: flex;
            align-items: center;
            span {
              font-weight: 400;
              font-size: 14px;
              color: #262626;
              line-height: 20px;
            }
          }
          &-content {
            padding: 16px 8px 8px 16px;
            display: flex;
            flex-wrap: wrap;
            ::ng-deep {
              .ant-tag {
                margin-right: 8px;
                margin-bottom: 8px;
                color: #262626;
                background-color: #ececec;
                border-color: #ececec;
                padding: 2px 8px;
                border-radius: 6px;
              }
            }
          }
        }
        .template-3 {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 100%;
          &-empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            img {
              margin-top: 60px;
              width: 182px;
              height: 173px;
            }
            p {
              font-weight: 400;
              font-size: 14px;
              color: #253238;
              line-height: 20px;
              margin-top: 24px;
              margin-bottom: 28px;
            }
          }
          &-tip {
            display: flex;
            flex-direction: column;
            align-items: center;
            span {
              font-weight: 400;
              font-size: 14px;
              background: #ffffff;
              box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
              border-radius: 8px;
              color: #595959;
              line-height: 22px;
              padding: 8px 24px 8px 16px;
              display: flex;
              align-items: center;
              i {
                margin-right: 8px;
              }
              .success {
                color: #01c320;
              }
              .error {
                color: #ff4f40;
              }
            }
          }
        }
        .template-4 {
          display: flex;
          justify-content: space-between;
          background: #f8f8f8;
          min-height: 40px;
          padding: 10px 16px;
          margin-bottom: 16px;
          &-file {
            display: flex;
            align-items: center;
            cursor: default;
            i {
              margin-right: 4px;
              font-size: 12px;
              color: #8c8c8c;
            }
            p {
              font-weight: 400;
              font-size: 14px;
              color: #262626;
              line-height: 20px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 290px;
            }
            span {
              margin-left: 2px;
              font-weight: 400;
              font-size: 12px;
              color: #8c8c8c;
              line-height: 20px;
            }
          }
          .ing {
            width: 120px !important;
          }
          &-progress {
            display: flex;
            align-items: center;
            &-bar {
              width: 150px;
              ::ng-deep {
                .ant-progress-inner {
                  background-color: #ddebf9;
                }
              }
            }
            &-text {
              margin-left: 16px;
              font-weight: 400;
              font-size: 12px;
              color: #409eff;
              line-height: 17px;
            }
          }
          &-btns {
            display: flex;
            align-items: center;
            // i {
            //   // margin-left: 16px;
            //   font-size: 14px;
            //   color: #409eff;
            //   cursor: pointer;
            // }
          }
        }
      }
    }
  }
}

//滚动条
.vxScrollbar() {
  scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #f1f1f1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #c1c1c1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}
::ng-deep {
  .round-right-drawer5 {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 108px);
      overflow: auto;
      .vxScrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer5-nofooter {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow: auto;
      .vxScrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-size: 20px;
      font-weight: bold;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
  &_shade {
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.05) 0%,
      rgba(255, 255, 255, 0) 100%
    );
    width: 960px;
    height: 38px;
    position: absolute;
    top: -48px;
    left: -16px;
  }

  span {
    width: 128px;
    height: 38px;
    text-align: center;
    line-height: 38px;
    border-radius: 19px;
    margin-right: 25px;
    cursor: pointer;
  }

  .primary {
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    color: #fff;
  }

  .default {
    background-color: #fafafa;
    color: #aaaaaa;
    font-weight: 500;
  }
}
::ng-deep {
  .ant-spin-nested-loading,
  .ant-spin-blur,
  .ant-spin-container {
    height: 100%;
  }
}
