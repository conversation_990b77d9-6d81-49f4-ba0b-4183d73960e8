@text-color: #17314c;

.content {
    width: 100%;
}

.s1 {
    width: 100%;
    background-color: #f5f6fa;

    &>div {
        margin: 0 auto;
        padding: 60px 0;
        display: flex;
    }

    .s1-l {
        flex: 1;
        // padding-right: 100px;
    }

    .s1-r {
        flex: 1;
        padding-left: 100px;
    }

    h5 {
        font-size: 30px;
        line-height: 42px;
        margin-bottom: 25px;

        span {
            font-size: 12px;
            color: #409eff;
            border-radius: 14px;
            padding: 2px 10px;
            margin-left: 30px;
            background-color: rgba(64, 158, 255, 0.1);
        }
    }

    p {
        font-size: 14px;
        line-height: 24px;
        margin-bottom: 80px;
    }

    .btn {
        width: 160px;
        height: 38px;
        line-height: 38px;
        background: linear-gradient(90deg,
                rgba(38, 208, 241, 1) 0%,
                rgba(64, 158, 255, 1) 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        font-size: 16px;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
    }
}

.s2 {
    width: 100%;
    background-color: #ffffff;
    text-align: center;

    &>div {
        margin: 0 auto;
        padding: 60px 0;
    }

    h5 {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        margin: 40px 0 50px;
    }
}

.s3 {
    width: 100%;
    background-color: #f5f6fa;
    padding: 70px 0;
    .s3-main {
        margin: 0 auto;
        padding: 60px 0;
        display: flex;
    }

    h5 {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 60px 0 60px;
        // background: url(../../../../assets/images/q.png) no-repeat center;
    }

    .s3-l {
        flex: 1;
        font-size: 16px;
        
        li {
            position: relative;
            line-height: 96px;

            div {
                width: 180px;
                display: inline-block;
            }

            span {
                font-size: 16px;
                color: #409eff;
                background-color: #f1f8ff;
                border-radius: 46px;
                padding: 10px 25px;
            }

            &:not(:last-child)::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 0;
                height: 1px;
                width: 60%;
                background: linear-gradient(to right, #ffffff, #ececec, #ffffff);
            }
        }
    }

    .s3-r {
        flex: 1;

        img {
            margin-top: 30px;
        }
    }
}

.s4 {
    width: 100%;
    background-color: #fff;

    .s5-main {
        margin: 0 auto;
        padding: 60px 0;
        display: flex;
    }

    h5 {
        font-size: 24px;
        font-weight: bold;
        padding: 50px 0 40px;
        text-align: center;
    }

    .s5-r {
        flex: 1;
        font-size: 16px;
        padding: 10%;

        p {
            position: relative;
            font-size: 16px;
            line-height: 55px;
            padding-left: 20px;
        }
    }

    .s5-l {
        width: 60%;

        img {
            margin-top: 30px;
        }

        text-align: center;
    }

    .btn {
        margin: 20px 0;

        button {
            width: 160px;
            height: 38px;
            line-height: 38px;
            background: linear-gradient(90deg,
                    rgba(38, 208, 241, 1) 0%,
                    rgba(64, 158, 255, 1) 100%);
            box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
            border-radius: 19px;
            font-size: 16px;
            font-weight: bold;
            color: #ffffff;
            cursor: pointer;
        }
    }
}


.tip_name {
    width: 310px;
    height: 38px;
    line-height: 38px;
    color: #fff;
    text-align: center;
    font-size: 16px;
    background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
    border-radius: 24px 24px 24px 0px;
}

.tip_ul {
    display: flex;
    margin-top: 15px;

    li {
        display: flex;
        align-items: center;

        em {
            width: 14px;
            height: 14px;
            background: linear-gradient(180deg, #0E65FE 0%, #28CAFD 100%);
            border-radius: 9px;
        }

        span {
            color: #17314c;
            font-size: 16px;
            margin-left: 5px;
        }
    }
}

.ground_f {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 10px 20px 0px rgba(161, 180, 199, 0.3);
    border-radius: 10px;
    padding-bottom: 60px;
}