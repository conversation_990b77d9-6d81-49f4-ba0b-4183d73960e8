<div class="index content client-width">
  <div
    *ngIf="step1 || step4"
    style="width: 100%;height: 60px;position: relative;margin-top: -60px;"
  >
    <div class="img_pos" *ngIf="step1">
      <img src="assets/images/showed.png" style="width: 220px;" alt="" />
      <p>点击这里，回到首页</p>
      <ul class="btn_ul">
        <li class="btn_left" (click)="jumprun()">跳过</li>
        <li class="btn_right" (click)="next1()">下一步</li>
      </ul>
    </div>
    <div class="img_pos_2" *ngIf="step4">
      <img src="assets/images/showed_l.png" alt="" />
      <p>点击这里，查看账户余额</p>
      <ul class="btn_ul">
        <li class="btn_left" (click)="jumprun()">跳过</li>
        <li class="btn_right" (click)="next4()">我知道了</li>
      </ul>
    </div>
  </div>
  <div *ngIf="newShow && array.length != 0" class="tips_div">
    <img src="assets/images/carousel.png" alt="" />
    <nz-carousel
      [nzEffect]="effect"
      class="carousel"
      [nzAutoPlaySpeed]="3000"
      [nzDots]="false"
      [nzAutoPlay]="true"
      (nzAfterChange)="nzAfterChange($event)"
    >
      <div
        class="carousel_div"
        nz-carousel-content
        *ngFor="let item of array; let i = index"
        (click)="choosediv(i)"
      >
       {{ item }}
      </div>
    </nz-carousel>
    <div style="color: #409EFF;cursor: pointer;flex: 1;" (click)="closeNews()">
      关闭消息
    </div>
  </div>
  <p class="index-title">
    欢迎使用测评调研云平台<img
      style="cursor: pointer;margin-left: 15px;"
      src="assets/images/shownew.png"
      (click)="getnewlead()"
      alt=""
    />
  </p>
  <ul class="user-status">
    <li
      class="new-ctivity"
      [ngClass]="step2 && hiddencard == 1 ? 'set1class' : ''"
    >
      <a routerLink="/new-activity">
        <img src="assets/images/add_activity.png" class="add_img" />
        <p class="new-act-tit">新建活动</p>
      </a>
      <div class="div_pos" *ngIf="hiddencard == 1">
        <img src="assets/images/icon_tip.png" alt="" />
        <p>点击卡片，开启活动，快去新建吧～</p>
        <div class="btn_ul">
          <div class="btn_left" (click)="jumprun()">跳过</div>
          <div class="btn_right" (click)="next2('2')">下一步</div>
        </div>
      </div>
    </li>

    <li
      class="user-kendou"
      [ngClass]="step2 && hiddencard == 2 ? 'set1class' : ''"
    >
      <a (click)="gotoactivemange()">
        <div class="title">
          <p class="p1">活动管理</p>
          <p class="activity_num">全部：{{ projectCount }}个</p>
        </div>
        <div class="new_pros">
          <div>
            <p style="font-size: 20px;font-weight: 400;text-align: center;">
              {{ announcedCount }}
            </p>
            <p style="color: #A5A5A5;">未发布</p>
          </div>
          <div style="border-left: 1px solid #eee ;height: 20px;"></div>
          <div>
            <p style="font-size: 20px;font-weight: 400;text-align: center;">
              {{ runningCount }}
            </p>
            <p style="color: #A5A5A5;">进行中</p>
          </div>
        </div>
        <!-- <div class="progress_text">进行中</div>
                <nz-progress [nzPercent]="process" [nzStrokeColor]="{ '0%': '#26D0F1', '100%': '#409EFF' }"
                    nzStatus="active"></nz-progress>
                <img src="assets/images/arrows.png" class="arrows" /> -->
      </a>
      <div class="div_pos" *ngIf="hiddencard == 2">
        <img
          src="assets/images/icon_tip.png"
          alt=""
          style="margin-left: 100px;"
        />
        <p>点击卡片，进入活动管理，追踪活动进程～</p>
        <div class="btn_ul" style="margin-left: 70px;">
          <div class="btn_left" (click)="jumprun()">跳过</div>
          <div class="btn_right" (click)="next2('3')">下一步</div>
        </div>
      </div>
    </li>

    <li
      class="user-report"
      [ngClass]="step2 && hiddencard == 3 ? 'set1class' : ''"
    >
      <a routerLink="/report-manage">
        <div class="title">
          <p class="p1">报告管理</p>
        </div>
        <div class="num">
          <div class="num-txt">
            <p class="p3">{{ resData?.reportSummary }}</p>
            <p class="p4">全部</p>
          </div>
        </div>
        <img src="assets/images/report.png" class="img_report" />
        <img src="assets/images/arrows.png" class="arrows" />
      </a>
      <div class="div_pos" *ngIf="hiddencard == 3">
        <img
          src="assets/images/icon_tip.png"
          alt=""
          style="margin-left: 100px;"
        />
        <p>点击卡片，进入报告管理，查看测评或调研报告～</p>
        <div style="display: flex;justify-content: flex-end;">
          <div class="btn_ul">
            <div class="btn_left" (click)="jumprun()">跳过</div>
            <div class="btn_right" (click)="next2('4')">下一步</div>
          </div>
        </div>
      </div>
    </li>
  </ul>

  <p class="index-title">产品分类及介绍</p>
  <div>
    <ul class="card_body">
      <li class="left_li">
        <div
          *ngFor="let tab of tabs; let i = index"
          class="menus_card"
          [ngClass]="selectedIndex == i ? 'selected_card' : ''"
          (click)="nzSelectChange(i)"
        >
          {{ tab }}
        </div>
      </li>
      <li class="cards_li">
        <ul
          class="recommend-check"
          *ngIf="selectedshow"
          style="position: relative;"
        >
          <ul class="big_user" *ngIf="step3">
            <div class="spance_div">
              <img
                style="width: 439px;height: 339px;"
                src=" assets/images/showed_5.png"
                alt=""
              />
              <div style="margin-top: 100px;margin-left: -60px;">
                <p>点击了解详情，浏览产品介绍</p>
                <div class="btn_ul">
                  <div class="btn_left" (click)="jumprun()">跳过</div>
                  <div class="btn_right" (click)="next3()">下一步</div>
                </div>
              </div>
            </div>
          </ul>
          <li
            *ngIf="
              selectedIndex == 0 ||
              selectedIndex == 1 ||
              selectedIndex == 2 ||
              selectedIndex == 3 ||
              selectedIndex == 4 ||
              selectedIndex == 6
            "
          >
            <img src="assets/images/index_check2.png" class="img1" />
            <p class="p1">EPA职业性格测评</p>
            <p class="p2">在职业情境下的工作风格、特点和偏好表现出的行为</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/epa">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <li
            *ngIf="
              selectedIndex == 0 ||
              selectedIndex == 1 ||
              selectedIndex == 2 ||
              selectedIndex == 3 ||
              selectedIndex == 5
            "
          >
            <img src="assets/images/index_check3.png" class="img1" />
            <p class="p1">AMA工作成就动机测评</p>
            <p class="p2">追求成功和卓越的内部驱动力来源</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/ama">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <li
            *ngIf="
              selectedIndex == 0 ||
              selectedIndex == 1 ||
              selectedIndex == 4 ||
              selectedIndex == 5
            "
          >
            <img src="assets/images/t_home_check6.png" class="img1" />
            <p class="p1">CA胜任力测评</p>
            <p class="p2">以能力为导向的现代人力资源管理体系的基础</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/ca">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <li *ngIf="selectedIndex == 2 || selectedIndex == 3">
            <img src="assets/images/t_mhs.png" class="img1" />
            <p class="p1">MHS心理健康筛查测评</p>
            <p class="p2">对工作最可能产生不利影响的关键心理健康风险</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/mhs">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>

          <li *ngIf="selectedIndex == 2 || selectedIndex == 3">
            <img src="assets/images/t_home_check5.png" class="img1" />
            <p class="p1">CSI危机筛查测评</p>
            <p class="p2">已存在或将会出现威胁正常工作和生活的危险因素</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/csi">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <li *ngIf="selectedIndex == 2 || selectedIndex == 3">
            <img src="assets/images/index_check4.png" class="img1" />
            <p class="p1">AT能力测试</p>
            <p class="p2">能否完成和胜任职场任务的基础素质情况</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/at">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>

          <li
            *ngIf="
              selectedIndex == 0 || selectedIndex == 1 || selectedIndex == 5
            "
          >
            <img src="assets/images/t_home_check7.png" class="img1" />
            <p class="p1">PWVO工作价值观取向测评</p>
            <p class="p2">对工作或职业抱有的态度和内在信念</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/pwvo">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>

          <li *ngIf="selectedIndex == 5">
            <img src="assets/images/t_home_check8.png" class="img1" />
            <p class="p1">PCA性格与职业测评</p>
            <p class="p2">工作中展现的性格特征以及适宜的职业领域</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/pca">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>

          <li *ngIf="selectedIndex == 1 || selectedIndex == 6">
            <img src="assets/images/t_home_check9.png" class="img1" />
            <p class="p1">PTA性格类型测评</p>
            <p class="p2">在职场中表达自我的形式和偏好的应对风格</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/pta">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>

          <li
            *ngIf="
              selectedIndex == 0 || selectedIndex == 1 || selectedIndex == 4
            "
          >
            <img src="assets/images/t_home_check10.png" class="img1" />
            <p class="p1">360°行为反馈</p>
            <p class="p2">综合多个评估角色的评价实现全方位分析</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/s360">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>

          <li
            *ngIf="
              selectedIndex == 0 || selectedIndex == 1 || selectedIndex == 4
            "
          >
            <img src="assets/images/t_home_check12.png" class="img1" />
            <p class="p1">270°行为反馈</p>
            <p class="p2">借助不同角色的他评反馈综合评估工作表现</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/s270">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>

          <li
            *ngIf="
              selectedIndex == 0 || selectedIndex == 1 || selectedIndex == 4
            "
          >
            <img src="assets/images/t_home_check11.png" class="img1" />
            <p class="p1">360°培养反馈</p>
            <p class="p2">基于不同评价角色综合反馈培训后提升情况</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/train">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>

          <li
            *ngIf="
              selectedIndex == 0 ||
              selectedIndex == 1 ||
              selectedIndex == 3 ||
              selectedIndex == 4
            "
          >
            <img src="assets/images/t_mca.png" class="img1" />
            <p class="p1">MCA“领航”测评</p>
            <p class="p2">管理和领导他人时展现的风格与特点</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/mca">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <li *ngIf="selectedIndex == 7">
            <img src="assets/images/t_prisma.png" class="img1" />
            <p class="p1">5G员工敬业度调研</p>
            <p class="p2">员工对组织及其使命持有的积极态度</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/prisma">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <li *ngIf="selectedIndex == 7">
            <img src="assets/images/t_prisma_cultrue.png" class="img1" />
            <p class="p1">企业文化调研</p>
            <p class="p2">洞悉企业内部共同的思维和行为模式</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/prismaCulture">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <!-- <li *ngIf="selectedIndex == 7">
            <img src="assets/images/t_orgSurvey_s1.png" class="img1" />
            <p class="p1">组织能力调研</p>
            <p class="p2">了解公司组织健康度</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/orgSurvey">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <li *ngIf="selectedIndex == 7">
            <img src="assets/images/t-doubleperspective.png" class="img1" />
            <p class="p1">双视角组织诊断</p>
            <p class="p2">通过组织能力和敬业度双时间洞悉企业内部情况</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/doublePerspective">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li> -->
          <li *ngIf="selectedIndex == 2 || selectedIndex == 3">
            <img src="assets/images/t_tip.png" class="img1" />
            <p class="p1">TIP人才画像测评</p>
            <p class="p2">结合各类资料深度分析，更有针对性地识别人才</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/tip">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <li *ngIf="selectedIndex == 6">
            <img src="assets/images/t_pdp.png" class="img1" />
            <p class="p1">PDP个人特质动态系统测验</p>
            <p class="p2">个人的自我特质、能量风格及动态变化</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/pdp">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
          <li *ngIf="selectedIndex == 0 || selectedIndex == 1">
            <img src="assets/images/t_sjtpt.png" class="img1" />
            <p class="p1">SJTP潜能情境测试</p>
            <p class="p2">对特定情境或问题的看法与处理方式</p>
            <div class="bottom">
              <a routerLink="/new-activity">
                <div class="btn">即刻使用</div>
              </a>
              <a routerLink="/tourist/sjtp">
                <p class="learn-more">了解详情</p>
              </a>
            </div>
          </li>
        </ul>
        <ul
          *ngIf="!selectedshow"
          style="display: flex;justify-content: center;align-items: center;height: 408px;"
        >
          <nz-spin [nzSpinning]="!selectedshow" nzSimple></nz-spin>
        </ul>
      </li>
    </ul>
  </div>
  <div class="new_div">
    <div class="div_img">
      <img src="assets/images/home_img.png" alt="" />
      <div class="abso_div">
        <p>解决方案</p>
        <ul class="big_ul">
          <li>
            <div style="padding-bottom: 30px;margin-right: 25px;">
              <div class="tip_titile">
                <span class="left_s">校招</span>
                <span class="right_s" routerLink="/tourist/xiaozhao"
                  >解决方案 ></span
                >
              </div>
              <div style="margin-top: 20px;">
                聚焦天资和性格倾向，考察应届求职者的潜在优势
              </div>
            </div>
            <div
              style="padding-bottom: 30px;border-left: 1px solid #DDE8F3;padding-left: 25px;"
            >
              <div class="tip_titile">
                <span class="left_s">社招</span>
                <span class="right_s" routerLink="/tourist/shezhao"
                  >解决方案 ></span
                >
              </div>
              <div style="margin-top: 20px;">
                对标岗位优秀人才画像，快速筛选适合岗位的人才
              </div>
            </div>
          </li>
          <li style="border-top: 1px solid #DDE8F3;">
            <div style="padding-top: 30px;margin-right: 25px;">
              <div class="tip_titile">
                <span class="left_s">盘点</span>
                <span class="right_s" routerLink="/tourist/rencai"
                  >解决方案 ></span
                >
              </div>
              <div style="margin-top: 20px;">
                通过自评或他评形式，全面有效评估员工能力水平
              </div>
            </div>
            <div
              style="padding-top: 30px;border-left: 1px solid #DDE8F3;padding-left: 25px;"
            >
              <div class="tip_titile">
                <span class="left_s">培养</span>
                <span class="right_s" routerLink="/tourist/culture"
                  >解决方案 ></span
                >
              </div>
              <div style="margin-top: 20px;">
                诊断员工能力现状，把握培养重点，量化培训成果
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <div class="mock_div " *ngIf="showmock">
    <ul class="bg_ul" *ngIf="showmock"></ul>
    <ul class="img_ul" *ngIf="noviceGuidance">
      <li>
        <div style="position: relative;">
          <img src="assets/images/dele_bg.png" alt="" />
          <div
            style="position: absolute;top: 180px;left: 0;right: 0;display: flex;flex-direction: column;align-items: center;"
          >
            <div class="btn_div" (click)="experience()">
              即刻体验
            </div>
            <div style="margin-top: 20px;">
              <label nz-checkbox [(ngModel)]="showchecked">跳过演示</label>
            </div>
          </div>
        </div>
        <div style="margin-top: 20px;cursor: pointer;" (click)="closed()">
          <img src="assets/images/dele_new.png" alt="" />
        </div>
      </li>
    </ul>
  </div>
</div>
