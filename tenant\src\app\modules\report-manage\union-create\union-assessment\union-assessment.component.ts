import { HttpClient } from "@angular/common/http";
import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd";
import { ReportService } from "../../report.service";
import { Observable } from "rxjs";
import { NzPopoverDirective } from "ng-zorro-antd";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-union-assessment",
  templateUrl: "./union-assessment.component.html",
  styleUrls: ["./union-assessment.component.less"],
})
export class UnionAssessmentComponent implements OnInit {
  @ViewChild(NzPopoverDirective, { static: false }) popover: NzPopoverDirective;

  // @Input() paramList: any[];

  @Input() createList: any[] = [];

  langage: string = "zh_CN";

  fileName: any = {
    zh_CN: null,
    en_US: null,
  };

  detailList: any[] = [];

  searchList: any[] = [];

  currentNairId: string = "";
  currentItem: any = null; // 选中
  searchText: string = "";

  isAgree: boolean = true;

  isDataMode: boolean = true;

  taskRunning: boolean = false;

  dash: string = "__";

  tenantUrl: string = "/tenant-api";

  mappingReportId: string = ""; // 人才地图报告id
  mappingReportPersonCount: number = 0; // 人才地图报告人数
  sudokuData: any[] = []; // 九宫格数据;
  sudokuDataCache: any[] = []; // 九宫格数据缓存;
  popoverVisible: boolean = false;
  isResetObjectSettingItemIds = []; //需要提示更新的九宫格
  constructor(
    private msgServ: NzMessageService,
    private http: HttpClient,
    private rptService: ReportService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    this.getServerData();
  }

  getServerData() {
    this.markReturnedData();
  }

  markReturnedData() {
    let firstItem = null;
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      // test common service
      // this.getQuestionnareCommonInfo(element.standardNairId);

      element.isSelect = false;
      element.isCurrent = false;
      if (index === 0) {
        firstItem = element;
        // element.isSelect = true;
      }
      for (let j = 0; j < element.detailList.length; j++) {
        const detail = element.detailList[j];
        detail.key = !!detail.personId
          ? detail.personId
          : detail.investigatorId;
      }
    }
    this.clickNair(firstItem);
  }

  // 此功能改到后端
  getQuestionnareCommonInfo(id: string) {
    const quesUrl =
      this.tenantUrl +
      `/survey/standard/questionnaire/getGroupReportInfo/${id}`;
    this.http.get(quesUrl).subscribe((res: any) => {
      if (res.result.code === 0) {
        let tmpData = res.data;
        console.info(
          `questionnaire id = ${id},  data = ` + JSON.stringify(tmpData)
        );
      }
    });
  }

  changefileName() {
    this.createList.forEach((item) => {
      if (this.currentNairId === item.nairId) {
        item.fileName = {
          zh_CN: this.fileName.zh_CN,
          en_US: this.fileName.en_US,
        };
      }
    });
  }

  clickNair(item) {
    const { nairId } = item;
    this.langage = "zh_CN";
    this.currentNairId = nairId;
    this.currentItem = item;
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      if (nairId === element.nairId) {
        element.isCurrent = true;
        this.detailList = element.detailList;
        if (element.fileName) {
          this.fileName.zh_CN = element.fileName.zh_CN;
          this.fileName.en_US = element.fileName.en_US;
        } else {
          this.fileName.zh_CN = null;
          this.fileName.en_US = null;
        }
      } else {
        element.isCurrent = false;
      }
    }
    this.isDataMode = true;
    this.searchText = "";
  }

  delPerson(data: any) {
    // 删除搜索结果中数据
    _.remove(this.searchList, function(userData) {
      return userData.key === data.key;
    });
    this.searchList = [...this.searchList];

    // 搜索结果删完，则显示剩余数据
    if (this.searchList.length === 0) {
      this.isDataMode = true;
      this.searchText = "";
    }

    // 删除当前table中数据
    _.remove(this.detailList, function(userData) {
      return userData.key === data.key;
    });
    this.detailList = [...this.detailList];

    // 删除问卷中数据
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      if (this.currentNairId === element.nairId) {
        element.isCurrent = true;
        let dList: any[] = element.detailList;
        _.remove(dList, function(userData) {
          return userData.key === data.key;
        });
        break;
      }
    }
  }

  searchData() {
    let txt: string = this.searchText;
    if (txt.trim() !== "") {
      this.isDataMode = false;
      this.searchList = _.filter(this.detailList, function(item) {
        return (
          item.name && item.name.toLowerCase().indexOf(txt.toLowerCase()) > -1
        );
      });
    } else {
      this.isDataMode = true;
    }
    this.searchList = [...this.searchList];
    this.detailList = [...this.detailList];
  }

  getDataList() {
    return this.isDataMode ? this.detailList : this.searchList;
  }

  getTotal() {
    let total: number = 0;
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      if (element.isSelect) {
        let curPrice: number = 2000;
        if (!!element.groupStyleType) {
          curPrice = element.cost;
        }
        total = total + curPrice;
      }
    }
    return total;
  }

  buildParam(): any[] {
    let nairCount: number = 0;
    for (let index = 0; index < this.createList.length; index++) {
      let element = this.createList[index];
      if (element.isSelect) {
        let dList: any[] = element.detailList;
        // if(dList.length === 0) {
        //   this.msgServ.error(`[ ${element.nairName.zh_CN} ]，选择人员为空，不能创建报告。`);
        //   return;
        // }
        nairCount++;
      }
    }

    let tmpAttr: any[] = _.filter(this.createList, function(item) {
      return item.isSelect;
    });

    // 360自定义 standardNairId nairId 中包含 '__'

    let param: any[] = [];
    tmpAttr.forEach((createVo) => {
      let newVo = JSON.parse(JSON.stringify(createVo));
      this.handleDash(newVo, "nairId");
      this.handleDash(newVo, "standardNairId");
      let newArr: any[] = [];
      for (let index = 0; index < newVo.detailList.length; index++) {
        const element = newVo.detailList[index];
        let obj: any = {
          personId: element.personId,
          investigatorId: element.investigatorId,
          name: element.name,
          projectId: element.projectId,
          questionnaireId: element.questionnaireId,
        };
        newArr.push(obj);
      }
      newVo.detailList = newArr;
      param.push(newVo);
    });

    return param;
  }

  handleDash(data: any, propName: string) {
    let val: string = data[propName];
    if (val && val.indexOf(this.dash) > -1) {
      let attrs: string[] = val.split(this.dash);
      let tmp = attrs[0];
      data[propName] = tmp;
    }
  }

  bgMakeGeneralGroupReport(json): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/makeGeneralGroupReport`;
    return this.http.post(api, json);
  }

  // 创建人才地图
  createMappingReport(item) {
    if (!!this.mappingReportId) {
      this.getSudokuList();
      return;
    }
    if (!item.talentMappingNairList.length) {
      // this.msgServ.warning("工具不足！");
      this.customMsg.open("warning", "工具不足");
      return;
    }
    const params = {
      name: item.nairName,
      nairList: item.talentMappingNairList.map((val) => ({
        id: val.nairId,
        name: val.nairName,
        code: val.reportType,
      })),
    };
    this.rptService.createMappingReport(params).subscribe((res) => {
      this.mappingReportId = res.data;
      this.addSudoku();
    });
  }
  // 九宫格列表
  getSudokuList() {
    this.rptService
      .getMappingReportSudokuList(this.mappingReportId)
      .subscribe((res) => {
        this.sudokuData = res.data.itemList;
        this.sudokuDataCache = _.cloneDeep(this.sudokuData);
        this.getMappingReportPersonCount();
      });
  }
  // 增加九宫格
  addSudoku() {
    // 创建人才地图
    if (!this.mappingReportId) {
      if (!this.fileName.zh_CN) {
        // this.msgServ.warning("请输入报告名称！");
        this.customMsg.open("warning", "请输入报告名称");
        return;
      }
      if (!!this.currentItem.talentMappingNairList) {
        this.createMappingReport(this.currentItem);
      }
    } else {
      // 增加九宫格
      for (let index = 0; index < this.sudokuData.length; index++) {
        if (
          !this.sudokuData[index].id ||
          !this.sudokuData[index].isDisplayComplete ||
          !this.sudokuData[index].isObjectComplete
        ) {
          // this.msgServ.warning("请完成当前九宫格配置");
          this.customMsg.open("warning", "请完成当前九宫格配置");
          return;
        }
      }
      this.sudokuData.push({
        name: {
          en_US: "",
          zh_CN: "",
        },
        reportId: this.mappingReportId,
      });
      this.sudokuDataCache = _.cloneDeep(this.sudokuData);
    }
  }
  changeSudokuName(item) {
    if (item.name.zh_CN.trim() == "") {
      // this.msgServ.warning("九宫格名称不能为空！");
      this.customMsg.open("warning", "九宫格名称不能为空");
      return;
    }
    if (item.id) {
      // 处理复制文本信息时会触发的问题
      const current = this.sudokuDataCache.find((val) => val.id == item.id);
      if (current.name.zh_CN == item.name.zh_CN) return;
      this.updateMappingReportSudoku(item);
    } else {
      this.createMappingReportSudoku(item);
    }
  }
  updateMappingReportSudoku(item) {
    this.rptService.updateMappingReportSudoku(item).subscribe((res) => {
      this.getSudokuList();
    });
  }
  createMappingReportSudoku(item) {
    this.rptService.createMappingReportSudoku(item).subscribe((res) => {
      this.getSudokuList();
    });
  }

  closeRnderPopover(e?, itemId?) {
    if (e != undefined) {
      if (e) {
        this.isResetObjectSettingItemIds.push(itemId);
      }
    }
    this.popover.hide();
    this.getSudokuList();
  }
  closeObjectPopover(itemId) {
    this.isResetObjectSettingItemIds = this.isResetObjectSettingItemIds.filter(
      (val) => val != itemId
    );
    this.popover.hide();
    this.getSudokuList();
  }
  delSudoku(itemId) {
    if (itemId) {
      this.rptService
        .postMappingReportSudokuItemsDel(itemId)
        .subscribe((res) => {
          if (res.result.code == 0) {
            this.msgServ.success("删除就九宫格成功！");
            this.getSudokuList();
          }
        });
    } else {
      this.sudokuData.pop();
      this.sudokuDataCache = _.cloneDeep(this.sudokuData);
    }
  }
  getMappingReportPersonCount() {
    if (this.mappingReportId) {
      this.rptService
        .getMappingReportPersonCount(this.mappingReportId)
        .subscribe((res) => {
          this.mappingReportPersonCount = res.data || 0;
        });
    }
  }
}
