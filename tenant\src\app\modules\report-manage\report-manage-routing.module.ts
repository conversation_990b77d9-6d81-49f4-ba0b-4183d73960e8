import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { ReportHomeComponent } from "./report-home/report-home.component";
import { BatchSendComponent } from "./batch-send/batch-send.component";
// 在线看板
import { OnlineReportComponent } from "./online-report/online-report.component";

const routes: Routes = [
  {
    path: "",
    component: ReportHomeComponent,
    data: {
      permissionCode: [
        "SAG:TENANT:REPORT_MGT:REPORT_LIST",
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP",
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:COMPARE",
        "SAG:TENANT:ALL",
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:ONLINE_REPORT",
      ],
    },
  },
  {
    path: "batch-send",
    component: BatchSendComponent,
    data: {
      permissionCode: [
        "SAG:TENANT:REPORT_MGT:REPORT_LIST",
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP",
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:COMPARE",
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:ONLINE_REPORT",
        "SAG:TENANT:ALL",
      ],
    },
  },
  {
    path: "online-report",
    component: OnlineReportComponent,
    data: {
      permissionCode: [
        "SAG:TENANT:REPORT_MGT:REPORT_LIST",
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP",
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:COMPARE",
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:ONLINE_REPORT",
        "SAG:TENANT:ALL",
      ],
    },
  },
  { path: "**", redirectTo: "" },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReportManageRoutingModule {}
