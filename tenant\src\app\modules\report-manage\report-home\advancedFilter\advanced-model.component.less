// 上传文件
.upload_button {
  ::ng-deep {
    .ant-upload {
      width: 100%;
    }
    .ant-btn {
      border-style: dashed;
    }
  }
}
.ant-input {
  border: 1px solid #d9d9d9 !important;
}

.text-blue {
  color: #409EFF;
}

.border-error {
  ::ng-deep {
    .ant-select-selection {
      border-color: #f5222d;
    }
  }
}

::ng-deep .filter-error {
  border: 1px #f5222d solid;
  .ant-select-selection--multiple {
    border-color: #f5222d;
  }
}

.box {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 120px;
  height: 32px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e4e4e4;
  margin: 0 8px 0 0;
  padding: 3px 11px;
  .filter-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    cursor: pointer;
    .arrow {
      font-size: 12px;
      color: #bfbfbf;
      transition: transform 0.3s;
      display: flex;
      align-items: center;
    }
    .arrow-open {
      transform: rotate(180deg);
      color: #409eff;
    }
  }
  .min {
    width: 450px;
    top: 42px;
    right: 0px;
    left: auto;
  }
}

.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}
::ng-deep {
  .advanced-model-drawer {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 108px);
      overflow: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}

.filter_box {
  // height: calc(100vh - 140px);
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
    span {
      font-size: 12px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #595959;
      line-height: 17px;
    }
    button,
    a {
      color: #409eff;
    }
  }
  .condition_box {
    width: 100%;
    height: 48px;
    background: #f8f8f8;
    border-radius: 8px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    span {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #595959;
      line-height: 20px;
      margin: 0 8px;
    }
    ::ng-deep {
      .ant-select-selection {
        color: #409eff;
        border: none;
        background-color: transparent;
      }
      .ant-select-open .ant-select-selection {
        border: none;
        box-shadow: none;
      }
      .ant-select-arrow {
        color: #409eff;
      }
      .condition {
        // 条件下拉菜单样式
        width: 64px;
      }
      margin-bottom: 16px;
    }
  }
  .content {
    height: calc(100vh - 246px);
    overflow-y: auto;
    overflow-x: hidden;
    .empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      p {
        padding-top: 26px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #495970;
        line-height: 20px;
        margin-bottom: 34px;
      }
    }

    .option-box {
      // 选项
      .option_empty {
        width: 100%;
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        border: 1px dashed #d9d9d9;
        color: #d9d9d9;
        text-align: center;
        cursor: default;
        font-size: 14px;
        > i {
          font-size: 12px;
          margin-right: 4px;
        }
      }
      .option_del {
        cursor: pointer;
        font-size: 14px;
        color: #262626;
        line-height: 32px;
        &:hover {
          color: #f5222d;
        }
      }
    }
  }

  footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 10px 16px;
    left: 0px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
  }
}
