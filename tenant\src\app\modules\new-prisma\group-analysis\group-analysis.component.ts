import { Component, OnInit, Input, ViewChild, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import {
  NzModalService,
  NzMessageService,
  NzFormatEmitEvent,
  NzTreeComponent,
} from "ng-zorro-antd";
import { HttpClient } from "@angular/common/http";
import _ from "lodash";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { NewPrismaService } from "../new-prisma.service";
import { NzDrawerRef } from "ng-zorro-antd/drawer";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-group-analysis",
  templateUrl: "./group-analysis.component.html",
  styleUrls: ["./group-analysis.component.less"],
})
export class GroupAnalysisComponent implements OnInit, OnDestroy {
  @Input() projectId: string;
  @Input() checkedCodes: string[];
  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;
  private routerSubscription: Subscription;

  constructor(
    private api: NewPrismaService,
    private nzModalService: NzModalService,
    private msg: NzMessageService,
    private http: HttpClient,
    private drawerRef: NzDrawerRef,
    private customMsg: MessageService,
    private router: Router
  ) {}
  // 展开的节点
  // defaultExpandedKeys = [];
  // 选中的节点
  defaultCheckedKeys = [];
  // 人口标签树
  factorData = [];
  // 结果数据
  resultData = [];

  // 选中的根节点
  rootKeys = [];

  firstRootKey = "";

  // loading
  factorDataLoading = false;
  factorAddLoading = false;
  resultDataLoading = false;
  resultClearLoading = false;
  ngOnInit() {
    // 获取人口学标签
    this.loadAnalysisFactorData();
    // 获取多群体分析数据
    this.loadGroupAnalysisData();
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
  // 获取人口学标签
  async loadAnalysisFactorData() {
    this.factorDataLoading = true;
    const res = await this.api.getAnalysisFactorDto(this.projectId).toPromise();
    const allData = res.data || [];
    const checkedData = allData.filter((val) =>
      this.checkedCodes.includes(val.code)
    );
    // 展开第一层
    // this.defaultExpandedKeys = checkedData.map((val) => val.id);
    this.factorData = this.formatter(checkedData);
    this.factorDataLoading = false;
  }
  async loadGroupAnalysisData() {
    this.resultDataLoading = true;
    const res = await this.api
      .getListMultiDemographic(this.projectId)
      .toPromise();
    // 将数据根交叉的一级标签分组
    this.resultData = this.classifyData(res.data || []);
    this.resultDataLoading = false;
  }

  formatter(data, root?) {
    // 过滤禁用的人口标签
    return data
      .filter((val) => val.status === "ENABLE")
      .map((item) => {
        const rootId = root || item.id;
        const formattedItem = {
          title: item.name.zh_CN,
          key: item.id,
          root: rootId,
          children: [],
          isLeaf: true,
          name: item.name,
        };
        if (item.child && item.child.length > 0) {
          formattedItem.isLeaf = false;
          formattedItem.children = this.formatter(item.child, rootId);
        }
        return formattedItem;
      });
  }
  // 展开
  expandedLeft(event: NzFormatEmitEvent): void {
    if (event.node.isChecked) {
      this.traverseChecked(event.node, false);
    } else {
      // event.node.isExpanded = true;
      this.traverseChecked(event.node, true);
    }
  }
  traverseChecked(node, isChecked) {
    node.isChecked = isChecked;
    if (node.children && node.children.length > 0) {
      node.children.forEach((son) => {
        this.traverseChecked(son, isChecked);
      });
    }
  }
  // 选中
  changeLeft(event): void {
    const rootKeys = _.uniq(event.checkedKeys.map((val) => val.origin.root));
    // 第一个被选中的作为首个
    if (rootKeys.length === 1) {
      this.firstRootKey = rootKeys[0];
    }
    if (rootKeys.length > 2) {
      // this.msg.warning("一级人口学最多只能选择两个!");
      this.customMsg.open("warning", "一级人口学最多只能选择两");
      let currentNode: any = event.node.origin;
      let allNodes = this.nzTreeComponent.getTreeNodes();
      this.setCheckedFun(allNodes, currentNode.root);
      this.rootKeys = rootKeys.filter((val) => val !== currentNode.root);
    }
    this.rootKeys = rootKeys;
  }
  // 清空选中，若传rootId清空根节点，若不传，清空所有
  setCheckedFun(tree, rootId?) {
    tree.forEach((node) => {
      if (node.origin.root === rootId || node.key === rootId || !rootId) {
        node.isChecked = false;
        node.isHalfChecked = false;
        if (node.children.length !== 0) {
          this.setCheckedFun(node.children, rootId);
        }
      }
    });
  }
  // 人口标签清除
  handClearLeft() {
    this.factorDataLoading = true;
    let allNodes = this.nzTreeComponent.getTreeNodes();
    this.setCheckedFun(allNodes);
    this.rootKeys = [];
    this.firstRootKey = "";
    this.factorDataLoading = false;
  }
  // 人口标签确定
  async handOK() {
    this.factorAddLoading = true;
    let allNodes = this.nzTreeComponent.getTreeNodes();
    const checkedTree = allNodes.filter((val) =>
      this.rootKeys.includes(val.key)
    );
    if (this.rootKeys.length < 2) {
      // this.msg.warning("请选择两个人口学!");
      this.customMsg.open("warning", "请选择两个人口学");
      this.factorAddLoading = false;
      return;
    }
    const outsideData = this.findChecked(checkedTree);
    const firstData = checkedTree.find((val) => val.key === this.firstRootKey);
    const secondData = checkedTree.find((val) => val.key !== this.firstRootKey);
    const first = {
      id: firstData.key,
      name: firstData.origin.name,
      children: outsideData
        .filter((val) => val.root === firstData.key)
        .map((val) => ({
          id: val.key,
          name: val.name,
        })),
    };
    const second = {
      id: secondData.key,
      name: secondData.origin.name,
      children: outsideData
        .filter((val) => val.root === secondData.key)
        .map((val) => ({
          id: val.key,
          name: val.name,
        })),
    };
    const params = {
      projectId: this.projectId,
      firstDemographic: first,
      secondDemographic: second,
    };
    const res = await this.api.saveMultiDemographic(params).toPromise();
    if (res.result.code === 0) {
      setTimeout(() => {
        this.factorAddLoading = false;
      }, 1000);
      this.msg.success("添加成功！");
      // 清除选中
      this.handClearLeft();
      // 获取新数据
      this.loadGroupAnalysisData();
    } else {
      this.factorAddLoading = false;
    }
  }
  findChecked(tree): any[] {
    const checked: any[] = [];
    for (const node of tree) {
      if (node.isChecked && (!node.children || node.children.length === 0)) {
        checked.push(node.origin);
      }

      if (node.children && node.children.length > 0) {
        const childCheckedIds = this.findChecked(node.children);
        checked.push(...childCheckedIds);
      }
    }
    return checked;
  }

  // 清空所有多群体
  async handClearRight() {
    this.resultClearLoading = true;
    const res = await this.api
      .removeAllMultiDemographic(this.projectId)
      .toPromise();
    if (res.result.code === 0) {
      this.resultClearLoading = false;
      this.msg.success("多群体分析结果清空成功！");
      this.loadGroupAnalysisData();
    } else {
      this.resultClearLoading = false;
    }
  }

  // 删除单个群体分析
  async handDelDemographic(item) {
    // const {
    //   firstParentDemographicName,
    //   secondParentDemographicName,
    //   firstDemographicName,
    //   secondDemographicName,
    // } = item;
    // const text = `${firstParentDemographicName.zh_CN}【${firstDemographicName.zh_CN}】 且 ${secondParentDemographicName.zh_CN}【${secondDemographicName.zh_CN}】`;
    const res = await this.api.removeOneMultiDemographic(item.id).toPromise();
    if (res.result.code === 0) {
      // this.msg.success(`${text} 已删除！`);
      this.msg.success(`删除成功！`);
      this.loadGroupAnalysisData();
    }
  }

  classifyData(data: any[]): { key: string; items: any[] }[] {
    const classifiedArray: {
      key: string;
      firstName: string;
      secondName: string;
      items: any[];
    }[] = [];
    for (const item of data) {
      const key = `${item.firstParentDemographicId}-${item.secondParentDemographicId}`;
      const firstName = item.firstParentDemographicName.zh_CN;
      const secondName = item.secondParentDemographicName.zh_CN;
      const foundClass = classifiedArray.find(
        (classItem) => classItem.key === key
      );
      if (foundClass) {
        foundClass.items.push(item);
      } else {
        classifiedArray.push({ key, firstName, secondName, items: [item] });
      }
    }
    return classifiedArray;
  }

  handClose() {
    this.drawerRef.close();
  }
}
