<div *ngIf="isLoading" style="width: 100%; text-align: center;">
    <nz-spin nzSimple [nzSize]="'large'"></nz-spin>
    <span style="font-size: 22px;">数据加载中</span>
</div>

<div class="con">

    <div class="tb" *ngIf="!isLoading">

        <div style="margin-bottom: 10px; display: flex;">
            <button nz-button nzType="default" (click)="selectAll(1)">
                {{allFlag1 ? '全选' : '全不选' }}
            </button>
            <input style="margin-left: 15px;" nz-input placeholder="请输入搜索内容" [(ngModel)]="text1" (change)="search(1)" />
        </div>

        <nz-table #t1 [nzData]="tbData1" nzSize="small">
            <tbody>
                <div style="max-height: 380px; overflow-y: auto;">
                    <tr *ngFor="let data of t1.data">
                        <td width="28px">
                            <label nz-checkbox [(ngModel)]="data.checked"></label>
                        </td>
                        <td><div class="ell" nz-tooltip [nzTooltipTitle]="data.title">{{ data.title }}</div></td>
                    </tr>
                </div>
            </tbody>
        </nz-table>

    </div>

    <div *ngIf="!isLoading" class="middle">

        <button nz-button nzType="default" (click)="toLeft()">
            <i nz-icon nzType="left" nzTheme="outline"></i>
        </button>

        <button nz-button nzType="default" style="margin: 0;" (click)="toRight()">
            <i nz-icon nzType="right" nzTheme="outline"></i>
        </button>

    </div>

    <div class="tb" *ngIf="!isLoading">

        <div style="margin-bottom: 10px; display: flex;">
            <button nz-button nzType="default" (click)="selectAll(2)">
                {{allFlag2 ? '全选' : '全不选' }}
            </button>
            <input style="margin-left: 15px;" nz-input placeholder="请输入搜索内容" [(ngModel)]="text2" (change)="search(2)" />
        </div>


        <nz-table #t2 [nzData]="tbData2" nzSize="small">
            <tbody>
                <div style="max-height: 380px; overflow-y: auto;">
                    <tr *ngFor="let data of t2.data">
                        <td width="28px">
                            <label nz-checkbox [(ngModel)]="data.checked"></label>
                        </td>
                        <td><div class="ell" nz-tooltip [nzTooltipTitle]="data.title">{{ data.title }}</div></td>
                    </tr>
                </div>
            </tbody>
        </nz-table>

    </div>
    
</div>

<ng-container *ngIf="listtype != 'orgcode'">
    <nz-upload style="cursor: pointer;" class="upload" [nzCustomRequest]="importTenantCongif" [nzFilter]="" [nzShowUploadList]="false">
        <i class="iconfont icon-icon_import"></i><span>导入</span>
    </nz-upload>
    <button (click)="downLoad()" nz-button nzType="link" [nzLoading]="buttonload">下载模板</button>
</ng-container>