import {
  Component,
  Input,
  OnInit,
  Output,
  EventEmitter,
  SimpleChanges,
} from "@angular/core";
import { NewActivityService } from "../new-activity.service";
import { Router } from "@angular/router";

@Component({
  selector: "app-tool-card",
  templateUrl: "./tool_cards.component.html",
  styleUrls: ["./tool_cards.component.less"],
})
export class ToolCardComponent implements OnInit {
  @Input() fl_num: any;
  @Input() toolslist = [];
  @Input() chooselistids = [];
  @Input() keyWordlist = [];
  @Input() nolike = "";
  @Input() tabshowindex;

  @Output() selectcard = new EventEmitter<any>();
  @Output() getReturn = new EventEmitter<any>();

  chooseoneid = "";
  sandtype = false;
  arrColor = [
    "linear-gradient(90deg,rgba(124,164,255,1) 0%,rgba(118,201,255,1) 100%)",
    "linear-gradient(90deg,rgba(120,196,255,1) 0%,rgba(130,219,222,1) 100%)",
    "linear-gradient(90deg,rgba(139,223,202,1) 0%,rgba(174,234,135,1) 99%)",
    "linear-gradient(90deg,rgba(255,147,147,1) 0%,rgba(251,195,188,1) 100%)",
    "linear-gradient(90deg,rgba(255,169,153,1) 0%,rgba(255,207,130,1) 100%)",
    "linear-gradient(90deg,rgba(253,202,124,1) 0%,rgba(255,176,204,1) 100%)",
    "linear-gradient(90deg,rgba(209,167,255,1) 0%,rgba(151,175,255,1) 100%)",
    "linear-gradient(90deg,rgba(219,173,255,1) 0%,rgba(244,190,232,1) 100%)",
    "linear-gradient(90deg,rgba(222,183,255,1) 0%,rgba(153,209,255,1) 100%,rgba(168,228,227,1) 100%)",
    "linear-gradient(90deg,rgba(132,210,221,1) 0%,rgba(127,226,182,1) 100%)",
    "linear-gradient(90deg,rgba(115,214,214,1) 0%,rgba(139,197,240,1) 100%)",
  ];
  ngcolor = [
    "magenta",
    "red",
    "volcano",
    "orange",
    "gold",
    "lime",
    "green",
    "cyan",
    "blue",
    "geekblue",
    "purple",
  ];

  constructor(private http: NewActivityService, private router: Router) {}

  ngOnInit() {}
  ngOnChanges(changesQuestion: SimpleChanges) {
    if (this.nolike == "no") {
      this.toolslist = this.keyWordlist;
      this.getReturn.emit(this.toolslist);
    } else {
    }

    this.toolslist.forEach((res) => {
      res.totalprice = 0;
      if (res.standardDimensionResultVOList) {
        res.standardDimensionResultVOList.forEach((val) => {
          val.reportTypeshow = true;
          val.showcard = true;
          val.reportTypeshowMhs = true;
          val.showcardMhs = true;
        });
      }
    });
  }

  getcards(toolslist, item, list, id, type, isStandtype, Dimension?): void {
    //ASSESSMENT 测评
    //EMPLOYEE_ENGAGEMENT 调研

    let data;
    let cancelIds;
    if (list.checked) {
      //当前已选中
      list.checked = false;
      this.chooselistids = [];
      toolslist.forEach((res) => {
        if (res.reportProjects) {
          res.reportProjects.forEach((val) => {
            if (val.checked) {
              this.chooselistids.push(res);
            }
          });
        }
        if (res.standardDimensionResultVOList) {
          res.standardDimensionResultVOList.forEach((nam) => {
            if (nam.checked) {
              this.chooselistids.push(res);
            }
          });
        }
      });
      data = this.removaldata(this.chooselistids);
      item.selected = false;
      item.reportProjects.forEach((val) => {
        if (val.checked) {
          item.selected = true;
        }
      });
      if (item.standardDimensionResultVOList) {
        item.standardDimensionResultVOList.forEach((nam) => {
          // 如果当前选项是点击的选项 且 当前选项是已经选中的互斥选项 那么就把所有的互斥选项状态重置
          if (Dimension == nam.name && !nam.reportTypeshow) {
            item.standardDimensionResultVOList.forEach((val) => {
              val.showcard = true;
              val.reportTypeshow = true;
            });
          }
          // 如果当前选项是点击的选项 且 当前选项是已经选中的互斥选项 那么就把所有的互斥选项状态重置(csi和mhs互斥)
          if (Dimension == nam.name && !nam.reportTypeshowMhs) {
            item.standardDimensionResultVOList.forEach((val) => {
              val.showcardMhs = true;
              val.reportTypeshowMhs = true;
            });
          }
          if (nam.checked) {
            item.selected = true;
          }
        });
      }
      if (item.selected == false) {
        cancelIds = item.id;
      }
    } else {
      //当前未选中
      if (isStandtype) {
        //是独立链接
        toolslist.forEach((val) => {
          if (val.id == id) {
            val.reportProjects.forEach((son) => {
              if (type == son.style) {
                son.checked = true;
                if (Dimension) {
                  val.standardDimensionResultVOList.forEach((nam) => {
                    if (Dimension == nam.name) {
                      nam.checked = true;
                    }
                  });
                }
              }
            });
            if (val.standardDimensionResultVOList) {
              val.standardDimensionResultVOList.forEach((nam) => {
                // 如果当前选项是点击的选项 那么就reportTypeshow设为false 作为互斥已选中的标记
                if (Dimension == nam.name) {
                  nam.checked = true;
                  if (
                    nam.reportType == "MCA" ||
                    nam.reportType == "CA" ||
                    nam.reportType == "CA_LPA"
                  ) {
                    nam.reportTypeshow = false;
                  }
                  if (
                    nam.reportType == "CSI" ||
                    nam.reportType == "CSI_DT"
                  ) {
                    nam.reportTypeshowMhs = false;
                  }
                }
              });
              val.standardDimensionResultVOList.forEach((nam) => {
                if (
                  nam.reportType == "MCA" ||
                  nam.reportType == "CA" ||
                  nam.reportType == "CA_LPA"
                ) {
                  //查询互斥选项中是否已经有选中的选项
                  let mapshow = val.standardDimensionResultVOList.every(
                    (item) => {
                      return item.reportTypeshow;
                    }
                  );

                  if (!mapshow) {
                    // 所有互斥选项 设为禁止点击 即showcard=false
                    nam.showcard = false;
                    val.standardDimensionResultVOList.forEach((item) => {
                      if (!item.reportTypeshow) item.showcard = true;
                    });
                  }
                }
                if (
                  nam.reportType == "CSI" ||
                  nam.reportType == "CSI_DT"
                ) {
                  //查询互斥选项中是否已经有选中的选项
                  let mapshow = val.standardDimensionResultVOList.every(
                    (item) => {
                      return item.reportTypeshowMhs;
                    }
                  );

                  if (!mapshow) {
                    // 所有互斥选项 设为禁止点击 即showcard=false
                    nam.showcardMhs = false;
                    val.standardDimensionResultVOList.forEach((item) => {
                      if (!item.reportTypeshowMhs) item.showcardMhs = true;
                    });
                  }
                }
              });
            }
          } else {
            if (val.reportProjects) {
              val.reportProjects.forEach((son) => {
                son.checked = false;
                if (val.standardDimensionResultVOList) {
                  val.standardDimensionResultVOList.forEach((nam) => {
                    nam.checked = false;
                  });
                }
              });
            }
          }
        });
        data = toolslist.filter((res) => {
          return res.id == id;
        });
        this.sandtype = true;
      } else {
        //不是独立链接
        toolslist.forEach((val) => {
          if (val.isStandalone) {
            if (val.reportProjects) {
              val.reportProjects.forEach((son) => {
                son.checked = false;
              });
              if (val.standardDimensionResultVOList) {
                val.standardDimensionResultVOList.forEach((nam) => {
                  nam.checked = false;
                });
              }
            }
          } else {
            if (val.id == id) {
              val.reportProjects.forEach((son) => {
                if (type == son.style) {
                  son.checked = true;
                } else {
                  // son.checked = false
                }
              });
              if (val.standardDimensionResultVOList) {
                val.standardDimensionResultVOList.forEach((nam) => {
                  if (Dimension == nam.name) {
                    nam.checked = true;
                  }
                });
              }
            }
          }
        });
        if (this.sandtype) {
          //是从独立链接切换到复合链接
          this.chooselistids = [];
          toolslist.forEach((res) => {
            if (res.reportProjects) {
              res.reportProjects.forEach((val) => {
                if (val.checked) {
                  this.chooselistids.push(res);
                }
              });
            }
          });
          data = this.removaldata(this.chooselistids);
          this.sandtype = false;
        } else {
          this.chooselistids = [];
          toolslist.forEach((res) => {
            if (res.reportProjects) {
              res.reportProjects.forEach((val) => {
                if (val.checked) {
                  this.chooselistids.push(res);
                }
              });
            }
          });
          data = this.removaldata(this.chooselistids);
        }
      }
    }
    let params = {
      data: data,
      nolike: this.nolike,
      cancelIds: cancelIds,
      sandtype: this.sandtype,
    };

    this.gettotalmoney(item, id, isStandtype, params);
  }
  gettotalmoney(data, id, isStandtype, sendparams) {
    let paymentConfig = [];
    let reportStyles = [];
    let reportTypeList = [];
    data.reportProjects.forEach((val) => {
      if (val.checked) {
        paymentConfig.push(val.paymentConfig);
        reportStyles.push(val.style);
      }
    });
    if (data.standardDimensionResultVOList) {
      data.standardDimensionResultVOList.forEach((val) => {
        if (val.checked) {
          reportTypeList.push(val.reportType);
        }
      });
    }
    let params = {
      reportType: data.reportType.indexOf("TIP") != -1 ? data.reportType : "",
      standardQuestionnaireId: data.id,
      reportStyles: reportStyles,
      paymentConfigs: paymentConfig,
      reportTypeList: reportTypeList,
    };
    this.http.getTotal(params).subscribe((res) => {
      if (res.result.code == 0) {
        this.toolslist.forEach((item) => {
          if (item.id == id) {
            item.totalprice = res.data.originalPrice;
            item.currentPrice = res.data.currentPrice;
            item.isDiscount = res.data.isDiscount;
          }
        });
      }
    });
    if (isStandtype) {
      this.toolslist.forEach((item) => {
        if (item.id != id) {
          item.totalprice = 0;
          item.isDiscount = false;
        }
      });
    }

    this.selectcard.emit(sendparams);
  }
  Jumplist(data) {
    let road = data.reportType.toLowerCase();
    let open = road;
    if (road.indexOf("investigation") != -1) {
      if (road == "culture_investigation_research") {
        // 文化调研
        open = "prismaCulture";
      } else if(road == "dp_investigation_research_custom"){
        // 双视角调研
        open = "doublePerspective";
      } else if(road == "oc_investigation_research"){
        // 组织能力调研
        open = "orgSurvey";
      } else {
        // 敬业度
        open = "prisma";
      }
    } else {
      if (road.indexOf("360") != -1) {
        if (road.indexOf("train") != -1) {
          open = "train";
        } else {
          open = "s360";
        }
      }
      if (road.indexOf("270") != -1) {
        open = "s270";
      }
      if (road.indexOf("tip") != -1) {
        open = "tip";
      }
      if (road.indexOf("mca") != -1) {
        open = "mca";
      }
      if (road.indexOf("csi_dt") != -1) {
        open = "mhs";
      }
    }
    if (road == "blank_custom") return; // 自定义问卷暂时不跳转
    this.router.navigate([`/tourist/${open}`]);
  }
  removaldata(arr) {
    let result = [];
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      if (!obj[arr[i].id]) {
        result.push(arr[i]);
        obj[arr[i].id] = true;
      }
    }
    return result;
  } //去重

  getnewcards() {
    console.log("ss");
  }
}
