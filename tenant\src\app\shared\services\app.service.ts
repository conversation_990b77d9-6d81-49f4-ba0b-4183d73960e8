import { Injectable } from "@angular/core";
import { Observable, Subject } from "rxjs";
import { Inject } from "@angular/core";
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ITokenService, DA_SERVICE_TOKEN } from '@knz/auth';
export let defaul: string;
@Injectable({
  providedIn: "root"
})
export class AppService {
  
  timer : any;
  tenantUrl: string = "/tenant-api";

  constructor(private http: HttpClient,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService
    ) {}
  
  public startTimer() {
    // console.log("last timer is : " + JSON.stringify(this.timer));
    if (this.timer) {
      clearInterval(this.timer);
    }
    
    // let refToken = this.getTokenFromStorage();
    // if(refToken) {
    //   this.doRefreshToken();
    // }
    
    // 循环执行
    let that = this;
    let timer = setInterval(function () {
      that.doRefreshToken();
    }, 
    (5 * 60 - 5) * 1000
    // 30 * 1000
    );
    this.timer = timer;
    // console.log("new timer created : " + JSON.stringify(this.timer));
  }

  getTokenFromStorage():string {
    let tmp = '';
    const usrToken = this.tokenService.get();
    // console.log("retrieve ref token from local storage : " + JSON.stringify(usrToken));
    if(usrToken) {
      tmp = usrToken.refreshToken;
    }
    return tmp;
  }

  doRefreshToken() {
    let refToken = this.getTokenFromStorage();
    if(!refToken) {
      // console.log("refresh job running , but no param found in storage.. ");
      return;
    }
    this.refreshToken(refToken).subscribe(res => {
      if (res.result.code == 0) {
        let storedData = 
        {
          token: res.data.token,
          refreshToken: refToken,
          time: Date()
        }
        
        this.tokenService.set(storedData);
        localStorage.setItem("token", JSON.stringify(storedData));
        
        // console.log(`in app service, time = ${this.timer}, token refreshed : ` + res.data.token);
      } 
    });
  }

   /** 刷新token */
   refreshToken(token: string): Observable<any> {
    let params = {refreshToken : token}
    // console.log("in app service, call refreshToken, param = " + JSON.stringify(params))
    const httpOptions = {headers : new HttpHeaders({'Content-Type': 'application/json'})};
    return this.http.post(this.tenantUrl + "/auth/refreshToken?_allow_anonymous=true", params, httpOptions);
  }

}
