import { Component, OnInit, ChangeDetectorRef, ViewChild } from "@angular/core";
import {
  NzDrawerService,
  NzMessageService,
  NzModalService,
} from "ng-zorro-antd";
import { UploadFile, UploadXHRArgs } from "ng-zorro-antd/upload";
import { ProjectManageService } from "../service/project-manage.service";
import { Router, ActivatedRoute } from "@angular/router";
import { AddCustomBookComponent } from "./add-custom-book/add-custom-book.component";
import _ from "lodash";
import { AnswerSettingComponent } from "./answer-setting/answer-setting.component";

import { ElementRef } from "@angular/core";
import { Subscription, fromEvent } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { DragulaService } from "ng2-dragula";
import { HttpEvent } from "@angular/common/http";
import { MicroAppService } from "@core/sub-micro-app/sub-micro-app.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-custom-book",
  templateUrl: "./custom-book.component.html",
  styleUrls: ["./custom-book.component.less"],
})
export class CustomBookComponent implements OnInit {
  @ViewChild("deliveryTable", { static: false }) deliveryView: ElementRef;
  private subscription: Subscription;
  tableTop: string = "";

  questionnaireId: string = ""; //问卷id
  dataList = []; //题目列表
  isAllDisplayDataChecked: boolean = false; //是否全选
  isIndeterminate: boolean = false; //是否半选
  keyWord: string = ""; //搜索关键字
  projectId: string = "";
  projectType: string = "";
  isEnableRoleDimension: boolean = false;
  isedited = false;
  dafaultdata = [];
  backtype = "";
  listChecked = "";
  standardReportType = "";
  standardQuestionnaireId;
  edittype;
  Breadcrumbs = [];
  lanIndex: number = 0;
  lans: any[] = [
    { key: "zh_CN", value: "中文" },
    { key: "en_US", value: "ENG" },
  ];
  lan: string = "zh_CN";
  tabSize: string = "small";
  throughisVisible = false;
  // 中英文切换 -ldx
  questionType: string = null; // 题型
  questionStr: string = "";
  resultStr: string = "";
  resultType: string = null;
  questionList: any[] = [];
  resultList: any[] = [];
  parentQuestionId: ""; // 条件题id
  questionId: ""; // 结果题id
  surveyStandardOptions: any[] = []; // 条件提选项id
  penetrationList: any[] = []; // 关联列表
  tipsTypelist: any[];
  popoverVisible: boolean = false; // 条件提选项id
  sourceList: any[] = [];

  topicList: any[] = [];

  isAllChecked: boolean = false;
  // 全选 -ldx
  disabledAllChecked: boolean = false;
  buttonload = false;
  fileList = [];

  // 维度Map-用于code展示
  dimensionMap = null;
  changeLan(e): void {
    let cur = this.lans[this.lanIndex];
    this.lan = cur.key;
  }
  queryParams = {};
  answerType = "FINAL";
  currentReportType = ""; // 多问卷用
  MANY_ITEMS = "VAMPIRESDRAGULA";
  tips: any = {
    SINGLE: {
      name: {
        zh_CN: "单选",
        en_US: "Single Choice",
      },
      color: "#9833FF",
      bg: "rgba(152, 51, 255, 0.08)",
    },
    SCALE: {
      name: {
        zh_CN: "量表",
        en_US: "Scale",
      },
      color: "#3372FF",
      bg: "rgba(51, 114, 255, 0.08)",
    },
    MULTIPLE_CHOICE: {
      name: {
        zh_CN: "多选",
        en_US: "Multiple Choice",
      },
      color: "#3372FF",
    },
    ESSAY_QUESTION: {
      name: {
        zh_CN: "开放",
        en_US: "Open Question",
      },
      color: "#24CC9E",
      bg: "rgba(36, 204, 158, 0.08)",
    },
    MULTIPLE_CHOICE_ESSAY_QUESTION: {
      name: {
        zh_CN: "多选开放",
        en_US: "Multiple Choice Open Question",
      },
      color: "#FF8D33",
      bg: "rgba(255, 141, 51, 0.08)",
    },
    isPierceThrough: {
      name: {
        zh_CN: "穿透",
        en_US: "Penetration Question",
      },
      color: "#FF58A6",
      bg: "rgba(255, 88, 166, 0.08)",
    },
    PROPORTION: {
      name: {
        zh_CN: "滑块",
        en_US: "Proportion",
      },
      color: "#238FFF",
      bg: "rgba(35, 143, 255, 0.08)",
    },
    PROPORTION_MULTIPLE: {
      name: {
        zh_CN: "多级比重",
        en_US: "Proportion Multiple",
      },
      color: "#12C6F9",
      bg: "rgba(18, 198, 249, 0.08)",
    },
    EVALUATION: {
      name: {
        zh_CN: "评价",
        en_US: "Scoring Qusetion",
      },
      color: "#6A5DEF",
      bg: "rgb(106 93 239 / 8%)",
    },
    DESCRIBE: {
      name: {
        zh_CN: "文本/标题",
        en_US: "Text/Title",
      },
      color: "#59C606",
      bg: "rgb(89 198 6 / 8%)",
    },
  };
  // #11253 滑块穿透-选项/分值区间
  proportionScope = [];
  proportionHighestScore = null;
  proportionLowestScore = null;

  constructor(
    private msg: NzMessageService,
    private api: ProjectManageService,
    private routeInfo: ActivatedRoute,
    private modalService: NzModalService,
    private drawerService: NzDrawerService,
    private router: Router,
    private microApp: MicroAppService,
    private dragulaService: DragulaService,
    private customMsg: MessageService
  ) {
    let _this = this;
    dragulaService.createGroup(this.MANY_ITEMS, {
      accepts: function(el: any, container: any, handle: any): any {
        return !_this.isNewQuestionBook();
      },
    });
  }

  ngOnDestroy() {
    // 销毁事件
    this.subscription.unsubscribe();
    this.dragulaService.destroy(this.MANY_ITEMS);
  }

  answerSetting(disableSave: boolean) {
    const modal = this.drawerService.create({
      nzContent: AnswerSettingComponent,
      nzContentParams: {
        projectId: this.projectId,
        standardQuestionnaireId: this.standardQuestionnaireId,
        disableSave: disableSave,
        isEnableRoleDimension: this.isEnableRoleDimension,
      },
      nzTitle: "填答设置",
      nzWidth: 680,
      nzWrapClassName: "round-right-drawer1",
      // nzOkText: "保存",
      // nzCancelText: "取消",
      // nzMaskClosable: false,
      // nzClosable: true,
      // nzFooter: null,
    });
  }

  pierceThrough() {
    this.throughisVisible = true;
    this.getlistByTypesAndName();
    this.getlistByTypesAndNameRes();
    this.getShowPierceThroughCombination();
    this.loadData();
  }
  ThroughCancel() {
    this.throughisVisible = false;
    this.getList();
  }
  ThroughOk() {
    this.throughisVisible = false;
  }

  ngOnInit(): void {
    this.tipsTypelist = this.api.tipsTypelist;

    this.questionnaireId = this.routeInfo.snapshot.queryParams.questionnaireId;
    this.projectId = this.routeInfo.snapshot.queryParams.projectId;
    this.projectType = this.routeInfo.snapshot.queryParams.type;
    this.backtype = this.routeInfo.snapshot.queryParams.backtype;
    this.listChecked = this.routeInfo.snapshot.queryParams.listChecked;
    this.standardReportType = this.routeInfo.snapshot.queryParams.standardReportType;
    this.edittype = this.routeInfo.snapshot.queryParams.edittype;
    this.standardQuestionnaireId = this.routeInfo.snapshot.queryParams.standardQuestionnaireId;
    this.currentReportType = this.routeInfo.snapshot.queryParams.currentReportType;
    this.queryParams = this.routeInfo.snapshot.queryParams;

    this.Breadcrumbs = JSON.parse(localStorage.getItem("break")) || [];
    let flag = false; //是否已经添加了题本管理面包屑
    this.Breadcrumbs.forEach((item) => {
      if (item.Highlight) {
        if (item.name == "活动管理") {
          item.path = "/project-manage/home";
        }
        if (item.name == "活动设置") {
          item.path = "/new-create";
        }
        item.Highlight = false;
      }
      if (item.name == "题本管理") {
        flag = true;
        item.Highlight = true;
      }
    });
    if (!flag) {
      this.Breadcrumbs.push({
        path: "",
        name: "题本管理",
        Highlight: true,
      });
    }
    if (this.edittype == "STAND") {
      this.isedited = false;
    } else {
      this.isedited = true;
    }

    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    this.getList();
    this.api.getProjectSetting(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        sessionStorage.setItem(
          "projectLanguages",
          JSON.stringify(res.data.availableLanguages)
        );
        sessionStorage.setItem("language", res.data.language);
        let projectInfo = res.data;
        this.isEnableRoleDimension = projectInfo.isEnableRoleDimension;
        if (!this.projectType) {
          this.projectType = projectInfo.status;
        }
        if (projectInfo.language === "en_US") {
          this.lan = "en_US";
          this.lanIndex = 1;
        }
      }
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.tableTop =
        this.listChecked !== "checked"
          ? `${this.deliveryView.nativeElement.clientHeight - 276 - 40}px`
          : `${this.deliveryView.nativeElement.clientHeight - 201 - 40}px`;
    });
    this.subscription = fromEvent(window, "resize")
      .pipe(debounceTime(100)) // 以免频繁处理
      .subscribe((event) => {
        // 这里处理页面变化时的操作
        this.tableTop =
          this.listChecked !== "checked"
            ? `${this.deliveryView.nativeElement.clientHeight - 276 - 40}px`
            : `${this.deliveryView.nativeElement.clientHeight - 201 - 40}px`;
      });
  }

  dragnumber(e) {
    let ids = [];
    e.map((item) => {
      if (item.id) ids.push(item.id);
    });
    if (ids.length > 0) {
      this.api.reSort(ids, this.questionnaireId).subscribe((res) => {
        if (res.result.code === 0) {
          this.msg.success("排序保存成功");
        }
      });
    }
  }

  /**
   * onEditQuestion 编辑题本
   * @param id
   */
  onEditQuestion(id, data) {
    this.showModal(id, data);
  }

  /**
   * onDeleteQuestion 删除题本
   * @param id
   */
  onDeleteQuestion(id) {
    this.api.deleteQuestion(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("删除成功");
        this.getList();
      } else {
        // this.msg.error(res.result.message);
      }
    });
  }

  /**
   * 批量删除
   */
  onDeleteBatch() {
    let ids = [];
    for (const item of this.dataList) {
      if (item.checked) ids.push(item.id);
    }

    this.api.batchDeleteQuestion(ids).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("批量删除成功");
        this.isAllDisplayDataChecked = false;
        this.isIndeterminate = false;
        this.getList();
      } else {
        // this.msg.error(res.result.message);
      }
    });
  }

  /**
   * 新增 编辑弹窗
   */
  public showModal(id = "", data?): void {
    if (this.isNewQuestionBook()) {
      let params = {
        reportType: this.standardReportType,
        ...this.routeInfo.snapshot.queryParams,
      };
      if (id) {
        params["questionId"] = id;
      }

      if (data) {
        params["parentQuestionId"] = data.id;
      }

      this.microApp.navigateByUrl("/customTopic", {
        appName: "sag-tenant",
        params: params,
      });
    } else {
      let name = "";
      if (id != "") {
        name = "编辑";
      } else {
        name = "新增";
      }
      const modal = this.modalService.create({
        nzTitle: name,
        nzWidth: 1300,
        nzContent: AddCustomBookComponent,
        nzComponentParams: {
          nametip: name,
          id: id,
          questionnaireId: this.questionnaireId,
          projectId: this.projectId,
          isedited: this.isedited,
          standardReportType: this.standardReportType,
          currentReportType: this.currentReportType,
        },
      });

      this.modalService.afterAllClose.subscribe(() => {
        this.getList();
        return;
      });
    }
  }

  /**
   * 全选事件
   * @param value
   */
  checkAll(value: boolean): void {
    this.isAllDisplayDataChecked = value;
    this.isIndeterminate = false;
    if (value) {
      for (const item of this.dataList) {
        item.checked = true;
      }
    } else {
      for (const item of this.dataList) {
        item.checked = false;
      }
    }
  }

  /**
   * checkbox状态更新
   */
  refreshStatus(): void {
    if (
      _.filter(this.dataList, { checked: true }).length === this.dataList.length
    ) {
      this.isAllDisplayDataChecked = true;
    } else {
      this.isAllDisplayDataChecked = false;
    }
    if (
      _.filter(this.dataList, { checked: true }).length &&
      !this.isAllDisplayDataChecked
    ) {
      this.isIndeterminate = true;
    } else {
      this.isIndeterminate = false;
    }
  }

  /**
   * preview 预览上传的文件
   * @param file
   */
  preview(file) {
    window.open(window.URL.createObjectURL(file.originFileObj));
  }

  /**
   * uploadExcel 上传配置
   */
  upload(formData, item) {
    return this.api.customImport(formData, this.questionnaireId).subscribe(
      (res) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.getList();
        } else {
          item.onError!(item.file!);
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  uploadStandard(formData, item) {
    return this.api.standardImport(formData, this.questionnaireId).subscribe(
      (res) => {
        if (res.result.code === 0) {
          item.onSuccess!();
          this.getList();
        } else {
          item.onError!(item.file!);
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let params = {
      fileType: "." + item.file.name.split(".")[1],
    };
    if (this.edittype == "STAND") {
      this.uploadStandard(formData, item);
    } else {
      this.upload(formData, item);
    }
  };

  /**
   * 获取题目列表
   */
  getList() {
    // 维度获取
    this.getDimension(this.questionnaireId);
    this.api
      .listByQuestionnaireId(this.questionnaireId, this.keyWord, 0)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.dataList = res.data;
        }
      });
  }
  handleChange(info: any): void {
    let fileList = [...info.fileList];
    fileList = fileList.slice(-1);
    fileList = fileList.map((file) => {
      if (file.response) {
        file.url = file.response.url;
      }
      return file;
    });
    this.fileList = fileList;
  }
  sortfunction(ary, key) {
    return ary.reduce((total, next) => {
      let index = total.findIndex((item, index, self) => {
        return item[key] === next[key];
      });
      return index === -1
        ? total.push(next) && total
        : total.splice(index, 0, next) && total;
    }, []);
  } //把名称相同的放在一起

  /**
   * 下载模板
   */
  downLoad() {
    this.buttonload = true;
    this.api
      .customExport(this.edittype, this.questionnaireId)
      .subscribe((res) => {
        const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
        let fileName = res.headers
          .get("Content-Disposition")
          .split(";")[1]
          .split("filename=")[1];
        const fileNameUnicode = res.headers
          .get("Content-Disposition")
          .split("filename*=")[1];
        // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
        if (fileName) {
          fileName = decodeURIComponent(fileName);
        }
        if (fileNameUnicode) {
          fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
        }
        const link = document.createElement("a");
        link.setAttribute("href", URL.createObjectURL(blob));
        link.setAttribute("download", fileName);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        this.buttonload = false;
      });
  }
  confirm() {
    let data = {
      projectId: this.projectId,
      questionnaireId: this.questionnaireId,
      type: "QUESTION_BOOK",
      isConfirmed: true,
    };
    this.api.confirmRelation(data).subscribe((res) => {
      if (res.result.code == 0) {
        if (this.backtype == "home") {
          this.router.navigateByUrl("/project-manage/home");
        } else {
          localStorage.setItem("backurl", this.router.routerState.snapshot.url);
          this.router.navigate(["/new-create"], {
            queryParams: {
              projectId: this.projectId,
              projectType: this.projectType,
              standardQuestionnaireId: this.standardQuestionnaireId,
              questionnaireId: this.questionnaireId,
              standardReportType: this.standardReportType,
            },
          });
        }
      }
    });
  }

  /**
   * 导出列表
   */
  exportList() {
    this.api.exportQuestionList(this.questionnaireId).subscribe((res) => {
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }
  getShowPierceThroughCombination() {
    // 获取穿透题列表
    this.api
      .showPierceThroughCombination(this.questionnaireId)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.penetrationList = res.data;
          // 有穿透题就为逐一填答
        }
      });
  }
  getlistByTypesAndName() {
    let questionTypes = [];
    if (
      this.standardReportType.includes("360_") ||
      this.standardReportType.includes("270_")
    ) {
      if (this.standardReportType.includes("_CUSTOMIZE")) {
        questionTypes = [
          "SINGLE",
          "SCALE",
          "MULTIPLE_CHOICE_ESSAY_QUESTION",
          "EVALUATION",
          "PROPORTION",
          "PROPORTION_MULTIPLE",
        ];
      } else {
        questionTypes = ["SINGLE", "SCALE", "MULTIPLE_CHOICE_ESSAY_QUESTION"];
      }
    } else {
      questionTypes = ["SINGLE", "SCALE"];
    }
    let params = {
      questionTypes: !this.questionType ? questionTypes : [this.questionType],
      searchField: this.questionStr,
      questionnaireId: this.questionnaireId,
      questionOperationType: 'PIE_THROUGH',
      questionAssociationType: 'CONDITION', // 穿透题类型-条件题
    };
    this.api.listByTypesAndName(params).subscribe((item) => {
      if (item.result.code === 0) {
        if (item.data.length !== 0) {
          item.data.forEach((item) => {
            item.checked = false;
          });
        }
        this.questionList = item.data;
        console.log(1111, item.data);
      }
    });
  }
  getlistByTypesAndNameRes() {
    let questionTypes = this.isNewQuestionBook()
      ? [
          "ESSAY_QUESTION",
          "MULTIPLE_CHOICE_ESSAY_QUESTION",
          "PROPORTION",
          "PROPORTION_MULTIPLE",
        ]
      : ["ESSAY_QUESTION", "MULTIPLE_CHOICE_ESSAY_QUESTION"];
    let params = {
      questionTypes: !this.resultType ? questionTypes : [this.resultType],
      searchField: this.resultStr,
      questionnaireId: this.questionnaireId,
      questionOperationType: 'PIE_THROUGH',
      questionAssociationType: 'RESULT', // 穿透题类型-结果题
    };
    this.api.listByTypesAndName(params).subscribe((res) => {
      if (res.result.code === 0) {
        if (res.data.length !== 0) {
          res.data.forEach((item) => {
            item.checked = false;
          });
        }
        this.resultList = res.data;
      }
    });
  }
  changeQuestionType(e) {
    // 条件类型
    this.getlistByTypesAndName();
  }
  changeResultType(e) {
    // 结果类型
    this.getlistByTypesAndNameRes();
  }

  searchQuestion() {
    // 条件模糊搜索
    this.getlistByTypesAndName();
  }
  searchResult() {
    // 结果模糊搜索
    this.getlistByTypesAndNameRes();
  }

  chooseRes(e, id, idx) {
    //
    this.questionId = id;
    if (this.resultList.length > 1) {
      //
      this.resultList.forEach((item, index) => {
        if (idx !== index) item.checked = false;
      });
    }
  }
  chooseQues(e, id, idx) {
    if (e) {
      this.questionList.forEach((item) => {
        // 全选子选项
        if (item.id === this.parentQuestionId) {
          // 取消其他题目子选项
          this.surveyStandardOptions = [];
          item.checked = false;
          item.indeterminate = false;
          item.options.options.forEach((itm) => {
            itm.checked = false;
          });
        }
      });
      this.parentQuestionId = id;
      this.questionList.forEach((item) => {
        if (item.id === id) {
          item.indeterminate = false;
          item.options.options.forEach((itm) => {
            // 滑块分值穿透特殊处理-单个增加
            if (["PROPORTION", "PROPORTION_MULTIPLE"].includes(item.type)) {
              if (itm.isScoring) {
                itm.checked = true;
                this.surveyStandardOptions.push({
                  id: itm.id,
                  minScore: null,
                  maxScore: null,
                });
              }
            } else {
              itm.checked = true;
              this.surveyStandardOptions.push({ id: itm.id });
            }
          });
        }
      });
      console.log("surveyStandardOptions", 222, this.surveyStandardOptions);
    } else {
      this.parentQuestionId = "";
      this.surveyStandardOptions = [];
      this.questionList.forEach((item) => {
        item.options.options.forEach((itm) => {
          itm.checked = false;
        });
      });
    }
    // #11253 滑块穿透
    // 若为滑块题 需要清空区间缓存
  }

  selectQuestion(idx) {
    // 下拉Question
    // this.selectId = id
    this.questionList[idx].selectFlag = !this.questionList[idx].selectFlag;
  }
  chooseOpt(e, quesId, optId, type) {
    if (e && (!this.parentQuestionId || this.parentQuestionId === quesId)) {
      this.parentQuestionId = quesId;
    }
    if (e && this.parentQuestionId !== quesId) {
      this.questionList.forEach((item) => {
        if (quesId === item.id) {
          item.options.options.forEach((itm) => {
            if (itm === quesId) {
              itm.checked = true;
            }
          });
        } else {
          item.checked = false;
          item.indeterminate = false;
          item.options.options.forEach((itm) => {
            itm.checked = false;
          });
        }
      });

      this.parentQuestionId = quesId;
      this.surveyStandardOptions = []; // 清除掉之前选中（new）
    } // 判断选中并且id为当前条件提

    this.questionList.forEach((item) => {
      if (item.id === quesId) {
        // 滑块只能单个设置
        if (["PROPORTION", "PROPORTION_MULTIPLE"].includes(type)) {
          item.options.options.forEach((ele) => {
            ele.checked = ele.id === optId;
          });
        }
        if (item.options.options.every((itm) => !itm.checked)) {
          item.checked = false;
          item.indeterminate = false;
        } else if (item.options.options.every((itm) => itm.checked)) {
          item.checked = true;
          item.indeterminate = false;
        } else {
          item.indeterminate = true;
        }
      }
    });

    if (!e) {
      // 取消选中时
      this.surveyStandardOptions.forEach((item, index) => {
        if (item.id === optId) this.surveyStandardOptions.splice(index, 1);
      });
      if (this.surveyStandardOptions.length === 0) this.parentQuestionId = "";
      return;
    }
    if (["PROPORTION", "PROPORTION_MULTIPLE"].includes(type)) {
      // this.surveyStandardOptions.push({
      //   id: optId,
      //   minScore: null,
      //   maxScore: null,
      // });
      this.surveyStandardOptions = [
        {
          id: optId,
          minScore: null,
          maxScore: null,
        },
      ];
    } else {
      this.surveyStandardOptions.push({ id: optId });
    }
    console.log("surveyStandardOptions", 111, this.surveyStandardOptions);
  }

  checkOverlap(existingScores, newScore) {
    // 查找所有具有相同id的对象
    const itemsWithSameId = existingScores.filter(
      (item) => item.id === newScore.id
    );

    // 检查新区间是否与具有相同id的现有区间重叠
    for (let i = 0; i < itemsWithSameId.length; i++) {
      const existing = itemsWithSameId[i];
      const maxStart = [existing.minScore, newScore.minScore];
      const minEnd = [existing.maxScore, newScore.maxScore];
      // 如果新区间的最小值小于现有区间的最大值，且新区间的最大值大于现有区间的最小值，则表示重叠
      if (Math.max(...maxStart) <= Math.min(...minEnd)) {
        return { res: false, score: existing }; // 发现重叠，返回false
      }
    }

    return { res: true, score: null }; // 没有重叠，返回true
  }

  createQues() {
    // 关联穿透题

    if (!this.parentQuestionId || this.surveyStandardOptions.length === 0) {
      // return this.msg.error("请选择条件题选项");
      this.customMsg.open("error", "请选择条件题选项");
      return;
    }
    if (!this.questionId) {
      // return this.msg.error("请选择结果题");
      this.customMsg.open("error", "请选择结果题");
      return;
    }
    // if (maxScore)
    for (let index = 0; index < this.surveyStandardOptions.length; index++) {
      const item = this.surveyStandardOptions[index];
      // 是滑块题
      if (item.maxScore !== undefined && item.minScore !== undefined) {
        if (item.minScore === null) {
          this.customMsg.open("error", "请输入条件题选项区间最小值");
          return;
        }
        if (item.maxScore === null) {
          this.customMsg.open("error", "请输入条件题选项区间最大值");
          return;
        }
        if (item.maxScore < item.minScore) {
          this.customMsg.open(
            "error",
            `条件题选项区间（${item.minScore}-${item.maxScore}）错误`
          );
          return;
        }
        // 结果题
        const penetrationScore = [];
        this.penetrationList.forEach((val) => {
          if (val.parentQuestionId === this.parentQuestionId) {
            console.log("this.penetrationLis", val);
            val.surveyStandardOptions.forEach((ele) => {
              penetrationScore.push({
                id: ele.id,
                maxScore: ele.maxScore,
                minScore: ele.minScore,
              });
            });
          }
        });
        const { res, score } = this.checkOverlap(penetrationScore, item);
        if (!res) {
          this.customMsg.open(
            "error",
            `条件题选项区间（${item.minScore}-${item.maxScore}）与已选关联中区间（${score.minScore}-${score.maxScore}）冲突`
          );
          return;
        }
      }
    }
    let params = {
      parentQuestionId: this.parentQuestionId,
      questionId: this.questionId,
      surveyStandardOptions: this.surveyStandardOptions,
      answerType: this.answerType,
    };
    // this.tplModalButtonLoading = true
    console.log(222, params);
    this.api.createSurveyStandardPierceThrough(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("关联成功");
        this.getList();
        this.getShowPierceThroughCombination();
        // this.tplModal.destroy();
        this.questionId = "";
        this.surveyStandardOptions = [];
        this.questionList.forEach((item) => {
          item.checked = false;
          item.indeterminate = false;
          item.options.options.forEach((itm) => {
            itm.checked = false;
          });
        });
        this.resultList.forEach((item) => {
          item.checked = false;
        });
      }
      // this.tplModalButtonLoading = false
    });
  }

  reset() {
    this.parentQuestionId = "";
    this.questionId = "";
    this.surveyStandardOptions = [];
    this.answerType = "FINAL";
    this.questionList.forEach((item) => {
      item.checked = false;
      item.indeterminate = false;
      item.options.options.forEach((itm) => {
        itm.checked = false;
      });
    });

    this.getList();

    this.resultList.forEach((item) => {
      item.checked = false;
    });
  }

  closeAssociation() {
    this.popoverVisible = false;
  }
  confirmtip() {
    this.api
      .deleteSurveyPierceThrough(this.questionnaireId)
      .subscribe((res) => {
        // 删除所有关联
        if (res.result.code === 0) {
          this.getShowPierceThroughCombination();
          this.getList();
          this.msg.success("删除成功");
        }
      });
  }
  canceltip() {}
  delOne(
    parentQuestionId: string,
    questionId: string,
    surveyStandardOptions: any[],
    parentType: string
  ) {
    let arr = [];
    if (surveyStandardOptions.length !== 0) {
      surveyStandardOptions.forEach((item) => {
        if (["PROPORTION", "PROPORTION_MULTIPLE"].includes(parentType)) {
          arr.push({
            id: item.id,
            minScore: item.minScore,
            maxScore: item.maxScore,
          });
        } else {
          arr.push({ id: item.id });
        }
      });
    }
    const params = {
      parentQuestionId: parentQuestionId,
      questionId: questionId,
      surveyStandardOptions: arr,
    };
    this.api.deleteOneSurveyPierceThrough(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.getShowPierceThroughCombination();
        // this.loadData.next()
        this.msg.success("删除成功");
      }
    });
  }

  loadData() {
    this.api.getQuestionsByProjId(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.topicList = _.filter(res.data.surveyQuestions, function(q) {
          return q.type !== "PAGE_SPLIT";
        });

        this.sourceList = this.topicList;

        this.topicList.forEach((item) => {
          if (
            item.name.zh_CN !== item.replaceName.zh_CN ||
            item.name.en_US !== item.replaceName.en_US
          ) {
            item.canEdit = true;
          }
        });
        let flag = this.topicList.findIndex((item) => {
          return item.isForceSelect === false;
        });
        if (flag === -1) {
          this.disabledAllChecked = true;
          this.isAllChecked = false;
        } else {
          this.disabledAllChecked = false;
          this.isAllChecked = false;
        }
      }
    });
  }

  /**
   * customReq 上传
   * @param item
   */
  customReqload = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.api.importThrough(formData, this.questionnaireId).subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          item.onSuccess!();
          this.msg.success("导入文件成功");
          this.pierceThrough();
          this.getList();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  exportR() {
    this.api.exportThrough(this.questionnaireId).subscribe((res) => {
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }

  /**
   * 中英文切换 -ldx
   * @param e
   */
  onSelectI18n(e): void {
    this.lan = e;
  }

  /**
   * 获取维度列表
   * @param questionnaireId
   */
  getDimension(questionnaireId) {
    this.api.listSurveyCustomDimension(questionnaireId).subscribe((res) => {
      const data = res.data || [];
      this.dimensionMap = _.keyBy(data, "code");
      // console.log('维度Map-用于code展示', this.dimensionMap)
    });
  }
  /**
   * 是否跳转到新的题本编辑页面
   *@author:wangxiangxin
   *@Date:2023/08/25
   */
  isNewQuestionBook() {
    //是否的是360 270自定义
    let flag =
      this.standardReportType.includes("_CUSTOMIZE") &&
      (this.standardReportType.includes("360_") ||
        this.standardReportType.includes("270_"));
    return flag;
  }

  // #11253 滑块穿透
  addProportionScope(ques, opt) {
    console.log("ques", ques);
    console.log("opt", opt);
    // proportionScope
  }
}
