:root {
  --ifm-scrollbar-size: 7px;
  --ifm-scrollbar-track-background-color: #f1f1f1;
  --ifm-scrollbar-thumb-background-color: silver;
  --ifm-scrollbar-thumb-hover-background-color: #a7a7a7;
}

.content {
  width: 100%;
  // height: 340px;
  // height: calc(100vh - 10px);
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
  border-radius: 4px;
  flex-wrap: wrap;
  overflow-y: auto;

  .list {
    width: 310px;
    height: 233px;
    background: #F5FAFF;
    border-radius: 8px;
    box-sizing: border-box;
    padding: 10px 15px 15px;
    margin-right: 16px;
    margin-top: 16px;
    cursor: pointer;
    border: 1px solid #F5FAFF;
    transition: all 0.1s linear;
    &:nth-child(3n) {
      margin-right: 0;
    }
    &:nth-child(1) {
      margin-top: 0;
    }
    &:nth-child(2) {
      margin-top: 0;
    }
    &:nth-child(3) {
      margin-top: 0;
    }
    &.show-list:hover {
      box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.15);
      border: 1px solid #409eff;
      padding-top: 12px;
      .list-top {
        .lab {
          display: none;
        }

        .btn {
          display: block;
          transition: all 0.5s;
        }
      }
    }

    .list-top {
      display: flex;
      height: 32px;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;

      .title {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: bold;
        color: #17314c;
        line-height: 22px;
      }

      .lab {
        padding: 2px 6px;
        background: rgba(64, 158, 255, 0.08);
        border-radius: 2px;
        font-size: 13px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #409eff;
        line-height: 18px;
      }

      .btn {
        display: none;
        background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        padding: 4px 15px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .list-content {
      width: 100%;
      height: 169px;
      background: #ffffff;
      border-radius: 4px;
      margin: 0 auto;

      h3 {
        width: 100%;
        height: 32px;
        background: linear-gradient(270deg, #74cdff 0%, #409eff 100%);
        border-radius: 4px 4px 0px 0px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 1;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding-left: 10px;
      }
      &-img{
        width: 100%;
        height: calc(100% - 32px);
        display: flex;
        justify-content: center;
        align-items: center;
        img{
          height: 100%;
          width: 100%;
        }
      }
      .h-full {
        height: 100%;
      }

      // img {
      //   width: calc(100% - 24px);
      //   height: 136px;
      //   margin: 12px auto 0;
      //   display: block;
      // }
    }

    &.disabled-list {
      background: #f3f3f3;
      border-color: #f3f3f3;

      &:hover {
        cursor: not-allowed;
      }

      .list-top {
        .lab {
          background: rgba(170, 170, 170, 0.08);
          color: #aaaaaa;
        }
      }

      .list-content {
        h3 {
          background: linear-gradient(90deg, #aaaaaa 0%, #c7c7c7 100%);
          color: #ffffff;
        }
      }
    }
  }
}

.del_icon,
.edit_icon_close {
  width: 18px;
  height: 18px;
  background-size: 100% 100% !important;
  margin: 0 5px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  // background-color: transparent;
  background-color: #f1f1f1;
  box-shadow: none;
}

::-webkit-scrollbar-thumb {
  // background-color: #e9e9e9;
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
