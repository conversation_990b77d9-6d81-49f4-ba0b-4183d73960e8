import { Component, OnInit, Input, ViewChild } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { NzTreeComponent, NzMessageService } from "ng-zorro-antd";
import { SurveyApiService } from "../../../service/survey-api.service";
import { DownloadUtilService } from "../../../service/download-util.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";
@Component({
  selector: "app-population-label-page",
  templateUrl: "./population-label-page.component.html",
  styleUrls: ["./population-label-page.component.less"],
})
export class PopulationLabelPageComponent implements OnInit {
  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;
  @Input() projectId: string;

  constructor(
    private api: SurveyApiService,
    private msgServ: NzMessageService,
    private until: DownloadUtilService,
    private http: HttpClient,
    private customMsg: MessageService
  ) {}
  isAll: boolean = false;
  searchValue: string = "";
  searchName: string = "";
  ids: string[] = []; // 选中id
  treeList: any[] = [];
  dataList: any[] = [];
  checkedKeys: any[] = [];

  selectedNodes: any = {}; // 树选中

  chartLoaded: boolean = false;

  orgPersonList: any[] = [];
  defaultExpandedKeys: any[] = [];

  public chartOption: any;
  containerWidth = "520";
  containerHeight = "350";

  page: any = {
    current: 1,
    size: 20,
    total: 1,
  };

  loadData(isSearch?: boolean) {
    if (isSearch) {
      this.page = {
        current: 1,
        size: 20,
        total: 1,
      };
    }
    let params: any = {
      isAll: this.isAll,
      ids: this.ids,
      page: this.page,
      projectId: this.projectId,
      value: this.searchName,
    };
    this.api.searchDemographicAnswerRate(params).subscribe((res) => {
      this.dataList = res.data;
      this.page = res.page;
      this.dataList.forEach((item) => {
        item.checked = false;
        if (this.isAll) item.checked = true;
      });
    });
  }

  changeRespondentAllChecked() {
    // 列表全选
    this.ids = [];
    this.isAll = !this.isAll;
    this.loadData();
  }

  changeRespondentChecked(e, id) {
    // 单选
    let index = this.ids.findIndex((item) => {
      return item === id;
    });
    if (index === -1) {
      if (e) {
        this.ids.push(id);
      }
    } else {
      if (!e) {
        this.ids.splice(index, 1);
      }
    }
  }

  exportRespondentList() {
    // 导出填答人列表
    if (this.ids.length === 0 && !this.isAll)
      // return this.msgServ.warning("请选择要导出的人员");
      return this.customMsg.open("warning", "请选择要导出的人员");
    let params: any = {
      isAll: this.isAll,
      ids: this.ids,
      page: this.page,
      projectId: this.projectId,
      value: this.searchName,
    };
    this.api.exportDemographicAnswerRate(params).subscribe((res) => {
      this.until.downFile(res);
    });
  }

  ngOnInit() {
    this.loadData();
  }
}
