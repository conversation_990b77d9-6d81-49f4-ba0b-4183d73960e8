.container {
  //   background-color: yellowgreen;
  height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

img {
  width: 56px;
  height: 57px;
}

.text1 {
  //   background-color: wheat;
  margin-top: 26px;
  margin-bottom: 17px;

  height: 33px;
  font-size: 24px;
  font-weight: 300;
  color: rgba(23, 49, 76, 1);
  line-height: 33px;
}

.text2 {
  //   background-color: thistle;
  margin-bottom: 5px;
  height: 24px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(23, 49, 76, 1);
  line-height: 24px;
}

.text3 {
  //   border: tomato;
  margin-bottom: 5px;
  height: 24px;
  font-size: 12px;
  font-weight: 400;
  color: rgba(23, 49, 76, 1);
  line-height: 24px;
}

.btn {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;

  a {
    color: #17314C;
  }

  button {
    flex: 1;
  }

  .submit {
    width: 156px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    height: 38px;
    line-height: 38px;
    font-size: 16px;
    color: #aaa;
    font-weight: 500;
    background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    color: #fff;
    border-radius: 19px;
    border-style: none;
    outline: none;

    &.act {
      background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      color: #fff;
    }
  }

}