.setType {
  &-menu {
    width: 100%;
    margin-top: -16px;
    ::ng-deep {
      .ant-tabs-tab {
        font-size: 16px;
        font-weight: 400;
        color: #409eff;
        line-height: 22px;
      }
      .ant-tabs-ink-bar {
        height: 4px;
        border-radius: 2px;
      }
    }
    &-top {
      .gutter-row {
        display: flex;
        justify-content: flex-start;
        > span {
          white-space: nowrap;
          margin-right: 8px;
          margin-top: 8px;
        }
      }
    }
    &-add {
      margin-top: 16px;
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      flex-direction: row;
      justify-content: space-between;
      a {
        font-size: 14px;
        font-weight: 400;
        color: #409eff;
        line-height: 20px;
      }
    }
    &-items {
      .model-card {
        margin-top: 16px;
        &-title {
          background: #f5faff;
          border-radius: 8px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 16px;
          p {
            font-size: 16px;
            font-weight: 600;
            color: #17314c;
            line-height: 25px;
          }
          i {
            color: #409eff;
            font-size: 14px;
            cursor: pointer;
          }
        }
        &-row {
          margin-top: 8px;
          border-radius: 8px;
          border: 1px solid #ececec;
          padding: 8px 16px;
          &-col {
            margin: 8px 0;
          }
        }
        &-extra {
          border-radius: 8px;
          border: 1px solid #ececec;
          padding: 16px 16px 8px 16px;
          ::ng-deep {
            .ant-descriptions-title {
              margin-bottom: 8px;
              font-weight: 600;
            }
            .ant-descriptions-row > th,
            .ant-descriptions-row > td {
              padding: 8px 0;
            }
          }
        }
      }
      &-empty {
        margin: 12px 0;
        border-radius: 8px;
        border: 1px solid #ececec;
        padding-top: 16px;
      }
      .model-table {
        margin: 12px 0;
        ::ng-deep {
          .ant-table-column-title {
            font-weight: 600;
          }
        }
        .danger {
          color: #ff4f40;
        }
      }
    }
    ::ng-deep {
      .ant-select-selection--multiple .ant-select-selection__choice {
        max-width: 90%;
      }
    }
  }
  &-footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 16px;
    text-align: right;
    left: 0px;
    background: #fff;
  }
}
.thumbnail-img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
  }
}
