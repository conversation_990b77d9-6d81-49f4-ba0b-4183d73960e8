import { Component, OnInit, ElementRef } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import _ from "lodash";
import { ActivatedRoute } from "@angular/router";
import { ProjectManageService } from "../../service/project-manage.service";

import {
  NzMessageService,
  NzModalService,
  NzModalRef,
  UploadXHRArgs,
} from "ng-zorro-antd";
import { HttpEvent } from "@angular/common/http";
@Component({
  selector: "app-project-manage-norm",
  templateUrl: "./project-manage-norm.component.html",
  styleUrls: ["./project-manage-norm.component.less"],
  // providers: [DatePipe]
})
export class ProjectManageNormComponent implements OnInit {
  Breadcrumbs = [];

  name: string = ""; // 搜索 活动名称

  page: any = {
    // 分页条件
    current: 1,
    pages: 1,
    size: 10,
    total: 11,
  };
  regularList = [
    { name: "被评估人编号", id: "code", isEdit: false, inDetail: false },
    { name: "被评估人姓名", id: "name", isEdit: false, inDetail: false },
    {
      name: "团队均分",
      id: "averageScore",
      active: true,
      isEdit: true,
      inDetail: false,
    },
    { name: "排名", id: "rank", isEdit: true, inDetail: false },
    { name: "总人数", id: "totalNum", isEdit: true, inDetail: false },
  ];
  list = [];
  averageScoreName: any = {}; //团队均分名称
  listOfData: any[] = [];
  validateForm: FormGroup;
  visible: boolean;
  visibleTitle: boolean;
  isLoading: boolean;
  nzLoading: boolean;
  projectId: string;
  is360Project = false; //是否是360活动
  tableList = []; //table列表
  activeIndex = 0; //当前选中的index
  constructor(
    private projectManageService: ProjectManageService,
    private route: ActivatedRoute,
    private ele: ElementRef,
    private fb: FormBuilder,
    private msg: NzMessageService
  ) {}
  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      this.projectId = params["projectId"];
    });
    this.Breadcrumbs = JSON.parse(localStorage.getItem("break")) || [];
    this.Breadcrumbs.forEach((item) => {
      if (item.Highlight) {
        if (item.name == "活动管理") {
          item.path = "/project-manage/home";
        }
        if (item.name == "活动设置") {
          item.path = "/new-create";
        }
        item.Highlight = false;
      }
    });
    this.Breadcrumbs.push({
      path: "",
      name: "内外部常模",
      Highlight: true,
    });
    this.getTableList();
  }

  submitForm(): void {
    for (const i in this.validateForm.controls) {
      this.validateForm.controls[i].markAsDirty();
      this.validateForm.controls[i].updateValueAndValidity();
    }

    if (this.validateForm.valid) {
      let data = {
        name: {
          en_US: this.validateForm.value.en_US,
          zh_CN: this.validateForm.value.zh_CN,
        },
        projectId: this.projectId,
        reportType: this.tableList[this.activeIndex].type,
        questionaireId: this.tableList[this.activeIndex].questionaireId,
      };
      this.projectManageService
        .updateAverageScoreName(data)
        .subscribe((res) => {
          if (res.result.code == 0) {
            this.averageScoreName = data.name;
            this.msg.success("修改成功！");
          }
          this.visible = false;
        });
    }
  }
  loadListData(isSearch?: boolean) {
    if (isSearch) {
      this.page = {
        // 分页条件
        current: 1,
        pages: 1,
        size: 10,
        total: 1,
      };
    }
    let params: any = {
      page: { ...this.page, searchCount: true },
    };
    this.name ? (params.name = this.name) : "";
    this.projectId ? (params.projectId = this.projectId) : "";
    params.reportType = this.tableList[this.activeIndex].type;
    params.questionaireId = this.tableList[this.activeIndex].questionaireId;
    this.nzLoading = true;
    this.projectManageService.getPagedListByPage(params).subscribe((res) => {
      let data = res.data;

      if (data.page.current === 1) {
        this.tableList[this.activeIndex].list = [...this.regularList];
        data.heads.forEach((element) => {
          element.isEdit = true;
          element.inDetail = true;
          element.name = element.name.zh_CN;
          this.tableList[this.activeIndex].list.push(element);
        });
      }
      this.averageScoreName = data.averageScoreName;
      this.nzLoading = false;
      this.page = data.page;
      this.tableList[this.activeIndex].listOfData = data.list;
    });
  }

  search() {
    this.loadListData(true);
  }
  clear() {
    this.projectManageService
      .clearDeleteAll(this.projectId)
      .subscribe((res) => {
        if (res.result.code == 0) {
          this.msg.success("清除成功！");
          this.visibleTitle = false;
          this.clearParams();
        }
      });
  }
  clearParams() {
    // 清空参数
    this.name = ""; // 搜索 活动名称
    this.page = {
      // 分页条件
      current: 1,
      pages: 1,
      size: 10,
      total: 1,
    };
    this.loadListData(true);
  }
  clickMe(val): void {
    if (val) {
      this.visible = false;
    } else {
      this.visibleTitle = false;
    }
  }
  // 分页相关
  pageIndexChange(e) {
    // 页码改变
    if (e === 0 || e > this.page.pages) return;
    this.page.current = e;
    this.loadListData();
    console.log(1);
  }

  pageSizeChange(e) {
    // 每页条数改变
    this.page.size = e;
    this.page.current = 1;
    this.loadListData();
  }
  onChange(value: string, index: number, item: any): void {
    this.updateValue(value, index, item);
  }

  onBlur(index: number, item: any): void {
    if (item.inDetail) {
      if (
        this.tableList[this.activeIndex].listOfData[index].detail[
          item.id
        ].charAt(
          this.tableList[this.activeIndex].listOfData[index].detail[item.id]
            .length - 1
        ) === "."
      ) {
        this.updateValue(
          this.tableList[this.activeIndex].listOfData[index].detail[
            item.id
          ].slice(0, -1),
          index,
          item
        );
      }
    } else {
      if (
        this.tableList[this.activeIndex].listOfData[index][item.id].charAt(
          this.tableList[this.activeIndex].listOfData[index][item.id].length - 1
        ) === "."
      ) {
        this.updateValue(
          this.tableList[this.activeIndex].listOfData[index][item.id].slice(
            0,
            -1
          ),
          index,
          item
        );
      }
    }
  }

  updateValue(value: string, index: number, item: any): void {
    const reg = /^-?(0|[1-9][0-9]*)(\.[0-9]*)?$/;
    if ((!isNaN(+value) && reg.test(value)) || value === "") {
      if (item.id == "rank" || item.id == 'totalNum') {
        if (value.split(".").length < 2) {
          item.inDetail
            ? (this.tableList[this.activeIndex].listOfData[index].detail[
                item.id
              ] = value)
            : (this.tableList[this.activeIndex].listOfData[index][
                item.id
              ] = value);
        }
      } else {
        if (value.split(".").length < 2 || value.split(".")[1].length <= 2) {
          item.inDetail
            ? (this.tableList[this.activeIndex].listOfData[index].detail[
                item.id
              ] = value)
            : (this.tableList[this.activeIndex].listOfData[index][
                item.id
              ] = value);
        }
      }
    }
    this.ele.nativeElement.querySelector(
      "#" + item.id + index
    ).value = item.inDetail
      ? this.tableList[this.activeIndex].listOfData[index].detail[item.id]
      : this.tableList[this.activeIndex].listOfData[index][item.id];
  }
  edit(index: number) {
    if (this.tableList[this.activeIndex].listOfData[index].isEdit) {
      //这里处理保存
      let data = { ...this.tableList[this.activeIndex].listOfData[index] };
      let detail = [];
      for (let key in data.detail) {
        detail.push({ dimensionCode: key, score: data.detail[key] });
      }
      data.detail = detail;
      this.projectManageService.updateNorm(data).subscribe((res) => {
        if (res.result.code == 0) {
          this.tableList[this.activeIndex].listOfData[index].isEdit = false;
          this.msg.success("保存成功！");
        }
      });
    } else {
      this.tableList[this.activeIndex].listOfData[index].isEdit = true;
    }
    console.log(index, this.tableList[this.activeIndex].listOfData[index]);
  }
  editName() {
    this.validateForm = this.fb.group({
      formLayout: ["horizontal"],
      zh_CN: [this.averageScoreName.zh_CN, [Validators.required]],
      en_US: [this.averageScoreName.en_US],
    });
  }
  /**
   * customReq 导入
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };
  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.projectManageService
      .importNorm(formData, this.projectId)
      .subscribe(
        (event: HttpEvent<any>) => {
          item.onSuccess!();
          let res: any = event;
          if (res.result.code === 0) {
            this.msg.success("导入文件成功");
            this.clearParams();
          }
        },
        (err) => {
          item.onError!(err, item.file!);
        }
      );
  }

  exportR() {
    // 导出
    if (this.isLoading) false;
    this.isLoading = true;
    this.projectManageService.exportNorm(this.projectId).subscribe((res) => {
      this.isLoading = false;
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）

      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }
  /**
   * 获取table列表
   *@author:wangxiangxin
   *@Date:2023/12/19
   */
  getTableList() {
    // 测评展示内外部常模的type类型
    let type = [
      "CA",
      "LPA",
      "CA_LPA",
      "EPA",
      "MCA",
      "MCA_MID",
      "MCA_HIGH",
      "AT",
      "AMA",
      "BLANK_CUSTOM",
    ];
    this.projectManageService.getDetail(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.is360Project = res.data.is360Project;
        res.data.questionnaires.forEach((item) => {
          if (item.reportType == "TIP_NEW_2") {
            item.tipReportItems.forEach((data) => {
              if (
                type.includes(data.reportType) ||
                data.reportType == "TIP_NEW_2"
              ) {
                let obj = {
                  type: data.reportType,
                  name: data.name.zh_CN,
                  questionaireId: item.id,
                  list: [],
                  listOfData: [],
                };
                this.tableList.push(obj);
              }
            });
          } else {
            // // 因为空白问卷也不需要展示 table 所以也把this.is360Project 手动改为true 好做判断
            // if (item.reportType == "BLANK_CUSTOM") {
            //   this.is360Project = true;
            // }
            if (type.includes(item.reportType) || this.is360Project) {
              let obj = {
                type: item.reportType,
                name: item.originalName.zh_CN,
                questionaireId: item.id,
                list: [],
                listOfData: [],
              };
              this.tableList.push(obj);
            }
          }
        });
        this.activeIndex = 0;
        this.loadListData();
      }
    });
  }
  /**
   * 切换table 更新索引 重新请求数据
   *@author:wangxiangxin
   *@Date:2023/12/19
   */
  updateIndex(i) {
    if (this.activeIndex == i) {
      return;
    }
    this.activeIndex = i;
    this.name = "";
    this.loadListData(true);
  }
}
