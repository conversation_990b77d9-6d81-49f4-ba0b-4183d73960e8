import { Component, OnInit, Input } from "@angular/core";

import { NewPrismaService } from "../new-prisma.service";
import { Router, ActivatedRoute } from "@angular/router";
@Component({
  selector: "app-draw",
  templateUrl: "./draw.component.html",
  styleUrls: ["./draw.component.less"],
})
export class DrawComponent implements OnInit {
  @Input() privewData: any;
  @Input() lotteryResultData: any;
  @Input() pageChange: boolean;
  @Input() type: string;

  model;
  projectId;
  // selectId : number = -1;

  data: any;
  // prizeBg : string = '';
  imgUrl: string = "assets/images/prisma/";
  customImgUrl: string = "";
  // conBg : string = '';

  // showPrize : boolean = false;
  d: any;
  h: any;
  m: any;

  showWinningPage: boolean = false;

  constructor(
    private api: NewPrismaService,
    private activatedRoute: ActivatedRoute
  ) {}

  // leftMargin;

  ngOnInit() {
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "http://***********/";
    }
    this.customImgUrl = `${baseUrl}api/file/www/`;
    this.projectId = this.activatedRoute.snapshot.queryParams.projectId;
    // this.getModel()
    this.countdown();
  }

  countdown() {
    // 计时器
    let now = new Date(this.privewData.nowTime);
    let end = new Date(this.privewData.endTime);
    let time = end.getTime() - now.getTime();
    let d = parseInt(time / 1000 / 60 / 60 / 24 + "");
    let h = parseInt(((time / 1000 / 60 / 60) % 24) + "");
    let m = parseInt(((time / 1000 / 60) % 60) + "");
    this.d = d <= 9 ? "0" + d : d;
    this.h = h <= 9 ? "0" + h : h;
    this.m = m <= 9 ? "0" + m : m;
    setTimeout(() => {
      this.countdown();
    }, 60000);
  }

  toWinningPage() {
    // 切换到中奖也
    this.showWinningPage = true;
  }
}
