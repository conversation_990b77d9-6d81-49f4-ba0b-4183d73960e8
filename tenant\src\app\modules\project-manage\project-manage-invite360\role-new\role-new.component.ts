import { Component, Input, OnInit, ChangeDetector<PERSON>ef, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { ProjectManageService } from "../../../service/project-manage.service";
import { NewPrismaService } from "@src/modules/new-prisma/new-prisma.service";
import { ComponentService } from "../../../../shared/component.service";
import { NzDrawerRef, NzMessageService } from "ng-zorro-antd";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";

@Component({
  selector: "app-role-new",
  templateUrl: "./role-new.component.html",
  styleUrls: ["./role-new.component.less"],
})
export class RoleNewComponent implements OnInit,OnDestroy {
  private routerSubscription: Subscription;
  constructor(
    private api: ProjectManageService,
    private common: ComponentService,
    private cdr: ChangeDetectorRef,
    private drawerRef: NzDrawerRef,
    private msg: NzMessageService,
    private customMsg: MessageService,
    private router: Router,
    private prismaApi: NewPrismaService
  ) {}
  @Input() projectId;
  @Input() standardReportType;
  @Input() sonrolelist;
  @Input() roleskey;
  @Input() isCustomRoleWeight;
  @Input() Projectstatus;
  @Input() standardQuestionnaireId;

  demoValue: number = 3;
  newRolelist: any[] = [];
  rolelist: any[] = [];
  standardType = false;
  precision = 2;
  i18nData: any[] = [];

  ngOnInit() {
    this.getRoleList();
    if (this.standardReportType.indexOf("270") != -1) {
      this.standardType = true;
    }
    this.routerSubscriptionFun();
    // this.standardReportType.indexOf("270") != -1
    this.getLanOptions();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  addRole(data?) {
    this.rolelist.push({
      name: data ? data.name : { zh_CN: "", en_US: "" },
      investigatorTitle: data
        ? data.investigatorTitle
        : { zh_CN: "", en_US: "" },
      projectId: this.projectId,
      percentage: data ? data.percentage : 1,
    });
  }

  deleteRole(index: number): void {
    // let list = _.cloneDeep
    this.rolelist.splice(index, 1);
  }

  getRoleList() {
    this.api.listRole(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        if (this.sonrolelist) {
          this.sonrolelist.forEach((val) => {
            res.data.forEach((item) => {
              if (val.roleId == item.id) {
                item.selet = true;
                item.percentage = val.weights;
              }
            });
          });
          this.rolelist = res.data.filter((item) => {
            return item.selet;
          });
        } else {
          this.rolelist = res.data;
        }
        console.log('this.rolelist', this.rolelist)
      }
    });
  }

  submit() {
    let isSave = true;
    this.rolelist.forEach((val) => {
      if (!isSave) {
        return;
      }
      if (!val.name || !val.name.zh_CN) {
        isSave = false;
        // this.msg.error("角色名称不能为空");
        this.customMsg.open("error", "角色名称不能为空");
      }

      if (!val.investigatorTitle || !val.investigatorTitle.zh_CN) {
        isSave = false;
        // this.msg.error("对被评估人的称呼不能为空");
        this.customMsg.open("error", "对被评估人的称呼不能为空");
      }
    });

    if (!isSave) {
      return;
    }
    let that = this;
    that.drawerRef.close(this.rolelist);
    // this.api.createSurveyRole({ projectId: this.projectId, surveyRoles: this.rolelist }).subscribe(res => {
    //   if (res.result.code === 0) {
    //     this.common.message('success');
    //   }
    // });
  }
  changeNameValue(name, i, str) {
    this.rolelist[i][str] = name;
  }
  // trackByIndex(_: number, data: VirtualDataInterface): number {
  //   return data.index;
  // }

  
  /**
   * getLanOptions 获取语言的配置信息
   */
  async getLanOptions() {
    const currentLansRes = await this.prismaApi.getLanguages().toPromise();
    const defaultCode = ["zh_CN", "en_US"];
    const sessionProjectLanguages =
      JSON.parse(sessionStorage.getItem("projectLanguages")) || [];
    const projectLanguages = sessionProjectLanguages.length
      ? sessionProjectLanguages
      : defaultCode;
    let lanData = currentLansRes.data
      .filter((val) => projectLanguages.includes(val.value))
      .map((val) => ({
        label: val.name,
        key: val.value,
        // value: this.value[val.value],
      }));
    lanData.sort((a, b) => {
      if (a.key === "zh_CN") return -1;
      if (b.key === "zh_CN") return 1;
      if (a.key === "en_US") return -1;
      if (b.key === "en_US") return 1;
      return 0;
    });
    this.i18nData = lanData;
  }
  getLanOptionsValue(value){
    const data = this.i18nData.map((val)=>({
      ...val,
      value: value[val.key],
    }))
    return data;
  }

}
