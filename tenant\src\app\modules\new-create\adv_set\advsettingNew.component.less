:host {
  .advanced {
    .mr-8 {
      margin-right: 8px;
    }
    .ml-8 {
      margin-left: 8px;
    }
    .ml-16 {
      margin-left: 16px;
    }
    .mr-16 {
      margin-right: 16px;
    }
    .mb-16 {
      margin-bottom: 16px;
    }
    .mb-24 {
      margin-bottom: 24px;
    }
    .mr-24 {
      margin-right: 24px;
    }
    .mt-6 {
      margin-top: 6px;
    }
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      line-height: 22px;
      span {
        margin-left: 8px;
      }
    }
    .title-between {
      display: flex;
      justify-content: space-between;
      span {
        &:nth-child(1) {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          line-height: 22px;
        }
      }
    }
    .suggest {
      font-size: 12px;
      font-weight: 400;
      color: #595959;
      line-height: 17px;
    }
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #595959;
      line-height: 20px;
    }
    .avatar-uploader {
      .avatar {
        width: 86px;
        height: 86px;
      }
      .ant-upload-text {
        margin-top: 8px;
        font-size: 14px;
        color: #409eff;
      }
    }
    .tip-iocn{
      color: #BFBFBF;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 10px 16px;
    text-align: right;
    left: 0px;
    background: #fff;
  }
  ::ng-deep {
    .ant-tabs .ant-tabs-tab {
      padding: 0 0 8px 0;
    }
    .ant-tabs-ink-bar {
      height: 4px;
      border-radius: 2px;
    }
    .ant-checkbox-wrapper {
      margin-left: 0;
      margin-right: 12px;
    }
    .ant-checkbox + span {
      padding: 0 4px;
    }
    // .ant-tabs-tabpane {
    //   overflow-y: auto;
    //   overflow-x: hidden;
    //   height: calc(100vh - 200px);
    //   padding-right: 10px;
    // }
  }
}

::ng-deep {
  .permission-tool {
    margin-top: 8px;
    .ant-tooltip-arrow {
      &::before {
        width: 8px;
        height: 8px;
        background-color: #ffffff;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.2);
      }
    }
    .ant-tooltip-inner {
      max-width: 240px;
      padding: 0;
      background: #ffffff;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.2);
      .permission-tool-body {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #495970;
        ul {
          padding: 16px 16px 6px 16px;
          li {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            .tips {
              font-size: 12px;
              color: #aaaaaa;
            }
          }
        }
      }
      .permission-tool-footer {
        display: flex;
        align-items: center;
        justify-content: right;
        padding: 10px 16px;
        border-top: 1px solid #ececec;
      }
    }
  }
  // 角色有效填答数
  .answer-role-person-num {
    section {
      max-height: calc(60vh - 90px);
      overflow-y: auto;
      // padding-top: 4px;
      padding-right: 16px;
      margin-bottom: 12px;
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        line-height: 22px;
      }
      .list {
        margin-top: 16px;
        display: flex;
        align-items: center;
        > span {
          font-size: 14px;
          font-weight: 400;
          color: #595959;
          line-height: 20px;
        }
        .mr-8 {
          margin-right: 8px;
        }
        .ml-8 {
          margin-left: 8px;
        }
        &:nth-child(1){
          margin-top: 0;
        }
      }
    }
    footer {
      margin: 0 -16px;
      display: flex;
      justify-content: flex-end;
      padding: 12px 16px 0 16px;
      border-top: 1px solid #ececec;
    }
  }
  .answer-same-rate {
    section {
      max-height: calc(65vh - 90px);
      overflow-y: auto;
      min-width: 660px;
      padding-right: 16px;
      margin-bottom: 12px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        > span {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          line-height: 22px;
          margin-right: 8px;
        }
        &-extra {
          button {
            padding: 0;
          }
          .my-16 {
            margin-left: 16px;
            margin-right: 16px;
          }
          .mr-8 {
            margin-right: 8px;
          }
        }
      }
      .desc {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #595959;
        line-height: 17px;
        margin-top: 16px;
        // margin-bottom: 16px;
      }
      .list1 {
        min-width: 80%;
        margin-top: 8px;
        margin-bottom: 8px;
        ::ng-deep{
          .ant-descriptions .ant-descriptions-view{
            border: none !important;
            table{
              table-layout: auto;
              td,th,td{
                border: none !important;
              }
              .ant-descriptions-row{
                border: none !important;
              }
              .ant-descriptions-item-label{
                background-color: #fff;
                padding-left: 0;
                padding-right: 0;
              }
              .ant-descriptions-item-content{
                padding-left: 0;
                padding-right: 0;
              }
            }
          } 
        }
        > span {
          font-size: 14px;
          font-weight: 400;
          color: #595959;
          line-height: 20px;
        }
        .mr-8 {
          margin-right: 8px;
        }
        .ml-8 {
          margin-left: 8px;
        }
        .mr-24 {
          margin-right: 24px;
        }
      }
      .list2 {
        margin-top: 16px;
        &-item {
          margin-bottom: 16px;
          > span {
            font-size: 14px;
            font-weight: 400;
            color: #595959;
            line-height: 20px;
          }
          .mr-8 {
            margin-right: 8px;
          }
          .ml-8 {
            margin-left: 8px;
          }
        }
      }
    }
    footer {
      margin: 0 -16px;
      display: flex;
      justify-content: flex-end;
      padding: 12px 16px 0 16px;
      border-top: 1px solid #ececec;
    }
  }
  .ant-popover-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #262626;
    line-height: 28px;
    padding: 8px 16px;
  }
  .ant-popover-inner {
    border-radius: 18px;
  }
  .link-btn{
    padding-left: 0;
    padding-right: 0;
    margin-left: 16px;
    margin-right: 16px;
  }
}
