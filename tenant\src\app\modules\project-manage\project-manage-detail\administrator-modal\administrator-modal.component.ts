import { Component, OnInit, Input, OnDestroy } from "@angular/core";
import { TransferItem } from "ng-zorro-antd/transfer";
import { NzMessageService } from "ng-zorro-antd/message";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { NzDrawerRef } from "ng-zorro-antd";
@Component({
  selector: "app-administrator-modal",
  templateUrl: "./administrator-modal.component.html",
  styleUrls: ["./administrator-modal.component.less"],
})
export class AdministratorModalComponent implements OnInit, OnDestroy {
  @Input() userName;
  @Input() projectId;

  // loading
  isSpinning: boolean = false;
  // 保存loading
  isLoading = false;
  // 穿梭框数据
  listMainManager: TransferItem[] = [];
  private routerSubscription: Subscription;

  constructor(
    private drawerRef: NzDrawerRef,
    private api: SurveyApiService,
    private msg: NzMessageService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadList();
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  // select(ret: {}): void {
  // console.log('nzSelectChange', ret);
  // console.log('nzSelectChange', this.listMainManager);
  // }

  // change(ret: {}): void {
  // console.log('nzChange', ret);
  // console.log('nzChange', this.listMainManager);
  // }

  // search(e?) {}

  /**
   * 获取左右侧数据
   */
  async loadList() {
    this.isSpinning = true;
    let listMainManager_left = [];
    let listMainManager_right = [];
    // 左侧-未使用
    const {
      data: leftData = [],
      result: leftRes,
    } = await this.api.getListUnSelectMainManager(this.projectId).toPromise();
    if (leftRes.code === 0) {
      listMainManager_left = this.mapList(leftData);
    }
    // 右侧-已设置
    const {
      data: rightData = [],
      result: rightRes,
    } = await this.api.getListMainManager(this.projectId).toPromise();
    if (rightRes.code === 0) {
      listMainManager_right = this.mapList(rightData, "right");
    }
    this.listMainManager = [...listMainManager_left, ...listMainManager_right];
    this.isSpinning = false;
  }

  /**
   * 穿梭框数据处理
   * @param list
   * @param status
   * @returns
   */
  mapList(list: any[], status?: any) {
    const data = list.map((item) => ({
      ...item,
      key: item.id,
      title: item.userName,
      description: "",
      direction: status,
    }));
    return data;
  }

  /**
   * 取消
   */
  close() {
    this.drawerRef.close();
  }

  /**
   * 保存
   */
  createManager() {
    let params = [];
    this.listMainManager.map((item) => {
      if (item.direction === "right") {
        params.push({
          projectId: this.projectId,
          userId: item.id,
        });
      }
    });
    this.isLoading = true;
    this.api.createMainManager(this.projectId, params).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("创建主管理员成功！");
        // this.NzModalRef.triggerOk();
        this.drawerRef.close(true);
      }
      this.isLoading = false;
    });
  }
}
