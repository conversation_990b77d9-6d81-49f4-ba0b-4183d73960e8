:root {
  --ifm-scrollbar-size: 7px;
  --ifm-scrollbar-track-background-color: #f1f1f1;
  --ifm-scrollbar-thumb-background-color: silver;
  --ifm-scrollbar-thumb-hover-background-color: #a7a7a7;
}

.box {
  // padding: 30px;
  padding-top: 0;
  max-height: 610px;
  position: relative;
}

.content {
  width: 100%;
  // height: 340px;
  height: calc(100vh - 150px);
  display: flex;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  overflow-y: auto;

  > div {
    flex: 1 0 450px;
    box-sizing: border-box;
    justify-content: space-between;

    .title {
      padding: 0 15px;
      display: flex;
      align-items: center;
      height: 46px;
      box-sizing: border-box;
      border-bottom: 1px solid #e6e6e6;

      h3 {
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #17314c;
        line-height: 1;
      }

      p {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 1;
        margin-left: 10px;
      }
    }
  }

  &-left {
    .list-box {
      padding: 12px;

      .item {
        width: 100%;
        height: 36px;
        box-sizing: border-box;
        padding: 0 8px;
        border-radius: 2px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;

        h5 {
          flex: 1 0 200px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #17314c;
          line-height: 1;
        }

        &-btn {
          display: none;
          justify-content: end;
          align-items: center;

          &.showTip {
            display: flex;
          }

          span {
            font-size: 12px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #409eff;
            line-height: 1;
            margin-left: 15px;
          }
        }

        &:hover {
          background: rgba(64, 158, 255, 0.05);

          .item-btn {
            display: flex;
          }
        }

        &.active {
          background: rgba(64, 158, 255, 0.05);

          h5 {
            font-weight: 600;
            color: #409eff;
          }

          // .item-btn {
          //   display: flex;
          // }
        }
      }
    }
  }

  &-right {
    border-left: 1px solid #e6e6e6;

    .title {
      justify-content: space-between;

      ::ng-deep .ant-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 5px 14px;
        min-width: 72px;
        height: 30px;
        background: #ffffff;
        border-radius: 15px;
        border-color: #409eff;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #409eff;
        text-shadow: none;
        box-shadow: none;
        margin-left: 8px;
        cursor: pointer;

        .anticon-loading {
          margin-left: 0px;
        }
      }
    }

    .file-list {
      padding: 0 15px;

      .upload-box {
        img {
          display: block;
          width: 93px;
          height: 93px;
          margin: 40px auto 0;
        }

        p {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #409eff;
          line-height: 20px;
          text-align: center;
          margin: 0;
          margin-top: 8px;
        }
      }

      .tip {
        width: 301px;
        height: 40px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;
        text-align: center;
        margin: 20px auto 0;
      }

      .list-box {
        display: flex;
        flex-direction: column;

        .list-head {
          width: 100%;
          height: 48px;
          padding: 0 15px;
          box-sizing: border-box;
          background: #f5f6fa;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .list-head-l {
            flex: 1 0 0;
            display: flex;
            align-items: center;

            span {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #495970;
              line-height: 20px;

              &:last-of-type {
                color: rgba(248, 68, 68, 1);
                margin-left: 5px;
              }
            }
          }

          .list-head-r {
            display: flex;
            align-items: center;
            cursor: pointer;

            &.dis {
              cursor: not-allowed;
            }

            img {
              display: block;
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }

            span {
              font-size: 14px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #419eff;
              line-height: 20px;
            }
          }
        }

        .item-file {
          height: 50px;
          box-sizing: border-box;
          justify-content: space-between;
          align-items: center;
          // border: 1px solid #d9d9d9;
          // border-radius: 4px;
          padding: 4px 8px;

          .item-file-img {
            display: flex;
            align-items: center;

            .icon-caidan {
              margin-right: 8px;
              font-size: 20px;
            }

            .img-box {
              display: flex;
              align-items: center;
              cursor: pointer;

              img {
                max-width: 100px;
                max-height: 32px;
                margin-right: 8px;
              }
            }

            .img-name {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #495970;
              line-height: 20px;
            }
          }

          .img-del {
            display: flex;
            align-items: center;
            cursor: pointer;

            img {
              display: block;
              width: 18px;
              height: 18px;
            }
          }
        }
      }
    }
  }
}

.del_icon,
.edit_icon_close {
  width: 18px;
  height: 18px;
  background-size: 100% 100% !important;
  margin: 0 5px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  // background-color: #e9e9e9;
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  // background-color: #e9e9e9;
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
::ng-deep .ant-modal-body {
  padding: 32px 8px 8px;
}
::ng-deep .ant-modal-close-x {
  width: 32px;
  height: 32px;
  line-height: 32px;
}
.scroll {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}