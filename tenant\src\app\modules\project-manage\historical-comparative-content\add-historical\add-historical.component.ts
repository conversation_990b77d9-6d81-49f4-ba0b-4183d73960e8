import { Component, OnInit } from "@angular/core";
import { DragulaService } from "ng2-dragula";
import { NzMessageService, UploadXHRArgs } from "ng-zorro-antd";
import { Subscription } from "rxjs";
import { HttpClient, HttpHeaders, HttpEvent } from "@angular/common/http";
import { DownloadUtilService } from "@src/modules/service/download-util.service";
import { ProjectManageService } from "../../../service/project-manage.service";
import { ActivatedRoute } from "@angular/router";
import { Router } from "@angular/router";
import { Location as Location1 } from "@angular/common"; //路由返回
import { MessageService } from "@src/shared/custom-message/message-service.service";
@Component({
  selector: "app-add-historical",
  templateUrl: "./add-historical.component.html",
  styleUrls: ["./add-historical.component.less"],
})
export class AddHistoricalComponent implements OnInit {
  subs = new Subscription();
  showResult: boolean = false; // 是否显示结果
  dataName: any = {
    zh_CN: "",
    en_US: "",
  }; // 数据名称
  oldDataName: any = null; // 对比数据名称
  matchWay: string = "ALL"; // 计算匹配度类型
  matchActivityId: string = null; // 匹配活动
  calculate: any = {
    matchRate: 0,
    matchNumber: 0,
    totalNumber: 0,
  }; // 匹配度
  tenantUrl: string = "/tenant-api";

  codeKeyWord: string = ""; // 基线code搜索
  quesKeyWord: string = ""; // 基线题本搜索
  oldKeyWord: string = ""; // 对比code搜索
  quesOldKeyWord: string = ""; // 对比题本搜索
  searchType: Array<string> = []; // 状态搜索
  dragKeyWord: string = ""; // 对比数据搜索

  projectId: string = ""; // 活动id
  hisId: string = ""; // 历史数据id
  hisData: any; // 历史数据详情
  projectSource: any; // 活动详情
  listOfOption: any;

  quesList: any[] = []; // 标准题匹配结果列表
  oldQuesList: any[] = []; // 旧题本列表
  matchMappingResultList: any; // 匹配结果
  quesType: number = 1;

  enableDemographicTextMatch: boolean = true; // 启用人口标签文本匹配
  enableDemographicCodeMatch: boolean = true; // 启用人口标签编码匹配
  enableOrganizationCodeMatch: boolean = true; // 启用组织编码匹配
  enableOrganizationTextMatch: boolean = true; // 启用组织文本匹配
  enableQuestionCodeMatch: boolean = true; // 启用题目编码匹配
  enableQuestionTextMatch: boolean = true; // 启用题目文本匹配
  enableDimensionCodeMatch: boolean = false; // 启用维度编码匹配
  enableDimensionTextMatch: boolean = false; // 启用维度文本匹配

  tableBodyHeight = 0;
  tableList = [
    {
      title: "题本",
      key: 1,
    },
    {
      title: "组织架构",
      key: 2,
    },
    {
      title: "人口标签",
      key: 3,
    },
    {
      title: "维度",
      key: 4,
    },
  ];
  pageIndex = 1;
  page: any = {
    // 分页条件
    current: 1,
    pages: 1,
    searchCount: true,
    size: 10,
    total: 1,
  };
  isSearch = false;
  isSaveLoading = false;
  constructor(
    private dragulaService: DragulaService,
    private msg: NzMessageService,
    private http: HttpClient,
    private projectManageSer: ProjectManageService,
    private activatedRoute: ActivatedRoute,
    private downUtil: DownloadUtilService,
    private router: Router,
    private location: Location1,
    private customMsg: MessageService
  ) {
    dragulaService.createGroup("PERSON", {
      copy: (el, source) => {
        return source.id === "right";
      },
      copyItem: (person) => {
        return person;
      },
      accepts: (el, target, source, sibling) => {
        if (target.id !== source.id) return target.id !== "right";
      },
    });
    // 题目不能重复
    this.subs.add(
      dragulaService
        .dropModel("PERSON")
        .subscribe(({ el, target, source, sourceModel, targetModel, item }) => {
          let flag = 0;

          if (this.quesType === 2) {
            targetModel.map((ele, index) => {
              if (ele.code === item.code) flag++;
              if (flag > 1) targetModel.splice(index, 1);
            });
          } else {
            targetModel.splice(0, targetModel.length);
            targetModel.push(item);
          }

          setTimeout(() => {
            this.calculateMatchRate();
          });
        })
    );
  }

  ngOnDestroy() {
    // destroy all the subscriptions at once
    this.dragulaService.destroy("PERSON");
    this.subs.unsubscribe();
  }

  matchWayChange(e) {
    // 切换计算匹配度类型
    this.matchWay = e;
    this.calculateMatchRate();
  }

  searchQuesText() {
    // 基线题本搜索
    this.quesList = [];
    if (this.quesType === 1) {
      this.quesList = this.search(this.matchMappingResultList.baseQuestions);
    } else if (this.quesType === 2) {
      this.quesList = this.search(
        this.matchMappingResultList.baseOrganizations
      );
    } else if (this.quesType === 3) {
      this.quesList = this.search(this.matchMappingResultList.baseDemographics);
    } else {
      this.quesList = this.search(this.matchMappingResultList.baseDimensions);
    }
  }

  search(arr: any[]) {
    let newArr = arr;
    if (this.codeKeyWord) {
      newArr = newArr.filter((item) => {
        return item.code.indexOf(this.codeKeyWord) >= 0;
      });
    } else {
      newArr = newArr;
    }
    if (this.quesKeyWord) {
      newArr = newArr.filter((item) => {
        return item.name.zh_CN.indexOf(this.quesKeyWord) >= 0;
      });
    } else {
      newArr = newArr;
    }
    if (this.oldKeyWord) {
      let arr = [];
      let obj: any;
      newArr = newArr.filter((item) => {
        obj = item.relativeList.filter((itm) => {
          return itm.code.indexOf(this.oldKeyWord) >= 0;
        });
        if (obj.length !== 0) arr.push(item);
      });
      newArr = arr;
    } else {
      newArr = newArr;
    }
    if (this.quesOldKeyWord) {
      let arr = [];
      let obj: any;
      newArr.filter((item) => {
        obj = item.relativeList.filter((itm) => {
          return itm.name.zh_CN.indexOf(this.quesOldKeyWord) >= 0;
        });
        if (obj.length !== 0) arr.push(item);
      });
      newArr = arr;
    } else {
      newArr = newArr;
    }
    if (this.searchType.length <= 0) {
      newArr = newArr;
    } else {
      let arr = [];
      let obj: any;
      newArr.filter((item) => {
        obj = item.relativeList.filter((itm) => {
          let flag1 = false;
          let flag2 = false;
          let flag3 = false;
          let flag4 = false;
          if (this.searchType.includes("code_T")) {
            flag1 = itm.isCodeMatch === true;
          }
          if (this.searchType.includes("code_F")) {
            flag2 = itm.isCodeMatch === false;
          }
          if (this.searchType.includes("text_T")) {
            flag3 = itm.isTextMatch === true;
          }
          if (this.searchType.includes("text_F")) {
            flag4 = itm.isTextMatch === false;
          }
          return flag1 || flag2 || flag3 || flag4;
        });
        if (obj.length !== 0) arr.push(item);
      });
      newArr = arr;
    }
    return newArr;
  }
  searchTypeChange(e) {
    // 状态搜索
    this.searchType = e;
    this.searchQuesText();
  }

  searchdragList() {
    // 搜索vs数据
    if (this.quesType === 1) {
      this.oldQuesList = this.searchOld(
        this.matchMappingResultList.relativeQuestions
      );
    } else if (this.quesType === 2) {
      this.oldQuesList = this.searchOld(
        this.matchMappingResultList.relativeOrganizations
      );
    } else {
      this.oldQuesList = this.searchOld(
        this.matchMappingResultList.relativeDemographics
      );
    }
  }

  searchOld(arr: any) {
    let newArr = arr;
    if (this.dragKeyWord) {
      newArr = newArr.filter((item) => {
        return item.name.zh_CN.indexOf(this.dragKeyWord) >= 0;
      });
    } else {
      newArr = newArr;
    }
    return newArr;
  }

  del(fatherInd, childInd) {
    console.log(1111, fatherInd, childInd);
    this.quesList[fatherInd].relativeList.splice(childInd, 1);
    this.calculateMatchRate();
  }

  refresh() {
    // 刷新
    this.getList();
  }

  clear() {
    this.showResult = false;
    this.quesType = 1;
    this.searchType = [];
    this.projectManageSer
      .clearMatchMappingResult(this.hisId)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.msg.success("数据清除成功");
          this.matchMappingResultList = null;
        }
      });
  }

  export() {
    // 导出数据表
    this.projectManageSer
      .exportMatchMappingResult(this.hisId)
      .subscribe((res) => {
        this.downUtil.downFile(res);
      });
  }

  calculateMatchRate() {
    const params = {
      baseItemList: this.quesList,
      id: this.hisId,
      matchWay: this.matchWay,
    };

    if (this.quesType === 1) {
      this.matchMappingResultList.baseQuestions.forEach((element) => {
        let obj = this.quesList.find((item) => {
          return item.id === element.id;
        });
        if (obj) element = obj;
      });
      params.baseItemList = JSON.parse(
        JSON.stringify(this.matchMappingResultList.baseQuestions)
      );
    } else if (this.quesType === 2) {
      this.matchMappingResultList.baseOrganizations.forEach((element) => {
        let obj = this.quesList.find((item) => {
          return item.id === element.id;
        });
        if (obj) element = obj;
      });
    } else if (this.quesType === 3) {
      this.matchMappingResultList.baseDemographics.forEach((element) => {
        let obj = this.quesList.find((item) => {
          return item.id === element.id;
        });
        if (obj) element = obj;
      });
    } else {
      this.matchMappingResultList.baseDimensions.forEach((element) => {
        let obj = this.quesList.find((item) => {
          return item.id === element.id;
        });
        if (obj) element = obj;
      });
    }

    this.projectManageSer.calculateMatchRate(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.calculate = res.data;
        if (this.quesType === 1) {
          this.matchMappingResultList.baseQuestions.forEach((element) => {
            let obj = res.data.baseItemList.find((item) => {
              return item.id === element.id;
            });

            if (obj) return (element.relativeList = obj.relativeList);
          });
          // console.log(this.matchMappingResultList.baseQuestions);
          // console.log(res.data.baseItemList);
          this.quesList = JSON.parse(
            JSON.stringify(this.matchMappingResultList.baseQuestions)
          );
        } else if (this.quesType === 2) {
          this.matchMappingResultList.baseOrganizations.forEach((element) => {
            let obj = res.data.baseItemList.find((item) => {
              return item.id === element.id;
            });
            if (obj) element.relativeList = obj.relativeList;
          });
          this.quesList = JSON.parse(
            JSON.stringify(this.matchMappingResultList.baseOrganizations)
          );
        } else if (this.quesType === 3) {
          this.matchMappingResultList.baseDemographics.forEach((element) => {
            let obj = res.data.baseItemList.find((item) => {
              return item.id === element.id;
            });
            if (obj) element.relativeList = obj.relativeList;
          });
          this.quesList = JSON.parse(
            JSON.stringify(this.matchMappingResultList.baseDemographics)
          );
        } else {
          this.matchMappingResultList.baseDimensions.forEach((element) => {
            let obj = res.data.baseItemList.find((item) => {
              return item.id === element.id;
            });
            if (obj) element.relativeList = obj.relativeList;
          });
          this.quesList = JSON.parse(
            JSON.stringify(this.matchMappingResultList.baseDimensions)
          );
        }
      }
      const tableBodyHeight = document.getElementById("tableBody");
      this.tableBodyHeight = tableBodyHeight.offsetHeight;
      this.searchQuesText();
    });
  }

  changeResData(type?) {
    if (type == this.quesType) {
      return;
    }
    // 切换题本分类数据  type 0 题本 1 组织架构 2 人口标签 3 维度
    this.quesOldKeyWord = "";
    this.oldKeyWord = "";
    this.quesKeyWord = "";
    this.codeKeyWord = "";
    this.searchType = [];
    this.pageIndex = 1;
    type ? (this.quesType = type) : "";
    if (this.quesType === 1) {
      this.quesList = this.matchMappingResultList.baseQuestions;
      this.oldQuesList = this.matchMappingResultList.relativeQuestions;
    } else if (this.quesType === 2) {
      this.quesList = this.matchMappingResultList.baseOrganizations;
      this.oldQuesList = this.matchMappingResultList.relativeOrganizations;
    } else if (this.quesType === 3) {
      this.quesList = this.matchMappingResultList.baseDemographics;
      this.oldQuesList = this.matchMappingResultList.relativeDemographics;
    } else {
      this.quesList = this.matchMappingResultList.baseDimensions;
      this.oldQuesList = this.matchMappingResultList.relativeDimensions;
    }
    this.calculateMatchRate();
  }

  ngOnInit() {
    this.projectId = this.activatedRoute.snapshot.queryParams["projectId"];
    this.hisId = this.activatedRoute.snapshot.queryParams["hisId"];

    if (this.hisId) {
      this.projectManageSer.getDetailById(this.hisId).subscribe((res) => {
        console.log(res);
        this.matchActivityId = res.data.relativeProjectId;
        // this.oldDataName = res.data.relativeProjectName.zh_CN
        // this.dataName = res.data.name.zh_CN
        console.log(11111, 333, res.data);
        this.oldDataName = res.data.relativeProjectName;
        this.dataName = res.data.name;
        this.generateResult(false);
      });
    }
    this.loadData();
  }

  loadData() {
    this.getActive();
    this.getDetail();
  }

  getDetail() {
    // 创建历史对比
    this.projectManageSer.getDetail(this.projectId).subscribe((res) => {
      this.projectSource = res.data;
    });
  }

  getActive() {
    // 获取下拉活动名称
    const apiOption = `${this.tenantUrl}/survey/project/listClosedProjectName?surveyType=EMPLOYEE_ENGAGEMENT`;
    this.http.get(apiOption).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.listOfOption = res.data;
        this.matchActivityChange();
      }
    });
  }

  matchActivityChange(e?) {
    // 切换匹配活动获取名称
    this.listOfOption.map((res) => {
      if (res.id === this.matchActivityId) {
        this.oldDataName = res.projectName;
      }
    });
  }

  // 生成匹配结果
  generateResult(isDefault = true) {
    if (!this.matchActivityId)
      // return this.msg.error("请选择匹配活动");
      return this.customMsg.open("error", "请选择匹配活动");
    // if(!this.dataName.zh_CN) return this.msg.error('请输入数据名称-中文')
    // if(!this.dataName.en_US) return this.msg.error('请输入数据名称-英文')
    this.showResult = true;
    const params = {
      projectId: this.projectId,
      relativeProjectId: this.matchActivityId,
      // name: {
      //   zh_CN: this.dataName,
      //   en_US: ''
      // },
      name: this.dataName,
      memo: "",
    };
    if (this.hisId) {
      this.getList(false, isDefault);
    } else {
      this.projectManageSer.createPrismaHistoryData(params).subscribe((res) => {
        if (res.result.code === 0) {
          this.hisId = res.data.id;
          this.getList();
        }
      });
    }
  }

  getEnableQuestionCodeMatch(e) {
    //
    this.enableQuestionCodeMatch = e;
    this.changeResData(1);
    this.getList();
  }
  getEnableQuestionTextMatch(e) {
    //
    this.enableQuestionTextMatch = e;
    this.changeResData(1);
    this.getList();
  }
  getEnableOrganizationCodeMatch(e) {
    //
    this.enableOrganizationCodeMatch = e;
    this.changeResData(2);
    this.getList();
  }
  getEnableOrganizationTextMatch(e) {
    //
    this.enableOrganizationTextMatch = e;
    this.changeResData(2);
    this.getList();
  }

  getList(isSearch = false, isDefault = true) {
    if (isSearch) {
      this.isSearch = isSearch;
      this.page = {
        // 分页条件
        current: 1,
        pages: 1,
        searchCount: true,
        size: 10,
        total: 1,
      };
    } else {
      this.isSearch = false;
    }
    // 获取匹配结果
    const resParams = {
      page: this.page,
      id: this.hisId,
      isDefault,
      enableDemographicTextMatch: this.enableDemographicTextMatch, // 启用人口标签文本匹配
      enableDemographicCodeMatch: this.enableDemographicCodeMatch, // 启用人口标签文本匹配
      enableOrganizationCodeMatch: this.enableOrganizationCodeMatch, // 启用组织编码匹配
      enableOrganizationTextMatch: this.enableOrganizationTextMatch, // 启用组织文本匹配
      enableQuestionCodeMatch: this.enableQuestionCodeMatch, // 启用题目编码匹配
      enableQuestionTextMatch: this.enableQuestionTextMatch, // 启用题目文本匹配
      enableDimensionCodeMatch: this.enableDimensionCodeMatch, // 启用题目编码匹配
      enableDimensionTextMatch: this.enableDimensionTextMatch, // 启用题目文本匹配
    };
    this.projectManageSer.getMatchMappingResult(resParams).subscribe((res) => {
      if (res.result.code === 0) {
        this.matchMappingResultList = res.data;
        this.changeResData();
        this.calculateMatchRate();
      }
    });
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.projectManageSer
      .importMatchMappingResult(formData, this.hisId)
      .subscribe(
        (event: HttpEvent<any>) => {
          item.onSuccess!();
          let res: any = event;
          if (res.result.code === 0) {
            this.msg.success("数据已加载，请检查后保存");
            this.matchMappingResultList = res.data;
            this.changeResData();
            this.calculateMatchRate();
          }
        },
        (err) => {
          item.onError!(err, item.file!);
        }
      );
  }

  // 保存并更新
  save() {
    this.isSaveLoading = true;
    if (!this.matchMappingResultList || !this.hisId) {
      this.isSaveLoading = false;
      // return this.msg.error("匹配结果未生成");
      return this.customMsg.open("error", "匹配结果未生成");
    }

    const params1 = {
      projectId: this.projectId,
      relativeProjectId: this.matchActivityId,
      // name: {
      //   zh_CN: this.dataName,
      //   en_US: ''
      // },
      name: this.dataName,
      memo: "",
      id: this.hisId,
    };
    const params = {
      baseDemographics: this.matchMappingResultList.baseDemographics,
      baseOrganizations: this.matchMappingResultList.baseOrganizations,
      baseQuestions: this.matchMappingResultList.baseQuestions,
      baseDimensions: this.matchMappingResultList.baseDimensions,
      id: this.hisId,
    };
    this.projectManageSer.updatePrismaHistoryData(params1).subscribe((res) => {
      if (res.result.code === 0) {
        this.projectManageSer
          .saveMatchMappingResult(params)
          .subscribe((res) => {
            if (res.result.code === 0) {
              this.msg.success("保存成功");
              this.isSaveLoading = false;
              this.location.back();
            } else {
              this.isSaveLoading = false;
            }
          });
      } else {
        this.isSaveLoading = false;
      }
    });
  }

  changeNameValue(e) {
    this.dataName = e;
  }
  // 分页相关
  pageIndexChange(e) {
    // 页码改变
    if (e === 0 || e > this.page.pages) return;
    console.log(e);
    this.page.current = e;
    this.getList();
  }

  pageSizeChange(e) {
    // 每页条数改变
    this.page.size = e;
    this.getList();
  }
}
