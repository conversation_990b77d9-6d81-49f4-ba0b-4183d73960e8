/**
 *
 *  @author: <PERSON>
 *  @Date: 2023/09/18
 *  @content: i18n input组件
 *
 */
import {
  Component,
  Input,
  Output,
  SimpleChanges,
  EventEmitter,
  OnInit,
} from "@angular/core";
import { NewPrismaService } from "@src/modules/new-prisma/new-prisma.service";
import _ from "lodash";
interface I18nInput {
  zh_CN: string;
  en_US: string;
  [key: string]: string;
}
interface I18nOptions {
  label: string;
  key: string;
  value: string;
}

@Component({
  selector: "app-i18n-input",
  templateUrl: "./i18n-input.component.html",
  styleUrls: ["./i18n-input.component.less"],
})
export class I18nInputComponent implements OnInit {
  @Input() value: I18nInput;
  @Input() isDefault?: boolean = false; // 使用默认中英文
  @Input() placeholder?: string = "请输入";
  @Input() i18nData?: any[] = [];
  @Output() changeValue = new EventEmitter<any>();

  i18n: any[] = [];
  visible: boolean;

  constructor(private api: NewPrismaService) {}

  ngOnInit() {
    if (!this.isDefault) {
      this.i18n = [
        {
          name: "中文",
          key: "zh_CN",
          value: this.value.zh_CN || "",
        },
      ];
      if (this.i18nData && this.i18nData.length) {
        // this.i18n = this.i18nData.map((val) => ({
        //   ...val,
        //   value: this.value[val.key],
        // }));
        this.i18n = this.i18nData
        
      } else {
        this.getLanOptions();
      }
    } else {
      this.i18n = [
        {
          label: "中文",
          key: "zh_CN",
          value: this.value.zh_CN || "",
        },
        {
          label: "ENG",
          key: "en_US",
          value: this.value.en_US || "",
        },
      ];
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // 默认中英文时的处理
    if (changes.value && this.isDefault) {
      const newValue = changes.value.currentValue;
      this.i18n = [
        {
          label: "中文",
          key: "zh_CN",
          value: this.value.zh_CN || "",
        },
        {
          label: "ENG",
          key: "en_US",
          value: this.value.en_US || "",
        },
      ];
      this.i18n.forEach((val) => {
        Object.keys(newValue).forEach((key) => {
          if (val.key === key) {
            val.value = newValue[key];
          }
        });
      });
    }
  }

  /**
   * getLanOptions 获取语言的配置信息
   */
  async getLanOptions() {
    const currentLansRes = await this.api.getLanguages().toPromise();
    const defaultCode = ["zh_CN", "en_US"];
    const sessionProjectLanguages =
      JSON.parse(sessionStorage.getItem("projectLanguages")) || [];
    const projectLanguages = sessionProjectLanguages.length
      ? sessionProjectLanguages
      : defaultCode;
      this.i18n = currentLansRes.data
      .filter((val) => projectLanguages.includes(val.value))
      .map((val) => ({
        label: val.name,
        key: val.value,
        value: this.value[val.value],
      }))
      .sort((a, b) => {
        if (a.key === "zh_CN") return -1;
        if (b.key === "zh_CN") return 1;
        if (a.key === "en_US") return -1;
        if (b.key === "en_US") return 1;
        if (a.key === "jp") return -1;
        if (b.key === "jp") return 1;
        if (a.key === "ko") return -1;
        if (b.key === "ko") return 1;
        if (a.key === "cs_1") return -1;
        if (b.key === "cs_1") return 1;
        if (a.key === "cs_2") return -1;
        if (b.key === "cs_2") return 1;
        if (a.key === "cs_3") return -1;
        if (b.key === "cs_3") return 1;
        return 0;
      });
  }

  /**
   * changeVal 语言内容保存
   */
  changeVal() {
    let i18nObj = {};
    this.i18n.forEach((val) => {
      i18nObj[val.key] = val.value;
    });
    this.changeValue.emit(i18nObj);
  }

  /**
   * handOk 弹窗确定-保存内容
   */
  handOk() {
    this.changeVal();
    this.visible = false;
  }
  /**
   * handI18n 点击语言图标，加载语言设置
   */
  handI18n() {
    if (!this.isDefault) {
      this.getLanOptions();
    }
  }
}
