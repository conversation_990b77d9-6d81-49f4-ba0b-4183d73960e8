<div class="container">
  <div class="left scroll">
    <div
      class="nair"
      [ngClass]="{ currentNair: item.isCurrent }"
      *ngFor="let item of createList"
      (click)="clickNair(item)"
    >
      <div class="line1">
        <span class="text0">{{ item.nairName.zh_CN }}</span>
        <label nz-checkbox [(ngModel)]="item.isSelect"></label>
      </div>
      <div class="line2">
        <span class="text1 autoTxt" *ngIf="!!item.groupStyleType"
          >机出报告</span
        >
        <span class="text1 handTxt" *ngIf="!item.groupStyleType">手出报告</span>
        <span class="text1 singlText" *ngIf="!item.talentMappingNairList"
          >单工具</span
        >
        <span class="text1 multiTxt" *ngIf="!!item.talentMappingNairList"
          >多工具</span
        >
        <span class="text2"
          >{{
            !item.talentMappingNairList
              ? item.detailList.length
              : mappingReportPersonCount
          }}人</span
        >
      </div>
      <div class="line3">
        <span class="text3">售价：{{ item.cost }}K米/份</span>
      </div>
    </div>
  </div>

  <div class="right">
    <div class="title">
      <!-- 单工具 -->
      <ng-container *ngIf="!currentItem.talentMappingNairList; else singular">
        <div class="input">
          <nz-input-group [nzSuffix]="suffixIconSearch" class="search">
            <input
              type="text"
              nz-input
              placeholder="请输入姓名查找"
              [(ngModel)]="searchText"
              (keyup.enter)="searchData()"
            />
          </nz-input-group>
          <ng-template #suffixIconSearch>
            <span nz-icon nzType="search" (click)="searchData()"></span>
          </ng-template>
        </div>
      </ng-container>
      <ng-template #singular>
        <div class="addSudoku" (click)="addSudoku()">
          <span nz-icon nzType="plus-circle" nzTheme="fill"></span>新建九宫格
        </div>
      </ng-template>
      <span>团队报告人数建议≥5人</span>
    </div>
    <div class="file-name">
      <nz-input-group nzCompact>
        <nz-select [(ngModel)]="langage" style="width: 15%;">
          <nz-option [nzLabel]="'中文'" [nzValue]="'zh_CN'"></nz-option>
          <nz-option [nzLabel]="'英文'" [nzValue]="'en_US'"></nz-option>
        </nz-select>
        <input
          style="width: 85%;"
          type="text"
          nz-input
          [(ngModel)]="fileName[langage]"
          placeholder="请输入报告名称"
          (blur)="changefileName()"
        />
      </nz-input-group>
    </div>

    <ng-container *ngIf="!currentItem.talentMappingNairList; else singularList">
      <!-- 单工具 -->
      <div class="table scroll">
        <nz-table
          [nzSize]="'small'"
          #tmpTable
          [nzData]="getDataList()"
          [nzNoResult]="'暂无数据'"
          [nzFrontPagination]="false"
        >
          <thead class="thead">
            <tr>
              <th class="th">姓名</th>
              <th class="th">活动名称</th>
              <th class="th">填答日期</th>
              <th class="th">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of tmpTable.data">
              <td width="20%" class="td col1">{{ data.name }}</td>
              <td width="40%" class="td col2">{{ data.projectName }}</td>
              <td width="30%" class="td">
                {{ data.time | date: "yyyy/MM/dd HH:mm" }}
              </td>
              <td width="10%">
                <div class="btnIcon" (click)="delPerson(data)"></div>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </ng-container>
    <ng-template #singularList>
      <!-- 多工具 -->
      <ng-container *ngIf="sudokuData.length; else noSudokuData">
        <div class="overflowBox">
          <div class="multitool" *ngFor="let item of sudokuData">
            <input
              [ngClass]="{
                warnInput:
                  item.id && isResetObjectSettingItemIds.includes(item.id)
              }"
              nz-input
              placeholder="请输入"
              [(ngModel)]="item.name.zh_CN"
              (keyup.enter)="changeSudokuName(item)"
              (blur)="changeSudokuName(item)"
            />
            <div class="multitool_btm">
              <ng-container
                *ngIf="
                  item.id && isResetObjectSettingItemIds.includes(item.id);
                  else warnText
                "
                ><span class="warnText"
                  >*呈现设置调整，请重新完善对象设置</span
                ></ng-container
              >
              <ng-template #warnText><span></span></ng-template>
              <div class="multitool_btm_btns">
                <ng-template #renderSetting>
                  <render-setting
                    [reportId]="item.reportId"
                    [itemId]="item.id"
                    [talentMappingNairList]="currentItem.talentMappingNairList"
                    (closePopover)="closeRnderPopover($event, item.id)"
                  ></render-setting>
                </ng-template>
                <button
                  nz-button
                  nzType="link"
                  nz-popover
                  [nzPopoverContent]="renderSetting"
                  nzPopoverTrigger="click"
                  nzPopoverPlacement="leftTop"
                  nzSize="small"
                  [disabled]="!item.id"
                  nzOverlayClassName="popoverCard"
                >
                  <nz-badge
                    nzDot
                    [nzShowDot]="item.id && !item.isDisplayComplete"
                  >
                    呈现设置
                  </nz-badge>
                </button>
                <ng-template #objectSetting>
                  <object-setting
                    [itemId]="item.id"
                    (closePopover)="closeObjectPopover(item.id)"
                  ></object-setting>
                </ng-template>
                <button
                  nz-button
                  nzType="link"
                  nz-popover
                  [nzPopoverContent]="objectSetting"
                  nzPopoverTrigger="click"
                  nzPopoverPlacement="leftTop"
                  nzSize="small"
                  [disabled]="!item.id || !item.isDisplayComplete"
                  nzOverlayClassName="popoverCard"
                >
                  <nz-badge
                    nzDot
                    [nzShowDot]="item.id && !item.isObjectComplete"
                  >
                    对象设置
                  </nz-badge>
                </button>
                <div
                  class="del "
                  style="margin-left: 20px; "
                  (click)="delSudoku(item.id)"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-template #noSudokuData>
        <div class="empty-box">
          <app-empty text="暂无数据"></app-empty>
        </div>
      </ng-template>
    </ng-template>
  </div>
</div>
