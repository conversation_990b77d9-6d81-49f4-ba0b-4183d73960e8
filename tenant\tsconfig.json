{"compileOnSave": false, "compilerOptions": {"downlevelIteration": true, "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "esnext", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "baseUrl": "./", "paths": {"@src": ["src/app/"], "@src/*": ["src/app/*"], "@shared": ["src/app/shared/index"], "@shared/*": ["src/app/shared/*"], "@core": ["src/app/core/index"], "@core/*": ["src/app/core/*"], "@env/*": ["src/environments/*"]}}}