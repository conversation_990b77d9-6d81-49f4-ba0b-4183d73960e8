:host {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #f1f1f1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #c1c1c1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

::ng-deep {
  .popover-img-box {
    width: 318px;
    height: 256px;
    display: flex;
    justify-content: center;
    align-items: center;
    > img {
      // width: 100%;
      height: 100%;
    }
  }
  .ant-dropdown {
    .solid-color-bg {
      padding: 10px;
      background-color: #ffffff;
      box-shadow: 0px 4px 10px 1px rgba(0, 0, 0, 0.10000000149011612);
      border-radius: 6px;
      .bg-color-box {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 36px;
        height: 24px;
        box-sizing: border-box;
        border-radius: 2px 2px 2px 2px;
        margin-right: 4px;
        cursor: pointer;
        > div {
          width: 28px;
          height: 16px;
          border-radius: 2px 2px 2px 2px;
        }
      }
      > li {
        display: flex;
        margin-bottom: 4px;
        .bg-color-box-active {
          border: 1px solid #aaaaaa;
        }
      }
      > li:last-child {
        margin: 0px;
      }
    }
  }
}

.icon-btn-minus {
  width: 16px;
  height: 16px;
  background: url(../../../../assets/images/prisma/icon-minus.png) no-repeat;
  cursor: pointer;
  margin-right: 12px;
}
.icon-btn-minus:hover {
  background: url(../../../../assets/images/prisma/icon-minus-blue.png)
    no-repeat;
}
.icon-btn-add {
  width: 16px;
  height: 16px;
  background: url(../../../../assets/images/prisma/icon-add.png) no-repeat;
  cursor: pointer;
}
.icon-btn-add:hover {
  background: url(../../../../assets/images/add-condition.png) no-repeat;
}

.lottery-box {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  background-color: #f5f6fa;
  height: 100%;
  .container-box {
    > .title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;
      .span_left {
        font-size: 24px;
        font-weight: normal;
        color: #17314c;
        line-height: 34px;
      }
      .span_right {
        font-size: 12px;
        font-weight: normal;
        color: #495970;
        line-height: 17px;
        span {
          cursor: pointer;
        }
        > .span_blue {
          color: #53a8fe;
        }
      }
    } // title end

    .content-box {
      display: flex;
      justify-content: space-between;

      .content_left {
        flex: 1;
        /**电脑pc 移动切换tab**/
        .tab {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 30px;
          > div {
            display: flex;
            justify-content: space-between;
            width: 214px;
            height: 48px;
            background: #f1f2f4;
            border-radius: 24px 24px 24px 24px;
            button {
              width: 102px;
              height: 40px;
              background: #f1f2f4;
              border-radius: 20px 20px 20px 20px;
              font-size: 16px;
              font-weight: normal;
              color: #aaaaaa;
              line-height: 22px;
              margin: 4px;
              cursor: pointer;
            }
            .active {
              color: #17314c;
              background: #ffffff;
            }
          }
        }

        .tab-preview {
          display: flex;
          justify-content: center;
          .pc {
            position: relative;
            width: 562px;
            height: 374px;
            background: url(../../../../assets/images/prisma/lottery-pc-bg.png)
              no-repeat;
            margin-bottom: 85px;
            > div {
              position: absolute;
              left: 53px;
              top: 30px;
              width: 456px;
              height: 287px;
              // background: url(../../../../assets/images/prisma/bg-pc.png) no-repeat;
              background-position: center;
              background-size: 648px;
              > div.title-img {
                width: 133px;
                height: 50px;
                margin: 25px 0 0 98px;
                background: url(../../../../assets/images/prisma/image-title-PC.png)
                  no-repeat;
                background-position: center;
                background-size: cover;
              }
              ul {
                width: 110px;
                height: 110px;
                margin: 10px 0 8px 109px;
                li {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  > div {
                    // 抽奖块
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 40px;
                    height: 40px;
                    border-radius: 5px 5px 5px 5px;
                    border: 1px solid transparent;
                    box-sizing: border-box;
                    > div {
                      // 抽奖块内块
                      width: 36px;
                      height: 36px;
                      border-radius: 4px 4px 4px 4px;
                      background: #ffffff;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      img {
                        width: 18px;
                        height: 18px;
                        margin-bottom: 2px;
                      }
                      span {
                        display: inline-block;
                        text-align: center;
                        width: 60px;
                        font-size: 12px;
                        font-weight: normal;
                        color: #707070;
                        line-height: 9px;
                        transform: scale(0.5);
                        overflow: hidden;
                        text-overflow: ellipsis;
                        word-break: break-all;
                      }
                    }
                    > div.lottery-btn-mobile {
                      // 抽奖按钮
                      background: url(../../../../assets/images/prisma/lottery-btn-mobile.png);
                      background-position: center;
                      background-size: cover;
                    }
                    > div.active {
                      background: #9877e1;
                      span {
                        color: #f4f0f7;
                      }
                    }
                  }
                  div.active {
                    border: 1px solid #bca7eb;
                  }
                }
              }
            }
          }
          .phone {
            position: relative;
            width: 226px;
            height: 465px;
            background: url(../../../../assets/images/prisma/lottery-ph-bg.png)
              no-repeat;
            margin-right: 40px;
            margin-bottom: 24px;
            > div {
              width: 202px;
              height: 398px;
              overflow-y: auto;
              margin-top: 55px;
              margin-left: 12px;
              // background: url(../../../../assets/images/prisma/bg-mobile.png) no-repeat;
              background-position: center;
              background-size: cover;
              border-radius: 0px 0px 20px 20px;
              > ul {
                // 抽奖跑马灯box
                padding: 73px 28px 0;
                li {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-bottom: 2px;
                  > div {
                    // 抽奖块
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 47px;
                    height: 47px;
                    border-radius: 6px 6px 6px 6px;
                    border: 1px solid transparent;
                    box-sizing: border-box;
                    margin-right: 2px;
                    > div {
                      // 抽奖块内块
                      width: 43px;
                      height: 43px;
                      border-radius: 5px 5px 5px 5px;
                      background: #ffffff;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      img {
                        width: 22px;
                        height: 22px;
                        margin-bottom: 2px;
                      }
                      span {
                        display: inline-block;
                        text-align: center;
                        width: 80px;
                        font-size: 12px;
                        font-weight: normal;
                        color: #707070;
                        line-height: 9px;
                        transform: scale(0.5);
                        overflow: hidden;
                        text-overflow: ellipsis;
                        word-break: break-all;
                      }
                    }
                    > div.lottery-btn-mobile {
                      // 抽奖按钮
                      background: url(../../../../assets/images/prisma/lottery-btn-mobile.png);
                    }
                    > div.active {
                      background: #9877e1;
                      span {
                        color: #f4f0f7;
                      }
                    }
                  }
                  div.active {
                    border: 1px solid #bca7eb;
                  }
                }
              }
              .time {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 130px;
                height: 22px;
                margin: 0 auto;
                font-size: 12px;
                font-weight: normal;
                color: #f4f0f7;
                line-height: 17px;
                .time-block {
                  width: 28px;
                  height: 24px;
                  background: #ffffff;
                  border-radius: 4px 4px 4px 4px;
                  margin: 0 8px;
                }
              }
            }
          }
        }
        .features {
          // 左侧功能选项
          display: flex;
          justify-content: center;
          align-items: center;
          li {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 30px;
            cursor: pointer;
            box-sizing: border-box;
            border-bottom: 2px solid transparent;
            .icon-icon_refresh {
              margin-right: 6px;
            }
          }
          li:hover {
            color: #53a8ff;
          }
          li.active {
            color: #53a8ff;
            border-bottom: 2px solid #409eff;
          }
        }
      }
      .content_right {
        flex: 1;
        .card-container ::ng-deep .ant-tabs-card .ant-tabs-content {
          margin-top: -16px;
        }

        .card-container
          ::ng-deep
          .ant-tabs-card
          .ant-tabs-content
          .ant-tabs-tabpane {
          background: #fff;
        }

        .card-container ::ng-deep .ant-tabs-card .ant-tabs-bar {
          border-color: #fff;
        }

        .card-container ::ng-deep .ant-tabs-card .ant-tabs-bar .ant-tabs-tab {
          border-color: transparent;
          background: transparent;
          font-size: 16px;
          font-weight: normal;
          color: #aaaaaa;
        }

        .card-container
          ::ng-deep
          .ant-tabs-card
          .ant-tabs-bar
          .ant-tabs-tab-active {
          border-color: #fff;
          background: #fff;
          color: #17314c;
        }

        .basic-settings {
          padding: 24px 0;
          .basic {
            height: calc(100vh - 350px);
            overflow-y: auto;
            padding: 0 24px;
            // box-shadow: 0px 12px 20px -12px #5aff6e inset, 0px -12px 20px -12px #fff inset;
            .set-box {
              margin-bottom: 40px;
              .prize-table {
                // 奖品列表
                width: 536px;
                background: #ffffff;
                border-radius: 4px 4px 4px 4px;
                // border: 1px solid #E6E6E6;
                .img-little {
                  width: 50px;
                  height: 42px;
                  border-radius: 2px 2px 2px 2px;
                  cursor: url(../../../../assets/images/prisma/enlarge.png);
                  > img {
                    // width: 100%;
                    height: 100%;
                  }
                }
              }
              .tips {
                display: flex;
                width: 536px;
                background: rgba(251, 184, 83, 0.07000000029802322);
                border-radius: 2px 2px 2px 2px;
                border: 1px solid #ffddad;
                padding: 6px 14px 6px 12px;
                margin-top: 16px;
                img {
                  margin-right: 10px;
                  width: 16px;
                  height: 16px;
                }
                p {
                  font-size: 14px;
                  font-weight: normal;
                  color: #fbb853;
                  line-height: 22px;
                }
              }
              .set-name {
                display: flex;
                align-items: center;
                justify-content: space-between;
                // margin-bottom: 20px;
                a {
                  display: flex;
                  align-items: center;
                }
                .name {
                  font-size: 14px;
                  font-weight: normal;
                  color: #17314c;
                  line-height: 20px;
                }
                .set-name-right {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  .tip-name {
                    font-size: 14px;
                    font-weight: normal;
                    color: #aaaaaa;
                    line-height: 20px;
                    margin-right: 10px;
                  }
                  .tip-btn {
                    width: 84px;
                    height: 30px;
                    background: inherit;
                    border-radius: 15px 15px 15px 15px;
                    border: 1px solid #409eff;
                    font-size: 14px;
                    font-weight: normal;
                    color: #409eff;
                    line-height: 20px;
                  }
                }
              }

              // 主题背景
              .img-box {
                // 自定义
                display: flex;
                justify-content: space-between;
                padding-top: 32px;
                // 上传图片
                .img-group {
                  margin: 0 38px;
                  .top {
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;
                    > p {
                      white-space: nowrap;
                      font-size: 14px;
                      font-weight: normal;
                      color: #459efb;
                      line-height: 20px;
                      margin-right: 24px;
                    }
                    .upload-box {
                      // 上传
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      width: 80px;
                      height: 80px;
                      border-radius: 4px 4px 4px 4px;
                      border: 1px solid #409eff;
                      > .upload {
                        width: 80px;
                        height: 80px;
                      }
                      > p {
                        padding-top: 4px;
                        font-size: 14px;
                        font-weight: normal;
                        color: #409eff;
                        line-height: 20px;
                      }
                    }
                  }
                  > p {
                    font-size: 14px;
                    font-weight: normal;
                    color: #ff7575;
                    line-height: 22px;
                  }
                }
              }
              .bg-color-box {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 36px;
                height: 24px;
                box-sizing: border-box;
                border-radius: 2px 2px 2px 2px;
                margin-right: 4px;
                cursor: pointer;
                > div {
                  width: 28px;
                  height: 16px;
                  border-radius: 2px 2px 2px 2px;
                }
              }
              .solid-color-bg {
                padding-left: 30px;
                padding-top: 22px;
                > li {
                  display: flex;
                  margin-bottom: 4px;
                  .bg-color-box-active {
                    border: 1px solid #aaaaaa;
                  }
                }
                > li:last-child {
                  margin: 0px;
                }
              }
              .suffix {
                font-size: 14px;
                font-weight: normal;
                color: #aaaaaa;
                line-height: 20px;
              }

              .prize-settings {
                // 奖品设置
                display: flex;
                align-items: center;
                span {
                  font-size: 14px;
                  font-family: PingFang, PingFang SC;
                  font-weight: normal;
                  color: #aaaaaa;
                  margin-right: 12px;
                }
                .unit {
                  margin-left: 12px;
                }
              }
            }
          }
        }
      }
    }
  }

  .submit_xy {
    // 底部按钮
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 70px;
    background: #fff;
    box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);
    display: flex;
    justify-content: center;
    .center_menu {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .menus_xy {
        display: flex;
        align-items: center;
        .menus_left {
          width: 128px;
          height: 38px;
          border-radius: 19px;
          border: 1px solid #409eff;
          text-align: center;
          line-height: 38px;
          cursor: pointer;
          color: #409eff;
        }
        .menus_right {
          width: 128px;
          height: 38px;
          text-align: center;
          line-height: 38px;
          cursor: pointer;
          background: #fafafa;
          border-radius: 19px;
          color: #fff;
          margin-left: 30px;
        }
        .menus_right_new {
          width: 128px;
          height: 38px;
          border-radius: 19px;
          text-align: center;
          line-height: 38px;
          cursor: pointer;
          background: linear-gradient(270deg, #26d0f1 0%, #409eff 100%);
          box-shadow: 0px 3px 8px 1px rgba(55, 175, 250, 0.5);
          color: #fff;
          margin-left: 30px;
        }
      }
    }
  }
}

::ng-deep {
  .prize-modal {
    .ant-drawer-body {
      padding: 0;
      .icon-penetra-close {
        font-size: 10px;
        margin-top: 8px;
        margin-right: 13px;
        margin-bottom: 9px;
        cursor: pointer;
      }
      header {
        // width: 960px;
        .prize-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 24px;
          font-weight: normal;
          color: #17314c;
          line-height: 33px;
          padding-bottom: 16px;
          margin: 0 30px;
          border-bottom: 1px solid #e6e6e6;
          .prize-title-name {
            display: flex;
            align-items: center;
          }
          .prize-title-icon {
            width: 30px;
            height: 30px;
            cursor: pointer;
          }
          .little-title {
            font-size: 18px;
            font-weight: normal;
            color: #17314c;
            line-height: 25px;
            margin-left: 9px;
          }
          .right-btn {
            width: 129px;
            height: 38px;
            background: url(../../../../assets/images/addfactor.png) no-repeat;
            line-height: 38px;
            padding-left: 36px;
            font-size: 16px;
            color: #419eff;
            cursor: pointer;
          }
        }
      }
      .prize-body {
        // 弹窗内容
        box-sizing: border-box;
        margin: 0 30px;
        padding: 28px 0;
        border-bottom: 1px solid #e6e6e6;
        ul {
          padding: 39px 0;
          li {
            display: flex;
            // justify-content: center;
            align-items: center;
            margin-bottom: 32px;
            .delt-icon {
              width: 30px;
              height: 30px;
              background: url("../../../../assets/images/delete-ico.png");
              margin-left: 24px;
              background-position: center;
              background-size: cover;
              cursor: pointer;
            }
            .delt-icon-empty {
              width: 30px;
              height: 30px;
              margin-left: 24px;
            }
            .delt-icon:hover {
              background: url("../../../../assets/images/delete-ico-hover.png");
              background-position: center;
              background-size: cover;
            }
          }
        }
        .prize-box {
          display: flex;
          justify-content: space-between;
          margin-bottom: 32px;
          > div {
            width: 420px;
          }
          .prize-name {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 30px;
            margin-bottom: 15px;
            .prize-updata-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 84px;
              height: 30px;
              border-radius: 15px 15px 15px 15px;
              border: 1px solid #409eff;
              font-size: 14px;
              font-weight: normal;
              color: #409eff;
              line-height: 20px;
              cursor: pointer;
            }
            p {
              font-size: 14px;
              font-weight: normal;
              color: #17314c;
              line-height: 20px;
            }
          }
          .prize-settings {
            // 奖品设置
            display: flex;
            align-items: center;

            span {
              font-size: 14px;
              font-family: PingFang, PingFang SC;
              font-weight: normal;
              color: #aaaaaa;
              margin-right: 12px;
            }
            .unit {
              margin-left: 12px;
            }
          }

          .prize-icon {
            // 奖品设置 图标
            ul {
              li {
                display: flex;
                // align-items: center;
                > div:last-child {
                  margin-right: 0;
                }
                .upload-box {
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  width: 65px;
                  height: 65px;
                  box-sizing: border-box;
                  border-radius: 4px 4px 4px 4px;
                  border: 1px solid transparent;
                  cursor: pointer;
                  > img.upload {
                    width: 48px;
                    height: 48px;
                  }
                  > p {
                    padding-top: 4px;
                    font-size: 12px;
                    font-weight: normal;
                    color: #409eff;
                    line-height: 17px;
                  }
                }
                .icon-box {
                  width: 65px;
                  height: 65px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  font-size: 14px;
                  font-weight: normal;
                  color: #aaaaaa;
                  line-height: 20px;
                  box-sizing: border-box;
                  border-radius: 4px 4px 4px 4px;
                  border: 1px solid transparent;
                  cursor: pointer;
                  margin-right: 24px;
                  img {
                    max-width: 48px;
                    height: 48px;
                  }
                }
                .icon-box:not(:last-child) {
                  margin-right: 24px;
                }
                li:not(:last-child) {
                  margin-bottom: 24px;
                }
                .icon-box-active {
                  border: 1px solid #409eff;
                }
              }
            }
          }
        }
      }
      footer {
        display: flex;
        align-items: center;
        margin: 0 30px;
        height: 93px;
        .modal_foot_left {
          width: 128px;
          height: 38px;
          background: linear-gradient(270deg, #26d0f1 0%, #409eff 100%);
          box-shadow: 0px 3px 8px 1px rgba(55, 175, 250, 0.5);
          border-radius: 19px 19px 19px 19px;
          border: none;
        }
        .modal_foot_right {
          width: 128px;
          height: 38px;
          background: #fafafa;
          border-radius: 19px 19px 19px 19px;
          border: none;
          font-size: 16px;
          font-weight: normal;
          color: #aaaaaa;
          line-height: 22px;
        }
      }
    }
  }
}

.sudoku_row {
  display: flex;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
}
.sudoku_item {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 32%;
  padding-top: 10px;
  padding-bottom: 10px;
}

.sudoku_item img {
  margin-bottom: 3px;
  display: block;
  width: 100px;
  height: 100px;
}

.sudoku_item span {
  margin-top: 10px;
  padding-top: 10px;
  display: block;
  width: 100px;
  text-align: center;
  height: 30px;
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
::ng-deep {
  .prize-right-drawer {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow: auto;
      padding-bottom: 66px;
      .prize-body {
        // 弹窗内容
        box-sizing: border-box;
        ul {
          padding: 39px 0;
          li {
            display: flex;
            align-items: center;
            margin-bottom: 32px;
            .delt-icon {
              width: 30px;
              height: 30px;
              background: url("../../../../assets/images/delete-ico.png");
              margin-left: 24px;
              background-position: center;
              background-size: cover;
              cursor: pointer;
            }
            .delt-icon-empty {
              width: 30px;
              height: 30px;
              margin-left: 24px;
            }
            .delt-icon:hover {
              background: url("../../../../assets/images/delete-ico-hover.png");
              background-position: center;
              background-size: cover;
            }
          }
        }
        .prize-box {
          .prize-name {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 30px;
            .prize-updata-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 84px;
              height: 30px;
              border-radius: 15px 15px 15px 15px;
              border: 1px solid #409eff;
              font-size: 14px;
              font-weight: normal;
              color: #409eff;
              line-height: 20px;
              cursor: pointer;
            }
            p {
              font-size: 14px;
              font-weight: normal;
              color: #17314c;
              line-height: 20px;
            }
          }
          .prize-settings {
            // 奖品设置
            display: flex;
            align-items: center;

            span {
              font-size: 14px;
              font-family: PingFang, PingFang SC;
              font-weight: normal;
              color: #aaaaaa;
              margin-right: 12px;
            }
            .unit {
              margin-left: 12px;
            }
          }

          .prize-icon {
            // 奖品设置 图标
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            ul {
              padding: 12px;
              li {
                margin: 0 !important;
                .upload-box {
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  width: 65px;
                  height: 65px;
                  box-sizing: border-box;
                  border-radius: 4px 4px 4px 4px;
                  border: 1px solid transparent;
                  cursor: pointer;
                  > img.upload {
                    width: 48px;
                    height: 48px;
                  }
                  > p {
                    padding-top: 4px;
                    font-size: 12px;
                    font-weight: normal;
                    color: #409eff;
                    line-height: 17px;
                  }
                }
                .icon-box {
                  width: 65px;
                  height: 65px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  font-size: 24px;
                  font-weight: normal;
                  color: #aaaaaa;
                  line-height: 20px;
                  box-sizing: border-box;
                  border-radius: 4px 4px 4px 4px;
                  border: 1px solid transparent;
                  cursor: pointer;
                  margin: 12px;
                  img {
                    max-width: 48px;
                    height: 48px;
                  }
                }
                .icon-box-active {
                  border: 1px solid #409eff;
                }
              }
            }
          }
        }
        .prize-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      // 滑块背景
      &::-webkit-scrollbar-track {
        // background-color: transparent;
        background-color: #f1f1f1;
        box-shadow: none;
      }
      // 滑块
      &::-webkit-scrollbar-thumb {
        // background-color: #e9e9e9;
        background-color: #c1c1c1;
        outline: none;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
      }
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-size: 20px;
      font-weight: bold;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
.mb-8 {
  margin-bottom: 8px;
}
.mt-8 {
  margin-top: 8px;
}
