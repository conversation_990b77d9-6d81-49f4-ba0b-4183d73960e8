import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-btn',
  templateUrl: './btn.component.html',
  styleUrls: ['./btn.component.less']
})
export class BtnComponent implements OnInit {

  @Input() text;
  @Input() color;
  @Input() image;
  @Input() hoverColor;
  @Input() hoverImage;
  isHover : boolean = false;
  @Output() btnclick = new EventEmitter<any>();

  constructor() { }
  
  ngOnInit() {

  }

  clickLink() {
    this.btnclick.emit('');
  }

  enter() {
    this.isHover = true;
    // console.log(`this.isHover = ${this.isHover}`);
  }

  leave() {
    this.isHover = false;
    // console.log(`this.isHover = ${this.isHover}`);
  }

}
