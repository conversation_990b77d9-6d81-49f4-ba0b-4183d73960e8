<div class="container">
  <div class="left">
    <div class="file-name">
      <nz-input-group nzCompact>
        <nz-select [(ngModel)]="langage">
          <nz-option [nzLabel]="'中文'" [nzValue]="'zh_CN'"></nz-option>
          <nz-option [nzLabel]="'英文'" [nzValue]="'en_US'"></nz-option>
        </nz-select>
        <input
          type="text"
          nz-input
          [(ngModel)]="fileName[langage]"
          placeholder="请输入报告名称"
          (blur)="changefileName()"
          style="width:calc(100% - 65px);"
        />
      </nz-input-group>
    </div>
    <div class="setting">
      <nz-tabset nzSize="small">
        <nz-tab nzTitle="组织架构">
          <div class="tab-content">
            <div class="tab-search1">
              <nz-input-group [nzPrefix]="suffixIconSearch1">
                <input
                  type="text"
                  nz-input
                  placeholder="请输入关键词"
                  [(ngModel)]="searchValue"
                />
              </nz-input-group>

              <ng-template #suffixIcon>
                <i nz-icon nzType="search"></i>
              </ng-template>

              <ng-template #suffixIconSearch1>
                <img src="./assets/images/icon_search.png" />
              </ng-template>
              <!-- 高级筛选 -->
              <app-advanced-filters
                #advancedChild
                [organizationDistanceList]="paramData?.organizationDistanceList"
                [isMin]="false"
                [father]="this"
              ></app-advanced-filters>
            </div>
            <div class="tree1 treeScroll">
              <nz-tree
                #nzTreeComponent
                nzCheckable
                [nzCheckStrictly]="true"
                [nzData]="orgList"
                [nzExpandAll]="false"
                [nzExpandedKeys]="expandedNodes"
                [nzSearchValue]="searchValue"
                (nzCheckBoxChange)="nzEvent($event)"
                (nzClick)="expanded($event)"
                [nzCheckedKeys]="checkedKeys"
              >
              </nz-tree>
            </div>
          </div>
        </nz-tab>
        <nz-tab nzTitle="人口标签">
          <div class="tab-content">
            <div class="tab-search2">
              <div class="top">
                <nz-input-group [nzPrefix]="suffixIconSearch1">
                  <input
                    type="text"
                    nz-input
                    placeholder="请输入关键词"
                    [(ngModel)]="searchRenValue"
                  />
                </nz-input-group>

                <ng-template #suffixIcon>
                  <i nz-icon nzType="search"></i>
                </ng-template>

                <ng-template #suffixIconSearch1>
                  <img src="./assets/images/icon_search.png" />
                </ng-template>
              </div>
              <div class="bottom">
                <label
                  nz-checkbox
                  [(ngModel)]="allChecked"
                  (ngModelChange)="updateAllChecked($event)"
                  [nzIndeterminate]="indeterminate"
                >
                  全选
                </label>
                <app-advanced-filters
                  #advancedChild
                  [isMin]="true"
                  [father]="this"
                >
                </app-advanced-filters>
              </div>
            </div>
            <div class="tree2 treeScroll">
              <nz-tree
                #nzTreeComponentRenkou
                nzCheckable
                [nzData]="demoList"
                [nzExpandAll]="false"
                [nzBlockNode]="true"
                [nzSearchValue]="searchRenValue"
                (nzCheckBoxChange)="nzEventRen($event)"
                (nzClick)="expanded($event)"
                [nzCheckedKeys]="checkedKeysRenkou"
              >
              </nz-tree>
            </div>
          </div>
        </nz-tab>
        <nz-tab nzTitle="内外部对比">
          <div class="tree3 treeScroll">
            <nz-tree
              #nzTreeComparisonList
              nzCheckable
              [nzData]="comparisonList"
              [nzExpandAll]="false"
              [nzExpandedKeys]="expandedRenNodes"
              [nzSearchValue]="searchRenValue"
              (nzCheckBoxChange)="nzEventComparison($event)"
              (nzClick)="expanded($event)"
              [nzCheckedKeys]="checkedKeysRenkou"
            >
            </nz-tree>
          </div>
        </nz-tab>
      </nz-tabset>
    </div>
  </div>

  <div class="right treeScroll">
    <!-- 
        <ng-container *ngFor="let type of showList">
            <div class="type" *ngIf="type.itemList.length > 0" >
                <span>{{type.name}}</span>
                
                <ng-container *ngFor="let item of type.itemList">
                    <nz-tag nzMode="closeable" (nzOnClose)="onClose()"> {{item.name}} </nz-tag>
                </ng-container>

            </div>
        </ng-container>
            -->

    <!-- <div *ngIf="showCount === 0"
      style="width: 100%; display: flex; flex-direction: column;align-items: center; margin-top: 85px;">
      <img src="./assets/images/org/group_empty.png">
      <div
        style="text-align:center; margin-top: 30px; font-size: 16px;font-family: PingFangSC-Light, PingFang SC;font-weight: 300;color: #495970;">
        请在左侧“组织架构”选择需要调研的因子，点击后可展开不同因子
      </div>
      
    </div> -->
    <div class="empty-box" *ngIf="showCount === 0">
      <app-empty
        text="请在左侧“组织架构”选择需要调研的因子，点击后可展开不同因子"
      ></app-empty>
    </div>

    <ng-template #expandedIcon>
      <img
        style="position: relative; top: -2px;"
        src="./assets/images/org/minus.png"
      />
      &nbsp;
      <!-- <i nz-icon nzType="minus-square" style="width: 18px; height: 18px;" class="ant-collapse-arrow"></i> -->
    </ng-template>

    <ng-template #collapseIcon>
      <img
        style="position: relative; top: -2px;"
        src="./assets/images/org/plus.png"
      />
      &nbsp;
    </ng-template>

    <ng-container *ngIf="showCount > 0">
      <div class="collapse-box">
        <nz-collapse>
          <ng-container *ngFor="let type of showList; let i = index">
            <nz-collapse-panel
              *ngIf="type.itemList.length > 0"
              [nzHeader]="temp"
              [(nzActive)]="type.active"
              [nzExpandedIcon]="type.active ? expandedIcon : collapseIcon"
              (nzActiveChange)="panelActiveChange(type.id, $event)"
            >
              <ng-template #temp>
                <div
                  style="display: inline-flex;flex-flow: column;width: calc(100% - 32px);"
                >
                  <div
                    style="display: inline-flex; justify-content: space-between; align-items: center;"
                  >
                    <span>{{ type.name }}</span>
                    <label
                      nz-checkbox
                      style="margin-left: 20px; margin-top: 0;"
                      *ngIf="type.name === '组织架构' && permission"
                      [(ngModel)]="isBatchCreate"
                      (ngModelChange)="choooseBatchCreate($event)"
                      >批量生成</label
                    >
                  </div>
                  <p
                    style="font-weight: 400;font-size: 12px;color: #BFBFBF;line-height: 17px;"
                    *ngIf="getIsShowDesc(type)"
                  >
                    *以下红色{{ type.name }}的数据，在报告/报表中设置为
                    【隐藏】，如需调整请联系管理员
                  </p>
                </div>
              </ng-template>
              <ng-container *ngIf="!type.haveOrder">
                <ng-container *ngFor="let item of type.itemList">
                  <nz-tag
                    *ngIf="item.isShowReport && item.isShowExcel"
                    nzMode="closeable"
                    (nzOnClose)="removeItem(type, item)"
                    class="tag-default"
                  >
                    {{ item.name }}
                    <!-- {{ item.isShowExcel }}
                    {{ item.isShowReport }} -->
                  </nz-tag>
                  <nz-tag
                    *ngIf="!(item.isShowReport && item.isShowExcel)"
                    nzMode="closeable"
                    class="tag-red"
                    nz-tooltip
                    [nzTitle]="titleTemplate"
                  >
                    {{ item.name }}
                    <!-- {{ item.isShowExcel }}
                    {{ item.isShowReport }} -->
                  </nz-tag>
                  <ng-template #titleTemplate>
                    <span
                      >不在{{ item.isShowExcel ? "" : "【报表】"
                      }}{{ item.isShowReport ? "" : "【报告】" }}中显示</span
                    >
                  </ng-template>
                </ng-container>
              </ng-container>

              <div
                *ngIf="type.haveOrder"
                [dragula]="'VAMPIRES' + type.id"
                [(dragulaModel)]="type.itemList"
              >
                <div
                  *ngFor="let item of type.itemList; let j = index"
                  class="orderedDiv"
                >
                  <div>
                    <div>
                      <span>
                        {{
                          j === 0 ? "主" + type.name : "次" + type.name
                        }}</span
                      >
                    </div>
                    <div>
                      <nz-tag
                        nzMode="closeable"
                        class="tag-default"
                        (nzOnClose)="removeItem(type, item)"
                      >
                        {{ item.name }}
                      </nz-tag>
                    </div>
                  </div>
                  <img src="./assets/images/org/col_drag.png " />
                </div>
              </div>
            </nz-collapse-panel>
          </ng-container>
        </nz-collapse>
      </div>
    </ng-container>
  </div>
</div>
