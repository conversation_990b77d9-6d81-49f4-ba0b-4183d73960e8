<section class="custom-page" #deliveryTable>
  <div class="client-width content">
    <ul style="display: flex;justify-content: flex-end;padding-top: 20px;">
      <app-break-crumb
        [Breadcrumbs]="Breadcrumbs"
        [queryParams]="queryParams"
      ></app-break-crumb>
    </ul>
    <div class="search-box">
      <div style="display: flex;">
        <h2 class="bread">题本管理</h2>

        <!-- <nz-radio-group
          [(ngModel)]="lanIndex"
          (ngModelChange)="changeLan($event)"
          [nzButtonStyle]="'solid'"
          [nzSize]="'small'"
        >
          <label nz-radio-button [nzValue]="0">中文</label>
          <label nz-radio-button [nzValue]="1">ENG</label>
        </nz-radio-group> -->
      </div>

      <div>
        <nz-input-group [nzPrefix]="IconSearch">
          <input
            class="input-search"
            type="text"
            nz-input
            placeholder="请输入关键词"
            [(ngModel)]="keyWord"
            (blur)="getList()"
            (keydown.enter)="getList()"
          />
          <app-btn
            *ngIf="
              projectType &&
              projectType !== 'ANNOUNCED' &&
              listChecked === 'checked'
            "
            [text]="'导出列表'"
            [image]="'./assets/images/org/export.png'"
            [hoverColor]="'#B483D6'"
            [hoverImage]="'./assets/images/org/export_hover.png'"
            (btnclick)="exportList()"
          >
          </app-btn>

          <ng-container
            *ngIf="
              standardReportType != 'BLANK_CUSTOM' &&
              currentReportType != 'BLANK_CUSTOM'
            "
          >
            <app-btn
              *ngIf="listChecked === 'checked'"
              [text]="'填答设置'"
              [image]="'./assets/images/org/col_set.png'"
              [hoverColor]="'#5CACFE'"
              [hoverImage]="'./assets/images/org/col_set_active.png'"
              (btnclick)="answerSetting(true)"
            >
            </app-btn>
          </ng-container>
        </nz-input-group>

        <ng-template #IconSearch>
          <i nz-icon nzType="search" class="icon-search"></i>
        </ng-template>
      </div>
    </div>
    <div class="mb-12 mt-12">
      <app-i18n-select
        [active]="lan"
        (selectChange)="onSelectI18n($event)"
      ></app-i18n-select>
    </div>

    <div class="btn-parent" *ngIf="listChecked !== 'checked'">
      <div class="btn-box">
        <button class="primary-btn mr-16" (click)="showModal()">
          <i nz-icon nzType="plus-circle" nzTheme="fill"></i>新增
        </button>
        <nz-upload
          [nzPreview]="preview"
          [nzCustomRequest]="customReq"
          [nzFileList]="fileList"
          (nzChange)="handleChange($event)"
        >
          <button class="default-btn">
            <i class="iconfont icon-icon_export"></i>批量上传
          </button>
        </nz-upload>
        <button
          nz-button
          nzType="link"
          (click)="downLoad()"
          [nzLoading]="buttonload"
        >
          下载模板
        </button>
      </div>

      <div style="display: flex;">
        <app-btn
          *ngIf="projectType"
          [text]="'导出列表'"
          [image]="'./assets/images/org/export.png'"
          [hoverColor]="'#B483D6'"
          [hoverImage]="'./assets/images/org/export_hover.png'"
          (btnclick)="exportList()"
        >
        </app-btn>

        <ng-container
          *ngIf="
            standardReportType != 'BLANK_CUSTOM' &&
            currentReportType != 'BLANK_CUSTOM'
          "
        >
          <app-btn
            [text]="'填答设置'"
            [image]="'./assets/images/org/col_set.png'"
            [hoverColor]="'#5CACFE'"
            [hoverImage]="'./assets/images/org/col_set_active.png'"
            (btnclick)="answerSetting(false)"
          >
          </app-btn>

          <app-btn
            [text]="'题本穿透'"
            [image]="'./assets/images/org/col_set_1.png'"
            [hoverColor]="'#5CACFE'"
            [hoverImage]="'./assets/images/org/col_set_active_1.png'"
            (btnclick)="pierceThrough()"
          >
          </app-btn>
        </ng-container>

        <div
          class="delete-box"
          *ngIf="isedited"
          nz-popconfirm
          nzPopconfirmTitle="要删除选中的题目吗？"
          (nzOnConfirm)="onDeleteBatch()"
        >
          <div class="svg-ico-18 svg-ico-delete" title="删除"></div>
          <div class="delete-txt">批量删除</div>
        </div>
      </div>
    </div>
    <nz-drawer
      [(nzVisible)]="throughisVisible"
      nzWrapClassName="round-right-drawer1"
      [nzTitle]="'题本穿透'"
      [nzWidth]="1000"
      (nzOnClose)="ThroughCancel()"
      (nzOnOk)="ThroughOk()"
    >
      <div
        style="display: flex; align-items: center; justify-content: space-between; padding: 0 0 16px 0;"
      >
        <div style="display: flex; align-items: center;">
          <div class="load_bt">
            <nz-upload
              [nzCustomRequest]="customReqload"
              [nzShowUploadList]="false"
            >
              <span class="title-icon">
                <span class="iconfont icon-icon_import"></span> 导入</span
              >
            </nz-upload>
            <nz-divider style="margin: 0 29px;" nzType="vertical"></nz-divider>
            <span class="title-icon" (click)="exportR()">
              <span class="iconfont icon-icon_export"></span> 导出</span
            >
          </div>
        </div>
        <div>
          <div
            class="all_open"
            nz-popover
            [nzPopoverTitle]="titleTemplate"
            nzPopoverTrigger="click"
            [(nzVisible)]="popoverVisible"
            [nzPopoverContent]="contentTemplate"
            nzPopoverPlacement="bottomRight"
          >
            已选关联({{ penetrationList.length }})
          </div>
        </div>
      </div>
      <ng-container style="position: relative;">
        <ul class="through_ul">
          <li class="th_li_left">
            <div style="display: flex;align-items: center;">
              <span>条件</span>
              <nz-select
                [(ngModel)]="questionType"
                (ngModelChange)="changeQuestionType($event)"
                nzAllowClear
                style="width: 150px; margin: 0 20px 0 15px;"
                nzPlaceHolder="请选择条件"
              >
                <nz-option [nzLabel]="'量表'" nzValue="SCALE"></nz-option>
                <nz-option [nzLabel]="'单选'" nzValue="SINGLE"></nz-option>
                <ng-container *ngIf="isNewQuestionBook()">
                  <nz-option
                    [nzLabel]="'评价'"
                    nzValue="EVALUATION"
                  ></nz-option>
                </ng-container>
                <ng-container
                  *ngIf="
                    standardReportType.includes('360_') ||
                    standardReportType.includes('270_')
                  "
                >
                  <nz-option
                    [nzLabel]="'多选式开放题'"
                    nzValue="MULTIPLE_CHOICE_ESSAY_QUESTION"
                  ></nz-option
                ></ng-container>
                <ng-container *ngIf="isNewQuestionBook()">
                  <nz-option
                    [nzLabel]="'滑块'"
                    nzValue="PROPORTION"
                  ></nz-option>
                  <!-- <nz-option
                    [nzLabel]="'多级比重'"
                    nzValue="PROPORTION_MULTIPLE"
                  ></nz-option> -->
                </ng-container>
              </nz-select>
              <nz-input-group
                style="width: 205px;"
                (keyup.enter)="searchQuestion()"
                [nzPrefix]="prefixTemplateUser"
              >
                <input
                  type="text"
                  nz-input
                  placeholder="请输入关键词"
                  [(ngModel)]="questionStr"
                />
              </nz-input-group>
              <ng-template #prefixTemplateUser
                ><i nz-icon nzType="search" nzTheme="outline"></i
              ></ng-template>
            </div>
            <p>请在题本选中选项，作为穿透条件</p>
            <ul class="question-box">
              <li *ngFor="let ques of questionList; let queIdx = index">
                <div class="label">
                  <!-- <label nz-radio [(ngModel)]="ques.checked" (ngModelChange)="chooseQues($event, ques.id, queIdx)"></label> -->
                  <label
                    nz-checkbox
                    [(ngModel)]="ques.checked"
                    [nzIndeterminate]="ques.indeterminate"
                    (ngModelChange)="chooseQues($event, ques.id)"
                  ></label>
                </div>
                <ul>
                  <li>
                    <div>
                      <i
                        class="icon-xiala"
                        nz-icon
                        nzType="caret-right"
                        nzTheme="outline"
                        [ngClass]="ques.selectFlag ? 'turn' : ''"
                        (click)="selectQuestion(queIdx)"
                      ></i>
                    </div>
                    <div>
                      {{ ques.name[lan] }}
                    </div>
                  </li>
                  <ng-container *ngIf="ques.selectFlag">
                    <li
                      class="opt secondOpt"
                      style="margin-left: 20px;"
                      *ngFor="let opt of ques.options.options"
                    >
                      <div class="secondOpt-opt">
                        <label
                          nz-checkbox
                          [(ngModel)]="opt.checked"
                          (ngModelChange)="
                            chooseOpt($event, ques.id, opt.id, ques.type)
                          "
                          [nzDisabled]="
                            ['PROPORTION', 'PROPORTION_MULTIPLE'].includes(
                              ques.type
                            ) && !opt.isScoring
                          "
                          >{{ opt.name[lan] }}</label
                        >
                      </div>
                      <ng-container
                        *ngIf="
                          ['PROPORTION', 'PROPORTION_MULTIPLE'].includes(
                            ques.type
                          ) &&
                          opt.isScoring &&
                          parentQuestionId === ques.id &&
                          opt.checked
                        "
                      >
                        <div
                          class="secondOpt-card"
                          *ngIf="
                            ['PROPORTION', 'PROPORTION_MULTIPLE'].includes(
                              ques.type
                            ) && opt.isScoring
                          "
                        >
                          <ng-container
                            *ngFor="let score of surveyStandardOptions"
                          >
                            <div *ngIf="score.id === opt.id">
                              <nz-input-number
                                [(ngModel)]="score.minScore"
                                [nzSize]="'small'"
                                [nzMin]="opt.lowestScore"
                                [nzMax]="opt.highestScore"
                                [nzStep]="opt.stepScore"
                                style="width: 60px;"
                              ></nz-input-number>
                              <span style="margin: 0 8px;">-</span>
                              <nz-input-number
                                [(ngModel)]="score.maxScore"
                                [nzSize]="'small'"
                                [nzMin]="opt.lowestScore"
                                [nzMax]="opt.highestScore"
                                [nzStep]="opt.stepScore"
                                style="width: 60px;"
                              ></nz-input-number>
                            </div>
                          </ng-container>
                        </div>
                      </ng-container>
                    </li>
                  </ng-container>
                </ul>
              </li>
            </ul>
          </li>
          <li class="th_li_right">
            <div style="display: flex;align-items: center;">
              <span>结果</span>
              <nz-select
                [(ngModel)]="resultType"
                (ngModelChange)="changeResultType($event)"
                nzAllowClear
                style="width: 150px; margin: 0 20px 0 15px;"
                nzPlaceHolder="请选择条件"
              >
                <nz-option
                  [nzLabel]="'开放题'"
                  [nzValue]="'ESSAY_QUESTION'"
                ></nz-option>
                <nz-option
                  [nzLabel]="'多选式开放题'"
                  [nzValue]="'MULTIPLE_CHOICE_ESSAY_QUESTION'"
                ></nz-option>
                <nz-option
                  *ngIf="isNewQuestionBook()"
                  [nzLabel]="'滑块'"
                  [nzValue]="'PROPORTION'"
                ></nz-option>
                <nz-option
                  *ngIf="isNewQuestionBook()"
                  [nzLabel]="'多级比重'"
                  [nzValue]="'PROPORTION_MULTIPLE'"
                ></nz-option>
              </nz-select>
              <nz-input-group
                style="width: 205px;"
                (keyup.enter)="searchResult()"
                [nzPrefix]="prefixTemplateUser"
              >
                <input
                  type="text"
                  nz-input
                  placeholder="请输入关键词"
                  [(ngModel)]="resultStr"
                />
              </nz-input-group>
              <ng-template #prefixTemplateUser
                ><i nz-icon nzType="search" nzTheme="outline"></i
              ></ng-template>
            </div>
            <p>请为选中的穿透条件设置关联题本，作为穿透结果</p>
            <ul class="question-box">
              <li *ngFor="let ques of resultList; let queIdx = index">
                <div class="label">
                  <label
                    nz-radio
                    [(ngModel)]="ques.checked"
                    (ngModelChange)="chooseRes($event, ques.id, queIdx)"
                  ></label>
                </div>
                <ul>
                  <li>{{ ques.name[lan] }}</li>
                  <li *ngFor="let option of ques.options.options">
                    {{ option.name[lan] }}
                  </li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
        <div class="bottom">
          <span style="margin-right: 42px; font-size: 14px; color: #495970;"
            >显示方式：</span
          >
          <nz-radio-group [(ngModel)]="answerType">
            <label style="margin-right: 100px;" nz-radio nzValue="FINAL"
              >所有条件题全部答完后</label
            >
            <label nz-radio nzValue="POPUP">选中后，立即弹出弹窗</label>
          </nz-radio-group>
        </div>
        <!-- <ul class="button_ul">
          <li class="btnok" (click)="createQues()">关联</li>
          <li class="btndef" (click)="reset()">恢复默认</li>
        </ul> -->

        <!-- <div class="load_bt">
          <nz-upload [nzCustomRequest]="customReqload" [nzShowUploadList]="false">
            <span class="title-icon" style="cursor: pointer;"> <span class="iconfont icon-icon_import" ></span> 导入</span>
          </nz-upload>
          <nz-divider style="margin: 0 29px;" nzType="vertical"></nz-divider>
          <span class="title-icon" (click)="exportR()" style="cursor: pointer;"> <span class="iconfont icon-icon_export" ></span> 导出</span>
        </div> -->

        <!-- <div class="all_open" nz-popover [nzPopoverTitle]="titleTemplate" nzPopoverTrigger="click"
          [(nzVisible)]="popoverVisible" [nzPopoverContent]="contentTemplate" nzPopoverPlacement="bottomRight">
          已选关联({{penetrationList.length}})</div> -->
        <ng-template #titleTemplate>
          <div class="popover-header">
            <span>关联详情</span
            ><a
              nz-popconfirm
              nzPopconfirmTitle="是否清空所有关联？"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="confirmtip()"
              (nzOnCancel)="canceltip()"
              >清空所有关联</a
            >
          </div>
        </ng-template>
        <ng-template #contentTemplate>
          <div class="popover-body">
            <ul>
              <li *ngFor="let ques of penetrationList">
                <div class="condition-box">
                  <div class="popover-tips">条件</div>
                  <div class="popover-text">
                    <p [innerHTML]="ques.parentQuestionName[lan] | html"></p>
                    <p>
                      <span
                        *ngFor="
                          let opt of ques.surveyStandardOptions;
                          let index = index
                        "
                        >{{ (index === 0 ? "" : "/") + opt.name[lan] }}
                        <ng-container
                          *ngIf="
                            ['PROPORTION', 'PROPORTION_MULTIPLE'].includes(
                              ques.parentType
                            )
                          "
                        >
                          ({{ opt.minScore }}-{{ opt.maxScore }})
                        </ng-container>
                      </span>
                    </p>
                  </div>
                  <div
                    class="popover-del"
                    nz-popconfirm
                    nzPopconfirmTitle="是否删除当前题目？"
                    nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="
                      delOne(
                        ques.parentQuestionId,
                        ques.questionId,
                        ques.surveyStandardOptions,
                        ques.parentType
                      )
                    "
                  ></div>
                </div>
                <div class="result-box">
                  <div class="popover-res-tips">结果</div>
                  <div class="popover-text">
                    <p [innerHTML]="ques.questionName[lan] | html"></p>
                  </div>
                </div>
                <div class="popup-method">
                  {{
                    ques.answerType === "POPUP"
                      ? "选中后，立即弹出弹窗"
                      : "所有条件全部答完"
                  }}
                </div>
              </li>
            </ul>
            <div class="close-association">
              <a (click)="closeAssociation()">收起已选关联</a>
            </div>
          </div>
        </ng-template>
      </ng-container>
      <div class="footer">
        <button nz-button style="margin-right: 8px;" (click)="reset()">
          恢复默认
        </button>
        <button nz-button nzType="primary" (click)="createQues()">
          确认
        </button>
      </div>
    </nz-drawer>

    <nz-table
      #basicTable
      [nzData]="dataList"
      [nzScroll]="{ y: tableTop + '' }"
      [nzFrontPagination]="false"
    >
      <thead>
        <tr>
          <th nzWidth="60px" *ngIf="projectType != 'ANSWERING'"></th>
          <th
            nzWidth="60px"
            *ngIf="isedited && listChecked !== 'checked'"
            nzShowCheckbox
            [(nzChecked)]="isAllDisplayDataChecked"
            [nzIndeterminate]="isIndeterminate"
            (nzCheckedChange)="checkAll($event)"
          ></th>
          <th nzWidth="60px">序号</th>
          <th nzWidth="130px">维度名称</th>
          <th nzWidth="100px"></th>
          <th nzWidth="240px">题干</th>
          <th nzWidth="240px">选项</th>
          <!-- <th>分值</th> -->
          <ng-container
            *ngIf="
              standardReportType != 'BLANK_CUSTOM' &&
              currentReportType != 'BLANK_CUSTOM'
            "
          >
            <th nzWidth="60px" *ngIf="isedited">比重</th>
          </ng-container>
          <th nzWidth="80px">状态</th>
          <th nzWidth="90px" *ngIf="listChecked !== 'checked'">操作</th>
        </tr>
      </thead>
      <tbody
        *ngIf="projectType != 'ANSWERING'"
        dragula="VAMPIRESDRAGULA"
        [(dragulaModel)]="basicTable.data"
        (dragulaModelChange)="dragnumber($event)"
      >
        <ng-container *ngFor="let data of basicTable.data; let i = index">
          <tr
            *ngIf="!data?.isPierceThrough"
            [ngClass]="data?.isPierceThrough ? 'through-tr' : ''"
          >
            <td>
              <i class="iconfont icon-caidan" style="cursor: pointer;"></i>
            </td>
            <td
              *ngIf="isedited && listChecked !== 'checked'"
              nzShowCheckbox
              [(nzChecked)]="data.checked"
              (nzCheckedChange)="refreshStatus()"
            ></td>
            <td>{{ i + 1 }}</td>
            <td>
              {{
                data.dimensionName && data.dimensionName[lan]
                  ? data.dimensionName[lan]
                  : "--"
              }}
              <span *ngIf="data.childCustomDimension"
                >/{{
                  data.childCustomDimension && dimensionMap
                    ? dimensionMap[data.childCustomDimension].name[lan]
                    : "--"
                }}</span
              >
            </td>
            <td>
              <span
                *ngIf="!data.isPierceThrough"
                class="tip-box"
                [ngStyle]="{
                  color: tips[data.type].color,
                  background: tips[data.type].bg
                }"
              >
                <!-- todo: 非中文多语言默认展示中文 -->
                {{ tips[data.type].name[lan] || tips[data.type].name.zh_CN }}
              </span>
              <span
                *ngIf="data.isPierceThrough"
                class="tip-box"
                [ngStyle]="{
                  color: tips.isPierceThrough.color,
                  background: tips.isPierceThrough.bg
                }"
                >{{
                  tips.isPierceThrough.name[lan] ||
                    tips.isPierceThrough.name.zh_CN
                }}</span
              >
            </td>
            <td>
              <span
                *ngIf="data.name"
                [innerHTML]="data.name[lan] | html"
              ></span>
              <span
                *ngIf="data.isRevisionName"
                class="block"
                nz-tooltip
                nzTooltipTitle="已修订"
              ></span>
            </td>
            <td>
              <span *ngFor="let option of data.options.options"
                >{{ option.name[lan] }}/</span
              >
              <span
                *ngIf="data.isRevisionOption"
                class="block"
                nz-tooltip
                nzTooltipTitle="已修订"
              ></span>
            </td>

            <ng-container
              *ngIf="
                standardReportType != 'BLANK_CUSTOM' &&
                currentReportType != 'BLANK_CUSTOM'
              "
            >
              <td *ngIf="isedited">
                <span>{{
                  data.type === "ESSAY_QUESTION" ||
                  data.type === "MULTIPLE_CHOICE_ESSAY_QUESTION"
                    ? ""
                    : data.proportion
                }}</span>
              </td>
            </ng-container>

            <td>
              <ng-container>
                <span *ngIf="lan === 'zh_CN'">{{
                  data.isRequire ? "必填" : "非必填"
                }}</span>
                <span *ngIf="lan !== 'zh_CN'">{{
                  data.isRequire ? "Required" : "Not Required"
                }}</span>
              </ng-container>
              <br />
            </td>
            <td *ngIf="!isedited && listChecked !== 'checked'">
              <ng-container
                *ngIf="
                  (data.type == 'ESSAY_QUESTION' ||
                    data.type == 'MULTIPLE_CHOICE_ESSAY_QUESTION') &&
                  listChecked !== 'checked'
                "
              >
                <div style="white-space: nowrap;">
                  <!-- 编辑 -->
                  <app-btn-iconfont
                    type="default"
                    iconType="edit"
                    theme="fill"
                    (btnclick)="onEditQuestion(data.id, data)"
                  ></app-btn-iconfont>
                  <!-- 删除 -->
                  <app-btn-iconfont
                    type="iconfont"
                    iconFont="icon-icon_delete"
                    [filled]="true"
                    nz-popconfirm
                    hoverColor="#F19672"
                    nzPopconfirmTitle="要删除选中的题目吗？"
                    (nzOnConfirm)="onDeleteQuestion(data.id)"
                  >
                  </app-btn-iconfont>
                </div>
              </ng-container>
            </td>
            <td *ngIf="isedited && listChecked !== 'checked'">
              <ng-container *ngIf="listChecked !== 'checked'">
                <div style="white-space: nowrap;">
                  <!-- 编辑 -->
                  <app-btn-iconfont
                    type="default"
                    iconType="edit"
                    theme="fill"
                    (btnclick)="onEditQuestion(data.id, data)"
                  ></app-btn-iconfont>
                  <!-- 删除 -->
                  <app-btn-iconfont
                    type="iconfont"
                    iconFont="icon-icon_delete"
                    [filled]="true"
                    nz-popconfirm
                    hoverColor="#F19672"
                    nzPopconfirmTitle="要删除选中的题目吗？"
                    (nzOnConfirm)="onDeleteQuestion(data.id)"
                  >
                  </app-btn-iconfont>
                </div>
              </ng-container>
            </td>
          </tr>
          <tr *ngIf="data.isPierceThrough">
            <td [attr.colspan]="'11'" style="padding: 0;">
              <nz-table [nzShowPagination]="false" [nzData]="[{}]">
                <thead>
                  <th style="padding: 16px; width: 60px;">
                    <i
                      class="iconfont icon-caidan"
                      style="cursor: pointer;"
                    ></i>
                  </th>
                  <th
                    style="padding: 16px; width: 60px;"
                    *ngIf="isedited && listChecked !== 'checked'"
                    nzShowCheckbox
                    [(nzChecked)]="data.checked"
                    (nzCheckedChange)="refreshStatus()"
                  ></th>
                  <th style="padding: 16px; width: 60px;">{{ i + 1 }}</th>
                  <th style="padding: 16px; width: 130px;">
                    {{
                      data.dimensionName && data.dimensionName[lan]
                        ? data.dimensionName[lan]
                        : "--"
                    }}
                    <span *ngIf="data.childCustomDimension"
                      >/{{
                        data.childCustomDimension && dimensionMap
                          ? dimensionMap[data.childCustomDimension].name[lan]
                          : "--"
                      }}</span
                    >
                  </th>
                  <th style="padding: 16px; width: 100px;">
                    <span
                      *ngIf="!data.isPierceThrough"
                      class="tip-box"
                      [ngStyle]="{
                        color: tips[data.type].color,
                        background: tips[data.type].bg
                      }"
                    >
                      <!-- todo: 非中文多语言默认展示中文 -->
                      {{
                        tips[data.type].name[lan] || tips[data.type].name.zh_CN
                      }}
                    </span>
                    <span
                      *ngIf="data.isPierceThrough"
                      class="tip-box"
                      [ngStyle]="{
                        color: tips.isPierceThrough.color,
                        background: tips.isPierceThrough.bg
                      }"
                      >{{
                        tips.isPierceThrough.name[lan] ||
                          tips.isPierceThrough.name.zh_CN
                      }}</span
                    >
                  </th>
                  <th style="padding: 16px; width: 240px;">
                    <span *ngIf="data.name">{{ data.name[lan] }}</span>
                  </th>
                  <th style="padding: 16px; width: 240px;">
                    <span *ngFor="let option of data.options.options"
                      >{{ option.name[lan] }}/</span
                    >
                  </th>

                  <ng-container
                    *ngIf="
                      standardReportType != 'BLANK_CUSTOM' &&
                      currentReportType != 'BLANK_CUSTOM'
                    "
                  >
                    <th style="padding: 16px; width: 60px;" *ngIf="isedited">
                      <span>{{
                        data.type === "ESSAY_QUESTION" ||
                        data.type === "MULTIPLE_CHOICE_ESSAY_QUESTION"
                          ? ""
                          : data.proportion
                      }}</span>
                    </th>
                  </ng-container>
                  <th style="padding: 16px; width: 80px;">
                    <ng-container>
                      <span *ngIf="lan === 'zh_CN'">{{
                        data.isRequire ? "必填" : "非必填"
                      }}</span>
                      <span *ngIf="lan !== 'zh_CN'">{{
                        data.isRequire ? "Required" : "Not Required"
                      }}</span>
                    </ng-container>
                    <br />
                  </th>
                  <th
                    style="padding: 16px; width: 90px;"
                    *ngIf="!isedited && listChecked !== 'checked'"
                  >
                    <ng-container
                      *ngIf="
                        (data.type == 'ESSAY_QUESTION' ||
                          data.type == 'MULTIPLE_CHOICE_ESSAY_QUESTION') &&
                        listChecked !== 'checked'
                      "
                    >
                      <div style="white-space: nowrap;">
                        <!-- 编辑 -->
                        <app-btn-iconfont
                          type="default"
                          iconType="edit"
                          theme="fill"
                          (btnclick)="onEditQuestion(data.id, data)"
                        ></app-btn-iconfont>
                        <!-- 删除 -->
                        <app-btn-iconfont
                          type="iconfont"
                          iconFont="icon-icon_delete"
                          [filled]="true"
                          nz-popconfirm
                          hoverColor="#F19672"
                          nzPopconfirmTitle="要删除选中的题目吗？"
                          (nzOnConfirm)="onDeleteQuestion(data.id)"
                        >
                        </app-btn-iconfont>
                      </div>
                    </ng-container>
                  </th>
                  <th
                    style="padding: 16px; width: 90px;"
                    *ngIf="isedited && listChecked !== 'checked'"
                  >
                    <ng-container *ngIf="listChecked !== 'checked'">
                      <div style="white-space: nowrap;">
                        <!-- 编辑 -->
                        <app-btn-iconfont
                          type="default"
                          iconType="edit"
                          theme="fill"
                          (btnclick)="onEditQuestion(data.id, data)"
                        ></app-btn-iconfont>
                        <!-- 删除 -->
                        <app-btn-iconfont
                          type="iconfont"
                          iconFont="icon-icon_delete"
                          [filled]="true"
                          nz-popconfirm
                          hoverColor="#F19672"
                          nzPopconfirmTitle="要删除选中的题目吗？"
                          (nzOnConfirm)="onDeleteQuestion(data.id)"
                        >
                        </app-btn-iconfont>
                      </div>
                    </ng-container>
                  </th>
                </thead>
                <tbody *ngIf="data.resultQuestion">
                  <tr>
                    <td></td>
                    <td *ngIf="isedited && listChecked !== 'checked'"></td>
                    <td></td>
                    <td>
                      {{
                        data.resultQuestion.dimensionName &&
                        data.resultQuestion.dimensionName[lan]
                          ? data.resultQuestion.dimensionName[lan]
                          : "--"
                      }}
                      <span *ngIf="data.resultQuestion.childCustomDimension"
                        >/{{
                          data.resultQuestion.childCustomDimension &&
                          dimensionMap
                            ? dimensionMap[
                                data.resultQuestion.childCustomDimension
                              ].name[lan]
                            : "--"
                        }}</span
                      >
                    </td>
                    <td>
                      <ng-container *ngIf="data.isPierceThrough">
                      </ng-container>
                    </td>
                    <td>
                      <span>{{ data.resultQuestion.name[lan] }}</span>
                      <span
                        *ngIf="data.resultQuestion.isRevisionName"
                        class="block"
                        nz-tooltip
                        nzTooltipTitle="已修订"
                      ></span>
                    </td>
                    <td>
                      <ng-container>
                        <span
                          *ngFor="
                            let option of data.resultQuestion.options.options
                          "
                          >{{ option.name[lan] }}/</span
                        >
                        <span
                          *ngIf="data.resultQuestion.isRevisionOption"
                          class="block"
                          nz-tooltip
                          nzTooltipTitle="已修订"
                        ></span>
                      </ng-container>
                    </td>
                    <ng-container
                      *ngIf="
                        standardReportType != 'BLANK_CUSTOM' &&
                        currentReportType != 'BLANK_CUSTOM'
                      "
                    ></ng-container>
                    <td *ngIf="isedited">
                      <span>
                        {{
                          data.resultQuestion.type === "ESSAY_QUESTION" ||
                          data.resultQuestion.type ===
                            "MULTIPLE_CHOICE_ESSAY_QUESTION"
                            ? ""
                            : data.resultQuestion.proportion
                        }}</span
                      >
                    </td>
                    <td>
                      <ng-container>
                        <span *ngIf="lan === 'zh_CN'">{{
                          data.resultQuestion.isRequire ? "必填" : "非必填"
                        }}</span>
                        <span *ngIf="lan !== 'zh_CN'">{{
                          data.resultQuestion.isRequire
                            ? "Required"
                            : "Not Required"
                        }}</span>
                      </ng-container>
                    </td>
                    <td *ngIf="!isedited && listChecked !== 'checked'">
                      <ng-container
                        *ngIf="
                          (data.resultQuestion.type == 'ESSAY_QUESTION' ||
                            data.resultQuestion.type ==
                              'MULTIPLE_CHOICE_ESSAY_QUESTION') &&
                          listChecked !== 'checked'
                        "
                      >
                        <div>
                          <!-- 编辑 -->
                          <app-btn-iconfont
                            type="default"
                            iconType="edit"
                            theme="fill"
                            (btnclick)="
                              onEditQuestion(data.resultQuestion.id, data)
                            "
                          ></app-btn-iconfont>
                          <!-- 删除 -->
                          <app-btn-iconfont
                            type="iconfont"
                            iconFont="icon-icon_delete"
                            [filled]="true"
                            nz-popconfirm
                            hoverColor="#F19672"
                            nzPopconfirmTitle="要删除选中的题目吗？"
                            (nzOnConfirm)="
                              onDeleteQuestion(data.resultQuestion.id)
                            "
                          >
                          </app-btn-iconfont>
                        </div>
                      </ng-container>
                    </td>
                    <td *ngIf="isedited && listChecked !== 'checked'">
                      <ng-container *ngIf="listChecked !== 'checked'">
                        <div>
                          <!-- 编辑 -->
                          <app-btn-iconfont
                            type="default"
                            iconType="edit"
                            theme="fill"
                            (btnclick)="
                              onEditQuestion(data.resultQuestion.id, data)
                            "
                          ></app-btn-iconfont>
                          <!-- 删除 -->
                          <app-btn-iconfont
                            type="iconfont"
                            iconFont="icon-icon_delete"
                            [filled]="true"
                            nz-popconfirm
                            hoverColor="#F19672"
                            nzPopconfirmTitle="要删除选中的题目吗？"
                            (nzOnConfirm)="
                              onDeleteQuestion(data.resultQuestion.id)
                            "
                          >
                          </app-btn-iconfont>
                        </div>
                      </ng-container>
                    </td>
                  </tr>
                </tbody>
              </nz-table>
            </td>
          </tr>
        </ng-container>
      </tbody>
      <tbody *ngIf="projectType == 'ANSWERING'">
        <ng-container *ngFor="let data of basicTable.data; let i = index">
          <tr [ngClass]="data.isPierceThrough ? 'through-tr' : ''">
            <td
              *ngIf="isedited && listChecked !== 'checked'"
              nzWidth="60px"
              nzShowCheckbox
              [(nzChecked)]="data.checked"
              (nzCheckedChange)="refreshStatus()"
            ></td>
            <td>{{ i + 1 }}</td>
            <td>
              {{
                data.dimensionName && data.dimensionName[lan]
                  ? data.dimensionName[lan]
                  : "--"
              }}
              <span *ngIf="data.childCustomDimension"
                >/{{
                  data.childCustomDimension && dimensionMap
                    ? dimensionMap[data.childCustomDimension].name[lan]
                    : "--"
                }}</span
              >
            </td>
            <td>
              <span
                *ngIf="!data.isPierceThrough"
                class="tip-box"
                [ngStyle]="{
                  color: tips[data.type].color,
                  background: tips[data.type].bg
                }"
              >
                <!-- todo: 非中文多语言默认展示中文 -->
                {{ tips[data.type].name[lan] || tips[data.type].name.zh_CN }}
              </span>
              <span
                *ngIf="data.isPierceThrough"
                class="tip-box"
                [ngStyle]="{
                  color: tips.isPierceThrough.color,
                  background: tips.isPierceThrough.bg
                }"
                >{{
                  tips.isPierceThrough.name[lan] ||
                    tips.isPierceThrough.name.zh_CN
                }}</span
              >
            </td>
            <td>
              <span
                *ngIf="data.name"
                [innerHTML]="data.name[lan] | html"
              ></span>
              <span
                *ngIf="data.isRevisionName"
                class="block"
                nz-tooltip
                nzTooltipTitle="已修订"
              ></span>
            </td>
            <td>
              <span *ngFor="let option of data.options.options"
                >{{ option.name[lan] }}/</span
              >
              <span
                *ngIf="data.isRevisionOption"
                class="block"
                nz-tooltip
                nzTooltipTitle="已修订"
              ></span>
            </td>
            <ng-container
              *ngIf="
                standardReportType != 'BLANK_CUSTOM' &&
                currentReportType != 'BLANK_CUSTOM'
              "
            >
              <td *ngIf="isedited">
                <span>{{
                  data.type === "ESSAY_QUESTION" ||
                  data.type === "MULTIPLE_CHOICE_ESSAY_QUESTION"
                    ? ""
                    : data.proportion
                }}</span>
              </td>
            </ng-container>

            <td>
              <ng-container>
                <span *ngIf="lan === 'zh_CN'">{{
                  data.isRequire ? "必填" : "非必填"
                }}</span>
                <span *ngIf="lan !== 'zh_CN'">{{
                  data.isRequire ? "Required" : "Not Required"
                }}</span>
              </ng-container>
              <br />
            </td>
            <td *ngIf="!isedited && listChecked !== 'checked'">
              <ng-container
                *ngIf="
                  (data.type == 'ESSAY_QUESTION' ||
                    data.type == 'MULTIPLE_CHOICE_ESSAY_QUESTION') &&
                  listChecked !== 'checked'
                "
              >
                <div style="white-space: nowrap;">
                  <!-- 编辑 -->
                  <app-btn-iconfont
                    type="default"
                    iconType="edit"
                    theme="fill"
                    (btnclick)="onEditQuestion(data.id, data)"
                  ></app-btn-iconfont>
                  <!-- 删除 -->
                  <app-btn-iconfont
                    type="iconfont"
                    iconFont="icon-icon_delete"
                    [filled]="true"
                    nz-popconfirm
                    hoverColor="#F19672"
                    nzPopconfirmTitle="要删除选中的题目吗？"
                    (nzOnConfirm)="onDeleteQuestion(data.id)"
                  >
                  </app-btn-iconfont>
                </div>
              </ng-container>
            </td>
            <td *ngIf="isedited && listChecked !== 'checked'">
              <ng-container *ngIf="listChecked !== 'checked'">
                <div style="white-space: nowrap;">
                  <!-- 编辑 -->
                  <app-btn-iconfont
                    type="default"
                    iconType="edit"
                    theme="fill"
                    (btnclick)="onEditQuestion(data.id, data)"
                  ></app-btn-iconfont>
                  <!-- 删除 -->
                  <app-btn-iconfont
                    type="iconfont"
                    iconFont="icon-icon_delete"
                    [filled]="true"
                    nz-popconfirm
                    hoverColor="#F19672"
                    nzPopconfirmTitle="要删除选中的题目吗？"
                    (nzOnConfirm)="onDeleteQuestion(data.id)"
                  >
                  </app-btn-iconfont>
                </div>
              </ng-container>
            </td>
          </tr>
          <tr *ngIf="data.isPierceThrough">
            <td *ngIf="isedited && listChecked !== 'checked'"></td>
            <td></td>
            <td>
              {{
                data.resultQuestion.dimensionName &&
                data.resultQuestion.dimensionName[lan]
                  ? data.resultQuestion.dimensionName[lan]
                  : "--"
              }}
              <span *ngIf="data.resultQuestion.childCustomDimension"
                >/{{
                  data.resultQuestion.childCustomDimension && dimensionMap
                    ? dimensionMap[data.resultQuestion.childCustomDimension]
                        .name[lan]
                    : "--"
                }}</span
              >
            </td>
            <td>
              <ng-container *ngIf="data.isPierceThrough">
                <!-- <div class="cardFit card_bg7" style="display: inline-block;">
                    {{ lan === 'zh_CN' ? '穿透' : 'Penetration Question'}}
                  </div> -->
              </ng-container>
            </td>
            <td>
              <span>{{ data.resultQuestion.name[lan] }}</span>
              <span
                *ngIf="data.resultQuestion.isRevisionName"
                class="block"
                nz-tooltip
                nzTooltipTitle="已修订"
              ></span>
            </td>
            <td>
              <ng-container>
                <span *ngFor="let option of data.resultQuestion.options.options"
                  >{{ option.name[lan] }}/</span
                >
                <span
                  *ngIf="data.resultQuestion.isRevisionOption"
                  class="block"
                  nz-tooltip
                  nzTooltipTitle="已修订"
                ></span>
              </ng-container>
            </td>
            <ng-container
              *ngIf="
                standardReportType != 'BLANK_CUSTOM' &&
                currentReportType != 'BLANK_CUSTOM'
              "
            >
              <td *ngIf="isedited">
                <span>
                  {{
                    data.resultQuestion.type === "ESSAY_QUESTION" ||
                    data.resultQuestion.type ===
                      "MULTIPLE_CHOICE_ESSAY_QUESTION"
                      ? ""
                      : data.resultQuestion.proportion
                  }}</span
                >
              </td>
            </ng-container>

            <td>
              <ng-container>
                <span *ngIf="lan === 'zh_CN'">{{
                  data.resultQuestion.isRequire ? "必填" : "非必填"
                }}</span>
                <span *ngIf="lan !== 'zh_CN'">{{
                  data.resultQuestion.isRequire ? "Required" : "Not Required"
                }}</span>
              </ng-container>
            </td>
            <td *ngIf="!isedited && listChecked !== 'checked'">
              <ng-container
                *ngIf="
                  (data.resultQuestion.type == 'ESSAY_QUESTION' ||
                    data.resultQuestion.type ==
                      'MULTIPLE_CHOICE_ESSAY_QUESTION') &&
                  listChecked !== 'checked'
                "
              >
                <div>
                  <!-- 编辑 -->
                  <app-btn-iconfont
                    type="default"
                    iconType="edit"
                    theme="fill"
                    (btnclick)="onEditQuestion(data.resultQuestion.id, data)"
                  ></app-btn-iconfont>
                  <!-- 删除 -->
                  <app-btn-iconfont
                    type="iconfont"
                    iconFont="icon-icon_delete"
                    [filled]="true"
                    nz-popconfirm
                    hoverColor="#F19672"
                    nzPopconfirmTitle="要删除选中的题目吗？"
                    (nzOnConfirm)="onDeleteQuestion(data.resultQuestion.id)"
                  >
                  </app-btn-iconfont>
                </div>
              </ng-container>
            </td>
            <td *ngIf="isedited && listChecked !== 'checked'">
              <ng-container *ngIf="listChecked !== 'checked'">
                <div>
                  <!-- 编辑 -->
                  <app-btn-iconfont
                    type="default"
                    iconType="edit"
                    theme="fill"
                    (btnclick)="
                      onEditQuestion(data.resultQuestion.id, data, data)
                    "
                  ></app-btn-iconfont>
                  <!-- 删除 -->
                  <app-btn-iconfont
                    type="iconfont"
                    iconFont="icon-icon_delete"
                    [filled]="true"
                    nz-popconfirm
                    hoverColor="#F19672"
                    nzPopconfirmTitle="要删除选中的题目吗？"
                    (nzOnConfirm)="onDeleteQuestion(data.resultQuestion.id)"
                  >
                  </app-btn-iconfont>
                </div>
              </ng-container>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </nz-table>
  </div>

  <div class="fixed-box">
    <div class="content client-width fixed">
      <!-- <div class="score">SUM：9~74分</div> -->
      <button class="next_button" (click)="confirm()">确认</button>
    </div>
  </div>
</section>
<!-- <app-add-custom-book [isVisible]="isVisible"></app-add-custom-book> -->
