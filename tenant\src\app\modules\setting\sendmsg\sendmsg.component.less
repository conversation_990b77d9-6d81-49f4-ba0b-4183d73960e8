.card_ul {
    position: relative;
  padding: 30px 0;

  .big_title {
    color: #495970;
    font-size: 24px;
    padding-bottom: 15px;
  }

  .clear_tip {
    position: absolute;
    top: 40px;
    right: 0;
    color: #495970;
    font-size: 12px;
    cursor: pointer;
    &:hover{
        color: #1890ff;
    }
  }

  .search_ul {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;

    .search_put {
      width: 150px;
    }

    .search_sele {
      width: 150px;
    }

    .search_sele_1 {
      width: 250px;
    }
  }

  .tips_ul {
    display: flex;
    justify-content: space-between;
  }
}

.tips {
  // width: 100px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.code-tag {
  color: rgba(0, 0, 0, 0.65);
  font-size: 12px;
  margin-right: 4px;
}

.split_span {
  width: 200px;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 显示最多 3 行 */
  -webkit-box-orient: vertical; /* 垂直排列 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

:host ::ng-deep {
  .number_put {
    .ant-input-number-handler-wrap {
      display: none;
    }
  }
}
