.renderSetting{
  width: 710px;
  height: 480px;
  .header{
    height: 55px;
    width: 100%;
    box-shadow: 0px 3px 15px -2px #EBEBEB; 
    display: flex;
    justify-content: flex-start;
    align-items: center;
    h2{
      font-size: 18px;
      font-weight: 500;
      color: #17314C;
      padding-left: 24px;
    }
    >div{
      span{
        font-size: 14px;
        font-weight: 400;
        color: #495970;
        margin: 0 24px;
        cursor: pointer;
      }
    }
  }
  .content{
    width: 100%;
    height: 365px;
    overflow-y: auto;
    padding: 24px;
    .title{
      font-size: 14px;
      font-weight: 500;
      color: #495970;
    }
    .row{
      display: flex;
      align-items: center;
      margin: 10px 0;
      .required{
        color: #FF7575;
      }
      .rule{
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      // .group{
      //   >
      // }
    }
  }
  .footer{
    height: 60px;
    width: 100%;
    box-shadow: 0px -3px 15px -2px #EBEBEB; 
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 24px;
    .btns{
      span{
        padding: 5px 20px;
        border-radius: 15px;
        cursor: pointer;
        &:nth-child(1){
          color: #409EFF;
          background: #FFFFFF;
          border: 1px solid #409EFF;
        }
        &:nth-child(2){
          background: #409EFF;
          color: #FFFFFF;
          border: 1px solid #409EFF;
          margin-left: 15px;
        }
        &:hover{
          box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        }
      }
    }
  }
}
::ng-deep{
  .ant-divider-horizontal {
    margin: 4px 0;
  }
  .ant-input-affix-wrapper .ant-input:not(:first-child) {
    padding-left: 50px;
  }
}
.selectAdd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  padding: 8px;
  .addItem {
    font-size: 12px;
  }
  .addItemDisabled{
    cursor: no-drop;
    opacity: 0.5;
  }
}