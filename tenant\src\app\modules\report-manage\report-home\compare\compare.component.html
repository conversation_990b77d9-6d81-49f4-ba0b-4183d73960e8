<nz-drawer
  [(nzVisible)]="isVisible"
  [nzWidth]="'70%'"
  nzTitle="对比图表"
  nzWrapClassName="round-right-drawer-nofooter"
  (nzOnClose)="handleCancel()"
>
  <div class="compare">
    <div class="left">
      <div class="left_title">
        <span class="title" nz-tooltip [nzTooltipTitle]="projectName">{{
          projectName
        }}</span>
        <span class="type" nz-tooltip [nzTooltipTitle]="questionnaireType">{{
          questionnaireType
        }}</span>
      </div>
      <div style="box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);">
        <div class="name_table">
          <span
            *ngIf="parentDimensions.length != 0"
            [ngClass]="showclass ? 'table_left' : ''"
            (click)="getbigcode(true)"
            >大维度</span
          >
          <span
            *ngIf="childDimensions.length != 0"
            [ngClass]="showclass ? '' : 'table_left'"
            (click)="getbigcode(false)"
            >子维度</span
          >
        </div>
        <div class="left_checkbox scroll_com">
          <nz-checkbox-wrapper style="width: 100%;">
            <div
              *ngFor="let item of showcheckbox; let i = index"
              [ngClass]="i % 2 == 0 ? 'box_div_2' : 'box_div_1'"
            >
              <label
                nz-checkbox
                [nzValue]="item.dimensionName.zh_CN"
                [(ngModel)]="item.checked"
                (ngModelChange)="chooselog(item.dimensionName.zh_CN)"
                >{{ item.dimensionName.zh_CN }}</label
              >
            </div>
          </nz-checkbox-wrapper>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="right_title">
        <span
          [ngClass]="showtitle ? 'title_left' : ''"
          (click)="getcharts(true)"
          >柱状图</span
        >
        <span
          [ngClass]="showtitle ? '' : 'title_left'"
          (click)="getcharts(false)"
          style="margin-left: 30px;"
          >折线图</span
        >
      </div>
      <nz-checkbox-wrapper class="right_checkbox">
        <div
          *ngFor="let item of personlist; let i = index"
          style="display: flex;align-items: center;justify-content: space-between;padding: 0 10px;"
        >
          <span
            class="name_span"
            nz-tooltip
            [nzTooltipTitle]="item.name"
            nz-checkbox
            [nzValue]="item.id"
            [(ngModel)]="item.checked"
            (ngModelChange)="choosetitle(item.id, i)"
            >{{ item.name }}</span
          >
          <span class="cicle" [ngClass]="'color' + i"></span>
        </div>
      </nz-checkbox-wrapper>
      <div *ngIf="showcharts" class="lineChart">
        <div style="width: 100%;height: 500px;">
          <app-lineChart
            [type]="charttype"
            [dataX]="lineChartX"
            [dataY]="lineChartY"
            [chartcolor]="chartcolor"
            [containerWidth]="'100%'"
            [containerHeight]="'500'"
          >
          </app-lineChart>
        </div>
      </div>
    </div>
  </div>
</nz-drawer>
