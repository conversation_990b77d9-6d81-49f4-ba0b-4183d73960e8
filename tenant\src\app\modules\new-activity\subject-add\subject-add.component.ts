import { ThrowStmt } from "@angular/compiler";
import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import {
  NzCollapseComponent,
  NzMessageService,
  NzModalRef,
  NzModalService,
} from "ng-zorro-antd";
import _ from "lodash";
import { OpenSubjectComponent } from "../open-subject/open-subject.component";
import { AnimationMetadataType } from "@angular/animations";
import { elementAt } from "rxjs/operators";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-subject-add",
  templateUrl: "./subject-add.component.html",
  styleUrls: ["./subject-add.component.less"],
})
export class SubjectAddComponent implements OnInit {
  // 搜索关键字
  keyWord: string = "";

  // 活动id
  @Input() projectId: string;

  @Input() questionnaireId: string;

  topicList: any[] = [];

  dimensionList: any[] = [];

  currentDim: any;

  mockList: any[] = [];

  constructor(
    private modalService: NzModalService,
    private routeInfo: ActivatedRoute,
    private modalRef: NzModalRef,
    private msg: NzMessageService,
    private projSerivce: ProjectManageService,
    private surveySerivce: SurveyApiService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    // if (!this.projectId) {
    //   this.projectId = "1382538380081758210";
    // }

    this.surveySerivce
      .getBackupQuestionsByProjId(this.projectId)
      .subscribe((res) => {
        this.dimensionList = res.data;
        for (let index = 0; index < this.dimensionList.length; index++) {
          const element = this.dimensionList[index];
          element.id = index;
          element.isSelect = false;
          // 是否开放题维度
          element.isOpen = _.startsWith(element.dimensionCode, "KFSHD-prisma");
          if (!element.surveyQuestions) {
            element.surveyQuestions = [];
          }
          let qList: any[] = element.surveyQuestions;
          qList.forEach((item) => {
            item.isSelect = false;
          });
        }

        // select first
        if (this.dimensionList.length > 0) {
          this.selectDim(this.dimensionList[0]);
        }
      });
  }

  selectDim(dim: any) {
    this.currentDim = dim;
    dim.isSelect = true;
    this.topicList = dim.surveyQuestions;
    this.dimensionList.forEach((item) => {
      if (item.id !== dim.id) {
        item.isSelect = false;
      }
    });
  }

  selectAll() {
    this.topicList.forEach((item) => {
      item.isSelect = true;
    });
  }

  dimensionCount(): number {
    let dims: number = 0;
    for (let index = 0; index < this.dimensionList.length; index++) {
      const element = this.dimensionList[index];
      let tmpList: any[] = element.surveyQuestions;
      let countList: any[] = _.filter(tmpList, { isSelect: true });
      if (countList && countList.length > 0) {
        dims++;
      }
    }
    return dims;
  }

  search() {
    let tmpDim: any = _.find(this.dimensionList, { isSelect: true });
    let tmpList: any[] = tmpDim.surveyQuestions;

    let that = this;
    this.topicList = _.filter(tmpList, function(o) {
      let nameCn: string = o.name.zh_CN;
      let nameEn: string = o.name.en_US;
      return (
        nameCn.indexOf(that.keyWord) >= 0 || nameEn.indexOf(that.keyWord) >= 0
      );
    });
  }

  getSelectedQuestions() {
    let params: any[] = [];
    for (let index = 0; index < this.dimensionList.length; index++) {
      const element = this.dimensionList[index];
      let tmpList: any[] = element.surveyQuestions;
      let selList: any[] = _.filter(tmpList, { isSelect: true });
      params = _.concat(params, selList);
    }
    return params;
  }

  ok() {
    let selList: any[] = this.getSelectedQuestions();
    if (selList.length === 0) {
      // this.msg.error(`没有选择任何题目`);
      this.customMsg.open("error", "没有选择任何题目");
      return;
    }
    this.modalRef.triggerOk();
  }

  addOpenSubject() {
    const modal = this.modalService.create({
      nzTitle: `新增开放题`,
      nzFooter: null,
      nzWidth: 400,
      nzContent: OpenSubjectComponent,
      nzComponentParams: {},
      nzOkText: "保存",
      nzCancelText: "取消",
      nzMaskClosable: false,
      nzOnOk: () => {
        let child: OpenSubjectComponent = modal.getContentComponent();

        let newSubject: any = {
          dimensionCode: this.currentDim.dimensionCode,
          name: child.name,
          questionnaireId: this.questionnaireId,
          isSelect: false,
        };

        this.surveySerivce.addOpenQuestion(newSubject).subscribe((res) => {
          if (res.result.code === 0) {
            newSubject.id = res.data.id || res.data;
            this.currentDim.surveyQuestions.push(newSubject);
            modal.close();
          }
        });
        return false;
      },
    });
  }
}
