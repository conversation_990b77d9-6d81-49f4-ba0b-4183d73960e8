<div class="drag" dragula="VAMPIRESCOLUMN" [(dragulaModel)]="columnList">
    <div class="row" *ngFor="let item of columnList">
        <!-- <span class="index">{{item.id}}</span> -->
        <label nz-checkbox [nzDisabled]="item.attrName === 'personId'" [(ngModel)]="item.isSelected" (ngModelChange)="selectChange()"></label>
        <div class="name" >
            <span style="display: inline-block; margin-right: 10px;">{{item.title}} </span>
            <img src="./assets/images/org/col_drag.png ">
        </div>
    </div>
</div>
