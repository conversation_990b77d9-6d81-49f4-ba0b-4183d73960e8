import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { WrapperRegisterComponent } from "./wrapper-register.component";

describe("WrapperRegisterComponent", () => {
  let component: WrapperRegisterComponent;
  let fixture: ComponentFixture<WrapperRegisterComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [WrapperRegisterComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(WrapperRegisterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
