<div class="container content client-width">
  <div class="headContent">
    <div>
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </div>
    <h1>内外部常模</h1>
  </div>
  <div class="mainContent">
    <div class="header flex  justify-content-between">
      <div class="search">
        <nz-input-group [nzSuffix]="suffixIconSearch" style="height: 100%;">
          <input
            [(ngModel)]="name"
            (keydown.enter)="search()"
            type="text"
            nz-input
            placeholder="请搜索关键词"
          />
        </nz-input-group>
        <ng-template #suffixIconSearch>
          <i class="iconfont icon-search_o"></i>
        </ng-template>
      </div>
      <div class="operate flex">
        <div
          nz-popover
          [nzPopoverTitle]="remove"
          [(nzVisible)]="visibleTitle"
          nzPopoverTrigger="click"
          nzPopoverPlacement="bottom"
          [nzPopoverContent]="clearBtn"
          nzPopoverPlacement="bottomRight"
        >
          <i class="iconfont icon-icon-"></i>
          <span>清除数据</span>
        </div>
        <ng-template #remove>
          <div class="tooltip_div_clear">
            <i class="iconfont icon-inquire"></i
            ><span>清除后不可修复，确定吗？</span>
          </div>
        </ng-template>
        <ng-template #clearBtn>
          <div class="clearBtn">
            <button nz-button (click)="clear()" nzType="primary">确定</button>
          </div>
        </ng-template>
        <div (click)="exportR()">
          <i class="iconfont icon-export_ic"></i>
          <span>导出</span>
        </div>
        <div>
          <nz-upload [nzCustomRequest]="customReq" [nzShowUploadList]="false">
            <i class="iconfont icon-import"></i>
            <span>导入</span>
          </nz-upload>
        </div>
      </div>
    </div>
    <div class="tabList" *ngIf="tableList.length > 0">
      <div class="tablist_header" *ngIf="!is360Project">
       <div class="list_box">
         <span
          [ngClass]="{ active: activeIndex == i }"
          *ngFor="let item of tableList; let i = index"
          (click)="updateIndex(i)"
          >{{ item.name }}</span
        >
       </div>
      </div>
      <nz-table
        #fixedTable
        [nzData]="tableList[activeIndex].listOfData"
        [nzLoading]="nzLoading"
        [nzScroll]="{ x: '1200px' }"
        [nzFrontPagination]="false"
        nzBordered
      >
        <thead>
          <tr class="headerTr">
            <ng-container *ngFor="let item of tableList[activeIndex].list">
              <th
                *ngIf="!item.active"
                [nzWidth]="
                  (item.name.length < 4 ? 4 : item.name.length) * 14 +
                  32 +
                  (item.active || item.id == 'code' ? 36 : 0) +
                  'px'
                "
              >
                <span>{{ item.name }}</span>
              </th>
              <th
                nz-popover
                [(nzVisible)]="visible"
                [nzPopoverTitle]="edit"
                [nzPopoverContent]="editBtn"
                nzPopoverTrigger="click"
                nzPopoverPlacement="bottomLeft"
                *ngIf="item.active"
                [nzWidth]="
                  averageScoreName.zh_CN.length * 14 +
                  32 +
                  (item.active ? 28 : 0) +
                  'px'
                "
                (click)="editName()"
              >
                <span>{{ averageScoreName.zh_CN }}</span>
                <i class="iconfont icon-edit_ic"></i>
                <ng-template #edit>
                  <div class="tooltip_div">
                    <h5 class="title">编辑</h5>
                    <form
                      nz-form
                      [nzLayout]="validateForm.get('formLayout')?.value"
                      [formGroup]="validateForm"
                      (ngSubmit)="submitForm()"
                    >
                      <nz-form-item>
                        <nz-form-label [nzSpan]="6"
                          >标题名称 (中)</nz-form-label
                        >
                        <nz-form-control [nzSpan]="17" nzErrorTip="请输入">
                          <input
                            nz-input
                            formControlName="zh_CN"
                            maxlength="6"
                            placeholder="请输入"
                          />
                        </nz-form-control>
                      </nz-form-item>
                      <nz-form-item>
                        <nz-form-label [nzSpan]="6"
                          >标题名称 (ENG)</nz-form-label
                        >
                        <nz-form-control
                          [nzSpan]="17"
                          nzErrorTip="Please enter"
                        >
                          <input
                            nz-input
                            formControlName="en_US"
                            maxlength="20"
                            placeholder="Please enter"
                          />
                        </nz-form-control>
                      </nz-form-item>
                    </form>
                  </div>
                </ng-template>
                <ng-template #editBtn>
                  <div class="editBtn">
                    <button nz-button nzType="default" (click)="clickMe(true)">
                      取消
                    </button>
                    <button nz-button nzType="primary" (click)="submitForm()">
                      确定
                    </button>
                  </div>
                </ng-template>
              </th>
            </ng-container>

            <th nzRight="0" nzWidth="60px" class="edit">操作</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let data of fixedTable.data; let i = index">
            <tr class="itemTr">
              <ng-container *ngFor="let item of tableList[activeIndex].list">
                <ng-container *ngIf="!item.inDetail">
                  <td
                    *ngIf="data[item.id] !== undefined"
                    [ngClass]="{ isEdit: item.isEdit && data.isEdit }"
                  >
                    <ng-container *ngIf="item.isEdit && data.isEdit; else text">
                    <input
                      autocomplete="off"
                      [id]="item.id + i"
                      style="width: 60px"
                      nz-input
                      nzOverlayClassName="numeric-input"
                      [ngModel]="data[item.id]"
                      (ngModelChange)="onChange($event, i, item)"
                      (blur)="onBlur(i, item)"
                    />
                  </ng-container>
                    <ng-template #text
                      ><span>{{ data[item.id] }}</span></ng-template
                    >
                  </td>
                </ng-container>
                <ng-container *ngIf="item.inDetail">
                  <td
                    *ngIf="data.detail[item.id] !== undefined"
                    [ngClass]="{ isEdit: item.isEdit && data.isEdit }"
                  >
                    <input
                      autocomplete="off"
                      *ngIf="item.isEdit && data.isEdit; else text"
                      [id]="item.id + i"
                      style="width: 60px"
                      nz-input
                      nzOverlayClassName="numeric-input"
                      [ngModel]="data.detail[item.id]"
                      (ngModelChange)="onChange($event, i, item)"
                      (blur)="onBlur(i, item)"
                    />
                    <ng-template #text
                      ><span>{{ data.detail[item.id] }}</span></ng-template
                    >
                  </td>
                </ng-container>
              </ng-container>
              <td nzRight="0" nzWidth="60px" class="edit" (click)="edit(i)">
                {{ data.isEdit ? "保存" : "编辑" }}
              </td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
    </div>
  </div>
  <footer
    class="normFooter"
    *ngIf="
      tableList.length > 0 && tableList[activeIndex].listOfData.length !== 0
    "
  >
    <nz-pagination
      [nzPageIndex]="page.current"
      [nzTotal]="page.total"
      nzShowSizeChanger
      [nzPageSizeOptions]="[10, 20, 50, 100, 200]"
      [nzPageSize]="page.size"
      (nzPageIndexChange)="pageIndexChange($event)"
      (nzPageSizeChange)="pageSizeChange($event)"
    ></nz-pagination>
    <span>共{{ page.total }}条</span>
  </footer>
</div>
