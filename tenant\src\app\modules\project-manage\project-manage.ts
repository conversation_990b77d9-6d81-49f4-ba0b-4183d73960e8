// 360类型项目人员列表接口数据
export interface SurveyInvestigatorPerson {
  isComplete: boolean; // 是否完成
  surveyInvestigator: SurveyInvestigator;
  surveyPeople: Array<SurveyPeople>;

  /**
   * 以下字段前端添加
   */
  progress?: string; // 完成进度
  expand?: boolean; // nzTable：当前子 table 展开状态
}

// 360类型项目人员邀请者信息
export interface SurveyInvestigator {
  createTime: string; // exp: "2020-12-18T14:42:04.707+08:00"
  email: string;
  id: string;
  isSelfEvaluation: boolean;
  name: string;
  phone: string;
  projectId: string;
  updateTime: string; // exp: "2020-12-18T14:42:04.707+08:00"
}

// 360类型项目参与测评人员信息
export interface SurveyPeople {
  answerStatus: AnswerStatus; // 答题状态
  createTime: string; // exp: "2020-12-18T14:42:04.642+08:00"
  defaultLanguage: string; // "ZH_CN"
  email: string;
  firstName: string;
  id: string;
  phone: string;
  projectId: string;
  status: InviteStatus; // 邀请状态
  updateTime: string; // exp: "2020-12-18T14:42:04.642+08:00"
}

// 答题状态：未邀请 / 未答题 / 答题中 / 已完成
export type AnswerStatus = 'NOT_INVITED' | 'WAITING_ANSWER' | 'ANSWERING' | 'ANSWERED';

// 邀请状态（发送邮件）：// 未发送 / 发送中 / 发送成功 / 发送失败
export type InviteStatus = 'NEW' | 'SENDING' | 'SENT' | 'FAIL';
