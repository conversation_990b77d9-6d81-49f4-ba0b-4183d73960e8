<div class="setType">
  <nz-tabset class="setType-menu" nzTabPosition="top" nzSize="large">
    <nz-tab
      *ngFor="let option of dropList"
      [nzTitle]="option.name.zh_CN"
      (nzClick)="clickMenu(option.id)"
    >
      <!-- 工具名称/报告类型/报告风格 -->
      <div
        nz-row
        nzGutter="24"
        class="setType-menu-top"
        *ngFor="let data of reportTypeData[nairId]"
      >
        <div nz-col class="gutter-row" nzSpan="6">
          <span>工具名称</span>
          <input
            nz-input
            placeholder="Basic usage"
            [(ngModel)]="data.name.zh_CN"
            [disabled]="true"
          />
        </div>
        <div nz-col class="gutter-row" nzSpan="9">
          <span>报告类型</span>
          <nz-select
            [(ngModel)]="data.standardReportTemplateId"
            (ngModelChange)="typeChange($event, data, true)"
            nzPlaceHolder="请选择"
            style="width: 284px;"
          >
            <ng-container
              *ngFor="
                let option of data.optionalStandardReportTemplateList;
                let i = index
              "
            >
              <nz-option
                nzValue="{{ option.id }}"
                nzLabel="{{ option.name.zh_CN }}"
                [nzCustomContent]="true"
              >
                <div nz-tooltip [nzTooltipTitle]="option.name.zh_CN">
                  {{ option.name.zh_CN }}
                </div>
              </nz-option>
            </ng-container>
          </nz-select>
        </div>
        <div nz-col class="gutter-row" nzSpan="9">
          <span>报告风格</span>
          <nz-select
            nzMode="tags"
            [(ngModel)]="data.styles"
            nzPlaceHolder="请选择"
            (ngModelChange)="reportStyleChange($event, data)"
            style="width: 284px;"
          >
            <ng-container *ngFor="let option of styleList; let i = index">
              <nz-option
                *ngIf="option.type === data.selectedType && !option.isGroup"
                [nzCustomContent]="true"
                [nzValue]="option.reportStyleEnum"
                [nzLabel]="option.name"
              >
                <div nz-tooltip [nzTooltipTitle]="option.name">
                  {{ option.name }}
                </div>
              </nz-option>
            </ng-container>
          </nz-select>
        </div>
      </div>
      <!-- 新增细分类型 -->
      <div class="setType-menu-add" *ngIf="reportType == 'TIP_NEW_2'">
        <a (click)="addtipdetails()"
          ><i nz-icon nzType="plus-circle" nzTheme="outline"></i>
          新增细分类型</a
        >
       <label nz-checkbox [(ngModel)]="isAnswerIncompleteGeneratePartReport" >
        当作答不完整时，以组合中完整填答的工具数据生成合并TIP报告
       </label>
      </div>
      <div class="setType-menu-items">
        <ng-container *ngFor="let data of reportTypeData[nairId]">
          <!-- 个人报告 -->
          <ng-container *ngIf="data.selectModules">
            <div *ngFor="let item of data.selectModules">
              <div
                *ngIf="!item.reportStyle.includes('ONE_PAGE')"
                class="model-card"
              >
                <div class="model-card-title">
                  <p>报告模块-{{ item.reportStyleName }}</p>
                  <ng-container *ngIf="includes360or270Fun(reportType)">
                    <i
                      nz-icon
                      (click)="
                        handUnfold(
                          item.questionnaireId,
                          item.reportStyle,
                          item.reportStyle
                        )
                      "
                      [nzType]="
                        displayActives.includes(
                          item.questionnaireId + '&&&' + item.reportStyle
                        )
                          ? 'up-circle'
                          : 'down-circle'
                      "
                      nzTheme="outline"
                    ></i>
                  </ng-container>
                  <ng-container *ngIf="isShowSetting">
                    <i nz-icon (click)="showUnfold()
                                        " [nzType]="
                                          !isShowUnfold
                                            ? 'up-circle'
                                            : 'down-circle'
                                        " nzTheme="outline"></i>
                  </ng-container>
                </div>
                <nz-checkbox-wrapper
                  style="width: 100%;"
                  (nzOnChange)="log($event, item)"
                >
                  <div nz-row class="model-card-row">
                    <div
                      nz-col
                      nzSpan="3"
                      *ngFor="let em of item.selectInfos"
                      class="model-card-row-col"
                    >
                      <ng-container *ngIf="!!em.thumbnailUrl; else elseTemp">
                        <label
                          nz-checkbox
                          [nzValue]="em.moduleType"
                          [ngModel]="em.isSelect"
                          nz-tooltip
                          [nzTooltipTitle]="titleTemplate"
                          nzTooltipPlacement="topLeft"
                          [nzDisabled]="isDisabled"
                          >{{ em.moduleName }}</label
                        >
                        <ng-template #titleTemplate>
                          <img
                            [attr.src]="em.thumbnailUrl"
                            class="thumbnail-img"
                          />
                        </ng-template>
                      </ng-container>
                      <ng-template #elseTemp>
                        <label
                          nz-checkbox
                          [nzValue]="em.moduleType"
                          [ngModel]="em.isSelect"
                          [nzDisabled]="isDisabled"
                          >{{ em.moduleName }}</label
                        >
                      </ng-template>
                    </div>
                  </div>
                </nz-checkbox-wrapper>
                <ng-container *ngIf="includes360or270Fun(reportType)">
                  <div
                    class="model-card-extra"
                    *ngIf="
                      displayActives.includes(
                        item.questionnaireId + '&&&' + item.reportStyle
                      )
                    "
                  >
                    <ng-container *ngFor="let display of displaySetting">
                      <nz-descriptions
                        nzTitle="显示设置"
                        [nzColumn]="2"
                        [nzColon]="false"
                        *ngIf="display.reportStyle == item.reportStyle"
                      >
                        <nz-descriptions-item
                          nzTitle="他评得分最高和最低的维度"
                        >
                          <nz-select
                            [(ngModel)]="display.dimensionNum"
                            nzPlaceHolder="请选择"
                            nzAllowClear
                            style="width: 70px;"
                          >
                            <nz-option
                              *ngFor="let option of selectOptions1"
                              [nzLabel]="option"
                              [nzValue]="option"
                            ></nz-option>
                          </nz-select>
                        </nz-descriptions-item>
                        <nz-descriptions-item
                          nzTitle="他评分歧差异最大的题目"
                          style="margin-bottom: 0;"
                        >
                          <nz-select
                            [(ngModel)]="display.questionRoleDeviationNum"
                            nzPlaceHolder="请选择"
                            nzAllowClear
                            style="width: 70px;"
                          >
                            <nz-option
                              *ngFor="let option of selectOptions2"
                              [nzLabel]="option"
                              [nzValue]="option"
                            ></nz-option>
                          </nz-select>
                        </nz-descriptions-item>
                        <nz-descriptions-item
                          nzTitle="他评得分最高和最低的题目"
                        >
                          <nz-select
                            [(ngModel)]="display.questionNum"
                            nzPlaceHolder="请选择"
                            nzAllowClear
                            style="width: 70px;"
                          >
                            <nz-option
                              *ngFor="let option of selectOptions2"
                              [nzLabel]="option"
                              [nzValue]="option"
                            ></nz-option>
                          </nz-select>
                        </nz-descriptions-item>
                        <nz-descriptions-item
                          nzTitle="他评与自评差异最大的维度"
                          *ngIf="!includes270Fun(reportType)"
                        >
                          <nz-select
                            [(ngModel)]="display.dimensionDeviationNum"
                            nzPlaceHolder="请选择"
                            nzAllowClear
                            style="width: 70px;"
                          >
                            <nz-option
                              *ngFor="let option of selectOptions1"
                              [nzLabel]="option"
                              [nzValue]="option"
                            ></nz-option>
                          </nz-select>
                        </nz-descriptions-item>
                        <nz-descriptions-item
                          nzTitle="他评分析差异最大和最小的维度"
                          *ngIf="
                            !includesSpecialType(
                              reportType,
                              display.reportStyle
                            )
                          "
                          style="margin-bottom: 0;"
                        >
                          <nz-select
                            [(ngModel)]="display.dimensionRoleDeviationNum"
                            nzPlaceHolder="请选择"
                            nzAllowClear
                            style="width: 70px;"
                          >
                            <nz-option
                              *ngFor="let option of selectOptions1"
                              [nzLabel]="option"
                              [nzValue]="option"
                            ></nz-option>
                          </nz-select>
                        </nz-descriptions-item>
                        <nz-descriptions-item
                          nzTitle="他评与自评差异最大的题目"
                          *ngIf="!includes270Fun(reportType)"
                        >
                          <nz-select
                            [(ngModel)]="display.questionDeviationNum"
                            nzPlaceHolder="请选择"
                            nzAllowClear
                            style="width: 70px;"
                          >
                            <nz-option
                              *ngFor="let option of selectOptions2"
                              [nzLabel]="option"
                              [nzValue]="option"
                            ></nz-option>
                          </nz-select>
                        </nz-descriptions-item>
                      </nz-descriptions>
                    </ng-container>
                  </div>
                </ng-container>
                <!-- 多选题展示规则 -->
                <ng-container *ngIf="isShowSetting && isShowUnfold">
                  <div class="model-card-extra">
                    <ng-container >
                      <nz-descriptions nzTitle="显示设置" [nzColumn]="2" [nzColon]="false" >
                        <nz-descriptions-item nzTitle="多选题展示规则">
                        <nz-checkbox-wrapper (nzOnChange)="optionalMultiOpenFrequencyCalRule($event)">
                          <label *ngFor="let item of multiOpenFrequencyCalRuleList; let i = index" nz-checkbox [nzValue]="item.value"
                            [(ngModel)]="item.checked">
                            {{ item.name }}
                          </label>
                        </nz-checkbox-wrapper>
                        </nz-descriptions-item>
                      </nz-descriptions>
                    </ng-container>
                  </div>
                </ng-container>
              </div>
            </div>
          </ng-container>
          <!-- 团队报告 -->
          <ng-container *ngIf="data.selectGroupModule">
            <div *ngIf="data.selectGroupModule.selectInfos.length">
              <div class="model-card">
                <div class="model-card-title">
                  <p>报告模块-{{ data.selectGroupModule.reportStyleName }}</p>
                  <ng-container *ngIf="includes360or270Fun(reportType)">
                    <i
                      nz-icon
                      (click)="
                        handUnfold(
                          data.selectGroupModule.questionnaireId,
                          data.selectModules[0].reportStyle,
                          data.selectGroupModule.reportStyle
                        )
                      "
                      [nzType]="
                        displayActives.includes(
                          data.selectGroupModule.questionnaireId +
                            '&&&' +
                            data.selectGroupModule.reportStyle
                        )
                          ? 'up-circle'
                          : 'down-circle'
                      "
                      nzTheme="outline"
                    ></i>
                  </ng-container>
                </div>
                <nz-checkbox-wrapper
                  style="width: 100%;"
                  (nzOnChange)="log($event, data.selectGroupModule)"
                >
                  <div nz-row class="model-card-row">
                    <div
                      nz-col
                      nzSpan="3"
                      *ngFor="let em of data.selectGroupModule.selectInfos"
                      class="model-card-row-col"
                    >
                      <label
                        nz-checkbox
                        [nzValue]="em.moduleType"
                        [ngModel]="em.isSelect"
                        [nzDisabled]="isDisabled"
                        >{{ em.moduleName }}</label
                      >
                    </div>
                  </div>
                </nz-checkbox-wrapper>
                <ng-container *ngIf="includes360or270Fun(reportType)">
                  <div
                    class="model-card-extra"
                    *ngIf="
                      displayActives.includes(
                        data.selectGroupModule.questionnaireId +
                          '&&&' +
                          data.selectGroupModule.reportStyle
                      )
                    "
                  >
                    <ng-container *ngFor="let display of displaySetting">
                      <nz-descriptions
                        nzTitle="显示设置"
                        [nzColumn]="2"
                        [nzColon]="false"
                        *ngIf="
                          display.reportStyle ==
                          data.selectGroupModule.reportStyle
                        "
                      >
                        <nz-descriptions-item
                          nzTitle="他评得分最高和最低的题目"
                        >
                          <nz-select
                            [(ngModel)]="display.questionNum"
                            nzPlaceHolder="请选择"
                            nzAllowClear
                            style="width: 70px;"
                          >
                            <nz-option
                              *ngFor="let option of selectOptions2"
                              [nzLabel]="option"
                              [nzValue]="option"
                            ></nz-option>
                          </nz-select>
                        </nz-descriptions-item>
                        <nz-descriptions-item
                          nzTitle="他评与自评差异最大的题目"
                          *ngIf="!includes270Fun(reportType)"
                        >
                          <nz-select
                            [(ngModel)]="display.questionDeviationNum"
                            nzPlaceHolder="请选择"
                            nzAllowClear
                            style="width: 70px;"
                          >
                            <nz-option
                              *ngFor="let option of selectOptions2"
                              [nzLabel]="option"
                              [nzValue]="option"
                            ></nz-option>
                          </nz-select>
                        </nz-descriptions-item>
                        <nz-descriptions-item
                          nzTitle="他评分歧差异最大的题目"
                          style="margin-bottom: 0;"
                        >
                          <nz-select
                            [(ngModel)]="display.questionRoleDeviationNum"
                            nzPlaceHolder="请选择"
                            nzAllowClear
                            style="width: 70px;"
                          >
                            <nz-option
                              *ngFor="let option of selectOptions2"
                              [nzLabel]="option"
                              [nzValue]="option"
                            ></nz-option>
                          </nz-select>
                        </nz-descriptions-item>
                      </nz-descriptions>
                    </ng-container>
                  </div>
                </ng-container>
              </div>
            </div>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="reportType == 'TIP_NEW_2'">
          <div class="setType-menu-items-empty" *ngIf="!tipdetaillist.length">
            <app-empty text="暂无细分类型"></app-empty>
          </div>
          <div class="model-table" *ngIf="tipdetaillist.length">
            <nz-table
              #editRowTable
              nzBordered
              [nzData]="tipdetaillist"
              nzSize="middle"
              [nzShowPagination]="false"
            >
              <thead>
                <tr>
                  <th nzWidth="30%">报告类型</th>
                  <th nzWidth="33%">报告风格</th>
                  <th nzWidth="30%">产品类型</th>
                  <th nzAlign="center" nzWidth="7%">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of editRowTable.data">
                  <td>
                    <nz-select
                      nzShowSearch
                      nzAllowClear
                      [(ngModel)]="data.standardReportTemplateId"
                      (ngModelChange)="
                        typeChangenew($event, ReportTemplateList, data, true)
                      "
                      nzPlaceHolder="请选择"
                      style="width: 100%;"
                    >
                      <ng-container
                        *ngFor="let option of ReportTemplateList; let i = index"
                      >
                        <nz-option
                          nzValue="{{ option.standardReportTemplateId }}"
                          nzLabel="{{ option.standardReportTemplateName }}"
                          [nzCustomContent]="true"
                        >
                          <div
                            nz-tooltip
                            [nzTooltipTitle]="option.standardReportTemplateName"
                          >
                            {{ option.standardReportTemplateName }}
                          </div>
                        </nz-option>
                      </ng-container>
                    </nz-select>
                  </td>
                  <td>
                    <nz-select
                      nzShowSearch
                      nzAllowClear
                      nzMode="tags"
                      [(ngModel)]="data.styleEnums"
                      nzPlaceHolder="请选择"
                      style="width: 100%;"
                    >
                      <ng-container
                        *ngFor="let option of styleList; let i = index"
                      >
                        <nz-option
                          *ngIf="
                            option.type === data.standardReportTypeEnum &&
                            !option.isGroup
                          "
                          [nzCustomContent]="true"
                          [nzValue]="option.reportStyleEnum"
                          [nzLabel]="option.name"
                        >
                          <div nz-tooltip [nzTooltipTitle]="option.name">
                            {{ option.name }}
                          </div>
                        </nz-option>
                      </ng-container>
                    </nz-select>
                  </td>
                  <td>
                    <nz-select
                      nzShowSearch
                      nzAllowClear
                      [(ngModel)]="data.productType"
                      nzPlaceHolder="请选择"
                      style="width: 100%;"
                    >
                      <ng-container
                        *ngFor="let option of TreeByType; let i = index"
                      >
                        <nz-option
                          [nzValue]="option.name"
                          [nzLabel]="option.name"
                          [nzCustomContent]="true"
                        >
                          <div nz-tooltip [nzTooltipTitle]="option.name">
                            {{ option.name }}
                          </div>
                        </nz-option>
                      </ng-container>
                    </nz-select>
                  </td>
                  <td nzAlign="center">
                    <a
                      class="danger"
                      (click)="
                        deletedetail(data.id, data.standardReportTemplateId)
                      "
                      >删除</a
                    >
                  </td>
                </tr>
              </tbody>
            </nz-table>
          </div>
        </ng-container>
      </div>
    </nz-tab>
  </nz-tabset>
  <div class="setType-footer">
    <button
      nz-button
      nzType="default"
      nz-popconfirm
      nzPopconfirmTitle="更改的数据将会被删除，确认恢复默认?"
      nzPopconfirmPlacement="top"
      (nzOnConfirm)="reset()"
    >
      恢复默认
    </button>
    <button
      nz-button
      nzType="primary"
      [nzLoading]="isUpdating"
      (click)="saveSetting()"
    >
      保存设置
    </button>
  </div>
</div>
