import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";

@Component({
  selector: 'app-userinfo',
  templateUrl: './userinfo.component.html',
  styleUrls: ['./userinfo.component.less']
})
export class UserinfoComponent implements OnInit {
  @Input() content: any;
  @Input() userInfoVisible: boolean;
  @Input() cancelTxt: null | '取消';
  @Input() confirmTxt: null | '确定';
  @Output('checked') checkedBack = new EventEmitter<any>();
  id: any = 'now';

  constructor() { }

  ngOnInit() {
  }
  
  handleOk(): void {
    this.checkedCallback();
  }

  handleCancel(): void {
    this.checkedCallback();
  }

  checkedCallback() {
    this.checkedBack.emit(this.id);
  }

}
