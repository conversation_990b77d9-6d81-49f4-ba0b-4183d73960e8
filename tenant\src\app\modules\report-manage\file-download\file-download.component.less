.title {
    font-size: 24px;
    font-family: PingFangSC-Light, <PERSON>Fang SC;
    font-weight: 300;
    color: #17314C;
    line-height: 33px;
    margin-bottom: 30px;

    display: flex;
    align-items: center;

    span {
        margin-left: 20px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #AAAAAA;
        line-height: 17px;
    }
}

.cat {
    margin: 30px 0;

    &>span {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #495970;
        line-height: 20px;
    }

    .items {
        margin-top: 15px;
        margin-left: 15px;
    }
}

.footer {
    border-top: solid 1px #E6E6E6;
    padding-top: 20px;

    .iptBtn {
        width: 128px;
        height: 38px;
        background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
        box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        font-weight: 500;
        color: #FFFFFF;
    }
}

:host ::ng-deep {
    .ant-checkbox-wrapper:first-child {
        margin-right: 50px;
    }
}