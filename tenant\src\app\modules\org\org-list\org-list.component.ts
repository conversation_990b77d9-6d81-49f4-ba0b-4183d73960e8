import { HttpEvent } from "@angular/common/http";
import { Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import {
  NzMessageService,
  NzModalService,
  UploadFile,
  UploadXHRArgs,
} from "ng-zorro-antd";
import { OrgEditComponent } from "../org-edit/org-edit.component";
import { OrgService } from "../org.service";
import { OrgChartsComponent } from "../org-charts/org-charts.component";
import { NewPrismaService } from "@src/modules/new-prisma/new-prisma.service";
import _ from "lodash";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-org-list",
  templateUrl: "./org-list.component.html",
  styleUrls: ["./org-list.component.less"],
})
export class OrgListComponent implements OnInit {
  @ViewChild("datepicker", { static: false }) datepicker: OrgChartsComponent;

  listType: string = "list";
  orgList: any[] = [];
  parentList: any[] = [];

  // 活动id
  projectId: string;
  projectname: string;
  projectCode: string;
  tenantName:string;
  projectType: string;
  // 搜索关键字
  keyWord: string = "";
  // 分页控制
  totalCount: number = 1;
  currentPage: number = 1;
  pageSize: number = 10;
  typeshow = false;
  Breadcrumbs = [];

  lan = "zh_CN";
  i18n = [];
  queryParams = {};
  permission: boolean;
  constructor(
    private orgSerivce: OrgService,
    private surveySerivce: SurveyApiService,
    private apiPrisma: NewPrismaService,
    private modalService: NzModalService,
    private routeInfo: ActivatedRoute,
    private router: Router,
    private msg: NzMessageService,
    public permissionService: PermissionService,
  ) {}

  ngOnInit() {
    this.projectId = this.routeInfo.snapshot.queryParams.projectId;
    this.projectname = this.routeInfo.snapshot.queryParams.name;
    this.projectType = this.routeInfo.snapshot.queryParams.projectType;
    this.projectCode = this.routeInfo.snapshot.queryParams.projectCode;
    this.Breadcrumbs = JSON.parse(localStorage.getItem("break"));
    this.queryParams = this.routeInfo.snapshot.queryParams;
    this.permission = this.permissionService.isPermission();

    this.Breadcrumbs.forEach((item) => {
      if (item.Highlight) {
        item.path = "/new-prisma";
        item.Highlight = false;
      }
    });
    this.Breadcrumbs.push({
      path: "",
      name: "组织架构",
      Highlight: true,
    });

    if (
      this.projectType == "OVER" ||
      this.projectType == "SUSPEND" ||
      this.projectType == "ANSWERING"
    ) {
      this.typeshow = true;
    }
    this.loadData();
    this.loadParentList();
    this.getLanOptions();
    // 获取sessionStorage中的用户信息
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
    this.tenantName = userInfo.data.name;

  }

  choosePageType(type: string) {
    this.listType = type;
  }

  orgConfirm() {
    let data = {
      projectId: this.projectId,
      // questionnaireId: this.questionnaireId,
      type: "ORGANIZATION",
      isConfirmed: true,
    };
    this.surveySerivce.confirmRelation(data).subscribe((res) => {
      if (res.result.code === 0) {
        localStorage.setItem("backurl", this.router.routerState.snapshot.url);
        this.router.navigate(["/new-prisma"], {
          queryParams: { projectId: this.projectId, type: this.projectType },
        });
      }
    });
  }

  loadData() {
    this.orgList = [];
    this.orgSerivce
      .getOrgList(this.projectId, this.currentPage, this.pageSize, this.keyWord)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.orgList = res.data;
          this.orgList.forEach((item) => {
            item.lodaing = false;
          });
          this.totalCount = res.page.total;
        }
      });
      this.surveySerivce.getProjectById(this.projectId).subscribe((res) => {
        if (res.result.code === 0) {
          this.projectCode = res.data.code;
        }
      });
  }

  search() {
    this.currentPage = 1;
    this.loadData();
  }

  loadParentList() {
    this.orgSerivce.getParentList(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        // this.orgList = res.data;
        this.parentList = res.data;
      }
    });
  }

  add(code: string) {
    let tmpModel = undefined;
    let passModel: any = {};

    let tmpTitle = "新增";
    let childParams: any = {
      id: "0",
      orgModel: passModel,
      parentList: this.parentList,
      typeshow: this.typeshow,
      i18n: this.i18n,
    };
    if (code) childParams.fatherCode = code;
    const modal = this.modalService.create({
      // nzTitle: `${tmpTitle}组织`,
      nzTitle: null,
      nzFooter: null,
      nzWidth: 560,
      nzContent: OrgEditComponent,
      nzComponentParams: childParams,
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child = modal.getContentComponent();
        child.submitForm();
        if (child.validateForm.status === "INVALID") {
          return false;
        } else {
          let orgModel = child.orgModel;

          let param: any = JSON.parse(JSON.stringify(orgModel));
          // return
          param.projectId = this.projectId;
          // param.name = {
          //   zh_CN: param.name,
          //   en_US: param.nameEn,
          // }
          // delete param.nameEn;
          let sub = this.orgSerivce.updateOrg(param);
          delete param.id;
          sub = this.orgSerivce.createOrg(param);

          sub.subscribe((res) => {
            if (res.result.code === 0) {
              this.msg.success("更新成功");
              this.loadData();
              this.loadParentList();
              this.datepicker.loadParentList();
              modal.close();
            }
          });
          return false;
        }
      },
    });
  }

  edit(id: string) {
    let tmpModel = undefined;
    for (let index = 0; index < this.orgList.length; index++) {
      const element = this.orgList[index];
      if (element.id === id) {
        tmpModel = element;
        break;
      }
    }

    let passModel: any = {};

    let tmpTitle = "新增";
    if (id !== "0") {
      tmpTitle = "修改";
      passModel = {
        id: tmpModel.id,
        name: tmpModel.name,
        isVirtual: tmpModel.isVirtual,
        code: tmpModel.code,
        distance: tmpModel.distance,
        description: tmpModel.description,
        isUpdateParentOrganization: tmpModel.isUpdateParentOrganization,
        statusNew: tmpModel.status == "ENABLE",
      };
      let parent = tmpModel.parentOrganization;

      if (parent) {
        passModel.parentCode = parent.code;
      }
    }
    let childParams: any = {
      id: id,
      orgModel: passModel,
      parentList: this.parentList,
      typeshow: this.typeshow,
      i18n: this.i18n,
    };

    const modal = this.modalService.create({
      // nzTitle: `${tmpTitle}组织`,
      nzTitle: null,
      nzFooter: null,
      nzWidth: 560,
      nzContent: OrgEditComponent,
      nzComponentParams: childParams,
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child = modal.getContentComponent();
        child.submitForm();
        if (child.validateForm.status === "INVALID") {
          return false;
        } else {
          let orgModel = child.orgModel;

          let param: any = JSON.parse(JSON.stringify(orgModel));

          // return
          param.projectId = this.projectId;
          // param.name = {
          //   zh_CN: param.name,
          //   en_US: param.nameEn,
          // }
          // delete param.nameEn;
          let sub = this.orgSerivce.updateOrg(param);
          if (id === "0") {
            delete param.id;
            sub = this.orgSerivce.createOrg(param);
          }

          sub.subscribe((res) => {
            if (res.result.code === 0) {
              this.msg.success("更新成功");
              this.loadData();
              this.loadParentList();
              modal.close();
            }
          });
          return false;
        }
      },
    });
  }
  editTree(data: any) {
    let tmpTitle = "修改";
    let childParams: any = {
      id: data.passModel.id,
      orgModel: data.passModel,
      parentList: data.parentList,
      typeshow: this.typeshow,
      i18n: this.i18n,
    };
    const modal = this.modalService.create({
      // nzTitle: `${tmpTitle}组织`,
      nzTitle: null,
      nzFooter: null,
      nzWidth: 560,
      nzContent: OrgEditComponent,
      nzComponentParams: childParams,
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child = modal.getContentComponent();
        child.submitForm();
        if (child.validateForm.status === "INVALID") {
          return false;
        } else {
          let orgModel = child.orgModel;
          let param: any = JSON.parse(JSON.stringify(orgModel));

          param.projectId = this.projectId;
          // param.name = {
          //   zh_CN: param.name,
          //   en_US: param.nameEn,
          // }
          // delete param.nameEn;
          let sub = this.orgSerivce.updateOrg(param);

          sub.subscribe((res) => {
            if (res.result.code === 0) {
              this.msg.success("更新成功");
              this.loadData();
              this.datepicker.loadParentList();
              modal.close();
            }
          });
          return false;
        }
      },
    });
  }

  delete(id: string) {
    this.orgSerivce.deleteOrg(id).subscribe((res) => {
      if (res.result.code === 0) {
        if (this.orgList.length == 1) {
          this.currentPage = 1;
        }
        this.loadData();
        this.loadParentList();
      }
    });
  }

  fileList: UploadFile[] = [];

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.orgSerivce.uploadFile(formData, this.projectId).subscribe(
      (event: HttpEvent<any>) => {
        item.onSuccess!();
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入组织成功");
          this.loadData();
          this.loadParentList();
          if (this.listType === "tree") {
            this.datepicker.loadParentList();
          }
        }
        // if (res.data && res.data[0][0]) {
        //   this.msg.error("Excel 模板格式错误");
        //   item.onError!(item.file!);
        // }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  // exportExcel 导出模板表格
  exportExcel() {
    this.orgSerivce.exportExcel(this.projectId).subscribe(
      (data) => {
        var blob = new Blob([data], { type: "application/vnd.ms-excel" });
        let tempa = document.createElement("a");
        tempa.href = URL.createObjectURL(blob);
        let imageName = "";
        let prefix = this.tenantName + "_";
        if (this.projectCode) { 
          prefix = prefix + this.projectCode +":"+this.projectname+"_";
        }
        imageName = prefix+"组织架构配置";
        let timestamp = new Date().getTime();
        imageName = imageName + "_" + timestamp;
        tempa.setAttribute("download", imageName + ".xlsx");
        document.body.append(tempa);
        tempa.click();
        tempa.remove();
      },
      (error) => {}
    );
  }

  // 导出组织列表
  exportList(projectId: any) {
    this.orgSerivce.exportList(projectId).subscribe(
      (data) => {
        var blob = new Blob([data], { type: "application/vnd.ms-excel" });
        let tempa = document.createElement("a");
        tempa.href = URL.createObjectURL(blob);
        let imageName = "";
        let prefix = this.tenantName + "_";
        if (this.projectCode) { 
          prefix = prefix + this.projectCode +":"+this.projectname+"_";
        }
        imageName = prefix+"组织架构配置";
        let timestamp = new Date().getTime();
        imageName = imageName + "_" + timestamp;
        tempa.setAttribute("download", imageName + ".xlsx");
        document.body.append(tempa);
        tempa.click();
        tempa.remove();
      },
      (error) => {}
    );
  }

  // 清除组织列表
  clearOrganization(projectId: any) {
    this.orgSerivce.clearOrganization(projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("清除成功");
        this.currentPage = 1;
        this.loadData();
        this.loadParentList();
        if (this.listType === "tree") {
          this.datepicker.loadParentList();
        }
      }
    });
  }

  onSelectI18n(e) {
    this.lan = e;
    if (this.listType !== "list") {
      // 树形模式下切换标题
      this.datepicker.loadParentList();
    }
  }

  async getLanOptions() {
    const currentLansRes = await this.apiPrisma.getLanguages().toPromise();
    const defaultCode = ["zh_CN", "en_US"];
    const projectLanguages =
      JSON.parse(sessionStorage.getItem("projectLanguages")) || defaultCode;
    this.i18n = currentLansRes.data
      .filter((val) => projectLanguages.includes(val.value))
      .sort((a, b) => {
        if (a.value === "zh_CN") return -1;
        if (b.value === "zh_CN") return 1;
        if (a.value === "en_US") return -1;
        if (b.value === "en_US") return 1;
        if (a.value === "jp") return -1;
        if (b.value === "jp") return 1;
        if (a.value === "ko") return -1;
        if (b.value === "ko") return 1;
        if (a.value === "cs_1") return -1;
        if (b.value === "cs_1") return 1;
        if (a.value === "cs_2") return -1;
        if (b.value === "cs_2") return 1;
        if (a.value === "cs_3") return -1;
        if (b.value === "cs_3") return 1;
        return 0;
      });
    // this.lan = projectLanguages[0] || 'zh_CN'
  }
}
