<div style="background:#F5F6FA;height: 100%;">
    <div class="container client-width">
        <div class="history-box">
            <header class="flex report-header space-between">
                <p class="index-title">历史对比（调研）</p>
                <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
            </header>
            <div class="tbody">
                <div>
                    <button nz-button nzType="primary" (click)="edit()" style="margin-bottom: 20px;">新增</button>
                    <a style="margin-left: 20px;" (click)="exportMore()">下载校验报表</a>
                </div>
                <div style="margin-bottom: 12px;">
                    <app-i18n-select [active]="lan" (selectChange)="onSelectI18n($event)" [isDefault]="true"></app-i18n-select>
                </div>
                <nz-table #basicTable [nzData]="historicalList" [nzScroll]="{ y: '480px' }" [nzFrontPagination]="false" [nzBordered]="true" [nzSize]="'middle'">
                    <thead>
                        <tr>
                            <th nzWidth="3.5%" nzShowCheckbox [(nzChecked)]="isAllDisplayDataChecked" [nzIndeterminate]="isIndeterminate" (nzCheckedChange)="checkAll($event)"></th>
                            <th nzWidth="11%" [nzAlign]="'left'">历史数据名称</th>
                            <th nzWidth="5%"  [nzAlign]="'left'">状态</th>
                            <th nzWidth="11%" [nzAlign]="'left'">对比活动</th>
                            <th nzWidth="21%" [nzAlign]="'left'">备注</th>
                            <th nzWidth="27%" [nzAlign]="'left'">一致性</th>
                            <th nzWidth="22%" [nzAlign]="'left'">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let data of basicTable.data, let index = index">
                            <td nzShowCheckbox [(nzChecked)]="data.checked" (nzCheckedChange)="refreshStatus($event)"></td>
                            <td nzBreakWord  >{{data.name[lan]}}</td>
                            <td >
                                <nz-switch nzSize="small" [ngModel]="(data.status !== 'DISABLE')" (ngModelChange)="switchHistory($event, data.id)"></nz-switch>
                            </td>
                            <td nzBreakWord >{{data.relativeProjectName.zh_CN}}</td>
                            <td  ><input type="text" nz-input placeholder="备注" [(ngModel)]="data.memo" (blur)="updateHis(data)" (keydown.enter)="updateHis()" /></td>
                            <td nzBreakWord>题本：{{data.questionMatchRate}}% 组织：{{data.organizationMatchRate}}% 人口标签：{{data.demographicMatchRate}}% <br/>{{data.dimensionMatchRate?'维度：'+data.dimensionMatchRate+'%':''}}</td>
                            <td >
                                <!-- <nz-upload [nzCustomRequest]="customReq" [nzShowUploadList]="false" >
              <a (click)=uploadId(data.id)>上传</a>
            </nz-upload> -->
                                <a (click)="edit(data.id)">校验数据</a>
                                <a (click)="export(data.id)">下载校验报表</a>
                                <a class="iconfont icon-icon_delete" nz-popconfirm nzPopconfirmTitle="删除后不可修复，确定吗？" (nzOnConfirm)="deleteHis(data.id)">
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
        </div>

        <footer>
            <button class="btn" (click)="back()">确认</button>
        </footer>
    </div>

</div>