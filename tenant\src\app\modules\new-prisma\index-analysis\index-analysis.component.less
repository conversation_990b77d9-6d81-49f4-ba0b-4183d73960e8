:root {
  --ifm-scrollbar-size: 7px;
  --ifm-scrollbar-track-background-color: #f1f1f1;
  --ifm-scrollbar-thumb-background-color: silver;
  --ifm-scrollbar-thumb-hover-background-color: #a7a7a7;
}
.close-box {
  text-align: right;
  padding: 9px;

  .icon-penetra-close {
    display: inline-block;
    width: 12px;
    height: 12px;
    font-size: 12px;
    cursor: pointer;
  }
}

.box {
  // padding: 30px;
  padding-top: 0;
  max-height: 610px;
  position: relative;
}
.group {
  width: 575px;
  height: 488px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 4px rgba(0, 0, 0, 0.04);
  padding: 20px;
  position: absolute;
  top: 40px;
  // right: 0;
  left: 0;
  z-index: 100;

  .gHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .gTitle {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #17314c;
      line-height: 25px;
      a {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #409eff;
        line-height: 20px;
        margin-left: 22px;
      }
    }
  }

  .gContent {
    height: 390px;
    overflow-y: auto;
  }

  .gAction {
    width: 100%;
    height: 38px;
    line-height: 38px;
    text-align: center;
  }
}
header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  .title-name {
    font-size: 24px;
    font-family: PingFangSC-Light, PingFang SC;
    font-weight: 300;
    color: #495970;
    line-height: 33px;
  }
  > div {
    display: flex;
    .ant-btn-link {
      padding: 0;
      margin-left: 24px;
    }
  }
  // .btn1 {
  //   width: 72px;
  //   height: 30px;
  //   border-radius: 15px;
  //   border: 1px solid #409eff;
  //   font-size: 14px;
  //   font-family: PingFangSC-Medium, PingFang SC;
  //   font-weight: 500;
  //   color: #409eff;
  //   line-height: 20px;
  // }

  // .btn2 {
  //   width: 115px;
  //   height: 30px;
  //   background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
  //   box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
  //   border-radius: 15px;
  //   font-size: 16px;
  //   font-family: PingFangSC-Medium, PingFang SC;
  //   font-weight: 500;
  //   color: #FFFFFF;
  //   line-height: 22px;
  //   border: none;
  // }
  .btn4 {
    color: #409eff;
    border-color: #409eff;
    border-radius: 6px;
  }
  .btn3 {
    color: #409eff;
    border-radius: 6px;
    background-color: #f5f8ff;
    border-color: #f5f8ff;
    box-shadow: none;
    text-shadow: none;
  }
}

.form-title {
  // display: inline-block;
  padding: 0 0 8px;
  height: 29px;
  text-align: right;

  > span {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    line-height: 20px;
  }

  a {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #409eff;
    line-height: 20px;
  }
}

.ant-form-item {
  margin-bottom: 0;
}

.virtual-tab {
  // 虚拟tab
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e6e6e6;

  ul {
    border-right: 1px solid #e6e6e6;
    height: 320px;

    li {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 146px;
      height: 48px;
      margin-top: 21px;
      border: 2px solid #ffffff;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #17314c;
      line-height: 20px;
    }

    li.active {
      background: rgba(64, 158, 255, 0.05);
      border-left: 2px solid #409eff;
    }
  }

  > div {
    flex: 1;
  }

  .tab-body_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    width: 100%;
    height: 53px;
    box-sizing: border-box;
    border-bottom: 1px solid #e6e6e6;
  }

  .tab-body_content {
    height: 266px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .tab-body_content::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  .tab-body_content::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    box-shadow: none;
  }
  .tab-body_content::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

.title {
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: #17314c;
  line-height: 20px;
}

.content {
  width: 100%;
  // height: 340px;
  height: calc(100vh - 300px);
  display: flex;
  justify-content: flex-start;

  border-radius: 4px;
  border: 1px solid #e6e6e6;
  .list {
    flex: 1;
    // min-height: 400px;
    border-right: 1px solid #e6e6e6;

    .listItem {
      // max-height: 268px;
      height: calc(100vh - 370px);
    }
  }
}
.treeScroll {
  overflow-y: auto;
  overflow-x: auto;
}

footer {
  border-top: 1px solid #e6e6e6;
  padding-top: 20px;
  margin-top: 20px;

  .btn_cancel {
    width: 128px;
    height: 38px;
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 22px;
  }

  .btn_confirm {
    width: 128px;
    height: 38px;
    background: #fafafa;
    border-radius: 19px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #aaaaaa;
    line-height: 22px;
  }
}

.second_lab {
  color: #409eff;
  cursor: pointer;
  position: relative;
  z-index: 99;

  .tipscard {
    width: 582px;
    height: 474px;
    position: absolute !important;
    top: 25px;
    right: -10px;
    z-index: 99;
    background-color: #ffffff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    padding: 20px;

    .card-title {
      text-align: left;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #495970;
      line-height: 24px;
      border-bottom: 1px solid #ececec;
      padding-bottom: 16px;
      margin-bottom: 10px;
    }

    .list_ul {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      li {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .table_ul {
      li {
        display: flex;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #17314c !important;
        line-height: 20px;

        div {
          text-align: left;

          &:nth-child(1) {
            flex: 1;
          }

          &:nth-child(2) {
            flex: 4;
            padding: 0 12px;
          }
        }
      }

      .line_div {
        height: 240px;
        overflow-y: auto;
      }

      .line_list {
        padding: 10px 10px 10px 0;
        display: flex;
        align-items: center;

        div {
          &:nth-child(1) {
            display: flex;
            align-items: center;
          }
        }
        .del-icon {
          width: 18px;
          height: 18px;
          background-size: 100% 100% !important;
          background: url("../../../../assets/images/org/del.png") no-repeat;
          cursor: pointer;
        }
        .del-icon:hover {
          background: url("../../../../assets/images/org/del_hover.png")
            no-repeat;
        }
      }

      .li_footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 20px;
        padding: 20px 0;
        border-top: 1px solid #ececec;

        .btn_cancel {
          width: 69px;
          height: 30px;
          background: #ffffff;
          border-radius: 15px;
          color: #409eff;
          border: 1px solid #409eff;
        }

        .btn_confirm {
          width: 69px;
          height: 30px;
          background: #409eff;
          border-radius: 15px;
          color: #ffffff;
          border: 1px solid #409eff;
        }
      }
    }
  }
}

.del_icon,
.edit_icon_close {
  width: 18px;
  height: 18px;
  background-size: 100% 100% !important;
  margin: 0 5px;
}

.del_icon {
  background: url("../../../../assets/images/org/del.png") no-repeat;
}
.del_icon:hover {
  background: url("../../../../assets/images/org/del_hover.png") no-repeat;
}
.edit_icon_close {
  background: url("../../../../assets/images/org/edit_bg.png") no-repeat;
}
.edit_icon_close:hover {
  background: url("../../../../assets/images/org/edit_bg_hover.png") no-repeat;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  box-shadow: none;
}
::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
