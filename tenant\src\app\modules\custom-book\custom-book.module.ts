import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { CustomBookComponent } from "./custom-book.component";
import { Routes, RouterModule } from "@angular/router";
import { SharedModule } from "../../shared/shared.module";
import { AddCustomBookComponent } from "./add-custom-book/add-custom-book.component";
import { AnswerSettingComponent } from "./answer-setting/answer-setting.component";
import { AlgorithmSelectComponent } from "./algorithm-select/algorithm-select.component";

const routes: Routes = [
  {
    path: "",
    component: CustomBookComponent,
    children: [],
  },
];

@NgModule({
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
  exports: [RouterModule],
  declarations: [
    CustomBookComponent,
    AddCustomBookComponent,
    AnswerSettingComponent,
    AlgorithmSelectComponent,
  ],
  entryComponents: [
    CustomBookComponent,
    AddCustomBookComponent,
    AnswerSettingComponent,
    AlgorithmSelectComponent,
  ],
})
export class CustomBookModule {}
