import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@shared';

import { Routes, RouterModule } from '@angular/router';
import { SettingComponent } from "@src/modules/setting/setting.component";
import { UpdatePasswordComponent } from './update-password/update-password.component';
import { DocumentComponent } from './document/document.component';
import { SendmsgComponent } from './sendmsg/sendmsg.component';
import { SetPermissionModalComponent } from './set-permission-modal/set-permission-modal.component';

const routes: Routes = [
    { path: '', component: SettingComponent, children:[
      { path: 'update', component: UpdatePasswordComponent, data: { title: '修改密码', titleI18n: '修改密码' } },
      ],
        data:{permissionCode: "SAG:TENANT:USER_CENTER"}
    },
    { path: 'document', component: DocumentComponent, data: { title: '文档中心', titleI18n: '文档中心' ,permissionCode: "SAG:TENANT:DOC_MGT"} },
    { path: 'sendmsg', component: SendmsgComponent, data: { title: '日志中心', titleI18n: '日志中心' ,permissionCode: "SAG:TENANT:MSG_SEND_LOG"} },
    
];

@NgModule({
  declarations: [SettingComponent, UpdatePasswordComponent, DocumentComponent, SendmsgComponent, SetPermissionModalComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule, SetPermissionModalComponent],
  entryComponents: [SetPermissionModalComponent]
})
export class SettingModule { }
