# See http://help.github.com/ignore-files/ for more about ignoring files.

# Compiled output
dist/
out-tsc/
*.tmp

# Dependencies
node_modules/

# IDEs and editors
.idea/
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.project
.classpath
.settings/
*.sublime-workspace

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
testem.log
libpeerconnection.log

# Coverage
coverage/

# System Files
.DS_Store
Thumbs.db

# Angular specific
.cache/
.angular/

# Editor directories and files
.history/
*.sw?
