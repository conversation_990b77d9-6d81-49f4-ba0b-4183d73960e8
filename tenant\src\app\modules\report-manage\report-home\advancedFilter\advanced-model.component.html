<div class="box">
  <div class="filter-title" (click)="showFilterBox(true)">
    <span [class]="FilterBoxVisiable ? 'text-blue' : ''">高级筛选</span>
    <span [class]="FilterBoxVisiable ? 'arrow arrow-open' : 'arrow'">
      <svg
        viewBox="64 64 896 896"
        fill="currentColor"
        width="1em"
        height="1em"
        class="ng-tns-c12-3"
        data-icon="down"
        aria-hidden="true"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        ></path>
      </svg>
    </span>
  </div>
  <nz-drawer
    [nzClosable]="true"
    [nzVisible]="FilterBoxVisiable"
    nzPlacement="right"
    nzTitle="高级筛选"
    [nzWidth]="isMin ? 450 : 580"
    (nzOnClose)="showFilterBox(false)"
    nzWrapClassName="advanced-model-drawer"
  >
    <div class="filter_box">
      <header>
        <span
          >已选择 <a>{{ list.length }}</a> 个条件筛选</span
        >
        <button
          nz-button
          nzType="link"
          (click)="addField($event)"
        >
          <i nz-icon nzType="plus" nzTheme="outline"></i>
          添加条件
        </button>
      </header>

      <div class="condition_box">
        <span>满足下列</span>
        <nz-select [(ngModel)]="match" [nzSize]="'small'" class="condition">
          <nz-option nzValue="MATCH_ANY" nzLabel="任一"></nz-option>
          <nz-option nzValue="MATCH_ALL" nzLabel="所有"></nz-option>
        </nz-select>
        <span>条件</span>
      </div>

      <div class="content">
        <!-- 暂无数据 -->
        <div class="empty" *ngIf="list.length === 0">
          <img src="assets/images/filter-empty.png" alt="" />
          <p>暂无筛选条件，请先点击右上角的添加条件～</p>
        </div>
        <div class="option-box" *ngIf="list.length > 0">
          <div
            nz-row
            nzGutter="8"
            *ngFor="let data of list; let index = index"
            style="margin-bottom: 16px;"
          >
            <!-- 筛选类型 -->
            <div nz-col nzSpan="6">
              <nz-select
                [(ngModel)]="data.eventName"
                [class]="
                  !errorFlag ? '' : data.eventName ? '' : ' border-error'
                "
                (ngModelChange)="optionChange(data.eventName, index)"
                nzPlaceHolder="请选择"
              >
                <ng-container
                  *ngFor="let option of advancedFilterObject.optionName"
                >
                  <nz-option
                    [nzValue]="option.value"
                    [nzLabel]="option.name"
                    *ngIf="option.isShow"
                  ></nz-option>
                </ng-container>
              </nz-select>
            </div>
            <!-- 包含关系&导出 -->
            <div nz-col nzSpan="6">
              <ng-container *ngIf="!data.eventName">
                <div class="option_empty">
                  <i nz-icon nzType="left" nzTheme="outline"></i>请选择
                </div>
              </ng-container>
              <ng-container *ngIf="data.eventName === 'name' || data.eventName === 'orgOrPerson'">
                <button
                  nz-button
                  nzType="primary"
                  nzGhost
                  (click)="downloadTemplate($event)"
                  style="width: 100%;"
                >
                  下载模板
                </button></ng-container
              >
              <ng-container
                *ngIf="!!data.eventName && data.eventName !== 'name' && data.eventName !== 'orgOrPerson'"
              >
                <nz-select
                  [(ngModel)]="data.judge"
                  [class]="!errorFlag ? '' : data.judge ? '' : ' border-error'"
                  nzPlaceHolder="请选择类型"
                  *ngIf="data.eventName !== 'generalCommentScore'"
                >
                  <nz-option
                    *ngFor="let option of advancedFilterObject.condition"
                    [nzValue]="option.value"
                    [nzLabel]="option.name"
                  ></nz-option>
                </nz-select>

                <nz-select
                  [(ngModel)]="data.judge"
                  [class]="!errorFlag ? '' : data.judge ? '' : ' border-error'"
                  nzPlaceHolder="请选择类型"
                  *ngIf="data.eventName === 'generalCommentScore'"
                >
                  <nz-option nzValue="GT" nzLabel="大于"></nz-option>
                  <nz-option nzValue="EQ" nzLabel="等于"></nz-option>
                  <nz-option nzValue="LT" nzLabel="小于"></nz-option>
                </nz-select>
              </ng-container>
            </div>
            <div nz-col [nzSpan]="isMin ? 10 : 11">
              <ng-container *ngIf="!data.eventName">
                <div class="option_empty">
                  <i nz-icon nzType="left" nzTheme="outline"></i>请选择
                </div>
              </ng-container>
              <ng-container *ngIf="data.eventName === 'name' || data.eventName === 'orgOrPerson'">
                <div class="upload_button">
                  <nz-upload
                    [nzShowUploadList]="false"
                    [nzCustomRequest]="customReq"
                    [nzAccept]="fileType"
                    [nzBeforeUpload]="beforeUploadProduct"
                  >
                    <button
                      [ngStyle]="{
                        'border-color': !errorFlag
                          ? ''
                          : !data.name
                          ? 'red'
                          : ''
                      }"
                      class=""
                      nz-button
                      nzType="primary"
                      nzGhost
                      style="width: 100%;"
                    >
                      <span>{{ uploadName }}</span>
                    </button>
                  </nz-upload>
                </div>
              </ng-container>
              <ng-container
                *ngIf="!!data.eventName && data.eventName !== 'name' && data.eventName !== 'orgOrPerson'"
              >
                <nz-select
                  *ngIf="data.eventName === 'questionnaireType'"
                  [(ngModel)]="data.name"
                  class="upload_button"
                  [ngStyle]="{
                    border: !errorFlag ? '' : !data.name ? '1px solid red' : ''
                  }"
                  nzSize="default"
                  nzMode="multiple"
                  nzPlaceHolder="请选择"
                  style="width: 100%;"
                >
                  <nz-option
                    *ngFor="let option of advancedFilterObject.product"
                    [nzValue]="option.name"
                    [nzLabel]="option.name"
                  ></nz-option>
                </nz-select>
                <input
                  *ngIf="
                    data.eventName !== 'questionnaireType' &&
                    data.eventName !== 'generalCommentScore'
                  "
                  class="upload_button"
                  [ngStyle]="{
                    'border-color': !errorFlag ? '' : !data.name ? 'red' : ''
                  }"
                  nz-input
                  placeholder="请输入"
                  [(ngModel)]="data.name"/>
                <input
                  class="upload_button"
                  [ngStyle]="{
                    'border-color': !errorFlag ? '' : !data.name ? 'red' : ''
                  }"
                  *ngIf="data.eventName === 'generalCommentScore'"
                  nz-input
                  [(ngModel)]="data.name"
                  placeholder="请输入数字"
                  (ngModelChange)="onChange($event, index)"
              /></ng-container>
            </div>
            <div nz-col [nzSpan]="isMin ? 2 : 1" style="text-align: center;">
              <i
                class="option_del iconfont icon-icon_delete"
                (click)="removeField(index, data.eventName, $event)"
              ></i>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <button
          nz-button
          nzType="default"
          (click)="clear()"
        >
          清空
        </button>
        <button
          nz-button
          nzType="primary"
          (click)="submitForm()"
        >
          确认
        </button>
      </footer>
    </div>
  </nz-drawer>
</div>
