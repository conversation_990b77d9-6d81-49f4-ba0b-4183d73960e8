.container {
  margin: 0px auto;
  color: #17314c;
}

.warp {
  position: relative;
  padding-bottom: 80px;
}

.title-warp {
  display: flex;
  justify-content: space-between;
  height: 86px;
  align-items: center;
  > div:first-child {
    display: flex;
    align-items: center;
  }
  .title-handle {
    // background: #fff !important;
    // border-radius: 8px;
    display: flex;
    margin-left: 30px;
    .upload_button {
      margin-right: 10px;
    }
    a {
      display: flex;
      align-items: center;
    }
  }
  .title {
    font-size: 24px;
    color: #17314c;
    line-height: 33px;
    font-weight: 100;
  }

  .progress {
    display: inline-block;
    margin-left: 56px;

    .step1 {
      font-size: 12px;
      margin-right: 15px;
    }

    .step2 {
      font-size: 12px;
      color: #a0a0a0;
      margin-left: 15px;
    }
  }

  .search {
    width: 418px;
    height: 38px;
    background: #ffffff;
    border-radius: 38px;
    border: 1px solid #e6e6e6;

    .ant-input {
      border-radius: 38px;
      border: none;
    }
  }
}

.sub-title-warp {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .add_button {
    cursor: pointer;
    width: 130px;
    height: 65px;
    background: url("../../../../assets/images/invite_button.png") no-repeat;
    background-position: center center;
    background-color: transparent;
    border-style: none;
    border-radius: 55px;
    padding-left: 18px;
    padding-bottom: 8px;
    color: #ffffff;
    font-size: 16px;
    margin-right: 17px;
  }

  .upload_button {
    width: 123px;
    height: 38px;
    font-size: 16px;
    font-weight: 500;
    color: #419eff;
    line-height: 38px;
    padding-left: 30px;
    margin: 0 10px;
    background: url("../../../../assets/images/upload_bg.png") no-repeat;
    border: 0;
  }
}

.observer {
  border-radius: 10px;
  border: 1px solid #e6e6e6;
  margin-bottom: 20px;

  .header {
    padding: 0 25px;
    display: flex;
    justify-content: space-between;
    height: 69px;
    align-items: center;
  }

  .btn-add-observer {
    width: 95px;
    height: 30px;
    border-radius: 15px;
    border: 1px solid #409eff;
    font-size: 14px;
    font-weight: 500;
    color: #409eff;
    line-height: 20px;
    background-color: #ffffff;
    cursor: pointer;
    margin-left: 25px;
    margin-right: 76px;
  }

  .label-ovserver {
    font-size: 14px;
    font-weight: 400;
    color: #495970;
    margin-right: 78px;

    &:before {
      content: "";
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #daecff;
      border-radius: 5px;
      margin-right: 10px;
    }
  }

  .label-reviewer {
    font-size: 14px;
    font-weight: 400;
    color: #495970;

    &:before {
      content: "";
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #ffffff;
      border-radius: 5px;
      border: 1px solid #cacaca;
      margin-right: 10px;
    }
  }
}
.nested-table {
  // .name-col {
  //   padding-right: 19px !important;
  // }
  // .email-col,
  // .phone-col,
  // .role-col,
  // .weight-col {
  //   padding: 0 20px 0 30px;
  // }
}
.name-col {
  width: 130px;
  input {
    width: 100%;
  }
}
.email-col {
  width: 140px;
}
.phone-col {
  width: 170px;
}
.role-col {
  width: 175px;
  nz-select {
    width: 100% !important;
  }
}
.weight-col {
  width: 170px;
}
.self-col {
  width: 170px;
}
.list-title {
  font-size: 20px;
  color: #495970;
  font-weight: 500;
}

.btn-delete {
  color: #f19672;
  cursor: pointer;
  white-space: nowrap;
}

.tr-parent {
  background-color: #daecff;

  nz-select {
    display: block;
  }

  input {
    height: 30px;
    padding: 0 5px;
    background-color: #daecff;
    border: 1px solid #daecff;
  }

  // :host ::ng-deep {
  .ant-select-selection {
    height: 30px;
    padding: 0 5px;
    background-color: #daecff;
    border: 1px solid #daecff;
  }
  // }
  input {
    height: 30px;
    padding: 0 5px;
    background-color: #ffffff;
    border: 1px solid #d3d3d3;
  }

  :host ::ng-deep {
    .ant-select-selection {
      height: 30px;
      padding: 0 5px;
      background-color: #ffffff;
      border: 1px solid #d3d3d3;
    }
  }
}

.tr-child {
  background-color: #ffffff;

  nz-select {
    display: block;
    width: 125px;
  }

  input,
  .ant-select-selection {
    height: 30px;
    padding: 0 5px;
    border: 1px solid #ffffff;
  }
  input,
  .ant-select-selection {
    height: 30px;
    padding: 0 5px;
    border: 1px solid #d3d3d3;
  }

  :host ::ng-deep {
    .ant-select-selection {
      height: 30px;
      padding: 0 5px;
      border: 1px solid #d3d3d3;
    }
  }
}

:host ::ng-deep {
  .ant-table-thead > tr > th {
    font-size: 12px;
    color: #aaaaaa;
    padding: 8px 25px;
  }
}

:host ::ng-deep {
  .ant-table-tbody > tr > td {
    padding: 15px 25px;
  }
}

:host ::ng-deep {
  .ant-select-selection {
    height: 30px;
    padding: 0 5px;
    border: 1px solid #ffffff;
  }
}
:host ::ng-deep {
  .ant-calendar-picker-input.ant-input {
    height: 48px;
    border-radius: 0;
  }
}
:host ::ng-deep {
  .ant-time-picker-input {
    height: 48px;
    border-radius: 0;
  }
}

.no-data-warp {
  border-radius: 10px;
  border: 1px solid #e6e6e6;
  text-align: center;
  font-size: 16px;
  color: #495970;
  padding: 46px;

  p {
    margin-top: 30px;
  }
}

.invite-footer {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  height: 70px;
  background: #ffffff;
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);
  text-align: right;
  padding-top: 16px;

  .next_button {
    cursor: pointer;
    width: 128px;
    height: 38px;
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
  }
}
.input-step {
  width: 80px;
  border: 1px solid #d3d3d3 !important;
  background-color: #ffffff !important;
}
.xy_invite {
  width: 100%;
  .ant-tabs {
    min-height: 650px;
    .xy_contant {
      background: #fff !important;
      border-radius: 8px;
      display: flex;
      .xy_letitle {
        min-height: 600px;
        display: flex;
        flex-direction: column;
        border-right: 1px solid #e6e6e6;
        .blueclass {
          background-color: #f5faff !important;
          color: #409eff;
          border-left: 1px solid #409eff;
        }
        .xy_li {
          width: 146px;
          text-align: center;
          line-height: 48px;
          background: #f9f9f9;
          margin-top: 20px;
          cursor: pointer;
          font-weight: 600;
        }
      }
      .xy_rititle {
        padding: 30px 20px;
        display: flex;
        width: 100%;
        position: relative;
        > div:first-child {
          max-width: 350px;
        }
        .xy_co_bottom {
          display: flex;
          align-items: center;
          width: 400px;
          justify-content: space-between;
          margin-top: 20px;
          .xy_local {
            color: #409eff;
            cursor: pointer;
          }
        }
        .xy_pers {
          .xy_ser_load {
            display: flex;
            align-items: center;
            margin-top: 20px;
            .xy_search {
              .sear_put {
                border-radius: 15px;
                background: #f8f8f8;
                border: none;
                width: 259px;
              }
            }
            .xy_load {
              width: 84px;
              height: 30px;
              line-height: 30px;
              border: 1px solid #409eff;
              color: #409eff;
              background: #fff;
              border-radius: 15px;
              text-align: center;
              cursor: pointer;
            }
          }
          .xy_two_sele {
            margin-top: 20px;
            .xy_select {
              width: 400px;
              border: 1px solid #eee;
            }
          }
        }
        .xy_code {
          margin-left: 80px;
          flex: 1;
          .xy_area_put {
            margin-top: 20px;
            width: 100%;
            border: 1px solid #eee;
            .xy_area {
              width: 100%;
              border: none;
            }
            .xy_btn {
              margin-top: 15px;
            }
          }
          .xy_int_mess {
            margin-top: 30px;
          }
          .xy_int_code {
            margin-top: 60px;
          }
          .xy_into {
            display: flex;
            margin-top: 20px;
            .into_img {
              margin-right: 30px;
            }
            .into_right {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              margin-left: 30px;
              .right_top {
                max-width: 276px;
                display: flex;
                align-items: center;
                margin-top: 10px;
                .top_img {
                  width: 30px;
                  height: 30px;
                }
                .top_text {
                  width: 186px;
                  height: 68px;
                  border: 1px solid #eee;
                  text-align: center;

                  margin: 0 15px;
                  .xy_name {
                    line-height: 68px;
                  }
                }
              }
              .right_bottom {
              }
            }
          }
          .code_name {
            margin-top: 20px;
            padding: 8px;
            width: 100%;
            height: 337px;
            background: #f9f9f9;
          }
          .email_content {
            // 邮件内容  动态字段样式
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            .email_dynamic {
              margin-left: 20px;
              .dynamic_li {
                font-size: 16px;
                padding: 0 5px;
                height: 28px;
                line-height: 28px;
                background-color: #409eff;
                color: #ebf5ff;
                text-align: center;
                margin-bottom: 10px;
                cursor: pointer;
              }
              .code_li {
                background-color: #fafafa;
                color: #aaaaaa;
                cursor: not-allowed; // #11774
              }
            }
          }
          .xy_menus {
            margin-top: 20px;
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: space-between;
            .menus_li {
              width: 16%;
              height: 28px;
              line-height: 28px;
              background-color: #409eff;
              color: #ebf5ff;
              text-align: center;
              cursor: pointer;
            }
            .code_li {
              background-color: #fafafa;
              color: #aaaaaa;
              cursor: not-allowed; // #11774
            }
          }
          .email-warp {
            display: flex;
            justify-content: space-between;
            .email {
              height: 287px;
              background: #ffffff;
              border-radius: 4px;
              border: 1px solid #e6e6e6;
              padding: 10px;
              overflow: auto;
            }
          }
          .email-left,
          .email-right {
            width: 45%;
            padding-right: 10px;
          }
          .textarea_email {
            height: 300px;
          }
          .title_email {
            // margin-top: 20px;
            // margin-bottom: 30px;
            margin: 16px 0; // #11774
          }
        }
      }
    }
  }
}
.label {
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(23, 49, 76, 1);
  line-height: 20px;
}
.file {
  position: absolute;
  right: 0;
  top: 0;
  width: 78px;
  height: 32px;
  opacity: 0;
}
.export-btn {
  padding: 0 10px;
  position: absolute;
  right: 0;
}
.invite-mid {
  display: flex;
  h3 {
    font-size: 14px;
    font-weight: 500;
  }
  li {
    flex: 1;
  }
  .invite-date {
    display: flex;
    .date {
      margin-right: 40px;
      // flex: 1;
    }
  }
  .invite-user {
    height: 48px;
    line-height: 48px;
  }
}
.code_name {
  margin-top: 20px;
  padding: 8px;
  width: 300px;
  height: 337px;
  background: #f9f9f9;
  overflow-y: auto;
}
.xy_demo_load {
  margin-top: 20px;
  color: #409eff;
}
.required {
  vertical-align: middle;
  white-space: nowrap;
  &::before {
    color: #FF4F40;
    content: "*";
    display: inline-block;
    margin-right: 6px;
  }
}
.choose {
  display: inline-block !important;
}
.firstClass {
  border: 1px solid #FF4F40 !important;
}
.emailClass {
  border: 1px solid #FF4F40 !important;
}
.roleIdClass {
  border: 1px solid #FF4F40 !important;
}
.invite-btm {
  h3 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 24px;
    display: inline-block;
  }
  p {
    font-size: 12px;
    color: #495970;
    font-weight: 400;
    margin-left: 22px;
    display: inline-block;
  }
  .invite-btm-title {
    margin: 50px 0 0;
  }
  .email-warp {
    display: flex;
    justify-content: space-between;
    .email {
      height: 287px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #e6e6e6;
      padding: 10px;
      overflow: auto;
    }
  }
  .email-left,
  .email-right {
    width: 45%;
    padding-right: 10px;
  }
  .textarea_email {
    height: 300px;
  }
  .title_email {
    // margin-top: 20px;
    // margin-bottom: 30px;
    margin: 16px 0; // #11774
  }
}

.top_ul {
  // padding-right: 40px;
  padding: 16px 56px 16px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .titile_add {
    display: flex;
    align-items: center;
    .left_p {
      font-size: 24px;
    }
    .right_p {
      font-size: 12px;
      margin-left: 20px;
    }
  }
  .pos_li {
    position: relative;
    cursor: pointer;
    .cur_div {
      line-height: 38px;
      position: absolute;
      top: 0;
      text-indent: 40px;
    }
  }
}
.body_contant {
  // margin-top: 30px;
  border-top: 1px solid #e6e6e6;
  display: flex;
  .left_body {
    border-right: 1px solid #e6e6e6;
    width: 335px;
    height: 100%;
    .little_div {
      color: #aaaaaa;
      .white_space {
        span {
          width: 210px;
          display: inline-block;
          overflow: hidden; /*超出部分隐藏*/
          white-space: nowrap; /*不换行*/
          text-overflow: ellipsis; /*超出部分文字以...显示*/
        }
      }
      .default {
        display: inline-block;
        width: 34px;
        line-height: 21px;
        text-align: center;
        background-color: #60cb7f;
        color: #fff;
        border-radius: 4px;
        margin-right: 15px;
      }
      .delete_icon {
        margin-left: 15px;
        color: #f8f8f8;
        background-color: #d0d0d0;
        border-radius: 50%;
        // font-size: 20px;
        padding: 4px;
        cursor: pointer;
        &:hover {
          color: #fff !important;
          background-color: #f19672 !important;
        }
      }
    }
    .add-template {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e6e6e6;
    }
    .template-list{
      height: calc(100vh - 186px);
    }
  }
  .right_body {
    flex: 1;
    padding: 20px;
    .title_top {
      display: flex;
      justify-content: space-between;
    }
    .xy_menus {
      margin-top: 20px;
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      .menus_li {
        width: 16%;
        height: 28px;
        line-height: 28px;
        background-color: #409eff;
        color: #ebf5ff;
        text-align: center;
        cursor: pointer;
      }
      .code_li {
        background-color: #fafafa;
        color: #aaaaaa;
        cursor: not-allowed; // #11774
      }
    }
  }
}

.put_div {
  width: 150px;
  height: 30px;
  background: #fff;
  border-radius: 15px;
  margin-left: 60px;
  input {
    border: none;
    border-radius: 15px;
  }
}

::ng-deep .upload-list-inline .ant-upload-list-item {
  float: left;
  width: 100px;
  margin-right: 8px;
}

::ng-deep {
  .custom-right-drawer-custom360 {
    .ant-drawer-body {
      padding: 0;
      height: calc(100% - 53px);
      overflow: hidden;
      // padding-bottom: 66px;
    }
    // .ant-drawer-header{
    //   padding: 16px;
    // }
    // .ant-drawer-title{
    //   font-weight: bold;
    // }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}

.no-invite-empty {
  background: #fff;
  border-radius: 8px;
  height: calc(100vh - 320px);
}

// #11774
.flex-between-center {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mb-10 {
  margin-bottom: 10px;
}
.mt-10 {
  margin-top: 10px;
}
.mb-16 {
  margin-bottom: 16px;
}
.mt-16 {
  margin-top: 16px;
}
.red-text {
  color: #FF4F40;
}
.red-text-right {
  color: #FF4F40;
  text-align: right;
}
