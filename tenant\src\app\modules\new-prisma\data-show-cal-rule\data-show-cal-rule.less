@primaryColor: #40a9ff;
@minorColor: #f5f8ff;
@delColor: #f19672;
@fgColor: #fff;
@pageBg: #e6e6e6;
@textColor_1: #17314c;
@textColor_2: #495970;
@textColor_3: #e6e6e6;
@textColor_4: #aaaaaa;
@textColor_5: #c4c4c4;
@textColor_6: #efefef;
@itemBgColor: #f5f6fa;
@tagBgColor: #f6f6f6;

:root {
  --ifm-scrollbar-size: 7px;
  --ifm-scrollbar-track-background-color: #f1f1f1;
  --ifm-scrollbar-thumb-background-color: silver;
  --ifm-scrollbar-thumb-hover-background-color: #a7a7a7;
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
// 滑块背景
::-webkit-scrollbar-track {
  // background-color: transparent;
  background-color: #F1F1F1;
  box-shadow: none;
}
// 滑块
::-webkit-scrollbar-thumb {
  // background-color: #e9e9e9;
  background-color: #C1C1C1;
  outline: none;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}
.rule-box {
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    height: 52px;
    .tip{
      font-size: 12px;
      color: #999;
    }
  }
  .options{
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .border-left{
    border-left: 1px solid #ECECEC ;
  }
}
.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}
