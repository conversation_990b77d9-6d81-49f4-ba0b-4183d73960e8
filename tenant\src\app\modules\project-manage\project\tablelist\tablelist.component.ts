import {
  Component,
  OnInit,
  Input,
  SimpleChanges,
  Output,
  EventEmitter,
} from "@angular/core";
import { NzMessageService, UploadXHRArgs, zh_CN, en_US } from "ng-zorro-antd";
import { ProjectManageService } from "../../../service/project-manage.service";

import { HttpClient, HttpEvent } from "@angular/common/http";
import { Observable } from "rxjs";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";
@Component({
  selector: "app-table-config",
  templateUrl: "./tablelist.component.html",
  styleUrls: ["./tablelist.component.less"],
})
export class tablelistconfig implements OnInit {
  permission; //更新租户配置权限
  tenantUrl: string = "/tenant-api";

  constructor(
    private http: ProjectManageService,
    private msg: NzMessageService,
    private httpClient: HttpClient,
    private customMsg: MessageService,
        public permissionService: PermissionService,
  ) {}

  @Input() projectModel;
  @Input() selectedname;
  @Input() nzSelectedIndex;
  @Input() surveyType = "";
  listOfData: any = [];
  listOfTagOptions = null;
  searchType = null;
  searchName = null;
  PageIndex = 1;
  PageSize = 10;
  Pagetotal = 1;
  nzLoading = false;
  language = "zh_CN"; //题本落地建议 语言类型
  // 维度解释
  desctitle = [
    {
      name: "序号",
      nzWidth: "65px",
      nzAlign: "center",
    },
    {
      name: "配置类型",

      nzWidth: "100px",
    },
    {
      name: "维度编号",
      nzWidth: "100px",
    },
    {
      name: "语言类型",
      Left: "0px",
      nzWidth: "100px",
    },
    {
      name: "维度名称",
      Left: "100px",
      nzWidth: "180px",
    },
    {
      name: "维度解释",
      nzWidth: "300px",
    },
    {
      name: "靠左描述",
      nzWidth: "300px",
    },
    {
      name: "靠左优势/偏好取向",
      nzWidth: "300px",
    },
    {
      name: "靠左劣势",
      nzWidth: "300px",
    },
    {
      name: "靠右描述",
      nzWidth: "300px",
    },
    {
      name: "靠右优势//偏好取向",
      nzWidth: "300px",
    },
    {
      name: "靠右劣势",
      nzWidth: "300px",
    },
    {
      name: "反思问题/工作环境",
      nzWidth: "300px",
    },
    {
      name: "推荐书籍/管理方式/职业建议/核心关键词",
      nzWidth: "300px",
    },
    {
      name: "面试问题/压力因素/潜在问题",
      nzWidth: "300px",
    },
    {
      name: "任务训练/优势/理想环境",
      nzWidth: "300px",
    },
    {
      name: "推荐课程/发展建议/沟通建议",
      nzWidth: "300px",
    },
    {
      name: "操作",
      nzWidth: "120px",
      Right: "0px",
      nzAlign: "center",
    },
  ];
  // 维度区间
  scoretitle = [
    {
      name: "序号",
      nzWidth: "65px",
      nzAlign: "center",
    },
    {
      name: "配置类型",

      nzWidth: "100px",
    },
    {
      name: "维度编号",

      nzWidth: "100px",
    },
    {
      name: "语言类型",
      Left: "0px",
      nzWidth: "100px",
    },
    {
      name: "维度名称",
      Left: "100px",
      nzWidth: "180px",
    },
    {
      name: "分段高分值",
      nzWidth: "300px",
    },
    {
      name: "分段低分值",
      nzWidth: "300px",
    },
    {
      name: "描述",
      nzWidth: "300px",
    },
    {
      name: "评语",
      nzWidth: "300px",
    },
    {
      name: "优势描述/评价要点（优势）",
      nzWidth: "300px",
    },
    {
      name: "发展描述/评价要点（风险）",
      nzWidth: "300px",
    },
    {
      name: "操作",
      nzWidth: "120px",
      Right: "0px",
      nzAlign: "center",
    },
  ];
  // 自定义
  customtitle = [
    {
      name: "序号",
      nzWidth: "65px",
      nzAlign: "center",
    },
    {
      name: "语言类型",
      nzWidth: "100px",
    },
    {
      name: "维度名称",
      Left: "0px",
      nzWidth: "100px",
    },
    {
      name: "维度说明",
      nzWidth: "300px",
    },
    {
      name: "反思问题",
      nzWidth: "300px",
    },
    {
      name: "推荐书籍/管理方式/职业建议/核心关键词",
      nzWidth: "300px",
    },
    {
      name: "推荐课程/发展建议/沟通建议",
      nzWidth: "300px",
    },
    {
      name: "操作",
      nzWidth: "120px",
      Right: "0px",
      nzAlign: "center",
    },
  ];
  // 报告自定义
  reportCustomizationTitle = [
    {
      name: "序号",
      nzWidth: "65px",
      nzAlign: "center",
      Left: "0px",
    },
    {
      name: "语言类型",
      nzWidth: "100px",
      Left: "65px",
    },
    {
      name: "报告风格",
      nzWidth: "140px",
      Left: "165px",
    },
    {
      name: "编码",
      nzWidth: "170px",
    },
    {
      name: "名称",
      nzWidth: "100px",
    },
    {
      name: "自定义名称",
      nzWidth: "200px",
    },
    {
      name: "描述",
      nzWidth: "300px",
    },
    {
      name: "操作",
      nzWidth: "120px",
      nzAlign: "center",
      Right: "0px",
    },
  ];
  // 题本落地建议
  proposaltle = [
    {
      name: "编码",
      nzWidth: "100px",
      Left: "0px",
    },
    {
      name: "题本",
      nzWidth: "300px",
      Left: "100px",
    },
    {
      name: "落地建议1",
      nzWidth: "300px",
    },
    {
      name: "落地建议2",
      nzWidth: "300px",
    },
    {
      name: "落地建议3",
      nzWidth: "300px",
    },
    {
      name: "落地建议4",
      nzWidth: "300px",
    },
    {
      name: "落地建议5",
      nzWidth: "300px",
    },
    {
      name: "落地建议6",
      nzWidth: "300px",
    },
    {
      name: "操作",
      nzWidth: "70px",
      Right: "0px",
      nzAlign: "center",
    },
  ];
  reportCodeName = {
    REFLECT_PROBLEM: {
      zh_CN: "反思问题",
      en_US: "Question and Reflection",
    },
    RECOMMENDED_BOOKS: {
      zh_CN: "推荐书籍",
      en_US: "Recommended Books",
    },
    RECOMMENDED_COURSES: {
      zh_CN: "推荐课程",
      en_US: "Recommended Courses",
    },
    TENCENT_RESEARCH_RAGE: {
      zh_CN: "腾讯调研范围",
      en_US: "Survey scope",
    },
    TENCENT_VALID_ANSWER_RAGE: {
      zh_CN: "腾讯有效填答率",
      en_US: "Effective response rate",
    },
    NET_EASE_VALID_ANSWER_RAGE: {
      zh_CN: "调研参与情况",
      en_US: "",
    },
    NET_EASE_FOUR_WINDOW_INSTRUCTIONS: {
      zh_CN: "网易四窗说明",
      en_US: "",
    },
    ON_LINE_REPORT_FOUR_WINDOW_INSTRUCTIONS_QUADRANT_1: {
      zh_CN: "四窗第一象限，坐标(+,+)",
      en_US: "First Quadrant (+,+)",
    },

    ON_LINE_REPORT_FOUR_WINDOW_INSTRUCTIONS_QUADRANT_2: {
      zh_CN: "四窗第二象限，坐标(−,+)",
      en_US: "Second Quadrant (−,+)",
    },

    ON_LINE_REPORT_FOUR_WINDOW_INSTRUCTIONS_QUADRANT_3: {
      zh_CN: "四窗第三象限，坐标(−,−)",
      en_US: "Third Quadrant (−,−)",
    },
    ON_LINE_REPORT_FOUR_WINDOW_INSTRUCTIONS_QUADRANT_4: {
      zh_CN: "四窗第四象限，坐标(+,-)",
      en_US: "Fourth Quadrant (+,-)",
    },
    VALIDITY_DESCRIPTION: {
      zh_CN: "填答有效性描述",
      en_US: "Answer validition description",
    },
    NOT_EVALUATION: {
      zh_CN: "无法评价",
      en_US: "Not Evaluation",
    },
    NOT_EVALUATION_SELF: {
      zh_CN: "无法评价(只有自评)",
      en_US: "Not Evaluation(Only Self)",
    },
    ANSWERS_SINGLE: {
      zh_CN: "填答单一",
      en_US: "Answers Single",
    },
    MISSING_ANSWERS: {
      zh_CN: "题目漏答",
      en_US: "Missing Answers",
    },
    LITTLE_TIME: {
      zh_CN: "用时过少",
      en_US: "Little Time",
    },
    DEMOGRAPHIC_STANDARD_JOB_FUNCTION_DESCRIPTION: {
      en_US: "采样说明-职能-描述",
      zh_CN: "采样说明-职能-描述"
    },
    DEMOGRAPHIC_STANDARD_JOB_LEVEL_DESCRIPTION: {
      en_US: "采样说明-职级-描述",
      zh_CN: "采样说明-职级-描述"
    },
    OPEN_QUESTION_DESCRIPTION: {
      en_US: "整体结果概览-开放题-描述",
      zh_CN: "整体结果概览-开放题-描述"
    },
    SELECT_OPEN_QUESTION_DESCRIPTION: {
      en_US: "整体结果概览-分发开放题-描述",
      zh_CN: "整体结果概览-分发开放题-描述"
    },
    MULTI_OPEN_QUESTION_DESCRIPTION: {
      en_US: "整体结果概览-多选开放题-描述",
      zh_CN: "整体结果概览-多选开放题-描述"
    },
    SELECT_MULTI_OPEN_QUESTION_DESCRIPTION: {
      en_US: "整体结果概览-分发多选开放题-描述",
      zh_CN: "整体结果概览-分发多选开放题-描述"
    },
    THROUGH_RESULT_MULTI_OPEN_QUESTION_DESCRIPTION: {
      en_US: "整体结果概览-追问多选开放题-描述",
      zh_CN: "整体结果概览-追问多选开放题-描述"
    },
    SELECT_THROUGH_RESULT_MULTI_OPEN_QUESTION_DESCRIPTION: {
      en_US: "整体结果概览-分发追问多选开放题-描述",
      zh_CN: "整体结果概览-分发追问多选开放题-描述"
    },
    THROUGH_RESULT_OPEN_QUESTION_DESCRIPTION: {
      en_US: "整体结果概览-追问开放题-描述",
      zh_CN: "整体结果概览-追问开放题-描述"
    },
    SELECT_THROUGH_RESULT_OPEN_QUESTION_DESCRIPTION: {
      en_US: "整体结果概览-分发追问开放题-描述",
      zh_CN: "整体结果概览-分发追问开放题-描述"
    },
    THROUGH_RESULT_PROPORTION_QUESTION_TOTAL_AVG_DESCRIPTION: {
      en_US: "整体结果概览-追问比重题-结果题总分均分-描述",
      zh_CN: "整体结果概览-追问比重题-结果题总分均分-描述"
    },
    THROUGH_RESULT_PROPORTION_QUESTION_OPTION_AVG_DESCRIPTION: {
      en_US: "整体结果概览-追问比重题-结果题选项均分-描述",
      zh_CN: "整体结果概览-追问比重题-结果题选项均分-描述"
    },
    ADVANTAGE_TOP10_QUESTION_DESCRIPTION: {
      en_US: "重点题目-综合优势Top10-描述",
      zh_CN: "重点题目-综合优势Top10-描述"
    },
    DISADVANTAGE_TOP10_QUESTION_DESCRIPTION: {
      en_US: "重点题目-关键问题Top10-描述",
      zh_CN: "重点题目-关键问题Top10-描述"
    },
    HISTORY_TREND_VS_DESCRIPTION: {
      en_US: "历史趋势对比-VS主历史下降群体-描述",
      zh_CN: "历史趋势对比-VS主历史下降群体-描述"
    },
    OCH_DISAPPROVAL_DESCRIPTION: {
      en_US: "群体反对度分析-组织能力视角-描述",
      zh_CN: "群体反对度分析-组织能力视角-描述"
    },
    MORE_NEXT_ORGANIZATION_OCI_VS_SUBJECT_DESCRIPTION: {
      en_US: "更多群体数据-下一级部门-组织能力VS分析主体-描述",
      zh_CN: "更多群体数据-下一级部门-组织能力VS分析主体-描述"
    },
    MORE_NEXT_ORGANIZATION_OCI_VS_HISTORY_DESCRIPTION: {
      en_US: "更多群体数据-下一级部门-组织能力VS主历史-描述",
      zh_CN: "更多群体数据-下一级部门-组织能力VS主历史-描述"
    },
    MORE_NEXT_ORGANIZATION_EEI_VS_SUBJECT_DESCRIPTION: {
      en_US: "更多群体数据-下一级部门-敬业度VS分析主体-描述",
      zh_CN: "更多群体数据-下一级部门-敬业度VS分析主体-描述"
    },
    MORE_NEXT_ORGANIZATION_EEI_VS_HISTORY_DESCRIPTION: {
      en_US: "更多群体数据-下一级部门-敬业度VS主历史-描述",
      zh_CN: "更多群体数据-下一级部门-敬业度VS主历史-描述"
    },
    MORE_NEXT_DEMOGRAPHIC_OCI_VS_SUBJECT_DESCRIPTION: {
      en_US: "更多群体数据-人口标签-组织能力VS分析主体-描述",
      zh_CN: "更多群体数据-人口标签-组织能力VS分析主体-描述"
    },
    MORE_NEXT_DEMOGRAPHIC_OCI_VS_HISTORY_DESCRIPTION: {
      en_US: "更多群体数据-人口标签-组织能力VS主历史-描述",
      zh_CN: "更多群体数据-人口标签-组织能力VS主历史-描述"
    },
    MORE_NEXT_DEMOGRAPHIC_EEI_VS_SUBJECT_DESCRIPTION: {
      en_US: "更多群体数据-人口标签-敬业度VS分析主体-描述",
      zh_CN: "更多群体数据-人口标签-敬业度VS分析主体-描述"
    },
    MORE_NEXT_DEMOGRAPHIC_EEI_VS_HISTORY_DESCRIPTION: {
      en_US: "更多群体数据-人口标签-敬业度VS主历史-描述",
      zh_CN: "更多群体数据-人口标签-敬业度VS主历史-描述"
    },
    APPENDIX_OCI_DESCRIPTION: {
      en_US: "数据附录-组织能力-描述",
      zh_CN: "数据附录-组织能力-描述"
    },
  };
  reportTextConfigTitle = [
    {
      name: "序号",
      nzWidth: "60px",
    },
    {
      name: "语言类型",
      nzWidth: "100px",
    },
    {
      name: "编码",
      nzWidth: "150px",
    },
    {
      name: "名称",
      nzWidth: "200px",
    },
    {
      name: "自定义内容",
      nzWidth: "200px",
    },
    {
      name: "描述",
      nzWidth: "200px",
    },
    {
      name: "操作",
      nzWidth: "180px",
      nzAlign: "center",
      Right: "0px",
    },
  ];
  reportStyle = {};
  reportTabList = []; //报告自定义数据存放
  reportTitle = []; //报告自定义 风格数据存放
  tabletitle = [];
  selectlist = [];
  ngOnInit(): void {
    this.permission = this.permissionService.isPermission();
    console.log(this.projectModel);
  }
  ngOnChanges(changesQuestion: SimpleChanges) {
    this.PageIndex = 1;
    if (this.selectedname == "维度解释") {
      this.nzLoading = true;
      this.tabletitle = this.desctitle;
      this.getdesclist(this.PageIndex, "", "", "");
      this.getlistquestionnaire();
    }
    if (this.selectedname == "维度区间") {
      this.nzLoading = true;
      this.tabletitle = this.scoretitle;

      this.getscorelist(this.PageIndex, "", "", "");
      this.getlistquestionnaire();
    }
    if (this.selectedname == "自定义") {
      this.nzLoading = true;
      this.tabletitle = this.customtitle;
      this.getcustomlist(this.PageIndex, "", "");
      this.getlistquestionnaire();
    }
    if (this.selectedname == "报告自定义") {
      this.nzLoading = true;
      this.reportCustomizationTitle[5].name =
        this.surveyType == "EMPLOYEE_ENGAGEMENT" ? "自定义内容" : "自定义名称";
      this.tabletitle = this.reportCustomizationTitle;
      this.getreportlist(""); //获取数据
    }
    if (this.selectedname == "题本落地建议") {
      this.nzLoading = true;
      this.tabletitle = this.proposaltle;

      this.getProposalList(this.PageIndex, "", "zh_CN");
    }
    if (this.selectedname == "报告文字自定义") {
      this.nzLoading = true;
      this.tabletitle = this.reportTextConfigTitle;
      this.getreportTextList(); //获取数据
    }
  }

  getdesclist(PageIndex, type, code, questionnaireId) {
    let parmas = {
      configType: type, //配置类型
      dimensionNameOrCode: code, //维度名称或者是code  搜索
      page: {
        current: PageIndex, //当前页
        searchCount: true,
        size: 10,
      },
      projectId: this.projectModel.id,
      questionnaireId: questionnaireId,
    };
    this.http.customizelist(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.listOfData = res.data;
        this.addlistcode(this.listOfData);
        this.Pagetotal = res.page.total;
        this.nzLoading = false;
        console.log(this.listOfData);
      }
    });
  }
  getscorelist(PageIndex, type, code, questionnaireId) {
    let parmas = {
      type: type, //配置类型
      dimensionNameOrCode: code, //维度名称或者是code  搜索
      page: {
        current: PageIndex, //当前页
        searchCount: true,
        size: 10,
      },
      projectId: this.projectModel.id,
      questionnaireId: questionnaireId,
    };
    this.http.scorelist(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.listOfData = res.data;
        this.addlistcode(this.listOfData);
        this.Pagetotal = res.page.total;
        this.nzLoading = false;
        console.log(this.listOfData);
      }
    });
  }
  getreportlist(reportStyle) {
    this.nzLoading = true;
    this.http
      .listReportConfig({
        projectId: this.projectModel.id,
        style: reportStyle || "",
      })
      .subscribe(
        (res) => {
          if (res.result.code == 0) {
            this.listOfData = res.data.sagReportConfigVoList;
            this.addlistcode(this.listOfData);
            this.reportTitle = res.data.reportStyleNameVoList;
            res.data.reportStyleNameVoList.forEach((item) => {
              this.reportStyle[item.code] = item.name;
            });
            this.nzLoading = false;
          } else {
            this.nzLoading = false;
            this.customMsg.open("error", res.result.message || "获取报告配置失败");
          }
        },
        (error) => {
          this.nzLoading = false;
          this.customMsg.open("error", "获取报告配置失败");
          console.error("获取报告配置失败", error);
        }
      );
  }
  getcustomlist(PageIndex, name, questionnaireId) {
    let parmas = {
      dimensionName: name, //维度名称或者是code  搜索
      page: {
        current: PageIndex, //当前页
        searchCount: true,
        size: 10,
      },
      projectId: this.projectModel.id,
      questionnaireId: questionnaireId,
    };
    this.http.Customlist(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.listOfData = res.data;
        this.addlistcode(this.listOfData);
        this.Pagetotal = res.page.total;
        this.nzLoading = false;
        console.log(this.listOfData);
      }
    });
  }

  //题本落地建议
  getProposalList(PageIndex, name, language) {
    console.log("language", language);
    this.http.proposalList(this.projectModel.id).subscribe((res) => {
      if (res.result.code == 0) {
        if (name != "" && name != null) {
          this.listOfData = res.data.filter(
            (a) => a.name.indexOf(name) > -1 || a.code.indexOf(name) > -1
          );
        } else {
          this.listOfData = res.data;
        }
        this.Pagetotal = this.listOfData.length;
        this.listOfData = this.listOfData.slice(
          (PageIndex - 1) * this.PageSize,
          PageIndex * this.PageSize
        );
        this.nzLoading = false;
      }
    });
  }

  //报告文字自定义
  getreportTextList() {
    this.http
      .listReportTextConfig({
        projectId: this.projectModel.id,
      })
      .subscribe((res) => {
        if (res.result.code == 0) {
          this.listOfData = res.data.sagReportTextConfigVoList;
          this.addlistcode(this.listOfData);
          this.reportTitle = res.data.reportStyleNameVoList;
          res.data.reportStyleNameVoList.forEach((item) => {
            this.reportStyle[item.code] = item.name;
          });
          this.nzLoading = false;
        }
      });
  }

  addlistcode(listdata) {
    listdata.forEach((res) => {
      res.lang = "zh_CN";
      res.editor = false;
      if (this.selectedname == "报告自定义") {
        if (res.name) {
          res.name.zh_CN = res.name.zh_CN !== undefined ? res.name.zh_CN : null;
          res.name.en_US = res.name.en_US !== undefined ? res.name.en_US : null;
        } else {
          res.name = {
            zh_CN: null,
            en_US: null,
          };
        }
      } else if (this.selectedname == "报告文字自定义") {
        res.lang = this.language != null ? this.language : "zh_CN";
        res.editor = false;
        if (res.name) {
          res.name.zh_CN = res.name.zh_CN !== undefined ? res.name.zh_CN : null;
          res.name.en_US = res.name.en_US !== undefined ? res.name.en_US : null;
        } else {
          res.name = {
            zh_CN: null,
            en_US: null,
          };
        }
        if (res.customName) {
          res.customName.zh_CN =
            res.customName.zh_CN !== undefined ? res.customName.zh_CN : null;
          res.customName.en_US =
            res.customName.en_US !== undefined ? res.customName.en_US : null;
        } else {
          res.customName = {
            zh_CN: null,
            en_US: null,
          };
        }
        if (res.describe) {
          res.describe.zh_CN =
            res.describe.zh_CN !== undefined ? res.describe.zh_CN : null;
          res.describe.en_US =
            res.describe.en_US !== undefined ? res.describe.en_US : null;
        } else {
          res.describe = {
            zh_CN: null,
            en_US: null,
          };
        }
      }
    });
  }
  PageSizeChange() {
    this.nzLoading = true;
    this.getdatalist();
  }
  getdatalist() {
    if (this.selectedname == "维度解释") {
      this.getdesclist(
        this.PageIndex,
        this.searchType,
        this.searchName,
        this.listOfTagOptions
      );
    }
    if (this.selectedname == "维度区间") {
      this.getscorelist(
        this.PageIndex,
        this.searchType,
        this.searchName,
        this.listOfTagOptions
      );
    }
    if (this.selectedname == "自定义") {
      this.getcustomlist(
        this.PageIndex,
        this.searchName,
        this.listOfTagOptions
      );
    }

    if (this.selectedname == "报告自定义") {
      this.getreportlist(this.listOfTagOptions);
    }
    if (this.selectedname == "题本落地建议") {
      this.getProposalList(this.PageIndex, this.searchName, this.language);
    }
    if (this.selectedname == "报告文字自定义") {
      this.getreportTextList();
    }
  }
  getlistquestionnaire() {
    this.http.listquestionnaire(this.projectModel.id).subscribe((res) => {
      this.selectlist = [];
      if (res.result.code == 0) {
        res.data.forEach((item) => {
          this.selectlist.push({
            id: item.id,
            name: item.name.zh_CN ? item.name.zh_CN : item.name,
          });
        });
      }
    });
  }
  Searchlist() {
    this.nzLoading = true;
    if (this.selectedname == "维度解释") {
      this.getdesclist(
        this.PageIndex,
        this.searchType,
        this.searchName,
        this.listOfTagOptions
      );
    }
    if (this.selectedname == "维度区间") {
      this.getscorelist(
        this.PageIndex,
        this.searchType,
        this.searchName,
        this.listOfTagOptions
      );
    }
    if (this.selectedname == "自定义") {
      this.getcustomlist(
        this.PageIndex,
        this.searchName,
        this.listOfTagOptions
      );
    }
    if (this.selectedname == "报告自定义") {
      this.getreportlist(this.listOfTagOptions);
    }
    if (this.selectedname == "题本落地建议") {
      this.getProposalList(this.PageIndex, this.searchName, this.language);
    }
    if (this.selectedname == "报告文字自定义") {
      this.getreportTextList();
    }
  }
  SearchReturn() {
    this.listOfTagOptions = null;
    this.searchType = null;
    this.searchName = null;
    this.PageIndex = 1;
    this.getdatalist();
  }

  geteditor(e) {
    let iseditor = "true";
    this.listOfData.forEach((res) => {
      if (res.editor) {
        iseditor = "false";
      }
    });
    if (iseditor == "false") {
      // this.msg.warning("请先完成当前编辑！");
      this.customMsg.open("warning", "请先完成当前编辑");
    } else {
      e.editor = true;
    }
  }
  
  canceleditor(e) {
    e.editor = false;
    this.nzLoading = true;
    this.getdatalist();
  }
  saveditor(e) {
    this.nzLoading = true;
    if (this.selectedname == "维度解释") {
      this.savedimen(e);
    }
    if (this.selectedname == "维度区间") {
      this.savescore(e);
    }
    if (this.selectedname == "自定义") {
      this.savecustom(e);
    }

    if (this.selectedname == "报告自定义") {
      this.saveReport(e);
    }

    if (this.selectedname == "题本落地建议") {
      this.saveProposal(e);
    }
    if (this.selectedname == "报告文字自定义") {
      this.saveReportText(e);
    }
  }

  //保存维度解释
  savedimen(e) {
    let parmas = e;

    this.http.updatecustomizelist(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.getdatalist();
      } else {
        this.nzLoading = false;
      }
    });
  }

  //保存维度区间
  savescore(e) {
    let parmas = e;
    this.http.updatescorelist(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.getdatalist();
      } else {
        this.nzLoading = false;
      }
    });
  }

  //自定义
  savecustom(e) {
    let parmas = e;
    this.http.updateCustomlist(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.getdatalist();
      } else {
        this.nzLoading = false;
      }
    });
  }
  //保存报告自定义
  saveReport(e) {
    let parmas = e;
    this.http.saveReportConfig([parmas]).subscribe((res) => {
      if (res.result.code == 0) {
        this.getdatalist();
      } else {
        this.nzLoading = false;
      }
    });
  }
  //保存报告文字自定义
  saveReportText(e) {
    let parmas = e;
    this.http.saveReportTextConfig([parmas]).subscribe((res) => {
      if (res.result.code == 0) {
        this.getdatalist();
      } else {
        this.nzLoading = false;
      }
    });
  }
    //删除报告文字自定义
    removeReportText(e) {
     this.http.removeReportTextConfig(e.id).subscribe((res) => {
      if (res.result.code == 0) {
        this.getdatalist();
      } else {
        this.nzLoading = false;
      }
    });
  }
  // importScore读excel并修改维度区间表 exportScore导出excel维度区间表 exportDimension 导出excel度解释表
  importScore = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let api: string = `${this.tenantUrl}/survey/report/dimension/readAndUpdateReportScore?projectId=${this.projectModel.id}`;
    let sub: Observable<any>;
    sub = this.httpClient.post(api, formData);

    return sub.subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入成功");
          this.getscorelist(
            this.PageIndex,
            this.searchType,
            this.searchName,
            this.listOfTagOptions
          );
        } else {
          if (res.result.code < 10000) {
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  };

  importScorecus = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let api: string = `${this.tenantUrl}/survey/report/dimension/readAndUpdateReportCustomDimension?projectId=${this.projectModel.id}`;
    let sub: Observable<any>;
    sub = this.httpClient.post(api, formData);

    return sub.subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入成功");
          this.getcustomlist(
            this.PageIndex,
            this.searchName,
            this.listOfTagOptions
          );
        } else {
          if (res.result.code < 10000) {
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  };

  exportScorecus() {
    let api: string = `${this.tenantUrl}/survey/report/dimension/exportExcelReportCustomDimension?projectId=${this.projectModel.id}`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any>;

    sub = this.httpClient.post(api, {}, param);

    sub.subscribe(
      (data) => {
        this.downFile(data);
      },
      (error1) => {
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  }

  /**
   * importDimension读excel并修改维度解释表
   */
  importDimension = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let api: string = `${this.tenantUrl}/survey/report/dimension/readAndUpdateReportDimension?projectId=${this.projectModel.id}`;
    let sub: Observable<any>;
    sub = this.httpClient.post(api, formData);

    return sub.subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入成功");

          this.getdesclist(
            this.PageIndex,
            this.searchType,
            this.searchName,
            this.listOfTagOptions
          );
        } else {
          if (res.result.code < 10000) {
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  };

  // exportScore导出excel维度区间表
  exportScore = () => {
    let api: string = `${this.tenantUrl}/survey/report/dimension/exportExcelReportScore?projectId=${this.projectModel.id}`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any>;

    sub = this.httpClient.post(api, {}, param);

    sub.subscribe(
      (data) => {
        this.downFile(data);
      },
      (error1) => {
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  };

  //exportDimension 导出excel度解释表
  exportDimension = () => {
    let api: string = `${this.tenantUrl}/survey/report/dimension/exportExcelReportDimension?projectId=${this.projectModel.id}`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any>;

    sub = this.httpClient.post(api, {}, param);

    sub.subscribe(
      (data) => {
        this.downFile(data);
      },
      (error1) => {
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  };

  downFile(data) {
    this.showBlobErrorMessage(data);
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];
    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  showBlobErrorMessage(data: any) {
    let body = data.body;
    if (body.type === "application/json") {
      let that = this;
      const reader = new FileReader();
      reader.readAsText(body, "utf-8");
      reader.onload = () => {
        // 处理报错信息
        // JSON.parse(reader.result) 拿到报错信息
        let resp: any = JSON.parse(reader.result + "");
        let code: number = resp.result.code;
        let errMsg: string = resp.result.message;

        if (code !== 0) {
          // that.msg.error(`${errMsg}，请联系管理员。`);
          that.customMsg.open("error", `${errMsg}，请联系管理员。`);
        }
      };
    }
  }

  //导入题本落地建议
  importProposal = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let api: string = `${this.tenantUrl}/survey/project/importIasDimensionConfig?projectId=${this.projectModel.id}`;
    let sub: Observable<any>;
    sub = this.httpClient.post(api, formData);

    return sub.subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入成功");
          this.getProposalList(this.PageIndex, "", "zh_CN");
        } else {
          if (res.result.code < 10000) {
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  };

  //导出题本落地建议
  exportProposal() {
    let api: string = `${this.tenantUrl}/survey/project/exportIasDimensionConfig?projectId=${this.projectModel.id}`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any>;

    sub = this.httpClient.post(api, {}, param);

    sub.subscribe(
      (data) => {
        this.downFile(data);
      },
      (error1) => {
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  }

  //保存题本落地建议
  saveProposal(e) {
    let parmas = e;
    console.log(parmas);
    this.http.saveProposalConfig(parmas, this.language).subscribe((res) => {
      if (res.result.code == 0) {
        this.getdatalist();
      } else {
        this.nzLoading = false;
      }
    });
  }

  //导入报告文字自定义
  importCustomText = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let api: string = `${this.tenantUrl}/survey/project/importReportTextConfig?projectId=${this.projectModel.id}`;
    let sub: Observable<any>;
    sub = this.httpClient.post(api, formData);

    return sub.subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入成功");
          this.getreportTextList();
        } else {
          if (res.result.code < 10000) {
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  };

  //导出报告文字自定义
  exportCustomText() {
    let api: string = `${this.tenantUrl}/survey/project/exportReportTextConfig?projectId=${this.projectModel.id}`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any>;

    sub = this.httpClient.post(api, {}, param);

    sub.subscribe(
      (data) => {
        this.downFile(data);
      },
      (error1) => {
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  }
  
  //导入-报告自定义
  importReportConfig = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let api: string = `${this.tenantUrl}/survey/project/importReportConfig?projectId=${this.projectModel.id}`;
    let sub: Observable<any>;
    sub = this.httpClient.post(api, formData);
    return sub.subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入成功");
          this.getreportlist(""); //获取数据
        } else {
          if (res.result.code < 10000) {
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  };
  //导出-报告自定义
  exportReportConfig() {
    let api: string = `${this.tenantUrl}/survey/project/exportReportConfig?projectId=${this.projectModel.id}`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any>;
    sub = this.httpClient.post(api, {}, param);
    sub.subscribe(
      (data) => {
        this.downFile(data);
      },
      (error1) => {
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  }
}
