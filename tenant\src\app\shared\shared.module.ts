import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
// vx
import { KNZThemeModule } from "@knz/theme";
import { KNZAssemblyModule } from "@knz/assembly";
import { KNZChartModule } from "@knz/chart";
import { KNZRoleModule } from "@knz/role";
import { KNZFormModule } from "@knz/form";
// i18n
import { TranslateModule } from "@ngx-translate/core";

// #region third libs
import { NgZorroAntdModule } from "ng-zorro-antd";
import { CountdownModule } from "ngx-countdown";
import { UEditorModule } from "ngx-ueditor";
import { NgxTinymceModule } from "ngx-tinymce";
import { NoticeComponent } from "./notice/notice.component";
import { TipsComponent } from "./tips/tips.component";
import { QRCodeModule } from "angularx-qrcode";
import { MessageComponent } from "./message/message.component";
import { BtnComponent } from "./btn/btn.component";
import { BtnIconfontComponent } from "./btn-iconfont/btn-iconfont.component";
import { DisableTimeDirective } from "./directive/disable-time.directive";
import { RainbowDirective } from "./directive/rainbow.directive";

import { EChartsComponent } from "../modules/new-create/modal_type/echarts.component";
import { PieComponent } from "../modules/new-create/modal_type/modal-pie.component";

import { DragulaModule } from "ng2-dragula";
import { KnxConfig, KnxCoreModule } from "@knx/knx-ngx/core";
import { UnusualPageModule } from "@knx/knx-ngx/unusual-pages";

import { CusFactorComponent } from "../modules/new-prisma/cusfactor/cusfactor.component";

import { AdvancedModelComponent } from "../modules/report-manage/report-home/advancedFilter/advanced-model.component";
import { BreakCrumbComponent } from "../layout/breakcrumb/breakcrumb.component";
import { SignComponent } from "../layout/completeSign/sign.component";
import { ModalContentComponent } from "./tip-modal/modal-content/modal-content.component";

import { TaskCardComponent } from "./task-card/task-card.component"; // 关联任务卡片
import { TopicEmptyComponent } from "./topic-empty/topic-empty.component"; // 导入题本

import { I18nSelectComponent } from "./i18n/i18n-select/i18n-select.component"; // i18n Tabs选择
import { I18nInputComponent } from "./i18n/i18n-input/i18n-input.component"; // i18n 输入框
import { I18nSettingComponent } from "./i18n/i18n-setting/i18n-setting.component"; // i18n 语言设置

import { EmptyComponent } from "./empty/empty.component"; // 无状态
import { HideMaskComponent } from "./hide-mask/hide-mask.component"; // 邀请-隐藏遮罩

import { MsgContentComponent } from "./custom-message/msg-content/msg-content.component"; // 自定义消息体
import { CustomMessageComponent } from "./custom-message/custom-message.component"; // 自定义消息体

const COMPONENTS_Chart = [];

const THIRDMODULES = [
  NgZorroAntdModule,
  CountdownModule,
  UEditorModule,
  QRCodeModule,
  DragulaModule,
  UnusualPageModule,
];
// #endregion

// #region your componets & directives
const COMPONENTS = [
  NoticeComponent,
  TipsComponent,
  MessageComponent,
  BtnComponent,
  BtnIconfontComponent,
  PieComponent,
  EChartsComponent,
  CusFactorComponent,
  AdvancedModelComponent, // 高级筛选
  BreakCrumbComponent,
  ModalContentComponent,
  SignComponent,
  TaskCardComponent,
  TopicEmptyComponent,
  I18nSelectComponent,
  I18nInputComponent,
  I18nSettingComponent,
  // 抽屉相关组件
  EmptyComponent, // 无状态
  // 消息
  MsgContentComponent,
  CustomMessageComponent,
  HideMaskComponent, // 邀请-隐藏遮罩
];

const DIRECTIVES = [DisableTimeDirective, RainbowDirective];
// #endregion

const PIPES = [];

const knxConfig: KnxConfig = {
  productCode: "SAG",
  storageKeyPrefix: "SAG",
  baseHref: "/main",
};

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    KNZThemeModule.forChild(),
    KNZAssemblyModule,
    KNZChartModule,
    KNZRoleModule,
    KNZFormModule,
    NgxTinymceModule.forRoot({
      baseURL: "./assets/tinymce/",
      config: {
        height: 150,
        menubar: false, // 为true时会有顶部的工具栏
        toolbar_mode: "wrap", // 工具不出现三个点下拉 sliding 滑动下拉
        // print preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap hr pagebreak nonbreaking anchor insertdatetime advlist lists wordcount image textpattern help emoticons autosave autoresize
        plugins: "textcolor image", //autoresize这个插件会使当文本过长时自动拉长富文本编辑器，使内容全部展示
        toolbar:
          "code undo redo restoredraft  | styleselect | forecolor formatselect fontsizeselect bold italic underline strikethrough link anchor  |  alignleft aligncenter alignright alignjustify outdent indent | image",
        // code undo redo restoredraft | cut copy | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent | styleselect formatselect fontselect fontsizeselect | bullist numlist | blockquote subscript superscript removeformat |  table image media charmap emoticons hr pagebreak insertdatetime print preview | fullscreen |  indent2em
        language: "zh_CN",
        language_url: "./assets/tinymce/langs/zh_CN.js", //配置中文语言包，需要下载
        automatic_uploads: false, // 自动上传
        paste_data_images: true,
        //  statusbar:false,  // 隐藏底部 http://tinymce.ax-z.cn/configure/editor-appearance.php
        branding: false, // 隐藏右下角技术支持
        browser_spellcheck: true, // 拼写检查
        placeholder: "请输入内容",

        // toolbar_location:'bottom', //位置底部
        /*初始化文字的方法
        init_instance_callback: function(editor) {
          editor.setContent('请输入');
          console.log(editor.content);
        },*/

        // 编辑区样式设置
        content_style: "p {margin: 0}",
      },
    }),
    KnxCoreModule.forRoot(knxConfig),
    ...THIRDMODULES,
  ],
  declarations: [...COMPONENTS, ...DIRECTIVES, ...PIPES, ...COMPONENTS_Chart],
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    KNZThemeModule,
    KNZAssemblyModule,
    KNZChartModule,
    KNZRoleModule,
    KNZFormModule,
    TranslateModule,
    NgxTinymceModule,
    KnxCoreModule,
    ...THIRDMODULES,
    ...COMPONENTS,
    ...DIRECTIVES,
    ...PIPES,
    ...COMPONENTS_Chart,
  ],
  providers: [],
  entryComponents: [
    MessageComponent,
    AdvancedModelComponent,
    ModalContentComponent,
  ],
})
export class SharedModule {}
