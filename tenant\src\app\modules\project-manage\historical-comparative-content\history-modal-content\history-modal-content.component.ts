import { Component, OnInit, Input } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
@Component({
  selector: 'app-history-modal-content',
  templateUrl: './history-modal-content.component.html',
  styleUrls: ['./history-modal-content.component.less']
})
export class HistoryModalContentComponent implements OnInit {
  @Input() projectId: string
  @Input() id?: string

  validateForm: FormGroup;
  constructor(
    private fb: FormBuilder,
  ) { }

  formData = new FormData(); //文件数据

  ngOnInit() {
    this.validateForm = this.fb.group({
      zh_CN: [null, [Validators.required]],
      en_US: [null, []]
    });

    if(this.id) {
      // id存在 为编辑 掉接口  名字赋值
    }
  }

  ok(): void {
    for (const key in this.validateForm.controls) {
      this.validateForm.controls[key].markAsDirty();
      this.validateForm.controls[key].updateValueAndValidity();
    }
    if(this.validateForm.status === 'VALID') {
      return this.validateForm.value
    }
  }

}
