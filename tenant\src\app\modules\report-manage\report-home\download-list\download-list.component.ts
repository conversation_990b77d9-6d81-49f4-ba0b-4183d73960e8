import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  Input,
  SimpleChanges,
} from "@angular/core";
import { ReportService } from "../../report.service";
import { concat, Observable } from "rxjs";
import { HttpClient } from "@angular/common/http";
import { NzMessageService } from "ng-zorro-antd";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import { PermissionService } from "@src/modules/service/permission-service.service";
@Component({
  selector: "app-download-list",
  templateUrl: "./download-list.component.html",
  styleUrls: ["./download-list.component.less"],
})
export class DownloadListComponent implements OnInit {
  @Output() backclick = new EventEmitter<any>();
  @Input() downloadnum;
  visible: boolean = false;
  page: any = {
    current: 1,
    size: 9999999999999,
    total: 1,
  };

  name: string;

  typeObj: any = {
    GROUP: "团队报告",
    COMMON: "标准报告",
  };

  typeName: any = {
    ANSWER: "原始填答",
    DIMENSION_SCORE: "维度得分",
    STANDARD_REPORT: "报告",
  };

  tenantUrl: string = "/tenant-api";

  list: any[] = [];

  permission;
  // downloadnum = 0
  showred = 0;
  downloadLoading = false; //防止重复点击下载按钮
  constructor(
    private reportService: ReportService,
    private http: HttpClient,
    private msgServ: NzMessageService,
    private knxFunctionPermissionService: KnxFunctionPermissionService,
    public permissionService: PermissionService
  ) {}

  search() {
    this.loadList();
  }

  loadList() {
    let param: any = {
      page: this.page,
    };
    if (this.name) {
      param.name = this.name;
    }

    this.reportService.listReportScheduleByPage(param).subscribe((res) => {
      if (res.result.code === 0) {
        this.list = res.data;
        this.page.current = res.page.current;
        this.page.total = res.page.total;
      }
    });
  }

  changePageIndex(e) {
    this.page.current = e;
    this.loadList();
  }

  showDownloadProgress() {
    this.getAnglemark();

    this.loadList();
    this.visible = true;
  }

  close() {
    this.visible = false;
  }
  getAnglemark() {
    this.reportService.getReportScheduleInitCount().subscribe((res) => {
      if (res.result.code == 0) this.showred = res.data;
      this.backclick.emit(this.showred);
    });
  }

  clear() {
    this.reportService.deleteReportSchedule().subscribe((res) => {
      if (res.result.code === 0) {
        this.loadList();
      }
    });
  }

  cancel() {}

  download(data) {
    if (data.requestIds.length === 1 && data.reportName !== "STANDARD_REPORT") {
      this.startDownlod(data.requestIds[0]);
    } else {
      this.downloadStandardFiles(data);
    }
  }

  downloadNew(data) {
    if (data.requestIds.length === 1 && data.reportName !== "STANDARD_REPORT") {
      this.startDownlod(data.requestIds[0]);
    } else {
      this.downloadStandardFilesNew(data);
    }
  }

  startDownlod(fileId: string) {
    if (this.downloadLoading) {
      return;
    }
    this.downloadLoading = true;
    const api = `${this.tenantUrl}/survey/standard/file/download/${fileId}`;
    this.http.get(api, { responseType: "blob", observe: "response" }).subscribe(
      (data) => {
        this.reportService.downFile(data);
        this.downloadLoading = false;
      },
      (error1) => {
        this.downloadLoading = false;
      }
    );
  }

  // 导出标准报告
  downloadStandardFiles(data) {
    // 中台文件接口
    const api = `${this.tenantUrl}/file/downloadMultiFile`;

    let groups: any[] = [data.requestIds];

    let obs: Observable<any>[] = [];
    for (let index = 0; index < groups.length; index++) {
      const curFileIds = groups[index];
      let sub = this.http.post(api, curFileIds, {
        responseType: "blob",
        observe: "response",
      });
      obs.push(sub);
    }

    concat(...obs).subscribe(
      (res) => {
        this.reportService.downFile(res);
      },
      (error1) => {}
    );
  }

  // 导出标准报告
  downloadStandardFilesNew(data) {
    if (this.downloadLoading) {
      return;
    }
    this.downloadLoading = true;
    // 中台文件接口
    const api = `${this.tenantUrl}/file/downloadMultiFile`;

    let groups: any[] = [data.requestIds];

    let obs: Observable<any>[] = [];
    for (let index = 0; index < groups.length; index++) {
      const curFileIds = groups[index];
      let sub = this.http.post(api, curFileIds, {
        responseType: "blob",
        observe: "response",
      });
      obs.push(sub);
    }

    let fileName = data.name;
    if (fileName && fileName.length > 0 && fileName.indexOf(".zip") < 0) {
      if (fileName.indexOf("({0})") >= 0) {
        fileName = fileName.replace("({0})", "");
      }
      fileName = fileName + ".zip";
    }

    concat(...obs).subscribe(
      (res) => {
        this.reportService.downFileWithName(res, fileName);

        this.downloadLoading = false;
      },
      (error1) => {
        this.downloadLoading = false;
      }
    );
  }

  hasDownLoadPermission() {
    if (this.permission) {
      return true;
    }

    if (
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_REPORT"
      ) ||
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_DIMENSION_SCORE"
      ) ||
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_DIMENSION_SCORE"
      ) ||
      this.knxFunctionPermissionService.has(
        "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_REPORT"
      )
    ) {
      return true;
    }

    return false;
  }

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
  }

  ngOnChanges(changesQuestion: SimpleChanges) {
    this.showred = this.downloadnum;
  }
}
