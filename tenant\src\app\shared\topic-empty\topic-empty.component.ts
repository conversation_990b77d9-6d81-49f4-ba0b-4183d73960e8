import { Component, EventEmitter, Input, OnInit, Output, } from '@angular/core';

@Component({
  selector: 'app-topic-empty',
  templateUrl: './topic-empty.component.html',
  styleUrls: ['./topic-empty.component.less']
})
export class TopicEmptyComponent implements OnInit {

  @Input() text: string = '暂无问卷，请先导入题本';
  @Input() btnText: string = '定制题本';
  @Output() onClick = new EventEmitter<void>();
  constructor() { }
  
  ngOnInit() {}

  handClick(){
    this.onClick.emit()
  }
}
