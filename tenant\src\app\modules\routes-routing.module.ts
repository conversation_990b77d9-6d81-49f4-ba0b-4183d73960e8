import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
// layout
import { LayoutDefaultComponent } from '../layout/default/default.component';
// import { LayoutPassportComponent } from '../layout/passport/passport.component';
//
// import { LoginComponent } from './auth/login/index.component';
import { HomeComponent } from './home/<USER>';

import { LoginGuardService } from "@shared/guard/login-guard.service";
import { ArtistComponent } from './tourist/artist/artist.component';
import { ProgrammerComponent } from './tourist/programmer/programmer.component';
import { PurchaseComponent } from './tourist/purchase/purchase.component';
import { SalesComponent } from './tourist/sales/sales.component';
import { MarketComponent } from './tourist/market/market.component';
import { FinanceComponent } from './tourist/finance/finance.component';
import { LoadingComponent } from './auth/loading/loading.component';
import { LegalComponent } from './tourist/legal/legal.component';
import { RiskControlComponent } from './tourist/risk-control/risk-control.component';
import { JumpComponent } from './auth/jump/jump.component';
import { KnxJumpingComponent } from '@knx/knx-ngx/jumping';
import {UrlPermissionGuard} from "@knx/knx-ngx/core";
import {NopermissionPageComponent,NoexistPageComponent} from '@knx/knx-ngx/unusual-pages'
import { APP_BASE_HREF } from '@angular/common';
import { SubMicroAppComponent, SubMicroAppModule, VUE_MICRO_APP_BASEROUTE } from '@src/core/sub-micro-app';

const routes: Routes = [
  // 登录相关

  // {
  //   path: 'auth',
  //   component: LayoutPassportComponent,
  //   children: [
  //     {
  //       path: 'login',
  //       component: LoginComponent,
  //       data: { title: '登录', titleI18n: 'app.login.login' },
  //     }
  //   ]
  // },
  // { path: '', redirectTo: 'tourist', pathMatch: 'full' }, TouristGuardService
  { path: '', redirectTo: 'tourist/home', pathMatch: 'full', },
  { path: 'tourist', loadChildren: () => import('./tourist/tourist.module').then(m => m.TouristModule)},
  { path: 'legal', component: LegalComponent, data: { title: 'legal', titleI18n: 'legal' }},
  { path: 'risk-control', component: RiskControlComponent, data: { title: 'risk-control', titleI18n: 'risk-control' }},
  { path: 'market', component: MarketComponent, data: { title: 'market', titleI18n: 'market' }},
  { path: 'finance', component: FinanceComponent, data: { title: 'finance', titleI18n: 'finance' }},
  { path: 'purchase', component: PurchaseComponent, data: { title: 'purchase', titleI18n: 'purchase' }},
  { path: 'artist', component: ArtistComponent, data: { title: 'artist', titleI18n: 'artist' }},
  { path: 'programmer', component: ProgrammerComponent, data: { title: 'programmer', titleI18n: 'programmer' }},
  { path: 'sales', component: SalesComponent, data: { title: 'sales', titleI18n: 'sales' }},
  { path: 'user', loadChildren: () => import('./login/login.module').then(m => m.LoginModule), },
  { path: 'tenant', component: LoadingComponent, data: { title: 'loading', titleI18n: 'loading' }},
  { path: 'jump', component: JumpComponent, data: { title: 'jump', titleI18n: 'jump' }},
  { path: 'jumping', component: KnxJumpingComponent, data: { title: 'jumping', titleI18n: 'jumping' } },
  {
    path: '',
    component: LayoutDefaultComponent,
    canActivate: [LoginGuardService],
    data:{
      guards:[
        UrlPermissionGuard
      ]
    },
    canActivateChild: [LoginGuardService,UrlPermissionGuard],
    children: [
      { path: 'index', redirectTo: 'home', pathMatch: 'full' },
      { path: 'home', loadChildren: () => import('./home/<USER>').then(m => m.HomeModule), data: { title: '首页', titleI18n: '首页'} },
      { path: 'new-activity', loadChildren: () => import('./new-activity/new-activity.module').then(m => m.NewActivityModule), data: { title: '新建活动', titleI18n: '新建活动'} },
      { path: 'new-create', loadChildren: () => import('./new-create/new-create.module').then(m => m.NewCreateModule), data: { title: '新建活动', titleI18n: '新建活动'} },
      { path: 'new-prisma', loadChildren: () => import('./new-prisma/new-prisma.module').then(m => m.NewPrismaModule), data: { title: '新建活动', titleI18n: '新建活动' } },
      { path: 'report-manage', loadChildren: () => import('./report-manage/report-manage.module').then(m => m.ReportManageModule), data: { title: '报告管理', titleI18n: '报告管理' } },
      { path: 'project-manage', loadChildren: () => import('./project-manage/project-manage.module').then(m => m.ProjectManageModule),data: { title: '活动管理', titleI18n: '活动管理'}},
      { path: 'setting', loadChildren: () => import('./setting/setting.module').then(m => m.SettingModule)}, 
      { path: 'custom-book', loadChildren: () => import('./custom-book/custom-book.module').then(m => m.CustomBookModule)},
      { path: 'org', loadChildren: () => import('./org/org.module').then(m => m.OrgModule)},
      { path: 'nopermission', component: NopermissionPageComponent, },
      { path: VUE_MICRO_APP_BASEROUTE, children: [
        {
          path: '**',
          component: SubMicroAppComponent,
        }
      ] },
      { path: 'noexist', component: NoexistPageComponent, },
    ],
  }
];

export function getBaseURL () {
  if (window.__MICRO_APP_BASE_ROUTE__) {
    return window.__MICRO_APP_BASE_ROUTE__;
  }
  const baseEl = document.querySelector('base');
  return baseEl.getAttribute('href');
}

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      // useHash: environment.useHash,
      scrollPositionRestoration: 'top',
    }),
  ],
  providers: [
    {
      provide: APP_BASE_HREF,
      useFactory: getBaseURL,
    },
  ],
  exports: [RouterModule],
})
export class RouteRoutingModule {}
