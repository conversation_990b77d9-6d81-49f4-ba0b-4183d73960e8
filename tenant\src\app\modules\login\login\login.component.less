.container {

  padding-top: 10px;
  margin-top: 15px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header_1 {
  width: 48px;
  height: 33px;
  font-size: 24px;
  font-weight: 300;
  color: rgba(23, 49, 76, 1);
  line-height: 33px;
}

.header_2 {
  width: 136px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(23, 49, 76, 1);
  line-height: 20px;
}

.footer {
  display: flex;
  justify-content: center;
  align-items: flex-start;

  a {
    color: #17314C;
  }

  button {
    flex: 1;
  }
}

.footer_1 {
  display: flex;
  height: 17px;
  font-size: 12px;
  font-weight: 400;
  // color: rgba(64, 158, 255, 1);
  line-height: 17px;
  margin: 10px 0;
}

.footer_2 {
  display: flex;
  justify-content: flex-end;
  height: 17px;
  font-size: 12px;
  font-weight: 400;
  color: rgba(64, 158, 255, 1);
  line-height: 17px;
  margin: 10px 0;
}

.content {
  width: 100%;
  padding-top: 30px;
}

.input_long {
  width: 416px;
  height: 40px;
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
  border: 1px solid rgba(230, 230, 230, 1);
}

.input_row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  // margin-bottom: 16px;
}

.input_item {
  flex: 1;
}

.label_item {
  width: 166px;
  height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 117, 117, 1);
  line-height: 20px;
}

.submit {
  width: 416px;
  text-align: center;
  cursor: pointer;
  user-select: none;
  height: 38px;
  line-height: 38px;
  font-size: 16px;
  color: #aaa;
  font-weight: 500;
  background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
  box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
  color: #fff;
  border-radius: 19px;
  border-style: none;
  outline: none;

  &.act {
    background: linear-gradient(90deg, rgba(38, 208, 241, 1) 0%, rgba(64, 158, 255, 1) 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    color: #fff;
  }
}

.input_row nz-form-item {
  width: 100%;
}

.input_row nz-form-item input {
  width: 100%;
}