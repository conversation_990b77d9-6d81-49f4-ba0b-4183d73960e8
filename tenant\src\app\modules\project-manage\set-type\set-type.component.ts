import { Component, Input, OnInit, OnDestroy } from "@angular/core";
import { NzMessageService, NzDrawerRef } from "ng-zorro-antd";
import { ProjectManageService } from "../project-manage.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
@Component({
  selector: "app-set-type",
  templateUrl: "./set-type.component.html",
  styleUrls: ["./set-type.component.less"],
})
export class SetTypeComponent implements OnInit, OnDestroy {
  @Input() projectId: string;
  @Input() projectName: any;

  tipdetaillist = [];
  nairId: string;
  dropList: any[] = [];
  styleList: any[] = [];
  isUpdating: boolean = false;
  reportType = "";
  surveyType = "";
  isDisabled = false;
  questionnaireId = "";
  ReportTemplateList = [];
  TreeByType = [];
  treeindex = 0;
  reportTypeData = {};
  displaySetting = []; // 显示设置
  displayActives = [];
  selectOptions1 = [2, 3, 4, 5];
  selectOptions2 = [3, 4, 5, 6, 7, 8, 9, 10];
  isShowUnfold = false;
  isShowSetting = false;
  multiOpenFrequencyCalRule: string[] = []; // 多选题展示规则
  multiOpenFrequencyCalRuleList = [
    { value: 'ANSWER_PERSON_NUM', name: '选项选中人数/有效填答人数', checked: false },
    { value: 'ANSWER_SELECT_NUM', name: '选项选中人数/选项选中频次', checked: false }
  ];
  isAnswerIncompleteGeneratePartReport = false;
  private routerSubscription: Subscription;
  clickMenu(value) {
    this.nairId = value;
    //如果不包含新的工具数据则重新请求，否则用旧数据
    if (!Object.keys(this.reportTypeData).includes(value)) {
      this.loadData();
    }
  }
  constructor(
    private msgServ: NzMessageService,
    private drawerRef: NzDrawerRef,
    private apiService: ProjectManageService,
    private customMsg: MessageService,
    private router: Router
  ) {}

  ngOnInit() {
    this.apiService.getDropdonwData(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.dropList = res.data;
        this.dropList.forEach((item, i) => {
          if (i === 0) {
            item.selected = true;
          } else {
            item.selected = false;
          }
        });
        this.questionnaireId = this.dropList[0].id;
        this.nairId = this.dropList[0].id;
        this.loadData();
        this.apiService
          .getDetailReport(this.dropList[0].id)
          .subscribe((item) => {
            this.tipdetaillist = item.data;
            this.tipdetaillist.forEach((item) => {
              if (!item.projectId) item.projectId = this.projectId;
            });
            this.apiService
              .getallDetailReport(this.dropList[0].id)
              .subscribe((res) => {
                this.ReportTemplateList = res.data;

                for (
                  let index = 0;
                  index < this.tipdetaillist.length;
                  index++
                ) {
                  const element = this.tipdetaillist[index];
                  this.typeChangenew(
                    element.standardReportTemplateId,
                    this.ReportTemplateList,
                    element,
                    false
                  );
                }
              });
            this.apiService
              .getallTreeByType("QUESTIONNAIRE_TYPE")
              .subscribe((val) => {
                this.TreeByType = val.data;
              });
          });
        this.multiOpenFrequencyCalRule = this.dropList[0].multiOpenFrequencyCalRule;
        this.includesInvResearchCustomOrDPFun(this.dropList[0].reportType);
        this.showSetting();
      }
    });

    this.apiService.getStyles().subscribe((res) => {
      if (res.result.code === 0) {
        this.styleList = res.data;
        for (let index = 0; index < this.styleList.length; index++) {
          const element = this.styleList[index];
          let tmp: string = element.reportStyleEnum;
          let isGroup: boolean = tmp.indexOf("GROUP") > -1;
          element.isGroup = isGroup;
        }
      }
    });
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }


  getQueryParam(): any {
    let param = {
      page: {
        current: 1,
        size: 300,
      },
      projectId: this.projectId,
      questionnaireId: this.nairId,
    };
    return param;
  }

  loadData() {
    this.reportTypeData[this.nairId] = [];
    let sub = this.apiService.getList(this.getQueryParam());
    sub.subscribe((res) => {
      if (res.result.code === 0) {
        this.reportTypeData[this.nairId] = res.data;
        for (
          let index = 0;
          index < this.reportTypeData[this.nairId].length;
          index++
        ) {
          const element = this.reportTypeData[this.nairId][index];
          this.typeChange(element.standardReportTemplateId, element, false);
        }
        this.isAnswerIncompleteGeneratePartReport = this.reportTypeData[this.nairId][0].isAnswerIncompleteGeneratePartReport;
        this.reportType = this.reportTypeData[this.nairId][0].reportType;
        this.surveyType = this.reportTypeData[this.nairId][0].surveyType;
        this.isDisabled = !(
          [
            "DP_INVESTIGATION_RESEARCH_CUSTOM",
            "OC_INVESTIGATION_RESEARCH",
          ].includes(this.reportType) || this.surveyType === "ASSESSMENT"
        );
      }
    });
  }
  log(value, item) {
    item.selectInfos.forEach((em) => {
      if (value.includes(em.moduleType)) {
        em.isSelect = true;
      } else {
        em.isSelect = false;
      }
    });
  }
  typeChange(e, data, isClear: boolean) {
    //切换报告风格的时候
    if (isClear) {
      data.selectModules = [];
    }
    for (
      let index = 0;
      index < data.optionalStandardReportTemplateList.length;
      index++
    ) {
      const element = data.optionalStandardReportTemplateList[index];
      if (e === element.id) {
        data.selectedType = element.standardReportType;
        if (isClear) {
          data.styles = [];
        }
        break;
      }
    }
    this.multiOpenFrequencyCalRule = data.multiOpenFrequencyCalRule;
    this.includesInvResearchCustomOrDPFun(data.selectedType);
    this.showSetting();
  }

  showSetting() {
    if (this.isShowSetting) {
      if (this.multiOpenFrequencyCalRule == null || this.multiOpenFrequencyCalRule.length == 0) {
        this.multiOpenFrequencyCalRule = ['ANSWER_PERSON_NUM'];
      }
      // 多选题展示规则
      //初始化
      this.multiOpenFrequencyCalRuleList.forEach((item) => {
        item.checked = false;
      });
      this.multiOpenFrequencyCalRule.forEach((val) => {
        this.multiOpenFrequencyCalRuleList.forEach((item) => {
          if (val == item.value) {
            item.checked = true;
          }
        });
      });
      this.isShowUnfold = false;
    }
  }
  reportStyleChange(value, item) {
    const data = {
      questionnaireId: this.nairId,
      reportStyles: value,
      standardReportTemplateId: item.standardReportTemplateId,
    };
    this.apiService.getReportTemplate(data).subscribe((res) => {
      item.selectModules = res.data;
    });
  }
  typeChangenew(e, data, list, isClear: boolean) {
    for (let index = 0; index < data.length; index++) {
      const element = data[index];
      if (e === element.standardReportTemplateId) {
        list.standardReportTypeEnum = element.standardReportTypeEnum;

        if (isClear) {
          list.styleEnums = [];
        }
        break;
      }
    }
  }

  addtipdetails() {
    this.tipdetaillist = [
      ...this.tipdetaillist,
      {
        questionnaireId: this.questionnaireId,
        standardReportTemplateId: null,
        standardReportTemplateName: "",
        standardReportTypeEnum: "",
        styleEnums: [],
        productType: null,
        projectId: this.projectId,
        id: this.treeindex,
      },
    ];
    this.treeindex++;
  }

  deletedetail(id, TemplateId) {
    if (TemplateId) {
      this.tipdetaillist = this.tipdetaillist.filter(
        (d) => d.standardReportTemplateId !== TemplateId
      );
    } else {
      this.tipdetaillist = this.tipdetaillist.filter((d) => d.id !== id);
    }
  }

  savedetaillist() {
    let standardReportTemplateId = this.tipdetaillist.every((item) => {
      return item.standardReportTemplateId;
    });

    let styleEnums = this.tipdetaillist.every((item) => {
      return item.styleEnums.length != 0;
    });

    let productType = this.tipdetaillist.every((item) => {
      return item.productType;
    });

    if (standardReportTemplateId && styleEnums && productType) {
      let parmas = this.tipdetaillist;
      this.apiService
        .createTipDetailReport(parmas, this.projectId, this.questionnaireId)
        .subscribe((item) => {
          if (item.result.code == 0) {
            this.onSave();
          }
        });
    } else {
      // this.msgServ.warning("请将细分类型选项填写完整");
      this.customMsg.open("warning", "请将细分类型选项填写完整");
    }
  }
  saveSetting() {
    if (this.reportType == "TIP_NEW_2") {
      this.savedetaillist();
    } else {
      this.onSave();
      if (this.includes360or270Fun(this.reportType)) {
        this.onSaveDisplaySetting();
      }
    }
  }
  reset() {
    this.reportTypeData = {};
    this.loadData();
    if (this.includes360or270Fun(this.reportType)) {
      this.displaySetting = [];
    }
    this.multiOpenFrequencyCalRule = this.dropList[0].multiOpenFrequencyCalRule;
    this.includesInvResearchCustomOrDPFun(this.dropList[0].reportType);
    this.showSetting();
  }
  onSave() {
    let data = [];
    if(this.isShowSetting && this.multiOpenFrequencyCalRule.length == 0){
      this.customMsg.open("error", "多选题展示规则至少选择一个");
      return false;
    }
    for (let key in this.reportTypeData) {
      if (this.reportTypeData[key].some((item) => item.styles.length === 0)) {
        // this.msgServ.error("报告风格不能为空");
        this.customMsg.open("error", "报告风格不能为空");
        return false;
      }
      const dataItem = this.reportTypeData[key].map((item) => {
        const params = {
          questionnaireId: item.id,
          reportStyleSelects: item.selectModules
            ? item.selectModules.map((em) => {
                return {
                  reportStyle: em.reportStyle,
                  selectInfos: em.selectInfos.map((ele) => {
                    return {
                      isSelect: ele.isSelect,
                      moduleType: ele.moduleType,
                    };
                  }),
                };
              })
            : [],
          standardReportTemplateId: item.standardReportTemplateId,
          styles: item.styles,
          multiOpenFrequencyCalRule: this.multiOpenFrequencyCalRule,
          isAnswerIncompleteGeneratePartReport: this.isAnswerIncompleteGeneratePartReport,
        };
        if (item.selectGroupModule) {
          // 团队
          const groupReportStyleSelect = {
            reportStyle: item.selectGroupModule.reportStyle,
            selectInfos: item.selectGroupModule.selectInfos.map((val) => ({
              isSelect: val.isSelect,
              moduleType: val.moduleType,
            })),
          };
          params["groupReportStyleSelect"] = groupReportStyleSelect;
        }
        return params;
      });
      data = [...data, ...dataItem];
    }
    this.isUpdating = true;
    this.apiService.updateType(data).subscribe(
      (res) => {
        this.isUpdating = false;
        if (res.result.code === 0) {
          this.msgServ.success("保存成功");
          this.drawerRef.close();
        }
      },
      (err) => {
        this.isUpdating = false;
        // this.msgServ.error("保存数据不能为空");
        this.customMsg.open("error", "保存数据不能为空");
      }
    );
  }

  handUnfold(questionnaireId, getStyle, realityStyle) {
    const active = `${questionnaireId}&&&${realityStyle}`;
    if (
      this.displaySetting.filter(
        (val) =>
          val.questionnaireId == questionnaireId &&
          val.reportStyle == realityStyle
      ).length > 0
    ) {
      if (this.displayActives.includes(active)) {
        this.displayActives = this.displayActives.filter(
          (val) => val !== active
        );
      } else {
        this.displayActives.push(active);
      }
    } else {
      this.apiService
        .getListSagBehaviorReportSetting({ questionnaireId, style: getStyle })
        .subscribe((res) => {
          const data = res.data || [];
          const displayReport = data.find(
            (val) =>
              val.questionnaireId == questionnaireId &&
              val.reportStyle == realityStyle
          );
          this.displaySetting.push(displayReport);
          this.displayActives.push(active);
        });
    }
    
  }
  showUnfold() {
    this.isShowUnfold = !this.isShowUnfold;
  }
  
   optionalMultiOpenFrequencyCalRule(e) {
    this.multiOpenFrequencyCalRule = e.sort((a, b) => {
      if (a === "ANSWER_PERSON_NUM") return -1;
      if (b === "ANSWER_PERSON_NUM") return 1;
      if (a === "ANSWER_SELECT_NUM") return -1;
      if (b === "ANSWER_SELECT_NUM") return 1;
      return 0;
    });
  }

  onSaveDisplaySetting() {
    console.log("displaySetting", this.displaySetting);
    this.apiService.saveOrUpdateSetting(this.displaySetting).subscribe(
      (res) => {
        console.log("显示设置：res", res);
      },
      (err) => {
        console.log("显示设置：err", err);
      }
    );
  }

  includes360or270Fun(str) {
    return str.includes("270") || str.includes("360");
  }
   includesInvResearchCustomOrDPFun(str) {
     if (str === "INVESTIGATION_RESEARCH_CUSTOM" || str === "DP_INVESTIGATION_RESEARCH_CUSTOM") { 
      this.isShowSetting = true;
     } else {
      this.isShowSetting = false;
     }
  }
  includes270Fun(str) {
    return str.includes("270");
  }
  //是否是特殊类型 党建中寰
  includesSpecialType(str, str1) {
    console.log(str, str1);
    let type = ["_270_CUSTOMIZE_ZHONGHUAN", "_360_CUSTOMIZE_RED"];
    let flag = type.includes(str) || type.includes(str1);
    console.log(flag);

    return flag;
  }
}
