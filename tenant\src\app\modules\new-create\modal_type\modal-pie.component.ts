import {
  Component,
  OnInit,
  Input,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
  SimpleChanges,
} from "@angular/core";
import { reduce } from "rxjs/operators";

@Component({
  selector: "app-echarts-pie",
  templateUrl: "./modal-pie.component.html",
  styleUrls: ["./modal-pie.component.less"],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PieComponent implements OnInit {
  @Input() containerWidth;
  @Input() containerHeight;
  @Input() data;
  @Input() sunecharts;
  legends = [];
  colors = ["#E793BB", "#8CCDBB", "#86BCD8", "#ABA3D5"];
  colortwo = [
    "rgb(237, 125, 49)",
    "rgb(225, 102, 204)",
    "rgb(180, 230, 226)",
    "rgb(255, 227, 139)",
    "rgb(119, 180, 137)",
    "rgb(203, 212, 237)",
    "rgb(220, 242, 244)",
    "rgb(231, 230, 230)",
    "rgb(217, 235, 205)",
    "rgb(203, 169, 229)",
    "rgb(247, 197, 163)",
    "rgb(255, 201, 237)",
    "rgb(53, 161, 153)",
    "rgb(255, 192, 0)",
    "rgb(26, 30, 51)",
    "rgb(38, 56, 107)",
    "rgb(71, 182, 195)",
    "rgb(175, 171, 171)",
    "rgb(112, 173, 71)",
    "rgb(112, 48, 160)",
  ];
  public chartOption: any;
  constructor(private cdf: ChangeDetectorRef) {}

  setChartOption(data) {
    if (this.sunecharts) {
      this.chartOption = {
        visualMap: {
          type: "continuous",
          min: 0,
          max: 10,
          show: false,
        },
        series: {
          type: "sunburst",
          data: data,
          radius: [0, "90%"],
          label: {
            rotate: "radial",
          },
        },
      };
    } else {
      this.chartOption = {
        color: this.colors,
        series: [
          {
            type: "pie",
            radius: [39, 200],
            center: ["50%", "50%"],
            roseType: "area",
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 6,
            },
            label: {
              formatter: function(params) {
                return params.name;
              },
              fontSize: 14,
            },
            data: data,
          },
        ],
      };
    }
  }

  ngOnInit() {}
  ngOnChanges(changesQuestion: SimpleChanges) {
    if (this.sunecharts) {
      let datas = [];
      console.log(this.data);
      this.data.forEach((item, index) => {
        item.id = index;
      });
      let data = JSON.parse(JSON.stringify(this.data));
      data.forEach((item, index) => {
        item.data.forEach((val) => {
          datas.push({
            id: index,
            childrenlist: val.children,
          });
        });
      });
      datas.forEach((item) => {
        item.children = [];
        item.childrenlist.forEach((val) => {
          item.children.push({
            name: val.name.zh_CN,
            id: item.id,
            children: val.detailedScoreSubChildDimensions,
          });
        });
        item.children.forEach((val) => {
          val.children.forEach((res) => {
            (res.name = res.name.zh_CN),
              // res.id = val.id
              (res.value = 30);
          });
        });
      });
      let datalist = [];
      datas.forEach((item) => {
        datalist.push(...item.children);
      });
      datalist = this.removalchildren(datalist);
      datalist.forEach((item, index) => {
        item.itemStyle = {
          color: this.colortwo[item.id],
        };
        item.children.forEach((val) => {
          val.itemStyle = {
            color: this.colortwo[item.id],
          };
        });
      });
      this.setChartOption(datalist);
    } else {
      let data = [];
      this.data.forEach((item, index) => {
        item.data.forEach((element) => {
          data.push({
            value: element.value,
            name: element.name,
            itemStyle: { color: this.colors[index] },
          });
        });
      });
      this.setChartOption(data);
    }

    this.cdf.detectChanges();
  }
  removalchildren(arr) {
    let result = [];
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      if (!obj[arr[i].name]) {
        result.push(arr[i]);
        obj[arr[i].name] = true;
      }
    }
    return result;
  } //去重
}
