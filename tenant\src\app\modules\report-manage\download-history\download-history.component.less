.container {
    // background-color: cornflowerblue;
    margin-top: 20px;
    width: 100%;
    height: auto;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;

        .text {
            width: 102px;
            height: 33px;
            font-size: 24px;
            font-family: PingFangSC-Thin, PingFang SC;
            font-weight: 100;
            color: #17314C;
            line-height: 33px;
        }

        .input {
            .search {
                width: 186px;
                height: 30px;
                background: rgba(255, 255, 255, 1);
                border-radius: 15px;
                .ant-input {
                    border-radius: 15px;
                    border: none;
                    border: solid 1px gainsboro;
                }
            }
        }
    }

    .body {
        margin: 10px 0px;

    }
}

.maxW {
    max-width: 350px;
}

.nowrap {
    white-space: nowrap;
}

button {
    padding: 0;
}

.inprogress {
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #45BFD9;
    line-height: 20px;
}
