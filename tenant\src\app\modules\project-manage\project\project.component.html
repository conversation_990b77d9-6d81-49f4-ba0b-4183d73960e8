<div style="background:#FFFFFF;height: 100%;">
  <div class="panel">
    <div class="content_name">
      <div class="modal_div">
        <a
          nz-dropdown
          nzTrigger="click"
          [nzDropdownMenu]="menu"
          [nzClickHide]="false"
          [(nzVisible)]="visible"
          [nzPlacement]="'bottomLeft'"
          style="width: 30px;height: 30px;"
        >
          <img
            *ngIf="!visible"
            src="assets/images/event-management/home/<USER>"
            alt=""
          />
          <img
            *ngIf="visible"
            src="assets/images/event-management/home/<USER>"
            alt=""
          />
        </a>
        <nz-dropdown-menu #menu="nzDropdownMenu" class="Nz_menus">
          <ul nz-menu class="drop_ul">
            <!-- <li nz-menu-item (click)="chagnemodify('modify')">修改</li> -->
            <!-- <li nz-menu-item (click)="chagnemodify('copy')">复制</li> -->
            <a
              *ngIf="projectModel.isShow && !permission"
              nz-popconfirm
              nzPopconfirmTitle="确定删除这条选项?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="deleteFacNameok()"
              (nzOnCancel)="cancel()"
            >
              <li nz-menu-item>
                删除
              </li>
            </a>
            <a
              *ngIf="projectModel.isShow && permission"
              nz-popconfirm
              nzPopconfirmTitle="确定隐藏这条选项?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="deleteFacNameok()"
              (nzOnCancel)="cancel()"
            >
              <li nz-menu-item>
                隐藏
              </li>
            </a>
            <a
              *ngIf="!projectModel.isShow && permission"
              nz-popconfirm
              nzPopconfirmTitle="确定恢复这条选项?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="deleteFacNameok()"
              (nzOnCancel)="cancel()"
            >
              <li nz-menu-item>
                恢复显示
              </li>
            </a>

            <li
              nz-menu-item
              *ngIf="
                isEditor(projectModel?.questionnaires) &&
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:LIST:EDIT_QUESTION'
                ) &&
                !prismaCus
              "
              (click)="chagnemodify('editor')"
            >
              编辑题本
            </li>
            <li
              nz-menu-item
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:LIST:EDIT_QUESTION'
                ) && prismaCus
              "
              (click)="chagnemodify('editorprimsa')"
            >
              编辑题本
            </li>

            <li
              nz-menu-item
              *ngIf="permission && projectModel.standardReportType != 'PRISMA'"
              (click)="chagnemodify('update')"
            >
              矫正算法
            </li>
            <!-- <li nz-menu-item *ngIf="projectModel.is360Project" (click)="chagnemodify('relationship')">评价关系</li> -->
            <li
              nz-menu-item
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:LIST:CUSTOMIZED_POSITIONS'
                ) && projectModel.isTipProject
              "
              (click)="chagnemodify('jobber')"
            >
              定制岗位
            </li>
            <a
              *ngIf="permission"
              nz-popconfirm
              nzPopconfirmTitle="确定更新配置?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="chagnemodify('question')"
            >
              <li nz-menu-item>
                更新配置
              </li>
            </a>
            <li
              nz-menu-item
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:LIST:REPORT_CONFIG'
                )
              "
              (click)="chagnemodify('configs')"
            >
              报告配置表
            </li>
            <!-- <li
              nz-menu-item
              *ngIf="
                permission  &&
                ([
                  'DP_INVESTIGATION_RESEARCH_CUSTOM',
                  'OC_INVESTIGATION_RESEARCH',
                  'INVESTIGATION_RESEARCH_CUSTOM'
                ].includes(projectModel.standardReportType) ||
                  projectModel.surveyType == 'ASSESSMENT')
              "
              (click)="setReportType()"
            >
              报告类型
            </li> -->
            <li
              nz-menu-item
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:LIST:REPORT_TYPE'
                )
              "
              (click)="setReportType()"
            >
              报告类型
            </li>
            <li
              nz-menu-item
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:LIST:HIS_COMPARE'
                ) &&
                projectModel.surveyType == 'EMPLOYEE_ENGAGEMENT' &&
                projectModel.status != 'ANNOUNCED'
              "
              (click)="chagnemodify('history')"
            >
              历史对比(调研)
            </li>
            <li
              nz-menu-item
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:LIST:CORRECTION_DIMENSION'
                ) &&
                projectModel.status != 'ANNOUNCED' &&
                projectModel?.questionnaires[0]?.reportType.indexOf(
                  'CUSTOMIZE'
                ) == -1 &&
                !prismaCus
              "
              (click)="getCorrect()"
            >
              矫正维度
            </li>
            <a
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:LIST:INTERFACE_PUSH'
                ) && isSystemInterface
              "
              nz-popconfirm
              nzPopconfirmTitle="确定接口推送?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="interfacePush(projectModel)"
            >
              <li nz-menu-item>
                接口推送({{ projectModel.isPushThird ? "Y" : "N" }})
              </li>
            </a>
            <li
              nz-menu-item
              *ngIf="
                permissionService.isPermissionOrSag(
                  'SAG:TENANT:PROJECT_MGT:LIST:INTER_EXTER_NORMS'
                ) &&
                isShowNorm(projectModel) &&
                (projectModel.status == 'OVER' ||
                  projectModel.status == 'SUSPEND' ||
                  projectModel.status == 'ANSWERING')
              "
              (click)="chagnemodify('norm')"
            >
              内外部常模
            </li>
            <!-- 更新问卷 -->
          </ul>
        </nz-dropdown-menu>
      </div>
    </div>
    <nz-modal
      [(nzVisible)]="showtip"
      nzTitle="对标岗位"
      (nzOnCancel)="handlejob()"
      (nzOnOk)="handleOk()"
      [nzWidth]="'800'"
    >
      <div>
        <p style="padding: 10px 0;font-size: 20px;">岗位名称</p>
        <ul>
          <nz-radio-group
            [(ngModel)]="standardReportTemplateJobId"
            (ngModelChange)="nzOnJobStype($event)"
          >
            <label
              style="width: 120px;margin-bottom: 10px;"
              *ngFor="let item of Tipjobslist; let i = index"
              [nzValue]="item.id"
              nz-radio
              >{{ item.name.zh_CN }}</label
            >
          </nz-radio-group>
        </ul>
      </div>
    </nz-modal>

    <nz-drawer
      nzPlacement="right"
      nzTitle="报告配置表"
      [nzVisible]="configtation"
      (nzOnClose)="handleconfig()"
      [nzWidth]="'1010px'"
      nzWrapClassName="round-right-drawer-report-config"
    >
      <div>
        <nz-tabset [(nzSelectedIndex)]="selectedIndex" nzSize="large">
          <nz-tab
            *ngFor="let tab of tabslist"
            [nzTitle]="tab.name"
            (nzClick)="Changelog(['click', selectedIndex])"
          >
            <app-table-config
              [projectModel]="projectModel"
              [surveyType]="tab.surveyType"
              *ngIf="tabslist[selectedIndex].name == tab.name"
              [selectedname]="tab.name"
              [nzSelectedIndex]="selectedIndex"
            ></app-table-config>
          </nz-tab>
        </nz-tabset>
      </div>
    </nz-drawer>
    <!-- <ng-template #configtationTitleTemplate>
      <p>报告配置表</p>
      <a style="font-size: small;" nz-tooltip [nzTooltipTitle]="configtationDescriptionTemplate" nzTooltipPlacement="rightTop" [nzOverlayStyle]="{'max-width':'1000px'}" >提示说明</a>
    </ng-template> -->
    <!--<ng-template #configtationDescriptionTemplate>
        <p>培养建议，反思建议，发展建议中可插入动态链接，供用户点击打开新的链接页面。</p>
        <p>插入格式：&lt;a href=&quot;https://www.knx.com.cn/&quot; target=&quot;_blank&quot;&gt;肯耐珂萨&lt;/a&gt;</p>
    </ng-template>-->

    <nz-modal
      [(nzVisible)]="Correctshow"
      nzTitle="维度矫正"
      [nzFooter]="CorrectFooter"
      (nzOnCancel)="Correctconfig()"
      [nzWidth]="'70%'"
    >
      <ul class="code_ul" *ngIf="!CorrectNextshow">
        <li *ngFor="let item of codelist; let i = index" class="code_li">
          <div
            *ngFor="let res of item.Dimensionslist; let j = index"
            class="code_div"
          >
            <label
              class="big_lable"
              style="width: 200px;"
              nz-checkbox
              nzValue="res.name"
              [(ngModel)]="res.isChecked"
              (ngModelChange)="chooseselect(res.name, res.isChecked)"
            >
              {{ res.parentNameI18n["zh_CN"]
              }}{{
                res.parentNameI18n["en_US"]
                  ? "/" + res.parentNameI18n["en_US"]
                  : "/null"
              }}
            </label>
            <nz-checkbox-wrapper
              class="min_div"
              style="flex: 1;"
              (nzOnChange)="log($event)"
            >
              <div nz-row class="label_div">
                <div
                  nz-col
                  nzSpan="8"
                  *ngFor="let val of res.data; let k = index"
                >
                  <label
                    nz-checkbox
                    nzValue="val.code"
                    [(nzDisabled)]="!res.isChecked"
                    [(ngModel)]="val.isChecked"
                  >
                    {{ val.name["zh_CN"] ? val.name["zh_CN"] : "null"
                    }}{{
                      val.name["en_US"] ? "/" + val.name["en_US"] : "/null"
                    }}
                  </label>
                </div>
              </div>
            </nz-checkbox-wrapper>
          </div>
        </li>
      </ul>
      <ul class="code_ul" *ngIf="CorrectNextshow">
        <li *ngFor="let item of codelist; let i = index" class="code_li">
          <div
            *ngFor="let res of item.Dimensionslist; let j = index"
            class="code_div"
          >
            <div class="big_lable" style="width: 200px">
              {{ res.parentNameI18n["zh_CN"]
              }}{{
                res.parentNameI18n["en_US"]
                  ? "/" + res.parentNameI18n["en_US"]
                  : "/null"
              }}
            </div>
            <div class="min_div" style="flex: 1;">
              <div nz-row class="label_div">
                <div
                  nz-col
                  class="col_div"
                  nzSpan="8"
                  *ngFor="let val of res.data; let k = index"
                >
                  <input
                    nz-input
                    placeholder="中文"
                    style="width: 90px;"
                    [(ngModel)]="val.name['zh_CN']"
                    nzSize="small"
                  />
                  <input
                    nz-input
                    placeholder="英文"
                    style="width: 90px;margin-left: 5px;"
                    [(ngModel)]="val.name['en_US']"
                    nzSize="small"
                  />
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </nz-modal>

    <ng-template #CorrectFooter>
      <div class="correct-footer">
        <button
          nz-button
          nzType="default"
          [nzSize]="size"
          nzShape="round"
          (click)="CorrectNext()"
          *ngIf="!CorrectNextshow"
        >
          下一步
        </button>
        <ng-container *ngIf="CorrectNextshow">
          <button
            nz-button
            nzType="default"
            [nzSize]="size"
            (click)="CorrectToDefalt()"
            nzShape="round"
          >
            恢复默认
          </button>
          <button
            nz-button
            nzType="primary"
            [nzSize]="size"
            nzShape="round"
            (click)="CorrectOk()"
          >
            保存
          </button>
          <a (click)="CorrectNext()">返回上一步</a>
        </ng-container>
      </div>
    </ng-template>
  </div>
</div>
