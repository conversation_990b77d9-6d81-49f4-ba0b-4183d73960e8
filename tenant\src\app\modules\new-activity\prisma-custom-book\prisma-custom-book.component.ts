import { Compo<PERSON>, <PERSON>Ini<PERSON>, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import {
  NzMessageService,
  NzModalService,
  UploadXHRArgs,
  NzModalRef,
} from "ng-zorro-antd";
import { SubjectEditComponent } from "../subject-edit/subject-edit.component";
import _ from "lodash";
import { SubjectBatchEditComponent } from "../subject-batch-edit/subject-batch-edit.component";
import { BookSettingComponent } from "../book-setting/book-setting.component";
import { HttpEvent } from "@angular/common/http";

import { ElementRef } from "@angular/core";
import { Subscription, fromEvent } from "rxjs";
import { debounceTime } from "rxjs/operators";

import { AddPrismaCustomBookComponent } from "./add-prisma-custom-book/add-prisma-custom-book.component";
import { DragulaService } from "ng2-dragula";
import { MicroAppService } from "@core/sub-micro-app/sub-micro-app.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";
@Component({
  selector: "app-prisma-custom-book",
  templateUrl: "./prisma-custom-book.component.html",
  styleUrls: ["./prisma-custom-book.component.less"],
})
export class PrismaCustomBookComponent implements OnInit {
  @ViewChild("deliveryTable", { static: false }) deliveryView: ElementRef;
  private subscription: Subscription;
  sourceList: any[] = [];

  topicList: any[] = [];

  projectInfo: any;

  tips: any = {
    SINGLE: {
      name: {
        zh_CN: "单选",
        en_US: "Single Choice",
      },
      color: "#9833FF",
      bg: "rgba(152, 51, 255, 0.08)",
    },
    SCALE: {
      name: {
        zh_CN: "量表",
        en_US: "Scale",
      },
      color: "#3372FF",
      bg: "rgba(51, 114, 255, 0.08)",
    },
    MULTIPLE_CHOICE: {
      name: {
        zh_CN: "多选",
        en_US: "Multiple Choice",
      },
      color: "#3372FF",
    },
    ESSAY_QUESTION: {
      name: {
        zh_CN: "开放",
        en_US: "Open Question",
      },
      color: "#24CC9E",
      bg: "rgba(36, 204, 158, 0.08)",
    },
    MULTIPLE_CHOICE_ESSAY_QUESTION: {
      name: {
        zh_CN: "多选开放",
        en_US: "Multiple Choice Open Question",
      },
      color: "#FF8D33",
      bg: "rgba(255, 141, 51, 0.08)",
    },
    isPierceThrough: {
      name: {
        zh_CN: "穿透",
        en_US: "Penetration Question",
      },
      color: "#FF58A6",
      bg: "rgba(255, 88, 166, 0.08)",
    },
    PROPORTION: {
      name: {
        zh_CN: "滑块",
        en_US: "Proportion",
      },
      color: "#238FFF",
      bg: "rgba(35, 143, 255, 0.08)",
    },
    PROPORTION_MULTIPLE: {
      name: {
        zh_CN: "多级比重",
        en_US: "Proportion Multiple",
      },
      color: "#12C6F9",
      bg: "rgba(18, 198, 249, 0.08)",
    },
    EVALUATION: {
      name: {
        zh_CN: "评价",
        en_US: "Scoring Qusetion",
      },
      color: "#6A5DEF",
      bg: "rgb(106 93 239 / 8%)",
    },
    DESCRIBE: {
      name: {
        zh_CN: "文本/标题",
        en_US: "Text/Title",
      },
      color: "#59C606",
      bg: "rgb(89 198 6 / 8%)",
    },
  };
  requireMap = {
    zh_CN: "必填",
    en_US: "Required",
  };
  noRequireMap = {
    zh_CN: "非必填",
    en_US: "Not required",
  };
  isSpinning: boolean = false;

  // 活动id
  projectId: string;
  //活动状态
  projectType: string;
  // 搜索关键字
  keyWord: string = "";
  // 分页控制
  totalCount: number = 3;
  currentPage: number = 1;
  pageSize: number = 2;

  questionnaireId: string;
  reportType: string;
  // 语言控制
  // lans: any[] = [
  //   { key: 'zh_CN', value: '中文' },
  //   { key: 'en_US', value: 'ENG' },
  // ];
  lan: string = "zh_CN";
  tabSize: string = "small";
  checked = true;
  exportshow = false;
  downLoadShow = false;
  backtype = "";
  uploadshow = false;
  // 批量/删除 -ldx
  deleteIds: any[] = [];
  // 全选 -ldx
  isAllChecked: boolean = false;
  // 全选 -ldx
  disabledAllChecked: boolean = false;

  isedited = false;

  permission: boolean; // 权限判断

  listChecked = "";
  status: string = "";
  tableTop: string = "";
  colSpan: number = 10;

  buttonload = false;

  isOpenValidQuestion: boolean = false;

  Breadcrumbs = [];
  queryParams = {};
  MANY_ITEMS = "VAMPIRESNEW";
  subs = new Subscription();
  constructor(
    private modalService: NzModalService,
    private routeInfo: ActivatedRoute,
    private msg: NzMessageService,
    private router: Router,
    private api: ProjectManageService,
    private surveySerivce: SurveyApiService,
    private el: ElementRef<HTMLElement>,
    private dragulaService: DragulaService,
    private microApp: MicroAppService,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {
    let _this = this;
    dragulaService.createGroup(this.MANY_ITEMS, {
      accepts: function(el: any, container: any, handle: any): any {
        return !_this.isNewQuestionBook();
      },
    });
  }
  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    this.backtype = this.routeInfo.snapshot.queryParams.backtype;
    this.listChecked = this.routeInfo.snapshot.queryParams.listChecked;
    this.projectId = this.routeInfo.snapshot.queryParams.projectId;
    this.projectType = this.routeInfo.snapshot.queryParams.projectType;
    this.questionnaireId = this.routeInfo.snapshot.queryParams.questionnaireId;
    this.reportType = this.routeInfo.snapshot.queryParams.reportType;
    this.Breadcrumbs = JSON.parse(localStorage.getItem("break"));
    let flag = false; //是否已经添加了题本管理面包屑
    this.queryParams = this.routeInfo.snapshot.queryParams;
    this.Breadcrumbs.forEach((item) => {
      if (item.Highlight) {
        if (item.name == "活动管理") {
          item.path = "/project-manage/home";
        }
        if (item.name == "活动设置") {
          item.path = "/new-prisma";
        }
        item.Highlight = false;
      }
      if (item.name == "题本管理") {
        flag = true;
        item.Highlight = true;
      }
    });
    if (!flag) {
      this.Breadcrumbs.push({
        path: "",
        name: "题本管理",
        Highlight: true,
      });
    }

    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    let edittype = this.routeInfo.snapshot.queryParams.edittype;

    if (edittype == "STAND") {
      this.isedited = false;
    } else {
      this.isedited = true;
    }

    if (!this.projectType || this.projectType == "ANNOUNCED") {
      this.downLoadShow = true;
    }

    if (this.projectType == "OVER" || this.projectType == "SUSPEND") {
      this.exportshow = true;
    }
    if (this.permission !== true) {
      if (!this.projectType || this.projectType == "ANNOUNCED") {
        this.uploadshow = true;
      }
    } else {
      if (this.listChecked !== "checked") {
        this.exportshow = false;
        this.uploadshow = true;
      }
    }

    if (this.projectType == "ANSWERING") {
      this.colSpan = this.colSpan - 1;
    }
    if (!this.uploadshow) {
      this.colSpan = this.colSpan - 2;
    }
    this.getProject();
    this.loadData();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      console.log(this.deliveryView.nativeElement.clientHeight);
      this.tableTop = `${this.deliveryView.nativeElement.clientHeight - 300}px`;
    });
    this.subscription = fromEvent(window, "resize")
      .pipe(debounceTime(100)) // 以免频繁处理
      .subscribe((event) => {
        // 这里处理页面变化时的操作
        console.log(this.deliveryView.nativeElement.clientHeight);
        this.tableTop = `${this.deliveryView.nativeElement.clientHeight -
          300}px`;
      });
  }

  ngOnDestroy() {
    // 销毁事件
    this.subscription.unsubscribe();
    this.dragulaService.destroy(this.MANY_ITEMS);
  }

  getProject() {
    this.surveySerivce.getProjectById(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.projectInfo = res.data;
        // 存储高级设置-可选语言
        sessionStorage.setItem(
          "projectLanguages",
          JSON.stringify(this.projectInfo.availableLanguages)
        );
        sessionStorage.setItem("language", this.projectInfo.language);
        this.lan = this.projectInfo.language;
      }
    });
    this.api.getProjectSetting(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.status = res.data.status;
        this.questionnaireId = res.data.questionnaires[0].id;
        this.isOpenValidQuestion = res.data.isOpenValidQuestion;
      }
    });
  }

  dragnumber(e) {
    let ids = [];
    e.map((item) => {
      ids.push(item.id);
    });

    this.surveySerivce.reSort(ids, this.questionnaireId).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("排序保存成功");
      }
    });
  }

  loadData() {
    // table
    // this.getlistByTypesAndName()
    // this.getlistByTypesAndNameRes()
    this.isSpinning = true;
    this.surveySerivce.getQuestionsByProjId(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        // 排序
        // res.data.surveyQuestions.sort(function (a, b) { return a.sort - b.sort });

        this.topicList = _.filter(res.data.surveyQuestions, function(q) {
          return q.type !== "PAGE_SPLIT";
        });
        this.sourceList = this.topicList;

        this.topicList.forEach((item) => {
          if (
            item.name.zh_CN !== item.replaceName.zh_CN ||
            item.name.en_US !== item.replaceName.en_US
          ) {
            item.canEdit = true;
          }
        });
        let flag = this.topicList.findIndex((item) => {
          return item.isForceSelect === false;
        });
        if (flag === -1) {
          this.disabledAllChecked = true;
          this.isAllChecked = false;
        } else {
          this.disabledAllChecked = false;
          this.isAllChecked = false;
        }
      }
      this.isSpinning = false;
    });
  }

  search() {
    let content: string = this.keyWord;

    this.topicList = _.filter(this.sourceList, function(q) {
      let tmpCN: string = q.replaceName.zh_CN;
      let tmpEN: string = q.replaceName.en_US;
      if (!!tmpEN) {
        return tmpCN.indexOf(content) >= 0 || tmpEN.indexOf(content) >= 0;
      } else {
        return tmpCN.indexOf(content) >= 0;
      }
    });
  }

  canEdit() {
    return true;
  }

  // 中英文切换 -ldx
  onSelectI18n(e): void {
    console.log("==================>", e);
    this.lan = e;
  }

  /**
   * 新增 编辑弹窗
   */
  public showModal(id = ""): void {
    if (this.isNewQuestionBook()) {
      this.microApp.navigateByUrl("/customTopic", {
        appName: "sag-tenant",
        params: {
          projectId: this.projectId,
          projectType: this.projectType,
          questionnaireId: this.questionnaireId,
          reportType: this.reportType,
          backtype: this.backtype,
        },
      });
    } else {
      let name = "";
      if (id != "") {
        name = "编辑";
      } else {
        name = "新增";
      }

      const modal = this.modalService.create({
        nzTitle: name,
        nzWidth: 1300,
        nzClassName: "rev-mod",
        nzContent: AddPrismaCustomBookComponent,
        nzComponentParams: {
          nametip: name,
          id: id,
          questionnaireId: this.questionnaireId,
          projectId: this.projectId,
          isedited: this.isedited,
          status: this.status,
          reportType: this.reportType,
        },
      });

      this.modalService.afterAllClose.subscribe(() => {
        this.loadData();
        return;
      });
    }
  }

  /**
   * onEditQuestion 编辑题本
   * @param id
   */
  onEditQuestion(id) {
    this.showModal(id);
  }

  // 删除问题 -ldx
  onDeleteQuestion() {
    if (this.deleteIds.length === 0)
      // return this.msg.warning("请选择想要删除的题目");
      return this.customMsg.open("warning", "请选择想要删除的题目");
    const ids = _.uniq(this.deleteIds);
    this.api.batchDeleteQuestion(ids).subscribe((res) => {
      if (res.result.code == 0) {
        this.msg.success("删除数据成功！");
        this.loadData();
      }
    });
    this.deleteIds = [];
  }

  delete(id: string) {
    this.surveySerivce.deleteQuestionById(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("删除成功");
        this.loadData();
      }
    });
  }
  // 全选
  checkAll(e) {
    // 全选
    if (e) {
      this.topicList.forEach((item) => {
        item.isDeleted = true;
      });
      this.deleteIds = this.topicList.map((item) => item.id);
    } else {
      // 取消全选
      this.topicList.forEach((item) => {
        item.isDeleted = false;
      });
      this.deleteIds = [];
    }
    console.log("deleteIds", this.deleteIds);
  }

  checkOne() {
    this.deleteIds = this.topicList
      .filter((item) => item.isDeleted)
      .map((item) => item.id);
    let flag = this.topicList.every((item) => {
      return item.isDeleted === true;
    });
    this.isAllChecked = flag;
  }

  edit(questionModel: any) {
    const modal = this.modalService.create({
      // nzTitle: `题目修订`,
      nzTitle: null,
      nzFooter: null,
      nzWidth: 800,
      nzContent: SubjectEditComponent,
      nzComponentParams: {
        subjectModel: questionModel,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child: SubjectEditComponent = modal.getContentComponent();
        let param = child.paramJson;
        this.surveySerivce.replaceQuestion(param).subscribe((res) => {
          if (res.result.code === 0) {
            // update local
            let tmp = _.find(this.topicList, { id: questionModel.id });
            tmp.name = param.surveyQuestion.name;
            this.msg.success("修订成功。");
            modal.close();
          }
        });
        return false;
      },
    });

    modal.afterClose.subscribe((result) => {
      this.loadData();
    });
  }

  batchEdit() {
    let tmpList = _.filter(this.topicList, function(q) {
      return q.canEdit;
    });

    const modal = this.modalService.create({
      nzTitle: null,
      nzFooter: null,
      nzWidth: 800,
      nzContent: SubjectBatchEditComponent,
      nzComponentParams: {
        modelList: tmpList,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzOnOk: () => {
        const child: SubjectBatchEditComponent = modal.getContentComponent();
        let param = child.editWidget.paramJson;
        this.surveySerivce.replaceQuestion(param).subscribe((res) => {
          if (res.result.code === 0) {
            this.msg.success("批量修订成功。");
            modal.close();
          }
        });
        return false;
      },
    });

    modal.afterClose.subscribe((result) => {
      this.loadData();
    });
  }

  bookConfirm() {
    let data = {
      projectId: this.projectId,
      questionnaireId: this.questionnaireId,
      type: "QUESTION_BOOK",
      isConfirmed: true,
    };
    this.surveySerivce.confirmRelation(data).subscribe((res) => {
      if (res.result.code === 0) {
        if (this.backtype == "home") {
          this.router.navigateByUrl("/project-manage/home");
        } else {
          localStorage.setItem("backurl", this.router.routerState.snapshot.url);
          this.router.navigate(["/new-prisma"], {
            queryParams: {
              projectId: this.projectId,
              type: this.projectType,
              questionnaireId: this.questionnaireId,
            },
          });
        }
      }
    });
  }

  config() {
    const modal = this.modalService.create({
      nzTitle: `题本设置`,
      nzFooter: null,
      nzWidth: 500,
      nzContent: BookSettingComponent,
      nzComponentParams: {
        questionnaireId: this.questionnaireId,
      },
      nzOkText: "保存",
      nzCancelText: "取消",
      nzMaskClosable: false,
      nzOnOk: () => {
        let child: BookSettingComponent = modal.getContentComponent();
        if (child.defaultList.length === 0) {
          // this.msg.warning("没有选择填答语言。");
          this.customMsg.open("warning", "没有选择填答语言");
          return false;
        }
        if (!child.radioValue) {
          // this.msg.warning("没有选择默认语言。");
          this.customMsg.open("warning", "没有选择默认语言");
          return false;
        }
        return true;
      },
    });
  }

  exportQusBook() {
    this.buttonload = true;
    this.surveySerivce
      .exportPrismaQuestionBook(this.questionnaireId)
      .subscribe((res) => {
        const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
        let fileName = res.headers
          .get("Content-Disposition")
          .split(";")[1]
          .split("filename=")[1];
        const fileNameUnicode = res.headers
          .get("Content-Disposition")
          .split("filename*=")[1];
        // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
        if (fileName) {
          fileName = decodeURIComponent(fileName);
        }
        if (fileNameUnicode) {
          fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
        }
        const link = document.createElement("a");
        link.setAttribute("href", URL.createObjectURL(blob));
        link.setAttribute("download", fileName);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        this.buttonload = false;
      });
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.surveySerivce
      .uploadPrismaCustomQuestionBook(formData, this.questionnaireId)
      .subscribe(
        (event: HttpEvent<any>) => {
          item.onSuccess!();
          let res: any = event;
          if (res.result.code === 0) {
            this.msg.success("导入文件成功");
            this.getProject();
            this.loadData();
          }
        },
        (err) => {
          item.onError!(err, item.file!);
        }
      );
  }
  /**
   * 是否跳转到新的题本编辑页面
   *@author:wangxiangxin
   *@Date:2023/08/25
   */
  isNewQuestionBook() {
    // let listtype = ['INVESTIGATION_RESEARCH_CUSTOM', 'TENCENT_INVESTIGATION_RESEARCH_CUSTOM', 'VIVO_INVESTIGATION_RESEARCH_CUSTOM','EPSON_INVESTIGATION_RESEARCH_CUSTOM','NETEASE_INVESTIGATION_RESEARCH_CUSTOM']
    // return listtype.includes(this.reportType)
    return this.reportType.indexOf("INVESTIGATION_RESEARCH_CUSTOM") !== -1;
  }

  // 进入子应用-自定义调研
  goSubCustom(item, data = null) {
    let params = {
      projectId: this.projectId,
      projectType: this.projectType,
      questionnaireId: this.questionnaireId,
      reportType: this.reportType,
      backtype: this.backtype,
      questionId: item.id,
    };
    if (data) {
      params["parentQuestionId"] = data.id;
    }
    this.microApp.navigateByUrl("/customTopic", {
      appName: "sag-tenant",
      params,
    });
  }
  /** 右边区域拖拽触发事件 */
  noReturnPredicate = () => {
    if (this.isNewQuestionBook()) {
      return false;
    }
    return true;
  };
}
