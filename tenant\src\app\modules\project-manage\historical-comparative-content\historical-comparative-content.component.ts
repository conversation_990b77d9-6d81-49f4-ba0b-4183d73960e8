import { Component, OnInit } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { HttpEvent } from "@angular/common/http";

import { NzMessageService, UploadXHRArgs, NzModalService } from "ng-zorro-antd";

import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { ProjectManageService } from "../../service/project-manage.service";
import { HistoryModalContentComponent } from "./history-modal-content/history-modal-content.component";
import { MessageService } from "@src/shared/custom-message/message-service.service";
@Component({
  selector: "app-historical-comparative-content",
  templateUrl: "./historical-comparative-content.component.html",
  styleUrls: ["./historical-comparative-content.component.less"],
})
export class HistoricalComparativeContentComponent implements OnInit {
  validateForm: FormGroup;
  constructor(
    private api: ProjectManageService,
    private activatedRoute: ActivatedRoute,
    private fb: FormBuilder,
    private msg: NzMessageService,
    private router: Router,
    private modalService: NzModalService,
    private customMsg: MessageService
  ) {}
  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/project-manage",
      name: "活动管理",
      Highlight: false,
    },
    {
      path: "",
      name: "历史对比",
      Highlight: true,
    },
  ];
  projectId: null;
  historyId: null;
  exportIds: any[] = [];
  switchHistoryType: boolean;

  isAllDisplayDataChecked: boolean = false; // 全选
  isIndeterminate: boolean = false; // 多选动画

  historicalList: any[]; // 历史对比列表
  isOkLoading: boolean = false; // modal加载状态
  formData = new FormData(); //文件数据
  modalType: string = "新增"; // 弹窗title

  lan: string = "zh_CN";

  ngOnInit() {
    this.validateForm = this.fb.group({
      name: [null, [Validators.required]],
      enName: [null, []],
    });
    this.getParams();
    this.getHistoryList();
  }

  getParams() {
    this.activatedRoute.queryParams.subscribe((res) => {
      this.projectId = res.projectId;
    });
  }

  getHistoryList() {
    this.exportIds = [];
    this.api.getPrismaHistoryData(this.projectId).subscribe((res) => {
      this.historicalList = res.data;
      this.historicalList.forEach((item) => {
        item.checked = false;
      });
    });
  }

  checkAll(e) {
    this.exportIds = [];
    this.isIndeterminate = false;
    this.historicalList.map((item) => {
      item.checked = e;
    });
    this.historicalList.map((item) => {
      if (item.checked) {
        this.exportIds.push(item.id);
      }
    });
  }

  refreshStatus(e) {
    this.exportIds = [];
    this.historicalList.map((item) => {
      if (item.checked) {
        this.exportIds.push(item.id);
      }
    });
    if (this.exportIds.length === this.historicalList.length) {
      this.isAllDisplayDataChecked = true;
      this.isIndeterminate = false;
      return;
    } else {
      if (this.exportIds.length > 0) {
        this.isAllDisplayDataChecked = false;
        this.isIndeterminate = true;
      } else {
        this.isAllDisplayDataChecked = false;
        this.isIndeterminate = false;
      }
    }
  }

  edit(hisId?) {
    // 新增 / 编辑历史调研名称
    const params: any = {
      projectId: this.projectId,
      hisId: hisId,
    };

    this.router.navigate(["/project-manage/add-historical-comparison"], {
      queryParams: params,
    });
  }

  deleteHis(id: string) {
    // 删除历史对比
    this.api.deletePrismaHistoryData(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("当前历史数据已删除");
        this.getHistoryList();
      }
    });
  }

  switchHistory(e, id: string) {
    this.api.switchPrismaHistoryStatus(id).subscribe((res) => {
      // this.getHistoryList()
      if (res.result.code === 0) {
        this.msg.success("状态切换成功");
      } else {
        this.getHistoryList();
      }
    });
  }

  updateHis(data: any) {
    console.log(data);
    const params = {
      id: data.id,
      memo: data.memo,
      name: data.name,
    };
    this.api.updatePrismaHistoryData(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("修改成功");
      }
      this.getHistoryList();
    });
  }

  deleteHistoryData(e, id) {
    console.log(e);

    this.api.deletePrismaHistoryData(id).subscribe((res) => {
      this.getHistoryList();
    });
  }

  export(id) {
    this.api.exportMatchMappingResult(id).subscribe((res) => {
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }

  exportMore() {
    // if(this.exportIds.length === 0) return this.msg.warning('请选择要下载的报表')
    if (this.exportIds.length === 0)
      return this.customMsg.open("warning", "请选择要下载的报表");
    this.exportIds.map((id) => {
      this.export(id);
    });
  }

  uploadId(id) {
    this.historyId = id;
  }

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item) {
    return this.api.uploadPrismaHistoryData(formData, this.historyId).subscribe(
      (event: HttpEvent<any>) => {
        item.onSuccess!();
        let res: any = event;
        if (res.result.code === 0) {
          this.msg.success("导入文件成功");
          this.getHistoryList();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item);
  };

  back() {
    this.router.navigateByUrl("/project-manage/home");
  }

  onSelectI18n(e) {
    this.lan = e;
  }
}
