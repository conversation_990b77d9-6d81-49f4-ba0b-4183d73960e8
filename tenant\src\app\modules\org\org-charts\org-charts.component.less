// :host ::ng-deep [node-id='1547840939770839041'] rect {
//   fill: red;
// }
@min: 0;
@max: 255;
@int: 1;

@num: `Math.ceil(Math.random() * (@{min} - @{max} + @{int}) + @{max} - @{int}) `;

:host ::ng-deep .node .field_0 {
  width: 100%;
  fill: black;
}
:host ::ng-deep .node .field_1 {
  width: 800px;
  fill: black;
}
:host ::ng-deep .node .field_3 {
  position: relative;

  &.DISABLE {
    &::after {
      position: relative;
      content: "禁用";
      font-size: 12px;
      font-weight: 400;
      border-radius: 2px;
      padding: 2px 5px;
      color: #8c8c8c;
      white-space: nowrap;
      background: rgba(140, 140, 140, 0.1);
      margin-left: 10px;
    }
  }
}
:host ::ng-deep [sl="0"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="1"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="2"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="3"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="4"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="5"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="6"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="7"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="8"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="9"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="10"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="11"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep [sl="12"] rect {
  fill: rgb(@num, @num, @num);
}
:host ::ng-deep #tree hr:nth-child(2) {
  margin: 6px 0;
}
.empty {
  display: flex;
  align-items: center;
  justify-content: center;
}
