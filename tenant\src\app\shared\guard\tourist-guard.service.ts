import { Injectable, Inject } from "@angular/core";
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot, CanActivateChild
} from "@angular/router";
import { SocialService, SocialOpenType, ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { HttpClient } from "@angular/common/http";
import { ReuseTabService } from "@knz/assembly";
import { NzMessageService } from "ng-zorro-antd";

@Injectable({
  providedIn: "root"
})
export class TouristGuardService implements CanActivate, CanActivateChild {
  constructor(
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private router: Router,
    private http: HttpClient,
    @Inject(ReuseTabService)
    private reuseTabService: ReuseTabService,
    private msg: NzMessageService,
  ) {
  }
  canActivate(_route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    return this.checkLogin();
  };

  canActivateChild(_route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    return this.checkLogin();
  };

  checkLogin(): boolean {
    const token = this.tokenService.get().token;
    // 如果token有值，表示登录成功，继续跳转，否则跳转到首页
    if (token) {
      return true;
    }
    // this.router.navigate(["/user/login"]);
    return true;
  }

}

