<div class="container">
  <div class="left scroll">
    <div
      class="nair"
      [ngClass]="{ currentNair: item.isCurrent }"
      *ngFor="let item of createList"
      (click)="clickProject(item.project.id)"
    >
      <div class="line1">
        <span
          class="text0"
          nz-tooltip
          [nzTitle]="item.project?.projectName?.zh_CN"
          nzPlacement="topLeft"
          >{{ item.project?.projectName?.zh_CN }}</span
        >
        <label nz-checkbox [(ngModel)]="item.isSelect"></label>
      </div>
      <div class="line2">
        <span class="text1 autoTxt">机出报告</span>
      </div>
      <div class="line3">
        <span class="text3">售价：{{ item.extInfo?.cost }} K米/份</span>
      </div>
    </div>
  </div>

  <ng-container *ngFor="let item of createList">
    <div class="right" [hidden]="item.project.id !== currentProjId">
      <app-single-survey [paramData]="item"></app-single-survey>
    </div>
  </ng-container>
</div>
