* {
    margin: 0;
    padding: 0;
}

.taskCard {
    margin-top: 15px;
    width: 160px;
    height: 90px;
    background: #FFFFFF;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    &_title{
        display: flex;
        justify-content: center;
        align-items: center;
        .confirmed{
            width: 14px;
            height: 14px;
            background-color: #409EFF;
            border-radius: 2px;
            border: 1px solid #409EFF;
            display: flex;
            justify-content: center;
            align-items: center;
            >i{
                font-size: 10px;
                color: #fff;
            }
            margin-right: 6px;
        }
        .unConfirmed{
            width: 14px;
            height: 14px;
            border-radius: 2px;
            border: 1px solid #E6E6E6;
            margin-right: 6px;

        }
        span{
            font-size: 14px;
            font-weight: 400;
            color: #495970;
            max-width: 126px;
            overflow:hidden;
            text-overflow:ellipsis;
            white-space:nowrap;
        }
    }
    &_btn{
        margin-top: 10px;
        display: flex;
        justify-content: center;
        span{
            padding: 5px 14px;
            background: #FFFFFF;
            font-size: 14px;
            line-height: 18px;
            font-weight: 500;
            color: #409EFF;
            border-radius: 15px;
            border: 1px solid #409EFF;
            max-width: 130px;
            overflow:hidden;
            text-overflow:ellipsis;
            white-space:nowrap;
            cursor: pointer;
        }
    }
}