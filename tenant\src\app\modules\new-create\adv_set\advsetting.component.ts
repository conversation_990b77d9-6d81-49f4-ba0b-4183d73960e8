// 360/测评高级设置
import {
  Component,
  Input,
  OnInit,
  ChangeDetectorRef,
  Inject,
  OnDestroy,
} from "@angular/core";
import { UploadFile, UploadXHRArgs } from "ng-zorro-antd/upload";
import { Observable, Observer, Subscription } from "rxjs";
import { NewPrismaService } from "../../new-prisma/new-prisma.service";
import { NzDrawerRef, NzMessageService } from "ng-zorro-antd";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { HttpClient, HttpEvent, HttpHeaders } from "@angular/common/http";
import { ActivatedRoute, Router, NavigationEnd } from "@angular/router";
import { NewCreateService } from "../../new-create/new-create.service";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import { PermissionService } from "@src/modules/service/permission-service.service";

// interface RolePersons {
//   roleId:string;
//   roleName: {
//     zh_CN: string;
//     en_US: string;
//     [key: string]: string;
//   };
//   validNum: number;
// }

// interface AnswerRolePersonNum {
//   validNum: number;
//   rolePersons: RolePersons[];
// }
interface ScaleCustomitem {
  roleId: string;
  roleName: {
    zh_CN: string;
    en_US: string;
  };
  rate: number;
}
interface ProportionCustomItem {
  roleId: string;
  roleName: {
    zh_CN: string;
    en_US: string;
  };
  score1: number;
  score2: number;
  score3: number;
  score4: number;
  rate: number;
}
interface AnswerSameRateCustom {
  scaleCustoms: ScaleCustomitem[];
  proportionCustoms: ProportionCustomItem[];
}

@Component({
  selector: "app-set",
  templateUrl: "./advsettingNew.component.html",
  styleUrls: ["./advsettingNew.component.less"],
})
export class AdvancedMoreSetting implements OnInit, OnDestroy {
  @Input() listdata;
  @Input() projecttype;
  @Input() reportType: string;
  @Input() projectStatus: string;
  @Input() oIsEmailReport;
  @Input() isUpdateing;
  @Input() disabledPagesAndSort?: boolean = false;
  @Input() reportTypeList?: string[] = [];

  // 活动ID
  @Input() projectId: string;

  isAllowedView = true;
  tenantApi: string = "/tenant-api";
  routProjectType = null;

  // 高级设置评价关系
  mm: number = 0;
  ss: number = 1;
  isInviteAnswer: boolean = false; // 被评估人(评分对象)自行邀请
  isInviteReviewEvaluatee: boolean = false; // 审核被评估人(评分对象)邀请
  // todo: 角色数量设置去除#7336
  // disableLimit: boolean = false
  // min: number = null
  // max: number = null

  Isactive = true;
  loading;
  avatarUrl;
  PcavatarUrl;
  MbavatarUrl;
  formData = new FormData(); //文件数据
  disanswerMode = false;
  // 页面loading遮罩状态
  isloading = false;
  settingData: any = {
    logoFile: "",
    pcBackgroundPic: "",
    mobileBackgroundPic: "",
    isShowKnxLogo: true, //logo
    isShowBackgroundPic: false,
    answerEffectiveRange: "1",
    answerSameRate: "100", // 一致性
    // 任意角色的有效填答人数
    answerRolePersonNum: {
      validNum: 0,
      rolePersons: [],
    },
    // 填答一致性-自定义设置
    answerSameRateCustom: {
      scaleCustoms: [],
      proportionCustoms: [],
    },
    answerEffectiveTime: "1", //默认时间
    questionNumInOnePage: "2",
    isCheckLicenseNotice: true, //许可声明
    isEnableRoleDimension: false,
    isEnableWelcomePage: false,
    isCheckedAnswerValid: false, // 不满足填答有效性不可提交
    welcomePage: {
      zh_CN: "",
      en_US: "",
    },
    endPage: {
      zh_CN: "",
      en_US: "",
    },
    isInviteAnswer: false,
    isInviteReviewEvaluatee: false,
    inviteAnswerSetting: {},
    isEnableEndPage: false,
    isPublicReport: false, //查看报告
    isEmailReport: false, //查看报告
    sequence: "QUESTION_TYPE",
    isCustomRoleWeight: false,
    answerMode: "MULTI",
    isHideRole: false,
    isShowRank: true,
    isShowScore: true,
    isShowUnderstand: true,
    language: "zh_CN", // 填答默认语言
    availableLanguages: ["zh_CN", "en_US"], // 新增语言
    optionalLanguages: ["zh_CN", "en_US"], // 填答可选语言
    isCheckAnswerInstructions: true, //作答说明
    isShowDemographicQuestion: true, //是否显示人口信息学题
    standardDemographicDTO: {
      standardDemographicIds: [],
      type: "DEMOGRAPHIC_DEFAULT", // 人口学 默认
    },
    // 关联任务
    isEnableQuestionBookDistribute: false, // 是否开启题本分发
    isEnableScaleExtend: false, //  是否开启量表扩展
    isQuestionCustomSort: false, //每页题数类型,默认false
    isAutoPage: true, //  一页一题时，自动翻页
    isAutoFillQuestionBook: false, // 题本分发致页面题目有缺时，自动补位后续题本
    // #12084 快速互评
    isQuickMutualEvaluation: false, // 是否开启快速互评
  };
  // projectLangOptions = [
  //   {
  //     value: "zh_CN",
  //     name: "中文",
  //     checked: false,
  //   },
  //   {
  //     value: "en_US",
  //     name: "英文",
  //     checked: false,
  //   },
  // ];
  projectLangOptions = []; // 填答可选语言项/当前活动的
  permission; // 管理员权限
  locwelcomepage = false;
  locengpage = false;
  wlcomecontant = "";
  Isactivelan = true;
  pagename = "";
  tinyconfig = {};
  precision = 0;
  precision_min = 0;
  // 关联任务
  isEnableQuestionBookDistribute: boolean = false; // 是否开启题本分发
  isEnableScaleExtend: boolean = false; //  是否开启量表扩展


  tableIndex: number = 0; // tabIndex
  loceng_locwelcome_lan = "zh_CN";
  // 角色的有效填答人数设置
  answerRolePersonNumVisible: boolean;
  // 填答一致性设置
  answerSameRateVisible: boolean;
  // 填答一致性-自定义设置
  // answerSameRateCustom: AnswerSameRateCustom = {
  //   scaleCustoms: [],
  //   proportionCustoms: [],
  // };
  private routerSubscription: Subscription;
  constructor(
    private cdr: ChangeDetectorRef,
    private msg: NzMessageService,
    private api: NewPrismaService,
    private new_create_service: NewCreateService,
    private drawerRef: NzDrawerRef,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private HttpClient: HttpClient,
    public activatedRoute: ActivatedRoute,
    private customMsg: MessageService,
    private router: Router,
    public knxFunctionPermissionService: KnxFunctionPermissionService,
    public permissionService: PermissionService
  ) {}

  changeHidden(e, item, index) {
    e.stopPropagation();
    item.isHidden = !item.isHidden;
  }

  ngOnInit() {
    this.lansFilter_cnen = _.cloneDeep(this.defaultLangOptions);
    this.projectLangOptions = _.cloneDeep(this.defaultLangOptions);
    this.activatedRoute.queryParams.subscribe((queryParams) => {
      this.routProjectType = queryParams.projectType;
      this.projectId = queryParams.projectId;
    });
    const _this = this;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.tinyconfig = {
      height: 270,
      fontsize_formats:
        "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 32pt 36pt",
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      toolbar: [
        "removeformat undo redo formatselect fontsizeselect",
        "bold italic underline strikethrough forecolor backcolor",
        "bullist numlist alignleft aligncenter alignright alignjustify outdent indent image link",
      ],
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      images_upload_url: `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`, // 配置你图片上传的url
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.HttpClient.post(
          `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`,
          formData,
          { headers: headers }
        ).subscribe(
          (response: any) => {
            if (response) {
              this.HttpClient.get(
                `${this.tenantApi}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                { headers: headers }
              ).subscribe((imgurl: any) => {
                let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                // if ( environment.dev ) {
                //    url = environment.SERVER_URL.substr(0, environment.SERVER_URL.length - 1)  + imgurl.data.url; // 这里是你获取图片url
                // } else {
                //   url = 'api' + imgurl.data.url; // 这里是你获取图片url
                // }
                // 把图片链接，img src标签显示图片的有效链接放到下面回调函数里
                success(url);
              });
            } else {
              if (response && response.rtnMsg) {
                failure(response.rtnMsg);
              } else {
                failure("上传失败：未知错误");
              }
            }
          },
          (error1) => {
            failure("上传失败：未知错误");
          }
        );
      },
    };
    if (this.reportType && this.reportType.indexOf("_270") !== -1) {
      this.isAllowedView = false;
    }
    this.permission = this.permissionService.isPermission();
    this.settingData = this.listdata;
    this.mm = parseInt(((this.settingData.answerEffectiveTime / 60) % 60) + "");
    this.ss = parseInt((this.settingData.answerEffectiveTime % 60) + "");
    // 关联任务设置
    this.isEnableQuestionBookDistribute = this.settingData.isEnableQuestionBookDistribute;
    this.isEnableScaleExtend = this.settingData.isEnableScaleExtend;
    // console.log(this.listdata)
    this.avatarUrl = this.listdata.logoFile
      ? `${baseUrl}api/file/www/${this.listdata.logoFile}`
      : null;
    this.PcavatarUrl = this.listdata.pcBackgroundPic
      ? `${baseUrl}api/file/www/${this.listdata.pcBackgroundPic}`
      : null;
    this.MbavatarUrl = this.listdata.mobileBackgroundPic
      ? `${baseUrl}api/file/www/${this.listdata.mobileBackgroundPic}`
      : null;

    if (this.settingData.isInviteAnswer)
      this.isInviteAnswer = this.settingData.isInviteAnswer;
    if (this.settingData.isInviteReviewEvaluatee)
      this.isInviteReviewEvaluatee = this.settingData.isInviteReviewEvaluatee;

    // todo: 角色数量设置去除#7336
    // if (this.reportType.indexOf("_360") !== -1) {
    //   !!this.settingData.inviteAnswerSetting.min
    //     ? (this.min = this.settingData.inviteAnswerSetting.min)
    //     : (this.min = null);
    //   !!this.settingData.inviteAnswerSetting.max
    //     ? (this.max = this.settingData.inviteAnswerSetting.max)
    //     : (this.max = null);
    //   if (this.settingData.inviteAnswerSetting.disableLimit)
    //     this.disableLimit = this.settingData.inviteAnswerSetting.disableLimit;
    // }

    this.settingData.optionalLanguages.forEach((res) => {
      this.projectLangOptions.forEach((val) => {
        if (res == val.value) {
          val.checked = true;
        }
      });
    });
    if (this.projecttype === "360") {
      // 角色的有效填答人数
      this.settingData.answerRolePersonNum.rolePersons.forEach((val) => {
        if (!val.validNum) {
          val.validNum = null;
        }
      });
      // this.answerSameRateCustom.scaleCustoms = this.settingData.answerSameRateCustom.scaleCustoms.map(
      //   (val) => ({
      //     roleId: val.roleId,
      //     roleName: val.roleName,
      //     rate: val.rate ,
      //   })
      // );
      // // 填答一致性-自定义设置
      // this.answerSameRateCustom.proportionCustoms = this.settingData.answerSameRateCustom.proportionCustoms.map(
      //   (val) => ({
      //     roleId: val.roleId,
      //     roleName: val.roleName,
      //     rate: val.rate,
      //     score1: val.score1,
      //     score2: val.score2,
      //     score3: val.score3,
      //     score4: val.score4,
      //   })
      // );
    }
    this.getLanOptions();
    console.log("填答一致性-自定义设置", this.settingData.answerSameRateCustom);
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
  // 高级设置 》 评价关系
  changeIsVisitAnswer(e) {
    this.isInviteAnswer = e;
    this.settingData.isInviteAnswer = e;
    if (e) {
      this.settingData.isQuickMutualEvaluation = false;
    }
    // 审核人联动处理 #7466
    if (!e) {
      this.changeIsInviteReviewEvaluatee(e);
    }
  }
  // 高级设置 》 评价关系
  changeIsInviteReviewEvaluatee(e) {
    this.isInviteReviewEvaluatee = e;
    this.settingData.isInviteReviewEvaluatee = e;
  }
  // todo: 角色数量设置去除#7336
  // changeMin(e) {
  //   if(e) {
  //     this.min = e
  //     if(e !== Math.trunc(e)) {
  //       this.cdr.detectChanges()
  //       this.min = Math.trunc(e)
  //       this.msg.error('请输入整数')
  //       return
  //     }
  //   }
  // }
  // changeMax(e) {
  //   if(e) {
  //     this.max = e
  //     if(e !== Math.trunc(e)) {
  //       this.cdr.detectChanges()
  //       this.max = Math.trunc(e)
  //       this.msg.error('请输入整数')
  //       return
  //     }
  //   }
  // }
  // changeDisableLimit(e) {
  //   if(e == true) {
  //     this.min = null
  //     this.max = null
  //   }
  // }

  changeIsShowDemographicQuestion(boo: boolean) {
    // if(boo){
    //   this.msg.success('已开启人口标签页')
    // }else{
    //   this.msg.error('已关闭人口标签页')
    // }
    if (!boo) this.settingData.isShowPreAnswer = false;
  }

  onChangeTime(e, type) {}
  getdefaultTime(e, type) {}

  mChange(e) {
    if (!this.ss && !e) {
      // this.msg.error("请输入有效时间！");
      this.customMsg.open("error", "请输入有效时间！");
      return;
    }
    if (!!e && !this.ss) {
      this.ss = 0;
    }
  }

  sChange(e) {
    if (!this.mm && !e) {
      // this.msg.error("请输入有效时间！");
      this.customMsg.open("error", "请输入有效时间！");
      return;
    }
    if (e > 60) {
      this.mm = parseInt(((e / 60) % 60) + "");
      this.ss = parseInt((e % 60) + "");
    }
    if (!!e && !this.mm) {
      this.mm = 0;
    }
  }

  funsequence(e) {}

  getActive(e) {
    if (e == "active") {
      this.Isactive = true;
    } else {
      this.Isactive = false;
    }
  }

  getSaveSet() {
    // todo: 角色数量设置去除#7336
    // if(this.isInviteAnswer) {
    //   if(!this.disableLimit) {
    //     if(!Number.isInteger(this.min) || !Number.isInteger(this.max)) {
    //       this.msg.error('人数区间必须为整数')
    //       return
    //     }
    //     if(!this.min || !this.max) {
    //       this.msg.error('请输入邀请人数区间')
    //       return
    //     }
    //     if(this.min >= this.max || this.max <= this.min) {
    //       this.msg.error('邀请人数区间填写错误')
    //       return
    //     }
    //   }
    // }

    // this.settingData.inviteAnswerSetting = {
    //   min: this.min,
    //   max: this.max,
    //   disableLimit: this.disableLimit
    // }

    if (this.settingData.name.en_US.length > 100) {
      // this.msg.error("活动名称不能超过100个字符！");
      this.customMsg.open("error", "活动名称不能超过100个字符！");
      // this.settingData.name.en_US = "";
      return;
    }

    if (
      this.settingData.name.en_US.indexOf("/") != -1 ||
      this.settingData.name.en_US.indexOf("\\") != -1
    ) {
      // this.msg.error("活动名称包含非法字符'/''\\'！");
      this.customMsg.open("error", "活动名称包含非法字符'/''\\'！");
      // this.settingData.name.en_US = "";
      return;
    }

    if (this.settingData.optionalLanguages.length == 0) {
      // this.msg.error("填答时可选语言至少选一个！");
      this.customMsg.open("error", "结束页内容不能为空！");

      return;
    }
    if (this.settingData.isEnableEndPage) {
      if (this.settingData.endPage.zh_CN == "") {
        // this.msg.error("结束页内容不能为空！");
        this.customMsg.open("error", "结束页内容不能为空！");

        return;
      }
    }
    if (this.settingData.isEnableWelcomePage) {
      if (this.settingData.welcomePage.zh_CN == "") {
        // this.msg.error("欢迎页内容不能为空！");
        this.customMsg.open("error", "欢迎页内容不能为空！");
        return;
      }
    }
    if (this.settingData.isShowBackgroundPic) {
      if (!this.PcavatarUrl && !this.MbavatarUrl) {
        // this.msg.error("请至少上传一个填答背景");
        this.customMsg.open("error", "请至少上传一个填答背景");
        return;
      }
    }
    this.settingData.answerEffectiveTime = this.mm * 60 + this.ss;
    // 关联任务
    this.settingData.isEnableQuestionBookDistribute = this.isEnableQuestionBookDistribute;
    this.settingData.isEnableScaleExtend = this.isEnableScaleExtend;
    let that = this;
    that.drawerRef.close(this.settingData);
  }
  optionalLan(e) {
    this.settingData.optionalLanguages = e.sort((a, b) => {
      if (a === "zh_CN") return -1;
      if (b === "zh_CN") return 1;
      if (a === "en_US") return -1;
      if (b === "en_US") return 1;
      return 0;
    });
  }

  getDefault() {
    //活动设置
    if (
      this.permissionService.isPermissionOrSag(
        "SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PROJECT_SETTING"
      )
    ) {
      this.settingData.isPublicReport = false; //查看报告
    }
    this.settingData.name.en_US = "";
    this.settingData.isCheckAnswerInstructions = true;
    this.settingData.isCheckLicenseNotice = true;
    this.settingData.isEnableWelcomePage = false;
    this.settingData.isCheckedAnswerValid = false;
    this.settingData.isEnableEndPage = false;
    this.settingData.welcomePage = {
      zh_CN: "",
      en_US: "",
    };
    this.settingData.endPage = {
      zh_CN: "",
      en_US: "",
    };
    this.settingData.isShowKnxLogo = true;
    this.settingData.isShowBackgroundPic = false;
    this.settingData.logoFile = "";
    this.settingData.pcBackgroundPic = "";
    this.settingData.mobileBackgroundPic = "";
    this.settingData.isEmailReport = false; //查看报告
    this.avatarUrl = "";
    //发布设置
    if (
      this.permissionService.isPermissionOrSag(
        "SAG:TENANT:PROJECT_MGT:CREATE_ADVANCED_SETTING:PROJECT_SETTING"
      )
    ) {
      this.settingData.isCustomRoleWeight = false;
      this.settingData.isInviteAnswer = false;
      this.isInviteAnswer = false;
      this.settingData.isInviteReviewEvaluatee = false;
      this.isInviteReviewEvaluatee = false;
      this.mm = 0;
      this.ss = 1;
      this.settingData.answerEffectiveTime = "1";
      this.settingData.answerEffectiveRange = "1";
      this.settingData.answerSameRate = "100";
      // 角色的有效填答人数
      if (this.projecttype === "360") {
        this.settingData.answerRolePersonNum.validNum = 0;
        this.settingData.answerRolePersonNum.rolePersons.forEach((val) => {
          val.validNum = null;
        });
        // 填答一致性-自定义设置
        this.settingData.answerSameRateCustom.scaleCustoms.forEach((val) => {
          val.rate = null;
        });
        this.settingData.answerSameRateCustom.proportionCustoms.forEach(
          (val) => {
            val.rate = null;
            val.score1 = null;
            val.score2 = null;
            val.score3 = null;
            val.score4 = null;
          }
        );
      }
    }
    this.settingData.questionNumInOnePage = this.disabledPagesAndSort
      ? this.settingData.questionNumInOnePage
      : "5";
    this.settingData.inviteAnswerSetting = {};
    // todo: 角色数量设置去除#7336
    // this.min = null
    // this.max = null
    // this.disableLimit = false
    this.settingData.sequence = this.disabledPagesAndSort
      ? this.settingData.sequence
      : "QUESTION_TYPE";
    this.settingData.language = "zh_CN";
    this.settingData.optionalLanguages = ["zh_CN"];
    // 关联任务
    this.isEnableQuestionBookDistribute = false;
    this.isEnableScaleExtend = false;
    this.settingData.optionalLanguages.forEach((res) => {
      this.projectLangOptions.forEach((val) => {
        val.checked = false;
        if (res == val.value) {
          val.checked = true;
        }
      });
    });
    this.addCodes = [];
    //发布设置结束

    this.settingData.isShowDemographicQuestion = true;
    this.settingData.isShowDemographicQuestion = true;
    this.settingData.isShowPreAnswer = true;
    // this.settingData.isEnableRoleDimension = false
    this.settingData.answerMode = "MULTI";
    this.settingData.isHideRole = false;
    this.settingData.isShowRank = true;
    this.settingData.isShowScore = true;
    this.settingData.isShowUnderstand = true;
    this.settingData.standardDemographicDTO = {
      standardDemographicIds: [],
      type: "DEMOGRAPHIC_DEFAULT", // 人口学 默认
    };
  }

  /**
   * beforeUpload 校验上传的图片格式
   * @param file
   */
  beforeUpload = (file: File) => {
    return new Observable((observer: Observer<boolean>) => {
      // file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/gif' || file.type === 'image/bmp'
      const isPNG = file.type === "image/png";
      const isSize = file.size / 1024 < 500;
      if (!isPNG) {
        // this.msg.error("文件类型不合法,只能是png类型！");
        this.customMsg.open("error", "文件类型不合法,只能是png类型！");
        observer.complete();
        return;
      }
      if (!isSize) {
        // this.msg.error("图片应小于500KB!");
        this.customMsg.open("error", "图片应小于500KB！");
        observer.complete();
        return;
      }

      observer.next(isPNG && isSize);
      observer.complete();
    });
  };

  /**
   * beforeUpload 校验上传的图片格式
   * @param file
   */
  beforeUpload1 = (file: File) => {
    return new Observable((observer: Observer<boolean>) => {
      // file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/gif' || file.type === 'image/bmp'
      const isPNG = file.type === "image/png";
      const isSize = file.size / 1024 < 300;
      if (!isPNG) {
        // this.msg.error("文件类型不合法,只能是png类型！");
        this.customMsg.open("error", "文件类型不合法,只能是png类型！");
        observer.complete();
        return;
      }
      if (!isSize) {
        // this.msg.error("图片应小于300KB!");
        this.customMsg.open("error", "图片应小于300KB！");
        observer.complete();
        return;
      }

      observer.next(isPNG && isSize);
      observer.complete();
    });
  };

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.formData = formData;
    this.upload(formData, null, item);
  };

  /**
   * uploadExcel 上传配置
   */
  upload(formData, params, item) {
    return this.api.uploadFile(formData, params).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.settingData.logoFile = res.data.id;
          item.onSuccess!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  customReqPc = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.formData = formData;
    this.uploadPc(formData, null, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadPc(formData, params, item) {
    return this.api.uploadFile(formData, params).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.settingData.pcBackgroundPic = res.data.id;
          item.onSuccess!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  customReqMb = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.formData = formData;
    this.uploadMb(formData, null, item);
  };

  /**
   * uploadExcel 上传配置
   */
  uploadMb(formData, params, item) {
    return this.api.uploadFile(formData, params).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.settingData.mobileBackgroundPic = res.data.id;
          item.onSuccess!();
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  handleChange(info: { file: UploadFile }): void {
    switch (info.file.status) {
      case "uploading":
        this.loading = true;
        this.getBase64(info.file!.originFileObj!, (img: string) => {
          this.avatarUrl = img;
        });
        break;
      case "done":
        // Get this url from response in real world.
        // this.loading = false;
        break;
      case "error":
        // this.msg.error("Network error");
        this.customMsg.open("error", "Network error");
        //this.loading = false;
        break;
    }
  }

  handleChangePic(info: { file: UploadFile }, type): void {
    switch (info.file.status) {
      case "uploading":
        this.loading = true;
        this.getBase64(info.file!.originFileObj!, (img: string) => {
          if (type == "PC") {
            this.PcavatarUrl = img;
          } else {
            this.MbavatarUrl = img;
          }
        });
        break;
      case "done":
        // Get this url from response in real world.
        // this.loading = false;
        break;
      case "error":
        // this.msg.error("Network error");
        this.customMsg.open("error", "Network error");
        //this.loading = false;
        break;
    }
  }

  /**
   * getBase64 获取图片base64
   * @param img
   * @param callback
   */
  private getBase64(img, callback: (img: string) => void): void {
    const reader = new FileReader();
    reader.addEventListener("load", () => callback(reader.result!.toString()));
    reader.readAsDataURL(img);
  }

  editorwelcome() {
    this.locwelcomepage = true;
    this.pagename = "欢迎页";
  }

  editorend() {
    this.locengpage = true;
    this.pagename = "结束页";
  }

  wlcomeSaveSet() {
    if (this.locwelcomepage) {
      this.locwelcomepage = false;
    }
    if (this.locengpage) {
      this.locengpage = false;
    }
    this.Isactivelan = true;
  }
  wlcomeDefault() {
    if (this.locwelcomepage) {
      this.locwelcomepage = false;
    }
    if (this.locengpage) {
      this.locengpage = false;
    }
    this.Isactivelan = true;
  }
  getActivenew(e) {
    if (e == "zh_CN") {
      this.Isactivelan = true;
    } else {
      this.Isactivelan = false;
    }
  }

  // 关联任务-自定义-互斥
  changeEnable(e, type) {
    if (!this.reportTypeList.includes("BLANK_CUSTOM")) return;
    if (type == "isEnableQuestionBookDistribute") {
      if (e) {
        this.isEnableScaleExtend = !e;
      }
    } else {
      if (e) {
        this.isEnableQuestionBookDistribute = !e;
      }
    }
  }

  /**
   * 如果开启了测评着可以邮件查看报告，可以手动触发邮件发送报告，默认为全部人员
   */
  sendReport() {
    return this.new_create_service
      .sendReport(this.projectId)
      .toPromise()
      .then((res) => {
        if (res.result.code === 0) {
          this.msg.success("操作成功");
        } else {
          // this.msg.error(res.result.message);
          this.customMsg.open("error", res.result.message);
        }
      })
      .catch((err) => {
        // this.msg.error("操作异常");
        this.customMsg.open("error", "操作异常");
      })
      .finally(() => {});
  }

  changeSelectedIndex(num) {
    this.tableIndex = num;
  }

  changeNameValue(name) {
    this.settingData.name = name;
  }
  onSelectI18n(e) {
    this.loceng_locwelcome_lan = e;
  }

  /**
   * 角色有效填答人数设置-确定
   */
  onClickAnswerRolePersonNumConfirm() {
    this.answerRolePersonNumVisible = false;
  }
  /**
   * 填答一致性-自定义设置-确定
   */
  onClickAnswerSameRateConfirm() {
    // for (
    //   let index = 0;
    //   index < this.settingData.answerSameRateCustom.scaleCustoms.length;
    //   index++
    // ) {
    //   const val = this.settingData.answerSameRateCustom.scaleCustoms[index];
    //   if (isNull(val.rate)) {
    //     this.msg.error(
    //       `请输入量表/单选题中${val.roleName.zh_CN}的一致性占比！`
    //     );
    //     return;
    //   }
    //   console.log(val)
    // }
    // for (
    //   let index = 0;
    //   index < this.settingData.answerSameRateCustom.proportionCustoms.length;
    //   index++
    // ) {
    //   const val = this.settingData.answerSameRateCustom.proportionCustoms[index];
    //   console.log(val)
    //   if (isNull(val.rate)) {
    //     this.msg.error(
    //       `请输入滑块/多级比重题中${val.roleName.zh_CN}的一致性占比！`
    //     );
    //     return;
    //   }
    // }
    this.answerSameRateVisible = false;
  }
  /**
   * 填答一致性-自定义设置-清空
   */
  onClickAnswerSameRateClear() {
    this.settingData.answerSameRateCustom.scaleCustoms.forEach((val) => {
      val.rate = null;
    });
    this.settingData.answerSameRateCustom.proportionCustoms.forEach((val) => {
      val.rate = null;
      val.score1 = null;
      val.score2 = null;
      val.score3 = null;
      val.score4 = null;
    });
  }

  /**
   * 用于导出360有效数据
   *
   */
  export360ValidData() {
    // 前面有操作在操作中，不能支持此操作，需等前面操作完成后。
    if (this.isloading) {
      return false;
    }
    // 操作开始，设置loading遮罩状态。避免用户进行其他操作，显性提醒用户此操作在进行中，还未结束。
    this.isloading = true;

    let api: string = `${this.tenantApi}/survey/download/answer/export360ValidData?projectId=${this.projectId}`;
    let param: any = { responseType: "blob", observe: "response" };
    let sub: Observable<any> = this.HttpClient.post(api, {}, param);

    // 调取后端服务接口
    sub.subscribe(
      (data) => {
        this.downFile(data);
        // 服务端操作完成，取消置loading遮罩状态。显性让用户明白此操完成了。
        this.isloading = false;
      },
      (error1) => {
        // 服务端操作失败，取消置loading遮罩状态。
        this.isloading = false;

        // 提示用户操完失败
        // this.msg.error("下载失败");
        this.customMsg.open("error", "下载失败");
      }
    );
  }

  /**
   * 导入360有效数据
   *
   * @param item
   */
  import360ValidData = (item: UploadXHRArgs) => {
    const isUpdata =
      item.file.type ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      item.file.type === "application/vnd.ms-excel";

    if (!isUpdata) {
      // return this.msg.error("上传文件格式应为excel");
      this.customMsg.open("error", "上传文件格式应为excel");
      return;
    }

    const formData = new FormData();
    formData.append("excel", item.file as any);

    // 前面有操作在操作中，不能支持此操作，需等前面操作完成后。
    if (this.isloading) {
      return false;
    }

    // 操作开始，设置loading遮罩状态。避免用户进行其他操作，显性提醒用户此操作在进行中，还未结束。
    this.isloading = true;

    this.import360ValidDataApi(formData, this.projectId).subscribe(
      (event: HttpEvent<any>) => {
        let res: any = event;
        // 服务端操作完成，取消置loading遮罩状态。显性让用户知道此操完成了。
        this.isloading = false;
        if (res.result.code === 0) {
          this.msg.success("导入成功");
        } else {
          // 为什么大于10000的错误不显示？？
          if (res.result.code < 10000) {
            // this.msg.error(res.result.message);
            this.customMsg.open("error", res.result.message);
          }
        }
      },
      (err) => {
        // 服务端操作完成，取消置loading遮罩状态。显性让用户知道此操完成了。
        this.isloading = false;
        item.onError!(err, item.file!);
      }
    );
  };

  public import360ValidDataApi(formData, projectId: string): Observable<any> {
    let api = `${this.tenantApi}/survey/download/answer/import360ValidData`;
    api = api + `?projectId=${projectId}`;
    return this.HttpClient.post(api, formData);
  }

  downFile(data) {
    this.showBlobErrorMessage(data);

    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });
    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];
    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];
    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * 文件报错提示
   * @param data
   */
  showBlobErrorMessage(data: any) {
    let body = data.body;
    if (body.type === "application/json") {
      let that = this;
      const reader = new FileReader();
      reader.readAsText(body, "utf-8");
      reader.onload = () => {
        // 处理报错信息
        // JSON.parse(reader.result) 拿到报错信息
        let resp: any = JSON.parse(reader.result + "");
        let code: number = resp.result.code;
        let errMsg: string = resp.result.message;

        if (code !== 0) {
          // that.msg.error(`${errMsg}，请联系管理员。`);
          this.customMsg.open("error", `${errMsg}，请联系管理员。`);
        }
      };
    }
  }

  is360Or270() {
    return (
      this.reportType.indexOf("_360") >= 0 ||
      this.reportType.indexOf("_270") >= 0
    );
  }

  // isDisabledCustomValidData() {
  //   return !this.projectId || this.projectStatus === "ANNOUNCED";
  // }
  /**
   * 是否跳转到新的题本编辑页面
   *@author:wangxiangxin
   *@Date:2023/08/25
   */
  isNewQuestionBook() {
    //是否的是360 270自定义
    let flag =
      this.reportType.includes("_CUSTOMIZE") &&
      (this.reportType.includes("360_") || this.reportType.includes("270_"));
    return flag;
  }
  isNewQuestionBook360() {
    //是否的是360 270
    let flag =
      this.reportType.includes("360_") || this.reportType.includes("270_");
    return false;
  }
  //多语言 start
  allLansOpen = false;
  lanVisible: boolean = false; // 语言设置
  addCodes: string[] = [];
  defaultCode = ["zh_CN", "en_US"];
  allLans: any[] = []; // 所有语言-当前租户下
  lansFilter_cnen: any[] = [];
  defaultLangOptions = [
    {
      value: "zh_CN",
      name: "中文",
      checked: false,
    },
    {
      value: "en_US",
      name: "英文",
      checked: false,
    },
  ];
  // 显示语言设置-抽屉
  showanSetting() {
    this.allLansOpen = false; // 关闭选择器
    this.lanVisible = true;
  } // 语言设置-抽屉-关闭
  onLanSettingClose() {
    this.lanVisible = false;
    this.getLanOptions();
  } // 获取所有的语言类型
  async getLanOptions() {
    // 租户可选的语言项
    const allLansRes = await this.api.getLanguages().toPromise();
    if (allLansRes.result.code === 0) {
      // 排序-中英文最前
      const allLans = allLansRes.data.sort((a, b) => {
        if (a.value === "zh_CN") return -1;
        if (b.value === "zh_CN") return 1;
        if (a.value === "en_US") return -1;
        if (b.value === "en_US") return 1;
        if (a.value === "jp") return -1;
        if (b.value === "jp") return 1;
        if (a.value === "ko") return -1;
        if (b.value === "ko") return 1;
        if (a.value === "cs_1") return -1;
        if (b.value === "cs_1") return 1;
        if (a.value === "cs_2") return -1;
        if (b.value === "cs_2") return 1;
        if (a.value === "cs_3") return -1;
        if (b.value === "cs_3") return 1;
        return 0;
      });
      const filterCodes = ["zh_TW", "th"];
      this.allLans = allLans.filter((val) => !filterCodes.includes(val.value));
    }
    // 选项-新增语言
    const lansFilter_cnen = this.allLans.filter(
      (val) => !this.defaultCode.includes(val.value)
    );
    this.lansFilter_cnen = lansFilter_cnen;

    // 选项-可选语言
    const checkCode = this.settingData.availableLanguages; // 默认中文+填答可选语言+默认填答语言
    const projectLan = this.allLans.filter((val) =>
      checkCode.includes(val.value)
    ); //  请选择新增语言

    const selectCodes = _.uniq([...this.settingData.optionalLanguages]);

    const projectLangOptions = projectLan.map((val) => ({
      ...val,
      checked: selectCodes.includes(val.value),
    })); //  填答时可选语言

    // 新增语言-选中值
    if (this.addCodes.length === 0) {
      this.addCodes = _.uniq(
        checkCode.filter((val) => !this.defaultCode.includes(val))
      );
      this.projectLangOptions = projectLangOptions;
    } else {
      const newCheckCode = _.uniq([
        ...this.defaultCode,
        ...this.addCodes,
        ...selectCodes,
      ]);
      const newProjectLan = this.allLans.filter(
        (val) =>
          newCheckCode.includes(val.value) && !this.defaultCode.includes(val)
      );
      this.addCodes = _.uniq(
        newCheckCode.filter((val) => !this.defaultCode.includes(val))
      );
      this.projectLangOptions = newProjectLan.map((val) => ({
        ...val,
        checked: selectCodes.includes(val.value),
      }));
    }
  }
  // 配置语言-填答语言的可选项
  changeProjectLan(e) {
    const fullCode = _.uniq([...e, ...this.defaultCode]);
    const lans = this.allLans.filter((val) => fullCode.includes(val.value));
    // 存储高级设置-可选语言
    this.projectLangOptions = lans.map((val) => ({
      ...val,
      // checked: this.settingData.optionalLanguages.includes(val.value)
      checked: true,
    }));

    this.settingData.availableLanguages = fullCode;
    this.settingData.optionalLanguages = fullCode;
    sessionStorage.setItem("projectLanguages", JSON.stringify(fullCode));
  }
  // 语言设置-抽屉-保存
  onLanSettingSave(e) {
    this.lansFilter_cnen.forEach((val) => {
      if (val.value === e.value) {
        val.name = e.name;
      }
    });
  }
  // 标准360系列 隐藏多语言设置
  isShowLan() {
    let flag = false;
    const type = ["_270_DEGREES", "_360_DEGREES", "_360_TRAIN"];
    flag = !type.includes(this.reportType);
    return flag;
  }
  // 题数类型变更
  changeQuestionCustomSort(e) {
    if (!e) {
      this.settingData.isAutoFillQuestionBook = false;
    }
  }
}
