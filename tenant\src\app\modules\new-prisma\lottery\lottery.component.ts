import {
  Component,
  Input,
  ElementRef,
  OnInit,
  ViewChild,
  TemplateRef,
  Inject,
} from "@angular/core";

import { Router, ActivatedRoute } from "@angular/router";
import { NzModalRef, NzModalService } from "ng-zorro-antd/modal";
import { NewPrismaService } from "../new-prisma.service";
import { NzMessageService } from "ng-zorro-antd/message";
import { UploadFile, UploadXHRArgs } from "ng-zorro-antd/upload";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import _ from "lodash";

@Component({
  selector: "app-lottery",
  templateUrl: "./lottery.component.html",
  styleUrls: ["./lottery.component.less"],
})
export class LotteryComponent implements OnInit {
  @ViewChild("inputNumberDraws", { static: false })
  inputNumberDraws: ElementRef; // 抽奖次数
  @ViewChild("inputNumberParticipants", { static: false })
  inputNumberParticipants: ElementRef; // 参与人数
  @ViewChild("PrizeSettingData", { static: false })
  PrizeSettingData: ElementRef; // 设置奖品数量

  tplModal: NzModalRef;
  tplModalButtonLoading: boolean = false;
  // 中英文设置
  partakeBtnLan: string = "zh_CN"; // 参与
  subjectNameLan: string = "zh_CN"; // 主题名称
  drawBtnNameLan: string = "zh_CN"; // 抽奖按钮
  drawRuleDescLan: string = "zh_CN"; // 活动规则
  categoryListLan: string = "zh_CN"; // 奖品分类 中英
  cashRuleDescLan: string = "zh_CN"; // 兑换规则
  setPrizeNameLan: string = "zh_CN"; // 奖品名称
  setReminderLan: string = "zh_CN"; // 提示语

  showBorderColorPopo: boolean = false;
  showBgColorPopo: boolean = false;

  iconImgList: any[] = ["empty"];

  settingData: any = {
    boxBorderColor: "", // 九宫格边框颜色
    boxBackgroundColor: "", // 九宫格背景颜色
    cashRuleDescription: {
      en_US: "string",
      zh_CN: "string",
    },
    createTime: "2022-05-11T08:30:35.599Z",
    currentPartakeNumber: 0,
    drawButtonName: {
      en_US: "string",
      zh_CN: "string",
    },
    drawButtonPic: "string",
    drawNumber: 0,
    drawRuleDescription: {
      en_US: "string",
      zh_CN: "string",
    },
    endTime: "2022-05-11T08:30:35.599Z",
    id: "",
    partakeButtonName: {
      en_US: "string",
      zh_CN: "string",
    },
    partakeNumber: "0",
    projectId: "string",
    startTime: "2022-05-11T08:30:35.599Z",
    subjectBackgroundColor: "string",
    subjectBackgroundMobilePic: "string",
    subjectBackgroundPcPic: "string",
    subjectBackgroundType: "COLOR",
    subjectName: {
      en_US: "string",
      zh_CN: "string",
    },
    subjectPic: "string",
    updateTime: "2022-05-11T08:30:35.599Z",
  };

  prizeSettingData: any = {
    // 奖品设置弹窗
    projectId: "", // 活动ID
    categoryId: null, // 奖品分类
    prizeName: {
      // 奖品名称"
      zh_CN: "",
      en_US: "",
    },
    prizePic: "", // 奖品图片
    prizeIcon: "", // 奖品图标
    prizeIconPic: "", // 奖品图标图片
    totalNumber: 1, // 奖品总数量
    currentNumber: "", // 奖品当前数量
    reminder: {
      // 提示语
      zh_CN: "",
      en_US: "",
    },
  };

  prizeList: any[] = []; // 奖品列表
  categoryList: any[] = []; // 奖品分类列表

  timeRange: Date[] = [];

  tabIndex: number = 0;

  projectId: string = "";

  type: boolean = true; // 电脑 false 手机

  value: "";

  tinymce: any;

  tinyconfig: any = {};

  tenantApi: string = "/tenant-api";

  privewData: any; // 预览数据
  lotteryResultData: any; // 预览中奖数据

  themes: string = "A"; // 主题背景

  selectColor: string = ""; // 选中的纯色背景

  numberDraws: string = "1"; // 抽奖次数
  numberParticipants: string = "1"; // 参与人数

  // modal参数
  prizeType: string = "1";

  showSetCategory: boolean = false; // 切换奖品设置 / 奖品分类设置

  showPreview: boolean = true; // 预览抽奖/中奖切换

  projectType: string = ""; // 活动状态

  solidColorBg: any = [
    // 纯色背景颜色
    [
      "#B61C1C",
      "#D3312C",
      "#F44333",
      "#E57574",
      "#FFCED4",
      "#FFEBED",
      "#E65101",
      "#F87E03",
      "#FF9700",
      "#FFB64D",
      "#FFDFB6",
      "#FFF4E0",
    ],
    [
      "#B28711",
      "#F9C12C",
      "#FDEC3C",
      "#FEF377",
      "#FFFBC1",
      "#FFFFE7",
      "#827A17",
      "#B1B52C",
      "#CEDD3C",
      "#DFE87B",
      "#EFF6C3",
      "#F9FBE5",
    ],
    [
      "#1E6022",
      "#368F3F",
      "#4DB252",
      "#81C788",
      "#C9E7CB",
      "#E9F7E8",
      "#1A4C51",
      "#0098A6",
      "#04BCD8",
      "#4DD0E4",
      "#B5ECF3",
      "#DEF8F5",
    ],
    [
      "#1045A1",
      "#1678D3",
      "#2196F5",
      "#65B7F1",
      "#BCDFFB",
      "#E1F2FC",
      "#4B158D",
      "#7920A2",
      "#9D28B4",
      "#BB66CB",
      "#E2BDE9",
      "#F3E5F6",
    ],
    [
      "#3C2722",
      "#5E4437",
      "#7C5649",
      "#A3887F",
      "#DBCDCA",
      "#F1EEE9",
      "#222222",
      "#424242",
      "#767676",
      "#9E9E9E",
      "#E1E1E1",
      "#F6F6F6",
    ],
  ];

  imgUrl: string = "";

  iconList: any[] = [];

  visible: boolean = false;

  lan: string = "zh_CN";
  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private modalService: NzModalService,
    private prismAapi: NewPrismaService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private HttpClient: HttpClient,
    private msg: NzMessageService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    // const defaultCode = ["zh_CN","en_US"];
    // const projectLanguages = JSON.parse(sessionStorage.getItem('projectLanguages'))  || defaultCode;
    this.lan = "zh_CN";
    this.partakeBtnLan = this.lan; // 参与
    this.subjectNameLan = this.lan; // 主题名称
    this.drawBtnNameLan = this.lan; // 抽奖按钮
    this.drawRuleDescLan = this.lan; // 活动规则
    this.cashRuleDescLan = this.lan; // 兑换规则

    this.setPrizeNameLan = this.lan; // 奖品名称-弹窗
    this.setReminderLan = this.lan; // 提示语-弹窗
    this.categoryListLan = this.lan; // 奖品分类-弹窗
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.imgUrl = `${baseUrl}api/file/www/`;
    this.projectId = this.activatedRoute.snapshot.queryParams.projectId;
    this.projectType = this.activatedRoute.snapshot.queryParams.projectType;
    this.getDetail();
    this.getPrivewData();
    this.getLotteryResult();
    const _this = this;
    this.tinyconfig = {
      height: 150,
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.HttpClient.post(
          `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`,
          formData,
          { headers: headers }
        ).subscribe(
          (response: any) => {
            //console.log(response);
            if (response) {
              this.HttpClient.get(
                `${this.tenantApi}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                { headers: headers }
              ).subscribe((imgurl: any) => {
                let baseUrl: string = window.location.origin + "/";
                if (baseUrl.indexOf("http://localhost") !== -1) {
                  baseUrl = "http://***********/";
                }
                let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                // if ( environment.dev ) {
                //    url = environment.SERVER_URL.substr(0, environment.SERVER_URL.length - 1)  + imgurl.data.url; // 这里是你获取图片url
                // } else {
                //   url = 'api' + imgurl.data.url; // 这里是你获取图片url
                // }
                // 把图片链接，img src标签显示图片的有效链接放到下面回调函数里
                //console.log(url);
                success(url);
              });
            } else {
              if (response && response.rtnMsg) {
                failure(response.rtnMsg);
              } else {
                failure("上传失败：未知错误");
              }
            }
          },
          (error1) => {
            //console.log(error1);
            failure("上传失败：未知错误");
          }
        );
      },
      language: "zh_CN",
      language_url: "./assets/tinymce/langs/zh_CN.js", //配置中文语言包，需要下载
      automatic_uploads: false, // 自动上传
      paste_data_images: true,
      statusbar: false, // 隐藏底部 http://tinymce.ax-z.cn/configure/editor-appearance.php
      branding: false, // 隐藏右下角技术支持
      browser_spellcheck: true, // 拼写检查
      placeholder: "请输入内容",

      // 编辑区样式设置
      content_style: "p {margin: 0}",
    };
  }

  privewRefresh() {
    // 刷新预览
    this.update(true);
  }

  default() {
    // 恢复默认
    this.settingData.partakeButtonName = {
      // 参与按钮
      zh_CN: "参与抽奖",
      en_US: "",
    };
    this.settingData.subjectName = {
      // 主题名称
      zh_CN: "",
      en_US: "",
    };
    this.settingData.subjectBackgroundType = "CUSTOM"; // 主题背景
    this.settingData.subjectBackgroundMobilePic = "";
    this.settingData.subjectBackgroundPcPic = "";
    this.settingData.drawButtonName = {
      // 抽奖按钮
      zh_CN: "",
      en_US: "",
    };
    this.settingData.drawButtonPic = "";
    this.settingData.subjectPic = ""; // 标题图片
    this.settingData.subjectBackgroundColor = "";
    this.settingData.boxBorderColor = "#9877E1";
    this.settingData.boxBackgroundColor = "#4D29B1";
    this.update(true);
  }

  mapData() {
    // 格式化预览数据
    if (this.privewData.subjectBackgroundType === "CUSTOM") {
      if (this.privewData.subjectBackgroundMobilePic) {
        this.privewData.bgPhoneImg = `url(${this.imgUrl +
          this.privewData.subjectBackgroundMobilePic}) no-repeat`;
      } else {
        this.privewData.bgPhoneImg = "";
      }
      if (this.privewData.subjectBackgroundPcPic) {
        this.privewData.bgPCImg = `url(${this.imgUrl +
          this.privewData.subjectBackgroundPcPic}) no-repeat`;
      } else {
        this.privewData.bgPCImg = "";
      }
    } else {
      if (this.privewData.subjectBackgroundColor) {
        this.privewData.bgPhoneImg = this.privewData.subjectBackgroundColor;
        this.privewData.bgPCImg = this.privewData.subjectBackgroundColor;
      } else {
        this.privewData.bgPhoneImg = "";
        this.privewData.bgPCImg = "";
      }
    }
    // 标题背景图片
    if (this.privewData.subjectPic) {
      this.privewData.subjectPic = `url(${this.imgUrl +
        this.privewData.subjectPic}) no-repeat`;
    } else {
      this.privewData.subjectPic = `url(assets/images/prisma/image-title-PC.png) no-repeat`;
    }
    if (this.privewData.prizeDisplayVoList) {
      this.privewData.prizeDisplayVoList.splice(4, 0, {
        categoryId: "",
        categoryName: this.privewData.drawButtonName,
        lotteryId: "",
        prizeIcon: !this.privewData.drawButtonPic
          ? "lottery-btn-mobile.png"
          : this.privewData.drawButtonPic,
      });
    }
    this.privewData.prizeDisplayVoList.forEach((element, index) => {
      let url = "assets/images/prisma/";
      if (element.prizeIcon.indexOf(".png") === -1) {
        url = this.imgUrl;
      } else {
        url = "assets/images/prisma/";
      }
      if (index === 4) {
        element.localIcon = `url(${url + element.prizeIcon}) no-repeat`;
      } else {
        if (element.prizeIcon !== "empty") {
          element.localIcon = url + element.prizeIcon;
        } else {
          element.localIcon = element.prizeIcon;
        }
      }
    });
    console.log(this.privewData);
  }

  getPrivewData() {
    // 获取预览数据
    this.prismAapi.getDisplayDetail(this.projectId).subscribe((res) => {
      this.privewData = res.data;
      this.mapData();
    });
  }
  getLotteryResult() {
    // 获取预览中奖数据
    this.prismAapi.getLotteryResult(this.projectId).subscribe((res) => {
      let url = "assets/images/prisma/";
      if (res.data.prizePic.indexOf(".png") === -1) {
        url = this.imgUrl;
      } else {
        url = "assets/images/prisma/";
      }
      if (res.data.prizePic) {
        res.data.localPic = url + res.data.prizePic;
      } else {
        res.data.localPic = res.data.prizePic;
      }
      this.lotteryResultData = res.data;
      console.log(this.lotteryResultData);
    });
  }

  getDetail() {
    // 奖品设置详情
    this.prismAapi.getLotteryDetail(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        this.settingData = res.data;
        if (!this.settingData.boxBorderColor)
          this.settingData.boxBorderColor = "#9877E1";
        if (!this.settingData.boxBackgroundColor)
          this.settingData.boxBackgroundColor = "#4D29B1";

        this.timeRange = [this.settingData.startTime, this.settingData.endTime];
      }
    });
    this.getPrizeList();
  }

  getPrizeList() {
    // 获取奖品列表
    this.prismAapi.getPrizeList(this.projectId).subscribe((res) => {
      if (res.result.code === 0) {
        let url = "assets/images/prisma/";
        res.data.forEach((element) => {
          if (element.prizeIcon.indexOf(".png") === -1) {
            url = this.imgUrl;
          } else {
            url = "assets/images/prisma/";
          }
          if (element.prizeIcon !== "empty") {
            element.localIcon = url + element.prizeIcon;
          } else {
            element.localIcon = element.prizeIcon;
          }
          if (element.prizePic)
            element.localPic = this.imgUrl + element.prizePic;
        });
        this.prizeList = res.data;
        console.log(this.prizeList, "this.prizeList");
      }
    });
  }

  choosePrizeIcon(iconName: string) {
    // 奖品设置 选择奖品图标
    this.prizeSettingData.prizeIcon = iconName;
  }

  update(refresh?: boolean) {
    // 创建或修改接口
    this.prismAapi.createOrUpdate(this.settingData).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("修改成功");
        if (refresh) {
          this.getPrivewData();
          this.getLotteryResult();
        }
        this.getDetail();
      }
    });
  }

  getHome() {
    localStorage.setItem("backurl", this.router.routerState.snapshot.url);
    this.router.navigate(["/new-prisma"], {
      queryParams: {
        projectId: this.projectId,
        type: this.projectType,
      },
    });
  }

  channgeType(key: boolean) {
    // 左侧切换tab
    this.type = key;
  }

  // 改变主题背景类型 CUSTOM 自定义 COLOR 纯色
  changeThemeType(e) {
    if (e === "CUSTOM") this.selectColor = "";
  }

  // 选择纯色背景
  chooseBg(color: string) {
    if (this.settingData.subjectBackgroundColor === color)
      return (this.settingData.subjectBackgroundColor = "");
    this.settingData.subjectBackgroundColor = color;
  }
  // 选择九宫格背景
  chooseNineBg(color: string) {
    if (this.settingData.boxBackgroundColor === color) {
      this.settingData.boxBackgroundColor = "";
    } else {
      this.settingData.boxBackgroundColor = color;
    }
    this.showBgColorPopo = false;
  }
  // 选择九宫格边框背景
  chooseNineBorderBg(color: string) {
    if (this.settingData.boxBorderColor === color) {
      this.settingData.boxBorderColor = "";
    } else {
      this.settingData.boxBorderColor = color;
    }
    this.showBorderColorPopo = false;
  }

  numberDrawsChange(value: string) {
    // 抽奖次数改变 仅限数字
    this.updateValueDraws(value);
    this.getPrizeList();
  }

  addNumDraws() {
    // 增加抽奖人数
    if (!this.settingData.drawNumber) {
      this.settingData.drawNumber = 1;
    } else {
      this.settingData.drawNumber++;
    }
    this.inputNumberDraws.nativeElement.value = this.settingData.drawNumber;
    this.update();
  }

  minusNumDraws() {
    //

    if (this.settingData.drawNumber === 1) {
      // this.msg.error("抽奖次数最少为1");
      this.customMsg.open("error", "抽奖次数最少为1");
    } else {
      if (!this.settingData.drawNumber) {
        this.settingData.drawNumber = 1;
      } else {
        this.settingData.drawNumber--;
      }
      this.inputNumberDraws.nativeElement.value = this.settingData.drawNumber;
      this.update();
    }
  }

  updateValueDraws(value: string): void {
    // 抽奖次数改变逻辑
    const reg = /^-?([1-9][0-9]*)?$/;
    if ((!isNaN(+value) && reg.test(value)) || value === "") {
      this.settingData.drawNumber = value;
    }
    this.inputNumberDraws.nativeElement.value = this.settingData.drawNumber;
  }

  numberDrawsOnBlur() {
    this.update();
  }

  // model 奖品数量
  updateValueSettingTotalNumber(value) {
    const reg = /^-?([1-9][0-9]*)?$/;
    if ((!isNaN(+value) && reg.test(value)) || value === "") {
      this.prizeSettingData.totalNumber = value;
    }
    this.PrizeSettingData.nativeElement.value = this.prizeSettingData.totalNumber;
  }

  SettingTotalNumber(value: string) {
    this.updateValueSettingTotalNumber(value);
  }

  minusTotalNumber() {
    if (this.prizeSettingData.totalNumber === 1) {
      // this.msg.error("抽奖次数最少为1");
      this.customMsg.open("error", "抽奖次数最少为1");
    } else {
      if (!this.prizeSettingData.totalNumber) {
        this.prizeSettingData.totalNumber = 1;
      } else {
        this.prizeSettingData.totalNumber--;
      }
      this.PrizeSettingData.nativeElement.value = this.prizeSettingData.totalNumber;
    }
  }

  addTotalNumber() {
    if (!this.prizeSettingData.totalNumber) {
      this.prizeSettingData.totalNumber = 1;
    } else {
      this.prizeSettingData.totalNumber++;
    }
    this.PrizeSettingData.nativeElement.value = this.prizeSettingData.totalNumber;
  }

  numberPartsChange(value: string) {
    //参与人数改变 仅限数字
    this.updateValuePart(value);
    this.getPrizeList();
  }

  minusNumParts() {
    if (this.settingData.partakeNumber === 1) {
      // this.msg.error("抽奖次数最少为1");
      this.customMsg.open("error", "抽奖次数最少为1");
    } else {
      if (!this.settingData.partakeNumber) {
        this.settingData.partakeNumber = 1;
      } else {
        this.settingData.partakeNumber--;
      }
      this.inputNumberParticipants.nativeElement.value = this.settingData.partakeNumber;
      this.update();
    }
  }
  addNumParts() {
    if (!this.settingData.partakeNumber) {
      this.settingData.partakeNumber = 1;
    } else {
      this.settingData.partakeNumber++;
    }
    this.inputNumberParticipants.nativeElement.value = this.settingData.partakeNumber;
    this.update();
  }

  updateValuePart(value: string): void {
    const reg = /^-?([1-9][0-9]*)?$/;
    if ((!isNaN(+value) && reg.test(value)) || value === "") {
      this.settingData.partakeNumber = value;
    }
    this.inputNumberParticipants.nativeElement.value = this.settingData.partakeNumber;
  }

  numberPartsOnBlur() {
    this.update();
  }

  getCategoryList() {
    // 获取抽奖分类
    this.prizeSettingData.categoryId = null;
    this.prismAapi.getCategoryList(this.projectId).subscribe((res) => {
      this.categoryList = res.data;
    });
  }

  deletePrize(id) {
    // 删除 奖品
    this.prismAapi.deletePrize(id).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("删除奖品成功");
        this.getPrizeList();
      }
    });
  }

  getIconList() {
    this.prismAapi.getIconList(this.projectId).subscribe((res) => {
      res.data.forEach((element) => {
        let url = "assets/images/prisma/";
        if (element.id) {
          url = this.imgUrl;
        } else {
          url = "assets/images/prisma/";
        }
        if (element.name !== "empty") element.localIcon = url + element.name;
      });
      let arr = [...res.data, { name: "upload" }];
      this.iconList = _.chunk(arr, 5);
    });
  }

  creteIcon(iconId) {
    let params = {
      projectId: this.projectId,
      iconPic: iconId,
    };
    this.prismAapi.createIcon(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.getIconList();
      }
    });
  }
  addPrize(tplContent: TemplateRef<{}>, data?: any) {
    // this.partakeBtnLan = this.lan // 参与
    // this.subjectNameLan = this.lan // 主题名称
    // this.drawBtnNameLan = this.lan// 抽奖按钮
    // this.drawRuleDescLan = this.lan // 活动规则
    // this.cashRuleDescLan = this.lan// 兑换规则

    this.setPrizeNameLan = this.lan; // 奖品名称-弹窗
    this.setReminderLan = this.lan; // 提示语-弹窗
    this.categoryListLan = this.lan; // 奖品分类-弹窗
    this.getCategoryList();
    this.getIconList();
    if (data) {
      this.prizeSettingData = JSON.parse(JSON.stringify(data));
    }
    this.visible = true;
    // this.tplModal = this.modalService.create({
    //   nzTitle: null,
    //   nzContent: tplContent,
    //   nzFooter: null,
    //   nzMaskClosable: false,
    //   nzClosable: false,
    //   nzClassName: 'prize-modal',
    //   nzWidth: 960,
    //   nzOnOk: () => console.log('Click ok')
    // });
  }

  setCategory() {
    // 展示奖品分类弹窗
    this.showSetCategory = true;
  }

  addCategorys() {
    // 添加奖品分类
    this.categoryList.push({
      name: {
        en_US: "",
        zh_CN: "",
      },
    });
  }

  deleteCategory(index: number, id?: string) {
    if (id) {
      this.prismAapi.deleteCategory(id).subscribe((res) => {
        if (res.result.code === 0) {
          this.categoryList.splice(index, 1);
          this.msg.success("删除奖品分类成功");
        }
      });
    } else {
      this.categoryList.splice(index, 1);
      this.msg.success("删除奖品分类成功");
    }
  }

  goBackPage() {
    // 返回上一页 、 批量保存奖品分类
    let flag = this.categoryList.every((item) => {
      return item.name.zh_CN !== "";
    });

    let idFlag = this.categoryList.every((item) => {
      return item.id !== undefined;
    });

    if (!flag) {
      // this.msg.error("请填写分类按钮");
      this.customMsg.open("error", "请填写分类按钮");
    } else {
      if (idFlag) {
        // 没有修改不必调接口
        this.showSetCategory = false;
      } else {
        this.prismAapi
          .batchCreateOrUpdateCategory({
            categoryList: this.categoryList,
            projectId: this.projectId,
          })
          .subscribe((res) => {
            if (res.result.code === 0) {
              this.getCategoryList();
              this.showSetCategory = false;
              this.msg.success("修改奖品分类成功");
            }
          });
      }
    }
  }

  submitAddPrize() {
    // 新建/ 修改奖品
    this.prizeSettingData.projectId = this.projectId;
    if (!this.prizeSettingData.categoryId) {
      // return this.msg.error("请选择奖品分类");
      this.customMsg.open("error", "请选择奖品分类");
      return;
    }
    if (!this.prizeSettingData.prizeName.zh_CN) {
      // return this.msg.error("请输入奖品名称");
      this.customMsg.open("error", "请输入奖品名称");
      return;
    }
    if (
      !this.prizeSettingData.totalNumber &&
      !this.prizeSettingData.isPartakePrize
    ) {
      // return this.msg.error("请输入奖品数量");
      this.customMsg.open("error", "请输入奖品数量");
      return;
    }
    if (!this.prizeSettingData.prizeIcon) {
      // return this.msg.error("请选择奖品图标");
      this.customMsg.open("error", "请选择奖品图标");
      return;
    }
    if (!this.prizeSettingData.reminder) {
      // return this.msg.error("请输入提示语");
      this.customMsg.open("error", "请输入提示语");
      return;
    }
    if (!this.prizeSettingData.prizePic) {
      // return this.msg.error("请上传图片");
      this.customMsg.open("error", "请上传图片");
      return;
    }
    this.prismAapi
      .createOrUpdatePrize(this.prizeSettingData)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.msg.success("创建奖品成功");
          // this.tplModal.destroy();
          this.closeTplModal();
          this.getPrizeList();
        }
      });
  }

  closeTplModal(): void {
    // 关闭窗口 清空参数
    this.prizeSettingData = {
      // 奖品设置弹窗
      projectId: "", // 活动ID
      categoryId: null, // 奖品分类
      prizeName: {
        // 奖品名称"
        zh_CN: "",
        en_US: "",
      },
      prizePic: "", // 奖品图片
      prizeIcon: "", // 奖品图标
      prizeIconPic: "", // 奖品图标图片
      totalNumber: 1, // 奖品总数量
      currentNumber: "", // 奖品当前数量
      reminder: {
        // 提示语
        zh_CN: "",
        en_US: "",
      },
    };
    // this.tplModal.destroy();
    this.visible = false;
  }

  // onChange(result: Date): void {
  //   console.log('Selected Time: ', result);
  // }

  onOk(result: Date): void {
    this.settingData.startTime = result[0];
    this.settingData.endTime = result[1];
  }
  submitSave() {}
  getpreview() {
    if (this.settingData.startTime || this.settingData.endTime) {
      // this.msg.error("请选择活动周期");
      this.customMsg.open("error", "请选择活动周期");
      return;
    }
    this.update();
  }

  tabChange(e) {
    this.tabIndex = e.index;
  }

  togglePreview(flag: boolean) {
    // 切换抽奖、中奖预览
    this.showPreview = flag;
  }

  clearPic() {
    // 清除图片
    this.settingData.subjectPic = ``;
    this.update(true);
  }

  /**
   * customReqPrizePic 上传 奖品图片
   * @param item
   */
  customReqPrizePic = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.upload(formData, null, item, "PrizePic");
  };

  customReqPrizeIconPic = (item: UploadXHRArgs) => {
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.upload(formData, null, item, "PrizeIconPic");
  };

  customReqDrawBtn = (item: UploadXHRArgs) => {
    // 抽奖按钮 上传
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.upload(formData, null, item, "DrawBtn");
  };

  customReqSubjectPic = (item: UploadXHRArgs) => {
    // 主题图片 上传
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.upload(formData, null, item, "SubjectPic");
  };

  customReqSubjectBackgroundPcPic = (item: UploadXHRArgs) => {
    // 背景pc
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.upload(formData, null, item, "SubjectBackgroundPcPic");
  };

  customReqSubjectBackgroundMobilePic = (item: UploadXHRArgs) => {
    // 背景pc
    const formData = new FormData();
    const fileType = "." + item.file.name.split(".")[1];
    formData.append("file", item.file as any);
    formData.append("isPublic", "true");
    formData.append("effectiveFileTypes", fileType.toLowerCase());
    formData.append("businessType", "SAG_REPORT");
    this.upload(formData, null, item, "SubjectBackgroundMobilePic");
  };

  /**
   * uploadExcel 上传配置
   */
  upload(formData, params, item, uploadType) {
    return this.prismAapi.uploadFile(formData, params).subscribe(
      (res) => {
        if (res.result.code === 0) {
          switch (uploadType) {
            case "PrizePic":
              this.prizeSettingData.prizePic = res.data.id;
              break;

            case "PrizeIconPic":
              this.prizeSettingData.prizeIconPic = res.data.id;
              this.creteIcon(res.data.id);
              break;

            case "DrawBtn":
              this.settingData.drawButtonPic = res.data.id;
              break;

            case "SubjectPic":
              this.settingData.subjectPic = res.data.id;
              break;

            case "SubjectBackgroundPcPic":
              this.settingData.subjectBackgroundPcPic = res.data.id;
              break;

            case "SubjectBackgroundMobilePic":
              this.settingData.subjectBackgroundMobilePic = res.data.id;
              break;

            default:
              break;
          }

          item.onSuccess!();
          this.msg.success("图片上传成功");
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  saveData() {
    if (!this.settingData.partakeButtonName.zh_CN)
      // return this.msg.error("请填写参与按钮文字");
      return this.customMsg.open("error", "请填写参与按钮文字");
    // if(!this.settingData.subjectName.zh_CN) return this.msg.error('请输入主题名称')
    // if(!this.settingData.drawButtonName.zh_CN) return this.msg.error('请输入抽奖按钮名称')
    if (!this.settingData.startTime && !this.settingData.endTime)
      // return this.msg.error("请选择活动周期");
      return this.customMsg.open("error", "请选择活动周期");
    if (!this.settingData.drawRuleDescription.zh_CN)
      // return this.msg.error("请填写活动规则");
      return this.customMsg.open("error", "请填写活动规则");
    if (!this.settingData.drawNumber)
      // return this.msg.error("请输入抽奖次数");
      return this.customMsg.open("error", "请输入抽奖次数");
    if (!this.settingData.partakeNumber)
      // return this.msg.error("请填写参与人数");
      return this.customMsg.open("error", "请填写参与人数");
    if (!this.settingData.cashRuleDescription.zh_CN)
      // return this.msg.error("请填写兑奖规则");
      return this.customMsg.open("error", "请填写兑奖规则");
    this.updatePage();
  }

    updatePage(refresh?: boolean) {
        // 创建或修改接口
        this.prismAapi.createOrUpdate(this.settingData).subscribe((res) => {
            if (res.result.code === 0) {
                this.msg.success("修改成功");
                if (refresh) {
                    this.getPrivewData();
                    this.getLotteryResult();
                }
                this.getDetail();
                this.getHome();
            }
        });
    }

  // 国际化-参与按钮
  onSelectI18nPartakeBtn(e) {
    this.partakeBtnLan = e;
  }
  // 国际化-主题名称
  onSelectI18nSubjectName(e) {
    this.subjectNameLan = e;
  }
  // 国际化-抽奖按钮
  onSelectI18nBtnName(e) {
    this.drawBtnNameLan = e;
  }
  // 国际化-活动规则
  onSelectI18nRuleDesc(e) {
    this.drawRuleDescLan = e;
  }
  // 国际化-兑换规则
  onSelectI18nCashRule(e) {
    this.cashRuleDescLan = e;
  }
  // 国际化-提示语
  onSelectI18nReminder(e) {
    this.setReminderLan = e;
  }
  // 国际化-奖品名称
  onSelectI18nPrizeName(e) {
    this.setPrizeNameLan = e;
  }
  // 国际化-分类设置
  onSelectI18nCategoryList(e) {
    this.categoryListLan = e;
  }

  close() {
    this.visible = false;
  }
}
