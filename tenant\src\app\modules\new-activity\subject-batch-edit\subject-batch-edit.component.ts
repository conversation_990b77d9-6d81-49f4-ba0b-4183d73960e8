import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { NzMessageService, NzModalRef, NzModalService } from "ng-zorro-antd";
import { SubjectEditComponent } from "../subject-edit/subject-edit.component";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-subject-batch-edit",
  templateUrl: "./subject-batch-edit.component.html",
  styleUrls: ["./subject-batch-edit.component.less"],
})
export class SubjectBatchEditComponent implements OnInit {
  @Input() modelList: any[];
  currentModel: any;
  currentIndex = 0;

  isLoading: boolean = false;
  isNext: boolean = false;

  @ViewChild(SubjectEditComponent, { static: false })
  editWidget: SubjectEditComponent;

  constructor(
    private modalService: NzModalService,
    private routeInfo: ActivatedRoute,
    private modalRef: NzModalRef,
    private msg: NzMessageService,
    private projSerivce: ProjectManageService,
    private surveySerivce: SurveyApiService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    this.currentModel = this.modelList[this.currentIndex];
  }

  ok() {
    if (this.editWidget.checkAndTrans()) {
      this.modalRef.triggerOk();
    }
  }

  pre() {
    if (this.currentIndex === 0) {
      // return this.msg.warning("已经是第一条");
      return this.customMsg.open("warning", "已经是第一条");
    }
    if (this.editWidget.checkAndTrans()) {
      this.commitData(false);
    }
  }

  next() {
    if (this.currentIndex >= this.modelList.length - 1) {
      // return this.msg.warning("已经是最后一条");
      return this.customMsg.open("warning", "已经是最后一条");
    }
    if (this.editWidget.checkAndTrans()) {
      this.commitData(true);
    }
  }

  commitData(isNext: boolean) {
    this.isNext = isNext;
    let param = this.editWidget.paramJson;
    this.isLoading = true;
    this.surveySerivce.replaceQuestion(param).subscribe(
      (res) => {
        this.isLoading = false;
        if (res.result.code === 0) {
          let retData: any = res.data;
          // update local 称谓
          let tmpId = param.surveyQuestion.id;
          let tmpModel = _.find(this.modelList, { id: tmpId });
          if (retData && retData.name) {
            tmpModel.name = retData.name;
          }
          if (retData && retData.replaceName) {
            tmpModel.replaceName = retData.replaceName;
          }

          // update local model type: 1.称谓 2.描述
          // if(param.type === 1) {
          //   tmpModel.name = param.surveyQuestion.name;
          //   if(retData && retData.name) {
          //     tmpModel.name = retData.name;
          //     tmpModel.replaceName = retData.replaceName;
          //   }
          // } else if(param.type === 2) {
          //   let tmpName = param.surveyQuestion.name;
          //   let zhStr = tmpName.zh_CN;
          //   let enStr = tmpName.en_US;

          //   tmpModel.name.zh_CN = zhStr;
          //   tmpModel.name.en_US = enStr;
          //   tmpModel.replaceName.zh_CN = zhStr;
          //   tmpModel.replaceName.en_US = enStr;
          // }

          if (isNext) {
            this.currentIndex++;
          } else {
            this.currentIndex--;
          }
        }
      },
      (err) => {
        this.isLoading = false;
      }
    );
  }
}
