<div class="title">
    <span>创建组织报告</span>
    <div class="lan">
        <nz-checkbox-group [(ngModel)]="lans"></nz-checkbox-group>
    </div>
</div>


<div class="container">
    <div class="left">
        <nz-tabset [(nzSelectedIndex)]="indexNum" [nzTabPosition]="position" [nzType]="'line'" style="height:500px;">
            <!-- 组织架构 -->
            <nz-tab [nzTitle]="'组织架构'">
                <div>
                    <nz-input-group [nzSuffix]="suffixIcon" style="padding: 0 0 10px 0">
                        <input type="text" nz-input placeholder="Search" [(ngModel)]="searchValue" />
                    </nz-input-group>
                    <ng-template #suffixIcon>
                        <i nz-icon nzType="search"></i>
                    </ng-template>
                    <div class="tree scroll">
                        <nz-tree #nzTreeComponent nzMultiple
                            [nzData]="orgList" 
                            [nzExpandAll]="true" 
                            [nzSearchValue]="searchValue" 
                            (nzCheckBoxChange)="nzEvent($event)"
                            (nzClick)="nzEvent($event)"
                            
                            >
                        </nz-tree>
                    </div>
                </div>
            </nz-tab>
        
            <!-- 人口标签 -->
            <nz-tab *ngFor="let tab of demoTabs" [nzTitle]="tab.name">
                <div class="list scroll">
                    <div style="margin-bottom: 20px;">
                        <label
                          nz-checkbox
                          [(ngModel)]="tab.allChecked"
                          (ngModelChange)="updateAllChecked(tab, $event)"
                          [nzIndeterminate]="tab.indeterminate"
                        >
                          全选
                        </label>
                    </div>
                    <hr style="width: 96%;">
                    <div style="margin-top: 20px;"></div>
                    <nz-checkbox-group [(ngModel)]="tab.items" (ngModelChange)="updateSingleChecked(tab, $event)">
                    </nz-checkbox-group>
                </div>
            </nz-tab>

        </nz-tabset>
    </div>

    <div class="right scroll">
        <ng-container *ngFor="let type of getShowList()">
            <div class="type" *ngIf="type.itemList.length > 0" >
                <span>{{type.name}}</span>
                <div class="items">
                    <div class="label" *ngFor="let item of type.itemList" >
                        {{item.name}}
                    </div>
                </div>
            </div>
        </ng-container>
    </div>

</div>

<div class="footer">
    <button nz-button class="iptBtn" (click)="ok()">
        <span>确认</span>
    </button>

    <div *ngIf="normList.length > 0" class="after">
        <span class="line1">共计：
            <span class="total">{{getTotal()}}</span>K米
        </span>
        <span class="line2">K米将自动从您的账户中扣除</span>
    </div>

</div>

