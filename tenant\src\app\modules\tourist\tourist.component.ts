import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { LoginService } from "@src/modules/login/login.service";
import { HttpClient } from "@angular/common/http";
import { MicroApp } from '@knx/micro-app-ng';

@Component({
  selector: 'app-tourist',
  templateUrl: './tourist.component.html',
  styleUrls: ['./tourist.component.less']
})

export class TouristComponent implements OnInit {

  listdata = [
    {
      img:'assets/images/list_li_1.png',
      word:'便捷应用'
    },
    {
      img:'assets/images/list_li_2.png',
      word:'轻松管理'
    },
    {
      img:'assets/images/list_li_3.png',
      word:'专业服务'
    },
    {
      img:'assets/images/list_li_4.png',
      word:'深度分析'
    },
    {
      img:'assets/images/list_li_5.png',
      word:'融合开放'
    }
  ]
  tenantUrl: string = "/tenant-api";
  showIndex = 0;
  showhidden = null
  showdiv = false;
  Menustype = '0'
  Menuslive = true
  Menuslivebig = true

  carousellist = []

  inMicroApp = this.microApp.inMicroApp()

  constructor(
    private routeInfo: ActivatedRoute,
    private loginService:LoginService,
    private router: Router,
    private http: HttpClient,
    private microApp: MicroApp
  ) {}

  ngOnInit() {
    this.getallcarouse()
    let tmpToken = localStorage.getItem("token");
    if(tmpToken) {
      try {
        let token2 = JSON.parse(tmpToken);
        if (token2) {
          let nowDate = new Date().getTime() - new Date(token2.time).getTime();
          let minutes = (nowDate % (1000 * 60 * 60)) / (1000 * 60);
          if (minutes < 10) {
             // history.back();
            this.router.navigate(['/home']);
          }
        }
      } catch(e) {

      }
    }

    // this.loginService.login().subscribe((res:any)=>{
    //   if(res.result.code===0){
    //     this.router.navigate(['/home'])
    //   }
    // })
   

  }

  changeli(i){
    this.showIndex = i
  }

  getreturn(e){
    // console.log(e)
    this.showhidden = e
  }
  gethidden(){
    this.showhidden = 0
  }
  clickMenus(type){
    this.Menustype = type
    this.Menuslive = false
    this.Menuslivebig = false
    setTimeout(() =>{
      this.Menuslive = true
      this.Menuslivebig = true
    },200)
   
    
  }
  
  getallcarouse() {
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf('http://localhost') !== -1) {
      baseUrl = 'http://sag-qa.knxdevelop.com/'
    }
    const api = `${this.tenantUrl}/survey/standard/carousel/listAll?_allow_anonymous=true`;
    this.http.get(api).subscribe((res: any) => {
      
      this.carousellist = res.data.list
      this.carousellist.forEach(res => {
        res.previewImg = `${baseUrl}api/file/www/${res.fileUrl}`
      })
      console.log(this.carousellist)
    })
  }
  OpenUrl(item){
    if(item.url){
      if(item.target == 'BLANK'){
        window.open(item.url,'_blank');
      }else{
        window.location.href = item.url
      }
    }
    
    
  }
}
