import { Component, OnInit, Input, On<PERSON><PERSON>roy } from "@angular/core";
import { NzModalService, NzMessageService, UploadXHRArgs } from "ng-zorro-antd";
import { HttpClient, HttpEvent } from "@angular/common/http";
import _ from "lodash";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { NewPrismaService } from "../new-prisma.service";
import { NzDrawerRef } from "ng-zorro-antd/drawer";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { DownloadUtilService } from "@src/modules/service/download-util.service";

@Component({
  selector: "app-data-show-cal-rule",
  templateUrl: "./data-show-cal-rule.html",
  styleUrls: ["./data-show-cal-rule.less"],
})
export class DataShowCalRuleComponent implements OnInit, OnD<PERSON>roy {
  @Input() projectId: string;
  private routerSubscription: Subscription;

  constructor(
    private api: NewPrismaService,
    private nzModalService: NzModalService,
    private msg: NzMessageService,
    private http: HttpClient,
    private drawerRef: NzDrawerRef,
    private customMsg: MessageService,
    private downUtil: DownloadUtilService,
    private router: Router
  ) {}
  mapOfExpandData: { [key: string]: boolean } = {
    // DEMOGRAPHIC: true,
    // ORGANIZATION: true,
    // DIMENSION: true,
  };
  listOfData = [];
  isTableSpinning = false; // 表格loading
  isDownLoadSpinning = false; // 导出loading
  isImportSpinning = false; // 导入loading
  currentCode = ""; // 当前code

  ngOnInit() {
    this.initData();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
  /**
   * 关闭弹窗
   */
  handClose() {
    this.drawerRef.close();
  }
  // 初始化
  async initData() {
    this.isTableSpinning = true;
    const res = await this.api.getDataShowCalRules(this.projectId).toPromise();
    const { demographicRules, dimensionsRules, organizationsRules } = res.data;
    const list = [
      {
        code: "DEMOGRAPHIC",
        name: "一级标签",
        rules: demographicRules.map((val) => ({
          id: val.id,
          name: {
            zh_CN: val.name.zh_CN,
            en_US: val.name.en_US,
          },
          isShowReport: val.isShowReport || false,
          isShowExcel: val.isShowExcel || false,
          isUpCalculate: val.isUpCalculate || false,
        })),
      },
      {
        code: "ORGANIZATION",
        name: "一级组织",
        rules: organizationsRules.map((val) => ({
          id: val.id,
          name: {
            zh_CN: val.name.zh_CN,
            en_US: val.name.en_US,
          },
          isShowReport: val.isShowReport || false,
          isShowExcel: val.isShowExcel || false,
          isUpCalculate: val.isUpCalculate || false,
        })),
      },
      {
        code: "DIMENSION",
        name: "一级维度",
        rules: dimensionsRules.map((val) => ({
          id: val.id,
          name: {
            zh_CN: val.name.zh_CN,
            en_US: val.name.en_US,
          },
          isShowReport: val.isShowReport || false,
          isShowExcel: val.isShowExcel || false,
          isUpCalculate: val.isUpCalculate || false,
        })),
      },
    ];
    this.listOfData = list;
    this.isTableSpinning = false;
  }

  // 恢复默认or保存
  async onSave() {
    // 默认是报告展示、报表展示全选。向上计算置灰
    this.isTableSpinning = true;
    let demographicRules = []; // 一级标签
    let organizationsRules = []; // 一级组织
    let dimensionsRules = []; //一级维度
    this.listOfData.forEach((item) => {
      if (item.code === "DEMOGRAPHIC") {
        demographicRules = item.rules.map((val) => ({
          id: val.id,
          name: {
            zh_CN: val.name.zh_CN,
            en_US: val.name.en_US,
          },
          isShowReport: val.isShowReport,
          isShowExcel: val.isShowExcel,
          isUpCalculate: val.isUpCalculate,
        }));
      } else if (item.code === "ORGANIZATION") {
        organizationsRules = item.rules.map((val) => ({
          id: val.id,
          name: {
            zh_CN: val.name.zh_CN,
            en_US: val.name.en_US,
          },
          isShowReport: val.isShowReport,
          isShowExcel: val.isShowExcel,
          isUpCalculate: val.isUpCalculate,
        }));
      } else if (item.code === "DIMENSION") {
        dimensionsRules = item.rules.map((val) => ({
          id: val.id,
          name: {
            zh_CN: val.name.zh_CN,
            en_US: val.name.en_US,
          },
          isShowReport: val.isShowReport,
          isShowExcel: val.isShowExcel,
          isUpCalculate: val.isUpCalculate,
        }));
      }
    });
    const params = {
      projectId: this.projectId,
      dimensionsRules: dimensionsRules,
      organizationsRules: organizationsRules,
      demographicRules: demographicRules,
    };
    const res = await this.api.setProjectDataShowCalRules(params).toPromise();
    if (res.result.code === 0) {
      this.msg.success("保存成功!");
      this.initData();
    } else {
      this.isTableSpinning = false;
    }
  }

  // 重置规则
  async onReset() {
    const res = await this.api.resetShowCalRules(this.projectId).toPromise();
    if (res.result.code === 0) {
      this.msg.success("重置成功!");
      this.initData();
    }
  }
  /**
   *  导出
   *  @author: Sid Wang
   *  @todo: 数据呈现/计算规则-导出
   *  @Date: 2025/03/21
   */
  onExport(code) {
    this.isDownLoadSpinning = true;
    this.currentCode = code;
    const codeMap = {
      DEMOGRAPHIC: "人口标签",
      ORGANIZATION: "组织",
      DIMENSION: "维度",
    };
    if (code === "DEMOGRAPHIC") {
      // 导出-人口标签
      this.api
        .exportProjectDataShowCalRulesDemographic(this.projectId)
        .subscribe((res) => {
          this.isDownLoadSpinning = false;
          this.currentCode = "";
          this.downUtil.downFile(res);
          this.msg.success(`${codeMap[code]}-导出成功!`);
        });
    } else if (code === "ORGANIZATION") {
      // 导出-组织
      this.api
        .exportProjectDataShowCalRulesOrganization(this.projectId)
        .subscribe((res) => {
          this.isDownLoadSpinning = false;
          this.currentCode = "";
          this.downUtil.downFile(res);
          this.msg.success(`${codeMap[code]}-导出成功!`);
        });
    } else if (code === "DIMENSION") {
      // 导出-维度
      this.api
        .exportProjectDataShowCalRulesDimension(this.projectId)
        .subscribe((res) => {
          this.isDownLoadSpinning = false;
          this.currentCode = "";
          this.downUtil.downFile(res);
          this.msg.success(`${codeMap[code]}-导出成功!`);
        });
    }
  }
  // 在组件类中添加方法
  customUpload(code: string) {
    return (item: UploadXHRArgs) => {
      // 创建表单数据
      const formData = new FormData();
      formData.append("file", item.file as any);
      this.uploadExcel(formData, item, code);
    };
  }
  // 导入-人口标签
  customReqload_DEMOGRAPHIC = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item, "DEMOGRAPHIC");
  };
  // 导入-组织
  customReqload_ORGANIZATION = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item, "ORGANIZATION");
  };
  // 导入-维度
  customReqload_DIMENSION = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    this.uploadExcel(formData, item, "DIMENSION");
  };

  /**
   * uploadExcel 上传配置
   */
  uploadExcel(formData, item, code) {
    const codeMap = {
      DEMOGRAPHIC: "人口标签",
      ORGANIZATION: "组织",
      DIMENSION: "维度",
    };
    this.currentCode = code;
    this.isImportSpinning = true;
    if (code === "DEMOGRAPHIC") {
      return this.api
        .importProjectDataShowCalRulesDemographic(formData, this.projectId)
        .subscribe(
          (res) => {
            console.log(res);
            if (res.result.code === 0) {
              item.onSuccess!();
              this.msg.success(`${codeMap[code]}-导入成功!`);
              this.isImportSpinning = false;
              this.currentCode = "";
              this.initData();
            } else {
              this.isImportSpinning = false;
              this.currentCode = "";
            }
          },
          (err) => {
            this.isImportSpinning = false;
            this.currentCode = "";
          }
        );
    } else if (code === "ORGANIZATION") {
      return this.api
        .importProjectDataShowCalRulesOrganization(formData, this.projectId)
        .subscribe(
          (res) => {
            if (res.result.code === 0) {
              item.onSuccess!();
              this.msg.success(`${codeMap[code]}-导入成功!`);
              this.isImportSpinning = false;
              this.currentCode = "";
              this.initData();
            } else {
              this.isImportSpinning = false;
              this.currentCode = "";
            }
          },
          (err) => {
            item.onError!(err, item.file!);
            this.isImportSpinning = false;
            this.currentCode = "";
          }
        );
    } else if (code === "DIMENSION") {
      return this.api
        .importProjectDataShowCalRulesDimension(formData, this.projectId)
        .subscribe(
          (event: HttpEvent<any>) => {
            let res: any = event;
            if (res.result.code === 0) {
              item.onSuccess!();
              this.msg.success(`${codeMap[code]}-导入成功!`);
              this.isImportSpinning = false;
              this.currentCode = "";
              this.initData();
            } else {
              this.isImportSpinning = false;
              this.currentCode = "";
            }
          },
          (err) => {
            item.onError!(err, item.file!);
            this.isImportSpinning = false;
            this.currentCode = "";
          }
        );
    }
  }

  onChangeCheckbox(rule){
    console.log(rule);
    if (rule.isShowReport && rule.isShowExcel) {
      rule.isUpCalculate = true;
    }
  }
}
