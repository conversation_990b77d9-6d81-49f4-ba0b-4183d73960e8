<ng-container
  *ngIf="disabled || (!permission && selected == 'CUSTOM_NORM'); else show"
>
  <button [disabled]="true" nz-button class="algorithm_select">
    <span [ngClass]="{ placeholder: !algorithmType }">{{
      algorithmType ? optionsMap[algorithmType] : "请输入"
    }}</span>
    <i nz-icon nzType="down" [ngClass]="{ placeholder: !algorithmType }"></i>
  </button>
</ng-container>
<ng-template #show>
  <button
    nz-button
    nz-dropdown
    [nzDropdownMenu]="menu4"
    class="algorithm_select"
    [nzClickHide]="false"
    [(nzVisible)]="visible"
    nzTrigger="click"
  >
    <span [ngClass]="{ placeholder: !algorithmType }">{{
      algorithmType ? optionsMap[algorithmType] : "请输入"
    }}</span>
    <i
      nz-icon
      [nzType]="visible ? 'up' : 'down'"
      [ngClass]="{ placeholder: !algorithmType }"
    ></i>
  </button>
</ng-template>
<nz-dropdown-menu #menu4="nzDropdownMenu">
  <ul nz-menu class="algorithm_ul">
    <li
      nz-menu-item
      (click)="handClick('HIT_RATE')"
      [ngClass]="{ active: selected == 'HIT_RATE' }"
    >
      <div class="item">击中率</div>
    </li>
    <li
      nz-menu-item
      (click)="handClick('CUSTOM_AVERAGE')"
      [ngClass]="{ active: selected == 'CUSTOM_AVERAGE' }"
    >
      <div class="item">平均分</div>
      <div class="average" *ngIf="selected == 'CUSTOM_AVERAGE'">
        <div>
          <label nz-checkbox [(ngModel)]="isUseAverage">
            按设置维度的总分的平均计算</label
          >
        </div>
        <div>
          <label
            nz-checkbox
            [(ngModel)]="isEnablePassGrade"
            style="margin-left: 0;"
            (ngModelChange)="checkboxChage($evemt)"
          >
            <span style="margin-left: 3px;margin-right: 0;"
              >设置各维度总和的及格</span
            >
          </label>
          <nz-input-number
            nzSize="small"
            style="width: 55px; margin-right:8px;"
            [(ngModel)]="passGrade"
            [nzMin]="1"
            [nzMax]="100"
            [nzStep]="1"
            [nzPrecision]="0"
          ></nz-input-number>
          <label>分</label>
        </div>
      </div>
    </li>
    <li
      *ngIf="permission"
      nz-menu-item
      (click)="handClick('CUSTOM_NORM')"
      [ngClass]="{ active: selected == 'CUSTOM_NORM' }"
    >
      <div class="item">常模</div>
      <div class="norm" *ngIf="selected == 'CUSTOM_NORM'">
        <textarea
          nz-input
          style="padding: 0 8px;"
          placeholder="请输入常模"
          [(ngModel)]="assessmentNormstext"
          [nzAutosize]="{ minRows: 2, maxRows: 6 }"
        ></textarea>
      </div>
    </li>
    <li *ngIf="selected == 'CUSTOM_AVERAGE' || selected == 'CUSTOM_NORM'">
      <div class="algorithm_btns">
        <span (click)="onCancel()">取消</span>
        <span (click)="onConfirm()">确认</span>
      </div>
    </li>
  </ul>
</nz-dropdown-menu>
