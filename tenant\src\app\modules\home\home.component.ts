import { Component, OnInit } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd";
import { HttpClient } from "@angular/common/http";
import { timer } from "rxjs";
import { ActivatedRoute, Router } from "@angular/router";
declare const document: any;
@Component({
  selector: "app-home-page",
  templateUrl: "./home.component.html",
  styleUrls: ["./home.component.less"],
})
export class HomeComponent implements OnInit {
  balance: any; // 肯豆余额
  process: string; // 进行中活动数 百分比 ,
  projectCount: number; // 进行中活动数 ,
  runningCount: number; // 进行中活动数
  announcedCount: number;
  resData: any;
  tenantUrl = "/tenant-api";
  showchecked = false;
  noviceGuidance = false;
  step1 = false;
  step2 = false;
  step3 = false;
  step4 = false;
  showmock = false;
  newShow = true;
  hiddencard = 0;
  showzero = true;
  tabs = [
    "盘点",
    "培养",
    "校招",
    "社招",
    "选拔",
    "职业指导",
    "团队建设",
    "调研",
  ];
  selectedshow = true;
  selectedIndex = 0;
  effect = "scrollx";
  array = [];
  oldinterval = false;
  newinterval = true;
  Menustype = "1";
  Menuslive = true;
  carousellist = [];
  constructor(
    public msg: NzMessageService,
    private http: HttpClient,
    private route: Router
  ) {}
  ngOnInit() {
    const api = `${this.tenantUrl}/survey/sagittarius/homeStatsVO`;
    this.http.get(api).subscribe((res: any) => {
      console.log(res);
      this.noviceGuidance = res.data.noviceGuidance;
      this.showmock = this.noviceGuidance;
      if (this.noviceGuidance) {
        this.hiddencard = 1;
      }
      let isEnableWxWorkMsg = res.data.isEnableWxWorkMsg;
      let isEnableDingMsg = res.data.isEnableDingMsg;
      let isEnableMoka = res.data.isEnableMoka;
      let isEnableFeishuMsg = res.data.isEnableFeishuMsg;
      let data = res.data;
      this.resData = res.data;
      this.balance = data.knxBeanAccount ? data.knxBeanAccount.balance : 0;
      this.process = data.projectSummaryVO ? data.projectSummaryVO.process : 0;
      this.projectCount = data.projectSummaryVO
        ? data.projectSummaryVO.projectCount
        : 0;
      this.announcedCount = data.projectSummaryVO
        ? data.projectSummaryVO.announcedCount
        : 0;
      this.runningCount = data.projectSummaryVO
        ? data.projectSummaryVO.runningCount
        : 0;
      sessionStorage.setItem(
        "noviceGuidance",
        JSON.stringify(this.noviceGuidance)
      );
      sessionStorage.setItem(
        "isEnableWxWorkMsg",
        JSON.stringify(isEnableWxWorkMsg)
      );
      sessionStorage.setItem(
        "isEnableDingMsg",
        JSON.stringify(isEnableDingMsg)
      );
      sessionStorage.setItem(
        "isEnableFeishuMsg",
        JSON.stringify(isEnableFeishuMsg)
      );
      sessionStorage.setItem("isEnableMoka", JSON.stringify(isEnableMoka));
    });
    this.getPlatforms();

    let opens = sessionStorage.getItem("newsshow");
    if (opens == "true") {
      this.newShow = true;
    } else if (opens == "false") {
      this.newShow = false;
    }
    this.getallcarouse();
  }
  nzSelectChange(e) {
    this.selectedshow = false;

    timer(300).subscribe(() => {
      this.selectedshow = true;
    });
    this.selectedIndex = e;
    console.log(this.selectedIndex);
  }
  gotoactivemange() {
    this.route.navigateByUrl(`/project-manage`);
    sessionStorage.setItem("activepage", null);
  }
  closed() {
    if (this.showchecked) {
      const api = `${this.tenantUrl}/survey/tenant/hideNoviceGuidance`;
      this.http.post(api, {}).subscribe((res: any) => {});
      this.showmock = false;
      this.noviceGuidance = true;
    } else {
      this.noviceGuidance = false;
      this.step1 = true;
    }
  }
  experience() {
    if (this.showchecked) {
      const api = `${this.tenantUrl}/survey/tenant/hideNoviceGuidance`;
      this.http.post(api, {}).subscribe((res: any) => {});
      this.showmock = false;
      this.noviceGuidance = true;
    } else {
      this.noviceGuidance = false;
      this.step1 = true;
    }

    // this.showmock = false
    // this.noviceGuidance = false
    // if(this.showchecked){

    // }
  }
  next1() {
    this.step1 = false;
    this.step2 = true;
    this.hiddencard = 1;
  }
  next2(e) {
    this.hiddencard = e;

    if (e == 4) {
      this.step2 = false;
      this.step3 = true;
    }
  }
  next3() {
    this.step3 = false;
    this.step4 = true;
  }
  next4() {
    this.step4 = false;
    this.showmock = false;
    this.noviceGuidance = false;
  }
  jumprun() {
    this.hiddencard = 0;
    this.step1 = false;
    this.step2 = false;
    this.step3 = false;
    this.step4 = false;
    this.showmock = false;
    this.noviceGuidance = false;
  }
  getnewlead() {
    this.showmock = true;
    this.step1 = true;
    // this.noviceGuidance = true
  }
  getPlatforms() {
    const api = `${this.tenantUrl}/survey/sagittarius/listPlatformAnnouncement`;
    this.http.get(api).subscribe((res: any) => {
      this.array = res.data.list;
    });
  }
  closeNews() {
    this.newShow = false;
    sessionStorage.setItem("newsshow", JSON.stringify(this.newShow));
  }
  nzAfterChange(e) {
    this.oldinterval = false;
    this.newinterval = true;

    // clearInterval(this.interval);
  }
  choosediv(e) {
    // this.marquee(e)
  }
  marquee(num?) {
    let nums = 0;
    if (num == undefined) {
      nums = 0;
    } else {
      nums = Math.abs(num);
    }
  }
  clickMenus(type) {
    this.Menustype = type;
    this.Menuslive = false;
    setTimeout(() => {
      this.Menuslive = true;
    }, 200);
  }

  getallcarouse() {
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "http://***********/";
    }
    const api = `${this.tenantUrl}/survey/standard/carousel/listAll`;
    this.http.get(api).subscribe((res: any) => {
      console.log(res);

      this.carousellist = res.data.list;
      this.carousellist.forEach((res) => {
        res.previewImg = `${baseUrl}api/file/www/${res.fileUrl}`;
      });
    });
  }
}
