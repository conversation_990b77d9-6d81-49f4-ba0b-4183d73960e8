* {
  margin: 0;
  padding: 0;
}

.algorithm {
  &_select {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;

    .placeholder {
      color: #bfbfbf !important;
    }

    &:hover {
      color: #bfbfbf !important;
    }

    i {
      font-size: 12px !important;
    }
  }

  &_ul {
    padding: 8px;

    li {
      padding: 8px;
      min-width: 250px;

      &:hover {
        .item {
          color: #409EFF;
        }
      }

      &:active {
        .item {
          color: #409EFF;
        }
      }

      .average,
      .norm {
        padding-bottom: 8px;
        margin-top: 16px;
        display: flex;
        flex-flow: column;
        justify-content: flex-start;
      }
    }

    .active {
      background-color: #e6f7ff;

      .item {
        color: #409EFF;
      }
    }
  }

  &_btns {
    padding-top: 8px;
    display: flex;
    justify-content: flex-end;

    span {
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      padding: 5px 20px;
      border: 1px solid #409EFF;
      border-radius: 15px;
      cursor: pointer;

      &:nth-child(1) {
        background-color: #fff;
        color: #409EFF;
      }

      &:nth-child(2) {
        background-color: #409EFF;
        color: #fff;
        margin-left: 8px;
      }
    }
  }
}