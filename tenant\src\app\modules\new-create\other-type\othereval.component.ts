import { Component, OnInit, Inject, Input, ViewChild } from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import { Router, ActivatedRoute } from "@angular/router";
import {
  NzModalService,
  NzMessageService,
  NzDrawerService,
} from "ng-zorro-antd";
import { NewCreateService } from "../new-create.service";
import { NewPrismaService } from "../../new-prisma/new-prisma.service";
import * as differenceInCalendarDays from "date-fns/difference_in_calendar_days";
import { AdvancedMoreSetting } from "../adv_set/advsetting.component";
import { timer } from "rxjs";
import { ITokenService, DA_SERVICE_TOKEN } from "@knz/auth";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { ModalContentComponent } from "../../../shared/tip-modal/modal-content/modal-content.component";
import _ from "lodash";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { TipDescComponent } from "../tip-desc/tip-desc.component";

import { ScaleExpansionComponent } from "../scale-expansion/scale-expansion.component";
import { TopicDistributionComponent } from "../topic-distribution/topic-distribution.component";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { KnxFunctionPermissionService } from '@knx/knx-ngx/core'
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-new-other",
  templateUrl: "./othereval.component.html",
  styleUrls: ["./othereval.component.less"],
})
export class OtherEvalComponent implements OnInit {
  @ViewChild(ScaleExpansionComponent, { static: false })
  scaleExpansion: ScaleExpansionComponent;
  @ViewChild(TopicDistributionComponent, { static: false })
  topicDistribution: TopicDistributionComponent;
  @Input() comeReportType;
  shownumber = 0;
  modelImageUrl = "";
  showIndex = 0;
  showmock = false;
  noviceGuidance = false;
  today = new Date();
  isNzOkLoading = false;
  isNzRELoading = false;
  isNzPreLoading = false;
  tooltype = "";
  factortable: any = {};
  normaldatas = [];
  chooseIndex = 0;
  chooseName = "";
  tabledata = [];
  toolsname = [];
  dateRange = []; // 活动周期
  addfactorshow = false; //自定义弹窗
  analysisFactorTitle;
  showAnalysisFactor;
  nzBodyStyle: any = { padding: "0" };
  factorisSpinning = false;
  echartData = [];
  isVisiblemodal = false;
  factorshow = false;
  limitcode = "";
  modaltip = [];
  descTip = [];
  TipJobtip = [];
  isSpinning = false;
  standardQuestionnaireId;
  projectId = "";
  projectType = "";
  questionnaireId = "";
  visibleDesc = false;
  tinyconfig = {};
  Isactivelan = true;
  nzSimple = false;
  standardReportType;
  isdisabled = false;
  isUpdate = false;
  isUpdateing = false;
  projectReportDimensionslist = [];
  showprojectReportlist = [];
  deletecode;
  standardQuestionnaireIds = [];
  customfactorshow = false;
  //tip
  ismodalclosed = false;
  visibleJob = false;
  reportTemplateId;
  radioValue;
  radioCode;
  radioCodelist = [];
  checkedvalue = false;
  radiolist = [];
  Tipjobslist;
  createtipid;
  //tip
  showname = false;
  changeeditorName: any = {};
  projectingtdata;
  changeNumber = 0;
  confirmModal;
  oIsEmailReport: false;
  prismaData: any = {
    name: {
      zh_CN: "",
      en_US: "",
    },
    startTime: null,
    endTime: null,
    analysisFactorDto: [], //选中的人口标签
    questionNumInOnePage: "5", // 默认时间
    isCheckLicenseNotice: true, // 许可声明
    isCheckedAnswerValid: false, // 校验答案
    isEnableWelcomePage: false,
    welcomePage: {
      zh_CN: "",
      en_US: "",
    },
    endPage: {
      zh_CN: "",
      en_US: "",
    },
    isEnableEndPage: false,
    isCheckAnswerInstructions: true, // 作答说明
    isPublicReport: false, // 允许测评者查看报告
    isEmailReport: false,
    sequence: "QUESTION_TYPE",
    isCustomRoleWeight: false,
    isShowDemographicQuestion: true, // 是否显示人口信息学题
    isShowPreAnswer: true, //
    answerMode: "MULTI",
    isHideRole: false,
    relationPermissions: [],
    isShowRank: true,
    isShowScore: true,
    isShowUnderstand: true,
    language: "zh_CN",
    optionalLanguages: ["zh_CN"],
    availableLanguages: ["zh_CN", "en_US"], // 新增语言
    isShowKnxLogo: true, // 显示射手座logo
    isShowBackgroundPic: false,

    logoFile: "", // 企业客户logo
    pcBackgroundPic: "",
    mobileBackgroundPic: "",
    standardDemographicDTO: {
      standardDemographicIds: [],
      type: "DEMOGRAPHIC_DEFAULT", // 人口学 默认
    }, // 除了敬业度都要传这个

    surveyType: "ASSESSMENT",
    projectReportDimensions: [],
    standardQuestionnaireDTO: {
      code: "",
      standardQuestionnaireDimensionDTO: [],
    },
    projectReportParentDimensions: [],
    answerDescription: {
      zh_CN: "",
      en_US: "",
    },
  };
  factornameshow = false;
  factornames = {
    zh_CN: "",
    en_US: "",
  };
  factorindex = null;

  AllrelationPermissions = [];
  reportTypes = [];
  paymentConfig = [];
  reportStyles = [];
  DifferencePrice: any;
  pricenums = 1;
  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "/new-activity",
      name: "新建活动",
      Highlight: false,
    },
    {
      path: "",
      name: "活动设置",
      Highlight: true,
    },
  ];
  backUrl: string = "/new-activity";
  backUrlName: string = "新建活动";

  includeCa = false;
  jobcodeshow = false;

  // AT-考试设置
  isEditAT = false;
  atTip = []; // AT-关联任务
  atAdaptiveDefaultAnalysisFactors = []; // AT自适应默认人口标签
  visibleExam = false; // 考试弹窗
  atMode = "FIX"; // FIX固定 ADAPTIVE自适应
  originalAtMode = "FIX"; // FIX固定 ADAPTIVE自适应  缓存原先atMode
  atSetting = null; // AT数据
  difficultyOptions = [
    {
      label: "困难",
      value: "DIFFICULT",
    },
    {
      label: "中等",
      value: "MEDIUM",
    },
    // {
    //   label: "容易",
    //   value: "SIMPLE",
    // },
  ]; // 考试难度-字典
  answerTimeLimitModeOptions = [
    {
      label: "整卷限时",
      value: "FULL_QUESTIONNAIRE",
    },
    {
      label: "每题限时",
      value: "PER_QUESTION",
    },
    {
      label: "不限时间",
      value: "NONE",
    },
  ]; // 作答时间-字典
  nums_10 = [];
  nums_24 = [];
  nums_60 = [];
  selfAdaptionOptions = {
    initQuestionDifficultyOption: [{ label: "中等", value: "MEDIUM" }],
    answerTimeLimitMode: [
      { label: "每题限时", value: "PER_QUESTION" },
      { label: "01分00秒", value: "0-0-1-0" },
    ],
    otherSetting: [
      { label: "进入下一题后不可逆", value: "isCannotAnswerBack" },
      { label: "一页一题和自适应题目顺序", value: "ADAPTIVE" },
    ],
    atAdaptiveDefaultAnalysisFactors: [],
  };
  tenantApi: string = "/tenant-api";
  // 固定试卷设置
  fixdForm: FormGroup;
  // 自适应试卷设置
  selfAdaptionForm: FormGroup;
  // 其他设置
  otherForm: FormGroup;
  permission: boolean; // 超管权限
  isOtherChange: boolean; // 是否可以修改其他设置
  // 管理身份：固定试卷--可以修改，有icon
  // 管理身份。自适应试卷--可以修改，有icon
  // 租户身份：固定试卷----可以修改，有icon
  // 租户身份：自适应试卷----不可以修改，无icon
  atTmpDesc = null; // 默认自定义作答说明，at用
  // 是否开启自适应
  isOpenAdaption = false;
  // tip list
  hasQuestionnaireTipItems = false; // 是否是新的tip
  questionnaireTipItems = null; // 新Tip-所有维度缓存
  checkReportTypes = []; // 新Tip-选中所有的父维度类型 arr
  checkTipMenuItem = null; // 新Tip=当前选中的父维度 obj
  checkReportType = ""; // 新Tip=当前选中的父维度
  checkDimensionMap = {}; // 选中的维度指标
  tipCurrentAnswerDescription = {
    en_US: "",
    zh_CN: "",
  }; // 新tip-当前选中维度的作答说明
  tipCurrentDimensions = []; // 新tip-当前选中维度的关键指标
  isTipJobtip = false; // 新Tip是否确认过岗位
  // 自定义----------------------------------------------------------
  question_book = []; // 题本
  atAdaptiveDesc = null;
  lan = "zh_CN";
  i18n = [
    { name: "中文", value: "zh_CN" },
    { name: "美国英语", value: "en_US" },
  ];
  isShowAll = true;//一键显示/隐藏人口学标签
  constructor(
    private routerInfo: ActivatedRoute,
    private router: Router,
    private modalService: NzModalService,
    private drawerService: NzDrawerService,
    private msg: NzMessageService,
    private http: NewCreateService,
    private prismaApi: NewPrismaService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private HttpClient: HttpClient,
    private modal: NzModalService,
    private fb: FormBuilder,
    private sanitizer: DomSanitizer,
    private customMsg: MessageService,
    public knxFunctionPermissionService: KnxFunctionPermissionService,
        public permissionService: PermissionService,
  ) {}

  changeHidden(e, item, index) {
    e.stopPropagation();
    item.isHidden = !item.isHidden;
  }

  ngOnInit(): void {
    this.backUrl = localStorage.getItem("backurl");
    this.permission = this.permissionService.isPermission();
    const url = localStorage.getItem("backurl").split("?")[0];
    switch (url) {
      case "/project-manage/home-detail":
        this.backUrlName = "活动详情";
        break;
      case "/project-manage/home":
        this.backUrlName = "活动管理";
        break;
      case "/new-activity":
        this.backUrlName = "新建活动";
        break;

      default:
        this.backUrlName = "新建活动";
        break;
    }
    this.Breadcrumbs[1].name = this.backUrlName;
    this.Breadcrumbs[1].path = this.backUrl;
    localStorage.setItem("break", JSON.stringify(this.Breadcrumbs));
    const _this = this;
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }
    this.tinyconfig = {
      height: 300,
      fontsize_formats:
        "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 32pt 36pt",
      plugins: [
        "lists",
        "advlist",
        "autolink",
        "link",
        "image",
        "imagetools",
        "preview",
        "table",
        "textcolor",
        "code",
        "hr",
        "wordcount",
        "searchreplace",
        "paste",
      ],
      menubar: "edit insert view format table tools",
      menu: {
        edit: {
          title: "Edit",
          items:
            "undo redo | cut copy paste pastetext | selectall | searchreplace",
        },
        view: { title: "View", items: "preview" },
        insert: { title: "Insert", items: "image link inserttable | hr " },
        format: {
          title: "Format",
          items:
            "bold italic underline strikethrough superscript subscript codeformat | align | removeformat",
        },
        tools: { title: "Tools", items: "code" },
        table: {
          title: "Table",
          items:
            "inserttable | cell row column | advtablesort | tableprops deletetable",
        },
      },
      relative_urls: false,
      remove_script_host: false,
      document_base_url: baseUrl,
      images_upload_url: `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`, // 配置你图片上传的url
      // ---------------------------------------------------------------------- #12290
      paste_word_valid_elements: "*[*]", // 允许保留所有元素和属性
      paste_retain_style_properties: "all", // 保留所有样式
      paste_webkit_styles: "all", // 保留所有样式
      images_upload_handler: (blobInfo, success, failure) => {
        const token = _this.tokenService.get().token;
        let headers = new HttpHeaders({ token: token, Authorization: token });
        let fileType = blobInfo.filename().split(".")[1];
        let formData;
        formData = new FormData();
        formData.append("file", blobInfo.blob(), blobInfo.filename());
        formData.append("isPublic", "true");
        formData.append("effectiveFileTypes", "." + fileType.toLowerCase());
        formData.append("businessType", "SAG_REPORT");
        this.HttpClient.post(
          `${this.tenantApi}/survey/standard/file/uploadWithBusinessType`,
          formData,
          {
            headers: headers,
          }
        ).subscribe(
          (response: any) => {
            if (response) {
              this.HttpClient.get(
                `${this.tenantApi}/survey/standard/file/getFileInfoById?fileId=${response.data.id}`,
                {
                  headers: headers,
                }
              ).subscribe((imgurl: any) => {
                let url = `${baseUrl}api${imgurl.data.url}`; // 这里是你获取图片url
                // 把图片链接，img src标签显示图片的有效链接放到下面回调函数里
                success(url);
              });
            } else {
              if (response && response.rtnMsg) {
                failure(response.rtnMsg);
              } else {
                failure("上传失败：未知错误");
              }
            }
          },
          (error1) => {
            failure("上传失败：未知错误");
          }
        );
      },
    };
    this.tabledata = JSON.parse(localStorage.getItem("noprismadata"));
    this.projectId = this.routerInfo.snapshot.queryParams.projectId; // 修改用的

    if (this.routerInfo.snapshot.queryParams.projectType) {
      this.projectType = this.routerInfo.snapshot.queryParams.projectType;
    }
    if (this.projectId) {
      this.standardQuestionnaireIds = JSON.parse(
        sessionStorage.getItem("standardQuestionnaireIds")
      );

      this.prismaData.projectReportDimensions = [];
      if (this.projectType == "ANNOUNCED" || this.projectType == "PREVIEW") {
        this.isUpdate = true; // 未发布状态下的更新
      }

      if (
        this.projectType != "PREVIEW" &&
        this.projectType != "ANNOUNCED" &&
        this.projectType != undefined
      ) {
        this.isUpdateing = true; // 发布状态下的更新
        this.isdisabled = true;
      }
      this.getProjectSetting();
    } else {
      this.setLanOptions();
      this.NewCreate();
    }
    // 初始化考试时间选项
    const arrs_10 = [];
    const arrs_24 = [];
    const arrs_60 = [];
    for (let index = 0; index < 60; index++) {
      if (index <= 10) {
        arrs_10.push(index);
      }
      if (index < 24) {
        arrs_24.push(index);
      }
      arrs_60.push(index);
    }
    this.nums_10 = arrs_10;
    this.nums_24 = arrs_24;
    this.nums_60 = arrs_60;

    // 试卷模板
    // 固定试卷
    this.fixdForm = this.fb.group({
      difficulty: ["MEDIUM", [Validators.required]],
      answerTimeLimitMode: ["FULL_QUESTIONNAIRE", [Validators.required]],
      isRandomSampleQuestions: [false],
      isCannotAnswerBack: [false],
      // 填答限制时间
      days: [0],
      hours: [1],
      minutes: [0],
      seconds: [0],
    });
    this.initSelfAdaptionForm(0);
    // 其他设置
    this.otherForm = this.fb.group({
      isCanSwitchScreen: [false],
      switchScreenTimes: [1],
      isCanReEnter: [false],
      reEnterInterval: [2],
      isEnableQuestionProtection: [false],
    });
  }

  backU(item) {
    this.router.navigateByUrl(item.path);
  }

  NewCreate() {
    let standardQuestionnaireIds = [];
    this.tabledata.forEach((res) => {
      standardQuestionnaireIds.push(res.id);
      res.styles = [];
      res.reportProjects.forEach((val) => {
        if (val.checked) {
          res.styles.push(val.style);
        }
      });
      this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO.push(
        {
          name: res.name.zh_CN,
          isSelected: false,
          questionnaireId: res.id,
          standardQuestionnaireDimensionIds: [],
          styles: res.styles,
          standardReportTemplateId: "",
        }
      );
      if (this.comeReportType == "TIP_NEW_2") {
        res.standardDimensionResultVOList.forEach((item) => {
          if (item.checked) this.reportTypes.push(item.reportType);
        });
      } else {
        this.reportTypes.push(res.reportType);
      }
      //
      if (res.reportProjects) {
        res.reportProjects.forEach((val) => {
          if (val.checked) {
            this.paymentConfig.push(val.paymentConfig);
            this.reportStyles.push(val.style);
          }
        });
      }
    });
    this.toolsname = [];
    this.normaldatas = [];
    this.tabledata.forEach((val) => {
      this.toolsname.push({
        title: val.name,
        description: val.description,
        questionnaireId: val.id,
        reportType: val.reportType,
      });
    });
    this.AllrelationPermissions = [];
    standardQuestionnaireIds.forEach((item) => {
      this.getSetInfo(this.projectId, item);
    });
  } //新建活动

  isZero(m) {
    return m < 10 ? "0" + m : m;
  } //时间处理

  formatDate(shijianchuo, type) {
    const time = new Date(shijianchuo); // 需要使用Date格式进行日期转化，若是时间戳、字符串时间，需要通过new Date(..)转化

    const y = time.getFullYear();

    const m = time.getMonth() + 1;

    const d = time.getDate();

    const h = time.getHours();

    const mm = time.getMinutes();

    const s = time.getSeconds();
    if (type === "date") {
      return y + "-" + this.isZero(m) + "-" + this.isZero(d);
    } else {
      return this.isZero(h) + ":" + this.isZero(mm) + ":" + this.isZero(s);
    }
  } //日期格式化
  onChange(result: Date): void {
    if (result[0]) {
      if (!this.prismaData.startTime) {
        this.dateRange[0].setHours(0);
        this.dateRange[0].setMinutes(0);
      } else {
        const startTime_ = new Date(this.prismaData.startTime);
        if (
          startTime_.getHours() !== this.dateRange[0].getHours() &&
          startTime_.getMinutes() !== this.dateRange[0].getMinutes()
        ) {
          this.dateRange[0].setHours(startTime_.getHours());
          this.dateRange[0].setMinutes(startTime_.getMinutes());
        }
      }
      this.prismaData.startTime = `${this.formatDate(
        this.dateRange[0],
        "date"
      )}T${this.formatDate(this.dateRange[0], "time")}`;
    } else {
      this.prismaData.startTime = null;
    }

    if (result[1]) {
      if (!this.prismaData.endTime) {
        this.dateRange[1].setHours(23);
        this.dateRange[1].setMinutes(30);
      } else {
        const endTime_ = new Date(this.prismaData.endTime);
        if (
          endTime_.getHours() !== this.dateRange[1].getHours() &&
          endTime_.getMinutes() !== this.dateRange[1].getMinutes()
        ) {
          this.dateRange[1].setHours(endTime_.getHours());
          this.dateRange[1].setMinutes(endTime_.getMinutes());
        }
      }
      this.prismaData.endTime = `${this.formatDate(
        this.dateRange[1],
        "date"
      )}T${this.formatDate(this.dateRange[1], "time")}`;
    } else {
      this.prismaData.endTime = null;
    }
    this.prismaData.dateRange = this.dateRange;
  } //日期选中回调

  getSetInfo(projectId, standardQuestionnaireId) {
    const noprismadata = JSON.parse(localStorage.getItem("noprismadata"));
    const currentTabNoprismadata = noprismadata
      ? noprismadata.filter((item) => item.id === standardQuestionnaireId)
      : [];
    const reportStyleSelected =
      currentTabNoprismadata.length > 0
        ? currentTabNoprismadata[0].reportProjects
            .filter((item) => item.checked)
            .map((item) => item.style)
        : [];
    const pramas = {
      projectId: projectId,
      standardQuestionnaireId: standardQuestionnaireId,
      reportTypeList:
        this.comeReportType == "TIP_NEW_2" ? this.reportTypes : [],
      reportStyleList: reportStyleSelected,
      editFlag: true,
    };
    this.prismaApi.listByQuestionnaireCodeCreate(pramas).subscribe((item) => {
      // 获取保存过，或者发布过的维度
      if (item.result.code == 0) {
        // 是否为新版Tip
        if (
          item.data.questionnaireTipItems &&
          item.data.questionnaireTipItems.length > 0
        ) {
          this.hasQuestionnaireTipItems = true;
          // 新tip-子问卷
          this.questionnaireTipItems = item.data.questionnaireTipItems;
          if (projectId) {
            // 根据活动的父维度指标，匹配出对应问卷
            const types = [];
            item.data.questionnaireTipItems.forEach((a) => {
              item.data.projectReportParentDimensions.forEach((b) => {
                if (
                  a.parentDimension == b.code ||
                  a.anotherName.zh_CN == b.name
                ) {
                  types.push(a.reportType);
                }
              });
            });
            this.checkReportTypes = types;
            // 根据TIP保存地子指标，构建checkDimensionMap
            this.questionnaireTipItems.forEach((menu) => {
              let codes = [];
              menu.detailedScoreConfigs.forEach((sup) => {
                sup.detailedScoreChildDimensions.forEach((son) => {
                  if (sup.isSelect && son.isSelected) {
                    // this.checkDimensionMap[menu.reportType].push(son.code)
                    codes.push(son.code);
                  }
                });
              });
              if (codes.length > 0) {
                this.checkDimensionMap[menu.reportType] = codes;
              }
            });
          } else {
            this.checkReportTypes = item.data.questionnaireTipItems.map(
              (val) => val.reportType
            );
          }
        }
        // 获取AT-自适应需要的人口标签
        if (item.data.atAdaptiveDefaultAnalysisFactors) {
          this.atAdaptiveDefaultAnalysisFactors =
            item.data.atAdaptiveDefaultAnalysisFactors;
          // 自适应考卷选项 人口信息分析用
          this.selfAdaptionOptions.atAdaptiveDefaultAnalysisFactors = item.data.atAdaptiveDefaultAnalysisFactors.map(
            (val) => ({
              label: val.name.zh_CN,
              value: val.code,
            })
          );
        }
        this.Processing(item.data);
      }

      let checkedCount =  this.prismaData.analysisFactorDto.filter((item) => { return item.isChecked;}).length;
      let hiddenCount = this.prismaData.analysisFactorDto.filter((item) => {return item.isChecked&&item.isHidden;}).length;
      let showCount = this.prismaData.analysisFactorDto.filter((item) => {return item.isChecked&&!item.isHidden;}).length;
      if(hiddenCount == checkedCount) {
        this.isShowAll = true;
      }

      if(showCount == checkedCount) {
      this.isShowAll = false;
      }
      });
  } //获取活动信息，如：选中维度，人口标签等
  //鼠标移入增加维度说明鼠标移入
  getnewEnums(data) {
    data.dimensionLevelEnumsZHnew = [];
    data.dimensionLevelEnums.forEach((res) => {
      if (res != "NONE") {
        data.dimensionLevelEnumsZHnew.push({
          code: res,
        });
      }
    });
    data.dimensionLevelEnumsZHnew.forEach((item) => {
      if (item.code == "HIGH") {
        item.name = "高层";
      }
      if (item.code == "MIDDLE") {
        item.name = "中层";
      }
      if (item.code == "PRIMARY") {
        item.name = "基层";
      }
      if (item.code == "PERSONAL") {
        item.name = "个人贡献者";
      }
    });
  }
  Processing(data) {
    this.normaldatas.push(data);
    this.chooseIndex = 0;
    this.standardReportType = this.routerInfo.snapshot.queryParams
      .standardReportType
      ? this.routerInfo.snapshot.queryParams.standardReportType
      : data.surveyStandardSagReportTemplate.standardReportType;
    data.dimensionLevelEnumsZH = [];
    if (data.surveyStandardQuestionnaire.reportType == "CA") {
      this.getnewEnums(data);
    }
    setTimeout(() => {
      this.normaldatas.forEach((item, index) => {
        if (item.questionnaireId) {
          if (
            item.questionnaireId ==
            this.toolsname[this.showIndex].questionnaireId
          ) {
            this.showprojectlist(index, data);
          }
        } else {
          if (
            item.surveyStandardQuestionnaire.name.zh_CN ==
            this.toolsname[this.showIndex].title.zh_CN
          ) {
            this.showprojectlist(index, data);
          }
        }
      });
    }, 300);
  } //从接口中获取数据后，处理数据
  showprojectlist(index, data) {
    this.factortable = this.normaldatas[index];
    this.factortable.standardAnalysisFactorVOS = this.normaldatas[0].standardAnalysisFactorVOS;
    this.prismaData.analysisFactorDto = this.factortable.standardAnalysisFactorVOS;
    this.standardQuestionnaireId = this.factortable.standardQuestionnaireId;

    this.prismaData.relationPermissions = this.factortable.relationPermissions;
    this.prismaData.answerDescription = this.factortable.answerDescription
      ? this.factortable.answerDescription
      : this.factortable.surveyStandardQuestionnaire.answerDescription;

    // 新tip-岗位关联任务若无CA展示JOB，若有不展示
    if (this.hasQuestionnaireTipItems && this.checkReportTypes.includes("CA")) {
      this.TipJobtip = [];
    } else {
      this.TipJobtip = this.factortable.relationPermissions.filter((res) => {
        return res.type == "JOB";
      });
    }
    if (this.hasQuestionnaireTipItems) {
      this.checkTipIsJob(); // 新Tip-子维度禁用控制
    }
    this.descTip = this.factortable.relationPermissions.filter((res) => {
      return res.type == "ANSWER_DESCRIPTION";
    });
    // 自定义-题本
    this.question_book = this.factortable.relationPermissions.filter((res) => {
      return res.type == "QUESTION_BOOK";
    });
    if (this.factortable.dimensionLevelEnumsZH.length != 0) {
      this.chooseName = this.factortable.dimensionLevelEnumsZH[0].code;
    } else {
      this.chooseName = "NONE";
    }
    if (
      this.standardReportType == "TIP_NEW_2" ||
      this.standardReportType == "TIP_NEW"
    ) {
      this.getTipjobs(this.factortable.standardReportTemplateId);
      if (this.factortable.projectReportParentDimensions.length != 0) {
        this.prismaData.projectReportParentDimensions = this.factortable.projectReportParentDimensions;
        this.prismaData.projectReportParentDimensions.forEach((elem) => {
          this.factortable.detailedScoreConfigs.forEach((val) => {
            if (elem.name == val.name.zh_CN) {
              val.checked = true;
            }
          });
        });
      } else {
        this.factortable.detailedScoreConfigs.forEach((res) => {
          res.showfactor = true;
          res.checked = true;
        });
        this.getParentDimensions();
      }
      this.factortable.detailedScoreConfigs.forEach((item) => {
        if (item.checked) {
          item.showfactor = true;
        } else {
          item.showfactor = false;
        }
      });
      this.checkcluedCa(); //检测是否有ca
    }
    if (this.factortable.surveyStandardQuestionnaire.reportType == "CA") {
      this.getCajobs();
    }

    // 展示第一个模型
    // 如果AT默认人口标签存在
    if (
      this.reportTypes.includes("AT") ||
      this.checkReportTypes.includes("AT")
    ) {
      this.getDescDefault();
      if (this.atMode == "ADAPTIVE") {
        this.checkAtAdaptiveDefaultAnalysisFactors();
      }
      this.atTip = this.factortable.relationPermissions.filter((res) => {
        return res.type == "AT_QUESTIONNAIRE_MODE";
      });
    }

    if (this.projectId) {
      data.detailedScoreConfigs.forEach((item) => {
        if (item.isSelect) {
          item.detailedScoreChildDimensions.forEach((res) => {
            if (res.isSelected) {
              this.prismaData.projectReportDimensions.push({
                code: res.code,
                name: res.name.zh_CN,
                parentName: item.name.zh_CN,
                isSelect: true,
                // standardQuestionnaireId: this.standardQuestionnaireId,
                standardReportTemplateId: this.factortable
                  .surveyStandardSagReportTemplate.id,
              });
            }
          });
        }
      }); //交互给后台的子维度选择
      this.getCheckedname(data, this.prismaData.projectReportDimensions);
    }
    this.normaldatas.forEach((items) => {
      items.relationPermissions.forEach((vals) => {
        vals.nametype = items.surveyQuestionnaire
          ? items.surveyQuestionnaire.name.zh_CN
          : items.surveyStandardQuestionnaire.name.zh_CN;
        if (vals.type == "ANSWER_DESCRIPTION")
          this.AllrelationPermissions.unshift(vals);
      });
    });
  }

  loadDataMap(data) {
    let tmpAttr: any[] = _.filter(
      this.factortable.standardAnalysisFactorVOS,
      function(item) {
        return item.type !== "PULL_DOWN_BOX";
      }
    );
    tmpAttr = tmpAttr.concat(data);
    this.factortable.standardAnalysisFactorVOS = tmpAttr;
    this.prismaData.analysisFactorDto = tmpAttr;
    this.isNzOkLoading = false;
    this.isNzRELoading = false;
    this.isNzPreLoading = false;
    this.isSpinning = false;
    this.shownumber++;
  }

  getProjectSetting() {
    this.toolsname = [];
    this.normaldatas = [];
    this.prismaApi.getProjectSetting(this.projectId).subscribe((item) => {
      if (item.result.code == 0) {
        sessionStorage.setItem(
          "projectLanguages",
          JSON.stringify(item.data.availableLanguages)
        );
        sessionStorage.setItem("language", item.data.language);
        item.data.questionnaires.forEach((val) => {
          this.toolsname.push({
            title: val.name,
            description: val.description,
            questionnaireId: val.id,
            reportType: val.reportType,
          });
        });
        this.projectingtdata = item.data;
        item.data.questionnaires.forEach((elemnt) => {
          if (elemnt.id == this.toolsname[this.showIndex].questionnaireId) {
            this.questionnaireId = elemnt.id;
            this.reportTemplateId = elemnt.reportTemplateId;
          }
          if (!this.reportTypes.includes(elemnt.reportType)) {
            this.reportTypes.push(elemnt.reportType);
          }
        });
        this.prismaData.answerMode = item.data.answerMode;
        this.projectType = item.data.status;
        this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO = [];
        item.data.questionnaires.forEach((val) => {
          this.prismaData.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO.push(
            {
              name: val.name.zh_CN,
              questionnaireId: item.data.standardQuestionnaireIds[0],
              standardQuestionnaireDimensionIds: [],
              styles: val.styles,
            }
          );
        });
        this.prismaData.name = item.data.projectName;
        this.prismaData.startTime = item.data.startTime;
        this.prismaData.endTime = item.data.endTime;
        this.dateRange = [item.data.startTime, item.data.endTime];
        this.prismaData.analysisFactorDto = item.data.analysisFactorDtos;
        // 高级设置属性
        this.prismaData.isShowKnxLogo = item.data.isShowKnxLogo;
        this.prismaData.isShowBackgroundPic = item.data.isShowBackgroundPic;
        this.prismaData.isCheckLicenseNotice = item.data.isCheckLicenseNotice;
          this.prismaData.isCheckedAnswerValid = item.data.isCheckedAnswerValid;
        this.prismaData.isEnableWelcomePage = item.data.isEnableWelcomePage;
        this.prismaData.isEnableEndPage = item.data.isEnableEndPage;
        this.prismaData.isShowPreAnswer = item.data.isShowPreAnswer;
        this.prismaData.isPublicReport = item.data.isPublicReport;
        this.prismaData.isEmailReport = item.data.isEmailReport;
        this.oIsEmailReport = item.data.isEmailReport;

        this.prismaData.welcomePage = item.data.welcomePage
          ? item.data.welcomePage
          : {};
        this.prismaData.endPage = item.data.endPage ? item.data.endPage : {};
        this.prismaData.isCheckAnswerInstructions =
          item.data.isCheckAnswerInstructions;
        this.prismaData.isShowDemographicQuestion =
          item.data.isShowDemographicQuestion;
        this.prismaData.language = item.data.language;
        this.prismaData.optionalLanguages = item.data.optionalLanguages;
        this.prismaData.availableLanguages = item.data.availableLanguages.length
          ? item.data.availableLanguages
          : ["zh_CN", "en_US"];
        this.prismaData.logoFile = item.data.logoFile
          ? item.data.logoFile.split("/")[3]
          : null;
        this.prismaData.pcBackgroundPic = item.data.pcBackgroundPic
          ? item.data.pcBackgroundPic.split("/")[3]
          : null;
        this.prismaData.mobileBackgroundPic = item.data.mobileBackgroundPic
          ? item.data.mobileBackgroundPic.split("/")[3]
          : null;
        this.prismaData.answerEffectiveRange =
          item.data.answerEffectiveRange || "95";
        this.prismaData.answerEffectiveTime = item.data.answerEffectiveTime;
        this.prismaData.questionNumInOnePage = item.data.questionNumInOnePage;
        this.prismaData.sequence = item.data.sequence;
        this.prismaData.isShow = item.data.isShow;
        // AT 考试模板属性
        this.prismaData.atMode = item.data.atMode;
        this.prismaData.atSetting = item.data.atSetting;
        this.originalAtMode = item.data.atMode;
        this.atMode = item.data.atMode || "FIX";
        this.atSetting = item.data.atSetting || null;
        // 题本分发 isEnableQuestionBookDistribute
        // 量表拓展 isEnableScaleExtend
        this.prismaData.isEnableQuestionBookDistribute =
          item.data.isEnableQuestionBookDistribute;
        this.prismaData.isEnableScaleExtend = item.data.isEnableScaleExtend;
        this.AllrelationPermissions = [];
        if (
          !this.standardQuestionnaireIds ||
          this.standardQuestionnaireIds.length == 0
        ) {
          let standardQuestionnaireIds = [];
          this.tabledata.forEach((res) => {
            standardQuestionnaireIds.push(res.id);
          });
          standardQuestionnaireIds.forEach((item) => {
            this.getSetInfo(this.projectId, item);
          });
        } else {
          this.standardQuestionnaireIds.forEach((item) => {
            this.getSetInfo(this.projectId, item.standardQuestionnaireId);
          });
        }
      }
    });
  } //活动创建后，获取保存的活动信息，如：活动名称、活动时间、高级设置等

  ngModelChange(e) {
    this.prismaData.analysisFactorDto = this.factortable.standardAnalysisFactorVOS;
  } //人口标签选中回调

  addfactor(status: boolean) {
    this.showAnalysisFactor = status;
    if (status === false) {
      this.analysisFactorTitle = "自定义";
    } else {
      this.analysisFactorTitle = "查看";
    }
    this.addfactorshow = true;
  } //自定义打开

  closemodal(type) {
    this.addfactorshow = type.addfactorshow;
    this.factortable.standardAnalysisFactorVOS =
      type.factorlist.standardAnalysisFactorVOS;
    this.prismaData.analysisFactorDto = this.factortable.standardAnalysisFactorVOS;
  } //人口标签保存设置

  getdefaultlist() {
    this.factorisSpinning = true;
    this.prismaApi
      .listByQuestionnaireCode(this.standardQuestionnaireId)
      .subscribe((item) => {
        if (item.result.code == 0) {
          this.factortable.standardAnalysisFactorVOS =
            item.data.standardAnalysisFactorVOS;
          // 如果AT默认人口标签存在
          if (this.atMode == "ADAPTIVE" && this.reportTypes.includes("AT")) {
            this.checkAtAdaptiveDefaultAnalysisFactors();
          }
          this.prismaData.analysisFactorDto = this.factortable.standardAnalysisFactorVOS;
          this.changeNumber += 1;
          this.factorisSpinning = false;
        } else {
          this.factorisSpinning = false;
        }
      });
      
  } //人口标签恢复默认

  // 清空选中的人口标签
  clearcheck() {
    this.factortable.standardAnalysisFactorVOS.forEach((res) => {
      if (res.isChecked && !res.isRequire) {
        res.isChecked = false;
      }
    });
    this.prismaData.analysisFactorDto = this.factortable.standardAnalysisFactorVOS;
  }

  //全选的人口标签
  checkAll() {
    this.factortable.standardAnalysisFactorVOS.forEach((res) => {
      if (!res.isChecked && !res.isRequire) {
        res.isChecked = true;
      }
    });
    this.prismaData.analysisFactorDto = this.factortable.standardAnalysisFactorVOS;
  }

  Modelchange(e, item, code) {
    let factorNum = this.prismaData.projectReportDimensions.length;
    let limitNum = this.factortable.surveyStandardSagReportTemplate
      .maxDimensionNum;
    if (factorNum + 1 > limitNum) {
      this.limitcode = code;
    }
    if (!e) {
      this.deletecode = code;
    } else {
      this.deletecode = null;
    }
  } //单个选中的维度code

  WrapperOnChange(e) {
    this.prismaData.projectReportDimensions = [];
    e.forEach((item) => {
      this.prismaData.projectReportDimensions.push({
        code: item,
      });
    });

    let factorNum = this.prismaData.projectReportDimensions.length;
    let limitNum = this.factortable.surveyStandardSagReportTemplate
      .maxDimensionNum;
    if (factorNum > limitNum) {
      this.factorshow = true;
      let deletindex;
      this.prismaData.projectReportDimensions.forEach((item, index) => {
        if (item.code == this.limitcode) {
          deletindex = index;
        }
      });
      this.prismaData.projectReportDimensions.splice(deletindex, 1);
    }
    this.getCheckedname(
      this.factortable,
      this.prismaData.projectReportDimensions
    );
  } //所有选中的维度code

  getCheckedname(data, projectReportDimensions) {
    data.detailedScoreConfigs.forEach((item) => {
      item.detailedScoreChildDimensions.forEach((res) => {
        projectReportDimensions.forEach((val) => {
          if (res.code == val.code) {
            val.name = res.name.zh_CN;
            val.parentName = item.name.zh_CN;
            val.isSelect = true;
            val.standardQuestionnaireId = data.standardQuestionnaireId; //standardQuestionnaireId
            val.standardReportTemplateId = this.factortable.surveyStandardSagReportTemplate.id;
          }
        });
      });
    });
    this.projectReportDimensionslist.push(...projectReportDimensions);

    this.projectReportDimensionslist = this.removaldata(
      this.projectReportDimensionslist
    );

    this.projectReportDimensionslist.forEach((item, index) => {
      if (item.code == this.deletecode) {
        this.projectReportDimensionslist.splice(index, 1);
      }
    });
    this.prismaData.projectReportDimensions = this.projectReportDimensionslist;
    this.showprojectReportlist = this.projectReportDimensionslist.filter(
      (item) => {
        return item.standardQuestionnaireId == this.standardQuestionnaireId;
      }
    );
    // 维度变更获取作答说明文本替换显示
    this.setDescDefault(false);
  } //projectReportDimensions填充内容

  removaldata(arr) {
    const result = [];
    const obj = {};
    for (let i = 0; i < arr.length; i++) {
      if (!obj[arr[i].code]) {
        result.push(arr[i]);
        obj[arr[i].code] = true;
      }
    }
    return result;
  } // 去重

  factorOk() {
    this.factortable.detailedScoreConfigs.forEach((res) => {
      if (res.isSelect) {
        res.detailedScoreChildDimensions.forEach((val) => {
          if (val.code == this.limitcode) {
            val.isSelected = false;
          }
        });
      }
    });
    this.factorshow = false;
  } //根据限制条件取消多余选择

  handleCancel() {
    this.addfactorshow = false;
    this.factortable.standardAnalysisFactorVOS = this.factortable.standardAnalysisFactorVOS.filter(
      (item) => {
        return item.standardDemographicId || item.saveisAdd || item.id;
      }
    );
  } //人口标签弹窗取消

  getshow(i, questionnaireId) {
    this.normaldatas.forEach((item, index) => {
      if (item.questionnaireId) {
        if (item.questionnaireId == questionnaireId) {
          this.factortable = this.normaldatas[index];
          this.factortable.standardAnalysisFactorVOS = this.normaldatas[0].standardAnalysisFactorVOS;
          this.standardQuestionnaireId = this.factortable.standardQuestionnaireId;
        }
      } else {
        if (item.surveyStandardQuestionnaire.id == questionnaireId) {
          this.factortable = this.normaldatas[index];
          this.factortable.standardAnalysisFactorVOS = this.normaldatas[0].standardAnalysisFactorVOS;
          this.standardQuestionnaireId = this.factortable.standardQuestionnaireId;
        }
      }
    });

    this.showIndex = i;
    if (this.projectId) {
      if (this.projectingtdata) {
        this.projectingtdata.questionnaires.forEach((elemnt) => {
          if (elemnt.standardQuestionnaireId == this.standardQuestionnaireId) {
            this.questionnaireId = elemnt.id;
            this.reportTemplateId = elemnt.reportTemplateId;
          }
        });
      } else {
        this.prismaApi.getProjectSetting(this.projectId).subscribe((item) => {
          item.data.questionnaires.forEach((elemnt) => {
            if (
              elemnt.standardQuestionnaireId == this.standardQuestionnaireId
            ) {
              this.questionnaireId = elemnt.id;
              this.reportTemplateId = elemnt.reportTemplateId;
            }
          });
        });
      }
    }
    this.prismaData.relationPermissions = this.factortable.relationPermissions;
    this.prismaData.answerDescription = this.factortable.answerDescription
      ? this.factortable.answerDescription
      : this.factortable.surveyStandardQuestionnaire.answerDescription;
    this.modaltip = this.factortable.relationPermissions.filter((res) => {
      return res.type == "MODEL";
    });
    this.TipJobtip = this.factortable.relationPermissions.filter((res) => {
      return res.type == "JOB";
    });
    this.descTip = this.factortable.relationPermissions.filter((res) => {
      return res.type == "ANSWER_DESCRIPTION";
    });
    // 自定义-题本
    this.question_book = this.factortable.relationPermissions.filter((res) => {
      return res.type == "QUESTION_BOOK";
    });
    this.factortable.detailedScoreConfigs.forEach((res) => {
      res.showfactor = true;
    });
    if (this.factortable.dimensionLevelEnumsZH.length != 0) {
      this.chooseName = this.factortable.dimensionLevelEnumsZH[0].code;
    } else {
      this.chooseName = "NONE";
    }
    this.showprojectReportlist = this.projectReportDimensionslist.filter(
      (item) => {
        return item.standardQuestionnaireId == this.standardQuestionnaireId;
      }
    );

    if (this.factortable.surveyStandardQuestionnaire.reportType == "CA") {
      this.getCajobs();
    }

    // 选择对应模型
    // 如果AT默认人口标签存在
    if (this.reportTypes.includes("AT")) {
      if (this.atMode == "ADAPTIVE") {
        this.checkAtAdaptiveDefaultAnalysisFactors();
      }
      this.getDescDefault();
      this.atTip = this.factortable.relationPermissions.filter((res) => {
        return res.type == "AT_QUESTIONNAIRE_MODE";
      });
    } else {
      this.atTip = [];
    }
  }
  getchoose(i, name) {
    this.chooseIndex = i;
    this.chooseName = name;
  } //选择层级

  disabledDate = (current: Date): boolean => {
    return differenceInCalendarDays(current, this.today) < 0;
  }; //日期--今日之前不可选择

  showRoleModal() {
    const {
      startTime,
      endTime,
      name: { zh_CN },
    } = this.prismaData;
    if (!zh_CN) {
      // this.msg.warning("请填写活动名称!");
      this.customMsg.open("warning", "请填写活动名称");
      return;
    }
    if (!endTime || !startTime) {
      // this.msg.warning("请填写活动周期!");
      this.customMsg.open("warning", "请填写活动周期");
      return;
    }
    // 模型类型-at用过
    const reportTypeList = this.toolsname.map((val) => val.reportType);
    let disabledPagesAndSort = false;
    // 单个AT整卷开启下一题不可以，高级设置那边写置灰
    if (reportTypeList.length == 1 && reportTypeList[0] == "AT") {
      if (this.atMode == "ADAPTIVE") {
        disabledPagesAndSort = true;
      } else {
        disabledPagesAndSort = this.atSetting
          ? this.atSetting.isCannotAnswerBack
          : false;
      }
    }
    const modal = this.drawerService.create({
      nzContent: AdvancedMoreSetting,
      nzTitle: "高级设置",
      nzWidth: 500,
      nzContentParams: {
        listdata: this.prismaData,
        projecttype: this.tooltype,
        projectStatus: this.projectType,
        reportType: this.standardReportType,
        reportTypeList,
        oIsEmailReport: this.oIsEmailReport,
        disabledPagesAndSort,
      },
      nzWrapClassName: "round-right-drawer3",
    });
    // this.modalService.afterAllClose.subscribe((isSave) => {
    //   const child: AdvancedMoreSetting = modal.getContentComponent();
    //   this.prismaData = child.settingData;
    // });
    modal.afterClose.subscribe((settingData) => {
      // 高级设置保存调用
      if (settingData) {
        this.prismaData = settingData;
        // this.submitSave();
        let submitData = this.synthesisTime();
        sessionStorage.setItem("savefactors", null);
        if (!this.projectId) {
          if (submitData) {
            this.createproject(submitData, "SeniorPage", "");
          }
        } else {
          this.isSpinning = true;
          this.isNzRELoading = true;
          this.isNzPreLoading = true;
          this.isNzOkLoading = true;
          if (submitData) {
            this.ActiveUpdataship(submitData, "SeniorPage", "");
          } else {
            this.isSpinning = false;
            this.isNzRELoading = false;
            this.isNzPreLoading = false;
            this.isNzOkLoading = false;
          }
        }
      }
    });
  } //高级设置弹窗

  getModalPie() {
    if (this.echartData) {
      this.echartData = [];
      this.factortable.detailedScoreConfigs.forEach((res) => {
        if (res.isSelect) {
          res.detailedScoreChildDimensions.forEach((val) => {
            if (val.isSelected) {
              this.echartData.push({
                name: val.name.zh_CN,
                value: Math.ceil(Math.random() * 10),
                parentName: res.name.zh_CN,
              });
            }
          });
        }
        if (!res.isSelect) {
          res.detailedScoreChildDimensions.forEach((val) => {
            this.echartData.push({
              name: val.name.zh_CN,
              value: Math.ceil(Math.random() * 10),
              parentName: res.name ? res.name.zh_CN : "",
            });
          });
        }
      });
      this.echartData = this.classification(this.echartData);
    }
    let baseUrl: string = window.location.origin + "/";
    if (baseUrl.indexOf("http://localhost") !== -1) {
      baseUrl = "https://sag-qa.knxdevelop.com/";
      // baseUrl = "http://***********/";;
    }

    this.modelImageUrl = `${baseUrl}api/file/www/${this.factortable.surveyStandardSagReportTemplate.modelImageUrl}`;
    this.isVisiblemodal = true;
  } //模型查看

  modalCancel() {
    this.isVisiblemodal = false;
  } //模型弹窗取消

  async showModalDesc() {
    let submitData = this.synthesisTime();
    if (submitData) {
      if (
        this.atTip.length > 0 &&
        this.factortable.surveyStandardQuestionnaire.reportType === "AT"
      ) {
        // if (this.hasQuestionnaireTipItems) {
        //   if (
        //     this.isCheackDetailedScoreConfigs(
        //       this.checkTipMenuItem.detailedScoreConfigs
        //     ) &&
        //     this.tipCurrentDimensions.length == 0
        //   ) {
        //     this.msg.warning("至少选择一个模型维度！");
        //     return;
        //   }
        // } else {
        //   if (
        //     this.isCheackDetailedScoreConfigs(
        //       this.factortable.detailedScoreConfigs
        //     ) &&
        //     this.projectReportDimensionslist.length == 0
        //   ) {
        //     this.msg.warning("至少选择一个模型维度！");
        //     return;
        //   }
        // }
        // AT试卷至少选中一个模型维度（关键指标）
        if (!this.hasQuestionnaireTipItems) {
          if (
            this.isCheackDetailedScoreConfigs(
              this.factortable.detailedScoreConfigs
            ) &&
            this.projectReportDimensionslist.length == 0
          ) {
            // this.msg.warning("至少选择一个模型维度！");
            this.customMsg.open("warning", "至少选择一个模型维度");
            return;
          }
        }
        if (!submitData.atMode) {
          // this.msg.warning("请先设置考试问卷！");
          this.customMsg.open("warning", "请先设置考试问卷");
          return;
        }
        // 根据维度与试卷配置组装作答说明
        if (this.atMode == "ADAPTIVE" && !this.hasQuestionnaireTipItems) {
          this.prismaData.answerDescription.zh_CN = this.packageDesc(
            submitData.atSetting
          );
          // innerHTML渲染问题 需用this.sanitizer.bypassSecurityTrustHtml
          this.atAdaptiveDesc = this.sanitizer.bypassSecurityTrustHtml(
            this.prismaData.answerDescription.zh_CN
          );
        }
      }
      // tip-作答说明
      if (this.hasQuestionnaireTipItems) {
        if (
          this.isCheackDetailedScoreConfigs(
            this.checkTipMenuItem.detailedScoreConfigs
          ) &&
          this.tipCurrentDimensions.length == 0
        ) {
          // this.msg.warning("至少选择一个模型维度！");
          this.customMsg.open("warning", "至少选择一个模型维度");
          return;
        }
        // 当前问卷
        const current = this.factortable.questionnaireTipItems.filter(
          (val) => val.reportType == this.checkReportType
        );
        // todo: tip时,维度变更获取作答说明文本替换显示
        const dimensionCodes = this.tipCurrentDimensions.map((val) => val.code);
        const isSelect = this.isCheackDetailedScoreConfigs(
          this.checkTipMenuItem.detailedScoreConfigs
        );
        const paramsrDesc = {
          projectId: this.projectId,
          standardQuestionnaireId: current[0].standardQuestionnaireId,
          questionnaireId: this.questionnaireId,
          reportType: this.checkReportType,
          // dimensionCodes,
          recoverFlag: false,
        };
        if (isSelect) {
          paramsrDesc["dimensionCodes"] = dimensionCodes;
        }
        const resDesc = await this.http
          .getQuestionnaireAnswerDescription(paramsrDesc)
          .toPromise();
        this.tipCurrentAnswerDescription = resDesc.data || null;
        // 自适应问卷-根据维度与试卷配置组装作答说明（暂未使用）
        if (this.atMode == "ADAPTIVE" && this.checkReportType === "AT") {
          this.tipCurrentAnswerDescription.zh_CN = this.packageDesc(
            submitData.atSetting
          );
        }
        // const dimensionCodes = this.getDimensionCodes();
        const params = {
          description: this.tipCurrentAnswerDescription,
          reportType: this.checkReportType,
          tinyconfig: this.tinyconfig,
          isAtADAPTIVE:
            this.atMode == "ADAPTIVE" && this.checkReportType === "AT",
          standardQuestionnaireId: current[0].standardQuestionnaireId,
          projectId: this.projectId,
          questionnaireId: this.questionnaireId,
        };
        if (isSelect) {
          params["dimensionCodes"] = dimensionCodes;
        }
        const modal = this.drawerService.create({
          nzContent: TipDescComponent,
          nzTitle: "作答说明",
          nzWidth: 500,
          nzContentParams: params,
          nzWrapClassName: "round-right-drawer3",
        });
        modal.afterClose.subscribe((result) => {
          if (result) {
            console.log(result);
            this.tipCurrentAnswerDescription = result.desc;
            this.okModalDesc();
            // 更新作答
            this.factortable.questionnaireTipItems.forEach((val) => {
              if (val.reportType == result.reportType) {
                val.answerDescription = result.desc;
              }
            });
          }
        });
      } else {
        // todo: 非tip时,维度变更获取作答说明文本替换显示
        this.setDescDefault(false);
        this.visibleDesc = true;
      }
      window.document.documentElement.scrollTop = 0;
    }
  } //作答说明

  getActivenew(e) {
    if (e == "zh_CN") {
      this.Isactivelan = true;
    } else {
      this.Isactivelan = false;
    }
  } //选择中英文

  okModalDesc() {
    this.nzSimple = true;
    let submitData = this.synthesisTime();
    if (!this.projectId) {
      if (submitData) {
        this.createproject(submitData, "AnswerPage", "");
      } else {
        this.nzSimple = false;
      }
    } else {
      this.isSpinning = true;
      this.isNzRELoading = true;
      this.isNzOkLoading = true;
      this.isNzPreLoading = true;
      if (submitData) {
        if (this.isUpdateing) {
          this.ActiveUpdataship(submitData, "AnswerPage", "");
        } else {
          this.UpdateAnswer(true);
        }
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
        this.lan = "zh_CN";
      }
    }
  } //确认

  cancelModalDesc() {
    this.visibleDesc = false;
  } //关闭作答说明弹窗

  setDescDefault(recoverFlag: boolean) {
    // this.http.getDimensions(this.standardQuestionnaireId).subscribe((res) => {
    //   let tmpDesc = res.data.answerDescription;
    //   this.prismaData.answerDescription.zh_CN = tmpDesc.zh_CN;
    //   this.prismaData.answerDescription.en_US = tmpDesc.en_US;
    // });
    const params = {
      projectId: this.projectId,
      standardQuestionnaireId: this.standardQuestionnaireId,
      reportType: this.factortable.surveyStandardQuestionnaire.reportType,
      questionnaireId: this.questionnaireId,
      recoverFlag,
    };
    const { codes: dimensionCodes, isSelect } = this.getDimensionCodes();
    if (isSelect) {
      params["dimensionCodes"] = dimensionCodes;
    }
    this.http.getQuestionnaireAnswerDescription(params).subscribe((res) => {
      if (res.result.code === 0) {
        let tmpDesc = res.data;
        this.prismaData.answerDescription.zh_CN = tmpDesc.zh_CN;
        this.prismaData.answerDescription.en_US = tmpDesc.en_US;
        console.log("retmpDescs", tmpDesc);
      }
    });
  } //恢复默认

  /**
   * 获取二级维度code
   */
  getDimensionCodes() {
    const codes = [];
    let isSelect = false;
    this.factortable.detailedScoreConfigs.forEach((res) => {
      isSelect = res.isSelect;
      if (res.isSelect) {
        res.detailedScoreChildDimensions.forEach((val) => {
          if (val.isSelected) {
            codes.push(val.code);
          }
        });
      }
    });
    return { codes, isSelect };
  }
  getDescDefault() {
    this.http.getDimensions(this.standardQuestionnaireId).subscribe((res) => {
      this.atTmpDesc = res.data.answerDescription;
      // console.log('默认自定义作答说明，at用',  res.data.answerDescription)
    });
  } //默认自定义作答说明，at用

  synthesisTime() {
    if (!this.prismaData.name.zh_CN) {
      // this.msg.error("请填写活动名称");
      this.customMsg.open("error", "请填写活动名称");
      this.isSpinning = false;
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.nzSimple = false;
      return;
    }

    if (this.prismaData.name.zh_CN.length > 50) {
      // this.msg.error("活动名称不能超过50个字符！");
      this.customMsg.open("error", "活动名称不能超过50个字符！");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      return;
    }

    if (
      this.prismaData.name.zh_CN.indexOf("/") != -1 ||
      this.prismaData.name.zh_CN.indexOf("\\") != -1
    ) {
      // this.msg.error("活动名称包含非法字符'/''\\'！");
      this.customMsg.open("error", "活动名称包含非法字符'/''\\'！");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      return;
    }

    if (!this.dateRange) {
      // this.msg.error("请选择活动周期");
      this.customMsg.open("error", "请选择活动周期");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      return;
    }
    if (this.dateRange.length === 0) {
      // this.msg.error("请选择活动周期");
      this.customMsg.open("error", "请选择活动周期");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      return;
    }
    if (this.prismaData.optionalLanguages.length == 0) {
      // this.msg.error("填答时可选语言至少选一个！");
      this.customMsg.open("error", "填答时可选语言至少选一个！");
      this.isNzOkLoading = false;
      this.isNzRELoading = false;
      this.isNzPreLoading = false;
      this.isSpinning = false;
      return;
    }
    if (this.prismaData.isEnableEndPage) {
      if (this.prismaData.endPage.zh_CN == "") {
        // this.msg.error("结束页内容不能为空！");
        this.customMsg.open("error", "结束页内容不能为空！");
        this.isNzOkLoading = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isSpinning = false;
        return;
      }
    }
    if (this.prismaData.isEnableWelcomePage) {
      if (this.prismaData.welcomePage.zh_CN == "") {
        // this.msg.error("欢迎页内容不能为空！");
        this.customMsg.open("error", "欢迎页内容不能为空！");
        this.isNzOkLoading = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isSpinning = false;
        return;
      }
    }
    this.prismaData.startTime = `${this.formatDate(
      this.dateRange[0],
      "date"
    )}T${this.formatDate(this.dateRange[0], "time")}`;
    this.prismaData.endTime = `${this.formatDate(
      this.dateRange[1],
      "date"
    )}T${this.formatDate(this.dateRange[1], "time")}`;

    const submitData = JSON.parse(JSON.stringify(this.prismaData));
    submitData.projectName = submitData.name;

    if (this.comeReportType == "TIP_NEW_2") {
      submitData.reportTypes = this.reportTypes;
    }
    return submitData;
  } //数据校验

  createproject(data, type, url) {
    if (this.hasQuestionnaireTipItems) {
      data.standardQuestionnaireDTO.standardQuestionnaireDimensionDTO[0].questionnaireTipItems = this.questionnaireTipItems; // 新Tip所有的维度
      // 父维度处理
      data.projectReportParentDimensions = this.formatNewTipProjectReportParentDimensions(); // 选中的父维度
      // 子指标处理
      data.projectReportDimensions = this.formatNewTipProjectReportDimensions(); // 选中的子维度
    }
    this.isSpinning = true;
    this.isNzRELoading = true;
    this.isNzOkLoading = true;
    this.ismodalclosed = true;
    this.isNzPreLoading = true;

    this.prismaApi.create(data).subscribe((res) => {
      if (res.result.code == 0) {
        const types = [
          "AnswerPage",
          "JobPage",
          "FactorPage",
          "SeniorPage",
          "AtSetting",
          "BookPage",
          "Scale",
          "QuestionBook",
        ];
        if (types.includes(type)) {
          //作答说明，岗位定制
          this.projectId = res.data[0].projectId;
          this.questionnaireId = res.data[0].id;
          this.reportTemplateId = res.data[0].reportTemplateId;
          this.projectType = "ANNOUNCED"; // 创建默认项目待发布，防止多模型阻碍后续
          res.data.forEach((elemnt) => {
            if (
              elemnt.standardQuestionnaireId == this.standardQuestionnaireId
            ) {
              this.questionnaireId = elemnt.id;
              this.reportTemplateId = elemnt.reportTemplateId;
            }
          });
          this.ismodalclosed = false;
          if (type == "AnswerPage") {
            this.UpdateAnswer(true);
          }
          if (type == "JobPage") {
            this.getcustomlist();
          }
          if (
            type == "FactorPage" ||
            type == "SeniorPage" ||
            type == "AtSetting"
          ) {
            // 确认AT试卷关联任务
            if (type == "AtSetting") {
              // 新Tip-AT关联任务
              if (this.hasQuestionnaireTipItems) {
                this.confirmRelation(
                  "AT_QUESTIONNAIRE_MODE",
                  true,
                  false,
                  "AT"
                );
              } else {
                this.confirmRelation("AT_QUESTIONNAIRE_MODE", true);
              }
              // 如果自定义作答说明的关联任务确认过，更新作答说明
              this.atDescDispose(data);
            }

            this.nzSimple = false;
            this.isSpinning = false;
            this.isNzRELoading = false;
            this.isNzOkLoading = false;
            this.isNzPreLoading = false;
            this.factornameshow = false;
            this.getProjectSetting();
          }
          if (type == "BookPage") {
            this.BookUpdate(url);
          }
          if (type == "Scale") {
            this.scaleExpansion.openModal();
          }

          if (type == "QuestionBook") {
            this.topicDistribution.openModal();
          }
        } else {
          //保存活动，回到活动列表页面
          this.msg.success("创建成功！");
          this.router.navigateByUrl(url);
        }
        sessionStorage.removeItem("standardQuestionnaireIds");
      } else {
        this.ismodalclosed = false;
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
        this.nzSimple = false;
        this.factornameshow = false;
      }
    });
  } //创建活动

  ActiveUpdataship(submitData, type, url) {
    // 新tip-数据处理
    if (this.hasQuestionnaireTipItems) {
      submitData.projectReportParentDimensions = this.formatNewTipProjectReportParentDimensions(); // 选中的父维度
      // 子指标处理
      submitData.projectReportDimensions = this.formatNewTipProjectReportDimensions(); // 选中的子维度
      // ca与Tip的岗位相关处理
    }
    if (this.isUpdateing) {
      //已发布更新
      submitData.projectId = this.projectId;
      this.prismaApi.updateProjectSetting(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          const types = [
            "AnswerPage",
            "JobPage",
            "FactorPage",
            "SeniorPage",
            "AtSetting",
            "BookPage",
            "Scale",
            "QuestionBook",
          ];
          if (types.includes(type)) {
            if (type == "BookPage") {
              this.BookUpdate(url);
            }
            if (type == "AnswerPage") {
              this.UpdateAnswer(true);
            }
            if (type == "JobPage") {
              this.getcustomlist();
            }
            const dialogTypes = [
              "FactorPage",
              "SeniorPage",
              "AtSetting",
              "Scale",
              "QuestionBook",
            ];
            if (dialogTypes.includes(type)) {
              // 确认AT试卷关联任务
              if (type == "AtSetting") {
                // 新Tip-AT关联任务
                if (this.hasQuestionnaireTipItems) {
                  this.confirmRelation(
                    "AT_QUESTIONNAIRE_MODE",
                    true,
                    false,
                    "AT"
                  );
                } else {
                  this.confirmRelation("AT_QUESTIONNAIRE_MODE", true);
                }
                // 如果自定义作答说明的关联任务确认过，更新作答说明
                // if (this.descTip.length != 0 && this.descTip[0].isConfirmed) {
                //   this.UpdateAnswer(false);
                // }
                this.atDescDispose(submitData);
              }
              if (type == "Scale") {
                this.scaleExpansion.openModal();
              }
              if (type == "QuestionBook") {
                this.topicDistribution.openModal();
              }
              this.nzSimple = false;
              this.isSpinning = false;
              this.isNzRELoading = false;
              this.isNzOkLoading = false;
              this.isNzPreLoading = false;
              this.factornameshow = false;
              this.getProjectSetting();
            }
          } else {
            //更新活动，回到活动列表页面
            this.msg.success("保存成功！");
            this.router.navigateByUrl(url);
          }
        } else {
          this.nzSimple = false;
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPreLoading = false;
          this.factornameshow = false;
        }
      });
    } else {
      //未发布更新
      submitData.id = this.projectId;
      this.prismaApi.updateAnnouncedProject(submitData).subscribe((res) => {
        if (res.result.code == 0) {
          const types = [
            "FactorPage",
            "SeniorPage",
            "AtSetting",
            "BookPage",
            "Scale",
            "QuestionBook",
          ];
          if (types.includes(type)) {
            if (type == "BookPage") {
              this.BookUpdate(url);
            }
            // 确认AT试卷关联任务
            if (type == "AtSetting") {
              this.confirmRelation("AT_QUESTIONNAIRE_MODE", true);
              // 如果自定义作答说明的关联任务确认过，更新作答说明
              // if (this.descTip.length != 0 && this.descTip[0].isConfirmed) {
              //   this.UpdateAnswer(false);
              // }
              this.atDescDispose(submitData);
            }
            if (type == "Scale") {
              this.scaleExpansion.openModal();
            }
            if (type == "QuestionBook") {
              this.topicDistribution.openModal();
            }
            this.nzSimple = false;
            this.isSpinning = false;
            this.isNzRELoading = false;
            this.isNzOkLoading = false;
            this.isNzPreLoading = false;
            this.factornameshow = false;
            this.getProjectSetting();
          } else {
            //更新活动，回到活动列表页面
            this.msg.success("保存成功！");
            this.router.navigateByUrl(url);
          }
        } else {
          this.nzSimple = false;
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzPreLoading = false;
          this.isNzOkLoading = false;
          this.factornameshow = false;
        }
      });
    }
  } //作答说明

  getcustomlist() {
    this.radioCodelist = this.factortable.dimensionLevelEnumsZHnew.filter(
      (item) => {
        return this.radioCode == item.code;
      }
    );
    this.radiolist = this.Tipjobslist.filter((res) => {
      return this.radioValue == res.id;
    });
    this.Customized(this.radiolist);
  }
  classification(arr) {
    var map = {},
      dest = [];
    for (var i = 0; i < arr.length; i++) {
      var ai = arr[i];
      if (!map[ai.parentName]) {
        //key 依赖字段 可自行更改
        dest.push({
          name: ai.parentName,
          data: [ai],
        });
        map[ai.parentName] = ai;
      } else {
        for (var j = 0; j < dest.length; j++) {
          var dj = dest[j];
          if (dj.name == ai.parentName) {
            //key 依赖字段 可自行更改
            dj.data.push(ai);
            break;
          }
        }
      }
    }
    return dest;
  } //数据归类
  UpdateAnswer(msg = true, isSpinningShow = true) {
    let params = {
      answerDescription: {
        en_US: this.prismaData.answerDescription.en_US,
        zh_CN: this.prismaData.answerDescription.zh_CN,
      },
      questionnaireId: this.questionnaireId,
    };
    // 新Tip-处理子维度作答说明
    if (this.hasQuestionnaireTipItems && this.checkReportType === "AT") {
      params["tipItemReportType"] = this.checkReportType;
      if (this.atMode == "ADAPTIVE") {
        params.answerDescription.zh_CN = this.packageDesc(
          this.prismaData.atSetting
        );
      } else {
        const current = this.factortable.questionnaireTipItems.filter(
          (val) => val.reportType == this.checkReportType
        );
        // params.answerDescription = current[0].answerDescription;
        params.answerDescription = this.tipCurrentAnswerDescription;
      }
    } else if (this.hasQuestionnaireTipItems && this.checkReportType !== "AT") {
      params["tipItemReportType"] = this.checkReportType;
      const current = this.factortable.questionnaireTipItems.filter(
        (val) => val.reportType == this.checkReportType
      );
      params.answerDescription = this.tipCurrentAnswerDescription;
    }
    // 普通测评
    if (
      !this.hasQuestionnaireTipItems &&
      this.toolsname[this.showIndex].reportType === "AT" &&
      this.atMode == "ADAPTIVE"
    ) {
      params.answerDescription.zh_CN = this.packageDesc(
        this.prismaData.atSetting
      );
    }
    // console.log(params, this.toolsname[this.showIndex])
    this.http.updateAnswer(params).subscribe((res) => {
      if (res.result.code == 0) {
        if (isSpinningShow) {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPreLoading = false;
          this.lan = "zh_CN";
        }
        if (this.hasQuestionnaireTipItems) {
          this.confirmRelation(
            "ANSWER_DESCRIPTION",
            true,
            msg,
            this.checkReportType
          );
        } else {
          this.confirmRelation("ANSWER_DESCRIPTION", true, msg);
        }
      } else {
        if (isSpinningShow) {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPreLoading = false;
          this.lan = "zh_CN";
        }
        this.nzSimple = false;
      }
    });
  } //更新作答说明
  confirmRelation(type, isConfirmed, msg = true, reportType?) {
    let data = {
      projectId: this.projectId,
      questionnaireId: this.questionnaireId,
      type: type,
      isConfirmed: isConfirmed,
    };
    // 新tip-确认关联任务需要增加reportType
    if (this.hasQuestionnaireTipItems && (this.checkReportType || reportType)) {
      // 外层Tip任务不需要reportType,内部子维度任务需要reportType
      data["reportType"] = reportType ? reportType : this.checkReportType;
    }
    this.prismaApi.confirmRelation(data).subscribe((item) => {
      if (item.result.code == 0) {
        if (msg) this.msg.success("保存成功！");
        if (type == "ANSWER_DESCRIPTION") {
          this.descTip[0].isConfirmed = true;
        }
        if (type == "JOB") {
          if (this.TipJobtip.length) {
            this.TipJobtip[0].isConfirmed = isConfirmed;
          }
          if (!isConfirmed) {
            // 如果是清空岗位任务-清楚活动关联的岗位
            this.clearCustomized();
          }
        }
        if (type == "AT_QUESTIONNAIRE_MODE") {
          this.atTip[0].isConfirmed = isConfirmed;
        }
        this.visibleDesc = false;
        this.nzSimple = false;
        if (this.hasQuestionnaireTipItems) {
          this.checkTipIsJob(); // 新Tip-子维度禁用控制
        }
        // this.getProjectSetting();
      }
    });
  } //确认关联任务

  removalselected(arr) {
    let result = [];
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      if (!obj[arr[i]]) {
        result.push(arr[i]);
        obj[arr[i]] = true;
      }
    }
    return result;
  }

  submitSave() {
    let submitData = this.synthesisTime();
    sessionStorage.setItem("savefactors", null);
    this.isSpinning = true;
    this.isNzRELoading = true;
    this.isNzOkLoading = true;
    this.isNzPreLoading = true;
    if (!this.projectId) {
      if (submitData) {
        this.createproject(submitData, "SavePage", "/project-manage/home");
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
      }
    } else {
      if (submitData) {
        this.ActiveUpdataship(submitData, "SavePage", "/project-manage/home");
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
      }
    }
  } //保存活动

  submitPreviewSave() {
    const submitData = this.synthesisTime();
    if (!submitData) return;
    // 新tip-数据处理
    if (this.hasQuestionnaireTipItems) {
      submitData.projectReportParentDimensions = this.formatNewTipProjectReportParentDimensions(); // 选中的父维度
      // 子指标处理
      submitData.projectReportDimensions = this.formatNewTipProjectReportDimensions(); // 选中的子维度
      // ca与Tip的岗位相关处理
    }
    this.AllrelationPermissions = this.removalrelation(
      this.AllrelationPermissions
    );

    let nocommitrelation = this.AllrelationPermissions.filter((item) => {
      return !item.isConfirmed;
    });
    if (nocommitrelation.length != 0) {
      // this.msg.error("请完成作答说明任务!");
      this.customMsg.open("error", "请完成作答说明任务!");
      return;
    }

    sessionStorage.setItem("savefactors", null);
    // todo:
    if (
      (this.standardReportType == "TIP_NEW_2" ||
        this.standardReportType == "TIP_NEW") &&
      submitData.projectReportParentDimensions.length == 0
    ) {
      // this.msg.error("请至少选择一个大维度");
      this.customMsg.open("error", "请至少选择一个大维度");
      return;
    }
    if (this.hasQuestionnaireTipItems) {
      // 新Tip-提交-岗位关联任务
      if (this.checkReportTypes.includes("CA")) {
        if (this.verifyTask("JOB", "CA") == false) {
          // this.msg.error("请先完成关联任务--对标岗位/层级");
          this.customMsg.open("error", "请先完成关联任务--对标岗位/层级");
          return;
        }
      } else {
        if (this.verifyTask("JOB") == false) {
          // this.msg.error("请先完成关联任务--对标岗位/层级");
          this.customMsg.open("error", "请先完成关联任务--对标岗位/层级");
          return;
        }
      }
      // 新Tip-提交-作答说明关联任务
      if (this.verifyTask("ANSWER_DESCRIPTION") == false) {
        // this.msg.error("请先完成关联任务--作答说明");
        this.customMsg.open("error", "请先完成关联任务--作答说明");
        return;
      }
    } else {
      const tipjob = submitData.relationPermissions.filter((res) => {
        return res.type == "JOB";
      });

      const answer = submitData.relationPermissions.filter((res) => {
        return res.type == "ANSWER_DESCRIPTION";
      });
      if (tipjob.length != 0) {
        if (!tipjob[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--对标岗位/层级");
          this.customMsg.open("error", "请先完成关联任务--对标岗位/层级");
          return;
        }
      }

      if (answer.length != 0) {
        if (!answer[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--作答说明");
          this.customMsg.open("error", "请先完成关联任务--作答说明");
          return;
        }
      }
    }
    this.isNzPreLoading = true;
    if (!this.projectId) {
      if (submitData) {
        submitData.status = "PREVIEW"; // (进行中) ----发布
        this.createproject(submitData, "SavePage", "/project-manage/home");
      }
    } else {
      this.isSpinning = true;
      this.isNzRELoading = true;
      this.isNzOkLoading = true;
      this.isNzPreLoading = true;
      if (submitData) {
        submitData.status = "PREVIEW";
        this.ActiveUpdataship(submitData, "SavePage", "/project-manage/home");
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
      }
    }
  } //保存活动

  getpreview() {
    if (this.projectId) {
      const submitData = this.synthesisTime();
      if (submitData) {
        this.preview(submitData);
      }
    } else {
      // this.msg.warning("请先保存活动！");
      this.customMsg.open("warning", "请先保存活动");
    }
  } //预览活动

  submitRelase() {
    const submitData = this.synthesisTime();
    if (!submitData) return;
    // 新tip-数据处理
    if (this.hasQuestionnaireTipItems) {
      submitData.projectReportParentDimensions = this.formatNewTipProjectReportParentDimensions(); // 选中的父维度
      // 子指标处理
      submitData.projectReportDimensions = this.formatNewTipProjectReportDimensions(); // 选中的子维度
      // ca与Tip的岗位相关处理
    }
    this.AllrelationPermissions = this.removalrelation(
      this.AllrelationPermissions
    );

    let nocommitrelation = this.AllrelationPermissions.filter((item) => {
      return !item.isConfirmed;
    });
    if (nocommitrelation.length != 0) {
      // this.msg.error("请完成作答说明任务!");
      this.customMsg.open("error", "请完成作答说明任务!");
      return;
    }

    sessionStorage.setItem("savefactors", null);
    if (
      (this.standardReportType == "TIP_NEW_2" ||
        this.standardReportType == "TIP_NEW") &&
      submitData.projectReportParentDimensions.length == 0
    ) {
      // this.msg.error("请至少选择一个大维度");
      this.customMsg.open("error", "请至少选择一个大维度");
      return;
    }
    if (this.hasQuestionnaireTipItems) {
      // 新Tip-提交-岗位关联任务
      if (this.checkReportTypes.includes("CA")) {
        if (this.verifyTask("JOB", "CA") == false) {
          // this.msg.error("请先完成关联任务--对标岗位/层级");
          this.customMsg.open("error", "请先完成关联任务--对标岗位/层级");
          return;
        }
      } else {
        if (this.verifyTask("JOB") == false) {
          // this.msg.error("请先完成关联任务--对标岗位/层级");
          this.customMsg.open("error", "请先完成关联任务--对标岗位/层级");
          return;
        }
      }
      // 新Tip-提交-作答说明关联任务
      if (this.verifyTask("ANSWER_DESCRIPTION") == false) {
        // this.msg.error("请先完成关联任务--作答说明");
        this.customMsg.open("error", "请先完成关联任务--作答说明");
        return;
      }
    } else {
      const tipjob = submitData.relationPermissions.filter((res) => {
        return res.type == "JOB";
      });

      const answer = submitData.relationPermissions.filter((res) => {
        return res.type == "ANSWER_DESCRIPTION";
      });

      if (tipjob.length != 0) {
        if (!tipjob[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--对标岗位");
          this.customMsg.open("error", "请先完成关联任务--对标岗位");
          return;
        }
      }

      if (answer.length != 0) {
        if (!answer[0].isConfirmed) {
          // this.msg.error("请先完成关联任务--作答说明");
          this.customMsg.open("error", "请先完成关联任务--作答说明");
          return;
        }
      }
    }

    if (!this.projectId) {
      this.isNzRELoading = true;
      if (submitData) {
        submitData.status = "ANSWERING"; // (进行中) ----发布
        this.createproject(submitData, "RelasePage", "/project-manage/home");
      }
    } else {
      if (submitData) {
        if (this.projectType == "PREVIEW") {
          const that = this;
          this.confirmModal = this.modal.create({
            nzTitle: null,
            nzContent: ModalContentComponent,
            nzFooter: null,
            nzComponentParams: {
              father: that,
              submitData: submitData,
            },
            nzClosable: false,
            nzMaskClosable: false,
          });
        } else {
          this.isSpinning = true;
          this.isNzRELoading = true;
          this.isNzOkLoading = true;
          this.isNzPreLoading = true;
          submitData.status = "ANSWERING";
          this.ActiveUpdataship(
            submitData,
            "RelasePage",
            "/project-manage/home"
          );
        }
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
      }
    }
  } //发布活动
  removalrelation(arr) {
    const result = [];
    const obj = {};
    for (let i = 0; i < arr.length; i++) {
      if (!obj[arr[i].nametype]) {
        result.push(arr[i]);
        obj[arr[i].nametype] = true;
      }
    }
    return result;
  } // 去重
  customfactor() {
    this.customfactorshow = true;
  } //自定义维度弹窗 --tip

  customOk() {
    this.factortable.detailedScoreConfigs.forEach((res) => {
      if (res.isSelect && !res.checked) {
        res.detailedScoreChildDimensions.forEach((val) => {
          val.isSelected = false;
        });
      }
    });
    this.getParentDimensions();
    this.confirmRelation("JOB", false);
    this.clearcode();
    this.mockfactor("OK");
  } //自定义维度选择确认 --tip

  getParentDimensions() {
    let projectReportParentDimensions = this.factortable.detailedScoreConfigs.filter(
      (item) => {
        return item.checked;
      }
    );
    this.prismaData.projectReportParentDimensions = [];
    projectReportParentDimensions.forEach((res) => {
      this.prismaData.projectReportParentDimensions.push({
        isSelect: true,
        name: res.name.zh_CN,
        standardQuestionnaireId: this.standardQuestionnaireId,
      });
    });
    this.checkcluedCa();
  } //projectReportParentDimensions 获取数据

  customCancel() {
    this.factortable.detailedScoreConfigs.forEach((res) => {
      res.checked = true;
      if (res.isSelect) {
        res.detailedScoreChildDimensions.forEach((val) => {
          val.isSelected = false;
        });
      }
    });
    this.prismaData.projectReportParentDimensions = [];
    this.factortable.detailedScoreConfigs.forEach((res) => {
      this.prismaData.projectReportParentDimensions.push({
        isSelect: true,
        name: res.name.zh_CN,
        standardQuestionnaireId: this.standardQuestionnaireId,
      });
    });
    this.getParentDimensions();
    this.confirmRelation("JOB", false);
    this.clearcode();
    this.mockfactor("default");
  } //自定义维度恢复默认 --tip

  mockfactor(type) {
    this.factortable.detailedScoreConfigs.forEach((item) => {
      if (item.checked) {
        item.showfactor = true;
      } else {
        item.showfactor = false;
      }
    });
    if (type == "OK") {
      this.prismaData.projectReportParentDimensions.forEach((item) => {
        this.projectReportDimensionslist.forEach((res) => {
          if (item.name == res.parentName) {
            res.saved = true;
          }
        });
      });
      let factortable = JSON.parse(JSON.stringify(this.factortable));

      factortable.detailedScoreConfigs = factortable.detailedScoreConfigs.filter(
        (res) => {
          return res.checked;
        }
      );
      this.projectReportDimensionslist = this.projectReportDimensionslist.filter(
        (res) => {
          return res.saved;
        }
      );
      this.prismaData.projectReportDimensions = this.projectReportDimensionslist;
      this.getCheckedname(factortable, this.prismaData.projectReportDimensions);

      this.checkcluedCa(); //检测是否有ca
    } else {
      this.prismaData.projectReportDimensions = this.projectReportDimensionslist = [];
    }

    this.customfactorshow = false;
  } //自定义维度 数据处理

  checkcluedCa() {
    let projectReportParentDimensions = this.factortable.detailedScoreConfigs.filter(
      (item) => {
        return item.checked;
      }
    );
    this.includeCa = false;
    if (this.hasQuestionnaireTipItems) {
      if (this.checkReportTypes.includes("CA")) this.includeCa = true;
    } else {
      projectReportParentDimensions.forEach((item) => {
        if (item.reportType == "CA") this.includeCa = true;
      }); //tip是否有ca活动
    }
    if (this.includeCa) {
      this.getnewEnums(this.factortable);
    } else {
      this.factortable.dimensionLevelEnumsZHnew = []; //没有ca就没有层级
    }
  }
  getTipjobs(reportTemplateId) {
    this.http.listTipjobs(reportTemplateId).subscribe((res) => {
      if (res.result.code == 0) {
        this.Tipjobslist = res.data;
        this.getselectjob();
      }
    });
  } //TIP对标岗位数据

  getCajobs() {
    this.http.listCajobs().subscribe((res) => {
      if (res.result.code == 0) {
        this.Tipjobslist = res.data;
        this.getselectjob();
      }
    });
  } //CA对标岗位数据

  getmodaljob() {
    let submitData = this.synthesisTime();
    if (submitData) {
      this.visibleJob = true;
      this.getselectjob();
    }
  } //对标岗位已经选择过的数据
  getselectjob() {
    if (this.reportTemplateId) {
      this.http.querylistTipjobs(this.reportTemplateId).subscribe((res) => {
        if (res.result.code == 0) {
          if (res.data) {
            this.radioValue = res.data.standardReportTemplateJobId;
            this.radioCode = res.data.dimensionLevel;
            this.checkedvalue = res.data.isSelectedModel;
            this.createtipid = res.data.id;
            this.radiolist = this.Tipjobslist.filter((res) => {
              return this.radioValue == res.id;
            });
            this.radioCodelist = this.factortable.dimensionLevelEnumsZHnew.filter(
              (item) => {
                return this.radioCode == item.code;
              }
            );
            this.jobcodeshow = true;
            this.mapcodelist();
          }
        }
      });
    }
  }

  ngModelSelect(e) {
    if (e) {
      if (
        this.standardReportType == "TIP_NEW_2" ||
        this.standardReportType == "TIP_NEW"
      ) {
        this.radioCode = null;
      } else {
        this.radioValue = null;
        this.radioCode = null;
      }
    }
  }

  nzOnJobStype() {
    this.jobcodeshow = false;
    if (
      this.standardReportType == "TIP_NEW_2" ||
      this.standardReportType == "TIP_NEW"
    ) {
    } else {
      this.checkedvalue = false;
    }
  } //选择岗位

  nzOnCodeStype() {
    this.jobcodeshow = false;
    this.checkedvalue = false;
  } //选择层级

  clearcode() {
    this.factortable.detailedScoreConfigs.forEach((item) => {
      item.detailedScoreChildDimensions.forEach((val) => {
        val.isSelected = false;
      });
    });
    this.clearCalist(this.prismaData.projectReportDimensions);
  }
  clearCalist(projectlist) {
    var i = projectlist.length;
    while (i--) {
      if (
        projectlist[i].standardQuestionnaireId == this.standardQuestionnaireId
      ) {
        projectlist.splice(i, 1);
      }
    }
    this.projectReportDimensionslist = projectlist;
    this.getCheckedname(this.factortable, projectlist);
  } //删除集合里面的ca选中的维度

  handlejob() {
    if (!this.ismodalclosed) {
      this.jobcodeshow = true;
      this.visibleJob = false;
    }
  } //对标岗位弹窗取消

  handleOk() {
    if (
      this.factortable.surveyStandardQuestionnaire.reportType == "CA" ||
      this.includeCa
    ) {
      if (
        this.standardReportType == "TIP_NEW_2" ||
        this.standardReportType == "TIP_NEW"
      ) {
        if (!this.radioCode && !this.checkedvalue) {
          // this.msg.warning("请选择层级或者自选模型！");
          this.customMsg.open("warning", "请选择层级或者自选模型");
          return;
        }
        if (!this.radioValue) {
          // this.msg.warning("请选择岗位！");
          this.customMsg.open("warning", "请选择岗位");
          return;
        }
      } else {
        if (!this.radioValue && !this.radioCode && !this.checkedvalue) {
          // this.msg.warning("请选择岗位和层级或者自选模型！");
          this.customMsg.open("warning", "请选择岗位和层级或者自选模型");
          return;
        }
      }
    } //是否是CA或者是否tip包含CA
    if (
      this.standardReportType == "TIP_NEW_2" ||
      this.standardReportType == "TIP_NEW"
    ) {
      if (!this.radioValue) {
        // this.msg.warning("请选择岗位！");
        this.customMsg.open("warning", "请选择岗位");
        return;
      }
    } //tip是否选择层级

    const submitData = this.synthesisTime();
    if (this.projectId) {
      if (this.projectType == "ANSWERING" || this.projectType == "OVER") {
        this.visibleJob = false;
        this.ismodalclosed = false;
      } else {
        this.isSpinning = true;
        this.isNzRELoading = true;
        this.isNzOkLoading = true;
        this.isNzPreLoading = true;
        if (submitData) {
          // this.getcustomlist()
          if (this.isUpdateing) {
            this.ActiveUpdataship(submitData, "JobPage", "");
          } else {
            this.getcustomlist();
          }
        } else {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPreLoading = false;
        }
      }
    } else {
      if (submitData) {
        this.createproject(submitData, "JobPage", "");
      }
    }
  } //对标岗位弹窗确认

  mapcodelist() {
    if (
      this.factortable.surveyStandardQuestionnaire.reportType == "CA" ||
      this.includeCa
    ) {
      let valuecode = this.checkedvalue
        ? null
        : (this.radioValue ? this.radioValue : "") +
          "-" +
          (this.radioCode ? this.radioCode : "");
      if (this.hasQuestionnaireTipItems) {
        // 新Tip-处理推荐指标
        this.factortable.questionnaireTipItems.forEach((a) => {
          a.detailedScoreConfigs.forEach((b) => {
            b.detailedScoreChildDimensions.forEach((c) => {
              c.jobLevel = [];
              c.jobLevelMappings.forEach((res) => {
                c.jobLevel.push(res.jobId + "-" + res.level);
              });
              c.color = "";
              c.jobLevel.forEach((element) => {
                if (valuecode != "-" && element.search(valuecode) != -1) {
                  c.color = "#FFBA3C";
                }
              });
            });
          });
        });
      } else {
        // 老Tip-处理推荐指标
        this.factortable.detailedScoreConfigs.forEach((item) => {
          item.detailedScoreChildDimensions.forEach((val) => {
            val.jobLevel = [];
            val.jobLevelMappings.forEach((res) => {
              val.jobLevel.push(res.jobId + "-" + res.level);
            });
            val.color = "";
            val.jobLevel.forEach((element) => {
              if (valuecode != "-" && element.search(valuecode) != -1) {
                val.color = "#FFBA3C";
              }
            });
          });
        });
      }
    }
  }
  Customized(list) {
    let data;
    data = {
      name: list.length != 0 ? list[0].name : null,
      projectId: this.projectId,
      standardQuestionnaireId: this.standardQuestionnaireId,
      standardReportTemplateJobId: list.length != 0 ? list[0].id : null,
      dimensionLevel: this.radioCode,
      isSelectedModel: this.checkedvalue,
    };

    if (this.createtipid) {
      data.id = this.createtipid;
    }
    this.prismaData.isSpinning = true;
    this.http.createlistTipjobs(data).subscribe((res) => {
      if (res.result.code == 0) {
        this.isSpinning = false;
        this.visibleJob = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
        this.jobcodeshow = true;
        this.mapcodelist();
        this.confirmRelation("JOB", true);
      }
    });
  } //确定对标岗位
  clearCustomized() {
    let data;
    data = {
      name: null,
      projectId: this.projectId,
      standardQuestionnaireId: this.standardQuestionnaireId,
      standardReportTemplateJobId: null,
      dimensionLevel: null,
      isSelectedModel: null,
    };

    if (this.createtipid) {
      data.id = this.createtipid;
    }
    this.prismaData.isSpinning = true;
    this.http.createlistTipjobs(data).subscribe((res) => {
      if (res.result.code == 0) {
        this.isSpinning = false;
        this.visibleJob = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
        this.jobcodeshow = true;
        this.mapcodelist();
        // this.confirmRelation('JOB', true) // 取消确定岗位
      }
    });
  } //清空对标岗位

  preview(submitData) {
    this.isSpinning = true;
    submitData.id = this.projectId;
    this.prismaApi.updateAnnouncedProject(submitData).subscribe((res) => {
      if (res.result.code == 0) {
        this.http.PreviewUrl(this.projectId).subscribe((res) => {
          if (res.result.code == 0) {
            window.open(res.data, "_blank");
          }
        });
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
      }
    });
  } //预览填答

  EditName() {
    this.showname = true;
    if (this.hasQuestionnaireTipItems) {
      this.changeeditorName = JSON.parse(
        JSON.stringify(this.checkTipMenuItem.name)
      );
    } else {
      this.changeeditorName = JSON.parse(
        JSON.stringify(this.toolsname[this.showIndex].title)
      );
    }
  }

  namehandleCancel() {
    this.showname = false;
  }
  namehandleOk() {
    if (this.changeeditorName.zh_CN.trim() == "") {
      // this.msg.error("工具名称不能为空！");
      this.customMsg.open("error", "工具名称不能为空！");
      return;
    }
    this.isSpinning = true;
    if (!this.projectId) {
      let submitData = this.synthesisTime();
      if (submitData) {
        this.prismaApi.create(submitData).subscribe((res) => {
          if (res.result.code == 0) {
            this.projectId = res.data[0].projectId;
            this.questionnaireId = res.data[0].id;
            this.UpdateName();
          }
        });
      } else {
        this.isSpinning = false;
      }
    } else {
      let submitData = this.synthesisTime();
      if (this.isUpdateing) {
        //发布的活动，
        if (submitData) {
          submitData.projectId = this.projectId;
          this.prismaApi.updateProjectSetting(submitData).subscribe((res) => {
            if (res.result.code == 0) {
              this.isSpinning = false;
              this.UpdateName();
            }
          });
        } else {
          this.isSpinning = false;
        }
      } else {
        //未发布的活动，
        if (submitData) {
          if (this.projectType == "PREVIEW") {
            submitData.status = "PREVIEW"; //(待发布)
          } else {
            submitData.status = "ANNOUNCED"; //(待发布)
          }

          submitData.id = this.projectId;
          this.prismaApi.updateAnnouncedProject(submitData).subscribe((res) => {
            if (res.result.code == 0) {
              this.isSpinning = false;
              this.UpdateName();
            }
          });
        } else {
          this.isSpinning = false;
        }
      }
    }
  }

  UpdateName() {
    let params = {
      name: this.changeeditorName,
      questionnaireId: this.questionnaireId,
    };
    if (this.hasQuestionnaireTipItems) {
      params["tipItemReportType"] = this.checkReportType;
    }
    this.prismaApi.updateAnswer(params).subscribe((res) => {
      if (res.result.code == 0) {
        this.showname = false;
        this.isSpinning = false;
        this.msg.success("更新成功！");
        this.getProjectSetting();
      } else {
        this.isSpinning = false;
      }
    });
  }

  getnewlead() {
    this.showmock = true;
    this.noviceGuidance = true;
  }
  closed() {
    this.showmock = false;
    this.noviceGuidance = false;
  }
  goHome(type) {
    if (type == "home") {
      this.router.navigateByUrl("/home");
    }
    if (type == "create") {
      this.router.navigateByUrl("/new-activity");
    }
  }

  spaneditor(item, i) {
    item.isChecked = !item.isChecked;
    if (this.prismaData.name.zh_CN) {
      this.factornameshow = true;
      this.factornames = JSON.parse(JSON.stringify(item.name));
      this.factorindex = i;
      localStorage.setItem("oldname", JSON.stringify(item.name));
    } else {
      // this.msg.warning("请填写活动名称！");
      this.customMsg.open("warning", "请填写活动名称");
    }
  }
  spanOk() {
    if (this.factornames.zh_CN) {
      this.factortable.standardAnalysisFactorVOS[
        this.factorindex
      ].name = this.factornames;
      this.nzSimple = true;
      let submitData = this.synthesisTime();
      if (!this.projectId) {
        if (submitData) {
          this.createproject(submitData, "FactorPage", "");
        } else {
          this.nzSimple = false;
        }
      } else {
        this.isSpinning = true;
        this.isNzRELoading = true;
        this.isNzOkLoading = true;
        this.isNzPreLoading = true;
        if (submitData) {
          this.ActiveUpdataship(submitData, "FactorPage", "");
        } else {
          this.isSpinning = false;
          this.isNzRELoading = false;
          this.isNzOkLoading = false;
          this.isNzPreLoading = false;
        }
      }
    } else {
      // this.msg.warning("中文名称不可为空！");
      this.customMsg.open("warning", "中文名称不可为空");
    }
  }
  spanCancel() {
    this.factornameshow = false;
    this.factornames = JSON.parse(localStorage.getItem("oldname"));
  }

  hideProcess() {
    this.isNzOkLoading = false;
    this.isNzRELoading = false;
    this.isNzPreLoading = false;
    this.isSpinning = false;
  }

  gettotalmoney(e) {
    this.pricenums = this.factortable.detailedScoreConfigs.filter((item) => {
      return !item.checked;
    }).length;
    if (!this.projectId) {
      let parmas = {
        reqCurrent: {
          reportType: this.standardReportType,
          standardQuestionnaireId: this.standardQuestionnaireId,
          reportStyles: this.reportStyles,
          paymentConfigs: this.paymentConfig,
          reportTypeList: e,
        },
        reqTotal: {
          reportType: this.standardReportType,
          standardQuestionnaireId: this.standardQuestionnaireId,
          reportStyles: this.reportStyles,
          paymentConfigs: this.paymentConfig,
          reportTypeList: this.reportTypes,
        },
      };
      this.prismaApi.getTipDifferenceNoSave(parmas).subscribe((item) => {
        if (item.result.code == 0) {
          this.DifferencePrice = item.data;
        }
      });
    } else {
      let parmas = {
        questionnaireId: this.questionnaireId,
        reportTypeEnumList: e,
      };
      this.prismaApi.getTipDifferenceSave(parmas).subscribe((item) => {
        if (item.result.code == 0) {
          this.DifferencePrice = item.data;
        }
      });
    }
  }
  // 打开AT考试问卷弹窗
  showModalExam() {
    let submitData = this.synthesisTime();
    if (submitData) {
      // AT试卷至少选中一个模型维度（关键指标）
      if (this.hasQuestionnaireTipItems) {
        if (
          this.isCheackDetailedScoreConfigs(
            this.checkTipMenuItem.detailedScoreConfigs
          ) &&
          this.tipCurrentDimensions.length == 0
        ) {
          // this.msg.warning("至少选择一个模型维度！");
          this.customMsg.open("warning", "至少选择一个模型维度");
          return;
        }
      } else {
        if (
          this.isCheackDetailedScoreConfigs(
            this.factortable.detailedScoreConfigs
          ) &&
          this.projectReportDimensionslist.length == 0
        ) {
          // this.msg.warning("至少选择一个模型维度！");
          this.customMsg.open("warning", "至少选择一个模型维度");
          return;
        }
      }
      // 创建活动 活动进行中 预发布可编辑
      this.isEditAT =
        !this.projectId ||
        this.projectType == "PREVIEW" ||
        this.projectType == "ANNOUNCED";
      this.visibleExam = true;
      window.document.documentElement.scrollTop = 0;
      const atMode = submitData.atMode || "FIX";
      const atSetting = submitData.atSetting || {};
      this.atMode = atMode;
      // 其他设置
      if (JSON.stringify(atSetting) != "{}") {
        this.otherForm = this.fb.group({
          isCanSwitchScreen: [atSetting.isCanSwitchScreen],
          switchScreenTimes: [atSetting.switchScreenTimes],
          isCanReEnter: [atSetting.isCanReEnter],
          reEnterInterval: [atSetting.reEnterInterval / 60],
          isEnableQuestionProtection: [atSetting.isEnableQuestionProtection],
        });
      }
      const options = this.factortable.standardAnalysisFactorVOS
        .filter((val) => val.isChecked)
        .map((val) => ({
          label: val.name.zh_CN,
          value: val.rule,
        }));
      if (this.permission) {
        this.isOtherChange = true;
      } else {
        this.isOtherChange = atMode == "FIX" ? true : false;
      }
      if (atMode === "ADAPTIVE") {
        // 自适应试卷
        this.initSelfAdaptionForm(1);
      } else {
        // 固定试卷
        if (JSON.stringify(atSetting) != "{}") {
          const { days, hours, minutes, seconds } = this.getTimes(
            atSetting.answerLimitTime || 0
          );
          this.fixdForm = this.fb.group({
            difficulty: [atSetting.difficulty, [Validators.required]],
            answerTimeLimitMode: [
              atSetting.answerTimeLimitMode,
              [Validators.required],
            ],
            isRandomSampleQuestions: [atSetting.isRandomSampleQuestions],
            isCannotAnswerBack: [atSetting.isCannotAnswerBack],
            // 填答限制时间
            days: [days],
            hours: [hours],
            minutes: [minutes],
            seconds: [seconds],
          });
        }
      }
    }
  }
  //关闭AT考试问卷弹窗
  cancelModalExam() {
    this.visibleExam = false;
  }

  //确认AT考试问卷弹窗
  okModalExam() {
    this.checkAtAdaptiveDefaultAnalysisFactors();
    let submitData = this.synthesisTime();
    // 校验
    let atSetting = {};
    if (this.atMode == "FIX") {
      for (const i in this.fixdForm.controls) {
        this.fixdForm.controls[i].markAsDirty();
        this.fixdForm.controls[i].updateValueAndValidity();
      }
      for (const i in this.otherForm.controls) {
        this.otherForm.controls[i].markAsDirty();
        this.otherForm.controls[i].updateValueAndValidity();
      }
      if (this.fixdForm.valid && this.otherForm.valid) {
        const {
          answerTimeLimitMode,
          difficulty,
          isCannotAnswerBack,
          isRandomSampleQuestions,
          days,
          hours,
          minutes,
          seconds,
        } = this.fixdForm.value;
        const {
          isCanReEnter,
          isCanSwitchScreen,
          isEnableQuestionProtection,
          reEnterInterval,
          switchScreenTimes,
        } = this.otherForm.value;
        atSetting = {
          answerTimeLimitMode,
          difficulty,
          isCannotAnswerBack,
          isRandomSampleQuestions,
          answerLimitTime:
            seconds + minutes * 60 + hours * 3600 + days * 3600 * 24,
          isCanReEnter,
          isCanSwitchScreen,
          isEnableQuestionProtection,
          switchScreenTimes,
          reEnterInterval: reEnterInterval * 60,
        };
        submitData.atMode = this.atMode;
        submitData.atSetting = atSetting;
        this.saveExam(submitData);
      }
    } else {
      for (const i in this.otherForm.controls) {
        this.otherForm.controls[i].markAsDirty();
        this.otherForm.controls[i].updateValueAndValidity();
      }
      if (this.otherForm.valid) {
        const {
          isCanReEnter = false,
          isCanSwitchScreen = false,
          isEnableQuestionProtection,
          reEnterInterval,
          switchScreenTimes,
        } = this.otherForm.value;
        atSetting = {
          initQuestionDifficulty: "MEDIUM",
          answerTimeLimitMode: "PER_QUESTION",
          answerLimitTime: 60,
          isCannotAnswerBack: true,
          isRandomSampleQuestions: true,
          isCanReEnter,
          isCanSwitchScreen,
          isEnableQuestionProtection,
          switchScreenTimes,
          reEnterInterval: reEnterInterval * 60,
        };
        submitData.atMode = this.atMode;
        submitData.atSetting = atSetting;

        this.saveExam(submitData);
      }
    }
  }

  async saveExam(submitData) {
    if (!this.projectId) {
      if (submitData) {
        this.createproject(submitData, "AtSetting", "");
      } else {
        this.nzSimple = false;
      }
    } else {
      this.isSpinning = true;
      this.isNzRELoading = true;
      this.isNzOkLoading = true;
      this.isNzPreLoading = true;
      if (submitData) {
        // 如果自定义转问卷，恢复默认
        this.ActiveUpdataship(submitData, "AtSetting", "");
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzOkLoading = false;
        this.isNzPreLoading = false;
      }
    }
    this.visibleExam = false;
  }

  changeExamType(mode) {
    if (this.permission ) {
      this.isOtherChange = true;
    } else {
      this.isOtherChange = mode == "FIX" ? true : false;
    }
    if (!this.isEditAT) return;
    this.atMode = mode;
    this.inteOtherForm(mode);
    this.initSelfAdaptionForm(mode == "ADAPTIVE" ? 1 : 0);
  }

  // 防作弊初始化
  inteOtherForm(mode) {
    // 普通租户-切换试卷类型为自适应是时，“是否可以切换”和“是否重新登录”,两个开关变更为之前保存的数值，防止越过权限操作
    const atSetting = this.prismaData.atSetting || {};
    if (mode == "ADAPTIVE") {
      this.otherForm = this.fb.group({
        isCanSwitchScreen: [true],
        isCanReEnter: [true],
        switchScreenTimes: [1],
        reEnterInterval: [2],
        isEnableQuestionProtection: [
          atSetting.isEnableQuestionProtection || false,
        ],
      });
    } else {
      this.otherForm = this.fb.group({
        isCanSwitchScreen: [false],
        isCanReEnter: [false],
        switchScreenTimes: [atSetting.switchScreenTimes || 1],
        reEnterInterval: [atSetting.reEnterInterval / 60 || 2],
        isEnableQuestionProtection: [
          atSetting.isEnableQuestionProtection || false,
        ],
      });
    }
  }
  groupClick() {
    if (!this.isEditAT) return;
    this.atMode = "ADAPTIVE";
    this.initSelfAdaptionForm(1);
  }

  // 初始化自适应表单数据 0无内容 1默认数据
  initSelfAdaptionForm(type) {
    this.selfAdaptionForm = this.fb.group({
      initQuestionDifficulty: [type ? ["MEDIUM"] : null, [Validators.required]],
      answerTimeLimitMode: [
        type ? ["PER_QUESTION", "0-0-1-0"] : null,
        [Validators.required],
      ],
      otherSetting: [
        type ? ["isCannotAnswerBack", "ADAPTIVE"] : null,
        [Validators.required],
      ],
      atAdaptiveDefaultAnalysisFactors: [
        type
          ? this.selfAdaptionOptions.atAdaptiveDefaultAnalysisFactors.map(
              (val) => val.value
            )
          : null,
        [Validators.required],
      ],
    });
  }

  // 如果AT默认人口标签存在
  checkAtAdaptiveDefaultAnalysisFactors() {
    if (this.atAdaptiveDefaultAnalysisFactors.length > 0) {
      const code = this.atAdaptiveDefaultAnalysisFactors.map((val) => val.code);
      this.factortable.standardAnalysisFactorVOS.forEach((item) => {
        if (code.findIndex((val) => val === item.rule) >= 0) {
          // 自适应选中必选标签，固定去掉选中
          if (this.atMode == "ADAPTIVE") {
            item.isChecked = true;
            item.isRequire = true;
          }
        }
      });
    }
  }

  answerTimeLimitModeChange(value: string): void {
    this.atMode = "FIX";
    this.fixdForm.get("days")!.setValue(0);
    this.fixdForm.get("seconds")!.setValue(0);
    if (value == "FULL_QUESTIONNAIRE") {
      // 整卷限时
      this.fixdForm.get("hours")!.setValue(1);
      this.fixdForm.get("minutes")!.setValue(0);
      this.fixdForm.get("isCannotAnswerBack")!.setValue(false);
    } else if (value == "PER_QUESTION") {
      // 每题限时
      this.fixdForm.get("hours")!.setValue(0);
      this.fixdForm.get("minutes")!.setValue(1);
      this.fixdForm.get("isCannotAnswerBack")!.setValue(true);
    } else {
      // 不限时间
      this.fixdForm.get("minutes")!.setValue(0);
      this.fixdForm.get("isCannotAnswerBack")!.setValue(false);
    }
  }

  getTimes(seconds) {
    let d = Math.floor(seconds / (3600 * 24));
    let h = Math.floor((seconds % (3600 * 24)) / 3600);
    let m = Math.floor((seconds % 3600) / 60);
    let s = Math.floor(seconds % 60);
    return {
      days: d,
      hours: h,
      minutes: m,
      seconds: s,
    };
  }
  formatterPercent = (value: number): string => `${value}`;
  parserPercent = (value: string): string => `${Number(value)}`;

  // 根据维度与试卷配置组装作答说明
  packageDesc(atSetting) {
    // 组装数据 prismaData.answerDescription.zh_CN
    const texts = [
      "建议您在开始填答之前准备好草稿纸、笔和计算器，因为测验题目中包含一定量的数字计算。",
      "所有测验题目均为单选题，且无法返回修改。",
    ];
    // 维度 文字60s 逻辑75s 文字and逻辑75s
    // 文字 WZFX-3YN-AT-038
    // 逻辑 LJFX-5FC-AT-037
    // 数据 SZFX-5FC-AT-039
    // if (this.projectReportDimensionslist.length >1) {
    //   const isText = this.projectReportDimensionslist.findIndex((val)=> val.code == 'WZFX-3YN-AT-038');
    //   texts.push(`每道题左上角都有 ${isText>=0?'60秒/75秒':'75秒'} 倒计时。请务必在倒计时结束前选择您认为正确的答案，对于超时未作答的题目，系统将判该题为0分！`)
    // } else {
    //   texts.push(`每道题左上角都有 ${
    //     this.projectReportDimensionslist[0].code == 'WZFX-3YN-AT-038'? '60': '75'
    //   } 秒 倒计时。请务必在倒计时结束前选择您认为正确的答案，对于超时未作答的题目，系统将判该题为0分！`)
    // }
    texts.push(
      `每道题左上角都有 60秒 倒计时。请务必在倒计时结束前选择您认为正确的答案，对于超时未作答的题目，系统将判该题为0分！`
    );
    // 试卷 是否切换isCanSwitchScreen 是否重新登录isCanReEnter
    if (atSetting.isCanSwitchScreen) {
      texts.push(
        `填答过程中，尽量不要切换窗口，超过 ${atSetting.switchScreenTimes ||
          0}次 以后，您的所有作答都将被视为无效，整卷计0分！`
      );
    }
    if (atSetting.isCanReEnter) {
      texts.push(
        `填答过程中，如果不慎关闭窗口，请在 ${atSetting.reEnterInterval / 60 ||
          0}分钟 内重新登录且不要再次关闭窗口，否则您的所有作答也将被视为无效，整卷计0分！`
      );
    }
    const textHtml = texts
      .map(
        (text, index) =>
          `<p style="padding: 0px; min-height: 24px;"><span>${index +
            1}. ${text}</span></p>`
      )
      .join("");
    return textHtml;
  }

  // 新Tip-菜单选中回调
  onCheckTipMenusItem(e) {
    this.checkReportType = e.reportType;
    this.checkTipMenuItem = e.val;
    if (e.val.questionnaireTipItems) {
      // 选择TIP
      if (this.checkReportTypes.includes("CA")) {
        this.TipJobtip = [];
      } else {
        this.TipJobtip = e.val.relationPermissions.filter((res) => {
          return res.type == "JOB";
        });
      }
    } else {
      // 选择问卷
      this.TipJobtip = e.val.relationPermissions.filter((res) => {
        return res.type == "JOB";
      });
    }
    // 岗位关联任务判定：
    this.modaltip = e.val.relationPermissions.filter((res) => {
      return res.type == "MODEL";
    });
    this.descTip = e.val.relationPermissions.filter((res) => {
      return res.type == "ANSWER_DESCRIPTION";
    });
    // getCheckedname
    this.atTip = e.val.relationPermissions.filter((res) => {
      return res.type == "AT_QUESTIONNAIRE_MODE";
    });
    // 关键指标处理
    this.tipCurrentDimensions = this.formatNewTipProjectReportDimensions(
      e.reportType
    );
    // 子维度数据选中更新
    this.questionnaireTipItems.forEach((a) => {
      if (
        a.reportType == e.reportType &&
        this.checkDimensionMap[e.reportType]
      ) {
        a.detailedScoreConfigs.forEach((b) => {
          b.detailedScoreChildDimensions.forEach((c) => {
            if (this.checkDimensionMap[e.reportType].includes(c.code)) {
              c.isSelected = true;
            }
          });
        });
      }
    });
    this.checkTipIsJob(); // 新Tip-子维度禁用控制
    this.checkcluedCa(); //检测是否有ca
  }
  // 新tip-选择维度选中回调
  onCheckTipTypeItem(e) {
    this.checkReportTypes = e;
    // 新tip-岗位关联任务若无CA展示JOB，若有不展示
    if (this.hasQuestionnaireTipItems && this.checkReportTypes.includes("CA")) {
      this.TipJobtip = [];
    } else {
      this.TipJobtip = this.factortable.relationPermissions.filter((res) => {
        return res.type == "JOB";
      });
      this.questionnaireTipItems = this.factortable.questionnaireTipItems;
    }
  }
  // Tip-当前选中or取消的type
  onCheckType(e) {
    if (e == "CA") {
      // 将外层Tip关联任务取消确认
      this.factortable.relationPermissions.forEach((b) => {
        if (b.type == "JOB" && b.isConfirmed) {
          b.isConfirmed = false;
          this.confirmRelation("JOB", false, false);
        }
      });
      // this.clearcode() // ?好像没啥用
      // 若内部CA有对应关联任务,取消其确认状态
      this.factortable.questionnaireTipItems.forEach((a) => {
        if (a.reportType == "CA") {
          a.relationPermissions.forEach((b) => {
            if (b.type == "JOB" && b.isConfirmed) {
              b.isConfirmed = false;
              this.confirmRelation("JOB", false, false, "CA");
            }
          });
        }
      });
    }
    // 还需要清空指标（岗位变动，menu变动）
    // this.factortable.detailedScoreConfigs.forEach((res) => {
    //   if (res.isSelect && !res.checked) {
    //     res.detailedScoreChildDimensions.forEach((val) => {
    //       val.isSelected = false;
    //     });
    //   }
    // });
  }

  // 新Tip-选中的子维度/指标
  onCheckDimension(e) {
    this.checkDimensionMap[e.reportType] = e.checked;
    this.tipCurrentDimensions = this.formatNewTipProjectReportDimensions(
      e.reportType
    );

    // 作答说明更新显示
    const dimensionCodes = this.tipCurrentDimensions.map((val) => val.code);
    const params = {
      projectId: this.projectId,
      standardQuestionnaireId: this.standardQuestionnaireId,
      questionnaireId: this.questionnaireId,
      reportType: e.reportType,
      dimensionCodes,
      recoverFlag: false,
    };

    this.http.getQuestionnaireAnswerDescription(params).subscribe((res) => {
      if (res.result.code === 0) {
        this.factortable.questionnaireTipItems.forEach((val) => {
          if (val.reportType == e.reportType) {
            val.answerDescription = res.data;
          }
        });
      }
    });
  }

  // 新Tip组装选中父维度（保存用的）
  formatNewTipProjectReportParentDimensions() {
    const tipItemsFiltration = this.questionnaireTipItems.filter((val) =>
      this.checkReportTypes.includes(val.reportType)
    );
    // 选中的父维度/测评
    const sup = tipItemsFiltration.map((val) => ({
      code: val.parentDimension,
      isSelect: true,
      name: val.anotherName.zh_CN,
      // questionnaireId: this.normaldatas[0].standardQuestionnaireId, // ?应该是新tip标准问卷的id
      // standardQuestionnaireId: val.standardQuestionnaireId
      standardQuestionnaireId: this.standardQuestionnaireId,
    }));
    return sup;
  }
  // 新Tip组装选中所有子指标（保存用的）
  formatNewTipProjectReportDimensions(reportType?) {
    if (reportType) {
      // 具体父维度
      const dimensionsFiltrationObj = this.questionnaireTipItems.find(
        (val) => val.reportType == reportType
      );
      if (!dimensionsFiltrationObj) return [];
      const son = [];
      dimensionsFiltrationObj.detailedScoreConfigs.forEach((a) => {
        a.detailedScoreChildDimensions.forEach((b) => {
          if (
            this.checkDimensionMap[reportType] &&
            this.checkDimensionMap[reportType].includes(b.code)
          ) {
            son.push({
              code: b.code,
              isSelect: true,
              name: b.name.zh_CN,
              parentName: dimensionsFiltrationObj.anotherName.zh_CN,
              standardQuestionnaireId: this.factortable.standardQuestionnaireId,
              standardReportTemplateId: this.factortable
                .surveyStandardSagReportTemplate.id,
            });
          }
        });
      });
      return son;
    } else {
      // 全部地
      const dimensionsFiltration = this.questionnaireTipItems.filter(
        (val) => this.checkDimensionMap[val.reportType]
      );
      if (!dimensionsFiltration.length) return [];
      const son = [];
      dimensionsFiltration.forEach((a) => {
        a.detailedScoreConfigs.forEach((b) => {
          b.detailedScoreChildDimensions.forEach((c) => {
            if (this.checkDimensionMap[a.reportType].includes(c.code)) {
              son.push({
                code: c.code,
                isSelect: true,
                name: c.name.zh_CN,
                parentName: a.anotherName.zh_CN,
                standardQuestionnaireId: this.factortable
                  .standardQuestionnaireId,
                standardReportTemplateId: this.factortable
                  .surveyStandardSagReportTemplate.id,
              });
            }
          });
        });
      });
      return son;
    }
  }
  checkTipIsJob() {
    if (this.checkReportTypes.includes("CA")) {
      const jobTip = this.questionnaireTipItems
        .find((val) => val.reportType == "CA")
        .relationPermissions.find((res) => res.type == "JOB").isConfirmed;
      this.isTipJobtip = jobTip;
    } else {
      const jobTip = this.factortable.relationPermissions.find(
        (res) => res.type == "JOB"
      ).isConfirmed;
      this.isTipJobtip = jobTip;
    }
  }

  // 验证关联任务 type关联任务code reportType
  verifyTask(type: "JOB" | "ANSWER_DESCRIPTION", reportType?: string) {
    let targets = [];
    const tipItems = this.questionnaireTipItems.filter((val) =>
      this.checkReportTypes.includes(val.reportType)
    ); // 选中的父维度
    if (type == "JOB") {
      if (reportType) {
        targets = tipItems
          .find((val) => val.reportType == reportType)
          .relationPermissions.filter((val) => val.type == "JOB");
      } else {
        targets = this.factortable.relationPermissions.filter(
          (val) => val.type == "JOB"
        );
      }
    } else {
      tipItems.forEach((val) => {
        val.relationPermissions.forEach((item) => {
          if (item.type == "ANSWER_DESCRIPTION") {
            targets.push(item);
          }
        });
      });
    }
    const arr = [];
    targets.forEach((val) => {
      if (val.isNeedConfirm && !val.isConfirmed) {
        arr.push(true);
      }
    });
    return arr.length == 0;
  }
  onFormModeChange() {
    this.atMode = "FIX";
  }

  // 是否需要选中关键指标
  isCheackDetailedScoreConfigs(arr = []) {
    const isSelects = arr.filter((val) => val.isSelect);
    return isSelects.length > 0;
  }

  showScaleExpansionMode() {
    // this.submitSave();
    let submitData = this.synthesisTime();
    sessionStorage.setItem("savefactors", null);
    if (!this.projectId) {
      if (submitData) {
        this.createproject(submitData, "Scale", "");
      }
    } else {
      this.isSpinning = true;
      this.isNzRELoading = true;
      this.isNzPreLoading = true;
      this.isNzOkLoading = true;
      if (submitData) {
        this.ActiveUpdataship(submitData, "Scale", "");
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isNzOkLoading = false;
      }
    }
  }
  showTopicDistributionMode() {
    // this.submitSave();
    let submitData = this.synthesisTime();
    sessionStorage.setItem("savefactors", null);
    if (!this.projectId) {
      if (submitData) {
        this.createproject(submitData, "QuestionBook", "");
      }
    } else {
      this.isSpinning = true;
      this.isNzRELoading = true;
      this.isNzPreLoading = true;
      this.isNzOkLoading = true;
      if (submitData) {
        this.ActiveUpdataship(submitData, "QuestionBook", "");
      } else {
        this.isSpinning = false;
        this.isNzRELoading = false;
        this.isNzPreLoading = false;
        this.isNzOkLoading = false;
      }
    }
  }

  // 自定义-定制题本
  onClickbook() {
    // if(this.prismaData.projectReportDimensions.length == 0){
    //   this.msg.warning('请先选择题本模型！');
    //   return
    // }
    // if (this.relationrole.length != 0 && this.prismaData.isEnableRoleDimension) {
    //   this.prismaData.answerMode = 'SINGLE'
    // }
    this.isSpinning = true;
    this.isNzRELoading = true;
    this.isNzOkLoading = true;
    this.isNzPreLoading = true;
    if (this.question_book[0].isNeedSaveProject) {
      let submitData = this.synthesisTime();
      if (submitData) {
        if (this.projectId) {
          this.ActiveUpdataship(submitData, "BookPage", "custom-book");
        } else {
          this.createproject(submitData, "BookPage", "custom-book");
        }
      }
    }
  }
  // 定制题本跳转
  BookUpdate(url) {
    let queryParams = {};
    // let type =""
    // if (this.reportTypes.length <= 1) {
    //   type = this.standardReportType;
    // } else {
    //   type = this.reportTypes.includes('BLANK_CUSTOM')? 'BLANK_CUSTOM': this.standardReportType;
    // }
    const currentReportType = this.factortable.surveyStandardQuestionnaire
      .reportType;
    if (this.isUpdateing) {
      //进行中的活动修改
      queryParams = {
        type: this.projectType,
        projectId: this.projectId, // ?
        questionnaireId: this.questionnaireId, // ?
        standardQuestionnaireId: this.standardQuestionnaireId,
        standardReportType: this.standardReportType,
        edittype: "CUSTOMIZE",
        listChecked: "checked",
        currentReportType, // 多用问卷用
      };
    } else {
      //保存的活动修改
      queryParams = {
        type: this.projectType, // ?
        projectId: this.projectId, // ?
        questionnaireId: this.questionnaireId, // ?
        standardQuestionnaireId: this.standardQuestionnaireId,
        standardReportType: this.standardReportType,
        edittype: "CUSTOMIZE",
        // listChecked: 'checked',
        currentReportType, // 多用问卷用
      };
    }
    this.router.navigate([url], {
      queryParams: queryParams,
    });
  }
  // 量表拓展关联任务
  saveScaleExpansion() {
    console.log("量表拓展关联任务");
    // this.confirmRelation("ANSWER_DESCRIPTION", true, true);
  }
  // 存-默认多语言-新增情况下
  setLanOptions() {
    if (!this.routerInfo.snapshot.queryParams.projectId) {
      sessionStorage.setItem(
        "projectLanguages",
        JSON.stringify(["zh_CN", "en_US"])
      );
      sessionStorage.setItem("language", "zh_CN");
      return;
    }
  }

  // AT作答说明处理
  atDescDispose(submitData) {
    // 如果自定义作答说明的关联任务确认过，更新作答说明
    if (this.descTip.length != 0 && this.descTip[0].isConfirmed) {
      // 单问卷与多问卷AT处理
      if (!this.hasQuestionnaireTipItems) {
        if (this.atMode == "ADAPTIVE") {
          this.UpdateAnswer(false, false);
        } else {
          if (this.originalAtMode == "ADAPTIVE") {
            // 自适应变更为固定
            const { zh_CN = "", en_US = "" } = this.atTmpDesc;
            submitData.answerDescription.zh_CN = zh_CN;
            submitData.answerDescription.en_US = en_US;
            this.prismaData.answerDescription.zh_CN = zh_CN;
            this.prismaData.answerDescription.en_US = en_US;
            this.UpdateAnswer(false, false);
          }
        }
      } else {
        // tip时的处理
        if (this.atMode == "ADAPTIVE") {
          this.UpdateAnswer(false, false);
        } else {
          if (this.originalAtMode == "ADAPTIVE") {
            // 自适应变更为固定;
            const current = this.factortable.questionnaireTipItems.find(
              (val) => val.reportType == this.checkReportType
            );
            this.http
              .getDimensions(current.standardQuestionnaireId)
              .subscribe((res) => {
                this.factortable.questionnaireTipItems.forEach((val) => {
                  if (val.reportType == this.checkReportType) {
                    val.answerDescription = res.data.answerDescription;
                  }
                });
                this.UpdateAnswer(false, false);
              });
          }
        }
      }
    }
  }
  onSelectI18n(e) {
    this.lan = e;
  }

  //一键隐藏人口学标签
  hiddenAllcheck() {
    this.isShowAll = !this.isShowAll;
    this.factortable.standardAnalysisFactorVOS.forEach((res) => {
      if (res.isChecked && !res.isHidden) {
        res.isHidden = true;
      }
    });
    this.prismaData.analysisFactorDto = this.factortable.standardAnalysisFactorVOS;
  }

  //一键显示人口标签
  showAllCheck() {
    this.isShowAll = !this.isShowAll;
    this.factortable.standardAnalysisFactorVOS.forEach((res) => {
      if (res.isChecked  && res.isHidden) {
        res.isHidden = false;
      }
    });
    this.prismaData.analysisFactorDto = this.factortable.standardAnalysisFactorVOS;
  }

}
