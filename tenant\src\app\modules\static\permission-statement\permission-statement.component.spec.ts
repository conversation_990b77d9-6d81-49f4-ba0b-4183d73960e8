import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PermissionStatementComponent } from './permission-statement.component';

describe('PermissionStatementComponent', () => {
  let component: PermissionStatementComponent;
  let fixture: ComponentFixture<PermissionStatementComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PermissionStatementComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PermissionStatementComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
