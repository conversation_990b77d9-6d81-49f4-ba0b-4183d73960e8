# 活动与报告管理系统项目文档
## 一、项目核心概述
### 1.1 项目定位
本项目是基于Angular 8.2.14开发的活动与报告管理系统，聚焦于企业人才测评与调研场景的全流程管理。系统支持活动创建、配置、人员邀请、数据跟踪及报告生成等核心功能，覆盖题本管理、报告设置、组织架构关联等细分场景，为企业提供从活动发起到底层数据洞察的一站式解决方案。



### 1.2 核心功能模块
| 功能类别 | 具体说明 |
| --- | --- |
| **活动管理** | - 活动列表展示、创建、编辑、删除、延期等基础操作   - 活动人员邀请（支持360°/270°反馈场景的角色分配）   - 填答进度跟踪（人员/组织维度数据统计） |
| **活动设置** | - 活动基础信息配置（名称、描述、周期等）   - 题本关联与配置（算法选择、填答规则、得分设置）   - 高级设置（报告类型、常模配置、抽奖规则等） |
| **报告设置** | - 报告生成与下载（个人报告、团队报告、组织报告）   - 报告内容定制（指数/维度管理、发展建议、相关系数配置）   - 报告对比与分析（多报告对比、数据可视化） |
| **题本管理** | - 自定义题本创建、编辑、算法选择   - 填答设置（评价规则、显示逻辑、得分计算、评语配置） |
| **组织与权限** | - 组织架构管理（部门层级、人员关联）   - 管理员权限配置（主管理员/子管理员角色分配） |


## 二、技术栈详情
### 2.1 核心框架与工具
| 技术类型 | 具体技术栈 | 官方链接/说明 |
| --- | --- | --- |
| 前端框架 | Angular 8.2.14 | [Angular 8 文档](https://v8.angular.io/docs) |
| 状态管理 | Angular Service | Angular内置服务，用于组件通信与状态共享 |
| 路由管理 | @angular/router | Angular官方路由模块，支持懒加载、路由守卫 |
| UI组件库 | ng-zorro-antd 8.5.2 | [ng-zorro-antd 8 文档](https://8.ng.ant.design/docs/introduce/zh) |
| 设计资源 | 蓝湖（设计稿）、iconfont（图标库） | [蓝湖-knx 设计稿](https://lanhuapp.com/dashboard/#/item?tid=5a64515c)、[射手座-租户端图标](https://www.iconfont.cn/manage/index?projectId=3221334) |


### 2.2 关键依赖库
| 功能类别 | 依赖库 | 用途说明 |
| --- | --- | --- |
| 网络请求 | @angular/http | Angular内置HTTP客户端，处理接口请求与响应拦截 |
| 工具函数 | lodash | 提供数据处理、集合操作等工具函数，简化业务逻辑 |
| 图表展示 | echarts | 数据可视化图表库，用于活动数据统计、报告对比等场景的图表渲染 |
| 时间处理 | moment.js | 处理日期格式化、时间计算等操作，适配活动周期、填答时间等场景 |
| 富文本编辑 | tinymce | 支持复杂文本编辑，用于活动描述、报告评语等内容配置 |
| 拖拽功能 | ng2-dragula | 实现组件拖拽排序（如题本顺序调整、人员列表排序等） |
| 响应式编程 | rxjs | 处理异步数据流，实现组件状态管理、事件响应与请求联动 |
| 国际化支持 | 内置i18n模块（`src/app/core/i18n`） | 支持多语言切换，适配国际化企业需求 |


## 三、工程化配置
### 3.1 环境与工具
| 配置项 | 详情说明 |
| --- | --- |
| 包管理工具 | npm（随Node.js安装，推荐使用与Node版本兼容的最新版本） |
| Node.js版本 | 16.20.4（推荐使用NVM工具管理多版本Node环境，避免版本冲突） |
| 构建工具 | Angular CLI（基于Webpack，集成于项目，支持开发、构建、测试全流程） |


### 3.2 项目脚本命令
| 命令分类 | 具体命令 | 用途说明 |
| --- | --- | --- |
| 启动命令 | `npm run start-dev   # 启动开发环境（连接dev后端服务）`<br/>`npm run start-qa    # 启动测试环境（连接qa后端服务）`<br/>`npm run start-stage # 启动预发环境（连接stage后端服务）`<br/>`npm run start-prod  # 启动生产环境（模拟prod，谨慎使用）` | 端口4202 |
| 打包命令 | `npm run build   # 构建生产环境可部署的静态资源（输出到dist目录）` | 生成压缩后的静态文件，用于正式环境部署 |
| 环境变量配置 | 基于项目根目录下的 `src\environments` 文件夹管理，包含各环境的API地址、域名等配置 | 支持环境隔离，例如 `environments/environment.ts` 对应开发环境，`environment.prod.ts` 对应生产环境 |


## 四、开发与部署流程
### 4.1 部署环境
| 环境类型 | Jenkins部署地址 | 用途说明 |
| --- | --- | --- |
| QA环境 | [jenkins-qa](https://jenkins-java.knxdevelop.com/view/build-qa/job/local-sag-build-frontend-qa/build?delay=0sec) | 测试环境部署，用于功能测试与验证 |
| Dev环境 | [jenkins-dev](http://jenkins.dev.knxnet.cn:8080/login?from=%2Fview%2Fbuild-171) | 开发环境部署，用于开发团队内部联调 |


### 4.2 调试配置
#### 4.2.1 代理设置
推荐使用Chrome浏览器插件**XSwitch**配置代理，实现本地开发与远程服务联调，配置规则如下：

```javascript
qa-租户
[
  "https://sag-qa.knxdevelop.com/main/(.*)",
  "http://localhost:4202/$1"
],
stage-租户
[
  "https://sagittarius-stg.vxhcm.com/main/(.*)",
  "http://localhost:4202/$1"
],
线上租户
[
  "https://sagittarius.vxhcm.com/main/(.*)",
  "http://localhost:4202/$1"
],
```

#### 4.2.2 调试说明
+ 本地开发默认使用 `4202` 端口，需与代理配置端口保持一致；
+ 调试360°/270°反馈等复杂场景时，需确保人员角色分配、权限配置等接口正常联调；
+ 微前端场景下，通过 `src/app/core/sub-micro-app` 模块与基座应用通信，调试时需保证基座环境正常。



## 五、代码仓库规范
### 5.1 仓库地址
[GitLab仓库地址](https://gitlab.knxdevelop.com/knx-products-group/sagittarius-group/knx-platform-sagittarius-frontend-tenant)



### 5.2 分支管理策略
| 分支名称 | 对应环境 | 用途说明 |
| --- | --- | --- |
| master | 生产环境 | 存放正式发布版本代码，禁止直接提交，仅通过合并请求从release分支更新 |
| dev | 开发环境 | 开发主分支，用于集成功能分支，所有新功能开发基于此分支创建临时分支 |
| qa | 测试环境 | 供QA团队测试的分支，从dev分支合并，测试问题修复后同步回dev |
| release | 预发环境 | 生产发布前的准备分支，从qa分支合并，验证通过后合并至master |
| task-xxx | 功能开发分支 | 命名格式：`task-功能描述`（如task-activity-invite-360），基于dev创建，完成后合并至dev |
| bug-xx | 问题修复分支 | 命名格式：`bug-问题描述`（如bug-report-download-fail），基于对应环境分支（如qa）创建，修复后合并至对应分支 |


### 5.3 提交规范（强制）
提交信息需包含类型、描述及关联ID，确保历史可追溯：

+ 功能新增：`feature: 新增活动延期功能（关联需求ID：knx_platform_sagittarius#12457）`
+ 问题修复：`fix: 修复报告批量下载失败问题（关联bugID：knx_platform_sagittarius#12457）`
+ 文档更新：`docs: 更新活动设置模块文档`
+ 代码重构：`refactor: 重构活动邀请逻辑`
+ 样式调整：`style: 优化活动列表卡片样式`



## 六、项目结构详解
```plain
src/
├── app/
│   ├── core/                      # 核心模块（全局配置与基础能力）
│   │   ├── i18n/                  # 国际化相关（多语言配置）
│   │   ├── net/                   # HTTP请求封装（拦截器、请求工具）
│   │   ├── startup/               # 应用启动初始化（基础数据加载）
│   │   └── sub-micro-app/         # 微前端基座通信相关
│   ├── layout/                    # 布局组件
│   │   ├── breakcrumb/            # 面包屑导航
│   │   ├── default/               # 默认布局（含header/sidebar）
│   │   ├── footer/                # 页脚
│   ├── modules/                   # 功能模块（核心业务逻辑）
│   │   ├── auth/                  # 身份验证
│   │   │   ├── jump/              # SSO授权跳转页
│   │   │   ├── login/             # 登录组件
│   │   ├── custom-book/           # 题本管理
│   │   │   ├── add-custom-book/   # 新增题本（旧版）
│   │   │   ├── algorithm-select/  # 题本算法选择
│   │   │   ├── answer-setting/    # 填答规则设置（评价/得分/评语）
│   │   ├── home/                  # 系统首页
│   │   ├── new-activity/          # 创建活动
│   │   │   ├── prisma-custom-book/ # 调研-自定义题本管理
│   │   │   ├── subject-add/       # 新增题目
│   │   ├── new-create/            # 活动设置
│   │   │   ├── 360-type/          # 360°反馈活动设置
│   │   │   ├── adv_set/           # 高级设置（周期/权限等）
│   │   │   ├── topic-distribution/ # 题本分发配置
│   │   ├── new-prisma/            # 调研活动设置
│   │   │   ├── correlation-coefficient/ # 相关分析配置
│   │   │   ├── group-analysis/    # 多群体分析设置
│   │   │   ├── lottery/           # 抽奖规则设置
│   │   ├── org/                   # 组织架构管理
│   │   │   ├── org-charts/        # 组织架构图表展示
│   │   │   ├── org-edit/          # 组织信息编辑
│   │   ├── project-manage/        # 活动管理
│   │   │   ├── project-manage-home/ # 活动列表页
│   │   │   ├── project-manage-detail/ # 活动详情页
│   │   │   ├── invite-single/     # 人员邀请（单用户）
│   │   │   ├── person-list/       # 填答进度跟踪（人员列表）
│   │   ├── report-manage/         # 报告管理
│   │   │   ├── batch-send/        # 报告批量发送
│   │   │   ├── group-create/      # 创建团队报告
│   │   │   ├── prisma-set/        # 报告内容设置（指数/维度/评语）
│   │   │   ├── report-home/       # 报告列表与筛选
│   │   ├── service/               # 公共服务（API调用、工具方法）
│   │   ├── setting/               # 个人中心设置
│   │   └── tourist/               # 游客/产品展示模块（各测评类型介绍）
│   ├── shared/                    # 共享组件（通用UI与工具）
│   │   ├── btn/                   # 通用按钮组件
│   │   ├── i18n/                  # 国际化相关组件（多语言输入/选择）
│   │   ├── task-card/             # 活动设置右侧任务卡片
├── assets/                        # 静态资源
│   ├── iconfonts/                 # 图标字体
│   ├── images/                    # 图片资源
│   ├── tinymce/                   # 富文本编辑器配置
├── environments/                  # 环境变量配置
│   ├── environment.ts             # 开发环境
│   ├── environment.qa.ts          # QA环境
│   ├── environment.prod.ts        # 生产环境
├── styles/                        # 全局样式
│   ├── index.less                 # 样式入口
│   ├── theme.less                 # 主题变量
├── index.html                     # 入口HTML
├── main.ts                        # 应用入口
└── typings.d.ts                   # 类型声明
```

## 七、补充说明
1. **微前端集成**：系统作为微前端子应用，通过`sub-micro-app`模块与基座通信，调试时需确保基座环境（如租户端）正常运行。
2. **国际化支持**：多语言配置位于`core/i18n`，新增文本需同步添加中/英文翻译，避免界面乱码。
3. **富文本配置**：tinymce富文本编辑器资源位于`assets/tinymce`，自定义插件与样式需在此目录扩展。
4. **图表复用**：echarts图表统一封装于共享组件，新增图表时优先复用现有封装（如`report-home/lineChart`），确保风格统一。
5. **权限控制**：系统权限基于`service/permission-service.service.ts`实现，新增功能需同步更新权限校验逻辑。

通过以上文档，可全面了解项目的架构设计、开发规范与核心功能，便于团队协作与新成员快速上手。

