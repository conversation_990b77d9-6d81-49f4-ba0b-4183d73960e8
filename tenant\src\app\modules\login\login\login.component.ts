import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { Router } from "@angular/router";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { NzMessageService } from "ng-zorro-antd/message";
import { LoginService } from "../login.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-login",
  templateUrl: "./login.component.html",
  styleUrls: ["./login.component.less"],
})
export class LoginComponent implements OnInit {
  @Output() submit = new EventEmitter<any>();
  @Input() isLoadingOne = false;
  public rememberMe: boolean = false;
  act: true;
  Agreechecked = false;
  public loginModel = {
    tenantShortName: "",
    username: "",
    password: "",
  };

  validateForm: FormGroup;

  constructor(
    private router: Router,
    private fb: FormBuilder,
    private msg: NzMessageService,
    private loginServ: LoginService,
    private customMsg: MessageService
  ) {}

  ngOnInit(): void {
    if (this.loginServ.drawerRef) {
      this.loginServ.drawerRef.close();
    }

    this.validateForm = this.fb.group({
      tenantShortName: [null, [Validators.required]],
      username: [null, [Validators.required]],
      password: [null, [Validators.required]],
    });
  }

  toRegister() {
    this.router.navigateByUrl("user/register");
  }

  toForgot() {
    const loginName = this.loginModel.tenantShortName || "";
    this.router.navigateByUrl(
      loginName ? `/user/forgot?loginName=${loginName}` : "/user/forgot"
    );
  }

  onSubmit() {
    for (const i in this.validateForm.controls) {
      this.validateForm.controls[i].markAsDirty();
      this.validateForm.controls[i].updateValueAndValidity();
    }

    if (this.validateForm.invalid) {
      return;
    }
    if (!this.Agreechecked) {
      // this.msg.warning("请仔细阅读用户协议与隐私政策！");
      this.customMsg.open("warning", "请仔细阅读用户协议与隐私政策");
      return;
    }
    this.submit.emit(this.loginModel);
  }
  labelChange() {}
}
