import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class NewCreateService {
  tenantUrl: string;
  constructor(private http: HttpClient) {
    this.tenantUrl = "/tenant-api";
  }

  // 通过人口统计分组ID获取人口统计数据室
  listDemographicFromGroupId(json): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/demographic/listDemographicFromGroupId?demographicGroupId=${json}`;
    return this.http.post(api, {});
  }

  // 获取tip对标的岗位
  listTipjobs(reportTemplateId): Observable<any> {
    // const api = `${this.tenantUrl}/survey/standard/report/template/sag/listJob?questionnaireId=${questionnaireId}`;
    const api = `${this.tenantUrl}/survey/standard/report/template/sag/listJob?reportTemplateId=${reportTemplateId}`;
    return this.http.get(api);
  }

  // 获取tip对标的岗位
  listCajobs(): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/report/template/sag/listStandardJob`;
    return this.http.get(api);
  }

  getCaProject(): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getCaProject`;
    return this.http.get(api);
  } //360培养前测数据

  getCaMapping(): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getCa360DimensionMapping`;
    return this.http.get(api);
  } //360 mapping ca

  //创建tip对标岗位
  createlistTipjobs(json): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/saveOrUpdateReportTemplateJob`;
    return this.http.post(api, json);
  }

  //查询tip对标岗位
  querylistTipjobs(id): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getByReportTemplateId?reportTemplateId=${id}`;
    return this.http.get(api);
  }

  //确认关联任务
  confirmRelation(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/confirmRelationPermission`;
    return this.http.post(url, json);
  }

  //获取关联关系
  RoleAndDimensionInfo(projectId): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/getProjectRoleAndDimensionInfo?projectId=${projectId}`;
    return this.http.post(url, {});
  }

  //保存提交关联关系
  SaveRoleDimensions(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/saveRoleDimensions`;
    return this.http.post(url, json);
  }

  //作答说明 恢复默认
  getDimensions(id): Observable<any> {
    const url = `${this.tenantUrl}/survey/standard/questionnaire/get/${id}`;
    return this.http.get(url);
  }

  //作答说明 恢复默认
  getQuestionnaireAnswerDescription(params): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/getQuestionnaireAnswerDescription`;
    return this.http.post(url, params);
  }

  //更新填答指导
  updateAnswer(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/updateBasicInfo`;
    return this.http.post(url, json);
  }

  // 填答预览链接
  PreviewUrl(id): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/getPreviewUrl?projectId=${id}`;
    return this.http.get(url);
  }

  //获取量表尺度组数列表
  getGaugeScaleExtendList(questionnaireId): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/getGaugeScaleExtendList/${questionnaireId}`;
    return this.http.get(url);
  }
  //根据问卷ID及勾选的选项数获取问卷中量表题选项
  getGaugeOptionList(id, optionCounts): Observable<any> {
    console.log(id, optionCounts);
    const url = `${this.tenantUrl}/survey/project/getGaugeOptionList/${id}`;
    return this.http.get(url, {
      params: { optionCounts },
    });
  }
  //保存量表题扩展设置第一部分设置
  saveGaugeScaleExtend(params): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/saveGaugeScaleExtend`;
    return this.http.post(url, params);
  }
  //根据问卷ID获取第二部分问卷维度集合及维度下总分区间
  getDimensionList(questionnaireId, projectId): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/getDimensionList/${questionnaireId}/${projectId}`;
    return this.http.get(url);
  }
  // 保存量表尺度扩展第二部分常模映射关系
  saveGaugeScaleExtendNorm(params): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/saveGaugeScaleExtendNorm`;
    return this.http.post(url, params);
  }
  //根据ID获取量表题扩展设置
  getGaugeScaleExtend(questionnaireId, projectId): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/getGaugeScaleExtend/${questionnaireId}/${projectId}`;
    return this.http.get(url);
  }

  //获取二级维度列表
  getTwoLevelDimensions(params): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/dimension/twoLevelDimensions`;
    return this.http.get(url, { params });
  }
  //获取人口标签数据
  getListByProjectId(projectId): Observable<any> {
    const url = `${this.tenantUrl}/survey/demographic/listByProjectId/${projectId}`;
    return this.http.get(url);
  }
  //获取分发人口信息学分发维度
  getListSelectedDimensionsMappings(params): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listSelectedDimensionsMappings`;
    return this.http.get(url, { params });
  }
  // 保存人口信息学分发维度
  saveSelectedDimensionsMapping(params): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/saveSelectedDimensionsMapping`;
    return this.http.post(url, params);
  }

  // 手动触发再次邮件发送报告
  sendReport(projectId: any): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/sendEmailReportMessage?projectId=${projectId}`;
    return this.http.post(api, {});
  }

  /*
   *
   *  接口地址:
   *  @Description: 导出分发题
   *  @author: Sid Wang
   *  @Date: 2023/11/13
   *
   */
  public exportSelectQuestion(projectId: string): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    const api = `${this.tenantUrl}/survey/question/exportSelectQuestion?projectId=${projectId}`;
    return this.http.get(api, httpOptions);
  }
  /*
   *
   *  接口地址:
   *  @Description: 导入分发题
   *  @author: Sid Wang
   *  @Date: 2023/11/13
   *
   */
  public importSelectQuestion(formData, projectId): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/importSelectQuestion?projectId=${projectId}`;
    return this.http.post(api, formData);
  }

  /**
   *
   *  @author: Sid Wang
   *  @Date: 2025/06/06
   *  @code: #11997
   *  @todo: 360系列的分发：功能改造
   *
   */

  public listSelectDimension(projectId): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/listSelectDimension?projectId=${projectId}`;
    return this.http.post(api, {});
  }
  /**
   *
   *  @author: Sid Wang
   *  @Date: 2025/06/06
   *  @code: #11997
   *  @todo: 360系列的分发：功能改造
   *
   */
  public saveSelectDimension360(
    projectId,
    demographicId,
    isRole,
    params
  ): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/saveSelectDimension?projectId=${projectId}&isRole=${isRole}&demographicId=${demographicId}`;
    // return this.http.post(api, json);
    return this.http.post(api, params);
  }

  /*
   *
   *  接口地址:
   *  @Description: 360导出分发维度 exportSelectDimension
   *  @author: Sid Wang
   *  @Date: 2025/06/11
   *
   */
  public exportSelectDimension360(
    projectId,
    demographicId,
    isRole,
    params
  ): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    const api = `${this.tenantUrl}/survey/question/exportSelectDimension?projectId=${projectId}&isRole=${isRole}&demographicId=${demographicId}`;
    return this.http.post(api, params, httpOptions);
  }
  /*
   *
   *  接口地址:
   *  @Description: 360导入分发维度 importSelectDimension
   *  @author: Sid Wang
   *  @Date:  2025/06/11
   *
   */
  public importSelectDimension360(
    formData,
    projectId,
    demographicId,
    isRole
  ): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/importSelectDimension?projectId=${projectId}&isRole=${isRole}&demographicId=${demographicId}`;
    return this.http.post(api, formData);
  }

  /*
   *
   *  接口地址:
   *  @Description: 360导出分发题目 exportPersonQuestionMapping
   *  @author: Sid Wang
   *  @Date: 2025/06/11
   *
   */
  public exportPersonQuestionMapping360(
    projectId,
    demographicId,
    isRole
  ): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    const api = `${this.tenantUrl}/survey/question/exportPersonQuestionMapping?projectId=${projectId}&isRole=${isRole}&demographicId=${demographicId}`;
    return this.http.post(api, {}, httpOptions);
  }
  /*
   *
   *  接口地址:
   *  @Description: 360导入分发题目 importPersonQuestionMapping
   *  @author: Sid Wang
   *  @Date:  2025/06/11
   *
   */
  public importPersonQuestionMapping360(
    formData,
    projectId,
    demographicId,
    isRole
  ): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/importPersonQuestionMapping?projectId=${projectId}&isRole=${isRole}&demographicId=${demographicId}`;
    return this.http.post(api, formData);
  }

  

  /**
   * 清除360分发 removeAllSelected
   * @returns 
   */
  public removeAllSelected360(projectId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/question/removeAllSelected?projectId=${projectId}`;
    return this.http.post(api, {});
  }
}
