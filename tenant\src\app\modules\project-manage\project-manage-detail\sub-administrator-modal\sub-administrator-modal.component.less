// header {
//   span {
//     margin: 0;
//   }
//   .icon-box {
//     display: flex;
//     justify-content: right;
//     .icon-penetra-close {
//       font-size: 11px;
//       margin: 7px 7px 14px;
//       cursor: pointer;
//     }
//   }
//   .title {
//     font-size: 24px;
//     font-family: PingFangSC-Light, PingFang SC;
//     font-weight: 300;
//     color: #17314C;
//     line-height: 33px;
//     margin-bottom: 27px;
//     margin-left: 30px;
//   }
// }
.sub-admin {
  .content {
    display: flex;
    width: 100%;
    height: calc(100vh - 108px);
    background: #ffffff;
    // border-radius: 4px;
    // border: 1px solid #ECECEC;
    // margin: 0 34px;
    .left,
    .right {
      width: 333px;
    }
    .left {
      .header {
        padding: 16px;
        border: none !important;
        label {
          margin-left: 8px;
        }
      }
      .left-content {
        max-height: calc(100vh - 170px);
        overflow-y: auto;
        li {
          margin: 0 16px 8px 16px;
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 8px;
          color: #262626;
          > div {
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            font-weight: 400;
            margin-left: 8px;
          }
        }
        li.active {
          background: #f5f8ff;
          border-radius: 8px;
          color: #409eff;
        }
      }
    }
    .right {
      height: calc(100vh - 108px);
      overflow-y: auto;
      .expandedIcon {
        border: 1px solid #bfbfbf;
        padding: 2px;
        border-radius: 2px;
        font-size: 10px;
      }
    }
    .middle {
      width: 332px;
      border-left: 1px solid #ececec;
      border-right: 1px solid #ececec;
      .header {
        padding: 16px 16px 0 16px !important;
        border-bottom: none !important;
        ::ng-deep {
          .ant-select-selection,
          .ant-select-selection__rendered {
            height: 36px;
            line-height: 36px;
          }
        }
      }
      .middle-content {
        margin: 16px;
        border-radius: 8px;
        border: 1px solid #ececec;
        .content-header {
          border-bottom: 1px solid #ececec;
          padding: 16px;
          .space-between {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
        }
        .content-list {
          padding: 8px;
          ul {
            li {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              &:nth-last-child(1) {
                margin-bottom: 0;
              }
              > label {
                display: flex;
                ::ng-deep {
                  .ant-checkbox {
                    display: flex;
                    padding-top: 3px;
                  }
                }
              }
            }
          }
        }
        .scroll-height-1 {
          height: calc(100vh - 300px);
          overflow-y: auto;
        }
        .scroll-height-2 {
          height: calc(100vh - 260px);
          overflow-y: auto;
        }
        .scroll-height-3 {
          height: calc(100vh - 195px);
          overflow-y: auto;
        }
      }
    }
    .empty-text {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      > div {
        text-align: center;
        font-size: 16px;
        font-family: PingFangSC-Light, PingFang SC;
        font-weight: 300;
        color: #495970;
        padding: 16px;
      }
    }
    // .empty-text-right {
    //   width: 100%;
    //   height: 100%;
    //   display: flex;
    //   flex-direction: column;
    //   align-items: center;
    //   justify-content: center;
    //   text-align:center;
    //   font-size: 16px;
    //   font-family: PingFangSC-Light, PingFang SC;
    //   font-weight: 300;
    //   color: #495970;
    //   padding: 20px;
    // }
    .scroll-list {
      height: 100%;
      overflow-y: auto;
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #ececec;
    }
  }

  footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 10px 16px;
    text-align: right;
    left: 0px;
    background: #fff;
  }

  ::ng-deep {
    .ant-collapse-header {
      padding: 0 0 0 16px !important;
      display: flex;
      align-items: center;
      height: 56px;
      // border-bottom: 1px solid #ECECEC;
    }
    .ant-collapse-content {
      border-top: 1px solid #ececec;
      .ant-collapse-content-box {
        padding: 0 !important;
      }
    }
    .panel-content {
      display: flex;
      flex-wrap: wrap;
      padding: 0 15px 15px;
      background: #fbfbfb;
      li {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        margin-right: 5px;
        margin-top: 15px;
        // background: #ECECEC;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid rgba(140, 140, 140, 0.3);
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #262626;
        line-height: 17px;
        .text {
          margin-right: 7px;
          cursor: default;
        }
        .icon-penetra-close {
          font-size: 8px;
          cursor: pointer;
        }
      }
      li:hover {
        color: #409eff;
        border: 1px solid #409eff;
      }
    }
  }

 
}

.option_btn{

  position: absolute;
  margin-top: -54px;
  margin-left: 161px;

  .left_buttton {
    display: flex;
    align-items: center;
    margin-top: 10px;
    height: 32px;
    display:flex;
    li {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #495970;
      line-height: 20px;

      .iconfont {
        font-size: 18px;
        margin-right: 6px;
      }

      .ant-divider {
        background: #cbcbcb;
      }
    }

    .divider {
      margin: 0 10px;
    }

    .btn {
      cursor: pointer;

      .icon-icon_delete:hover,
      .icon-icon-:hover {
        color: #409eff;
        cursor: pointer;
      }
    }

    .btn:hover {
      color: #409eff;
    }
  }
  
}

.loading-div{
  position: absolute;
  text-align: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 30px 50px;
  width: 100%;
  height: 90%;

  .loading{
    margin-top: 25%;
    
  }
}
