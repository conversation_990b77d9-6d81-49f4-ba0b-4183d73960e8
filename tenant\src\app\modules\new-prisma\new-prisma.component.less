.iconfont {
  display: inline-flex;
  align-items: center;
  width: 16px;
  height: 16px;
  margin-left: 5px;
}

.hover-icon {
  color: #595959;
}

.hover-icon:hover {
  color: #409eff;
}

.index_xy {
  display: flex;
  justify-content: center;
  padding: 30px 0;
  background-color: #f5f6fa;
  height: 100%;
}

.big_contant {
  .create_p {
    display: flex;
    justify-content: space-between;

    .span_left {
      font-size: 24px;
    }

    .span_right {
      span {
        cursor: pointer;
      }

      >.span_blue {
        color: #53a8fe;
      }
    }
  }

  .create_name {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    font-size: 14px;
    font-weight: 400;
    color: #17314c;

    >li {
      >p {
        margin: 0px 0 30px 0;
        font-weight: bold;
      }

      .pri_name {
        width: 100%;
        height: 50px;
        border-radius: 5px;
        font-size: 16px;
      }
    }
  }

  .setmodal {
    margin-top: 30px;

    .setmodal_top {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;

      >.cur_span {
        cursor: pointer;
        color: #409eff;
      }
    }

    .setmodal_card {
      margin-top: 30px;
      width: 100%;
      min-height: 646px;
      background-color: #fff;
      border-radius: 8px;
      display: flex;

      .card_left {
        width: 146px;
        padding: 20px 0;
        border-right: 1px solid #e6e6e6;

        .left_div {
          width: 146px;
          height: 48px;
          line-height: 48px;
          text-align: center;
          font-size: 14px;
          font-weight: bold;
          cursor: pointer;

          >span {
            width: 100px;
            display: inline-block;
            overflow: hidden;
            /*超出部分隐藏*/
            white-space: nowrap;
            /*不换行*/
            text-overflow: ellipsis;
            /*超出部分文字以...显示*/
          }
        }

        .showClass {
          background-color: #f5faff;
          color: #409eff;
          border-left: 2px solid #409eff;
          width: 100%;
        }
      }

      .card_right {
        flex: 1;
        padding: 15px 30px;

        .right_top {
          display: flex;
          justify-content: space-between;

          .P_1 {
            font-size: 14px;
            font-weight: bold;
            color: #17314c;
          }

          .wei_span {
            cursor: pointer;
          }
        }

        .right_bottom {
          display: flex;
          margin-top: 40px;

          .bottom_left {
            max-width: 793px;
            flex: 1;

            .left_div {
              border: 1px solid #efefef;
              border-radius: 8px;
              padding: 20px;

              .topClass {
                margin-top: 15px;
              }

              .div_one {
                display: flex;

                .li_title {
                  font-size: 14px;
                  color: #17314c;
                  font-weight: bold;
                  width: 90px;
                }

                .li_list {
                  flex: 1;
                  display: flex;
                  flex-wrap: wrap;

                  .li_span {
                    padding-left: 20px;
                    display: inline-block;
                    max-width: 140px;
                    overflow: hidden;
                    /*超出部分隐藏*/
                    white-space: nowrap;
                    /*不换行*/
                    text-overflow: ellipsis;
                    /*超出部分文字以...显示*/
                  }
                }
              }
            }
          }

          .bottom_right {
            margin-left: 20px;
            width: 190px;
            min-height: 266px;
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .right_top {
              width: 100%;
              display: flex;
              justify-content: space-between;
              font-size: 12px;

              .diss_span {
                font-size: 14px !important;
                font-weight: bold;
                color: #17314c;
              }
            }

            .right_mid {
              position: relative;
              width: 160px;
              height: 90px;
              background: #ffffff;
              border-radius: 8px;
              margin-top: 15px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;

              .menu_xy {
                margin-top: 10px;
                font-size: 14px;
                font-weight: bold;
                color: #409eff;
                padding: 0 15px;
                height: 30px;
                background: #ffffff;
                border-radius: 15px;
                border: 1px solid #409eff;
                text-align: center;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}

.submit_xy {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 70px;
  background: #fff;
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: center;

  .center_menu {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .menus_xy {
      display: flex;
      align-items: center;

      .menus_left {
        width: 98px;
        height: 38px;
        border-radius: 19px;
        border: 1px solid #409eff;
        text-align: center;
        line-height: 38px;
        cursor: pointer;
        color: #409eff;

        &:hover {
          box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.15);
        }
      }

      .menus_right {
        width: 98px;
        height: 38px;
        background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
        box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
        border-radius: 19px;
        text-align: center;
        line-height: 38px;
        cursor: pointer;
        color: #fff;
        margin-left: 20px;
        border: 0;

        &:hover {
          box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.8);
        }
      }

      .menus_right_new {
        width: 98px;
        height: 38px;
        background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
        box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
        border-radius: 19px;
        text-align: center;
        line-height: 38px;
        cursor: pointer;
        color: #fff;
        margin-left: 20px;
        border: 0;

        &:hover {
          box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.8);
        }
      }
    }
  }
}

.time_picker {
  height: 50px;
  width: 500px;
  border-radius: 5px;

  ::ng-deep input {
    font-size: 16px;
  }
}

:host ::ng-deep {
  .ant-calendar-range-picker-separator {
    line-height: 40px;
  }

  .ant-calendar-picker-input.ant-input {
    height: 50px;
    width: 500px;
    border-radius: 5px;
  }

  .ant-calendar-picker-icon {
    color: #409eff;
    font-size: 20px;
  }

  .ant-calendar-picker-clear,
  .ant-calendar-picker-icon {
    right: 14px;
    margin-top: -10px;
  }
}

.nz_menu_ul {

  // padding: 15px;
  .nz_menu_li {
    display: flex;
    align-items: center;

    >span {
      display: inline-block;
      width: 30px;
    }

    >input {
      flex: 1;
      margin-left: 20px;
    }
  }

  .list_word {
    margin-left: 50px;
    margin-top: 5px;
    font-size: 12px;

    .word_left {
      width: 68px;
      background: #409eff;
      border-radius: 20px;

      color: #fff;
      text-align: center;
      cursor: pointer;
      margin-top: 10px;
    }

    .word_right {
      margin-top: 10px;
      width: 58px;
      background: #fafafa;
      border-radius: 20px;
      text-align: center;
      margin-left: 20px;
      color: #aaaaaa;
      cursor: pointer;
    }
  }
}

.nz_modal {
  position: relative;
}

.nz_spin {
  position: absolute;
  width: 100%;
  height: 120%;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #eee;
  opacity: 0.4;
  z-index: 9999999;
}

@keyframes example {
  0% {
    height: 0px;
  }

  100% {
    height: 200px;
  }
}

.mock_div {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999999999;

  .bg_ul {
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.6;
  }

  .img_ul {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;

    >li {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .btn_div {
      width: 160px;
      line-height: 38px;
      text-align: center;
      color: #fff;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 19px;
      cursor: pointer;
    }
  }
}

.title_right_1 {
  margin-left: -100px;
  display: flex;

  .linelin_left {
    width: 71px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 0px 2px 2px 0px;
    border: 1px solid #eee;
    font-weight: bold;
    cursor: pointer;
  }

  .linelin_right {
    width: 71px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 0px 2px 2px 0px;
    border: 1px solid #eee;
    font-weight: bold;
    cursor: pointer;
  }

  .linear {
    color: #fff;
    background: linear-gradient(90deg, #409eff 0%, #26d0f1 100%);
    border: none;
  }
}

.example {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  background: #f5f6fa;
  opacity: 0.5;
}

.upload_img {
  width: 129px;
  line-height: 38px;
  background-color: #ecf5ff;
  border: 1px dashed #419eff;
  text-align: center;
  border-radius: 4px;
  color: #419eff;
  cursor: pointer;
  user-select: none;
}

.Highlight {
  color: #049fff;
}

.footer_left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 128px;
  height: 38px;
  background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
  box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
  border-radius: 19px;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  cursor: pointer;
}

.footer_right {
  margin-left: 30px;
  cursor: pointer;
  width: 128px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background: #fafafa;
  border-radius: 19px;
}

.label_title {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    color: #17314c;
    font-size: 14px;
    font-weight: bold;
  }

  .div_right {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .custom_add_xy {
    color: #495970;
    font-size: 12px;
    display: flex;
    align-items: center;

    &:hover {
      color: #409eff;
    }

    i {
      margin-right: 4px;
      margin-left: 0;
    }

    >span {
      cursor: pointer;
    }
  }

  .border_left_d {
    border-left: 1px solid #e1e1e1;
    margin: 0 10px;
    height: 14px;
  }
}

.top_div {
  margin-top: 8px;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  width: 100%;
  min-height: 48px;

  .div_left {
    height: 100%;
    display: flex;
    align-items: center;

    .li_title {
      font-size: 14px;
      color: #17314c;
      font-weight: bold;
      width: 90px;
    }

    .li_list {
      display: flex;
      align-items: center;
      margin-top: -2px;
      padding-bottom: 14px;

      .li_span {
        display: flex;
        align-items: center;
        margin-top: 16px;
        margin-left: 20px;
        margin-right: 12px;

        ::ng-deep .ant-checkbox {
          top: 1px;
        }

        // max-width: 140px;

        // span {
        //   display: inline-block;
        //   max-width: 70px;
        //   overflow: hidden;
        //   /*超出部分隐藏*/
        //   white-space: nowrap;
        //   /*不换行*/
        //   text-overflow: ellipsis;
        //   /*超出部分文字以...显示*/
        // }
      }
    }
  }

  .div_right {
    // width: 150px;
    display: flex;
    // padding: 20px;
    align-items: center;
    justify-content: center;
  }

  .custom_add_xy {
    color: #409eff;
    font-size: 12px;

    >span {
      cursor: pointer;
    }
  }

  .border_left_d {
    border-left: 1px solid #e1e1e1;
    margin: 0 10px;
    height: 14px;
  }
}

.drawer-content {
  height: calc(100vh - 108px);
}

.drawer-footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}

.Pagebook {
  // border-bottom: 1px solid #e6e6e6;
  height: calc(100vh - 194px);
  display: flex;

  .left_page {
    width: 200px;
    border-right: 1px solid #e6e6e6;
    padding-top: 8px;

    div {
      // text-align: center;
      padding: 16px 18px;
      cursor: pointer;
      color: #17314c;
      border-left: 4px solid transparent;
    }

    .pagesclass {
      background-color: #f5faff;
      color: #262626;
      font-weight: bold;
      border-left: 4px solid #409eff;
    }
  }

  .right_page {
    padding-top: 8px;
    flex: 1;
    overflow-y: auto;
    .vxscrollbar();
  }
}

.pos_ships {
  position: absolute;
  top: 12px;
  right: 60px;
  display: flex;
}

.ships {
  width: 115px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  color: #fff;
  background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
  box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
  border-radius: 15px;
  cursor: pointer;
}

.ships_1 {
  width: 72px;
  text-align: center;
  line-height: 30px;
  border-radius: 15px;
  color: #409eff;
  border: 1px solid #409eff;
  cursor: pointer;
  margin-right: 30px;
}

.modal_foot {
  display: flex;
  padding: 20px;

  .next_1 {
    width: 128px;
    line-height: 38px;
    background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    text-align: center;
    color: #fff;
    cursor: pointer;
  }

  .return_1 {
    margin-left: 30px;
    width: 128px;
    line-height: 38px;
    background: #fafafa;
    border-radius: 19px;
    color: #aaaaaa;
    text-align: center;
    cursor: pointer;
  }
}

.put_search {
  width: 200px;
  height: 30px;
  background: #f8f8f8;
  border-radius: 15px !important;
}

.tree_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 0 10px 20px;
}

.scroll_page {
  overflow-y: auto;
  .vxscrollbar();
}

//滚动条
.vxscrollbar() {
  scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #f1f1f1;
    box-shadow: none;
  }

  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #c1c1c1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}

.li_span_new {
  display: inline-block;
  width: 100px;
  overflow: hidden;
  /*超出部分隐藏*/
  white-space: nowrap;
  /*不换行*/
  text-overflow: ellipsis;
  /*超出部分文字以...显示*/
}

.item_Question {
  width: 100%;
  border-bottom: 1px solid #e6e6e6;
  padding: 10px 20px;
}

.bg_blue {
  background-color: #f5faff;
}

.padd_bot {
  padding-bottom: 15px;
}

.ul_tips {
  width: 600px;
  height: 400px;
  padding-right: 20px;
  overflow-y: auto;
  .vxscrollbar();

  .top_tips {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.shiwRa {
  padding: 16px;
  height: calc(100vh - 86px);
  overflow-y: auto;
  .vxscrollbar();

  .details-head {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
  }
}

.bg_blue_tip {
  background-color: #f5faff;
  margin: 5px 0;
}

::ng-deep {
  .analytical-modal {
    .ant-modal-body {
      padding: 0;
    }
  }
}

::ng-deep {
  .round-right-drawer6 {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 108px);
      overflow: auto;
      .vxscrollbar();
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-title {
      font-size: 20px;
      font-weight: bold;
    }

    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }

  .round-right-drawer6-nofooter {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow: auto;
      .vxscrollbar();
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }

    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer6-bodyNoPadding {
    .ant-drawer-body {
      padding: 0;
      height: calc(100% - 107px);
      overflow: auto;
      .vxscrollbar();
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }

    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}

.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}

.drawer-head {
  width: 100%;

  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.drawer-card {
  border-radius: 8px;
  border: 1px solid #ececec;
  margin: 0 16px;
}

.btn1 {
  color: #409eff;
  border-radius: 6px;
  background-color: #f5f8ff;
  border-color: #f5f8ff;
  box-shadow: none;
  text-shadow: none;
  font-size: 12px;
  height: 28px;
}

.btn2 {
  color: #409eff;
  border-color: #409eff;
  border-radius: 6px;
  font-size: 12px;
  height: 28px;
}

.btn3 {
  padding: 0;
  font-size: 14px;
  height: 28px;
  margin-left: 16px;
}

.question-book-distribution {
  ::ng-deep {
    .ant-collapse {
      .ant-collapse-item {
        .ant-collapse-header {
          display: flex;
          align-items: center;
        }
      }
    }
  }
}