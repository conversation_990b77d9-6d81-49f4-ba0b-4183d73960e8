import { Component, OnInit } from '@angular/core';
import { HttpClient } from "@angular/common/http";

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.less']
})
export class FooterComponent implements OnInit {
  version="";
  tenantUrl="/tenant-api";

  constructor(private http: HttpClient) { }

  ngOnInit(): void {
    this.getVersion();
  }

  getVersion(){
    const api =`${this.tenantUrl}/survey/standard/versionManagement/getLatestVersion/SURVEY_SERVICE?_allow_anonymous=true`;
    this.http.get(api).subscribe((res:any) => {
      if(res.result.code == 0){
        this.version = res.data;
      }
    }
    )
  }

}
