import { Component, Input, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { NzModalService } from "ng-zorro-antd";

@Component({
  selector: "app-name-edit",
  templateUrl: "./name-edit.component.html",
  styleUrls: ["./name-edit.component.less"],
})
export class NameEditComponent implements OnInit {
  @Input() lan = "zh_CN";
  @Input() name: any;

  r = /(?<=\{\{)((?!(\{\{|\}\})).)+(?=\}\})/g;
  strOld: string;
  strNew: string = "";
  strList: string[] = [];

  selectionMap: any = {};
  selectionList: any[] = [];

  selfValue: string = "[ 自定义 ]";

  fDisabled: boolean = false;

  constructor(
    private modalService: NzModalService,
    private routeInfo: ActivatedRoute,
    private projSerivce: ProjectManageService,
    private surveySerivce: SurveyApiService
  ) {}

  th1: string = "序号";
  th2: string = "选项";
  th3: string = "调整后";

  ngOnInit() {
    if (!this.lan) {
      this.lan = "zh_CN";
    }
    if (this.lan !== "zh_CN") {
      this.th1 = "Order";
      this.th2 = "Options";
      this.th3 = "After Change";
      this.selfValue = "[ user-defined ]";
    }

    if (this.name) {
      this.strOld = this.name;
    }

    this.parseName();
  }

  changeDisabled() {
    this.fDisabled = true;
  }

  parseName() {
    this.strList = [];
    this.strNew = this.strOld;
    let arr = this.strOld.match(this.r);
    if (arr && arr.length > 0) {
      let repStr = this.strOld;
      arr.forEach((value, index) => {
        this.strList.push(value);
        repStr = repStr.replace(value, index + "");
        this.fillMap(value, index);
      });
      this.strNew = repStr;
    }
    Object.keys(this.selectionMap).forEach((key) => {
      this.selectionList.push({ key: key, value: this.selectionMap[key] });
    });
  }

  fillMap(value: string, index: number) {
    let options: any[] = [];
    let arr1: string[] = value.split(":");
    let oldValue = arr1[0];
    let newValue = oldValue;
    let listString = arr1[1];
    let arr2: string[] = listString.split(",");

    let selfFlag: boolean = true;
    for (let index = 0; index < arr2.length; index++) {
      const element = arr2[index];
      if (newValue === element) {
        selfFlag = false;
      }
      options.push({ id: element, name: element });
    }

    let key = this.generateKey(index);

    if (selfFlag) {
      oldValue = this.selfValue;
    }

    let tmp = {
      oldValue,
      newValue,
      options,
      listString,
      selfDefined: selfFlag,
    };
    this.selectionMap[key] = tmp;
  }

  generateKey(index) {
    return `{{${index}}}`;
  }

  getShowName(): string {
    let tmp: string = this.strNew;
    for (let index = 0; index < this.selectionList.length; index++) {
      const element = this.selectionList[index];
      let key = element.key;
      let value = element.value;
      tmp = tmp.replace(key, `${value.newValue}`);
    }
    return tmp;
  }

  getSaveName() {
    let tmp: string = this.strNew;
    for (let index = 0; index < this.selectionList.length; index++) {
      const element = this.selectionList[index];
      let key = element.key;
      let value = element.value;
      tmp = tmp.replace(key, `{{${value.newValue}:${value.listString}}}`);
    }
    return tmp;
  }

  itemChange(item) {
    if (item.oldValue === this.selfValue) {
      item.newValue = "";
      item.selfDefined = true;
    } else {
      item.newValue = item.oldValue;
      item.selfDefined = false;
    }
  }
}
