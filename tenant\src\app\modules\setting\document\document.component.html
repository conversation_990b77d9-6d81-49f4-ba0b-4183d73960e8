<div class="doc_section" *ngIf="isListPage">

    <div class="content client-width con" style="padding-bottom:30px;">

        <p class="title">共享中心</p>

        <div class="body">

            <div class="left">
                <ng-container *ngFor="let doc of mainList; let i = index;">
                    <div (click)="selectDoc(doc.id)" class="item" [ngStyle]="{'background': doc.bg, 'margin-right': (i+1)%3 === 0 ? '5px' : '15px' }">
                        <span>
                            {{doc.name.zh_CN}}
                        </span>
                    </div>
                </ng-container>
            </div>

            <div class="right">
                <span class="s1">常见问题</span>
                <span class="s2">涵盖系统操作及理论知识的常见问题，都在这里</span>
                <button (click)="viewCommon()" class="btn" nz-button nzType="default">
                    <span>查看</span> 
                </button>
   
            </div>

        </div>

    </div>

</div>

<!-- part 2 -->

<div class="guide_section" *ngIf="!isListPage">

    <div class="content client-width con" style="padding-bottom:30px;">

        <div class="title">
            <span class="share">共享中心</span>
            <div class="nav">
                <button (click)="backHome()" style="padding-right: 0px; color: #495970;" nz-button nzSize="middle" nzType="link">
                    共享中心
                </button>
                <span>
                    >
                </span>
                 <span style="color: #409EFF;">
                    {{currentDoc.name?.zh_CN}}
                 </span>
            </div>
        </div>
        

        <div class="body">

            <div class="left">

                <div class="doc_name">
                    {{currentDoc.name?.zh_CN}}
                </div>

                <div *ngFor="let dir of currentDoc.directorys">
                    <div [ngClass]="{'active': dir.id === currentDirId}" class="parent" (click)="selectDir(dir)">
                        {{dir.directoryName.zh_CN}}
                    </div>

                    <div class="childCon">
                        <ng-container *ngFor="let child of dir.children">
                            <div class="child" [ngClass]="{'active': child.id === currentDirId}" (click)="selectDir(child)" >
                                {{child.directoryName.zh_CN}}
                                <div *ngIf="child.id === currentDirId" class="mark">
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </div>

            </div>

            <div class="middle" id="middle">
                <p [innerHTML]="currentDirContent | html"></p>
                <!-- <img *ngIf="srcPath" [attr.src]="srcPath" [width]="imgWidth" [height]="imgHeight" alt=""> -->
            </div>

            <div class="right">
                <div class="rTitle">
                    其它共享
                </div>
                <div class="rContent">
                    <ng-container *ngFor="let dd of docList" >
                        <div (click)="selectDoc(dd.id)" class="rItem" [ngClass]="{'active': dd.id == currentDoc.id}">
                            <span>
                                {{dd.name.zh_CN}}
                            </span>
                        </div>
                    </ng-container>
                </div>
            </div>

        </div>

    </div>

</div>