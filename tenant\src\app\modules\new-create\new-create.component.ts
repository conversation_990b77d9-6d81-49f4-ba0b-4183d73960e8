import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { NzModalService, NzMessageService, treeCollapseMotion } from "ng-zorro-antd";
import * as  differenceInCalendarDays from 'date-fns/difference_in_calendar_days';
import { NewCreateService } from './new-create.service';
import { NewPrismaService } from './../new-prisma/new-prisma.service'
import { BehaviorSubject, timer } from 'rxjs';


@Component({
  selector: 'app-new-create',
  templateUrl: './new-create.component.html',
  styleUrls: ['./new-create.component.less']
})

export class NewCreateComponent implements OnInit {
  tabledata =[]
  standardReportType =''
  showtype =''
  constructor(
    private routerInfo: ActivatedRoute,

  ) { }
  ngOnInit(): void {

    this.tabledata = JSON.parse(localStorage.getItem('noprismadata'))
    
    this.standardReportType = this.routerInfo.snapshot.queryParams.standardReportType?this.routerInfo.snapshot.queryParams.standardReportType:this.tabledata[0].reportType
    if(this.standardReportType.indexOf('360') != -1 || this.standardReportType.indexOf('270') != -1){
      this.showtype = '360'
    }else{
      this.showtype = 'other'
    }

  }


  
}