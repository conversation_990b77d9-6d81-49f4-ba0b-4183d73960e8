import { Injectable, Inject } from "@angular/core";
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  CanActivateChild,
  PRIMARY_OUTLET,
} from "@angular/router";

import {KnxCoreService} from '@knx/knx-ngx/core';
import {of} from "rxjs";

@Injectable({
  providedIn: "root"
})
export class UrlPermissionGuard2 implements CanActivate, CanActivateChild {
  constructor(
    private router: Router,
    @Inject(KnxCoreService)
    private knxCoreService: KnxCoreService,
  ) {
  }

  canActivate(_route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    return this.setIsContinueTopage(_route, state);
  };

  canActivateChild(_route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    return this.setIsContinueTopage(_route, state);
  };

  setIsContinueTopage(route, state) : boolean {
    const currentRouetData = this.getRouteParams(route);
    const currentRouetPermissioncode = currentRouetData ? currentRouetData['permissionCode'] : null;
    if (currentRouetPermissioncode) {
      const sourceproname = this.knxCoreService.config.productCode + '_permissioncode';
      const storagePermissionCode = localStorage.getItem(sourceproname);
      if (storagePermissionCode) {
        let ispermission = true;
        if (Array.isArray(currentRouetPermissioncode)) {
          ispermission = JSON.parse(storagePermissionCode).some((
          (item) => currentRouetPermissioncode.includes(item)));
        }
        else {
          ispermission = JSON.parse(storagePermissionCode).includes(currentRouetPermissioncode);
        }
        if (ispermission) {
          return true;
        }
        else {
          this.router.navigateByUrl('/nopermission');
          return false;
        }
      }
      else {
        return false;
      }
    }
    else {
      return true;
    }
  }

  getRouteParams(route, url = '', routedata = null) {
    const ROUTE_DATA_BREADCRUMB = 'permissionCode';
    const children = route.children;
    if (children.length === 0) {
      if (route.outlet !== PRIMARY_OUTLET) {
        return routedata;
      }
      if (!route.data.hasOwnProperty(ROUTE_DATA_BREADCRUMB)) {
        return routedata;
      }

      if (routedata === null || routedata === undefined) {
        routedata = {};
      }

      if ('permissionCode' in routedata) {
        return routedata;
      }
      routedata.permissionCode = route.data[ROUTE_DATA_BREADCRUMB];
      return routedata;
    }

    for (const child of children) {
      if (child.outlet !== PRIMARY_OUTLET) {
        continue;
      }
      if (!child.data.hasOwnProperty(ROUTE_DATA_BREADCRUMB)) {
        return this.getRouteParams(child, url, routedata);
      }
      const routeURL = child.url.map((
      (segment) => segment.path)).join('/');
      url += `/${routeURL}`;
      routedata = {
        permissionCode: child.data[ROUTE_DATA_BREADCRUMB],
        queryParams: child.queryParams,
        url,
      };
      return this.getRouteParams(child, url, routedata);
    }
  }
}
