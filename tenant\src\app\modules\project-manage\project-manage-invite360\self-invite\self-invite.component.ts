import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { NzMessageService, zh_CN } from "ng-zorro-antd";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-self-invite",
  templateUrl: "./self-invite.component.html",
  styleUrls: ["./self-invite.component.less"],
})
export class SelfInviteComponent implements OnInit {
  @Input() rolelist: any[] = [];
  @Input() inviteSettings: any[] = [];
  @Output() saveInviteSettings = new EventEmitter<any>();

  isVisible = false;
  listOfData = [];
  rolelistfilter = [];
  constructor(
    private msg: NzMessageService,
    private customMsg: MessageService
  ) {}
  ngOnInit() {}
  ngOnChanges() {
    this.rolelistfilter = this.rolelist.filter((val) => val.type != "DEFAULT");
    this.listOfData = _.cloneDeep(this.inviteSettings);
  }
  openModal(): void {
    this.isVisible = true;
  }
  changeMin(e, id) {
    if (e) {
      this.listOfData.forEach((val) => {
        if (id == val.roleId) {
          val.min = e;
          if (e !== Math.trunc(e)) {
            val.min = Math.trunc(e);
            // this.msg.error('请输入整数')
            this.customMsg.open("error", "请输入整数");
          }
        }
      });
    }
  }
  changeMax(e, id) {
    if (e) {
      this.listOfData.forEach((val) => {
        if (id == val.roleId) {
          val.max = e;
          if (e !== Math.trunc(e)) {
            val.max = Math.trunc(e);
            // this.msg.error('请输入整数')
            this.customMsg.open("error", "请输入整数");
          }
        }
      });
    }
  }
  changeDisableLimit(e, id) {
    if (e == true) {
      this.listOfData.forEach((val) => {
        if (id == val.roleId) {
          val.max = null;
        }
      });
    }
  }
  handleOk(): void {
    for (let index = 0; index < this.listOfData.length; index++) {
      const element = this.listOfData[index];
      const role = this.rolelist.find((val) => val.id == element.roleId);
      if (
        !Number.isInteger(element.min) ||
        (!element.disableLimit && !Number.isInteger(element.max))
      ) {
        // this.msg.error(`【${role.name.zh_CN}】人数区间必须为整数`)
        this.customMsg.open(
          "error",
          `【${role.name.zh_CN}】人数区间必须为整数`
        );
        return;
      }
      // if (!element.min && element.min !== 0) {
      //   this.msg.error('请输入邀请人数区间')
      //   return
      // }

      // if (!element.max && !element.disableLimit) {
      //   this.msg.error('请输入邀请人数区间')
      //   return
      // }
      if (!element.disableLimit) {
        if (element.min > element.max || element.max < element.min) {
          // this.msg.error(`【${role.name.zh_CN}】邀请人数区间填写错误`)
          this.customMsg.open(
            "error",
            `【${role.name.zh_CN}】邀请人数区间填写错误`
          );
          return;
        }
      }
    }
    this.isVisible = false;
    this.saveInviteSettings.emit(this.listOfData);
  }
  handleCancel(): void {
    this.isVisible = false;
  }
}
