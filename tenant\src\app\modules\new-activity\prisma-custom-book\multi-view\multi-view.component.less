.penetration-questions {
  // 弹窗按钮
  margin-left: 50px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #495970;
  line-height: 20px;
  cursor: pointer;

  span {
    margin-left: 6px;
  }

  .penetration-span {
    padding-right: 18px;
  }
}

.penetration-questions:hover {
  color: #409eff;
}

::ng-deep {
  .popover-header {
    // 关联详情浮层
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 575px;
    height: 55px;

    span {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #17314c;
      line-height: 25px;
    }
  }

  .popup-method {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 33px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #495970;
    line-height: 17px;
  }

  .multi-popover-body {
    margin-bottom: 4px;
    max-height: 360px;
    overflow-y: auto;

    &-item {
      padding: 0 16px 16px;
      border-radius: 8px;
      border: 1px solid #ececec;
      margin-bottom: 16px;

      &:last-of-type {
        margin-bottom: 0;
      }

      &-header {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #ececec;
        justify-content: space-between;

        &-identifying {
          width: 12px;
          height: 12px;
          background: #409eff;
          transform: rotate(45deg);
          margin-right: 7px;
        }

        &-topic {
          width: 463px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        &-del {
          font-size: 12px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ff4f40;
          line-height: 17px;
          margin-left: 14px;
          cursor: pointer;
        }
      }

      &-main {
        padding: 16px 0;

        li {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #262626;
          line-height: 20px;
          margin-bottom: 8px;

          &:last-of-type {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .multi-view {
    .multi-view-body {
      display: flex;
      height: calc(100vh - 107px) !important;
      border-top: 1px solid #ececec;
      border-bottom: 1px solid #ececec;

      .left {
        flex: 0 0 200px;
        border-right: 1px solid #ececec;
        overflow-y: auto;

        ul {
          li {
            width: 100%;
            height: 54px;
            cursor: pointer;
            padding-left: 24px;
            display: flex;
            align-items: center;

            &.active {
              position: relative;
              background: #f5faff;

              &::before {
                position: absolute;
                content: "";
                width: 4px;
                height: 100%;
                background: #409eff;
                left: 0;
                top: 0;
              }
            }
          }
        }
      }

      .right {
        flex: 1;
        padding: 0 16px 16px;
        display: flex;
        flex-direction: column;

        .header {
          display: flex;
          align-items: center;
          height: 50px;
          flex: 0 0 50px;
          justify-content: space-between;
          border-bottom: 1px solid #ececec;

          &-left {
            display: flex;
            align-items: center;

            .associate-btn {
              background: #f5f8ff;
              border-radius: 6px;
              font-size: 12px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #409eff;
              line-height: 17px;
              padding: 6px 16px;
              cursor: pointer;
              margin-right: 10px;
            }
          }

          &-right {
            .btn3 {
              padding: 0;
              font-size: 14px;
              height: 28px;
              margin-left: 24px;
            }
          }
        }

        &-content {
          flex: 1;
          display: flex;
          overflow-y: auto;
          flex-direction: column;

          &-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;

            .search {
              width: 260px;
            }
          }

          &-main {
            flex: 1;
            overflow: auto;
            border-radius: 8px;
            border: 1px solid #ececec;

            .ant-tree {
              li {
                width: 100%;
                margin-left: 0 !important;
                padding: 16px;
                color: #262626;
                border-bottom: 1px solid #ececec;
                display: flex;
                white-space: inherit;
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #262626;
                line-height: 20px;

                span {
                  &.ant-tree-switcher {
                    display: none;
                  }

                  &.ant-tree-node-content-wrapper {
                    flex: 1;
                    height: auto;
                    word-break: break-all;
                  }
                }
              }
            }
          }
        }

        &-topic-content {
          flex: 1 0 0;
          overflow-y: auto;

          .ant-collapse-header {
            display: flex;
            align-items: center;
            background: #f8f8f8;
            justify-content: space-between;
            border-radius: 8px;
          }

          .ant-collapse-content-box {
            padding: 12px 6px 0;
          }

          &-list {
            &-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;
              height: 44px;

              &-drag-icon {
                font-size: 20px;
                color: #c4c4c4;
              }

              &-sort {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #262626;
                line-height: 20px;
                margin: 0 16px;
              }

              &-delete {
                color: #262626;
                font-size: 14px;
                width: 14px;
                cursor: pointer;
              }

              &.disable {
                .right-topic-content-list-item-drag-icon {
                  cursor: no-drop;
                }
              }
            }
          }
        }
      }
    }
  }
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  box-shadow: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  outline: none;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

.drawer-footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &-left {
    height: 28px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #262626;
    line-height: 1;
    display: flex;
    align-items: center;

    em {
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ff4f40;
      line-height: 1;
      padding: 0 6px 0 4px;
      font-style: normal;
    }
  }
}

::ng-deep {
  .multi-view-round-right-drawer {
    .ant-drawer-body {
      padding: 0px;
      // height: calc(100% - 108px);
      height: calc(100% - 106px);
      overflow: auto;
      // padding-bottom: 66px;
      // 滚动条
      scrollbar-color: auto;
      scrollbar-width: auto;
      overflow-y: overlay;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      // 滑块背景
      &::-webkit-scrollbar-track {
        // background-color: transparent;
        background-color: #f1f1f1;
        box-shadow: none;
      }
      // 滑块
      &::-webkit-scrollbar-thumb {
        // background-color: #e9e9e9;
        background-color: #c1c1c1;
        outline: none;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
      }
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }

    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
