import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'reportTitle'
})
export class ReportTitlePipe implements PipeTransform {
  private optionList : any[] = [];

  proStatusList = [
    { id: "csiHeader", name: "危机筛查测评(CSI)" },
    { id: "eapHeader", name: "职业性格测评(EPA)" },
    { id: "amaHeader", name: "工作成就动机测评(AMA)"},
    { id: "tipHeader", name: "人才画像测评TIP"},
    { id: "caHeader", name: "胜任力测评CA"}
  ];
  constructor() {
    this.initOptionList(this.proStatusList );
  }
  private initOptionList(list : any[]) : void {
    for (let index = 0; index < list.length; index++) {
      const element = list[index];
      this.optionList.push(element);
    }
  }
  transform(key: any): any {
    for (let index = 0; index < this.optionList.length; index++) {
      const element = this.optionList[index];
      if(key == element.id) {
        return element.name;
      }
    }
    return "/";
  }

}
