<div style="background:#F5F6FA;height: 100%;">
  <div class="container client-width">
    <ul style="display: flex;justify-content: flex-end;padding-top: 20px;">
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </ul>
    <header class="flex report-header space-between">
      <!-- <div class="flex left space-between"> -->
      <p class="index-title">租户配置</p>
      <div>
        <ul class="right">
          <nz-input-group [nzPrefix]="suffixIconSearch" class="search">
            <input
              type="text"
              nz-input
              placeholder="请输入关键词"
              [(ngModel)]="keyValue"
              (blur)="init()"
              (keydown.enter)="init()"
            />
          </nz-input-group>
          <ng-template #suffixIconSearch>
            <i nz-icon nzType="search" style="color: #409EFF"></i>
          </ng-template>

          <nz-select
            nzShowSearch
            nzAllowClear
            nzPlaceHolder="请选择工具类型"
            [(ngModel)]="selectedValue"
            (ngModelChange)="init()"
          >
            <nz-option
              *ngFor="let tool of tools"
              [nzLabel]="tool.name"
              [nzValue]="tool.id"
            ></nz-option>
          </nz-select>

          <ng-container
            *ngIf="permission"
          >
            <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
            <li class="btn">
              <nz-upload
                class="upload"
                [nzCustomRequest]="importTenantCongif"
                [nzFilter]=""
                [nzShowUploadList]="false"
              >
                <i class="iconfont icon-icon_import"></i><span>导入</span>
              </nz-upload>
            </li>
          </ng-container>

          <ng-container
            *ngIf="permission"
          >
            <li class="divider"><nz-divider nzType="vertical"></nz-divider></li>
            <li class="btn" (click)="exportTenantCongif()">
              <i class="iconfont icon-icon_export"></i><span>导出</span>
            </li>
          </ng-container>
        </ul>
      </div>
      <!-- </div> -->
    </header>

    <div class="tbody">
      <nz-table
        #basicTable
        [nzData]="listOfData"
        [nzFrontPagination]="false"
        [nzBordered]="true"
        [nzSize]="'middle'"
      >
        <thead>
          <tr>
            <th>维度编号</th>
            <th>维度名称</th>
            <th>题目组名称</th>
            <th>题目数</th>
            <th>计算方式</th>
            <th>配置公式</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of basicTable.data">
            <td>{{ data.dimensionCode }}</td>
            <td>{{ data.dimensionName?.zh_CN }}</td>
            <td>{{ data.name.zh_CN }}</td>
            <td>{{ data.count }}</td>
            <td>{{ data.algorithmName }}</td>
            <td>
              <div *ngIf="data.algorithmType != 'NORM_INTERFACE'">
                <div
                  *ngIf="
                    !data.algorithm?.assessmentNorms ||
                    !data.algorithm?.assessmentNorms.length
                  "
                >
                  <span>M：</span
                  ><input
                    class="M-input"
                    nz-input
                    [(ngModel)]="data.algorithm.m"
                    *ngIf="data.algorithm"
                  />
                  <span>SD：</span
                  ><input
                    class="SD-input"
                    nz-input
                    [(ngModel)]="data.algorithm.sd"
                    *ngIf="data.algorithm"
                  />
                </div>
                <div
                  *ngIf="
                    data.algorithm?.assessmentNorms &&
                    data.algorithm?.assessmentNorms.length > 0
                  "
                >
                  <span>CM：</span
                  ><textarea
                    #text
                    class="CM-input"
                    nz-input
                    [(ngModel)]="data.cm"
                    (blur)="textareaToModel(text.value)"
                    [nzAutosize]="{ minRows: 2, maxRows: 6 }"
                  ></textarea>
                </div>
              </div>

              <div *ngIf="data.algorithmType == 'NORM_INTERFACE'">
                <nz-radio-group [(ngModel)]="data.algorithm.normType">
                  <label nz-radio [nzValue]="0">人才招聘常模(严格)</label>
                  <label nz-radio [nzValue]="1">人才盘点常模(宽松)</label>
                </nz-radio-group>
              </div>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>

    <footer>
      <button class="btn" (click)="submit()">确认</button>
    </footer>
  </div>
</div>
