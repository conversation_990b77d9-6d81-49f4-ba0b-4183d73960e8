import { Component, EventEmitter, Input, Output } from "@angular/core";
import { NzMessageService } from "ng-zorro-antd";
import { ReportService } from "../../report.service";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "render-setting",
  templateUrl: "./render-setting.component.html",
  styleUrls: ["./render-setting.component.less"],
})
export class RenderSettingComponent {
  @Input() reportId: string = ""; // 报告id
  @Input() itemId: string = ""; // 九宫格id
  @Input() talentMappingNairList: any = null;

  @Output() closePopover = new EventEmitter<any>();

  demoValue = 100;
  currentStep = 0;
  renderOptions = []; // 呈现名称
  typeOptions = []; // 工具类型
  axisList = []; // 呈现设置配置-第一步
  axisCache = []; // 呈现设置配置-第一步-缓存
  areaList = []; // 区域设置-第二步
  areaCache = []; // 区域设置-第二步-缓存
  areaType = 0; // 显示样式 0:xyz无自定义，1:xy中有自定义，2:z中有自定义
  permission: boolean; // 管理权限
  confirmLoading: boolean = false;
  constructor(
    private msgServ: NzMessageService,
    private rptService: ReportService,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {}

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    this.getOptions();
    this.getMappingReportSudokuItems(this.itemId);
  }
  // 获取呈现名称or工具类型
  getOptions() {
    this.rptService
      .getMappingReportSudokuDictionaries("DISPLAY_NAME", this.reportId)
      .subscribe((res) => {
        this.renderOptions = res.data;
      });
    this.rptService
      .getMappingReportSudokuDictionaries("QUESTIONNAIRE_TYPE", this.reportId)
      .subscribe((res) => {
        this.typeOptions = res.data;
      });
  }
  // 自定义呈现名称
  addOption(
    input: HTMLInputElement,
    type: "DISPLAY_NAME" | "QUESTIONNAIRE_TYPE"
  ) {
    if (input.value) {
      const params = {
        name: {
          en_US: "",
          zh_CN: input.value,
        },
        reportId: this.reportId,
        type,
      };
      this.rptService
        .createMappingReportSudokuDictionaries(params)
        .subscribe((res) => {
          this.getOptions();
          input.value = "";
        });
    }
  }
  // 点击呈现设置-获取九宫格-详情
  getMappingReportSudokuItems(itemId) {
    this.rptService.getMappingReportSudokuItems(itemId).subscribe((res) => {
      const { axisList, areaList } = res.data;
      this.areaList = _.cloneDeep(areaList.sort((a, b) => a.number - b.number));
      if (axisList.length) {
        if (axisList.length == 3) {
          // 按XYZ排序
          this.axisList = axisList.sort((a, b) => a.axis.localeCompare(b.axis));
        } else {
          this.axisList = [
            ...axisList,
            {
              axis: "Z",
              highPercent: 25,
              itemId,
              lowPercent: 25,
              nameTypeId: null,
              questionnaireTypeId: null,
            },
          ];
        }
        // 缓存数据
        this.axisCache = _.cloneDeep(axisList);
        this.areaCache = _.cloneDeep(areaList);
      } else {
        const arr = ["X", "Y", "Z"];
        this.axisList = arr.map((val) => ({
          axis: val,
          highPercent: 25,
          itemId,
          lowPercent: 25,
          nameTypeId: null,
          questionnaireTypeId: null,
        }));
      }
    });
  }
  cilckStep(val: number) {
    if (val == 1) {
      const xAxisName = this.axisList.find((val) => val.axis == "X");
      const yAxisName = this.axisList.find((val) => val.axis == "Y");
      const zAxisName = this.axisList.find((val) => val.axis == "Z");
      let params = {
        itemId: this.itemId,
        xAxisNameTypeId: xAxisName.nameTypeId,
        yAxisNameTypeId: yAxisName.nameTypeId,
        zAxisNameTypeId: zAxisName.nameTypeId,
      };
      const customArr =
        this.renderOptions.filter((val) => val.code == "CUSTOM") || [];
      if (customArr.length) {
        const customIds = customArr.map((val) => val.id);
        if (
          customIds.includes(params.xAxisNameTypeId) ||
          customIds.includes(params.yAxisNameTypeId)
        ) {
          if (params.zAxisNameTypeId) {
            // 两轴
            this.areaType = 2;
          } else {
            // 三轴
            this.areaType = 1;
          }
        } else if (customIds.includes(params.zAxisNameTypeId)) {
          this.areaType = 2;
        } else {
          this.areaType = 0;
        }
      } else {
        this.areaType = 0;
      }
      if (!params.xAxisNameTypeId) {
        // this.msgServ.warning("X 必填");
        this.customMsg.open("warning", "X 必填");
        return;
      }
      if (xAxisName.nameTypeId && !xAxisName.questionnaireTypeId) {
        // this.msgServ.warning("请选择 X 的工具类型");
        this.customMsg.open("warning", "请选择 X 的工具类型");
        return;
      }
      if (!params.yAxisNameTypeId) {
        // this.msgServ.warning("Y 必填");
        this.customMsg.open("warning", "Y 必填");
        return;
      }
      if (yAxisName.nameTypeId && !yAxisName.questionnaireTypeId) {
        // this.msgServ.warning("请选择 Y 的工具类型");
        this.customMsg.open("warning", "请选择 Y 的工具类型");
        return;
      }
      if (!zAxisName.nameTypeId && zAxisName.questionnaireTypeId) {
        // this.msgServ.warning("请选择 Z 的呈现名称");
        this.customMsg.open("warning", "请选择 Z 的呈现名称");
        return;
      }
      if (zAxisName.nameTypeId && !zAxisName.questionnaireTypeId) {
        // this.msgServ.warning("请选择 Z 的工具类型");
        this.customMsg.open("warning", "请选择 Z 的工具类型");
        return;
      }
      this.rptService
        .getMappingReportSudokuDisplayAreaList(params)
        .subscribe((res) => {
          if (res.data) {
            // 修改-数据回填
            if (this.axisCache.length) {
              // 下一步时如果呈现名称未改变使用缓存回填
              const xAxisName_id = this.axisCache.find((val) => val.axis == "X")
                .nameTypeId;
              const yAxisName_id = this.axisCache.find((val) => val.axis == "Y")
                .nameTypeId;
              const zAxisName_id = this.axisCache.find((val) => val.axis == "Z")
                ? this.axisCache.find((val) => val.axis == "Z").nameTypeId
                : null;
              if (
                xAxisName.nameTypeId == xAxisName_id &&
                yAxisName.nameTypeId == yAxisName_id &&
                zAxisName.nameTypeId == zAxisName_id
              ) {
                // this.areaList = this.areaType == 1?this.areaList.map((val)=> ({
                //   ...val,
                //   developmentSuggestions: val.developmentSuggestions || {zh_CN:''},
                // })):this.areaList;
                const arr = _.cloneDeep(this.areaCache);
                arr.forEach((a) => {
                  const obj = res.data.find((b) => b.number == a.number);
                  a["isEditable"] = obj.isEditable;
                });
                this.areaList = arr;
              } else {
                this.areaList =
                  this.areaType == 1
                    ? res.data.map((val) => ({
                        ...val,
                        developmentSuggestions: val.developmentSuggestions || {
                          zh_CN: "",
                        },
                      }))
                    : res.data;
              }
            } else {
              // 新增
              this.areaList =
                this.areaType == 1
                  ? res.data.map((val) => ({
                      ...val,
                      developmentSuggestions: val.developmentSuggestions || {
                        zh_CN: "",
                      },
                    }))
                  : res.data;
            }
            this.currentStep = val;
          }
        });
    } else {
      this.currentStep = val;
    }
  }
  // 清空
  clear() {
    // this.getMappingReportSudokuItems(this.itemId);
    const arr = ["X", "Y", "Z"];
    this.axisList = arr.map((val) => ({
      axis: val,
      highPercent: 25,
      itemId: this.itemId,
      lowPercent: 25,
      nameTypeId: null,
      questionnaireTypeId: null,
    }));
    this.axisCache = [];
    this.areaList = [];
  }
  // 确认
  confirm() {
    if (this.confirmLoading) {
      return;
    }
    this.confirmLoading = true;
    const params = {
      areaList: this.areaList,
      axisList: this.axisList.filter((val) => !!val.nameTypeId),
      itemId: this.itemId,
      talentMappingNairList: this.talentMappingNairList,
    };
    this.rptService
      .getMappingReportSudokuCreateOrUpdate(params)
      .subscribe((res) => {
        if (res.data) {
          this.msgServ.success("呈现设置成功！");
          const { isResetObjectSetting } = res.data;
          this.closePopover.emit(isResetObjectSetting);
        }
        this.confirmLoading = false;
      });
  }
  formatterPercent = (value: number): string =>
    `${value ? value.toFixed(0) : 0} %`;
  parserPercent = (value: string): string => value.replace(" %", "");
  customDisabled(type: "DISPLAY_NAME" | "QUESTIONNAIRE_TYPE") {
    if (type == "DISPLAY_NAME") {
      const customArr =
        this.renderOptions.filter((val) => val.code == "CUSTOM") || [];
      return customArr.length > 0;
    } else {
      const customArr =
        this.typeOptions.filter((val) => val.code == "CUSTOM") || [];
      return customArr.length > 0;
    }
  }
  isNotSelectedOptions(
    value: any,
    type: "nameTypeId" | "questionnaireTypeId"
  ): boolean {
    const arr = [];
    this.axisList.forEach((val) => {
      if (type == "nameTypeId") {
        if (val.nameTypeId) arr.push(val.nameTypeId);
      } else {
        if (val.questionnaireTypeId) arr.push(val.questionnaireTypeId);
      }
    });
    return !arr.includes(value.id);
  }
  zRenderOptions(options, type) {
    if (type == "Z") {
      const codes = ["PERFORMANCE", "ABILITY"];
      return options.filter((val) => !codes.includes(val.code));
    }
    return options;
  }
  changePercent(item) {
    if (item.highPercent + item.lowPercent >= 100) {
      // this.msgServ.warning("数据规则相加需<100");
      this.customMsg.open("warning", "数据规则相加需<100");
      const arr = _.cloneDeep(this.axisList);
      arr.forEach((val) => {
        if (val.axis == item.axis) {
          val.highPercent = 25;
          val.lowPercent = 25;
        }
      });
      this.axisList = arr;
    }
  }
}
