import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  Inject,
} from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { ReuseTabService } from "@knz/assembly";
import { DA_SERVICE_TOKEN, ITokenService } from "@knz/auth";
import { NzMessageService } from "ng-zorro-antd/message";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-update-password",
  templateUrl: "./update-password.component.html",
  styleUrls: ["./update-password.component.less"],
})
export class UpdatePasswordComponent implements OnInit {
  visible = true;
  @Input() password: string;
  @Output("callBackPassword") callBack = new EventEmitter<any>();
  validateForm: FormGroup;
  act = false;
  type: string;
  tenantUrl: string = "/tenant-api";

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private router: Router,
    private routeInfo: ActivatedRoute,
    @Inject(ReuseTabService)
    private reuseTabService: ReuseTabService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private message: NzMessageService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    // this.validateForm.statusChanges.subscribe((ast) => {
    //   this.act=true;
    // })
    this.type = this.routeInfo.snapshot.queryParams["type"];

    console.log("type = " + this.type);

    this.validateForm = this.fb.group({
      newPassword: [null, [Validators.required]],
      checkPassword: [null, [Validators.required, this.confirmationValidator]],
    });

    if (this.type === "email") {
    } else {
      this.validateForm.addControl(
        "oldPassword",
        new FormControl(null, [Validators.required])
      );
    }
  }

  submitForm(): void {
    for (const i in this.validateForm.controls) {
      this.validateForm.controls[i].markAsDirty();
      this.validateForm.controls[i].updateValueAndValidity();
    }
    if (this.validateForm.valid) {
      this.callBack.emit(this.validateForm.value);
      let commitJson = {};
      if (this.type === "email") {
        let token = this.routeInfo.snapshot.queryParams["resetToken"];
        commitJson = {
          password: this.validateForm.value.newPassword,
          resetToken: token,
        };
        const api = `${this.tenantUrl}/auth/resetPassword?_allow_anonymous=true`;
        this.http.post(api, commitJson).subscribe((res: any) => {
          if (res.result.code === 0) {
            this.message.success("密码修改成功！");
            this.router.navigate(["/user/login"]);
          } else {
            // this.message.error('链接已经失效！');
            this.customMsg.open("error", "链接已经失效！");
          }
        });
      } else {
        commitJson = {
          oldPassword: this.validateForm.value.oldPassword,
          newPassword: this.validateForm.value.newPassword,
        };
        const api = `${this.tenantUrl}/userAccount/sysUser/changeSysUserPassword`;
        this.http.post(api, commitJson).subscribe((res: any) => {
          if (res.result.code === 0) {
            this.message.success("密码修改成功！");
            this.router.navigate(["/user/login"]);
          } else {
            // this.message.error(res.result.message);
          }
        });
      }
    }
  }

  updateConfirmValidator(): void {
    /** wait for refresh value */
    Promise.resolve().then(() =>
      this.validateForm.controls.checkPassword.updateValueAndValidity()
    );
  }

  confirmationValidator = (control: FormControl): { [s: string]: boolean } => {
    if (!control.value) {
      return { required: true };
    } else if (control.value !== this.validateForm.controls.newPassword.value) {
      return { confirm: true, error: true };
    }
    return {};
  };

  handleOk(): void {
    console.log("Button ok clicked!");
    this.submitForm();
  }

  handleCancel(): void {
    console.log("Button cancel clicked!");
    this.router.navigateByUrl("/setting");
    // this.visible=false;
    // this.callBack.emit('cancel');
  }
}
