<!--
    *
    *@author: <PERSON>
    *@Date: 2023/09/18
    *@content: 调研-高级设置-语言设置
    *
-->
<nz-drawer
  [nzWidth]="920"
  nzWrapClassName="i18n-setting-drawer"
  [nzVisible]="visible"
  nzTitle="语言设置"
  (nzOnClose)="handClose()"
>
  <nz-spin [nzSpinning]="isSpinning">
    <div class="i18nSetting">
      <div class="fixed-top">
        <div class="i18nSetting-tabs">
          <div class="i18nSetting-tabs-tab">
            <ng-container *ngIf="selectOptions.length > 0">
              <ng-container *ngIf="!disabled">
                <span
                  class="i18nSetting-tabs-tab-item"
                  *ngFor="let item of selectOptions"
                  [ngClass]="active === item.value ? 'active' : ''"
                  nz-popconfirm
                  nzTitle="当前正在编辑中，是否保存当前内容?"
                  (nzOnConfirm)="saveLan(item)"
                  (nzOnCancel)="cancelLan(item)"
                  nzPlacement="bottom"
                >
                  {{ item.name }}
                </span>
              </ng-container>
              <ng-container *ngIf="disabled">
                <span
                  *ngFor="let item of selectOptions"
                  class="i18nSetting-tabs-tab-item"
                  [ngClass]="active === item.value ? 'active' : ''"
                  (click)="handTab(item)"
                  >{{ item.name }}</span
                >
              </ng-container>
            </ng-container>
          </div>
          <div class="i18nSetting-tabs-extra">
            <nz-upload
              [nzCustomRequest]="customReq"
              [nzShowUploadList]="false"
              style="display: flex;align-items: flex-start;"
            >
              <button nz-button nzType="link">
                <i class="iconfont icon-icon_import"></i> 导入
              </button>
            </nz-upload>
            <button
              nz-button
              nzType="link"
              [nzLoading]="isDownLoadSpinning"
              (click)="downLoad()"
            >
              <i class="iconfont icon-icon_export"></i> 导出
            </button>
          </div>
        </div>
        <div class="i18nSetting-operation">
          <div>
            <div class="i18nSetting-operation-left">
              <ng-container>
                <span class="mr-8">语言简称</span>
                <input
                  nz-input
                  placeholder="请输入"
                  [(ngModel)]="languageSimplifyName"
                  style="width: 85px;"
                  maxlength="2"
                  class="mr-16"
                  [disabled]="disabled"
                />
                <span class="text-red">*仅2个字符</span>
              </ng-container>
            </div>
            <div>
              <ng-container *ngIf="disabled">
                <button
                  nz-button
                  nzType="primary"
                  nzGhost
                  (click)="disabled = false"
                >
                  编辑
                </button>
              </ng-container>
              <ng-container *ngIf="!disabled">
                <button nz-button nzType="default" (click)="clear()">
                  清空
                </button>
                <button nz-button nzType="primary" nzGhost (click)="saveLan()">
                  保存
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
      <div class="i18nSetting-group">
        <div
          *ngFor="let item of languageConfig; index as i"
          class="i18nSetting-group-item"
        >
          <p nz-tooltip [nzTitle]="'Key: ' + item.key" nzPlacement="topLeft">
            {{ i + 1 }}. {{ languageTitleMap[item.key] }}
          </p>
          <div>
            <input
              nz-input
              placeholder="请输入"
              [(ngModel)]="item.content"
              [disabled]="disabled"
              style="width: 100%;"
            />
          </div>
        </div>
      </div>
    </div>
  </nz-spin>
  <div class="footer">
    <button nz-button (click)="handClose()">返回</button>
  </div>
</nz-drawer>
