import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { PermissionStatementComponent } from './permission-statement/permission-statement.component';
import { RespondStatementComponent } from "@src/modules/static/respond-statement/respond-statement.component";


const routes: Routes = [
  { path: 'permission-statement', component: PermissionStatementComponent },
  { path: 'respond-statement', component: RespondStatementComponent }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StaticRoutingModule { }
