.card-body {
  width: 380px;
  height: 288px;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #E2E2E2;
  overflow: hidden;
  .border-top {
    width: 380px;
    height: 5px;
    background: #3372FF;
  }
  .border-top-research {
    background: #B374F2;
  }
  .content {
    padding: 20px 20px 19px 20px;
    header { // title
      display: flex;
      align-items: center;
      justify-content: space-between;
      h1 {
        width: 280px;
        height: 28px;
        font-size: 20px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #17314C;
        line-height: 28px;
        overflow: hidden;
        text-overflow: ellipsis;//文本溢出显示省略号
        white-space: nowrap;//文本不会换行
        cursor: pointer;
      }
      .process {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 46px;
        height: 21px;
        background: #419EFF;
        border-radius: 2px;
        font-size: 12px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 17px;
        margin-right: 4px;
      }
      .process-announced {
        background: #FFA36C;
      }
      .process-waiting_answer {
        background: #FF7575;
      }
      .process-over, .process-suspend {
        background: #F5F5F5;
        color: #AAAAAA;
      }
      .process-preview{
        background: #E96FCD;
      }
      .process-answering {
        background: #419EFF;
      }
    }
    .project-id{
      font-size: 12px;
      font-weight: 400;
      color: #AAAAAA;
      height: 17px;
      line-height: 17px;
      margin-top: 4px;
      margin-bottom: 10px;
    }
    .statistics { // 数据统计  已完成  进行中  未开始
      display: flex;
      justify-content: space-around;
      margin-bottom: 20px;
      li {
        p {
          text-align: center;
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #17314C;
          line-height: 25px;
        }
        span {
          position: relative;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #495970;
          line-height: 17px;
          &:after {
            content: '';
            position: absolute;
            left: -10px;
            top: 6px;
            width: 5px;
            height: 5px;
            background: #60CB7F;
            border-radius: 50%;
          }
        }
        .dots-blue {
          &:after {
            background: #409EFF;
          }
        }
        .dots-red {
          &:after {
            background: #FF7575;
          }
        }
      }
    }

    .detail-box {
      position: relative;
      height: 74px;
      background: #F9F9F9;
      border-radius: 4px;
      padding: 15px;
      .active {
        width: 275px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      p {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #17314C;
        line-height: 17px;
        margin-bottom: 10px;
      }
      .type-evaluation {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #3372FF;
        line-height: 17px;
        position: absolute;
        top: 7px;
        right: 8px;
        width: 40px;
        height: 26px;
        line-height: 26px;
        color: #3372FF;
        text-align: center;
        background: rgba(51, 114, 255, 0.08);
        border-radius: 2px;
      }
      .type-research {
        color: #AD66F1;
        background: rgba(173, 102, 241, 0.08);
      }
      .tips-box {
        position: absolute;
        bottom: 0;
        left: 74px;
        font-size: 11px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #F84444;
        line-height: 16px;
      }
    }
  }

  footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 15px 20px;
    .action-bar {
      display: flex;
      .icon-img {
        width: 30px;
        height: 30px;
        margin-right: 20px;
        // color: #F4F4F5;
        cursor: pointer;
      }
    }
    p {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #AAAAAA;
      line-height: 17px;
    }
  }
}



// 列表 
.standard-card-box {
  min-width: 1000px;
  height: 115px;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #E2E2E2;
  display: flex;
  overflow: hidden;
  .line-color {
    width: 5px;
    height: 115px;
    background: #3372FF;
  }
  .line-color-research {
    background: #B374F2;
  }
  .detail-box {
    flex: 1;
    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 20px 20px 15px;
      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 64%;
        &-text{
          display: flex;
          align-items: center;
          justify-content: space-between;
          h1 {
            max-width: 440px;
            height: 28px;
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #17314C;
            line-height: 28px;
            margin-right: 20px;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;//文本溢出显示省略号
            white-space: nowrap;//文本不会换行
          }
        }
        &-id{
          height: 17px;
          font-size: 12px;
          font-weight: 400;
          color: #AAAAAA;
          line-height: 17px;
        }
        .process {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 46px;
          height: 21px;
          background: #419EFF;
          border-radius: 2px;
          font-size: 12px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #FFFFFF;
          line-height: 17px;
          margin-right: 4px;
        }
        .process-announced {
          background: #FFA36C;
        }
        .process-waiting_answer {
          background: #FF7575;
        }
        .process-over, .process-suspend {
          background: #F5F5F5;
          color: #AAAAAA;
        }
        .process-answering {
          background: #419EFF;
        }
        .process-preview {
          background: #E96FCD;
        }
      }
      .tool-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 36%;
        .action-bar {
          display: flex;
          .icon-img {
            width: 30px;
            height: 30px;
            margin-right: 20px;
            // color: #F4F4F5;
            cursor: pointer;
          }
        }
        p {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #AAAAAA;
          line-height: 17px;
          margin-left: 44px;
        }
      }


    }
    // header end
    footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 0 20px 0 26px;
      .info{
        display: flex;
        align-items: center;
        // justify-content: space-between;
        width: 64%;
        padding-right: 6px;
        .statistics { // 数据统计  已完成  进行中  未开始
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 26px;
          width: 100%;
          // padding-right: 30px;
          li {
            padding-right: 30px;
            width: 33%;
            // background-color: #409EFF;
            p {
              display: flex;
              align-items: center;
              font-size: 18px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #17314C;
              line-height: 25px;
              span {
                position: relative;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #495970;
                line-height: 17px;
                margin-right: 10px;
                &:after {
                  content: '';
                  position: absolute;
                  left: -10px;
                  top: 6px;
                  width: 5px;
                  height: 5px;
                  background: #60CB7F;
                  border-radius: 50%;
                }
              }
              .dots-blue {
                &:after {
                  background: #409EFF;
                }
              }
              .dots-red {
                &:after {
                  background: #FF7575;
                }
              }
            }
          }
          li:nth-last-child(1) {
            margin-right: 0;
          }
        } //statistics end
  
        .activity-tool {
          display: flex;
          align-items: center;
          height: 26px;
          p {
            width: 300px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #17314C;
            margin-right: 10px;
          }
          .type-evaluation {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #3372FF;
            line-height: 17px;
            width: 40px;
            height: 26px;
            line-height: 26px;
            color: #3372FF;
            text-align: center;
            background: rgba(51, 114, 255, 0.08);
            border-radius: 2px;
          }
          .type-research {
            color: #AD66F1;
            background: rgba(173, 102, 241, 0.08);
          }
        } // activity-tool end
        
      }

      .activity-cycle {
        width: 36%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 26px;
        p {
          margin-left: 20px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #17314C;
          line-height: 17px;
        }
        .tips-box {
          margin-left: 20px;
          font-size: 11px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #F84444;
          line-height: 16px;
        }
      } // activity-cycle end


    } // footer end
  }
}
