import { Component, OnInit, Input, ChangeDetectorRef, ChangeDetectionStrategy, SimpleChanges } from '@angular/core';
import _ from 'lodash';
import { reduce } from 'rxjs/operators';

@Component({
    selector: 'app-lineChart',
    templateUrl: './lineChart.component.html',
    styleUrls: ['./lineChart.component.less'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class lineComponent implements OnInit {
    @Input() containerWidth
    @Input() containerHeight
    @Input() dataX
    @Input() dataY
    @Input() type
    @Input() chartcolor
    legends = [];
    colors = ['#409EFF', '#FFBA3B', '#FF7676', '##4FCF83','#A06BFF']
    public chartOption: any;

    constructor(private cdf: ChangeDetectorRef,) {

    }
    basicsetChartOption(dataX, dataY,per) {
        
        this.chartOption = {
            // grid: {
                
            //     top: -12,
            //   },
            xAxis: [
                {
                    type: 'category',
                    axisTick: { show: false },
                    data: dataX,
                    // axisLabel: { interval: 0, rotate: 20 },
                }
            ],
            yAxis: [
                {
                    type: 'value'
                }
            ],
            dataZoom: [{
                type: 'inside',
                start: 0,
                end: per
            }, {
                start: 0,
                end: 10
            }],
            series: dataY,
           
        };
    }
    setChartOption(dataX, dataY) {
        this.chartOption = {
            xAxis: {
                type: 'category',
                data: dataX,
                nameTextStyle:{
                    fontSize:12
                }
            },
            yAxis: {
                type: 'value'
            },
            dataZoom: [{
                type: 'inside',
                start: 0,
                end: 20
            }, {
                start: 0,
                end: 10
            }],
            series: [
                {
                    data: [150, 230, 224, 218, 135, 147, 260],
                    type: 'line'
                }
            //     {
            //     data: dataY,
            //     type: 'line'
            // },
            // {
            //     data: dataY[1]?dataY[1].data:[],
            //     type: 'line'
            // },
            // {
            //     data: dataY[2]?dataY[2].data:[],
            //     type: 'line'
            // },
            // {
            //     data: dataY[3]?dataY[3].data:[],
            //     type: 'line'
            // },
            // {
            //     data: dataY[4]?dataY[4].data:[],
            //     type: 'line'
            // },
            ]
        };

    }


    ngOnInit() {

    }
    ngOnChanges(changesQuestion: SimpleChanges) {
        let nameper =  Math.round((5/this.dataX.length)*100)
        let datalist = []
        let serieslist = []
        this.dataY.forEach(res => {
            res.scores.forEach((val,index) => {
                datalist.push({
                    score:val.score,
                    id:index,
                    color:val.color
                })
            })
            
        })
        
        let dataY = this.classification(datalist)
        dataY.forEach((res,i) =>{
            if(this.type != 'line'){
                serieslist.push({
                    type:'bar',
                    barGap:0,
                    data:res.data,
                    itemStyle:{
                        color:res.color
                    }
                })     
            }else{
                serieslist.push({
                    type:'line',
                    barGap:0,
                    data:res.data,
                    lineStyle:{
                        color:res.color
                    }
                })  
            }
        })


        this.basicsetChartOption(this.dataX, serieslist,nameper)
        this.cdf.detectChanges();
    }
    classification(arr) {
        var map = {},
          dest = [];
        for (var i = 0; i < arr.length; i++) {
          var ai = arr[i];
          if (!map[ai.id]) {　　//key 依赖字段 可自行更改
            dest.push({
              name: ai.id,
              data: [ai.score],
              color:ai.color
            });
            map[ai.id] = ai;
          } else {
            for (var j = 0; j < dest.length; j++) {
              var dj = dest[j];
              if (dj.name == ai.id) { //key 依赖字段 可自行更改
                dj.data.push(ai.score);
                break;
              }
            }
          }
        }
        return dest
      }
}
