<!-- 人员列表 -->
<div class="body bg">
  <div class="tit">
    <div style="padding-left: 10px;">
      <button
        class="draw-button"
        nz-button
        nzSize="small"
        nzType="primary"
        (click)="changeRespondentAllChecked()"
      >
        {{ isAll ? "全不选" : "全选" }}
      </button>
      <button
        class="draw-button"
        nz-button
        nzSize="small"
        nzType="primary"
        (click)="exportRespondentList()"
      >
        导出列表
      </button>
    </div>
    <div>人口标签</div>
    <div class="input">
      <nz-input-group [nzPrefix]="suffixIconSearch" class="search">
        <input
          type="text"
          nz-input
          placeholder="请输入关键词"
          [(ngModel)]="searchName"
          (keyup.enter)="loadData(true)"
        />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <img src="./assets/images/icon_search.png" (click)="loadData(true)" />
      </ng-template>
    </div>
  </div>

  <nz-table
    style="background-color: white;"
    #basicTable
    [nzData]="dataList"
    [nzFrontPagination]="false"
  >
    <!-- [nzScroll]="{ y: 'calc(100vh - 320px)' }" -->
    <thead>
      <tr>
        <th></th>
        <th nzWidth="112px">人口标签</th>
        <th>邀请人数</th>
        <th>应答数</th>
        <th>应答率</th>
        <th>有效应答数</th>
        <th>有效应答率</th>
      </tr>
    </thead>

    <tbody>
      <tr *ngFor="let data of basicTable.data; let ind = index">
        <td
          nzShowCheckbox
          [nzDisabled]="isAll"
          [(nzChecked)]="data.checked"
          (nzCheckedChange)="changeRespondentChecked($event, data.id)"
        ></td>
        <td>
          <span class="text-wrap" nz-tooltip [nzTooltipTitle]="data.name">{{
            data.name
          }}</span>
        </td>
        <td>{{ data.total }}</td>
        <td>{{ data.complete }}</td>
        <td>{{ data.completeRate }}</td>
        <td>{{ data.valid }}</td>
        <td>{{ data.validRate }}</td>
      </tr>
    </tbody>
  </nz-table>

  <!-- 分页控件 -->
  <div style="display: flex; justify-content: flex-end; padding: 12px 0;">
    <nz-pagination
      [(nzPageIndex)]="page.current"
      [(nzPageSize)]="page.size"
      [nzTotal]="page.total"
      [nzSize]="'small'"
      (nzPageIndexChange)="loadData()"
    >
    </nz-pagination>
  </div>
</div>
