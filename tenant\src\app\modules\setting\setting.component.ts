import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { Location } from "@angular/common";
import { NzMessageService } from "ng-zorro-antd";

import { ProjectManageService } from "../service/project-manage.service";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-setting",
  templateUrl: "./setting.component.html",
  styleUrls: ["./setting.component.less"],
})
export class SettingComponent implements OnInit {
  id: string;
  validateForm: FormGroup;
  subValidateForm: FormGroup;
  hiddenPsd = false;
  password = "xxxxxxx";
  type: any;

  peroidModel: any;
  percent: number;
  array = [];
  newsshow = "";
  tenantId = "";

  userList: any[] = [];
  showUserList: any[] = [];
  pageIndex = 1;
  pageTotal = 1;
  pageSize = 5;
  isPermission: boolean = false;
  editId: string;
  tenantUrl: string = "/tenant-api";
  constructor(
    private fb: FormBuilder,
    private routeInfo: ActivatedRoute,
    private http: HttpClient,
    private _location: Location,
    private router: Router,
    private msg: NzMessageService,
    private api: ProjectManageService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    /*let permission = sessionStorage.getItem('permission');
    this.isPermission = permission === 'true'
    this.isPermission ? this.loadUserData() : ''*/
    this.isAdmin();
    this.type = this.routeInfo.snapshot.queryParams["type"];
    if (this.type === "password") {
      this.hiddenPsd = true;
    }

    this.subValidateForm = this.fb.group({
      realName: [null],
      mobile: [null],
      email: [null, [Validators.email, Validators.required]],
    });

    this.validateForm = this.fb.group({
      name: [null, [Validators.required]], // 公司名称
      balance: [null], // 肯豆余额
      // remark : [null],
      realName: [null, [Validators.required]],
      // position:[null, [Validators.required]], 2022-11-24需求- 不需要显示职位
      mobile: [
        null,
        [Validators.pattern("^1[3-9]\\d{9}$"), Validators.required],
      ],
      email: [null, [Validators.email, Validators.required]],
      username: [null, [Validators.required]],
      password: [null],
    });
    let type = this.routeInfo.snapshot.queryParams["type"];
    if (type === "email") {
      return;
    }

    const api = `${this.tenantUrl}/userAccount/user/getSelfInfo`;
    this.http.post(api, {}).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.tenantId = res.data.tenantId;
        this.id = res.data.id;
        this.validateForm.patchValue({
          name: res.data.name, // res.data.name,  // 公司名称
          // balance:res.data.balance, // res.data.balance,                     // 肯豆余额
          // remark: res.data.remark,
          realName: res.data.realName,
          // position:res.data.position,
          mobile: res.data.mobile,
          email: res.data.email, // res.data.email,
          username: res.data.username, // res.data.username,
          password: "xxxxxxx",
        });
        this.getnewInfo();
      }
    });

    // 获取有效期
    const apiPeroid = `${this.tenantUrl}/survey/tenant/getPeriod`;
    this.http.get(apiPeroid).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.peroidModel = res.data;
        let used: number = this.peroidModel.usedTime;
        let remaining: number = this.peroidModel.remainingTime;
        this.percent = Math.floor((used * 100) / (used + remaining));
      }
    });
    this.getPlatforms();
    this.newsshow = sessionStorage.getItem("newsshow");
  }

  editSub(data, idx: number) {
    // if (this.editId) return this.msg.warning("请先保存当前人员信息");
    if (this.editId) return  this.customMsg.open("warning", "请先保存当前人员信息");
    this.userList.forEach((item, index) => {
      if (item.id !== data.id) {
        item.isEdit = false;
      } else {
        item.isEdit = true;
        this.editId = item.id;
      }
    });
    // this.requiredSubChange(true)
  }

  saveSub(data: any) {
    const phoneReg = /(^1\d{10}$)|(^[0-9]\d{7}$)/;
    const emailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.(com|cn|net)$$/;
    if (data.mobile || data.email || data.realName) {
      if (!data.realName) {
        // this.msg.error("请输入姓名");
        this.customMsg.open("error", "请输入姓名");
        return false;
      }
      if (!phoneReg.test(data.mobile)) {
        // this.msg.error("手机号格式错误，请重新输入");
        this.customMsg.open("error", "手机号格式错误，请重新输入");
        return false;
      }
      if (!emailReg.test(data.email)) {
        // this.msg.error("邮箱格式错误，请重新输入");
        this.customMsg.open("error", "邮箱格式错误，请重新输入");
        return false;
      }
    } else {
      this.editId = null;
      this.userList.forEach((item, index) => {
        item.isEdit = false;
      });
      return;
    }
    const api = `${this.tenantUrl}/userAccount/user/update`;
    const params = {
      realName: data.realName,
      mobile: data.mobile,
      email: data.email,
      id: data.id,
      username: data.username,
    };

    this.http.post(api, params).subscribe((res: any) => {
      if (res.result.code === 0) {
        this.editId = null;
        // this.loadUserData()
        this.userList.forEach((item, index) => {
          item.isEdit = false;
        });
        this.msg.success("提交成功！");
      }
    });
  }

  loadUserData() {
    this.api.getPermissionUser("").subscribe((res) => {
      if (res.result.code === 0) {
        res.data.forEach((element) => {
          element.isEdit = false;
        });
        this.userList = res.data;
        this.pageTotal = this.userList.length;
        this.changeIndex(1);
      }
    });
  }

  changeIndex(e) {
    this.pageIndex = e;
    let arr = _.chunk(this.userList, this.pageSize);
    this.showUserList = arr[this.pageIndex - 1];
  }

  changePage(e) {
    this.pageSize = e;
    this.pageIndex = 1;
    let arr = _.chunk(this.userList, this.pageSize);
    this.showUserList = arr[this.pageIndex - 1];
  }

  callBackPassword(e) {
    this.hiddenPsd = false;
    if (e !== "cancel") {
      this.validateForm.patchValue({
        password: e.password,
      });
    }
  }

  showPassword() {
    this.hiddenPsd = true;
  }

  submitForm() {
    for (const i in this.validateForm.controls) {
      this.validateForm.controls[i].markAsDirty();
      this.validateForm.controls[i].updateValueAndValidity();
    }
    if (this.validateForm.valid) {
      let commitJson = this.validateForm.value;
      commitJson.id = this.id;
      delete commitJson.password;
      const api = `${this.tenantUrl}/userAccount/user/update`;
      this.http.post(api, commitJson).subscribe((res: any) => {
        if (res.result.code === 0) {
          this.msg.success("提交成功！");
        }
      });
    }
  }

  cancel() {
    console.info("cancel button");
    //this._location.back();
    this.router.navigate(["/home"]);
  }
  openNews() {
    let shows = true;
    sessionStorage.setItem("newsshow", JSON.stringify(shows));
    this.newsshow = "true";
  }
  getPlatforms() {
    const api = `${this.tenantUrl}/survey/sagittarius/listPlatformAnnouncement`;
    this.http.get(api).subscribe((res: any) => {
      this.array = res.data.list;
    });
  }

  getnewInfo() {
    const api = `${this.tenantUrl}/knx/bean/account/getBalance/${this.tenantId}`;
    this.http.get(api).subscribe((res: any) => {
      if (res.result.code == 0) {
        this.validateForm.patchValue({
          balance: res.data + " K米",
        });
      }
    });
  }

  isAdmin() {
    this.api.isAdmin().subscribe((res) => {
      if (res.result.code === 0) {
        if (res.data === true) {
          this.isPermission = true;
          this.loadUserData();
        }
      }
    });
  }
}
