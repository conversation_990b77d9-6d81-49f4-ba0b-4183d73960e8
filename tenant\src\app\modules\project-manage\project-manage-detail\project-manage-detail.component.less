.detail-box {
  width: 1200px;
  margin: 0 auto;
  padding-bottom: 30px;

  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
    margin-bottom: 30px;

    h1 {
      font-size: 24px;
      font-family: PingFangSC-Thin, PingFang SC;
      font-weight: 100;
      color: #17314c;
      line-height: 33px;
      display: flex;
      align-items: center;
      img{
        margin-left: 8px;
      }
    }

    .breadcrumb {
      cursor: pointer;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #495970;
      line-height: 17px;
    }

    .breadcrumb:hover {
      color: #409eff;
    }
  }

  // header end

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    // span{
    //   margin-right: 10px;
    //   font-size: 16px;
    //   font-weight: 400;
    //   color: #AAAAAA;
    //   height: 28px;
    //   line-height: 28px;
    // }
    h2 {
      display: flex;
      align-items: center;
      margin-right: 20px;
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #253238;
      line-height: 28px;

      p {
        margin-right: 10px;
      }

      .process {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 46px;
        height: 21px;
        background: #419eff;
        border-radius: 2px;
        font-size: 12px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 17px;
        margin-right: 4px;
      }

      .process-announced {
        background: #ffa36c;
      }

      .process-waiting_answer {
        background: #ff7575;
      }

      .process-over,
      .process-suspend {
        background: #f5f5f5;
        color: #aaaaaa;
      }

      .process-preview {
        background: #e96fcd;
      }

      .process-answering {
        background: #419eff;
      }
    }

    .icon-box {
      display: flex;
      align-items: center;

      .delete-icon,
      .edit-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        cursor: pointer;
      }

      .delete-icon {
        background: url(../../../../assets/images/event-management/home/<USER>
          no-repeat;
      }

      .delete-icon:hover {
        background: url(../../../../assets/images/event-management/home/<USER>
      }

      .edit-icon {
        background: url(../../../../assets/images/event-management/home/<USER>
          no-repeat;
      }

      .edit-icon:hover {
        background: url(../../../../assets/images/event-management/home/<USER>
      }
    }
  }

  // title end

  .ell {
    display: inline-block;
    overflow: hidden;
    max-width: 140px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  ul.introduction {
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #e2e2e2;
    overflow: hidden;
    margin-bottom: 40px;

    li {
      display: flex;
      box-sizing: border-box;

      div {
        box-sizing: border-box;
      }

      .name-box {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding-left: 16px;
        width: 88px;
        height: 50px;
        background: #f3f3f3;
        // border-radius: 4px 0px 0px 4px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #17314c;
        line-height: 20px;
        overflow: hidden;
      }

      .data-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 512px;
        padding: 0 10px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #17314c;
        line-height: 20px;

        .ell {
          display: inline-block;
          overflow: hidden;
          max-width: 140px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .questionnaires {
          width: calc(100% - 40px);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .type-evaluation {
          // 测评
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #3372ff;
          line-height: 17px;
          width: 40px;
          height: 26px;
          line-height: 26px;
          color: #3372ff;
          text-align: center;
          background: rgba(51, 114, 255, 0.08);
          border-radius: 2px;
        }

        .type-research {
          // 调研
          color: #ad66f1;
          background: rgba(173, 102, 241, 0.08);
        }

        .tips-box {
          margin-left: 17px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #f84444;
          line-height: 20px;
        }

        a {
          font-style: normal;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #409eff;
          line-height: 20px;
        }

        p {
          display: flex;
          align-items: center;
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #17314c;
          line-height: 25px;
        }

        .dots {
          position: relative;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #495970;
          line-height: 17px;
          margin-right: 10px;

          &:after {
            content: "";
            position: absolute;
            left: -10px;
            top: 6px;
            width: 5px;
            height: 5px;
            background: #60cb7f;
            border-radius: 50%;
          }
        }

        // span end
        .dots-blue {
          &:after {
            background: #409eff;
          }
        }

        .dots-red {
          &:after {
            background: #ff7575;
          }
        }
      }

      .dots-box {
        padding-left: 30px;
      }
    }

    // li end
    li:first-child {
      border-bottom: 1px solid #e2e2e2;

      .name-box {
        border-radius: 4px 0px 0px 0px;
      }
    }

    li:last-child {
      .name-box {
        border-radius: 0px 0px 0px 4px;
      }
    }

    li {
      .name-box {
        border-left: 1px solid #e2e2e2;
        border-right: 1px solid #e2e2e2;
      }

      div:first-child {
        border-left: none;
      }
    }
  }

  // ul end

  .tool-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left {
      display: flex;
    }

    .new-event {
      // 邀请
      width: 137px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
      box-shadow: 0px 3px 8px 0px rgba(55, 175, 250, 0.5);
      border-radius: 2px;
      cursor: pointer;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      margin-right: 10px;
    }

    .right {
      display: flex;
      align-items: center;

      li {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #495970;
        line-height: 20px;

        .iconfont {
          font-size: 18px;
          margin-right: 6px;
        }

        .ant-divider {
          background: #cbcbcb;
        }
      }

      .divider {
        margin: 0 10px;
      }

      .btn {
        cursor: pointer;

        .icon-icon_delete:hover,
        .icon-icon-:hover {
          color: #409eff;
          cursor: pointer;
        }
      }

      .btn:hover {
        color: #409eff;
      }
    }
  }

  // table 相关
  .table-change-box {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #495970;
    line-height: 20px;
  }

  .remind {
    // 提醒 样式
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #60cb7f;
    line-height: 20px;
    cursor: pointer;
  }
}

.bubble-card {
  // 提醒 浮层
  li {
    display: flex;
    align-items: center;
    width: 101px;
    height: 36px;
    cursor: pointer;
    color: #495970;

    img {
      width: 15px;
      height: 15px;
      margin: 0 10px;
    }
  }

  li:hover {
    background: #f5faff;
    color: #409eff;
  }
}

footer {
  margin-top: 40px;
  text-align: right;
}

/**
 * 360table css   操作
 */

.action {
  cursor: pointer;
  display: inline-block;

  &:hover {
    border-bottom-width: 1px;
    border-bottom-style: solid;
  }

  &.view-report,
  &.invite {
    @color: #409eff;
    color: @color;

    &:hover {
      border-color: @color;
    }
  }

  &.re-invite {
    @color: #ff7575;
    color: @color;

    &:hover {
      border-color: @color;
    }
  }

  &.alert {
    @color: #60cb7f;
    color: @color;

    &:hover {
      border-color: @color;
    }
  }
}

//.action
/**
* 邀请状态样式
*/
.invite-status {
  width: 58px;
  height: 21px;
  background-color: #c4c4c4;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  color: #ffffff;
}

.invite-status.NEW {
  background-color: #c4c4c4;
}

.invite-status.SENDING {
  background-color: #f6a34a;
}

.invite-status.SENT {
  background-color: #60cb7f;
}

.invite-status.FAIL {
  background-color: #ff7575;
}

// .invite-status-td {
//   width: 16%;
// }
// .first-name-td {
//   width: 16%;
// }
// .date-td {
//   width: 28%;
// }
// .follow-td {
//   width: 14.9%;
// }
// .answer-td {
//   width: 13.7%;
// }

.icon-icon_delete,
.icon-icon_edit {
  margin-right: 35px;
}

.icon-icon- {
  font-size: 20px;
}

.icon-icon_delete:hover,
.icon-icon-:hover {
  color: #f2a080;
  cursor: pointer;
}

.icon-icon_edit:hover {
  color: #409eff;
  cursor: pointer;
}

.mock_div {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 9999;

  .bg_ul {
    width: 100%;
    height: 100%;
    background-color: #000000;
    opacity: 0.6;
  }

  .img_ul_3 {
    position: absolute;
    top: 200px;
    left: 140px;
    color: #fff;
  }

  .img_ul_2 {
    position: absolute;
    top: 300px;
    right: 100px;
    color: #fff;
  }

  .img_ul {
    position: absolute;
    top: 300px;
    left: 200px;
    color: #fff;
  }

  .btn_div {
    display: flex;
    margin-top: 20px;

    > div {
      cursor: pointer;
    }

    .left_d {
      width: 104px;
      line-height: 36px;
      border-radius: 19px;
      text-align: center;
      border: 1px solid #ffffff;
    }

    .right_d {
      margin-left: 20px;
      width: 104px;
      line-height: 36px;
      border-radius: 19px;
      text-align: center;
      background: linear-gradient(90deg, #26d0f1 0%, #409eff 100%);
    }
  }
}

::ng-deep .ant-popover-inner:has(.popconfirm_div_top) {
  min-width: 170px;
  height: 90px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);

  > div {
    height: 100%;

    .ant-popover-inner-content {
      height: 100%;
      padding: 16px 22px 16px 16px;

      .ant-popover-message {
        padding: 0;
        margin-bottom: 8px;

        .icon {
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 16px;
        }

        .popconfirm_div_top {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #262626;
          line-height: 22px;
          padding-left: 24px;
        }

        .popconfirm_div_bottom {
          padding-left: 38px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #595959;
          line-height: 22px;
          margin-top: 12px;
          margin-bottom: 20px;
        }
      }

      .ant-popover-buttons {
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: end;

        .ant-btn {
          width: 56px;
          height: 28px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 8px;
        }
      }
    }
  }
}
::ng-deep {
  .round-right-drawer8 {
    .ant-drawer-body {
      padding: 16px;
      // height: calc(100% - 108px);
      height: calc(100% - 106px);
      overflow: auto;
      // padding-bottom: 66px;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer8-nobody {
    .ant-drawer-body {
      padding: 0;
      // height: calc(100% - 108px);
      height: calc(100% - 106px);
      overflow: auto;
      // padding-bottom: 66px;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-size: 20px;
      font-weight: bold;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
  .round-right-drawer8-nofooter {
    .ant-drawer-body {
      padding: 16px;
      height: calc(100% - 55px);
      overflow-y: auto;
      .vxscrollbar();
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-size: 20px;
      font-weight: bold;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
//滚动条
.vxscrollbar() {
    scrollbar-color: auto;
  scrollbar-width: auto;
  overflow-y: overlay;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    // background-color: transparent;
    background-color: #F1F1F1;
    box-shadow: none;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    // background-color: #e9e9e9;
    background-color: #C1C1C1;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
  }
}
