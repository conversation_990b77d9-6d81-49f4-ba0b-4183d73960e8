import { Component, Input, OnInit } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { NzModalRef, NzMessageService } from "ng-zorro-antd";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";
@Component({
  selector: "app-file-download",
  templateUrl: "./file-download.component.html",
  styleUrls: ["./file-download.component.less"],
})
export class FileDownloadComponent implements OnInit {
  @Input() loading: any;

  @Input() isAdmin: boolean;

  @Input() isTeam: boolean;

  @Input() selectParam: any[];

  inProgressCount: number = 0;

  reportChecked: boolean = false;

  tenantUrl: string = "/tenant-api";

  catList: any[] = [
    {
      name: "数据报表",
      items: [
        {
          id: "dimScore",
          name: "标准报表Excel",
          isSelected: true,
          isHasPermission: this.knxFunctionPermissionService.has(
            "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_DIMENSION_SCORE"
          ),
        },
        {
          id: "answerData",
          name: "原始填答数据",
          isSelected: false,
          needAdmin: true,
          isHasPermission: true,
        },
      ],
    },
    {
      name: "报告",
      items: [
        {
          id: "zh_CN",
          name: "标准报告",
          isSelected: true,
          isHasPermission: this.knxFunctionPermissionService.has(
            "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_REPORT"
          ),
        },
        // { id: 'en_US', name: '英文报告', isSelected: false },
      ],
    },
  ];

  constructor(
    private msg: NzMessageService,
    private modalRef: NzModalRef,
    private http: HttpClient,
    private knxFunctionPermissionService: KnxFunctionPermissionService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    if (this.isTeam) {
      this.catList = [
        {
          name: "数据报表",
          items: [
            {
              id: "dimScore",
              name: "标准报表Excel",
              isSelected: true,
              isHasPermission: this.knxFunctionPermissionService.has(
                "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_DIMENSION_SCORE"
              ),
            },
            {
              id: "answerData",
              name: "原始填答数据",
              isSelected: false,
              needAdmin: true,
              isHasPermission: true,
            },
          ],
        },
        {
          name: "报告",
          items: [
            {
              id: "zh_CN",
              name: "团队报告",
              isSelected: true,
              isHasPermission: this.knxFunctionPermissionService.has(
                "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_REPORT"
              ),
            },
            // { id: 'en_US', name: '英文报告', isSelected: false },
          ],
        },
      ];
    }
  }

  ok() {
    // if()
    const arr: string[] = this.getSelectActions(false);
    // if(arr.length === 0) this.msg.warning('请选择要下载的文件类型')
    if (arr.length === 0)
      this.customMsg.open("warning", "请选择要下载的文件类型");
    this.modalRef.triggerOk();
  }

  getSelectActions(checkUnfinish?: boolean): string[] {
    let rets: string[] = [];
    for (let index = 0; index < this.catList.length; index++) {
      const cat = this.catList[index];
      cat.items.forEach((item) => {
        if (item.isSelected === true) {
          rets.push(item.id);
        }
      });
    }
    const downloadTypesMap = {
      dimScore: "DIMENSION_SCORE",
      answerData: "ORIGINAL_ANSWER",
      zh_CN: "REPORT_FILE",
    };
    const downloadTypes = rets.map((val) => downloadTypesMap[val]);
    // this.reportChecked = _.includes(rets, 'zh_CN');
    this.reportChecked = downloadTypes.length > 0;
    if (checkUnfinish) {
      this.bgGetInProgressCount(downloadTypes);
    }

    return rets;
  }

  bgGetInProgressCount(downloadTypes) {
    if (this.reportChecked) {
      this.inProgressCount = 0;
      const api = `${this.tenantUrl}/sagittarius/report/file/getDownloadFileInfo`;
      let newParam = {
        languages: ["zh_CN"],
        reportPersons: this.selectParam,
        downloadTypes,
      };
      this.http.post(api, newParam).subscribe((res: any) => {
        if (res.result.code === 0) {
          this.inProgressCount = res.data.unfinishedCount;
        }
      });
    } else {
      this.inProgressCount = 0;
    }
  }

  isShowCat(cat) {
    return (
      cat.items.filter(
        (item) => (!item.needAdmin || this.isAdmin) && item.isHasPermission
      ).length > 0
    );
  }
}
