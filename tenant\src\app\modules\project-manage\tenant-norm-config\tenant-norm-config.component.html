<div style="background:#F5F6FA;height: 100%;">
  <div class="container client-width">
    <ul style="display: flex;justify-content: flex-end;padding-top: 20px;">
      <app-break-crumb [Breadcrumbs]="Breadcrumbs"></app-break-crumb>
    </ul>
    <header class="flex report-header space-between">
      <!-- <div class="flex left space-between"> -->
      <p class="index-title">租户常模配置</p>
      <div>
        <ul class="right">
          <nz-input-group [nzPrefix]="suffixIconSearch" class="search">
            <input
              type="text"
              nz-input
              placeholder="请输入关键词"
              [(ngModel)]="keyValue"
              (blur)="init()"
              (keydown.enter)="init()"
            />
          </nz-input-group>
          <ng-template #suffixIconSearch>
            <i nz-icon nzType="search" style="color: #409EFF"></i>
          </ng-template>

          <ng-container
            *ngIf="permission"
          >
            <li class="btn" (click)="refresh()">
              <i class="iconfont icon-icon_refresh"></i><span>刷新</span>
            </li>
          </ng-container>
        </ul>
      </div>
      <!-- </div> -->
    </header>

    <div class="tbody">
      <nz-table
        #basicTable
        [nzData]="listOfData"
        [nzFrontPagination]="false"
        [nzBordered]="true"
        [nzSize]="'middle'"
      >
        <thead>
          <tr>
            <th>常模编码</th>
            <th>常模名称</th>
            <th>英文名称</th>
            <th>年份</th>
            <th>行业</th>
            <th>分位</th>
            <th>企业数量</th>
            <th>调研人次(万)</th>
            <th>题目数</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of basicTable.data">
            <td>{{ data.code }}</td>
            <td>{{ data.name?.zh_CN }}</td>
            <td>{{ data.name?.en_US }}</td>
            <td>{{ data.year }}</td>
            <td>{{ data.industry }}</td>
            <td>{{ data.quantile }}</td>
            <td>{{ data.enterpriseCount }}</td>
            <td>{{ data.personCount }}</td>
            <td>{{ data.questionNum }}</td>
            <td>
              <div class="setting-drawer__body-item">
                <nz-switch nzSize="small" [(ngModel)]="data.isEnable" 
                class="nz-switch"></nz-switch>
              </div>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>

    <footer>
      <button class="btn" (click)="submit()">确认</button>
    </footer>
  </div>
</div>
