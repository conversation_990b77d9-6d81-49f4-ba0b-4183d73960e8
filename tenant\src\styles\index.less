@import "./layout.less";
@import "./layout_header.less";
@import "./layout_sidebar.less";
@import "./svg-icos";

body,
ul,
ol,
dl,
li,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
input {
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: normal;
  font-family: "Microsoft YaHei";
}

img {
  border: none;
}

input,
button,
textarea,
select {
  *font-size: 100%;
  border: none;
}

body {
  background: #fff;
  color: #5e5e5e;
  font: 14px/24px Microsoft YaHei, SimSun, Arial;
}

ul,
ol {
  list-style: none;
  padding: 0;
}

::ng-deep {
  .round-right-drawer {
    .ant-drawer-body {
      padding: 16px;
      // height: calc(100% - 108px);
      height: calc(100% - 106px);
      overflow: auto;
      // padding-bottom: 66px;
      // 滚动条
      scrollbar-color: auto;
      scrollbar-width: auto;
      overflow-y: overlay;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      // 滑块背景
      &::-webkit-scrollbar-track {
        // background-color: transparent;
        background-color: #f1f1f1;
        box-shadow: none;
      }
      // 滑块
      &::-webkit-scrollbar-thumb {
        // background-color: #e9e9e9;
        background-color: #c1c1c1;
        outline: none;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
      }
    }
    .ant-drawer-header {
      padding: 16px;
    }
    .ant-drawer-title {
      font-weight: bold;
      font-size: 20px;
    }
    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
