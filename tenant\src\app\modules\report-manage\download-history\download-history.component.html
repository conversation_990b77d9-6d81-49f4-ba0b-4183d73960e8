<div class="container">

  <div class="header">
    <div class="text">
      团队报告
    </div>
    <div class="input">
      <nz-input-group [nzPrefix]="suffixIconSearch" class="search">
        <input type="text" nz-input placeholder="请输入关键词" [(ngModel)]="searchName" (keyup.enter)="search()" />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <i nz-icon nzType="search" style="color: #409EFF" (click)="search()"></i>
      </ng-template>
    </div>
  </div>

  <div class="body">

    <nz-table #basicTable [nzData]="dataSet" [nzFrontPagination]="false">

      <thead>
        <tr>
          <th class="nowrap">序号</th>
          <th class="nowrap">问卷</th>
          <th class="nowrap">人员列表</th>
          <th class="nowrap">生成时间</th>
          <th class="nowrap">报告类型</th>
          <th class="nowrap">操作</th>
        </tr>
      </thead>

      <tbody>
        <tr *ngFor="let data of basicTable.data; let i = index;">
          <td>{{i+1}}</td>
          <td class="nowrap"> {{data.standardQuestionnaireName}}
            <!-- {{reportStyleMap[data.reportStyle]}} -->
          </td>
          <td>
            <div *ngIf="data.details" class="maxW">
              <ng-container *ngFor="let item of data.details.details; let ind = index;">
                <span *ngIf="ind > 0">
                  ,&nbsp;
                </span>
                <span>
                  {{item.name}}
                </span>
              </ng-container>
            </div>
          </td>
          <td>{{data.createTime | date:'yyyy/MM/dd HH:mm'}}</td>
          <td>
            {{data.reportStyle === "EMPTY" ? '手出报告':'机出报告'}}
          </td>
          <td (click)="tdClick(data.id, data)">
            <div style="display: flex; justify-content: flex-start; align-items: center;">
              <ng-container *ngIf="data.fileId || data.url; else elseTemplate">
                <a *ngIf="data.url" [href]="data.url +'&token='+token" target="_blank">
                  <span style="word-break: keep-all;">查看&nbsp;</span>
                </a>
                <button *ngIf="data.fileId" nz-button nzType="link" (click)="startDownlod(data.fileId, data)" [nzLoading]="data.isDownloading">下载&nbsp;</button>
              </ng-container>
              <ng-template #elseTemplate>
                <span class="inprogress">{{data.reportStyle === 'EMPTY' ? '手出中' : '生成中'}}</span>
                &nbsp;
              </ng-template>
            </div>

            <div style="white-space: nowrap;" *ngIf="permission">
              <nz-upload [nzCustomRequest]="customReq" [nzShowUploadList]="false" >
                <button nz-button nzType="link" [nzLoading]="data.isUploading">
                  <span>上传</span>
                </button>
                &nbsp;
              </nz-upload>

              <button nz-button *ngIf="data.reportStyle !== 'EMPTY'" nzType="link" (click)="redoPdf(data.id, data)"  [nzLoading]="data.isCreating">
                <span>重新生成</span>
              </button>
            </div>

          </td>
        </tr>
      </tbody>

    </nz-table>

  </div>

 <!-- 分页控件 -->
 <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
  <nz-pagination nzShowQuickJumper
      [(nzPageIndex)]="currentPage" 
      [(nzPageSize)]="pageSize" 
      [nzTotal]="totalCount" 
      [nzSize]="'small'" 
      (nzPageIndexChange)="loadData()" >
  </nz-pagination>
</div>

</div>