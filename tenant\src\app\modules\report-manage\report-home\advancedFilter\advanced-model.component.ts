import {
  Component,
  OnInit,
  Input,
  TemplateRef,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChange,
} from "@angular/core";
import { NzModalRef, NzModalService } from "ng-zorro-antd/modal";
import { ReportService } from "../../report.service";
import { UploadFile, UploadXHRArgs, UploadFilter } from "ng-zorro-antd/upload";
import _ from "lodash";
import { NzMessageService } from "ng-zorro-antd/message";
import { Observable, Observer } from "rxjs";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-advanced-model",
  templateUrl: "./advanced-model.component.html",
  styleUrls: ["./advanced-model.component.less"],
})
export class AdvancedModelComponent implements OnInit, OnChanges {
  @Input() father: any;
  @Input() isMin: boolean;
  @Input() type: number; // 1:标准 2:团队 3:对比
  errorFlag: boolean = false;

  FilterBoxVisiable: boolean = false;

  list: any[] = []; // 参数

  tplModal: NzModalRef;
  tplModalButtonLoading: boolean = false;

  uploadName: string = "上传文件";
  // 选项值
  match: string = "MATCH_ALL";

  params: any[] = [];

  advancedFilterObject = {
    // 高级筛选 对象
    // 选项名称
    optionName: [
      {
        value: "projectName",
        name: "活动名称",
        isShow: true,
      },
      {
        value: "questionnaireType",
        name: "产品类型",
        isShow: true,
      },
      // 标准报告
      {
        value: "demographic",
        name: "人口学信息",
        isShow: false,
      },
      {
        value: "name",
        name: "人员姓名",
        isShow: false,
      },
      {
        value: "generalCommentScore",
        name: "总评-得分",
        isShow: false,
      },
      {
        value: "generalCommentRemark",
        name: "总评-评语",
        isShow: false,
      },
      // 团队报告&对比报告
      {
        value: "reportName",
        name: "报告名称",
        isShow: false,
      },
      {
        value: "groupDetail",
        name: "团队明细",
        isShow: false,
      },
      {
        value: "compareDetail",
        name: "对比明细",
        isShow: false,
      },
      // 团队报告&对比报告
      {
        value: "orgOrPerson",
        name: "组织/人员",
        isShow: false,
      },
      {
        value: "createBy",
        name: "创建人",
        isShow: false,
      },
      {
        value: "projectCode",
        name: "活动ID",
        isShow: false,
      },
    ],
    condition: [
      {
        value: "INCLUDE",
        name: "文本包含",
      },
      {
        value: "NOT_INCLUDE",
        name: "文本不包含",
      },
    ],
    product: [],
    // 活动名称
  };

  typeMap = {
    // 标准报告
    1: [
      "projectName",
      "questionnaireType",
      "demographic",
      "name",
      "generalCommentScore",
      "generalCommentRemark",
      "projectCode",
    ],
    // 团队报告
    2: [
      "projectName",
      "questionnaireType",
      "reportName",
      "groupDetail",
      "orgOrPerson",
      "createBy",
      "projectCode",
    ],
    // 对比报告
    3: [
      "projectName",
      "questionnaireType",
      "reportName",
      "compareDetail",
      "orgOrPerson",
      "createBy",
      "projectCode",
    ],
    // 填答进度
    4: ["name"],
  };
  fileType = ".xlsx,.xls";

  constructor(
    private modalService: NzModalService,
    private message: NzMessageService,
    private rptService: ReportService,
    private customMsg: MessageService,
  ) {}

  ngOnInit() {
    if (this.isMin) {
      this.advancedFilterObject.optionName.forEach((val) => {
        if (val.value === "name") {
          val.isShow = true;
        } else {
          val.isShow = false;
        }
      });
    }
    // 根据类型处理筛选内容
    this.advancedFilterObject.optionName.forEach((val) => {
      if (this.typeMap[this.type].includes(val.value)) {
        val.isShow = true;
      } else {
        val.isShow = false;
      }
    });
    this.rptService
      .gettoollist({ questionnaireSceneTypeEnum: "QUESTIONNAIRE_TYPE" })
      .subscribe((res) => {
        this.advancedFilterObject.product = res.data.sceneTypes;
      });
  }

  // 参数变更
  ngOnChanges(changes: { [propName: string]: SimpleChange }) {
    const type = changes.type.currentValue;
    if (this.isMin) {
      this.advancedFilterObject.optionName.forEach((val) => {
        if (val.value === "name") {
          val.isShow = true;
        } else {
          val.isShow = false;
        }
      });
    }
    // 根据类型处理筛选内容
    this.advancedFilterObject.optionName.forEach((val) => {
      if (this.typeMap[this.type].includes(val.value)) {
        val.isShow = true;
      } else {
        val.isShow = false;
      }
    });
  }

  beforeUploadProduct = (
    file: UploadFile // 限制文件类型
  ) =>
    new Observable((observer: Observer<boolean>) => {
      const isType = [""];
      file.name.lastIndexOf(".");
      const fileType = file.name.substring(
        file.name.lastIndexOf("."),
        file.name.length
      ); //从后往前截取文类型
      if (fileType == ".xlsx" || fileType == ".xls") {
        // fileType==".doc"||fileType==".docx"||fileType==".pdf"||fileType==".rar"||
        observer.next(true);
        observer.complete();
      } else {
        // this.message.warning("只支持excel类型!");
        this.customMsg.open("warning", "只支持excel类型");
        observer.complete();
        return;
      }
    });

  addField(e?: MouseEvent): void {
    console.log(e);
    if (e) {
      e.preventDefault();
    }
    this.errorFlag = false;

    this.list.push({
      eventName: null, // 第一选项
      judge: null, // 第二选项
      name: null, // 第三选项
    });
    this.father.clearParams(this.list.length);
  }

  downloadTemplate() {
    // 下载模板
    // type=1 个人报告；type=4 填答进度-填答人
    if ([1, 4].includes(this.type)) {
      // 标准
      this.rptService.downloadTemplate().subscribe((res) => {
        this.downFile(res);
      });
    } else {
      // 团队/对比
      let type = this.type === 2 ? "团队报告" : "对比报告";
      this.rptService.downloadOrgPersonTemplate(type).subscribe((res) => {
        this.downFile(res);
      });
    }
  }

  /**
   * customReq 上传
   * @param item
   */
  customReq = (item: UploadXHRArgs) => {
    const formData = new FormData();
    formData.append("excel", item.file as any);
    let params = {
      fileType: "." + item.file.name.split(".")[1],
    };
    // type=1 个人报告；type=4 填答进度-填答人
    if ([1, 4].includes(this.type)) {
      // 标准
      this.upload(formData, item);
    } else {
      // 团队&对比
      this.uploadGroupOrContrast(formData, item);
    }
  };

  /**
   * uploadExcel 上传配置
   */
  upload(formData, item) {
    return this.rptService.uploadTemplate(formData).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.message.success("文件上传成功");
          if (item.file.name.length > 20) {
            let arr = item.file.name.split(".");
            let str = arr[0]
              .split("")
              .splice(0, 16)
              .join("");
            let arrNew = [];
            arrNew.push(str);
            arrNew.concat(arr[1]).join(".");
            this.uploadName = arrNew.concat(arr[1]).join(".");
          } else {
            this.uploadName = item.file.name;
          }
          this.list.forEach((item) => {
            if (item.eventName === "name") {
              item.name = res.data;
            }
          });
        } else {
          this.message.success("文件上传失败" + res.result.message);
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  /**
   * uploadExcel 上传配置
   */
  uploadGroupOrContrast(formData, item) {
    return this.rptService.uploadOrgPersonTemplate(formData).subscribe(
      (res) => {
        if (res.result.code === 0) {
          this.message.success("文件上传成功");
          if (item.file.name.length > 20) {
            let arr = item.file.name.split(".");
            let str = arr[0]
              .split("")
              .splice(0, 16)
              .join("");
            let arrNew = [];
            arrNew.push(str);
            arrNew.concat(arr[1]).join(".");
            this.uploadName = arrNew.concat(arr[1]).join(".");
          } else {
            this.uploadName = item.file.name;
          }
          this.list.forEach((item) => {
            if (item.eventName === "orgOrPerson") {
              item.name = res.data;
            }
          });
        } else {
          this.message.success("文件上传失败" + res.result.message);
        }
      },
      (err) => {
        item.onError!(err, item.file!);
      }
    );
  }

  downFile(data) {
    const link = document.createElement("a");
    const blob = new Blob([data.body], { type: "application/vnd.ms-excel" });

    let fileName = data.headers
      .get("Content-Disposition")
      .split(";")[1]
      .split("filename=")[1];

    const fileNameUnicode = data.headers
      .get("Content-Disposition")
      .split("filename*=")[1];

    // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
    if (fileName) {
      fileName = decodeURIComponent(fileName);
      // .split('\'\'')[1]
    }
    if (fileNameUnicode) {
      fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
    }
    link.setAttribute("href", URL.createObjectURL(blob));
    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  optionChange(data: any, index: number): void {
    // 标准报告
    if (this.type === 1) {
      if (data === "name") {
        this.list.map((item, idx) => {
          if (item.eventName === "name" && index !== idx) {
            // this.message.create("error", "人员姓名最多只能有一项！");
            this.customMsg.open("error", "人员姓名最多只能有一项！");
            this.list[index] = {
              eventName: null,
              judge: null,
              name: null,
            };
          }
        });
      }
      if (data === "questionnaireType") {
        this.list.map((item, idx) => {
          if (item.eventName === "questionnaireType" && index !== idx) {
            // this.message.create("error", "产品类型最多只能有一项！");
            this.customMsg.open("error", "产品类型最多只能有一项！");
            this.list[index] = {
              eventName: null,
              judge: null,
              name: null,
            };
          }
        });
      }
      if (data !== "questionnaireType" && data !== "name") {
        this.list[index] = {
          eventName: data,
          judge: null,
          name: null,
        };
      }
    } else {
      // 团队报告
      const groupEventNameMap =
        this.type === 2
          ? {
              projectName: "活动名称",
              questionnaireType: "产品类型",
              reportName: "报告名称",
              groupDetail: "团队明细",
              orgOrPerson: "组织/人员",
              createBy: '创建人',
              projectCode: '活动ID'
            }
          : {
              projectName: "活动名称",
              questionnaireType: "产品类型",
              reportName: "报告名称",
              compareDetail: "对比明细",
              orgOrPerson: "组织/人员",
              createBy: '创建人',
              projectCode: '活动ID'
            };
      const groupEventNameArr =
        this.type === 2
          ? [
              "projectName",
              "questionnaireType",
              "reportName",
              "groupDetail",
              "orgOrPerson",
              "createBy",
              "projectCode",
            ]
          : [
              "projectName",
              "questionnaireType",
              "reportName",
              "compareDetail",
              "orgOrPerson",
              "createBy",
              "projectCode",
            ];
      if (groupEventNameArr.includes(data)) {
        this.list.map((item, idx) => {
          if (item.eventName === data && index !== idx) {
            // this.message.create("error", `${groupEventNameMap[data]}最多只能有一项！`);
            this.customMsg.open(
              "error",
              `${groupEventNameMap[data]}最多只能有一项！`
            );
            this.list[index] = {
              eventName: null,
              judge: null,
              name: null,
            };
          } else if (item.eventName === data) {
            this.list[index] = {
              eventName: data,
              judge: null,
              name: null,
            };
          }
        });
      }
    }
  }

  removeField(i, name: string, e: MouseEvent): void {
    // 移除选项
    e.preventDefault();
    console.log(name);
    if (name === "name" || name === "orgOrPerson") this.uploadName = "上传文件";

    this.list.splice(i, 1);
    this.father.clearParams(this.list.length);
  }

  createTplModal(
    tplTitle: TemplateRef<{}>,
    tplContent: TemplateRef<{}>,
    tplFooter: TemplateRef<{}>
  ): void {
    this.tplModal = this.modalService.create({
      nzTitle: tplTitle,
      nzContent: tplContent,
      nzFooter: tplFooter,
    });
  }

  submitForm(): void {
    // 提交表单
    // 表单验证
    this.verifyParams();
    if (this.errorFlag) return;
    this.father.loadData();
    this.FilterBoxVisiable = false;
  }

  verifyParams() {
    // 验证
    if (this.list.length !== 0) {
      for (let item of this.list) {
        if (item.eventName !== "name" && item.eventName !== "orgOrPerson") {
          if (!item.eventName || !item.judge || !item.name) {
            // this.message.warning("选项不能为空");
            this.customMsg.open("warning", "选项不能为空");
            this.errorFlag = true;
            return;
          } else {
            this.errorFlag = false;
          }
        } else {
          if (!item.name) {
            // this.message.warning("请上传文件");
            this.customMsg.open("warning", "请上传文件");
            this.errorFlag = true;
            return;
          } else {
            this.errorFlag = false;
          }
        }
      }
    } else {
      this.errorFlag = false;
    }
  }

  getSonParams() {
    this.params = [];
    // this.verifyParams()
    if (this.errorFlag) return;
    this.list.map((item) => {
      this.params.push({
        condition: item.eventName,
        judge: item.judge,
        value: item.name,
      });
    });
    return this.params;
  }

  getSonMatch() {
    return this.match;
  }

  clear() {
    this.list = [];
    this.uploadName = "上传文件";
    this.errorFlag = false;
    this.father.clearParams(this.list.length);
  }

  // box
  showFilterBox(visiable) {
    if (visiable) {
      this.verifyParams();
      if (this.errorFlag) return;
      this.FilterBoxVisiable = visiable;
    } else {
      this.clear();
      this.FilterBoxVisiable = visiable;
    }
  }

  onChange(value: string, index): void {
    setTimeout(() => {
      this.list[index].name = this.updateValue(value);
    }, 0);
  }

  updateValue(value: string) {
    const reg = /^-?(0|[1-9][0-9]*)(\.[0-9]*)?$/;
    if ((!isNaN(+value) && reg.test(value)) || value === "") {
      return value;
    }
  }
}
