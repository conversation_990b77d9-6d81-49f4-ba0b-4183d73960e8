<ng-container *ngIf="!locwelcomepage && !locengpage">
  <div class="modal">
    <ul class="modal_title">
      <li class="title_left">高级设置</li>
      <li class="title_right">
        <div
          class="linelin_left "
          [ngClass]="Isactive ? 'linear' : ''"
          (click)="getActive('active')"
        >
          活动设置
        </div>
        <div
          class="linelin_right"
          [ngClass]="!Isactive ? 'linear' : ''"
          (click)="getActive('release')"
        >
          发布设置
        </div>
      </li>
    </ul>
    <div *ngIf="Isactive" class="activeclass">
      <ul class="modal_set_1">
        <li class="set_left">
          <div class="act_tit">活动名称(ENG)</div>
          <input
            nz-input
            nzSize="large"
            style="width: 500px;"
            class="pri_name"
            placeholder="请输入"
            [(ngModel)]="settingData.name.en_US"
          />
        </li>
      </ul>
      <ul class="modal_set_1">
        <li class="set_left">
          <div class="act_tit">提示说明</div>
          <div class="flex" style="justify-content: flex-start;">
            <div style="margin-right: 70px;">
              <nz-switch
                [(ngModel)]="settingData.isCheckLicenseNotice"
                class="nz-switch"
              ></nz-switch>
              <span class="padd_span font-color-normal">许可声明</span>
              <a routerLink="/permission-statement" target="_blank">预览</a>
            </div>
            <div style="margin-right: 70px;">
              <nz-switch
                [(ngModel)]="settingData.isEnableWelcomePage"
                class="nz-switch"
              ></nz-switch>
              <span class="padd_span font-color-normal">欢迎页</span>
              <a
                *ngIf="settingData.isEnableWelcomePage"
                (click)="editorwelcome()"
                >编辑</a
              >
            </div>
            <div style="margin-right: 70px;">
              <nz-switch
                [(ngModel)]="settingData.isEnableEndPage"
                class="nz-switch"
              ></nz-switch>
              <span class="padd_span font-color-normal">结束页</span>
              <a *ngIf="settingData.isEnableEndPage" (click)="editorend()"
                >编辑</a
              >
            </div>
          </div>
        </li>
      </ul>

      <div class="upload_warp">
        <div>
          <div class="act_tit">
            企业LOGO
            <label
              class="font-color-normal"
              style="margin-left: 30px;"
              nz-checkbox
              [(ngModel)]="settingData.isShowKnxLogo"
            >
              显示测评调研云logo
            </label>
          </div>
          <nz-upload
            class="avatar-uploader"
            nzListType="picture-card"
            nzName="avatar"
            [nzShowUploadList]="false"
            [nzBeforeUpload]="beforeUpload1"
            [nzCustomRequest]="customReq"
            (nzChange)="handleChange($event)"
          >
            <img *ngIf="avatarUrl" [attr.src]="avatarUrl" class="avatar" />

            <ng-container>
              <div class="loading">
                <img src="./assets/images/upload.png" *ngIf="!avatarUrl" />
                <div *ngIf="!avatarUrl" class="ant-upload-text font-color-blue">
                  上传
                </div>
              </div>
            </ng-container>
          </nz-upload>
          <span
            class="upload-tip font-color-normal"
            style="width: 260px;display: inline-block;margin-right: 170px"
            >建议：800px*800px以上的1:1尺寸，300K以内，PNG格式</span
          >
        </div>
        <div>
          <div class="act_tit">
            填答背景
            <label
              class="font-color-normal"
              style="margin-left: 30px;"
              nz-checkbox
              [(ngModel)]="settingData.isShowBackgroundPic"
            >
              显示填答背景
            </label>
          </div>
          <div style="display: flex;">
            <div>
              <nz-upload
                class="avatar-uploader"
                nzListType="picture-card"
                nzName="avatar"
                [nzShowUploadList]="false"
                [nzBeforeUpload]="beforeUpload"
                [nzCustomRequest]="customReqPc"
                (nzChange)="handleChangePic($event, 'PC')"
              >
                <img
                  *ngIf="PcavatarUrl"
                  [attr.src]="PcavatarUrl"
                  class="avatar"
                />

                <ng-container>
                  <div class="loading">
                    <img
                      src="./assets/images/upload.png"
                      *ngIf="!PcavatarUrl"
                    />
                    <div
                      *ngIf="!PcavatarUrl"
                      class="ant-upload-text  font-color-blue"
                    >
                      上传PC端
                    </div>
                  </div>
                </ng-container>
              </nz-upload>
              <span
                class="upload-tip font-color-normal"
                style="width: 150px;display: inline-block;"
                >建议:1920px*1080px，500K以内，PNG格式</span
              >
            </div>
            <div style="margin-left: 30px;">
              <nz-upload
                class="avatar-uploader"
                nzListType="picture-card"
                nzName="avatar"
                [nzShowUploadList]="false"
                [nzBeforeUpload]="beforeUpload"
                [nzCustomRequest]="customReqMb"
                (nzChange)="handleChangePic($event, 'MB')"
              >
                <img
                  *ngIf="MbavatarUrl"
                  [attr.src]="MbavatarUrl"
                  class="avatar"
                />

                <ng-container>
                  <div class="loading">
                    <img
                      src="./assets/images/upload.png"
                      *ngIf="!MbavatarUrl"
                    />
                    <div
                      *ngIf="!MbavatarUrl"
                      class="ant-upload-text  font-color-blue"
                    >
                      上传移动端
                    </div>
                  </div>
                </ng-container>
              </nz-upload>
              <span
                class="upload-tip font-color-normal"
                style="width: 150px;display: inline-block;"
                >建议:160px*78px，500K以内，PNG格式</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="upload_warp">
        <div *ngIf="isAllowedView">
          <div class="act_tit">
            报告查看权限<span class="padd_span font-color-normal"></span>
          </div>
          <p class="act-txt">
            <span>选择查看方式 : </span>
            <label
              class="font-color-normal"
              nz-checkbox
              [(ngModel)]="settingData.isPublicReport"
              >填答设备</label
            >
            <!-- <label class="font-color-normal second-lable" nz-checkbox [(ngModel)]="settingData.isEmailReport" [nzDisabled]="routProjectType!=='OVER' && routProjectType!=='SUSPEND'">邮件</label>
                        <a *ngIf="settingData.isEmailReport" (click)="sendReport()">发送</a> -->
          </p>
        </div>
      </div>
    </div>

    <div *ngIf="!Isactive" class="release">
      <ul class="flex release_ul">
        <li>
          <div class="act_tit">问卷</div>
          <ul
            style="margin-top: -10px;"
            *ngIf="!isNewQuestionBook(); else elseTemplate"
          >
            <li>
              <span>每页题数:</span>
              <nz-input-number
                [nzDisabled]="disabledPagesAndSort"
                class="input_num"
                [(ngModel)]="settingData.questionNumInOnePage"
                [nzPrecision]="precision"
                nzPlaceHolder="题数"
              >
              </nz-input-number>
            </li>
          </ul>
          <div class="mb-16" #elseTemplate>
            <nz-radio-group [(ngModel)]="settingData.isQuestionCustomSort">
              <label nz-radio [nzValue]="false">每页题数</label>
              <nz-input-number
                class="mr-24"
                [(ngModel)]="settingData.questionNumInOnePage"
                [nzPrecision]="precision"
                nzPlaceHolder="题数"
                [nzDisabled]="settingData.isQuestionCustomSort"
              >
              </nz-input-number>
              <label
                nz-radio
                *ngIf="
                  reportType.indexOf('INVESTIGATION_RESEARCH_CUSTOM') !== -1
                "
                [nzValue]="true"
                >自定义</label
              >
            </nz-radio-group>
          </div>
        </li>
        <li>
          <div class="act_tit">排序</div>
          <ul>
            <nz-radio-group
              [nzDisabled]="disabledPagesAndSort"
              [(ngModel)]="settingData.sequence"
              (ngModelChange)="funsequence($event)"
              [nzDisabled]="settingData.isQuestionCustomSort"
              class="flex_dir"
            >
              <label nz-radio nzValue="QUESTION_TYPE">按题目类型排序</label>
              <label nz-radio nzValue="RANDOM" style="margin-top: 15px;">
                打乱排序
              </label>
            </nz-radio-group>
          </ul>
        </li>
      </ul>
      <ul class="flex release_ul" *ngIf="projecttype != '360'">
        <li>
          <div class="act_tit">语言</div>
          <ul style="display: flex;align-items: center;">
            <span>填答时可选语言：</span>
            <nz-checkbox-wrapper
              style="flex: 1;"
              (nzOnChange)="optionalLan($event)"
            >
              <div nz-row>
                <div
                  nz-col
                  nzSpan="6"
                  *ngFor="let item of projectLangOptions; let i = index"
                >
                  <label
                    nz-checkbox
                    [nzValue]="item.value"
                    [(ngModel)]="item.checked"
                    >{{ item.name }}</label
                  >
                </div>
              </div>
            </nz-checkbox-wrapper>
          </ul>
          <ul style="display: flex;align-items: center;margin-top: 20px;">
            <span>默认填答语言：</span>
            <nz-radio-group
              [(ngModel)]="settingData.language"
              (ngModelChange)="funsequence($event)"
              class="flex_dir_1"
            >
              <label
                *ngFor="let item of projectLangOptions; let i = index"
                nz-radio
                [nzValue]="item.value"
                >{{ item.name }}</label
              >
            </nz-radio-group>
          </ul>
        </li>
        <ng-container
          *ngIf="
            reportType == 'BLANK_CUSTOM' ||
              reportTypeList.includes('BLANK_CUSTOM');
            else right
          "
        >
          <!-- 自定义问卷-关联任务 -->
          <li
            *ngIf="
              permissionService.isPermissionOrSag(
                'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
              ) ||
              permissionService.isPermissionOrSag(
                'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:SCALE_EXTENSION'
              )
            "
          >
            <div class="act_tit">关联任务</div>
            <ul>
              <li
                *ngIf="
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
                  )
                "
              >
                <nz-switch
                  nzSize="small"
                  [(ngModel)]="isEnableQuestionBookDistribute"
                  class="nz-switch"
                  (ngModelChange)="
                    changeEnable($event, 'isEnableQuestionBookDistribute')
                  "
                ></nz-switch>
                <span style="margin-left: 8px;">题本分发任务</span>
              </li>
              <li
                style="margin-top: 15px;"
                *ngIf="
                  permissionService.isPermissionOrSag(
                    'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:SCALE_EXTENSION'
                  )
                "
              >
                <nz-switch
                  nzSize="small"
                  [(ngModel)]="isEnableScaleExtend"
                  class="nz-switch"
                  (ngModelChange)="changeEnable($event, 'isEnableScaleExtend')"
                ></nz-switch>
                <span style="margin-left: 8px;">量表尺度拓展</span>
              </li>
            </ul>
          </li>
        </ng-container>
        <ng-template #right>
          <li></li>
        </ng-template>
      </ul>
      <ul class="flex release_ul" *ngIf="projecttype == '360'">
        <li>
          <div class="act_tit">计算</div>
          <ul style="display: flex;align-items: center;">
            <span>被评估人(评分对象)：</span>
            <nz-radio-group
              [(ngModel)]="settingData.isCustomRoleWeight"
              (ngModelChange)="funsequence($event)"
              class="flex_dir_1"
            >
              <label nz-radio [nzValue]="false">角色权重一致</label>
              <label nz-radio [nzValue]="true">自定义分配角色权重</label>
            </nz-radio-group>
          </ul>
          <div style="color: #f00000;">
            *(切换该选项后，角色权重将恢复默认值，请谨慎选择！)
          </div>
        </li>
        <li>
          <div class="act_tit">语言</div>
          <ul style="display: flex;align-items: center;">
            <span>填答时可选语言：</span>
            <nz-checkbox-wrapper
              style="flex: 1;"
              (nzOnChange)="optionalLan($event)"
            >
              <div nz-row>
                <div
                  nz-col
                  nzSpan="6"
                  *ngFor="let item of projectLangOptions; let i = index"
                >
                  <label
                    nz-checkbox
                    [nzValue]="item.value"
                    [(ngModel)]="item.checked"
                    >{{ item.name }}</label
                  >
                </div>
              </div>
            </nz-checkbox-wrapper>
          </ul>
          <ul style="display: flex;align-items: center;margin-top: 20px;">
            <span>默认填答语言：</span>
            <nz-radio-group
              [(ngModel)]="settingData.language"
              (ngModelChange)="funsequence($event)"
              class="flex_dir_1"
            >
              <label
                *ngFor="let item of projectLangOptions; let i = index"
                nz-radio
                [nzValue]="item.value"
                >{{ item.name }}</label
              >
            </nz-radio-group>
          </ul>
        </li>
      </ul>
      <ul class="flex release_ul" *ngIf="projecttype == '360'">
        <li>
          <div class="act_tit">评价关系</div>
          <ul style="display: flex;align-items: center;">
            <span style="margin-right: 8px;">被评估人(评分对象)自行邀请</span>
            <nz-switch
              [(ngModel)]="isInviteAnswer"
              (ngModelChange)="changeIsVisitAnswer($event)"
              nzCheckedChildren="启用"
              nzUnCheckedChildren="禁用"
            ></nz-switch>
          </ul>
          <!-- todo: 角色数量设置去除#7336 -->
          <!-- <div style="margin-top: 16px;" *ngIf="!!isInviteAnswer">邀请人数：
                        <nz-input-number [(ngModel)]="min" (ngModelChange)="changeMin($event)" [nzDisabled]="disableLimit" [nzMin]="1" [nzStep]="1">
                        </nz-input-number> -
                        <nz-input-number [(ngModel)]="max" (ngModelChange)="changeMax($event)" [nzDisabled]="disableLimit" [nzStep]="1"></nz-input-number><span style="margin-left: 8px;"><label nz-checkbox [(ngModel)]="disableLimit"
                                (ngModelChange)="changeDisableLimit($event)">不做限制</label></span></div> -->
          <ul style="display: flex;align-items: center;margin-top: 16px;">
            <span style="margin-right: 8px;">审核被评估人(评分对象)邀请</span>
            <nz-switch
              [nzDisabled]="!isInviteAnswer"
              [(ngModel)]="isInviteReviewEvaluatee"
              (ngModelChange)="changeIsInviteReviewEvaluatee($event)"
              nzCheckedChildren="启用"
              nzUnCheckedChildren="禁用"
            ></nz-switch>
          </ul>
        </li>
        <li>
          <div class="act_tit">有效性</div>
          <ul style="margin-top: -10px;">
            <li>
              <span>填答有效时间≥ </span>
              <nz-input-number
                class="input_num"
                [(ngModel)]="mm"
                [nzMin]="0"
                [nzPrecision]="precision_min"
                nzPrecisionMode="cut"
                (nzBlur)="getdefaultTime()"
                (ngModelChange)="mChange($event)"
                nzPlaceHolder="分"
              ></nz-input-number
              ><span class="unit">分</span>
              <nz-input-number
                class="input_num"
                style="margin-left: 4px;"
                [(ngModel)]="ss"
                [nzMin]="0"
                [nzPrecision]="precision_min"
                nzPrecisionMode="cut"
                (nzBlur)="getdefaultTime()"
                (ngModelChange)="sChange($event)"
                nzPlaceHolder="秒"
              ></nz-input-number
              ><span class="unit">秒</span>
            </li>
            <li style="margin-top: 10px;">
              <span>填答完成率≥ </span>
              <nz-input-number
                class="input_num"
                [(ngModel)]="settingData.answerEffectiveRange"
                [nzMin]="1"
                [nzMax]="100"
                [nzStep]="1"
                [nzPrecision]="precision"
                nzPrecisionMode="cut"
                nzPlaceHolder="百分比"
              ></nz-input-number
              ><span class="unit">%</span>
            </li>
            <li style="margin-top: 10px;">
              <span>填答一致性≤</span>
              <nz-input-number
                class="input_num"
                [(ngModel)]="settingData.answerSameRate"
                [nzMin]="1"
                [nzMax]="100"
                [nzStep]="1"
                [nzPrecision]="precision"
                nzPrecisionMode="cut"
                nzPlaceHolder="百分比"
              ></nz-input-number
              ><span class="unit">%</span>
            </li>
          </ul>
        </li>
      </ul>
      <!-- todo: 360测评分发管理（角色维度管理）调整为所有360系列通用功能 #6120 -->
      <!-- <ul class="flex release_ul" *ngIf="projecttype == '360' && permission "> -->
      <ul
        class="flex release_ul"
        *ngIf="
          projecttype == '360' &&
          permissionService.isPermissionOrSag(
            'SAG:TENANT:PROJECT_MGT:CREATE_SELECT:DISTRIBUTE'
          )
        "
      >
        <li>
          <div class="act_tit">分发管理</div>
          <ul style="display: flex;align-items: center;">
            <span style="margin-right: 8px;">题本分发任务</span>
            <nz-switch
              [nzDisabled]="isUpdateing"
              [(ngModel)]="settingData.isEnableRoleDimension"
              nzCheckedChildren="启用"
              nzUnCheckedChildren="禁用"
            ></nz-switch>
          </ul>
        </li>
      </ul>
    </div>
    <!-- 测评 高级设置 保存设置 -->
    <ul
      class="modal_footer"
      *nzModalFooter
      [hidden]="locwelcomepage || locengpage"
    >
      <li class="footer_left" (click)="getSaveSet()">
        保存设置
      </li>
      <li class="footer_right" (click)="getDefault()">
        恢复默认
      </li>
    </ul>
  </div>
</ng-container>

<ng-container *ngIf="locwelcomepage || locengpage">
  <div style="padding-top: 30px;min-height: 321px;">
    <ul
      style="display: flex;align-items: center;justify-content: space-between;"
    >
      <li>
        <span style="font-size: 24px;color: #A4AEB9;">高级设置/</span
        ><span style="font-size: 18px;">{{ pagename }}</span>
      </li>
      <li class="title_right_1">
        <div
          class="linelin_left "
          [ngClass]="Isactivelan ? 'linear' : ''"
          (click)="getActivenew('zh_CN')"
        >
          中文
        </div>
        <div
          class="linelin_right"
          [ngClass]="!Isactivelan ? 'linear' : ''"
          (click)="getActivenew('en_US')"
        >
          ENG
        </div>
      </li>
    </ul>
    <div style="margin: 30px 0;" *ngIf="locengpage">
      <tinymce
        [config]="tinyconfig"
        [hidden]="!Isactivelan"
        [(ngModel)]="settingData.endPage.zh_CN"
        delay="10"
      >
      </tinymce>
      <tinymce
        [config]="tinyconfig"
        [hidden]="Isactivelan"
        [(ngModel)]="settingData.endPage.en_US"
        delay="10"
      >
      </tinymce>
    </div>
    <div style="margin: 30px 0;" *ngIf="locwelcomepage">
      <tinymce
        [config]="tinyconfig"
        [hidden]="!Isactivelan"
        [(ngModel)]="settingData.welcomePage.zh_CN"
        delay="10"
      >
      </tinymce>
      <tinymce
        [config]="tinyconfig"
        [hidden]="Isactivelan"
        [(ngModel)]="settingData.welcomePage.en_US"
        delay="10"
      >
      </tinymce>
    </div>
    <ul class="modal_footer">
      <li class="footer_left" (click)="wlcomeSaveSet()">
        确认
      </li>
      <li class="footer_right" (click)="wlcomeDefault()">
        返回上一步
      </li>
    </ul>
  </div>
</ng-container>
