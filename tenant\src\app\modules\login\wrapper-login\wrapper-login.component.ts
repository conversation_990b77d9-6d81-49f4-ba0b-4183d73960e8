import { Component, OnInit } from "@angular/core";
import { LoginComponent } from "../login/login.component";
import { NzMessageService, NzModalService, NzModalRef } from "ng-zorro-antd";
import { Router } from "@angular/router";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { SettingsService, _HttpClient } from "@knz/theme";
import { OnDestroy, Inject, Optional } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import {
  SocialService,
  SocialOpenType,
  ITokenService,
  DA_SERVICE_TOKEN,
} from "@knz/auth";
import { ReuseTabService } from "@knz/assembly";
import { environment } from "@env/environment";
import { StartupService } from "@core";
import { Subscription } from "rxjs/Subscription";
import { LoginService } from "@src/modules/login/login.service";
import { SharedModule } from "@shared";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-wrapper-login",
  templateUrl: "./wrapper-login.component.html",
  styleUrls: ["./wrapper-login.component.less"],
})
export class WrapperLoginComponent implements OnInit {
  constructor(
    private msg: NzMessageService,
    private modalService: NzModalService,
    private router: Router,
    @Optional()
    @Inject(ReuseTabService)
    private reuseTabService: ReuseTabService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private startupSrv: StartupService,
    public http: HttpClient,
    private loginService: LoginService,
    private sharedModule: SharedModule,
    private knxFunctionPermissionService: KnxFunctionPermissionService,
    private customMsg: MessageService
  ) {}
  subscription: Subscription;
  tourist: boolean = false;
  isNeedLogin: boolean = false;
  isVisible = true;
  isLoadingOne = false; // 提交的按钮
  tenantUrl = "/tenant-api";
  ngOnInit(): void {}

  onCancel(e) {
    this.isVisible = false;
    this.router.navigateByUrl("/");
  }

  login(
    loginModel: any
    // modalRef: NzModalRef
  ) {
    let params = {
      loginName: loginModel.tenantShortName,
      username: loginModel.username,
      password: loginModel.password,
      productCode: "SAG",
    };

    const defaultPermissions = [
      "SAG:TENANT:HOME",
      "SAG:TENANT:PROJECT_MGT:CREATE_SELECT",
      "SAG:TENANT:PROJECT_MGT:LIST",
      "SAG:TENANT:PROJECT_MGT:DETAIL",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:COMPARE",
      "SAG:TENANT:USER_CENTER",
      "SAG:TENANT:DOC_MGT",
      "SAG:TENANT:PROJECT_MGT:CREATE_SELECT_CONFIRM",
      "SAG:TENANT:PROJECT_MGT:LIST:UPDATE",
      "SAG:TENANT:PROJECT_MGT:LIST:COPY_PROJECT",
      "SAG:TENANT:PROJECT_MGT:LIST:HIDE",
      "SAG:TENANT:PROJECT_MGT:LIST:INVITE",
      "SAG:TENANT:PROJECT_MGT:DETAIL:EDIT",
      "SAG:TENANT:PROJECT_MGT:DETAIL:DELETE",
      "SAG:TENANT:PROJECT_MGT:DETAIL:INVITE_ANSWER",
      "SAG:TENANT:PROJECT_MGT:DETAIL:EXPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:CREATE_GROUP_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:CREATE_COMPARE_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:DOWNLOAD_DIMENSION_SCORE",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:VIDEO",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:EXPERT_INTERPRETATION",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_DIMENSION_SCORE",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:DOWNLOAD_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:HIDE",
      "SAG:TENANT:PROJECT_MGT:DETAIL:DELAY",
      "SAG:TENANT:PROJECT_MGT:DETAIL:STOP",
      "SAG:TENANT:PROJECT_MGT:DETAIL:ANSWER_RATE",
      "SAG:TENANT:PROJECT_MGT:DETAIL:BATCH_EXPORT_ANSWER_RATE",
      "SAG:TENANT:MSG_SEND_LOG",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:STANDARD:BATCH_EMAIL_SEND",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:GROUP_REPORT:BATCH_EMAIL_SEND",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:ONLINE_REPORT",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:ONLINE_REPORT:CREATE",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:CREATE_GROUP_REPORT_CN",
      "SAG:TENANT:REPORT_MGT:REPORT_LIST:CREATE_GROUP_REPORT_EN",
    ];

    // http://10.55.10.36:8090
    //http://10.55.10.36:8000
    //http://10.55.8.170:8090
    //http://10.55.10.40:8090
    this.http
      .post(`${this.tenantUrl}/auth/login?_allow_anonymous=true`, params)
      .subscribe((res: any) => {
        localStorage.removeItem(params.productCode + "_permissioncode");
        this.knxFunctionPermissionService.clearFunctionPermissionCodes();
        if (res.result.code != 0) {
          // this.msg.error(res.result.message);
          return;
        } else {
          // 使用新token
          res.data.token = res.data.newToken;
          res.data.time = new Date();
          localStorage.setItem("token", JSON.stringify(res.data));

          // modalRef.close();
          this.isVisible = false;
        }

        // 清空路由复用信息
        this.reuseTabService.clear();
        // 设置用户Token信息
        this.tokenService.set(res.data);

        // 重新获取 StartupService 内容，我们始终认为应用信息一般都会受当前用户授权范围而影响
        // this.startupSrv.load().then(() => {
        //   let url = this.tokenService.referrer!.url || '/';
        //   if (url.includes('/passport')) {
        //     url = '/';
        //   }
        //   this.router.navigateByUrl(url);
        // });
        // this.loginService.login2();
        this.loginLog({});
        this.loginService
          .getPermisionCode(params.productCode)
          .subscribe((res2) => {
            if (res2.result.code === 0) {
              if (!res2.data || res2.data.length === 0) {
                localStorage.setItem(
                  params.productCode + "_permissioncode",
                  JSON.stringify(defaultPermissions)
                );
              } else {
                localStorage.setItem(
                  params.productCode + "_permissioncode",
                  JSON.stringify(res2.data)
                );
              }
            } else {
              // this.msg.error(res2.result.message);
              this.customMsg.open("error", res2.result.message);
            }

            this.loginService.login().subscribe((res3) => {
              if (res.result.code === 0) {
                this.loginService.emitUser(res3);
              } else {
                // this.msg.error(res3.result.message);
                this.customMsg.open("error", res3.result.message);
              }
            });
            this.router.navigateByUrl("/home");
          });
      });
  }

  loginLog(loginModel: any) {
    let params = {};
    this.http
      .post(`${this.tenantUrl}/survey/standard/tenant/action/log/add`, params)
      .subscribe((res: any) => {});
  }
}
