import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  forwardRef,
} from "@angular/core";
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from "@angular/forms";
import { NzMessageService } from "ng-zorro-antd";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-algorithm-select",
  templateUrl: "./algorithm-select.component.html",
  styleUrls: ["./algorithm-select.component.less"],
})
export class AlgorithmSelectComponent implements OnInit {
  @Input() algorithmType: any; //value是提交到后台的数据
  @Input() algorithmData: any; //value是提交到后台的数据
  @Input() disabled: boolean; //value是提交到后台的数据
  @Output() onCheck = new EventEmitter<any>();

  onChange: any = () => {};
  onTouched: any = () => {};

  constructor(
    private message: NzMessageService,
    private customMsg: MessageService,
        public permissionService: PermissionService,
  ) {}

  isEnablePassGrade: boolean; // 是否按设置各维度总和的及格分
  isUseAverage: boolean; // 是否按设置维度的总分的平均计算
  passGrade: number; // 及格分
  assessmentNormstext: string; // 常模
  permission:boolean;

  visible = false;
  selected = "";
  optionsMap = {
    CUSTOM_NORM: "常模",
    CUSTOM_AVERAGE: "平均分",
    HIT_RATE: "击中率",
  };
  ngOnInit() {}
  ngOnChanges() {
    this.permission = this.permissionService.isPermission();
    const {
      isEnablePassGrade = false,
      isUseAverage = false,
      passGrade = null,
      assessmentNorms = [],
    } = this.algorithmData;
    this.selected = this.algorithmType;
    this.isEnablePassGrade = isEnablePassGrade;
    this.isUseAverage = isUseAverage;
    this.passGrade = passGrade;
    this.assessmentNormstext =
      assessmentNorms.length > 0
        ? assessmentNorms.map((val) => `${val.count}:${val.score}`).join("\n")
        : "";
  }

  handClick(val) {
    this.selected = val;
    if (val == "HIT_RATE") {
      this.visible = false;
      this.onCheck.emit({ algorithmType: val, algorithmData: {} });
    }
  }
  onCancel() {
    this.visible = false;
    this.isUseAverage = false;
    this.passGrade = null;
    this.assessmentNormstext = "";
    this.selected = "";
  }
  onConfirm() {
    if (this.selected == "CUSTOM_NORM") {
      const assessmentNorms = this.textareaToModel(this.assessmentNormstext);
      const isDecimal = assessmentNorms.filter(
        (val) => String(val.count).indexOf(".") > -1
      );
      if (isDecimal.length > 0) {
        return;
      }
      if (assessmentNorms.length == 0) {
        // this.message.error("请输入常模");
        this.customMsg.open("error", "请输入常模");
        return;
      }
      this.onCheck.emit({
        algorithmType: this.selected,
        algorithmData: { assessmentNorms },
      });
    } else if (this.selected == "CUSTOM_AVERAGE") {
      if (this.isEnablePassGrade == false && this.isUseAverage == false) {
        // this.message.error("请至少勾选一项");
        this.customMsg.open("error", "请至少勾选一项");
        return;
      }
      if (this.isEnablePassGrade && !this.passGrade) {
        // this.message.error("请设置及格分");
        this.customMsg.open("error", "请设置及格分");
        return;
      }
      const obj = {
        isUseAverage: this.isUseAverage,
        isEnablePassGrade: this.isEnablePassGrade,
        passGrade: this.passGrade,
      };
      this.onCheck.emit({ algorithmType: this.selected, algorithmData: obj });
    } else {
      this.onCheck.emit({});
    }
    this.visible = false;
    this.isEnablePassGrade = false;
    this.isUseAverage = false;
    this.passGrade = null;
    this.assessmentNormstext = "";
  }
  checkboxChage(e) {
    if (this.isEnablePassGrade == false) {
      this.passGrade = null;
    }
  }

  private textareaToModel(norm): any[] {
    let objs: any[] = [];
    let arr1: string[] = norm.split(/[(\r\n)\r\n;]+/);
    for (let index = 0; index < arr1.length; index++) {
      const element: string = arr1[index];
      if (element.trim() != "") {
        if (element.indexOf(":") == -1) {
          let msg = `常模的第${index + 1}行数据输入有误,必须含英文分号。`;
          // this.message.error(msg);
          this.customMsg.open("error", msg);
        }
        let arr2: string[] = element.split(":");
        let para1: any = arr2[0].trim();
        let para2: any = arr2[1].trim();
        const valid1 = isNaN(para1);
        const valid2 = isNaN(para2);
        if (valid1) {
          let msg = `常模的第${index + 1}行数据输入有误,  ${para1}  不是数字。`;
          // this.message.error(msg);
          this.customMsg.open("error", msg);
        }
        if (para1.indexOf(".") > -1) {
          let msg = `常模的第${index +
            1}行数据输入有误,  ${para1}  不能为小数。`;
          // this.message.error(msg);
          this.customMsg.open("error", msg);
        }
        if (valid2) {
          let msg = `常模的第${index + 1}行数据输入有误,  ${para2}  不是数字。`;
          // this.message.error(msg);
          this.customMsg.open("error", msg);
        }
        objs.push({ count: para1, score: para2 });
      }
    }
    return objs;
  }
}
