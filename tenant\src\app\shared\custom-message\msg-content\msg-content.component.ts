import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { MessageService } from '../message-service.service';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-msg-content',
  templateUrl: './msg-content.component.html',
  styleUrls: ['./msg-content.component.less']
})
export class MsgContentComponent implements OnInit {

  constructor(
    public message: NzMessageService,
    public msgService: MessageService
  ) { }

  ngOnInit() {}

  close() {
    this.message.remove(this.msgService.msgId);
    this.msgService.msgId = '';
  }

}
