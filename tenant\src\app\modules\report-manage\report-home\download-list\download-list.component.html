<div
  (click)="showDownloadProgress()"
  style="cursor: pointer;position: relative;"
  *ngIf="hasDownLoadPermission()"
>
  <div class="redtip" *ngIf="showred != 0"></div>
  <img class="tipIcon" src="assets/images/report-home/download.png" alt="" />
</div>
<ng-template #tempTitle>
  <div class="temp-title">
    <span style="margin-right: 20px;">下载列表</span>
    <div class="put_div">
      <nz-input-group [nzPrefix]="suffixIconSearch" class="search-box">
        <input
          style="border: 1px solid #E4E4E4; background-color: #F8F8F8; border: none;"
          type="text"
          nz-input
          placeholder="请输入关键词"
          [(ngModel)]="name"
          nzSize="small"
          (keydown.enter)="search()"
        />
      </nz-input-group>
    </div>
    <ng-template #suffixIconSearch>
      <img src="assets/images/icon_search.png" />
    </ng-template>
  </div>
</ng-template>

<nz-drawer
  [nzClosable]="true"
  [nzVisible]="visible"
  nzPlacement="right"
  [nzTitle]="tempTitle"
  [nzWidth]="450"
  (nzOnClose)="close()"
  nzWrapClassName="downloadLsit-right-drawer"
>
  <div class="scroll">
    <ul>
      <li *ngFor="let item of list">
        <div class="top">
          <div>
            <span class="type" [ngClass]="item.reportType.toLowerCase()">{{
              typeObj[item.reportType]
            }}</span>
            <p
              class="name"
              nz-tooltip
              [nzTooltipTitle]="item.name"
              style="color: #17314C;"
            >
              {{ item.name }}
            </p>
          </div>
          <span>({{ typeName[item.reportName] }})</span>
        </div>
        <div style="margin-bottom: 8px;">
          <nz-progress
            [nzStrokeWidth]="4"
            *ngIf="item.progressBar !== 100"
            [nzPercent]="item.progressBar"
            [nzStatus]="
              item.progressBar === 0 || item.progressBar === 100 ? '' : 'active'
            "
          ></nz-progress>
        </div>
        <footer>
          <div>
            <span style="margin-right: 10px;"
              >创建时间：{{ item.createTime | date: "yyyy/MM/dd" }}</span
            >
            <span *ngIf="status === 'INIT'"
              >预计完成还需要：{{
                item.estimateRemainingTime | date: "HH:mm:ss"
              }}</span
            >
          </div>
          <a *ngIf="item.progressBar === 100" (click)="downloadNew(item)">
            <i class="iconfont icon-icon_import" style="margin-right: 4px;"></i>
            <span>下载</span></a
          >
        </footer>
      </li>
    </ul>
  </div>
  <div class="bottom" style="text-align: center;">
    <a
      nz-popconfirm
      nzPopconfirmTitle="是否清除下载历史？"
      nzPopconfirmPlacement="bottom"
      (nzOnConfirm)="clear()"
      (nzOnCancel)="cancel()"
    >
      <i class="iconfont icon-icon_delete"></i> 清除下载历史
    </a>
    <!-- <nz-pagination nzSize="small" [nzPageIndex]="page.current" [nzTotal]="page.total" (nzPageIndexChange)="changePageIndex($event)"></nz-pagination> -->
  </div>
</nz-drawer>
