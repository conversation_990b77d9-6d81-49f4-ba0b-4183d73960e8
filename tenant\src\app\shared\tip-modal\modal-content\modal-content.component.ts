import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'app-modal-content',
  templateUrl: './modal-content.component.html',
  styleUrls: ['./modal-content.component.less']
})
export class ModalContentComponent implements OnInit {

  @Input() father: any;
  @Input() submitData: any;
  @Input() type?: string;

  constructor() { }

  cancel() {
    this.father.confirmModal.destroy()
    this.father.hideProcess()
  }

  retain() {
    this.submitData.isClearPersonAnswerData = false
    this.submitData.status = "ANSWERING"
    if(!this.type) {
      this.father.isSpinning = true
      this.father.isNzRELoading = true
      this.father.isNzOkLoading = true
      this.father.isNzPreLoading = true
      this.father.ActiveUpdataship(this.submitData, 'RelasePage', '/project-manage/home')
    } else {
      this.father.isNzRELoading = true;
      this.father.ActiveUpdata(this.submitData)//修改活动
    }
  }

  ok() {
    this.submitData.isClearPersonAnswerData = true
    this.submitData.status = "ANSWERING"
    if(!this.type) {
      this.father.isSpinning = true
      this.father.isNzRELoading = true
      this.father.isNzOkLoading = true
      this.father.isNzPreLoading = true
      this.father.ActiveUpdataship(this.submitData, 'RelasePage', '/project-manage/home')
    } else {
      this.father.isNzRELoading = true;
      this.father.ActiveUpdata(this.submitData)//修改活动
    }
  }

  ngOnInit() {
  }

}
