* {
  margin: 0;
  padding: 0;
}

img {
  display: inline-block;
}

.container {
  // 容器
  margin: 0px auto;

  .headContent {
    padding-top: 8px;

    h1 {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #262626;
      line-height: 28px;
      margin-top: 16px;
    }
  }

  .mainContent {
    padding: 16px 0;

    .header {
      align-items: center;
      margin-bottom: 20px;

      .search {
        width: 260px;
        height: 36px;
        background: #FFFFFF;
        border-radius: 8px;

        .ant-input {
          border: 1px solid #C4C4C4;
          padding: 0 30px 0 10px;

          &:hover {
            border-color: #409EFF;
          }
        }

        ::ng-deep .ant-input-suffix {
          right: 10px;

          i {
            font-size: 20px;
            color: #BFBFBF;
          }
        }
      }

      ::ng-deep .operate {
        align-items: center;

        >div,
        .ant-upload {
          display: flex;
          cursor: pointer;
          align-items: center;
          margin-left: 24px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #409EFF;
          line-height: 20px;

          i {
            font-size: 20px;
            margin-right: 3px;
          }
        }

        .ant-upload {
          margin-left: 0;
        }
      }
    }

    .tabList {
      .tablist_header {
        width: 100%;
        height: 40px;
        box-sizing: border-box;
        border-radius: 6px;
        border-bottom: 1px solid #E6E6E6;
        padding: 0 16px;
        margin-bottom: 16px;
        overflow-x: auto;
        overflow-y: hidden;

        .list_box {
          height: 100%;
          white-space: nowrap;

          span {
            position: relative;
            margin-right: 40px;
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #595959;
            line-height: 22px;
            cursor: pointer;

            &.active {
              color: #409EFF;

              &::after {
                content: "";
                position: absolute;
                width: 100%;
                height: 4px;
                background: #409EFF;
                border-radius: 2px;
                left: 0;
                bottom: -12px;
              }
            }
          }
        }
      }

      .headerTr,
      .itemTr {

        th,
        td {
          // padding: 12px 14px 12px 16px;
          padding: 4px 2px 4px 8px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #262626;
          line-height: 20px;
          max-width: 100%;
          word-break: break-all;

          .ng-star-inserted {
            display: block;
            padding: 8px 12px 8px 8px;
          }

          .icon-edit_ic {
            font-size: 20px;
            line-height: 20px;
            color: #409EFF;
            margin-left: 6px;
            cursor: pointer;
          }

        }
      }

      .itemTr {
        td {
          font-weight: 400;

          &.isEdit {
            // padding: 4px 2px 4px 8px;

            .ant-input {
              height: 36px;
              background: #FFFFFF;
              border-radius: 8px;
              border: 1px solid #C4C4C4;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #262626;
              padding: 8px 4px 8px 8px;
            }
          }

          &.edit {
            color: #409EFF;
            cursor: pointer;
          }
        }
      }

      ::ng-deep .ant-table-scroll {
        border-left: 1px solid rgba(236, 236, 236, 1);

        .ant-table-fixed {
          border-left: none;
          border-color: rgba(236, 236, 236, 1);
        }

        .ng-star-inserted {
          border-color: rgba(236, 236, 236, 1);
        }

        .ant-table-column-title {
          padding: 8px 12px 8px 8px;
          display: flex;
          align-items: center;

          &>span {
            display: flex;
            align-items: center;
          }
        }
      }

    }

  }
}

::ng-deep .ant-table-body.ng-star-inserted {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  // 滑块背景
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px transparent;
  }
  // 滑块
  &::-webkit-scrollbar-thumb {
    background-color: #999;
    border-radius: 6px;
    outline: none;
  }
}

::ng-deep .cdk-overlay-pane:has(.tooltip_div) {
  width: 416px;

  .ant-popover-placement-bottomLeft {
    padding-top: 0;
  }

  .ant-popover-arrow {
    border: none;
  }

  .ant-popover-title {
    padding: 16px 16px 32px;
    border-bottom: 1px solid rgba(236, 236, 236, 1);

    .tooltip_div {
      .title {
        font-size: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #262626;
        line-height: 28px;
        margin-bottom: 12px;
      }

      .ant-form-item {
        &:first-child {
          margin-bottom: 12px;
        }

        .ant-form-item-label {
          margin-right: 8px;

          label {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #595959;
            line-height: 20px;

            &::after {
              content: '';
              display: none;
            }
          }
        }

        .ant-input {
          height: 36px;
          padding: 8px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #262626;
          line-height: 20px;
          background: #FFFFFF;
          border-radius: 8px;
          border: 1px solid #C4C4C4;
        }
      }
    }
  }

  .editBtn {
    display: flex;
    justify-content: end;

    .ant-btn {
      padding: 0 24px;
      height: 36px;
      border-radius: 8px;
    }
  }
}

::ng-deep .cdk-overlay-pane:has(.tooltip_div_clear) {
  width: 248px;

  .ant-popover-title {
    padding: 16px 16px 32px;
    border-bottom: 0px solid rgba(236, 236, 236, 1);

    .tooltip_div_clear {
      display: flex;
      align-items: center;
      height: 22px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #262626;
      line-height: 14px;


      i {
        font-size: 20px;
        color: #FF9426;
        margin-right: 8px;
      }
    }
  }

  .ant-popover-inner-content {
    padding: 0 16px 16px;

    .clearBtn {
      display: flex;
      justify-content: end;

      .ant-btn {
        padding: 0 16px;
        height: 28px;
        border-radius: 6px;
      }
    }
  }

}

::ng-deep .ng-star-inserted.normFooter {
  display: flex;
  align-items: center;
  justify-content: end;
  margin-bottom: 32px;

  .ant-pagination {

    .ant-pagination-item,
    .ant-pagination-prev .ant-pagination-item-link,
    .ant-pagination-next .ant-pagination-item-link,
    .ant-select-selection {
      border-radius: 8px;
    }

  }
}