import { Component, Input, OnInit } from "@angular/core";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { NzMessageService, NzModalRef } from "ng-zorro-antd";
import _ from "lodash";
import { ThrowStmt } from "@angular/compiler";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-book-setting",
  templateUrl: "./book-setting.component.html",
  styleUrls: ["./book-setting.component.less"],
})
export class BookSettingComponent implements OnInit {
  @Input() questionnaireId: string;
  nairModel: any;

  // 控制tab
  tabs = ["多语言"]; // , '欢迎页', '结束页'
  indexNum: number = 0;
  position = "left";

  languageList: any[] = [
    { value: "zh_CN", label: "中文", checked: false, isDefault: false },
    { value: "en_US", label: "ENG", checked: false, isDefault: false },
    // { value: 'ja_JP', label: "日本語", checked: false, isDefault: false },
  ];

  defaultList: any[] = [];

  radioValue = "";

  constructor(
    private modalRef: NzModalRef,
    private msg: NzMessageService,
    private surveySerivce: SurveyApiService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {
    this.surveySerivce.getNairById(this.questionnaireId).subscribe((res) => {
      if (res.result.code === 0) {
        this.nairModel = res.data;
        this.radioValue = res.data.defaultLanguage;
        let tmpSelected: string[] = res.data.optionalLanguages;

        for (let index = 0; index < this.languageList.length; index++) {
          const element = this.languageList[index];
          if (_.includes(tmpSelected, element.value)) {
            element.checked = true;
          }
        }
        this.updateChecked();
      }
    });
  }

  ok() {
    if (this.defaultList.length === 0) {
      // this.msg.error("可选语言不能为空。");
      this.customMsg.open("error", "可选语言不能为空");
      return;
    }

    if (!this.radioValue) {
      // this.msg.error("默认语言不能为空。");
      this.customMsg.open("error", "默认语言不能为空");
      return;
    }

    let tmpArr: string[] = [];
    for (let index = 0; index < this.defaultList.length; index++) {
      const element = this.defaultList[index];
      tmpArr.push(element.value);
    }

    let param = {
      id: this.questionnaireId,
      defaultLanguage: this.radioValue,
      optionalLanguages: tmpArr,
    };

    this.surveySerivce.updateLanguageInfo(param).subscribe((res) => {
      if (res.result.code === 0) {
        this.msg.success("保存成功。");
        this.modalRef.triggerOk();
      }
    });
  }

  updateChecked(): void {
    let tmpArr: string[] = [];
    this.defaultList = [];
    this.languageList.forEach((item) => {
      if (item.checked) {
        this.defaultList.push(item);
        tmpArr.push(item.value);
      }
    });

    if (this.radioValue) {
      if (!_.includes(tmpArr, this.radioValue)) {
        this.radioValue = "";
      }
    }
  }
}
