.modal {
  .modal_title {
    align-items: center;
    display: flex;
    justify-content: space-between;
    padding-right: 60px;
    .title_left {
      font-size: 24px;
      color: #17314c;
    }
    .title_right {
      display: flex;
      .linelin_left {
        width: 71px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 0px 2px 2px 0px;
        border: 1px solid #eee;
        font-weight: bold;
        cursor: pointer;
      }
      .linelin_right {
        width: 71px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 0px 2px 2px 0px;
        border: 1px solid #eee;
        font-weight: bold;
        cursor: pointer;
      }
      .linear {
        color: #fff;
        background: linear-gradient(90deg, #409eff 0%, #26d0f1 100%);
        border: none;
      }
    }
  }
  .activeclass {
    min-height: 300px;
    .modal_set_1 {
      display: flex;
      margin-top: 21px;
      justify-content: space-between;
      .set_left {
        flex: 1;

        .senior_line {
          width: 16px;
          height: 1px;
          background: #e6e6e6;
          margin: 16px 16px 0 16px;
        }
      }
      .set_right {
        flex: 1;
        margin-left: 30px;
      }
    }
    .radio_select {
      margin-top: 30px;
      .custom_list {
        user-select: none;
        margin-top: 20px;
        li {
          cursor: pointer;
          display: inline-block;
          background: #fafafa;
          color: #aaaaaa;
          font-size: 14px;
          padding: 5px 16px;
          border-radius: 15px;
          margin: 0 8px 8px 0;
          &.act {
            background: rgba(64, 158, 255, 0.05);
            color: rgba(64, 158, 255, 0.5);
          }
          &.blue {
            background: rgba(64, 158, 255, 0.1);
            color: rgba(64, 158, 255, 1);
          }
        }
      }
    }
    .info_div {
      margin-top: 30px;
      .info_radio {
        display: flex;
        flex-direction: column;
      }
      .Info_list {
        display: flex;
        margin-top: 5px;
        margin-left: 10px;
        flex-wrap: wrap;
        .taked_div {
          padding: 0 15px;
          line-height: 30px;
          border-radius: 15px;
          margin: 0 10px;
          margin-top: 5px;
          cursor: pointer;
          background-color: #fafafa;
          color: #aaaaaa;
        }
        .name_div {
          background: rgba(64, 158, 255, 0.1);
          color: #409eff;
        }
      }
    }
    .upload_warp {
      display: flex;
      // justify-content: space-between;
      margin-top: 30px;
      .act-tit {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        color: #17314c;
      }
      .act-txt {
        span {
          font-size: 14px;
          color: #17314c;
          padding: 0 20px 0 0px;
        }
        .second-lable {
          padding: 0 0 0 48px;
        }
        a {
          font-size: 12px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #409eff;
          padding: 0 0 0 24px;
        }
      }
      .avatar {
        width: 80px;
        height: 80px;
      }
      .font-color-blue {
        color: #409eff;
        margin-top: 10px;
      }
      .upload-background-color-white {
        background-color: #fff;
      }
    }
  }
  .release {
    min-height: 300px;
    .release_ul {
      margin-top: 50px;
      li {
        flex: 1;
        .input_num {
          width: 70px;
          margin-left: 10px;
        }
        .unit {
          margin-left: 3px;
        }
      }
    }
  }
  .flex_dir {
    display: flex;
    flex-direction: column;
  }
  .flex_dir_1 {
    display: flex;
    align-items: center;
  }
  .flex_nsp {
    display: flex;
  }
  .flex {
    display: flex;
    justify-content: space-between;
  }
  .padd_span {
    padding: 0 20px 0 10px;
  }
  .act_tit {
    font-family: PingFangSC-Medium, PingFang SC;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;
    margin: 0px 0 10px 0;
    color: #17314c;
  }
  .font-color-normal {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #17314c;
    line-height: 20px;
  }
  .font-color-grey {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
  }
}
.modal_footer {
  display: flex;
  align-items: center;
  .footer_left {
    background: linear-gradient(90deg, #27d0f1 0%, #409eff 100%);
    width: 144px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    color: #fff;
    border-radius: 30px;
    cursor: pointer;
  }
  .footer_right {
    margin-left: 30px;
    cursor: pointer;
    width: 128px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    background: #fafafa;
    border-radius: 19px;
  }
}
// ::ng-deep .ant-btn-round {
//   background: linear-gradient(90deg, #27d0f1 0%, #409eff 100%) !important;
//   border: none;
// }

.title_right_1 {
  display: flex;
  .linelin_left {
    width: 71px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 0px 2px 2px 0px;
    border: 1px solid #eee;
    font-weight: bold;
    cursor: pointer;
  }
  .linelin_right {
    width: 71px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 0px 2px 2px 0px;
    border: 1px solid #eee;
    font-weight: bold;
    cursor: pointer;
  }
  .linear {
    color: #fff;
    background: linear-gradient(90deg, #409eff 0%, #26d0f1 100%);
    border: none;
  }
}
