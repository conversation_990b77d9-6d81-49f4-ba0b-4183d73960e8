/**
 *
 *  @author: <PERSON>
 *  @Date:2024/07/17
 *  @content: 报告设置
 *
 */
import { Component, OnInit, Input, OnDestroy } from "@angular/core";
import { KnxFunctionPermissionService } from "@knx/knx-ngx/core";
import _ from "lodash";

import { NzDrawerRef } from "ng-zorro-antd/drawer";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { PermissionService } from "@src/modules/service/permission-service.service";

@Component({
  selector: "app-report-settings",
  templateUrl: "./report-settings.component.html",
  styleUrls: ["./report-settings.component.less"],
})
export class ReportSettings implements OnInit, OnDestroy {
  @Input() reportList: any[];
  private routerSubscription: Subscription;
  constructor(
    private drawerRef: NzDrawerRef,
    public knxFunctionPermissionService: KnxFunctionPermissionService,
        public permissionService: PermissionService,
    private router: Router
  ) {}
  submitForm(form: any) {}
  closeModal() {
    // this.nzModalService.closeAll()
    this.drawerRef.close();
  }

  ngOnInit() {
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  handClick(item) {
    if (!item.disabled) item.onclick();
  }
  ok() {
    this.closeModal();
  }
}
