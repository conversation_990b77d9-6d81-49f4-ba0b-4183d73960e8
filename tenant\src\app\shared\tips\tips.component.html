<div class="tips">


<nz-modal
  [(nzVisible)]="visible"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  (nzOnCancel)="handleCancel()"
  class="tips2"
  [nzStyle]="{ top: '300px','border-radius':'8px' }"
>

  <ng-template #modalContent>
    <div class="title">{{title}}</div>
    <div class="info">
      <div [innerHtml]="content"></div>
    </div>
  </ng-template>

  <ng-template #modalFooter>
    <div class="flex btn">
      <button nz-button class="cancel" (click)="handleCancel()">{{cancelTxt}}</button>
      <button nz-button class="confirm" (click)="handleOk()" >{{confirmTxt}}</button>
    </div>
  </ng-template>
</nz-modal>
</div>
