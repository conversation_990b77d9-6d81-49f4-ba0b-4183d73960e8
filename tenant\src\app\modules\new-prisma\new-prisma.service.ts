import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class NewPrismaService {
  url: string;
  tenantUrl: string;
  constructor(private http: HttpClient) {
    this.tenantUrl = "/tenant-api";
  }
  // 获取5G敬业度 敬业度指数 驱动因素 人口标签
  listByQuestionnaireCode(id): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/questionnaire/getQuestionnaireInformation/${id}`;
    return this.http.get(api);
  }

  // 获取5G已经创建过的活动 敬业度 敬业度指数 驱动因素 人口标签
  listByQuestionnaireCodeCreate(json): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/getQuestionnaireInformation`;
    return this.http.post(api, json);
  }

  //创建敬业度活动
  create(json): Observable<any> {
    if (json.projectReportDimensions) {
      json.projectReportDimensions.forEach((element) => {
        delete element.name;
      });
    }
    const api = `${this.tenantUrl}/survey/project/create`;
    return this.http.post(api, json);
  }

  //发布活动
  Publish(projectId): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/publishProject/${projectId}`;
    return this.http.get(api);
  }

  //更新活动
  updateAnnouncedProject(param: any): Observable<any> {
    if (param.projectReportDimensions) {
      param.projectReportDimensions.forEach((element) => {
        delete element.name;
      });
    }
    const api = `${this.tenantUrl}/survey/project/updateAnnouncedProject`;
    return this.http.post(api, param);
  }
  /**
   * uploadFile 上传样例文件
   */
  public uploadFile(formData, params): Observable<any> {
    const api = `${this.tenantUrl}/file/uploadWithBusinessType`;
    return this.http.post(api, formData);
  }

  // 通过人口统计分组ID获取人口统计数据室
  listDemographicFromGroupId(json): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/demographic/listDemographicFromGroupId?demographicGroupId=${json}`;
    return this.http.post(api, {});
  }

  //查询活动信息
  getProject(projectId): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/get/${projectId}`;
    return this.http.get(api);
  }

  getProjectSetting(projectId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getProjectSetting/${projectId}`;
    return this.http.get(api);
  }

  getAnalysisFactorDto(projectId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getAnalysisFactorDto/${projectId}`;
    return this.http.get(api);
  }

  getQuestionnaireById(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/get/${id}`;
    return this.http.get(api);
  }

  // 修改活动高级设置 已经发布
  public updateProjectSetting(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/updateProjectSetting`;
    return this.http.post(url, json);
  } //修改成post

  public getQuestionsByProjId(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/listSurveyQuestionnaireAndQuestion/${projectId}`;
    return this.http.get(url);
  }

  //作答说明 回复默认
  getDimensions(id): Observable<any> {
    const url = `${this.tenantUrl}/survey/standard/questionnaire/get/${id}`;
    return this.http.get(url);
  }

  //更新填答知道
  updateAnswer(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/updateBasicInfo`;
    return this.http.post(url, json);
  }

  //确认关联任务
  confirmRelation(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/confirmRelationPermission`;
    return this.http.post(url, json);
  }

  //填答预览链接
  PreviewUrl(id): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/getPreviewUrl?projectId=${id}`;
    return this.http.get(url);
  }

  //修改模型预览图片
  updateTemplateModel(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/questionnaire/updateTemplateModel`;
    return this.http.post(url, json);
  }

  // 导入 人口标签
  uploadPrismaAnalysisFactor(
    formData,
    projectId,
    surveyType: string,
    reportType: string
  ): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/readDemographicExcel?projectId=${projectId}&surveyType=${surveyType}&reportType=${reportType}`;
    return this.http.post(api, formData);
  }

  // 导出 人口标签
  exportPrismaAnalysisFactor(
    projectId: string,
    surveyType: string,
    reportType: string
  ): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    let api =
      `${this.tenantUrl}/survey/project/exportDemographicExcel?projectId=` +
      projectId +
      "&surveyType=" +
      surveyType +
      "&reportType=" +
      reportType;
    return this.http.get(api, httpOptions);
  }

  //部门
  public listOrganizationByTree(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listOrganizationByTree/${projectId}`;
    return this.http.get(url);
  }

  //人口标签
  public listByProjectId(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/demographic/listByProjectId/${projectId}`;
    return this.http.get(url);
  }

  //人口标签
  public listQuestionDimensionByProjectIdId(
    projectId: string
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/listQuestionDimensionByProjectIdId/${projectId}`;
    return this.http.get(url);
  }

  //关联题本
  public saveIasSelectedQuestionMapping(json): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/saveIasSelectedQuestionMapping`;
    return this.http.post(url, json);
  }

  //展示关联
  public showSelectedQuestionMapping(projectId: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/showSelectedQuestionMapping?projectId=${projectId}`;
    return this.http.get(url);
  }

  //展示结束页demo
  public showEndPageDemo(type): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/showEndPageDemo?surveyType=${type}`;
    return this.http.get(url);
  }

  //清除所有关联
  public removeOneProjectAllIasSelectedQuestionMapping(
    projectId: string
  ): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/removeOneProjectAllIasSelectedQuestionMapping?projectId=${projectId}`;
    return this.http.get(url);
  }

  //清除单个关联
  public removeIasOneSelectedQuestionMapping(id: string): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/removeIasOneSelectedQuestionMapping?id=${id}`;
    return this.http.post(url, {});
  }

  // 抽奖
  public getLotteryDetail(id: string): Observable<any> {
    // 获取抽奖详情
    const url = `${this.tenantUrl}/survey/lottery/getDetail?projectId=${id}`;
    return this.http.get(url);
  }

  public createOrUpdate(params): Observable<any> {
    // 抽奖创建或修改接口
    const url = `${this.tenantUrl}/survey/lottery/createOrUpdate`;
    return this.http.post(url, params);
  }

  public getPrizeList(id): Observable<any> {
    // 获取抽奖奖品接口
    const url = `${this.tenantUrl}/survey/lottery/getPrizeList?projectId=${id}`;
    return this.http.get(url);
  }

  public getCategoryList(id): Observable<any> {
    // 获取奖品分类
    const url = `${this.tenantUrl}/survey/lottery/getCategoryList?projectId=${id}`;
    return this.http.get(url);
  }

  public createOrUpdatePrize(params): Observable<any> {
    // 创建或修改奖品
    const url = `${this.tenantUrl}/survey/lottery/createOrUpdatePrize`;
    return this.http.post(url, params);
  }

  public batchCreateOrUpdateCategory(params): Observable<any> {
    // 批量创建或修改奖品分类
    const url = `${this.tenantUrl}/survey/lottery/batchCreateOrUpdateCategory`;
    return this.http.post(url, params);
  }

  public deleteCategory(id): Observable<any> {
    // 批量创建或修改奖品分类
    const url = `${this.tenantUrl}/survey/lottery/deleteCategory/${id}`;
    return this.http.post(url, {});
  }

  public deletePrize(id): Observable<any> {
    // 删除某个奖品
    const url = `${this.tenantUrl}/survey/lottery/deletePrize/${id}`;
    return this.http.post(url, {});
  }

  public getIconList(id): Observable<any> {
    // 获取图表列表
    const url = `${this.tenantUrl}/survey/lottery/getIconList?projectId=${id}`;
    return this.http.get(url);
  }

  public createIcon(params): Observable<any> {
    // 获取图表列表
    const url = `${this.tenantUrl}/survey/lottery/createIcon`;
    return this.http.post(url, params);
  }

  public getDisplayDetail(id): Observable<any> {
    // 获取抽奖预览数据
    const url = `${this.tenantUrl}/survey/lottery/getDisplayDetail?projectId=${id}`;
    return this.http.get(url);
  }

  public getLotteryResult(id): Observable<any> {
    // 获取中奖预览数据
    const url = `${this.tenantUrl}/survey/lottery/getLotteryResult?projectId=${id}`;
    return this.http.get(url);
  }
  public getMyLotteryPrizeList(id): Observable<any> {
    // 获取我的奖品预览数据
    const url = `${this.tenantUrl}/survey/lottery/getMyLotteryPrizeList?projectId=${id}`;
    return this.http.get(url);
  }

  public getTipDifferenceSave(params): Observable<any> {
    // 获取TIP 活动已保存的差价
    const url = `${this.tenantUrl}/survey/project/getTipDifferenceSave`;
    return this.http.post(url, params);
  }

  public getTipDifferenceNoSave(params): Observable<any> {
    // 获取TIP 活动未保存的差价
    const url = `${this.tenantUrl}/survey/project/getTipDifferenceNoSave`;
    return this.http.post(url, params);
  }
  // 自定义指数
  // 返回活动下或者细分报告所选择的人口标签列表
  public listDemographicByProjectId(projId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/prisma/custom/index/listDemographicByProjectId/${projId}`;
    return this.http.get(api);
  }
  // 返回活动下或者细分报告所选择的人口标签列表
  public dimensionListByProjectId(projId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/prisma/custom/index/dimension/listByProjectId/${projId}`;
    return this.http.get(api);
  }
  public batchCreateOrUpdate(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/dimension/batchCreateOrUpdate`;
    return this.http.post(api, param);
  }

  public listQuestionDimensionByProjectId(projectId: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/listQuestionDimensionByProjectId/${projectId}`;
    return this.http.get(api);
  }
  //
  public listByProjectIdGroup(projectId: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/combination/listByProjectId/${projectId}`;
    return this.http.get(api);
  }

  // 人口标签拖拽排序
  public resort(params: {
    projectId: any;
    analysisFactorDto: any[];
  }): Observable<any> {
    const api = `${this.tenantUrl}/survey/demographic/resort`;
    return this.http.post(api, params);
  }

  // 人口标签拖拽排序
  public modifyName(params): Observable<any> {
    const api = `${this.tenantUrl}/survey/demographic/modifyName`;
    return this.http.post(api, params);
  }
  // 单个人口标签 报告显示设置
  public modifyReport(params): Observable<any> {
    const api = `${this.tenantUrl}/survey/demographic/modifyLabelShowInReport`;
    return this.http.post(api, params);
  }

  // 保存或更新人口标签接口
  public saveOrUpdate(params: {
    projectId: any;
    analysisFactorDto: any[];
  }): Observable<any> {
    const api = `${this.tenantUrl}/survey/demographic/saveOrUpdate`;
    return this.http.post(api, params);
  }
  // 判断当前人口标签是否可以删除
  public isCanDelete(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/demographic/isCanDelete/${id}`;
    return this.http.get(api);
  }

  // 多语言相关
  // 获取所选语言的配置信息
  public getLanguageConfig(languageCode: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getLanguageConfig?languageCode=${languageCode}`;
    return this.http.get(api);
  }
  // 获取当前活动下所以语言
  public getLanguages(): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getLanguages`;
    return this.http.get(api);
  }
  // 获取所有的语言类型
  public getLanguageTypes(): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getLanguageTypes`;
    return this.http.get(api);
  }

  // 保存自定义语言配置
  public saveLanguageConfig(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/saveLanguageConfig`;
    return this.http.post(api, params);
  }

  // 获取报告说明数据
  public pictureSettingList(questionnaireId): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/reportModule/pictureSetting/list?questionnaireId=${questionnaireId}`;
    return this.http.get(api);
  }

  // 保存报告说明数据
  public pictureSettingModify(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/reportModule/pictureSetting/modify`;
    return this.http.post(api, params);
  }

  // 导出多语言配置信息excel
  public exportLanguageConfigExcel(languageCode,projectId): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    const api = `${this.tenantUrl}/survey/project/exportLanguageConfigExcel?languageCode=${languageCode}&projectId=${projectId}`;
    return this.http.get(api, httpOptions);
  }

  //  读取多语言配置信息excel
  public readLanguageConfigExcel(
    formData: any,
    languageCode: any
  ): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/readLanguageConfigExcel?languageCode=${languageCode}`;
    return this.http.post(api, formData);
  }
  /*
   *
   *  接口地址:
   *  @Description: 展示多群体分析
   *  @author: Sid Wang
   *  @Date: 2023/09/19
   *
   */
  public getListMultiDemographic(projectId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/listMultiDemographic?projectId=${projectId}`;
    return this.http.get(api);
  }
  /*
   *
   *  接口地址:
   *  @Description: 保存多群体分析
   *  @author: Sid Wang
   *  @Date: 2023/09/19
   *
   */
  public saveMultiDemographic(params: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/saveMultiDemographic`;
    return this.http.post(api, params);
  }

  /*
   *
   *  接口地址:
   *  @Description: 删除所有多群体分析
   *  @author: Sid Wang
   *  @Date: 2023/09/19
   *
   */
  public removeAllMultiDemographic(projectId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/removeAllMultiDemographic?projectId=${projectId}`;
    return this.http.get(api);
  }
  /*
   *
   *  接口地址:
   *  @Description: 删除一个多群体分析
   *  @author: Sid Wang
   *  @Date: 2023/09/19
   *
   */
  public removeOneMultiDemographic(id: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/removeOneMultiDemographic?id=${id}`;
    return this.http.get(api);
  }
  /*
   *
   *  接口地址:
   *  @Description: 导出分发题
   *  @author: Sid Wang
   *  @Date: 2023/11/08
   *
   */
  public exportSelectQuestion(projectId: string): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    const api = `${this.tenantUrl}/survey/question/exportSelectQuestion?projectId=${projectId}`;
    return this.http.get(api, httpOptions);
  }
  /*
   *
   *  接口地址:
   *  @Description: 导入分发题
   *  @author: Sid Wang
   *  @Date: 2023/11/08
   *
   */
  public importSelectQuestion(formData, projectId): Observable<any> {
    let api = `${this.tenantUrl}/survey/question/importSelectQuestion?projectId=${projectId}`;
    return this.http.post(api, formData);
  }

  /**
   * 根据组织能力调研人口学标签
   * @returns
   */
  public getRelationLabel(): Observable<any> {
    const api = `${this.tenantUrl}/survey/demographic/getDemographicListBySurveyType/ORIGANIZATIONAL_CAPABILITY`;
    return this.http.get(api);
  }

  /**
   * 交叉分析-获取活动所有指数
   * @returns 
   */
  public getCrosslistAllLabels(projectId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/prisma/custom/index/listAllLabels/${projectId}`;
    return this.http.get(api);
  }
  /**
   * 交叉分析-获取交叉分析配置
   * @returns 
   */
  public getReportCrossAnalysisConfig(projectId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getReportCrossAnalysisConfig/${projectId}`;
    return this.http.get(api);
  }
  /**
   * 交叉分析-恢复默认
   * @returns 
   */
  public getListDefaultCrossAnalysisConfig(projectId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/getListDefaultCrossAnalysisConfig/${projectId}`;
    return this.http.get(api);
  }
  /**
   * 交叉分析-保存活动交叉配置
   * @returns 
   */
  public saveReportCrossAnalysisConfig(formData): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/saveReportCrossAnalysisConfig`;
    return this.http.post(api, formData);
  }

  /*
   *
   *  接口地址:
   *  @Description: 分发题排序
   *  @author: Crl
   *  @Date: 2024/07/23
   *
   */
  public saveIasSelectedQuestionMappingSort(data): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/saveIasSelectedQuestionMappingSort`;
    return this.http.post(api, data);
  }

  /**
   * 数据呈现/计算规则-获取数据呈现/计算规则
   * @returns 
   */
  public getDataShowCalRules(projectId: string): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/dataShowCalRules?projectId=${projectId}`;
    return this.http.get(api);
  }

  /**
   * 数据呈现/计算规则-保存
   * @returns 
   */
  public setProjectDataShowCalRules (params: any): Observable<any> {
    const url = `${this.tenantUrl}/survey/project/setProjectDataShowCalRules`;
    return this.http.post(url, params);
  }


  /*
   *
   *  接口地址:
   *  @Description: 数据呈现/计算规则-导出-人口标签
   *  @author: Sid Wang
   *  @Date: 2025/03/21
   *
   */
  public exportProjectDataShowCalRulesDemographic(projectId: string): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    const api = `${this.tenantUrl}/survey/project/exportProjectDataShowCalRules/demographic?projectId=${projectId}`;
    return this.http.get(api, httpOptions);
  }
  /*
   *
   *  接口地址:
   *  @Description: 数据呈现/计算规则-导入-人口标签
   *  @author: Sid Wang
   *  @Date: 2025/03/21
   *
   */
  public importProjectDataShowCalRulesDemographic(formData, projectId): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/importProjectDataShowCalRules/demographic?projectId=${projectId}`;
    return this.http.post(api, formData);
  }

  /*
   *
   *  接口地址:
   *  @Description: 数据呈现/计算规则-导出-组织
   *  @author: Sid Wang
   *  @Date: 2025/03/21
   *
   */
  public exportProjectDataShowCalRulesOrganization(projectId: string): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    const api = `${this.tenantUrl}/survey/project/exportProjectDataShowCalRules/organization?projectId=${projectId}`;
    return this.http.get(api, httpOptions);
  }
  /*
   *
   *  接口地址:
   *  @Description: 数据呈现/计算规则-导入-组织
   *  @author: Sid Wang
   *  @Date: 2025/03/21
   *
   */
  public importProjectDataShowCalRulesOrganization(formData, projectId): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/importProjectDataShowCalRules/organization?projectId=${projectId}`;
    return this.http.post(api, formData);
  }

  /*
   *
   *  接口地址:
   *  @Description: 数据呈现/计算规则-导出-维度
   *  @author: Sid Wang
   *  @Date: 2025/03/21
   *
   */
  public exportProjectDataShowCalRulesDimension(projectId: string): Observable<any> {
    let httpOptions: any = { responseType: "Blob", observe: "response" };
    const api = `${this.tenantUrl}/survey/project/exportProjectDataShowCalRules/dimension?projectId=${projectId}`;
    return this.http.get(api, httpOptions);
  }
  /*
   *
   *  接口地址:
   *  @Description: 数据呈现/计算规则-导入-维度
   *  @author: Sid Wang
   *  @Date: 2025/03/21
   *
   */
  public importProjectDataShowCalRulesDimension(formData, projectId): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/importProjectDataShowCalRules/dimension?projectId=${projectId}`;
    return this.http.post(api, formData);
  }
  /*
   *
   *  接口地址:
   *  @Description: 数据呈现/计算规则-重置规则
   *  @author: Sid Wang
   *  @Date: 2025/03/21
   *
   */
  public resetShowCalRules(projectId: string): Observable<any> {
    let api = `${this.tenantUrl}/survey/project/resetShowCalRules?projectId=${projectId}`;
    return this.http.post(api, {});
  }
}