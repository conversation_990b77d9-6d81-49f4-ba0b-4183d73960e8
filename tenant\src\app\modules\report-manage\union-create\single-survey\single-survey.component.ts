import { timer } from "rxjs";
import { filter } from "rxjs/operators";

import {
  Component,
  Input,
  OnInit,
  ViewChild,
  ChangeDetectorRef,
} from "@angular/core";
import { ProjectManageService } from "@src/modules/service/project-manage.service";
import { SurveyApiService } from "@src/modules/service/survey-api.service";
import {
  NzFormatEmitEvent,
  NzMessageService,
  NzTreeComponent,
  NzTreeNode,
  NzTreeNodeOptions,
} from "ng-zorro-antd";
import { DragulaService } from "ng2-dragula";
import { Subscription } from "rxjs";
import { AdvancedFiltersComponent } from "../advancedFilters/advanced-filters.component";
import _ from "lodash";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { HttpClient } from "@angular/common/http";
import { PermissionService } from "@src/modules/service/permission-service.service";
@Component({
  selector: "app-single-survey",
  templateUrl: "./single-survey.component.html",
  styleUrls: ["./single-survey.component.less"],
})
export class SingleSurveyComponent implements OnInit {
  @Input() paramData: any;
  @ViewChild("advancedChild", { static: false })
  advancedChild: AdvancedFiltersComponent;

  langage: string = "zh_CN";
  index: number = 1;
  fileName: any = {
    zh_CN: null,
    en_US: null,
  };
  projectId: string;
  extInfo: any = {};

  orgList: any[] = [];
  demoList: any[] = [];
  comparisonList: any[] = [];
  normList: any[] = []; // 外部
  internalNormList: any[] = []; // 内部
  historyDataList: any[] = [];
  orgaztionSinkingAnalysiss: any[] = [];

  indeterminate: boolean = false;
  allChecked: boolean = false;
  checkedValues: any[] = [];

  searchValue = "";
  searchRenValue = "";

  selectedNodes: any[] = [];
  selectedRenkouNodes: any[] = [];
  selectNormList: any[] = []; // 外部
  selectInternalNormList: any[] = []; // 内部
  selectHistoryDataList: any[] = [];
  chooseType; // 组织团队报告筛选条件s
  // isSpinning: boolean = false

  checkedKeys: any[] = [];
  checkedKeysRenkou: any[] = [];

  // 5大类型选择列表数据
  demoTabs = [];

  selectedCatId: string = undefined;

  expandedNodes: string[] = [];
  expandedRenNodes: string[] = [];

  lans: any[] = [
    { label: "中文", value: "zh_CN", checked: true },
    { label: "ENG", value: "en_US", checked: false },
  ];

  @ViewChild("nzTreeComponent", { static: false })
  nzTreeComponent: NzTreeComponent;
  @ViewChild("nzTreeComponentRenkou", { static: false })
  nzTreeComponentRenkou: NzTreeComponent;
  @ViewChild("nzTreeComparisonList", { static: false })
  nzTreeComparisonList: NzTreeComponent;

  // 所有选择的数据
  showList: any[] = [];
  // 选择的数据种类
  showCount: number = 0;
  // 折叠面板状态类
  expandMap: any = {};

  // 点击顺序
  checkSeq: number = 1;

  subs = new Subscription();

  timeHandle;
  isBatchCreate: boolean = false;
  permission;

  constructor(
    private http: HttpClient,
    private projSerivce: ProjectManageService,
    private surveySerivce: SurveyApiService,
    private msg: NzMessageService,
    private dragulaService: DragulaService,
    private changeRef: ChangeDetectorRef,
    private customMsg: MessageService,
    public permissionService: PermissionService
  ) {}

  ngOnInit() {
    this.permission = this.permissionService.isPermission();
    this.subs.add(
      this.dragulaService
        .drop("VAMPIRES")
        .subscribe(({ name, el, target, source, sibling }) => {
          for (let index = 0; index < this.showList.length; index++) {
            let element = this.showList[index];
            if (element.id === "norm" || element.id === "historyData") {
              this.changeOrderedSeq(element);
            }
          }
        })
    );
    this.loadData();
  }

  changefileName() {
    this.paramData.fileName = {
      zh_CN: this.fileName.zh_CN,
      en_US: this.fileName.en_US,
    };
  }

  choooseBatchCreate(e) {
    e.stopPropagation();
    this.isBatchCreate = e;
  }

  changeCategory(event) {}

  // 团队报告 选中不同级别后操作
  checkDepartment(num) {
    let allNodes = this.nzTreeComponent.getTreeNodes();

    this.timeHandle = setTimeout(() => {
      if (num !== 0) {
        allNodes.forEach((item) => {
          this.celDepartment(item);
          this.chooseDepartment(item, num);
        });
      } else {
        allNodes.forEach((item) => {
          this.celDepartment(item);
          this.chooseIsLeat(item);
        });
      }
      let nodeList: NzTreeNode[] = this.nzTreeComponent.getCheckedNodeList();
      let tmp: any[] = [];
      for (let index = 0; index < nodeList.length; index++) {
        const element = nodeList[index].origin;
        tmp.push({
          id: element.key,
          name: element.title,
          isShowExcel: element.isShowExcel,
          isShowReport: element.isShowReport,
        });
      }
      this.selectedNodes = tmp;

      this.getShowList();
      clearTimeout(this.timeHandle);
    });
  }

  chooseIsLeat(node) {
    // 全选
    if (node.isLeaf) {
      if (!node.origin.isVirtual) {
        this.checkedKeys.push(node.key);
        node.isChecked = true;
      }
    }
    if (node.children.length !== 0) {
      node.children.forEach((element) => {
        this.chooseIsLeat(element);
      });
    }
  }

  chooseDepartment(node, num) {
    if (num > 0) {
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.chooseDepartment(element, num - 1);
        });
      }
    } else if (num === 0) {
      if (!node.origin.isVirtual) {
        this.checkedKeys.push(node.key);
        node.isChecked = true;
      }
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.chooseDepartment(element, num - 1);
        });
      }
    } else if (num < 0) {
      if (!node.origin.isVirtual) {
        node.isDisabled = true;
      }
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.chooseDepartment(element, num - 1);
        });
      }
    }
  }

  celDepartment(node) {
    if (!node.origin.isVirtual) {
      node.isDisabled = false;
    }
    node.children.forEach((element) => {
      this.celDepartment(element);
    });
  }

  changeOrderedSeq(normType: any) {
    let sortMap: any = {};
    let KeyType: string = normType.id;
    for (let index = 0; index < normType.itemList.length; index++) {
      let item = normType.itemList[index];
      item.seq = index + 1;
      sortMap[item.id] = item.seq;
    }

    let originType: any = {};
    for (let index = 0; index < this.demoTabs.length; index++) {
      let tab = this.demoTabs[index];
      if (tab.id === KeyType) {
        originType = tab;
        break;
      }
    }

    for (let index = 0; index < originType.items.length; index++) {
      const element = originType.items[index];
      let sortValue = sortMap[element.value];
      if (sortValue) {
        element.seq = sortValue;
      }
    }
  }

  loadData() {
    console.log("this.paramData", this.paramData);
    this.projectId = this.paramData.project.id;
    this.extInfo = this.paramData.extInfo;

    this.orgList = this.paramData.organizationTrees;
    if (this.orgList && this.orgList.length > 0) {
      this.expandedNodes.push(this.orgList[0].key);
    }

    this.orgList.forEach((iie) => {
      this.nodeDisable(iie);
    });

    this.demoList = this.paramData.demographics;
    if (this.demoList && this.demoList.length > 0) {
      this.demoList.map((item) => {
        this.expandedRenNodes.push(item.id);
      });
    }

    this.normList = this.paramData.prismaNorms;

    this.comparisonFormatter(this.paramData.prismaNorms);
    this.comparisonFormatter(this.paramData.internalNorms);
    this.comparisonFormatter(this.paramData.historyDataList);

    this.comparisonList.push({
      key: "internalNorm",
      title: "内部常模",
      isLeaf: false,
      children: this.paramData.internalNorms,
    });
    this.comparisonList.push({
      key: "norm",
      title: "外部常模",
      isLeaf: false,
      children: this.paramData.prismaNorms,
    });
    if (this.paramData.reportType == "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
      this.comparisonFormatter(this.paramData.orgaztionSinkingAnalysiss);
      this.comparisonList.push({
        key: "orgaztionSinkingAnalysis",
        title: "组织下沉分析对比",
        isLeaf: false,
        children: this.paramData.orgaztionSinkingAnalysiss,
      });
    }
    this.comparisonList.push({
      key: "historyData",
      title: "历史对比",
      isLeaf: false,
      children: this.paramData.historyDataList,
    });
    this.normList = this.paramData.prismaNorms;
    this.internalNormList = this.paramData.internalNorms;
    this.historyDataList = this.paramData.historyDataList;
    this.orgaztionSinkingAnalysiss = this.paramData.orgaztionSinkingAnalysiss;
    this.initTab();
  }

  nodeDisable(node) {
    if (node.isVirtual) {
      node.disabled = true;
    }
    if (node.children.length !== 0) {
      node.children.forEach((element) => {
        this.nodeDisable(element);
      });
    }
  }

  twoDash: string = "__";

  demoWidgetList: any[] = [];

  initTab() {
    // 组织
    this.demoTabs.push({
      id: "org",
      name: "组织架构",
      allChecked: false,
      indeterminate: false,
      items: [],
      isNorm: false,
      checkedValues: [],
      searchText: "",
      reportType: "",
    });

    // 人口标签 多个
    let tmp: any[] = [];
    this.formatter(this.demoList);
    this.demoTabs.push({
      id: "factor",
      name: "人口标签",
      allChecked: false,
      indeterminate: false,
      items: [],
      isNorm: false,
      checkedValues: [],
      searchText: "",
    });

    // 内部常模
    tmp = [];
    this.internalNormList.forEach((item) => {
      tmp.push({
        label: item.name.zh_CN,
        value: item.id,
        checked: false,
        seq: 0,
      });
    });
    this.demoTabs.push({
      id: "internalNorm",
      name: "内部常模",
      allChecked: false,
      indeterminate: false,
      items: tmp,
      isNorm: false,
      isInternalNorm: true,
      checkedValues: [],
      searchText: "",
      haveOrder: true,
    });

    // 外部常模
    tmp = [];
    this.normList.forEach((item) => {
      tmp.push({
        label: item.name.zh_CN,
        value: item.id,
        checked: false,
        seq: 0,
      });
    });
    this.demoTabs.push({
      id: "norm",
      name: "外部常模",
      allChecked: false,
      indeterminate: false,
      items: tmp,
      isNorm: true,
      checkedValues: [],
      searchText: "",
      haveOrder: true,
    });
    if (this.paramData.reportType == "NETEASE_INVESTIGATION_RESEARCH_CUSTOM") {
      // 组织下沉分析对比
      tmp = [];
      this.orgaztionSinkingAnalysiss.forEach((item) => {
        tmp.push({
          label: item.name.zh_CN,
          value: item.id,
          checked: false,
          seq: 0,
        });
      });
      this.demoTabs.push({
        id: "orgaztionSinkingAnalysis",
        name: "组织下沉分析对比",
        allChecked: false,
        indeterminate: false,
        items: tmp,
        isNorm: false,
        checkedValues: [],
        searchText: "",
        haveOrder: true,
      });
    }
    // 历史数据
    tmp = [];
    this.historyDataList.forEach((item) => {
      tmp.push({
        label: item.name.zh_CN,
        value: item.id,
        checked: false,
        seq: 0,
      });
    });
    this.demoTabs.push({
      id: "historyData",
      name: "历史数据",
      allChecked: false,
      indeterminate: false,
      items: tmp,
      isNorm: false,
      checkedValues: [],
      searchText: "",
      haveOrder: true,
    });

    this.selectedCatId = this.demoTabs[0].id;
    // 人口标签-默认全选
    setTimeout(() => {
      this.updateAllChecked(true);
    }, 0);
  }

  formatter(data) {
    data.forEach((item) => {
      item.title = item.name.zh_CN;
      item.key = item.id;
      if (item.children.length !== 0) {
        item.isLeaf = false;
        this.formatter(item.children);
      } else {
        item.isLeaf = true;
      }
    });
  }

  comparisonFormatter(data) {
    data.forEach((item) => {
      item.title = item.name.zh_CN;
      item.key = item.id;
      item.isLeaf = true;
    });
  }

  factorSelectAll(e, isCheck: boolean, widg) {
    e.stopPropagation();
    for (let index = 0; index < widg.widgetItems.length; index++) {
      const it = widg.widgetItems[index];
      it.checked = isCheck;
    }
    this.getShowList();
  }

  // 获取折叠状态
  getActive(typeId: string): boolean {
    let tmp = this.expandMap[typeId];
    if (tmp === undefined) {
      tmp = true;
    }
    return tmp;
  }

  // 保存折叠状态
  panelActiveChange(typeId, e) {
    this.expandMap[typeId] = e;
  }
  //内外部对比  选择以后 进行拖拽排序 然后再选择复选框 排序会回到初始状态 进行修补
  sortCalculate() {
    let num = 1;
    this.showList.forEach((item) => {
      if (item.id == "factor" || item.id == "org") {
        return false;
      }
      item.itemList.forEach((element) => {
        if (element.seq > 0) {
          element.seq = num++;
        }
      });
    });
    for (let index = 0; index < this.demoTabs.length; index++) {
      const element = this.demoTabs[index];
      if (element.id === "org" || element.id === "factor") {
        continue;
      }
      this.showList.forEach((eln) => {
        if (eln.id == element.id) {
          element.items.forEach((parentItem, j) => {
            eln.itemList.forEach((childItem) => {
              if (parentItem.value == childItem.id) {
                this.demoTabs[index].items[j].seq = childItem.seq;
              }
            });
          });
        }
      });
    }
  }
  getShowList(): any[] {
    this.sortCalculate();
    let typeList: any[] = [];
    typeList.push({
      id: "org",
      name: "组织架构",
      itemList: this.selectedNodes,
      active: this.getActive("org"),
    });
    typeList.push({
      id: "factor",
      name: "人口标签",
      itemList: this.selectedRenkouNodes,
      active: this.getActive("factor"),
    });

    for (let index = 0; index < this.demoTabs.length; index++) {
      const element = this.demoTabs[index];
      if (element.id === "org" || element.id === "factor") {
        continue;
      }
      let itemList: any[] = [];
      element.items.forEach((item) => {
        if (item.checked) {
          itemList.push({ id: item.value, name: item.label, seq: item.seq });
        }
      });
      itemList = _.sortBy(itemList, ["seq"]);
      typeList.push({
        id: element.id,
        name: element.name,
        itemList: itemList,
        active: this.getActive(element.id),
        haveOrder: element.haveOrder,
      });
    }

    this.showList = typeList;
    this.showCount = 0;
    for (let index = 0; index < this.showList.length; index++) {
      const element = this.showList[index];
      if (element.itemList.length > 0) {
        this.showCount++;
      }
    }

    return typeList;
  }

  // get should select map
  selectChild(node: any, isSelected: boolean, map: any) {
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        map[child.key] = 1;
        this.selectChild(child, isSelected, map);
      }
    }
  }

  // set select node by map
  setSelectState(node: NzTreeNode, isSelected: boolean, map: any) {
    if (map[node.key] === 1) {
      if (node.origin.isVirtual) {
        node.isDisabled = true;
        node.isChecked = false;
      } else {
        node.isDisabled = isSelected;
        // node.setSelected(isSelected);
        node.isChecked = false;
      }
    }
    if (node.children && node.children.length > 0) {
      for (let index = 0; index < node.children.length; index++) {
        const child = node.children[index];
        this.setSelectState(child, isSelected, map);
      }
    }
  }

  nzEvent(event: NzFormatEmitEvent): void {
    if (event.node.isDisabled) {
      return;
    }
    this.chooseType = null;
    // select children
    //用户自定义属性
    let currentNode: any = event.node.origin;
    let isSelected: boolean = currentNode.checked;
    let map = {};
    this.selectChild(currentNode, isSelected, map);

    let allNodes: NzTreeNode[] = this.nzTreeComponent.getTreeNodes();
    allNodes.forEach((n: NzTreeNode) => {
      this.setSelectState(n, isSelected, map);
    });

    // update displaying selected nodes
    let nodeList: NzTreeNode[] = this.nzTreeComponent.getCheckedNodeList();
    let tmp: any[] = [];
    for (let index = 0; index < nodeList.length; index++) {
      const element = nodeList[index].origin;
      tmp.push({
        id: element.key,
        name: element.title,
        isShowExcel: element.isShowExcel,
        isShowReport: element.isShowReport,
      });
    }
    this.selectedNodes = tmp;

    this.getShowList();
    this.nzSelectChange();
  }

  checkAllRen(data, e) {
    const nodeList: NzTreeNode[] = this.nzTreeComponentRenkou.getTreeNodes();
    let tmp: any[] = [];

    nodeList.forEach((node1) => {
      if (node1.children.length !== 0) {
        node1.isChecked = e;
        node1.children.forEach((node2) => {
          node2.isChecked = e;
          if (node2.children.length !== 0) {
            node2.children.forEach((node3) => {
              node3.isChecked = e;
              if (e) {
                tmp.push({
                  id: node3.key,
                  name: node3.title,
                  isShowExcel: node3.origin.isShowExcel,
                  isShowReport: node3.origin.isShowReport,
                });
              }
            });
          } else {
            node2.isChecked = e;
            if (e) {
              tmp.push({
                id: node2.key,
                name: node2.title,
                isShowExcel: node2.origin.isShowExcel,
                isShowReport: node2.origin.isShowReport,
              });
            }
          }
        });
      }
    });
    this.selectedRenkouNodes = tmp;
    this.getShowList();
  }

  nzEventRen(event: NzFormatEmitEvent): void {
    this.chooseType = null;
    // update displaying selected nodes
    const nodeList: NzTreeNode[] = this.nzTreeComponentRenkou.getTreeNodes();
    let all = true; // 是否全选
    let tmp: any[] = [];
    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        node1.children.map((node2) => {
          if (node2.children.length !== 0) {
            node2.children.map((node3) => {
              if (node3.isChecked) {
                tmp.push({
                  id: node3.key,
                  name: node3.title,
                  isShowExcel: node3.origin.isShowExcel,
                  isShowReport: node3.origin.isShowReport,
                });
              } else {
                all = false;
              }
            });
          } else {
            if (node2.isChecked) {
              tmp.push({
                id: node2.key,
                name: node2.title,
                isShowExcel: node2.origin.isShowExcel,
                isShowReport: node2.origin.isShowReport,
              });
            } else {
              all = false;
            }
          }
        });
      }
    });
    if (!all && tmp.length !== 0) {
      this.indeterminate = true;
    } else {
      this.indeterminate = false;
    }
    this.allChecked = all;
    this.selectedRenkouNodes = tmp;

    this.getShowList();
  }
  nzEventComparison(event: NzFormatEmitEvent): void {
    let nodeKey: string = event.node.key;
    let isAllChecked: boolean = false;
    if (
      event.node.key == "internalNorm" ||
      event.node.key == "norm" ||
      event.node.key == "historyData" ||
      event.node.key == "orgaztionSinkingAnalysis"
    ) {
      isAllChecked = true;
    } else {
      nodeKey = event.node.parentNode.key;
    }
    this.demoTabs.forEach((node) => {
      if (node.id == nodeKey) {
        node.items.forEach((item) => {
          if (item.value == event.node.key || isAllChecked) {
            // console.log(item,event, event.node.isHalfChecked, "event");
            item.checked = event.node.isChecked;
            item.seq = this.index;
            this.index++;
            this.updateSingleChecked(node, item, event.node.isChecked);
          }
        });
      }
    });
    this.getShowList();
  }

  setSelectComparison(node: NzTreeNode) {
    let tabData: any;
    let itemDate: any;
    if (node.children.length != 0) {
      node.children.map((node1) => {
        tabData = this.demoTabs.filter((f) => f.id == node.key)[0];
        tabData.items.forEach((item) => {
          if (item.value == node1.key) {
            item.checked = node1.isChecked;
          }
        });
        switch (node.key) {
          case "internalNorm": {
            itemDate = this.internalNormList.filter((f) => f.id == node1.key);
            break;
          }
          case "norm": {
            itemDate = this.normList.filter((f) => f.id == node1.key);
            break;
          }
          case "historyData": {
            itemDate = this.historyDataList.filter((f) => f.id == node1.key);
            break;
          }
          case "orgaztionSinkingAnalysis": {
            itemDate = this.orgaztionSinkingAnalysiss.filter(
              (f) => f.id == node1.key
            );
            break;
          }
        }
        itemDate.checked = node1.isChecked;
        this.updateSingleChecked(tabData, itemDate, node1.isChecked);
      });
    }
  }

  expanded(event: NzFormatEmitEvent): void {
    if (event.node.isExpanded) {
      event.node.isExpanded = false;
    } else {
      event.node.isExpanded = true;
    }
  }

  updateAllChecked(e) {
    // if (e && tabData.isNorm && tabData.items.length > 4) {
    //   this.msg.warning("外部常模选择不能超过4个。");
    //   setTimeout(function () {
    //     tabData.allChecked = false;
    //   }, 1000);
    //   return;
    // }
    this.allChecked = e;
    let tmp: any[] = [];
    let all = true; // 是否全选
    const nodeList: NzTreeNode[] = this.nzTreeComponentRenkou.getTreeNodes();
    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        node1.isChecked = e;
        node1.isHalfChecked = false;
        node1.children.map((node2) => {
          node2.isChecked = e;
          node2.isHalfChecked = false;
          if (node2.children.length !== 0) {
            node2.children.map((node3) => {
              node3.isChecked = e;
              node3.isHalfChecked = false;
              if (e) {
                tmp.push({
                  id: node3.key,
                  name: node3.title,
                  isShowExcel: node3.origin.isShowExcel,
                  isShowReport: node3.origin.isShowReport,
                });
              } else {
                all = false;
              }
            });
          } else {
            if (e) {
              tmp.push({
                id: node2.key,
                name: node2.title,
                isShowExcel: node2.origin.isShowExcel,
                isShowReport: node2.origin.isShowReport,
              });
            } else {
              all = false;
            }
          }
        });
      }
    });
    this.indeterminate = false;
    //this.recordChecks(tabData);
    this.selectedRenkouNodes = tmp;
    this.getShowList();
  }
  normFlag = true; //外部常模一秒内只提示一次警告
  internalNormFalsg = false; //内部常模一秒内只提示一次警告
  updateSingleChecked(tabData, itemData, e) {
    if (this.isCheckDisabled(tabData)) {
      let _this = this;
      if (this.normFlag) {
        this.normFlag = false;
        // this.msg.warning("外部常模选择不能超过4个。");
        this.customMsg.open("warning", "外部常模选择不能超过4个");
        setTimeout(function() {
          _this.normFlag = true;
        }, 1000);
      }

      // revert selection
      // setTimeout(function () {
      //   tabData.items.forEach(item => {
      //     item.checked = _.includes(tabData.checkedValues, item.value);
      //   });
      // }, 1000);
      // return;
    }
    if (this.isCheckDisabledInternalNorm(tabData)) {
      let _this = this;
      if (this.normFlag) {
        this.normFlag = false;
        // this.msg.warning("内部常模选择不能超过3个。");
        this.customMsg.open("warning", "内部常模选择不能超过3个");
        setTimeout(function() {
          _this.normFlag = true;
        }, 1000);
      }
    }

    // update check order
    if (e) {
      itemData.seq = this.checkSeq++;
    }

    if (tabData.items.every((item) => !item.checked)) {
      tabData.allChecked = false;
      tabData.indeterminate = false;
    } else if (tabData.items.every((item) => item.checked)) {
      tabData.allChecked = true;
      tabData.indeterminate = false;
    } else {
      tabData.indeterminate = true;
    }
    this.recordChecks(tabData);

    this.getShowList();
  }

  // 移除选择tree node
  removeTreeNodeItem(item) {
    let tmpKey = item.id;
    let node: NzTreeNode = this.getNodeByKey(tmpKey);
    let currentNode = node.origin;

    node.setChecked(false);
    currentNode.isChecked = false;

    // cancel select children
    let isSelected: boolean = false;
    let map = {};
    this.selectChild(currentNode, isSelected, map);

    let allNodes: NzTreeNode[] = this.nzTreeComponent.getTreeNodes();
    allNodes.forEach((n: NzTreeNode) => {
      this.setSelectState(n, isSelected, map);
    });

    // update displaying selected nodes
    let nodeList: NzTreeNode[] = this.nzTreeComponent.getCheckedNodeList();
    let tmp: any[] = [];
    for (let index = 0; index < nodeList.length; index++) {
      const element = nodeList[index].origin;
      tmp.push({
        id: element.key,
        name: element.title,
        isShowExcel: element.isShowExcel,
        isShowReport: element.isShowReport,
      });
    }
    this.selectedNodes = tmp;

    this.getShowList();
  }

  removeRenTreeNodeItem(item) {
    let tmpKey = item.id;
    let node: NzTreeNode = this.getRenNodeByKey(tmpKey);

    let currentNode = node.origin;

    node.setChecked(false);
    currentNode.isChecked = false;

    // cancel select children
    // let isSelected: boolean = false;
    // let map = {};
    // this.selectChild(currentNode, isSelected, map);

    // let allNodes : NzTreeNode[] = this.nzTreeComponentRenkou.getTreeNodes();
    // allNodes.forEach((n:NzTreeNode) => {
    //   this.setSelectState(n, isSelected, map);
    // });

    // update displaying selected nodes
    let nodeList: NzTreeNode[] = this.nzTreeComponentRenkou.getTreeNodes();
    let tmp: any[] = [];

    let all = true; // 是否全选
    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        let isNode1Checked = true;
        node1.isHalfChecked = false;
        node1.children.map((node2) => {
          if (node2.children.length !== 0) {
            let isNode2Checked = true;
            node2.isHalfChecked = false;
            node2.children.map((node3) => {
              if (node3.isChecked) {
                tmp.push({
                  id: node3.key,
                  name: node3.title,
                  isShowExcel: node3.origin.isShowExcel,
                  isShowReport: node3.origin.isShowReport,
                });
                node2.isHalfChecked = true;
                node1.isHalfChecked = true;
                if (isNode2Checked) {
                  isNode2Checked = true;
                }
              } else {
                all = false;
                isNode2Checked = false;
              }
            });
            node2.isChecked = isNode2Checked;
            node2.isHalfChecked = isNode2Checked ? false : node2.isHalfChecked;
          } else {
            if (node2.isChecked) {
              tmp.push({
                id: node2.key,
                name: node2.title,
                isShowExcel: node2.origin.isShowExcel,
                isShowReport: node2.origin.isShowReport,
              });
              if (isNode1Checked) {
                isNode1Checked = true;
              }
              node1.isHalfChecked = true;
            } else {
              all = false;
              isNode1Checked = false;
            }
          }
        });
        node1.isChecked = isNode1Checked;
        node1.isHalfChecked = isNode1Checked ? false : node1.isHalfChecked;
      } else {
        if (node1.isChecked) {
          tmp.push({
            id: node1.key,
            name: node1.title,
            isShowExcel: node1.origin.isShowExcel,
            isShowReport: node1.origin.isShowReport,
          });
          node1.isHalfChecked = false;
        }
      }
    });

    if (!all && tmp.length !== 0) {
      this.indeterminate = true;
    } else {
      this.indeterminate = false;
    }
    this.allChecked = all;
    this.selectedRenkouNodes = tmp;

    this.getShowList();
  }

  getNodeByKey(key: string): NzTreeNode {
    let tmp: NzTreeNode;
    let nodeList: NzTreeNode[] = this.nzTreeComponent.getCheckedNodeList();
    for (let index = 0; index < nodeList.length; index++) {
      const element = nodeList[index];
      if (key === element.key) {
        tmp = element;
        break;
      }
    }
    return tmp;
  }
  getRenNodeByKey(key: string): NzTreeNode {
    let tmp: NzTreeNode;
    let nodeList: NzTreeNode[] = this.nzTreeComponentRenkou.getTreeNodes();
    nodeList.map((node1) => {
      if (key === node1.key) {
        tmp = node1;
      } else {
        if (node1.children.length !== 0) {
          node1.children.map((node2) => {
            if (key === node2.key) {
              tmp = node2;
            } else {
              if (node2.children.length !== 0) {
                node2.children.map((node3) => {
                  if (key === node3.key) {
                    tmp = node3;
                  }
                });
              }
            }
          });
        }
      }
    });
    // for (let index = 0; index < nodeList.length; index++) {
    //   const element = nodeList[index];
    //   if(key === element.key) {
    //     tmp = element;
    //     break;
    //   }
    // }
    return tmp;
  }

  // 移除选择项目
  removeItem(type, item) {
    if (type.id === "org") {
      this.removeTreeNodeItem(item);
      this.nzSelectChange();
      return;
    }
    if (type.id === "factor") {
      this.removeRenTreeNodeItem(item);
      return;
    }

    let tmp = {};
    let itemData = {};

    for (let index = 0; index < this.demoTabs.length; index++) {
      const element = this.demoTabs[index];
      if (type.id === element.id) {
        tmp = element;
        for (let j = 0; j < element.items.length; j++) {
          const child = element.items[j];
          if (item.id === child.value) {
            itemData = child;
            child.checked = false;
            break;
          }
        }
        break;
      }
    }
    const nodeList: NzTreeNode[] = this.nzTreeComparisonList.getCheckedNodeList();

    nodeList.forEach((node) => {
      let num: number = 0;
      if (node.key == item.id) {
        node.isChecked = false;
        node.parentNode.origin.children.forEach((child) => {
          if (child.checked) {
            num++;
          }
        });
        if (num == 0) {
          node.parentNode.isHalfChecked = false;
          node.parentNode.isChecked = false;
        } else {
          node.parentNode.isHalfChecked = true;
        }
      } else {
        if (node.children && node.children.length > 0) {
          node.children.forEach((child) => {
            if (child.key == item.id) {
              child.isChecked = false;
            }
            if (child.isChecked == true) {
              num++;
            }
          });
          if (num == 0) {
            node.isHalfChecked = false;
            node.isChecked = false;
          } else {
            node.isHalfChecked = true;
          }
        }
      }
    });

    this.updateSingleChecked(tmp, itemData, false);
  }

  recordChecks(nodeList) {
    let tmp: any[] = [];
    nodeList.items.forEach((item) => {
      if (item.checked) {
        tmp.push(item.label);
      }
    });
    // 暂未使用
    this.checkedValues = tmp;
    // update cost
    this.extInfo.cost = this.getTotal();
  }

  isCheckDisabled(tabData): boolean {
    let checkCount: number = 0;
    tabData.items.forEach((item) => {
      if (item.checked) {
        checkCount++;
      }
    });
    return tabData.isNorm && checkCount > 4;
  }
  isCheckDisabledInternalNorm(tabData): boolean {
    let checkCount: number = 0;
    tabData.items.forEach((item) => {
      if (item.checked) {
        checkCount++;
      }
    });
    return tabData.isInternalNorm && checkCount > 3;
  }
  getParamData() {
    let param = {};
    //直接调用 会影响排序 所有直接使用this.showList 但是不确定直接用this.showList会不会出问题 所以注释掉当前代码
    // let showList: any[] = this.getShowList();
    let showList: any[] = _.cloneDeep(this.showList);

    showList = _.filter(showList, function(o) {
      return o.itemList.length > 0;
    });
    showList.forEach((item) => {
      let children = _.map(item.itemList, (o) => o.id);
      param[item.id] = children;
    });
    return param;
  }

  getfactorListParam() {
    let param = {};
    //同getParamData
    // let showList: any[] =this.getShowList();
    let showList: any[] = _.cloneDeep(this.showList);
    let factorList = [];
    showList = _.filter(showList, function(o) {
      return o.itemList.length > 0;
    });
    showList.forEach((item) => {
      let children = _.map(item.itemList, (o) => o.id);
      param[item.id] = children;
    });
    param = param["factor"];

    this.demoList.map((children1) => {
      let demographicRootId;
      let demographicIds = [];
      if (children1.children.length !== 0) {
        children1.children.map((children2) => {
          if (children2.children.length !== 0) {
            children2.children.map((children3) => {
              if (
                _.find(param, function(o) {
                  return o === children3.key;
                })
              ) {
                demographicRootId = children1.key;
                demographicIds.push(children3.key);
              }
            });
          } else {
            if (
              _.find(param, function(o) {
                return o === children2.key;
              })
            ) {
              demographicRootId = children1.key;
              demographicIds.push(children2.key);
            }
          }
        });
      }
      if (demographicRootId) {
        factorList.push({
          demographicRootId: demographicRootId,
          demographicIds: demographicIds,
        });
      }
    });

    return factorList;
  }

  buildSingleParam(): any {
    let tmpLans: string[] = _.map(
      _.filter(this.lans, function(l) {
        return l.checked;
      }),
      (o) => o.value
    );
    // if(tmpLans.length === 0) {
    //   this.msg.error("没有选择报告语言");
    //   return;
    // }
    let tmp: any = this.getParamData();

    let factorList: any[] = [];
    factorList = this.getfactorListParam();
    // for (let i in tmp) {
    //   if (i !== 'org' && i !== 'norm' && i !== 'internalNorm' && i !== 'historyData') {
    //     factorList.push({ demographicRootId: i, demographicIds: tmp[i] });
    //   }
    // }
    // new factor change
    // let beforeFacts : any[] = [];
    // let fList : any[] = tmp["factor"];

    // if(fList && fList.length > 0) {

    //   for (let j = 0; j < fList.length; j++) {
    //     const fact = fList[j];
    //     let arr : string[] = _.split(fact, this.twoDash);
    //     beforeFacts.push( { pId: arr[0], subId: arr[1] } );
    //   }

    //   let afterFacts = _.groupBy(beforeFacts, "pId");

    //   for (let pId in afterFacts) {
    //     let subIds : string[] = [];
    //     let subObjs : any[] = afterFacts[pId];
    //     for (let j = 0; j < subObjs.length; j++) {
    //       const sub = subObjs[j];
    //       subIds.push(sub.subId);
    //     }
    //     factorList.push({ demographicRootId: pId, demographicIds: subIds });
    //   }

    // }

    // 常模不能为空
    // if(!tmp.norm) {
    //   this.msg.error("没有选择常模");
    //   return;
    // }

    let param = {
      projectId: this.projectId,
      organizationIds: tmp.org,
      normIds: tmp.norm,
      internalNormTypes: tmp.internalNorm,
      prismaHistoryDataIds: tmp.historyData,
      demographicContents: factorList,
      reportLanguages: tmpLans,
      isBatchCreate: this.isBatchCreate,
      fileName: this.fileName,
      orgaztionSinkingAnalysis:
        tmp.orgaztionSinkingAnalysis && tmp.orgaztionSinkingAnalysis.length > 0
          ? tmp.orgaztionSinkingAnalysis[0]
          : null,
    };
    return param;
    // this.surveySerivce.createPrismaReport(param).subscribe(res => {
    //   if (res.result.code === 0) {
    //     this.msg.success("创建报告成功。")
    //     this.ref.triggerOk();
    //   }
    // });
  }

  getTotal() {
    let arr: any[] = _.filter(this.demoTabs, function(o) {
      return o.id === "norm";
    });
    let norm: any = arr[0];
    let selItems: any[] = _.filter(norm.items, (o) => o.checked);
    let normIds: string[] = _.map(selItems, "value");

    let total: number = 0;
    for (let index = 0; index < this.normList.length; index++) {
      let element = this.normList[index];
      if (
        _.includes(normIds, element.id) &&
        !element.isFree &&
        !element.isBuy
      ) {
        let curPrice: number = 0;
        if (element.amount) {
          curPrice = element.amount;
        }
        total = total + curPrice;
      }
    }
    return total;
  }
  nzSelectChange() {
    // if (data.tab.nzTitle === '内外部对比') {
    // 如果组织架构选中的根组织 那么checkedIndex=1 如果选中了一级组织 那么checkedIndex=2
    // console.log(this.showList, "this.showList");
    let checkedIndex = null;
    this.orgList.forEach((item) => {
      if (item.checked && item.distance == 0) {
        checkedIndex = 1;
      } else if (item.checked && item.distance == 1) {
        checkedIndex = 2;
      } else {
        item.children.forEach((element) => {
          if (element.checked && element.distance == 1) {
            checkedIndex = 2;
          }
        });
      }
    });
    const arr = _.cloneDeep(this.comparisonList);
    arr.forEach((item) => {
      if (item.key == "internalNorm") {
        // iskey用来记录是否存在上一级或者一级部门选项 存在的话就在数组中存入固定的checkedIndex值
        let iskey = []; //

        item.children.forEach((element) => {
          if (element.key == "SUPERIOR_ORGANIZATION") {
            element.checked = checkedIndex == 1 ? false : element.checked;
            element.disabled = checkedIndex == 1;
            iskey.push(1);
            if (checkedIndex == 1) {
              this.showList.forEach((list) => {
                if (list.id == "internalNorm") {
                  list.itemList = _.filter(
                    list.itemList,
                    (o) => o.id !== "SUPERIOR_ORGANIZATION"
                  );
                }
              });
              this.demoTabs.forEach((obj) => {
                if (obj.id == "internalNorm") {
                  obj.items.forEach((element) => {
                    if (element.value == "SUPERIOR_ORGANIZATION") {
                      element.checked = false;
                    }
                  });
                }
              });
            }
          }
          if (element.key == "FIRST_LEVEL_ORGANIZATION") {
            element.checked = checkedIndex == 2 ? false : element.checked;
            element.disabled = checkedIndex == 2;
            iskey.push(2);
            if (checkedIndex == 2) {
              this.showList.forEach((list) => {
                if (list.id == "internalNorm") {
                  list.itemList = _.filter(
                    list.itemList,
                    (o) => o.id !== "FIRST_LEVEL_ORGANIZATION"
                  );
                }
              });

              this.demoTabs.forEach((obj) => {
                if (obj.id == "internalNorm") {
                  obj.items.forEach((element) => {
                    if (element.value == "FIRST_LEVEL_ORGANIZATION") {
                      element.checked = false;
                    }
                  });
                }
              });
            }
          }
          if (element.key == "COMPANY") {
            // 腾讯工具创建团队报告如果选择部门是活动根组织，禁止用户选内部常模公司整体
            if (
              this.paramData.reportType ===
              "TENCENT_INVESTIGATION_RESEARCH_CUSTOM"
            ) {
              const checkOrg =
                this.showList.find((val) => val.id === "org").itemList || [];
              const rootOrgId = this.orgList[0].id;
              // 腾讯工具创建团队报告如果选择部门是活动根组织，禁止用户选内部常模公司整体
              element.disabled =
                checkOrg.length === 1 && checkOrg[0].id === rootOrgId;
            }
          }
        });
        //如果内部常模中 上一级和一级都存在 或者存在一个且刚好满足组织架构当前选择项的禁用条件 那么checkedIndex保持不变 维持原有逻辑
        // 如果两项都没有或者不符合组织架构当前选择项的禁用条件 那么checkedIndex重置为null
        if (
          iskey.length == 2 ||
          (iskey.length == 1 && iskey[0] == checkedIndex)
        ) {
          checkedIndex = checkedIndex;
        } else {
          checkedIndex = null;
        }
        item.disabled = checkedIndex !== null;
        item.checked = checkedIndex === null ? item.checked : false;
      }
    });
    this.comparisonList = _.cloneDeep(arr);
  }
  /**
   * 自定义组织操作
   *@author:wangxiangxin
   *@Date:2023/09/26
   */
  customActions(list: Array<any>) {
    let allNodes = this.nzTreeComponent.getTreeNodes();

    this.timeHandle = setTimeout(() => {
      if (list) {
        allNodes.forEach((item) => {
          this.celDepartment(item);
          this.organizeData(item, list);
        });
      }
      let nodeList: NzTreeNode[] = this.nzTreeComponent.getCheckedNodeList();
      let tmp: any[] = [];
      for (let index = 0; index < nodeList.length; index++) {
        const element = nodeList[index].origin;
        tmp.push({
          id: element.key,
          name: element.title,
          isShowExcel: element.isShowExcel,
          isShowReport: element.isShowReport,
        });
      }
      this.selectedNodes = tmp;

      this.getShowList();
      clearTimeout(this.timeHandle);
    });
  }
  /**
   * 根据导入的数据 控制树形控件的选中
   *@author:wangxiangxin
   *@Date:2023/09/26
   */
  organizeData(node, list: Array<any>) {
    if (list.includes(node.key)) {
      if (!node.origin.isVirtual) {
        this.checkedKeys.push(node.key);
        node.isChecked = true;
      }
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.chooseDepartment(element, -1);
        });
      }
    } else {
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.organizeData(element, list);
        });
      }
    }
  }
  /**
   * 自定义人口操作
   *@author:wangxiangxin
   *@Date:2023/09/26
   */
  customActionsPopulation(list: Array<any>) {
    let tmp: any[] = [];
    let all = true; // 是否全选
    const nodeList: NzTreeNode[] = this.nzTreeComponentRenkou.getTreeNodes();
    nodeList.map((node1) => {
      if (node1.children.length !== 0) {
        // node1.isChecked = list.includes(node1.key);
        let isNode1Checked = true;
        node1.children.map((node2) => {
          if (node2.children.length !== 0) {
            let isNode2Checked = true;
            node2.children.map((node3) => {
              node3.isChecked =
                list.includes(node1.key) ||
                list.includes(node2.key) ||
                list.includes(node3.key);
              if (
                list.includes(node1.key) ||
                list.includes(node2.key) ||
                list.includes(node3.key)
              ) {
                node2.isHalfChecked = true;
                node1.isHalfChecked = true;
                if (isNode2Checked) {
                  isNode2Checked = true;
                }
                if (isNode1Checked) {
                  isNode1Checked = true;
                }
                tmp.push({
                  id: node3.key,
                  name: node3.title,
                  isShowExcel: node3.origin.isShowExcel,
                  isShowReport: node3.origin.isShowReport,
                });
              } else {
                all = false;
                isNode2Checked = false;
                isNode1Checked = false;
              }
            });
            node2.isChecked = isNode2Checked;
            //如果是全选中了 isHalfChecked改为false
            node2.isHalfChecked = isNode2Checked ? false : node2.isHalfChecked;
          } else {
            if (list.includes(node2.key) || list.includes(node1.key)) {
              tmp.push({
                id: node2.key,
                name: node2.title,
                isShowExcel: node2.origin.isShowExcel,
                isShowReport: node2.origin.isShowReport,
              });
              if (isNode1Checked) {
                isNode1Checked = true;
              }
              node1.isHalfChecked = true;
            } else {
              all = false;
              isNode1Checked = false;
            }
            node2.isChecked =
              list.includes(node2.key) || list.includes(node1.key);
          }
        });
        node1.isChecked = isNode1Checked;
        //如果是全选中了 isHalfChecked改为false
        node1.isHalfChecked = isNode1Checked ? false : node1.isHalfChecked;
      }
    });

    if (!all && tmp.length !== 0) {
      this.indeterminate = true;
    } else {
      this.indeterminate = false;
    }
    this.allChecked = all;
    this.selectedRenkouNodes = tmp;
    this.getShowList();
  }
  /**
   * 根据导入的数据 控制树形控件的选中
   *@author:wangxiangxin
   *@Date:2023/09/26
   */
  organizeDataPopulation(node, list: Array<any>) {
    if (list.includes(node.key)) {
      if (!node.origin.isVirtual) {
        this.checkedKeysRenkou.push(node.key);
        node.isChecked = true;
      }
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.organizeDataPopulation(element, list);
        });
      }
    } else {
      if (node.children.length !== 0) {
        node.children.forEach((element) => {
          this.organizeDataPopulation(element, list);
        });
      }
    }
  }
  updateList(params, isMin) {
    let timer = setTimeout(() => {
      if (!isMin) {
        this.checkedKeys = [];
        this.expandedNodes = [];
        if (params && params.length > 0) {
          let data = params[0];
          if (data.judge == "INCLUDE") {
            this.checkDepartment(data.chooseType);
          } else {
            this.customActions(data.name);
          }
        } else {
          this.checkDepartment(null);
        }
      } else {
        if (params && params.length > 0) {
          let data = params[0];
          this.customActionsPopulation(data.name);
        } else {
          this.updateAllChecked(false);
        }
        // this.customActionsPopulation(['1697126396649218049', "1697126396674383874", "1697126396703744001",
        //   "1697126396720521218", "1697126396762464257", "1697126396770852866", '1697132437587828738', '1697132437596217346', '1697132437600411650'])
        // // this.checkedKeysRenkou = []
        // // this.expandedRenNodes = []
        // // if (params && params.length > 0) {
        // //   let data = params[0]
        // //   // this.customActionsPopulation(data.name)
        // //   this.customActionsPopulation(['1697126396464668673', '1697126396733104129', '1697126396796018689', '1697126396867321857', '1697126396984762369', '1697126397022511106', '1697126397173506049'])
        // // } else {
        // //   this.updateAllChecked(false)
        // // }
      }
      clearInterval(timer);
    });
  }

  // 是否显示tag
  getIsShowDesc(type) {
    // 双视角调研 DP_INVESTIGATION_RESEARCH_CUSTOM
    // 组织敬业度调研 INVESTIGATION_RESEARCH_CUSTOM
    const dataShow = [
      "DP_INVESTIGATION_RESEARCH_CUSTOM",
      "INVESTIGATION_RESEARCH_CUSTOM",
    ];
    if (!dataShow.includes(this.paramData.reportType)) {
      return false;
    }
    // 组织架构 人口标签
    if (!["组织架构", "人口标签"].includes(type.name)) {
      return false;
    } else {
      // isShowExcel isShowReport 有不显示的才暂时
      const redTag =  type.itemList.filter(val => !(val.isShowExcel && val.isShowReport));
      let isShow = !!redTag.length;
      return isShow;
    }
  }
}
