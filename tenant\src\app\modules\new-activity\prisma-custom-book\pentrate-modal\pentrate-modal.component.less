.penetration-questions {
  // 弹窗按钮
  margin-left: 20px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #495970;
  line-height: 20px;
  cursor: pointer;

  span {
    margin-left: 6px;
  }

  .penetration-span {
    padding-right: 18px;
  }
}

.penetration-questions:hover {
  color: #409eff;
}

::ng-deep {
  .popover-header {
    // 关联详情浮层
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 575px;
    height: 55px;

    span {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #17314c;
      line-height: 25px;
    }
  }

  .popup-method {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 33px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #495970;
    line-height: 17px;
  }

  .popover-body {
    ul {
      width: 575px;
      height: 384px;
      overflow-y: auto;

      li {
        margin-bottom: 5px;
        background: #f5f6fa;
        padding: 0 15px;

        .condition-box {
          display: flex;
          justify-content: space-between;
          border-bottom: 1px solid #e6e6e6;
          padding: 14px 15px;

          .popover-tips {
            text-align: center;
            width: 34px;
            height: 21px;
            line-height: 21px;
            background: rgba(51, 114, 255, 0.08);
            border-radius: 2px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #3372ff;
            margin-right: 10px;
          }
        }

        .popover-text {
          flex: 1;
          p {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
          }
        }

        .result-box {
          display: flex;
          justify-content: space-between;
          padding: 14px 15px;
          border-bottom: 1px solid #e6e6e6;
          .popover-res-tips {
            text-align: center;
            width: 34px;
            height: 21px;
            line-height: 21px;
            background: rgba(36, 204, 158, 0.08);
            border-radius: 2px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #24cc9e;
            margin-right: 10px;
          }
        }

        .popover-del {
          width: 18px;
          height: 18px;
          cursor: pointer;
          background: url(../../../../../assets/images/org/del.png) no-repeat;
        }

        .popover-del:hover {
          background: url(../../../../../assets/images/org/del_hover.png);
        }
      }
    }

    .close-association {
      width: 100%;
      height: 38px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.penetra-modal {
  header {
    .penetra-modal-close {
      text-align: right;
      margin-bottom: 5px;

      .icon-penetra-close {
        font-size: 11px;
        margin-top: 7px;
        margin-right: 7px;
        cursor: pointer;
      }
    }

    .penetra-modal-title {
      position: absolute;
      right: 0;
      top: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 6px 0px;
      margin-right: 16px;
      // margin: 0 30px;
      // padding-bottom: 18px;
      // border-bottom: 1px solid #e6e6e6;
      .title-left {
        display: flex;
        align-items: center;

        span {
          font-size: 24px;
          font-family: PingFangSC-Light, PingFang SC;
          font-weight: 300;
          color: #5e5e5e;
          line-height: 33px;
        }

        .title-icon {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #5e5e5e;
          line-height: 20px;
          cursor: pointer;

          .iconfont {
            font-size: 14px;
          }
        }

        .title-icon:hover {
          color: #409eff;
          .iconfont {
            color: #409eff;
          }
        }
      }

      .associate-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 4px 15px;
        height: 30px;
        background: linear-gradient(90deg, #a1a9ff 0%, #bd97ff 100%);
        box-shadow: 0px 3px 8px 0px rgba(169, 163, 255, 0.5);
        border-radius: 15px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 22px;
        cursor: pointer;
      }
    }
  }

  .bottom {
    display: flex;
    align-items: center;
    height: 50px;
    background: #ffffff;
    box-shadow: 0px -7px 7px 0px rgba(0, 0, 0, 0.03);
    padding: 30px 0 83px 16px;
  }

  .penetra-modal-body {
    display: flex;
    height: calc(100vh - 215px) !important;
    > div {
      padding: 16px;
    }

    .left {
      width: 50%;
    }

    .right {
      width: 50%;
      background: #f8f8f8;
    }

    .filter-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
    }

    .tips {
      margin-left: 43px;
      margin-bottom: 10px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #aaaaaa;
      line-height: 17px;
    }

    .question-box {
      height: calc(100% - 77px);
      padding-right: 8px;
      overflow-y: auto;

      > li {
        display: flex;

        .label {
          display: flex;
          width: 18px;
          height: 18px;
        }

        > ul {
          margin-left: 14px;
          margin-bottom: 20px;

          li {
            display: flex;
            align-items: center;

            .icon-xiala {
              cursor: pointer;
              transform: rotate(-90deg);
              margin-right: 10px;
            }

            .turn {
              transform: rotate(0deg);
            }

            .icon-xiala,
            .turn {
              transition: all 0.1s linear;
            }
          }
        }
      }
    }
  }
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background-color: rgb(239, 239, 239);
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: #bfbfbf;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

.drawer-footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgb(232, 232, 232);
  padding: 10px 16px;
  text-align: right;
  left: 0px;
  background: #fff;
}

::ng-deep {
  .round-right-drawer2 {
    .ant-drawer-body {
      padding: 0px;
      // height: calc(100% - 108px);
      height: calc(100% - 106px);
      overflow: auto;
      // padding-bottom: 66px;
      // 滚动条
      scrollbar-color: auto;
      scrollbar-width: auto;
      overflow-y: overlay;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      // 滑块背景
      &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px transparent;
      }
      // 滑块
      &::-webkit-scrollbar-thumb {
        background-color: #999;
        border-radius: 6px;
        outline: none;
      }
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-title {
      font-weight: bold;
    }

    .ant-drawer-content {
      border-radius: 18px 0px 0px 18px;
    }
  }
}
::ng-deep .topic-contact-box .ant-tabs-nav .ant-tabs-tab {
  font-size: 16px !important;
}
::ng-deep .topic-contact-box .ant-tabs-bar {
  margin: 0px !important;
}
