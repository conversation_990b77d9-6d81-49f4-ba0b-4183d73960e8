.tipMenu {
  &_item {
    width: 100%;
    height: 48px;
    font-size: 14px;
    font-weight: 600;
    border-left: 2px solid #fff;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    > span {
      width: 100%;
      overflow: hidden; //溢出隐藏
      white-space: nowrap; //禁止换行
      text-overflow: ellipsis; //...
      text-align: center;
    }
    > div {
      > span {
        width: 95px;
        overflow: hidden; //溢出隐藏
        white-space: nowrap; //禁止换行
        text-overflow: ellipsis; //...
        text-align: center;
        display: block;
      }
    }
    cursor: pointer;
    &_icon {
      position: absolute;
      top: 14px;
      right: 10px;
    }
    &:hover {
      color: #409eff;
      background: rgba(64, 158, 255, 0.05);
      border-left: 2px solid #409eff;
    }
  }
  .active {
    color: #409eff;
    background: rgba(64, 158, 255, 0.05);
    border-left: 2px solid #409eff;
  }
  .child {
    font-weight: normal;
  }
}
