<div class="box">
  <div
    class="filter-title"
    nz-popover
    nzPopoverTrigger="click"
    [nzVisible]="FilterBoxVisiable"
    (nzVisibleChange)="popoverVisibleChange($event)"
    nzPopoverPlacement="bottomRight"
    [nzPopoverContent]="contentTemplate"
  >
    <span [class]="FilterBoxVisiable ? 'text-blue' : ''">高级筛选</span>
    <span [class]="FilterBoxVisiable ? 'arrow arrow-open' : 'arrow'">
      <svg
        viewBox="64 64 896 896"
        fill="currentColor"
        width="1em"
        height="1em"
        class="ng-tns-c12-3"
        data-icon="down"
        aria-hidden="true"
      >
        <path
          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        ></path>
      </svg>
    </span>
  </div>
  <ng-template #contentTemplate>
    <div class="filter_boxs min">
      <header>
        <span>高级筛选</span>
        <span
          >已选择 <a>{{ list.length }}</a> 个条件筛选</span
        >
      </header>
      <div class="header">
        <div>
          <span>满足下列</span>
          <nz-select [(ngModel)]="match" [nzSize]="'small'" class="condition">
            <nz-option nzValue="MATCH_ANY" nzLabel="任一"></nz-option>
            <nz-option nzValue="MATCH_ALL" nzLabel="所有"></nz-option>
          </nz-select>
          <span>条件</span>
        </div>
        <div (click)="addField($event)">
          <i nz-icon nzType="plus-circle" nzTheme="outline"></i>
          <span>添加条件</span>
        </div>
      </div>
      <div class="content">
        <div class="empty" *ngIf="list.length === 0">
          <img src="assets/images/filter-empty.png" alt="" />
          <p>暂无筛选条件，请先点击右上角的添加条件～</p>
        </div>
        <div class="option-box" *ngIf="list.length > 0">
          <div class="option-scroll">
            <div
              nz-row
              nzGutter="8"
              *ngFor="let data of list; let index = index"
            >
              <div nz-col nzSpan="6">
                <nz-select
                  [(ngModel)]="data.eventName"
                  [class]="
                    !errorFlag ? '' : data.eventName ? '' : ' border-error'
                  "
                  (ngModelChange)="optionChange(data.eventName, index)"
                  nzPlaceHolder="请选择"
                >
                  <nz-option
                    *ngFor="let option of advancedFilterObject.optionName"
                    [nzValue]="option.value"
                    [nzLabel]="option.name"
                  ></nz-option>
                </nz-select>
              </div>
              <div nz-col nzSpan="6">
                <ng-container *ngIf="data.eventName === 'demographic'">
                  <div class="upload_button">
                    <button
                      nz-button
                      [nzLoading]="downloading"
                      nzType="primary"
                      nzGhost
                      style="width: 100%;"
                      (click)="downloadTemplate($event)"
                    >
                      下载模板
                    </button>
                  </div>
                </ng-container>
                <ng-container
                  *ngIf="!!data.eventName && data.eventName !== 'demographic'"
                >
                  <nz-select
                    [(ngModel)]="data.judge"
                    [class]="
                      !errorFlag ? '' : data.judge ? '' : ' border-error'
                    "
                    nzPlaceHolder="请选择"
                  >
                    <nz-option
                      *ngFor="let option of advancedFilterObject.condition"
                      [nzValue]="option.value"
                      [nzLabel]="option.name"
                    ></nz-option>
                  </nz-select>
                </ng-container>
                <ng-container *ngIf="!data.eventName"
                  ><div class="option_empty">
                    <i nz-icon nzType="left" nzTheme="outline"></i>请选择
                  </div></ng-container
                >
              </div>
              <div nz-col nzSpan="10">
                <ng-container *ngIf="data.eventName === 'demographic'">
                  <div class="upload_button">
                    <nz-upload
                      [nzShowUploadList]="false"
                      [nzCustomRequest]="customReq"
                      [nzAccept]="fileType"
                      [nzBeforeUpload]="beforeUploadProduct"
                    >
                      <button
                        [nzLoading]="uploading"
                        [ngStyle]="{
                          'border-color': !errorFlag
                            ? ''
                            : !data.name
                            ? 'red'
                            : ''
                        }"
                        nzType="primary"
                        nzGhost
                        nz-button
                        [nzTitle]="uploadName"
                        nz-tooltip
                      >
                        {{ uploadName }}
                      </button>
                    </nz-upload>
                  </div>
                </ng-container>
                <ng-container
                  *ngIf="!!data.eventName && data.eventName !== 'demographic'"
                >
                  <nz-select
                    *ngIf="data.judge === 'INCLUDE'"
                    [(ngModel)]="data.chooseType"
                    [class]="
                      !errorFlag ? '' : data.chooseType ? '' : ' border-error'
                    "
                    nzPlaceHolder="请选择层级"
                  >
                    <nz-option
                      *ngFor="let opt of organizationDistanceList"
                      [nzValue]="opt.distance"
                      [nzLabel]="opt.name.zh_CN"
                    ></nz-option>
                  </nz-select>
                  <ng-container *ngIf="data.judge === 'NOT_INCLUDE'">
                    <div nz-row nzGutter="8">
                      <div nz-col nzSpan="12">
                        <div class="upload_button">
                          <button
                            nz-button
                            [nzLoading]="downloading"
                            nzType="primary"
                            nzGhost
                            (click)="downloadTemplate($event)"
                          >
                            下载模板
                          </button>
                        </div>
                      </div>
                      <div nz-col nzSpan="12">
                        <div class="upload_button">
                          <nz-upload
                            [nzShowUploadList]="false"
                            [nzCustomRequest]="customReq"
                            [nzAccept]="fileType"
                            [nzBeforeUpload]="beforeUploadProduct"
                          >
                            <button
                              [nzLoading]="uploading"
                              [ngStyle]="{
                                'border-color': !errorFlag
                                  ? ''
                                  : !data.name
                                  ? 'red'
                                  : ''
                              }"
                              nz-button
                              nzType="primary"
                              nzGhost
                              nz-tooltip
                              [nzTitle]="uploadName"
                            >
                              {{ uploadName }}
                            </button>
                          </nz-upload>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                  <ng-container *ngIf="!data.judge">
                    <div class="option_empty">
                      <i nz-icon nzType="left" nzTheme="outline"></i>请选择
                    </div>
                  </ng-container>
                </ng-container>
                <ng-container *ngIf="!data.eventName"
                  ><div class="option_empty">
                    <i nz-icon nzType="left" nzTheme="outline"></i>请选择
                  </div></ng-container
                >
              </div>
              <div nz-col nzSpan="2" style="text-align: center;">
                <i
                  class="option_del iconfont icon-icon_delete"
                  (click)="removeField(index, $event)"
                ></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <footer>
        <button nz-button nzType="default" (click)="clear()">
          清空
        </button>
        <button nz-button nzType="primary" (click)="submitForm()">
          确认
        </button>
      </footer>
    </div>
  </ng-template>
</div>
