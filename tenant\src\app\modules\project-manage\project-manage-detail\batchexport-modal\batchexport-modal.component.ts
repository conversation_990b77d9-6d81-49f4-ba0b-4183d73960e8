import { Component, OnInit, Input, OnD<PERSON>roy } from "@angular/core";
import { NzModalRef } from "ng-zorro-antd/modal";
import { TransferItem } from "ng-zorro-antd/transfer";
import { NzMessageService } from "ng-zorro-antd/message";

import { SurveyApiService } from "@src/modules/service/survey-api.service";
import { DownloadUtilService } from "@src/modules/service/download-util.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { Subscription } from "rxjs";
import { Router, NavigationEnd } from "@angular/router";
import { NzDrawerRef } from "ng-zorro-antd";
@Component({
  selector: "app-batchexport-modal",
  templateUrl: "./batchexport-modal.component.html",
  styleUrls: ["./batchexport-modal.component.less"],
})
export class BatchExportModalComponent implements OnInit, OnD<PERSON>roy {
  @Input() projectId: string;
  @Input() orgId: string;
  @Input() orgName: string;
  private routerSubscription: Subscription;

  constructor(
    private api: SurveyApiService,
    private msgServ: NzMessageService,
    private until: DownloadUtilService,
    private customMsg: MessageService,
    private drawerRef: NzDrawerRef,
    private router: Router
  ) {}

  organizationScopes: any[];
  demographicScopes: any[];
  organizations: any[] = [];
  demographics: any[] = [];
  conditions: any[] = [];
  labelIds: any[] = [];
  isLoading: boolean = false;
  organizationAllIds: any[] = [];
  demographicAllIds: any[] = [];
  isSelectAll: boolean = false;
  //保存参数
  params: any = {
    match: "MATCH_ALL",
    type: "ORGANIZATION",
    actionType: "MERGE",
    items: [],
  };
  ngOnInit() {
    this.params.projectId = this.projectId;
    this.loadList();
    this.routerSubscriptionFun();
  }
  routerSubscriptionFun() {
    // 订阅路由变化
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // 当路由结束时关闭抽屉
        this.drawerRef.close();
      }
    });
  }
  ngOnDestroy(): void {
    // 清理订阅
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
  // 初始化获取组织和人口学相关信息
  loadList() {
    let all = {
      id: "0",
      name: {
        zh_CN: "全选",
      },
      nameCh: "全选",
    };
    this.api
      .getBatchExportAnswerRateBasicData(this.projectId)
      .subscribe((res) => {
        if (res.result.code === 0) {
          this.organizationScopes = res.data.organizationScopes;
          this.demographicScopes = res.data.demographicScopes;
          this.demographics.push(all);
          this.organizations.push(all);
          res.data.demographics.forEach((item) => {
            this.demographics.push(item);
            this.demographicAllIds.push(item.id);
          });
          res.data.organizations.forEach((item) => {
            this.organizations.push(item);
            this.organizationAllIds.push(item.id);
          });
        }
      });
  }
  //添加条件事件
  addCondition() {
    if (this.conditions.length >= 2) {
      // this.msgServ.warning("批量导出数据的条件不能超过两个");
      this.customMsg.open("warning", "批量导出数据的条件不能超过两个");
      return false;
    }
    this.conditions.push({
      type: null,
      scope: null,
      levelTypeList: null,
      labelList: null,
      labelIds: [],
      isSelectAll: false,
    });
  }
  deleteCodition(i) {
    this.conditions.splice(i, 1);
    this.params.items.splice(i, 1);
  }
  subjectTypeChange(e, index) {
    this.conditions[index].type = e;
    this.conditions[index].scope = null;
    this.conditions[index].labelIds = null;
    this.conditions[index].isSelectAll = false;
    this.params.items.splice(index, 1);
    this.labelIds = [];
    if (e === "ORGANIZATION") {
      this.conditions[index].levelTypeList = [...this.organizationScopes];
      this.conditions[index].labelList = [...this.organizations];
    } else {
      this.conditions[index].levelTypeList = [...this.demographicScopes];
      this.conditions[index].labelList = [...this.demographics];
    }
  }
  scopeChange(e, index) {
    this.conditions[index].scope = e;

    let item = {
      type: this.conditions[index].type,
      scope: this.conditions[index].scope,
      demographicRootIds: [],
      organizationDistances: [],
    };
    if (this.conditions[index].type === "ORGANIZATION") {
      item.organizationDistances = this.labelIds;
    } else {
      item.demographicRootIds = this.labelIds;
    }
    if (
      this.params.items[index] == null ||
      this.params.items[index] == undefined
    ) {
      this.params.items.push(item);
    } else {
      this.params.items[index] = item;
    }
  }
  labelChange(e, index) {
    if (
      this.conditions[index].scope == null ||
      this.conditions[index].scope == ""
    ) {
      // this.msgServ.warning("请选择当前导出条件中待查询范围");
      this.customMsg.open("warning", "请选择当前导出条件中待查询范围");
      this.conditions[index].labelIds = [];
      return false;
    }

    if (!this.conditions[index].isSelectAll && e.indexOf("0") != -1) {
      this.conditions[index].isSelectAll = true;
      if (this.conditions[index].type === "ORGANIZATION") {
        this.labelIds = [...this.organizationAllIds];
        this.conditions[index].labelIds = [...this.organizationAllIds];
      } else {
        this.labelIds = [...this.demographicAllIds];
        this.conditions[index].labelIds = [...this.demographicAllIds];
      }
      this.conditions[index].labelIds.push("0");
    } else if (this.conditions[index].isSelectAll && e.indexOf("0") == -1) {
      this.conditions[index].isSelectAll = false;
      this.labelIds = [];
      this.conditions[index].labelIds = [];
    } else if (!this.conditions[index].isSelectAll) {
      if (this.conditions[index].type === "ORGANIZATION") {
        if (e.length == this.organizationAllIds.length) {
          this.conditions[index].isSelectAll = true;
          this.labelIds = [...this.organizationAllIds];
          this.conditions[index].labelIds.push("0");
        } else {
          this.labelIds = this.conditions[index].labelIds;
        }
      } else {
        if (e.length == this.demographicAllIds.length) {
          this.conditions[index].isSelectAll = true;
          this.labelIds = [...this.demographicAllIds];
          this.conditions[index].labelIds.push("0");
        } else {
          this.labelIds = this.conditions[index].labelIds;
        }
      }
    } else if (this.conditions[index].isSelectAll && e.indexOf("0") != -1) {
      this.conditions[index].isSelectAll = false;
      this.conditions[index].labelIds.splice(e.indexOf("0"), 1);
      this.labelIds = this.conditions[index].labelIds;
    }

    let item = {
      type: this.conditions[index].type,
      scope: this.conditions[index].scope,
      demographicRootIds: [],
      organizationDistances: [],
    };
    if (this.conditions[index].type === "ORGANIZATION") {
      item.organizationDistances = this.labelIds;
    } else {
      item.demographicRootIds = this.labelIds;
    }
    if (
      this.params.items[index] == null ||
      this.params.items[index] == undefined
    ) {
      this.params.items.push(item);
    } else {
      this.params.items[index] = item;
    }
  }
  //点击确定执行导出方法
  exportChange() {
    //校验传入参数
    if (this.params.match == "") {
      // this.msgServ.warning("请选择满足条件的范围");
      this.customMsg.open("warning", "请选择满足条件的范围");
      return false;
    }
    if (this.params.type == "") {
      // this.msgServ.warning("请选择主体的范围");
      this.customMsg.open("warning", "请选择主体的范围");
      return false;
    }
    if (this.params.actionType == "") {
      // this.msgServ.warning("请选择导出结果数据的方式");
      this.customMsg.open("warning", "请选择导出结果数据的方式");
      return false;
    }
    if (this.params.items == null || this.params.items.length == 0) {
      // this.msgServ.warning("请选择批量导出数据的条件");
      this.customMsg.open("warning", "请选择批量导出数据的条件");
      return false;
    }
    if (this.params.items.length > 2) {
      // this.msgServ.warning("批量导出数据的条件不能超过两个");
      this.customMsg.open("warning", "批量导出数据的条件不能超过两个");
      return false;
    }
    this.isLoading = true;
    this.api.batchExportAnswerRate(this.params).subscribe((res) => {
      this.isLoading = false;
      //下载文件
      let body = res.body;
      console.log(res);

      if (body.type && body.type === "application/json") {
        let that = this;
        const reader = new FileReader();
        reader.readAsText(body, "utf-8");
        reader.onload = () => {
          // 处理报错信息
          // JSON.parse(reader.result) 拿到报错信息
          let resp: any = JSON.parse(reader.result + "");
          let code: number = resp.result.code;
          let errMsg: string = resp.result.message;
          console.log("errMsg = " + errMsg);
          if (code !== 0) {
            // that.msgServ.error(`${errMsg}`);
            this.customMsg.open("error", errMsg);
          }
        };
      }
      const link = document.createElement("a");
      const blob = new Blob([res.body], { type: "application/vnd.ms-excel" });
      let fileName = res.headers
        .get("Content-Disposition")
        .split(";")[1]
        .split("filename=")[1];
      const fileNameUnicode = res.headers
        .get("Content-Disposition")
        .split("filename*=")[1];
      // 当存在 filename* 时，取filename* 并进行解码（为了解决中文乱码问题）
      if (fileName) {
        fileName = decodeURIComponent(fileName);
        // .split('\'\'')[1]
      }
      if (fileNameUnicode) {
        fileName = decodeURIComponent(fileNameUnicode.split("''")[1]);
      }
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }
  //清空当前已选条件
  clearChange() {
    this.params.match = "MATCH_ALL";
    this.params.type = "ORGANIZATION";
    this.params.actionType = "MERGE";
    this.params.items = [];
    this.conditions = [];
    this.addCondition();
  }
}
