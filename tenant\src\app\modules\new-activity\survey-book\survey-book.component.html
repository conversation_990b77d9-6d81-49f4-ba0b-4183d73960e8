<div style="background:#F5F6FA; height: 100%;" id="orgDiv">
  <div class="content client-width">
    <div class="body">
      <div style="display: flex; align-items: center;">
        <div class="title">
          定制题本
        </div>
        <div class="searchDiv">
          <nz-input-group [nzPrefix]="IconSearch">
            <input
              class="input-search"
              type="text"
              nz-input
              placeholder="请输入关键词"
              [(ngModel)]="keyWord"
              (keydown.enter)="search()"
            />
          </nz-input-group>
          <ng-template #IconSearch>
            <i
              nz-icon
              nzType="search"
              class="icon-search"
              (click)="search()"
            ></i>
          </ng-template>
        </div>
      </div>

      <div
        style="display: flex; align-items: center; justify-content: space-between"
      >
        <div class="tab">
          <nz-tabset
            [nzSize]="tabSize"
            [(nzSelectedIndex)]="lanIndex"
            (nzSelectChange)="changeLan()"
          >
            <nz-tab nzTitle="中文"> </nz-tab>
            <nz-tab nzTitle="ENG"> </nz-tab>
          </nz-tabset>
        </div>

        <div *ngIf="projectInfo" class="projName">
          {{ projectInfo?.name }}
        </div>

        <div class="action">
          <div *ngIf="!typeshow">
            <app-btn
              *ngIf="canEdit()"
              [text]="'新增题目'"
              [image]="'./assets/images/org/add.png'"
              [hoverColor]="'#409EFF'"
              [hoverImage]="'./assets/images/org/add_hover.png'"
              (btnclick)="add('0')"
            >
            </app-btn>

            <app-btn
              [text]="'批量修订'"
              [image]="'./assets/images/org/edit.png'"
              [hoverColor]="'#409EFF'"
              [hoverImage]="'./assets/images/org/edit_hover.png'"
              (btnclick)="batchEdit()"
            >
            </app-btn>
          </div>
        </div>
      </div>

      <div class="scroll topiclist">
        <ng-container
          *ngFor="let item of topicList; let i = index; last as isLast"
        >
          <div
            *ngIf="item.type !== 'PAGE_SPLIT'"
            class="topic"
            [ngClass]="{ double: item.active, last_topic: isLast }"
            (mouseenter)="mouseState(item, true)"
            (mouseleave)="mouseState(item, false)"
          >
            <div class="ques">
              <div class="quesName">
                <span>{{ i + 1 }}.&nbsp; </span>
                <span
                  *ngIf="item.replaceName"
                  [innerHTML]="item.replaceName[lan] | html"
                ></span>
              </div>

              <div *ngIf="!typeshow">
                <!-- *ngIf="item.canEdit" -->
                <app-btn
                  [text]="''"
                  [image]="'./assets/images/org/edit.png'"
                  [hoverColor]="'#409EFF'"
                  [hoverImage]="'./assets/images/org/edit_hover.png'"
                  (btnclick)="edit(item)"
                >
                </app-btn>
                <app-btn
                  *ngIf="!item.isForceSelect && canEdit()"
                  [text]="''"
                  [image]="'./assets/images/org/del.png'"
                  [hoverColor]="'#409EFF'"
                  [hoverImage]="'./assets/images/org/del_hover.png'"
                  nz-popconfirm
                  [nzPopconfirmTitle]="'确定要删除题目？'"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="delete(item.id)"
                >
                </app-btn>
              </div>
            </div>
            <div class="optionlist">
              <ng-container
                *ngFor="
                  let option of item.options.options;
                  let i = index;
                  let isLast = last
                "
              >
                <div [ngClass]="{ marg: !isLast }">
                  <label nz-radio [(ngModel)]="checked">
                    {{ option.name[lan] }}
                  </label>
                </div>
              </ng-container>

              <div
                style="border:dashed 1px grey; width: 100%; padding: 5px 15px; text-align: center;"
                *ngIf="item.type === 'ESSAY_QUESTION'"
              >
                {{ lan === "zh_CN" ? "开放题" : "Open question" }}
              </div>
            </div>
          </div>
        </ng-container>
      </div>

      <!-- 分页控件 -->
      <!-- <div style="display: flex; justify-content: flex-end; margin: 15px 0; "> -->
    </div>
  </div>

  <div
    style="width:100%; position: fixed; bottom: 0px; margin-top: 15px; background-color: white; text-align: center; height:60px; box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);"
  >
    <div
      style="max-width: 1200px; display: flex; justify-content: flex-end; margin:auto; height:100%; align-items: center;"
    >
      <button nz-button class="iptBtn" (click)="bookConfirm()" appDisableTime>
        <span>确认</span>
      </button>
    </div>
  </div>
</div>
