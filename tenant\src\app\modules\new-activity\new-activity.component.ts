import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import _ from "lodash";
import { NzMessageService } from "ng-zorro-antd/message";
import { NewActivityService } from "./new-activity.service";
import { MessageService } from "@src/shared/custom-message/message-service.service";
import { PermissionService } from "../service/permission-service.service";

@Component({
  selector: "app-new-activity",
  templateUrl: "./new-activity.component.html",
  styleUrls: ["./new-activity.component.less"],
})
export class NewActivityComponent implements OnInit {
  title: string = "新建活动";
  projectId: string; //活动id
  permission: boolean = this.permissionService.isPermission();
  isVisible: Boolean = false;
  islike = false;
  mapVisible: Boolean = false;
  ISPRODUCT = null;
  questionnaireTypeIds = []; //类型
  questionnaireTypes = [];
  sceneIds = []; //场景
  sceneTypes = [];
  questionnaireSceneTypeEnum = "STANDARD_PRODUCT";
  toolstands = [];
  mylikelist = [];
  chooselistids = [];
  choosecards = [];
  choosecardslike = [];
  loadingspin = false;
  keyWord = "";
  likekeyWord = "";
  keyWordlist = [];
  releasefash = true;
  showmock = false;
  noviceGuidance = false;
  returnlist = [];
  transverse = [
    {
      name: "场景/类型",
      code: "",
    },
    {
      name: "校招",
      code: "",
    },
    {
      name: "社招",
      code: "",
    },
    {
      name: "盘点",
      code: "",
    },
    {
      name: "选拔",
      code: "",
    },
    {
      name: "培养",
      code: "",
    },
    {
      name: "职业指导",
      code: "",
    },
    {
      name: "团队建设",
      code: "",
    },

    {
      name: "敬业度",
      code: "",
    },
  ];
  portrait = [
    {
      name: "职业性格",
      isxiao: true,
      isShe: true,
      ispan: true,
      isxuan: true,
      ispei: true,
      isteam: true,
    },
    {
      name: "成就动机",
      isxiao: true,
      isShe: true,
      ispan: true,
      ispei: true,
      isjob: true,
    },
    {
      name: "危机筛查",
      isxiao: true,
      isShe: true,
    },
    {
      name: "领导力",
      isShe: true,
      ispan: true,
      isxuan: true,
      ispei: true,
    },
    {
      name: "行为反馈",
      ispan: true,
      isxuan: true,
      ispei: true,
    },
    {
      name: "价值观",
      ispan: true,
      ispei: true,
      isjob: true,
    },
    {
      name: "认知能力",
      isxiao: true,
      isShe: true,
    },
    {
      name: "胜任力",
      ispan: true,
      isxuan: true,
      ispei: true,
      isjob: true,
    },

    {
      name: "性格与职业",

      isjob: true,
    },
    {
      name: "性格类型",
      isteam: true,
      ispei: true,
    },
    {
      name: "人才画像",
      isxiao: true,
      isShe: true,
    },
    {
      name: "敬业度",
      isprisma: true,
    },
  ];
  tabs = [
    {
      name: "标准产品",
      key: "STANDARD_PRODUCT",
      icon: "assets/images/chanpind_g.png",
      img: "assets/images/chanpind.png",
    },
    {
      name: "组合产品",
      key: "SOLUTION",
      icon: "assets/images/chanpinm_g.png",
      img: "assets/images/chanpinm.png",
    },
  ];
  tabshowindex = 0;

  myListTmp: any[] = [];
  selectIdone = "";
  SimpleSpin = false;
  hotsale = null;
  online = null;

  PageIndex = 1;
  PageTotal = 100;
  PageSize = 9;
  selectlogs = [];

  OrderBy = "COMPREHENSIVE_DESC";
  PageNumber = 0;
  OrderByshow = true;
  unique = null;
  indeterminate = false;
  childrenlist = [];

  Breadcrumbs = [
    {
      path: "/home",
      name: "首页",
      Highlight: false,
    },
    {
      path: "",
      name: "新建活动",
      Highlight: true,
    },
  ];
  constructor(
    private api: NewActivityService,
    private msg: NzMessageService,
    private router: Router,
    private customMsg: MessageService,
    public permissionService: PermissionService,
  ) {}

  ngOnInit(): void {
    this.getprojectlist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds
    );
  }
  getprojectlist(type, Qids, Sids) {
    let parmas = {
      cornerMarks: [],
      filterName: this.keyWord,
      orderBy: this.OrderBy,
      questionnaireSceneTypeEnum: type,
      questionnaireTypeIds: Qids,
      sceneIds: Sids,
      page: {
        current: this.PageIndex,
        size: this.PageSize,
      },
    };
    this.api.getPermissiontoollist(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.PageTotal = res.data.page.total;

        this.PageNumber = Math.ceil(this.PageTotal / this.PageSize);

        this.loadingspin = false;
        res.data.sceneTypes.forEach((res) => {
          res.checked = false;
          res.indeterminate = false;
          if (res.children) {
            res.children.forEach((val) => {
              val.label = val.name;
              val.value = val.id;
              val.checked = false;
            });
          }
        });
        this.sceneTypes = res.data.sceneTypes;
        res.data.questionnaireTypes.forEach((res) => {
          res.checked = false;
        });
        this.questionnaireTypes = res.data.questionnaireTypes;
        this.toolstands = res.data.standardQuestionnaireResultVOs;
        this.sceneTypes.unshift({
          checked: true,
          id: "all",
          name: "全部",
          children: [],
        });
        this.keyWordlist = this.toolstands;
        if (this.toolstands.length == 0) {
          this.releasefash = true;
        }
        this.SimpleSpin = false;
      }
      // sceneTypes  场景
      // questionnaireTypes 类型
    });
  }
  getToollist(type, Qids, Sids, ktype) {
    let parmas = {
      cornerMarks: this.selectlogs,
      filterName: this.keyWord,
      orderBy: this.OrderBy,
      questionnaireSceneTypeEnum: type,
      questionnaireTypeIds: Qids,
      sceneIds: Sids,
      page: {
        current: this.PageIndex,
        size: this.PageSize,
      },
    };
    this.api.getPermissiontoollist(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.PageTotal = res.data.page.total;
        this.PageNumber = Math.ceil(this.PageTotal / this.PageSize);
        this.loadingspin = false;
        this.toolstands = res.data.standardQuestionnaireResultVOs;
        this.choosecards.forEach((item) => {
          this.toolstands.forEach((res) => {
            if (res.id == item.id) {
              res.totalprice = item.totalprice;
              res.reportProjects = item.reportProjects;
            }
          });
        });

        if (this.toolstands.length == 0) {
          this.releasefash = true;
        }

        this.keyWordlist = this.toolstands;
        if (ktype == 1) {
          //类型合集
          this.questionnaireTypes = res.data.questionnaireTypes;
        }
      }
    });
  }
  removalchildren(arr) {
    let result = [];
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      if (!obj[arr[i]]) {
        result.push(arr[i]);
        obj[arr[i]] = true;
      }
    }
    return result;
  } //去重

  updateSingleChecked(item) {
    if (this.childrenlist.every((item) => !item.checked)) {
      this.sceneTypes[this.unique].indeterminate = false;
      this.sceneTypes[this.unique].checked = false;
    } else if (this.childrenlist.every((item) => item.checked)) {
      this.sceneTypes[this.unique].checked = true;
      this.sceneTypes[this.unique].indeterminate = false;
    } else {
      this.sceneTypes[this.unique].indeterminate = true;
    }

    this.Mapselectlist(this.sceneTypes);
  }
  Mapselectlist(data) {
    let newselectlist = [];
    data.forEach((item) => {
      if (item.indeterminate || item.checked) {
        newselectlist.push(item.id);
        if (item.children) {
          item.children.forEach((res) => {
            if (res.checked) {
              newselectlist.push(res.id);
            }
          });
        }
      }
    });
    this.PageIndex = 1;
    this.questionnaireTypeIds = [];
    this.loadingspin = true;
    this.sceneIds = this.removalchildren(newselectlist);
    this.getToollist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds,
      1
    );
  }
  nzOnChangechildren(e) {
    this.sceneTypes.forEach((item) => {
      if (item.id == "all") {
        item.checked = false;
      }
    });
  }
  nzOnChangeStype(e) {
    if (this.selectIdone == "all") {
      this.sceneIds = [];
      this.sceneTypes.forEach((res) => {
        res.indeterminate = false;
        if (res.children) {
          res.children.forEach((item) => {
            item.checked = false;
          });
        }
      });
      this.PageIndex = 1;
      this.questionnaireTypeIds = [];
      this.loadingspin = true;
      this.getToollist(
        this.questionnaireSceneTypeEnum,
        this.questionnaireTypeIds,
        this.sceneIds,
        1
      );
    } else {
      this.Mapselectlist(this.sceneTypes);
    }
  }
  ngModelChange(id, type, i) {
    this.selectIdone = id;
    if (!type && id == "all") {
      this.sceneTypes.forEach((res) => {
        if (res.id != id) {
          res.checked = false;
        }
      });
    }
    if (id != "all") {
      this.sceneTypes.forEach((res) => {
        if (res.id == "all") {
          res.checked = false;
        }
      });
    }
    this.unique = i;
    this.sceneTypes[i].indeterminate = false;
    if (!this.sceneTypes[i].checked) {
      this.sceneTypes[i].children = this.sceneTypes[i].children.map((item) => ({
        ...item,
        checked: true,
      }));
    } else {
      this.sceneTypes[i].children = this.sceneTypes[i].children.map((item) => ({
        ...item,
        checked: false,
      }));
    }
    if (this.sceneTypes[i].children) {
      this.childrenlist = this.sceneTypes[i].children;
    } else {
      this.childrenlist = [];
    }
  }
  uniqueshow(id, type, i) {
    this.unique = i;
    if (this.sceneTypes[i].children) {
      this.childrenlist = this.sceneTypes[i].children;
    } else {
      this.childrenlist = [];
    }
  }

  nzOnChangeQtype(e) {
    this.PageIndex = 1;
    this.questionnaireTypeIds = e;
    this.loadingspin = true;
    this.getToollist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds,
      2
    );
  }

  //更新后的新建活动
  getmylike(keyword?) {
    if (!keyword) {
      this.likekeyWord = null;
    }
    this.keyWord = "";
    this.isVisible = true;
    let parmas = {
      filterName: keyword,
    };
    this.api.getPermissionmybestlike(parmas).subscribe((res) => {
      if (res.result.code == 0) {
        this.myListTmp = res.data.standardQuestionnaireResultVOs;
        this.mylikelist = res.data.standardQuestionnaireResultVOs;
      }
    });
  }

  gettoolmap() {
    this.mapVisible = true;
  }
  likeCancel() {
    this.isVisible = false;
  }
  likeOk() {
    this.islike = true;
    if (this.choosecardslike.length != 0) {
      this.choosecards = this.choosecardslike;
      this.submit();
    } else {
      // this.msg.error("请最少选择一个活动工具！");
      this.customMsg.open("error", "请最少选择一个活动工具！");
    }
  }
  mapCancel() {
    this.mapVisible = false;
  }

  selectcard(params) {
    if (params.nolike == "no") {
      if (params.sandtype) {
        this.choosecards = params.data;
      } else {
        if (this.choosecards.length != 0) {
          this.choosecards[0].isStandalone ? (this.choosecards = []) : "";
        }
        this.choosecards.unshift(...params.data);
        this.choosecards = this.removaldata(this.choosecards);
        this.choosecards.forEach((item) => {
          params.data.forEach((res) => {
            if (item.id == res.id) {
              item.reportProjects = res.reportProjects;
              item.totalprice = res.totalprice;
            }
          });
        });
      }
      this.choosecards.forEach((item, index) => {
        if (params.cancelIds == item.id) {
          this.choosecards.splice(index, 1);
        }
      });
    } else {
      this.choosecardslike = params.data;
    }
  }

  removaldata(arr) {
    let result = [];
    let obj = {};
    for (var i = 0; i < arr.length; i++) {
      if (!obj[arr[i].id]) {
        result.push(arr[i]);
        obj[arr[i].id] = true;
      }
    }
    return result;
  } //去重

  choosetitle(e) {
    if (e == "STANDARD_PRODUCT") {
      this.questionnaireSceneTypeEnum = "STANDARD_PRODUCT";
      this.SimpleSpin = true;
    } else {
      this.questionnaireSceneTypeEnum = "SOLUTION";
      this.SimpleSpin = true;
    }
    this.questionnaireTypeIds = [];
    this.sceneIds = [];
    this.chooselistids = [];
    this.getprojectlist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds
    );
  }
  clearchoosed(e) {
    if (e == 0) {
      this.questionnaireSceneTypeEnum = "STANDARD_PRODUCT";
    } else {
      this.questionnaireSceneTypeEnum = "SOLUTION";
    }
    this.sceneTypes.forEach((item) => {
      if (item.children.length != 0) {
        item.children.forEach((res) => {
          res.checked = false;
        });
      }
    });
    this.loadingspin = true;
    this.questionnaireTypeIds = [];
    this.sceneIds = [];
    this.choosecards = [];
    this.hotsale = null;
    this.online = null;
    this.selectlogs = [];
    this.keyWord = "";
    this.unique = null;
    this.childrenlist = [];
    this.sceneTypes.forEach((item) => {
      if (item.children.length != 0) {
        item.children.forEach((res) => {
          res.checked = false;
        });
      }
    });
    this.OrderBy = "COMPREHENSIVE_DESC";
    this.getprojectlist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds
    );
  }

  getrelease() {
    this.questionnaireTypeIds = [];
    this.choosecards = [];
    this.sceneIds = [];
    this.hotsale = null;
    this.online = null;
    this.selectlogs = [];
    this.keyWord = "";
    this.unique = null;
    this.childrenlist = [];
    this.OrderBy = "COMPREHENSIVE_DESC";
    this.getprojectlist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds
    );
  }
  submit() {
    if (this.choosecards.length != 0) {
      let newArr = [];
      this.choosecards.forEach((item) => {
        if (item.reportType === "BLANK_CUSTOM") {
          //把空白问卷放在第一位
          newArr.unshift(JSON.parse(JSON.stringify(item)));
        } else {
          newArr.push(JSON.parse(JSON.stringify(item)));
        }
      });
      this.choosecards = newArr;
      if (this.choosecards.length == 1) {
        if (this.choosecards[0].type == "EMPLOYEE_ENGAGEMENT") {
          //判断敬业度
          let data = {
            isShowOrganization: this.choosecards[0].isShowOrganization,
            isShowQuestionBook: this.choosecards[0].isShowQuestionBook,
            standardQuestionnaireDimensionDTO: [],
          };
          this.choosecards.forEach((res) => {
            res.styles = [];
            res.reportProjects.forEach((val) => {
              if (val.checked) {
                res.styles.push(val.style);
              }
            });
            data.standardQuestionnaireDimensionDTO.push({
              name: res.name,
              questionnaireId: res.id,
              standardQuestionnaireDimensionIds: [],
              styles: res.styles,
              description: res.description,
            });
          });
          localStorage.setItem(
            "standardQuestionnaireDimensionDTO",
            JSON.stringify(data)
          );
          localStorage.setItem("backurl", this.router.routerState.snapshot.url);
          this.router.navigate(["new-prisma"]);
        } else if (this.choosecards[0].reportType.indexOf("TIP") != -1) {
          let reportProjectselect = false;
          let standardDimensionselect = false;
          this.choosecards.forEach((item) => {
            item.reportProjects.forEach((val) => {
              if (val.checked) {
                reportProjectselect = true;
              }
            });
            item.standardDimensionResultVOList.forEach((val) => {
              if (val.checked) {
                standardDimensionselect = true;
              }
            });
          });
          if (this.choosecards[0].reportType == "TIP_NEW_2") {
            if (reportProjectselect && standardDimensionselect) {
              localStorage.setItem(
                "backurl",
                this.router.routerState.snapshot.url
              );
              this.router.navigate(["new-create"]);
              localStorage.setItem(
                "noprismadata",
                JSON.stringify(this.choosecards)
              );
            }
          } else {
            if (reportProjectselect) {
              localStorage.setItem(
                "backurl",
                this.router.routerState.snapshot.url
              );
              this.router.navigate(["new-create"]);
              localStorage.setItem(
                "noprismadata",
                JSON.stringify(this.choosecards)
              );
            }
          }
        } else {
          localStorage.setItem("backurl", this.router.routerState.snapshot.url);
          this.router.navigate(["new-create"]);
          localStorage.setItem(
            "noprismadata",
            JSON.stringify(this.choosecards)
          );
        }
      } else {
        localStorage.setItem("backurl", this.router.routerState.snapshot.url);
        this.router.navigate(["new-create"]);
        localStorage.setItem("noprismadata", JSON.stringify(this.choosecards));
      }
      sessionStorage.setItem("activepage", null);
    } else {
      // this.msg.error("请最少选择一个活动工具！");
      this.customMsg.open("error", "请最少选择一个活动工具！");
    }
  }
  getcardone() {
    this.PageIndex = 1;
    this.getToollist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds,
      2
    );
  }
  searchMy() {
    this.PageIndex = 1;
    this.getmylike(this.likekeyWord);
    // this.getToollist(this.questionnaireSceneTypeEnum, this.questionnaireTypeIds, this.sceneIds, 2)
  }
  getReturn(e) {
    // this.returnlist = e
    if (e.length == 0) {
      this.returnlist = [];
    } else {
      this.returnlist = e;
    }
  }

  getnewlead() {
    this.showmock = true;
    this.noviceGuidance = true;
  }
  closed() {
    this.showmock = false;
    this.noviceGuidance = false;
  }

  nzSelectChange(data) {
    this.tabshowindex = data.index;
    let keyname = this.tabs[this.tabshowindex].key;
    this.hotsale = null;
    this.online = null;
    this.selectlogs = [];
    this.OrderBy = "COMPREHENSIVE_DESC";
    this.keyWord = "";
    this.PageIndex = 1;
    this.unique = null;
    this.sceneTypes.forEach((item) => {
      if (item.children.length != 0) {
        item.children.forEach((res) => {
          res.checked = false;
        });
      }
    });
    this.childrenlist = [];
    this.choosetitle(keyname);
  }

  onClose(i) {
    this.choosecards[i].checked = false;
    this.choosecards[i].reportProjects.forEach((item) => {
      item.checked = false;
    });
    if (this.choosecards[i].standardDimensionResultVOList) {
      this.choosecards[i].standardDimensionResultVOList.forEach((item) => {
        item.checked = false;
        item.reportTypeshow = true;
        item.showcard = true;
        item.reportTypeshowMhs = true;
        item.showcardMhs = true;
      });
    }
    this.choosecards[i].totalprice = 0;
    this.choosecards[i].isDiscount = false;

    this.choosecards.splice(i, 1);
  }

  showsortup(type) {
    this.OrderBy = type;
    this.getToollist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds,
      2
    );
  }

  selectlog(e) {
    this.selectlogs = e;
    this.PageIndex = 1;
    this.getToollist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds,
      2
    );
  }

  addnext(type) {
    // return
    if (type == "right") {
      if (this.PageIndex < this.PageNumber) {
        this.PageIndex++;
      }
    } else {
      if (this.PageIndex > 1) {
        this.PageIndex--;
      }
    }
    if (this.PageNumber == 1) {
      // this.msg.warning("暂无更多数据！");
      this.customMsg.open("warning", "暂无更多数据");
    } else {
      this.getToollist(
        this.questionnaireSceneTypeEnum,
        this.questionnaireTypeIds,
        this.sceneIds,
        2
      );
    }
  }

  nzPageIndexChange() {
    this.getToollist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds,
      2
    );
  }

  nzPageSizeChange() {
    this.getToollist(
      this.questionnaireSceneTypeEnum,
      this.questionnaireTypeIds,
      this.sceneIds,
      2
    );
  }
}
