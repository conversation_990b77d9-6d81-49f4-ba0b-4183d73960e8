<div class="modal-title">
  <span class="span1">新增题本</span>
  <span class="span2">已选：{{ dimensionNum }}个维度</span>
</div>
<div class="modal-border">
  <div></div>
</div>
<div class="survey-book-modal-content">
  <div class="left-content">
    <!-- <div class="flex select">
      <nz-select [(ngModel)]="selectedValue" (ngModelChange)="switchDimension($event)">
        <nz-option [nzValue]="0" nzLabel="EEI敬业度"></nz-option>
        <nz-option [nzValue]="1" nzLabel="ESI满意度"></nz-option>
        <nz-option [nzValue]="2" nzLabel="其他"></nz-option>
      </nz-select>
    </div>

    <ul class="title-child">
      <li [class]="this.questionsIndex === i ? 'active' : ''" *ngFor="let item of dimensionList?.dimensionQuestionVos, let i = index" (click)="chooseDimension(i)">
        <span>{{item?.dimensionName.zh_CN }}({{item?.surveyQuestions.length}})</span>
      </li>
    </ul> -->
    <nz-tree
      [nzData]="dimensionDetailData"
      [nzTreeTemplate]="treeTemplate"
      [nzExpandedKeys]="defaultExpandedKeys"
      (nzExpandChange)="nzEvent($event)"
    >
    </nz-tree>
    <ng-template #treeTemplate let-node let-origin="origin" let-index="index">
      <ng-container *ngIf="!node.isLeaf">
        <!-- <label nz-checkbox [(ngModel)]="node.checked" style="margin-right: 5px;"></label> -->
        <span>{{ node.title }}({{ node.children.length }}){{ index }}</span>
      </ng-container>
      <ng-container *ngIf="node.isLeaf">
        <span
          [ngClass]="defaultSelectedKey === node.key ? 'tree-node' : ''"
          style="cursor: pointer;"
          (click)="chooseDimension(node.key, node.parentNode.origin)"
          >{{ node.title }}({{ node.origin.surveyQuestions.length }}){{
            index
          }}</span
        >
      </ng-container>
    </ng-template>
  </div>
  <div class="right-content">
    <div class="right-content-header">
      <div *ngIf="showAdd">
        <label
          nz-checkbox
          [nzDisabled]="isAllDisable"
          (nzCheckedChange)="checkAll($event)"
          [(ngModel)]="isAllChecked"
          >全选</label
        >
        <app-btn
          [text]="'添加经典开放题'"
          [image]="'./assets/images/org/add.png'"
          [hoverColor]="'#419EFF'"
          [hoverImage]="'./assets/images/org/rpt_add.png'"
          (btnclick)="addOpenSubject(0)"
        >
        </app-btn>
        <nz-divider style="margin-left: 30px;" nzType="vertical"></nz-divider>
        <app-btn
          [text]="'添加多选式开放题'"
          [image]="'./assets/images/org/add.png'"
          [hoverColor]="'#BA9AFE'"
          [hoverImage]="'./assets/images/org/add_purple.png'"
          (btnclick)="addOpenSubject(1)"
        >
        </app-btn>
      </div>
      <div class="searchDiv">
        <nz-input-group [nzPrefix]="IconSearch">
          <input
            class="input-search"
            type="text"
            nz-input
            placeholder="请输入关键词"
            [(ngModel)]="keyWord"
            (keydown.enter)="search()"
          />
        </nz-input-group>
        <ng-template #IconSearch>
          <i nz-icon nzType="search" class="icon-search" (click)="search()"></i>
        </ng-template>
      </div>
    </div>

    <div class="modal-little-border">
      <div></div>
    </div>

    <ul class="questions-box" *ngIf="!showAdd">
      <li *ngFor="let data of questionsList; let index = index">
        <label
          nz-checkbox
          [nzDisabled]="data.isForceSelect"
          [(nzChecked)]="!data.isDeleted"
          (nzCheckedChange)="checkOne($event, index)"
        ></label>
        <div style="padding-left: 10px;">
          <p [innerHTML]="data.name.zh_CN | html"></p>
          <p [innerHTML]="data.name.en_US | html"></p>
        </div>
      </li>
    </ul>
    <div class="questions-box-open" *ngIf="showAdd">
      <div class="questions_scroll_box">
        <ng-container *ngIf="createOpenQuestion.length !== 0">
          <div
            *ngFor="let data of createOpenQuestion; let index = index"
            (click)="chooseOpenQuestion(index, true)"
            [class]="openQuestionNumClass === index ? 'questions_hover' : ''"
          >
            <div style="padding-left: 10px;">
              <input
                style="margin-bottom: 10px;"
                nz-input
                placeholder="请输入(中文)"
                [(ngModel)]="data.name.zh_CN"
              />
              <input
                nz-input
                placeholder="请输入(英文)"
                [(ngModel)]="data.name.en_US"
              />
            </div>
            <div
              style="display: flex; flex-direction: column; justify-content: space-around;"
            >
              <app-btn
                (btnclick)="save(data)"
                [text]="''"
                [image]="'./assets/images/org/save_hover.png'"
                [hoverColor]="'#409EFF'"
                [hoverImage]="'./assets/images/org/save_hover.png'"
              >
              </app-btn>
              <app-btn
                nz-popconfirm
                nzPopconfirmTitle="要删除当前的题目吗？"
                (nzOnConfirm)="delete(data)"
                [text]="''"
                [image]="'./assets/images/org/del.png'"
                [hoverColor]="'#409EFF'"
                [hoverImage]="'./assets/images/org/del_hover.png'"
              >
              </app-btn>
              <label
                style="margin-left: 10px;"
                *ngIf="
                  createOpenQuestion[0].type !==
                  'MULTIPLE_CHOICE_ESSAY_QUESTION'
                "
                nz-checkbox
                [(ngModel)]="createOpenQuestion[0].isRequire"
                (ngModelChange)="changeStatus($event)"
                >必填</label
              >
            </div>
          </div>
        </ng-container>
        <div
          *ngFor="let data of questionsList; let index = index"
          (click)="chooseOpenQuestion(index)"
          [class]="openQuestionIndexClass === index ? 'questions_hover' : ''"
        >
          <label
            nz-checkbox
            [nzDisabled]="data.isForceSelect"
            [(nzChecked)]="!data.isDeleted"
            (nzCheckedChange)="checkOne($event, index)"
          ></label>
          <div style="padding-left: 10px;">
            <p [innerHTML]="data.name.zh_CN | html"></p>
            <p [innerHTML]="data.name.en_US | html"></p>
          </div>
        </div>
      </div>
      <!-- 展示选项 -->
      <div
        style="padding-left: 10px; border-left: 1px solid #E6E6E6;"
        *ngIf="
          questionsList[openQuestionIndex].type ===
            'MULTIPLE_CHOICE_ESSAY_QUESTION' && showOption
        "
      >
        <div class="open-que-title">
          <span>选项（≤10）</span>
          <label
            style="font-size: 12px;"
            nzDisabled
            nz-checkbox
            [(ngModel)]="questionsList[openQuestionIndex].isRequire"
            (ngModelChange)="changeStatus($event)"
            >必填</label
          >
          <span style="font-size: 12px;">
            多选
            <input
              nz-input
              [disabled]="true"
              [(ngModel)]="
                questionsList[openQuestionIndex].options.multiSelectMin
              "
              [nzSize]="'small'"
              style="width: 30px;"
            />
            -
            <input
              nz-input
              [disabled]="true"
              [(ngModel)]="
                questionsList[openQuestionIndex].options.multiSelectMax
              "
              [nzSize]="'small'"
              style="width: 30px;"
            />
          </span>
        </div>
        <ul class="questionsList-options">
          <li
            *ngFor="
              let data of questionsList[openQuestionIndex].options.options;
              let idx = index
            "
          >
            <div>
              <p [innerHTML]="data.name.zh_CN | html"></p>
              <p [innerHTML]="data.name.en_US | html"></p>
            </div>
          </li>
        </ul>
      </div>
      <!-- 添加选项 -->
      <div
        style="padding-left: 10px; border-left: 1px solid #E6E6E6;"
        *ngIf="
          createOpenQuestion.length !== 0 &&
          createOpenQuestion[0].type === 'MULTIPLE_CHOICE_ESSAY_QUESTION' &&
          !showOption
        "
      >
        <div class="open-que-title">
          <span>选项（≤10）</span>
          <label
            style="font-size: 12px;"
            nz-checkbox
            [(ngModel)]="createOpenQuestion[0].isRequire"
            (ngModelChange)="changeStatus($event)"
            >必填</label
          >
          <span style="font-size: 12px;">
            多选
            <nz-input-number
              [(ngModel)]="createOpenQuestion[0].options.multiSelectMin"
              [nzSize]="'small'"
              [nzMin]="createOpenQuestion[0].isRequire ? '1' : '0'"
              [nzMax]="
                !!createOpenQuestion[0].options.multiSelectMax
                  ? createOpenQuestion[0].options.multiSelectMax
                  : '10'
              "
              [nzStep]="1"
              (ngModelChange)="formatterNum($event, 0)"
              style="width: 50px;"
            ></nz-input-number>
            -
            <nz-input-number
              [(ngModel)]="createOpenQuestion[0].options.multiSelectMax"
              [nzSize]="'small'"
              [nzMin]="createOpenQuestion[0].options.multiSelectMin"
              [nzMax]="10"
              [nzStep]="1"
              (ngModelChange)="formatterNum($event, 1)"
              style="width: 50px;"
            ></nz-input-number>
          </span>
          <a (click)="addOptions()">添加选项</a>
        </div>
        <ul class="questionsList-options">
          <li
            *ngFor="
              let data of createOpenQuestion[0].options.options;
              let idx = index
            "
          >
            <div>
              <div class="flex space-between" style="margin-bottom: 10px;">
                <label nz-checkbox [(ngModel)]="data.isEnableOpenAnswer"
                  >开放式回答</label
                >
                <label
                  nz-checkbox
                  [(ngModel)]="data.isRequireOpenAnswer"
                  *ngIf="data.isEnableOpenAnswer"
                  >必填</label
                >
                <app-btn
                  nz-popconfirm
                  nzPopconfirmTitle="是否删除当前的选项吗？"
                  (nzOnConfirm)="deleteOption(idx)"
                  [text]="''"
                  [image]="'./assets/images/org/del.png'"
                  [hoverColor]="'#409EFF'"
                  [hoverImage]="'./assets/images/org/del_hover.png'"
                >
                </app-btn>
              </div>
              <input
                style="margin-bottom: 10px;"
                nz-input
                placeholder="请输入(中文)"
                [(ngModel)]="data.name.zh_CN"
              />
              <input
                nz-input
                placeholder="请输入(英文)"
                [(ngModel)]="data.name.en_US"
              />
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>

<div class="modal-border">
  <div></div>
</div>

<div class="modal-btn-footer" #modalFooter>
  <button
    nz-button
    nzType="primary"
    nzShape="round"
    [nzLoading]="isLoading"
    (click)="ok()"
  >
    确认
  </button>
</div>
