import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ProjectManageService {
  tenantUrl: string;
  constructor(private http: HttpClient) {
    this.tenantUrl = "/tenant-api";
  }
  getList(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/listReportTypeByPage`;
    return this.http.post(api, param);
  }

  getDropdonwData(projectId): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/listByProjectId/${projectId}`;
    return this.http.get(api);
  }

  getStyles(): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/report/template/sag/listReportStyle`;
    return this.http.get(api);
  }

  updateType(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/updateReportType`;
    return this.http.post(api, param);
  }

  //选择的tip细分报告
  getDetailReport(questionnaireId): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/listSelectedTipDetailReport?questionnaireId=${questionnaireId}`;
    return this.http.get(api);
  }

  //所有的tip细分报告
  getallDetailReport(questionnaireId): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/listTipDetailReport?questionnaireId=${questionnaireId}`;
    return this.http.get(api);
  }

  //产品类型接口
  getallTreeByType(type): Observable<any> {
    const api = `${this.tenantUrl}/survey/standard/questionnaire/scene/type/listTreeByType?type=${type}`;
    return this.http.get(api);
  }

  createTipDetailReport(param: any, projectId:string, questionnaireId:string): Observable<any> {
    const api = `${this.tenantUrl}/sagittarius/report/content/createTipDetailReport?projectId=${projectId}&questionnaireId=${questionnaireId}`;
    return this.http.post(api, param);
  }
  getReportTemplate(param): Observable<any> {
    const api = `${this.tenantUrl}/survey/questionnaire/listModuleSelectInfo`;
    // const httpOptions = {headers : new HttpHeaders({'Content-Type': 'application/json'})};
    return this.http.post(api, param);
  }

  // 根据分类列出问卷题目
  getTopicsBasedOnClassification(projectId): Observable<any>{
    const api = `${this.tenantUrl}/survey/question/listOptionQuestionByType?projectId=${projectId}`;
    return this.http.get(api);
  }

  // 查询360报告题目维度数量设置
  getListSagBehaviorReportSetting(param: {questionnaireId:string, style:string}): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/listSagBehaviorReportSetting`;
    return this.http.post(api, param);
  }
  // 保存或修改360报告题目维度数量设置
  saveOrUpdateSetting(param: any): Observable<any> {
    const api = `${this.tenantUrl}/survey/project/saveOrUpdateSetting`;
    return this.http.post(api, param);
  }
}

