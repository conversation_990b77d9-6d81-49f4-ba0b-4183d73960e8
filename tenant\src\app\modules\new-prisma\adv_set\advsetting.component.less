:host {
  .advanced {
    .mr-8 {
      margin-right: 8px;
    }
    .ml-8 {
      margin-left: 8px;
    }
    .mr-16 {
      margin-right: 16px;
    }
    .mb-16 {
      margin-bottom: 16px;
    }
    .mb-24 {
      margin-bottom: 24px;
    }
    .mr-24 {
      margin-right: 24px;
    }
    .mt-6 {
      margin-top: 6px;
    }
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      line-height: 22px;
    }
    .title-between {
      display: flex;
      justify-content: space-between;
      span {
        &:nth-child(1) {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          line-height: 22px;
        }
      }
    }
    .suggest {
      font-size: 12px;
      font-weight: 400;
      color: #595959;
      line-height: 17px;
    }
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #595959;
      line-height: 20px;
    }
    .avatar-uploader {
      .avatar {
        width: 86px;
        height: 86px;
      }
      .ant-upload-text {
        margin-top: 8px;
        font-size: 14px;
        color: #409eff;
      }
    }
    .tip-iocn{
      color: #BFBFBF;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 10px 16px;
    text-align: right;
    left: 0px;
    background: #fff;
  }
  ::ng-deep {
    .ant-tabs .ant-tabs-tab {
      padding: 0 0 8px 0;
    }
    .ant-tabs-ink-bar {
      height: 4px;
      border-radius: 2px;
    }
    .ant-checkbox-wrapper {
      margin-left: 0;
      margin-right: 12px;
    }
    .ant-checkbox + span {
      padding: 0 4px;
    }
    // .ant-tabs-tabpane {
    //   overflow-y: auto;
    //   overflow-x: hidden;
    //   height: calc(100vh - 200px);
    //   padding-right: 10px;
    // }
  }
  .flex_dir_1 {
    display: flex;
  }
  .flex_dir_1 label {
    flex: 1;
  }
}

::ng-deep {
  .permission-tool {
    margin-top: 8px;
    .ant-tooltip-arrow {
      &::before {
        width: 8px;
        height: 8px;
        background-color: #ffffff;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.2);
      }
    }
    .ant-tooltip-inner {
      max-width: 240px;
      padding: 0;
      background: #ffffff;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.2);
      .permission-tool-body {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #495970;
        ul {
          padding: 16px 16px 6px 16px;
          li {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            .tips {
              font-size: 12px;
              color: #aaaaaa;
            }
          }
        }
      }
      .permission-tool-footer {
        display: flex;
        align-items: center;
        justify-content: right;
        padding: 16px;
        border-top: 1px solid #ececec;
      }
    }
  }
}
