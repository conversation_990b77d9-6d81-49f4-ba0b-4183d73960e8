<div class="content client-width">
  <ul class="card_ul">
    <li class="big_title">
      发送日志
    </li>
    <li class="clear_tip" (click)="clearlistMap()">
      清空条件
    </li>
    <ul class="search_ul">
      <li>
        <input
          style="margin-right: 6px;width: 125px;"
          type="text"
          nz-input
          placeholder="请输入活动名称"
          [(ngModel)]="projectName"
        />
        <nz-input-number
          class=" number_put"
          style="margin-right: 6px;width: 110px;"
          nzPlaceHolder="请输入活动ID"
          [(ngModel)]="projectCode"
          [nzPrecision]="0"
          [nzMin]="1"
        ></nz-input-number>
        <input
          style="margin-right: 6px;width: 150px;"
          type="text"
          nz-input
          placeholder="请输入发送对象姓名"
          [(ngModel)]="personName"
        />
        <!-- [nzShowTime]="{ nzFormat: 'HH:mm' }" -->
        <!-- [nzFormat]="'YYYY-MM-DD HH:mm:ss'" -->
        <nz-range-picker
          style="margin-right: 6px;width: 350px;"
          nzShowTime
          class="time_picker"
          [(ngModel)]="dateRange"
          [nzFormat]="'YYYY-MM-DD HH:mm:ss'"
          [nzPlaceHolder]="['请选择开始时间', '请选择结束时间']"
          (ngModelChange)="onChange($event)"
        ></nz-range-picker>
        <nz-select
          [(ngModel)]="messagePurpose"
          style="margin-right: 6px;width: 145px;"
          nzPlaceHolder="请选择发送类型"
        >
          <nz-option nzValue="ANSWER" nzLabel="作答邀请"></nz-option>
          <nz-option nzValue="INVITE_ANSWER" nzLabel="自邀请"></nz-option>
          <nz-option
            nzValue="AUDIT_INVITE_ANSWER"
            nzLabel="审核自邀请"
          ></nz-option>
          <nz-option nzValue="ANSWER_WARN" nzLabel="作答提醒"></nz-option>
          <nz-option nzValue="REPORT" nzLabel="作答报告"></nz-option>
        </nz-select>

        <nz-select
          style="width: 220px;"
          nzMode="tags"
          nzPlaceHolder="请选择发送方式"
          [(ngModel)]="messageType"
        >
          <nz-option
            *ngFor="let option of listOfOption"
            [nzLabel]="option.label"
            [nzValue]="option.value"
          >
          </nz-option>
        </nz-select>
      </li>
      <button
        nz-button
        nzType="default"
        (click)="searchlistMap()"
        style="margin-top: 1px;"
      >
        查询
      </button>
    </ul>
    <ul class="tips_ul">
      <li>
        <!-- <button
          nz-button
          nzType="primary"
          nz-popconfirm
          [disabled]="!setOfCheckedId.size"
          [nzTitle]="'选中记录' + setOfCheckedId.size + '条，确认重新发送吗？'"
          (nzOnConfirm)="ReturnSend()"
        >
          重新发送
        </button> -->
        <button nz-button nzType="primary" (click)="handReturnSend()">
          重新发送
        </button>
        <button
          nz-button
          nzType="default"
          style="margin-left: 10px;"
          (click)="exportList()"
        >
          导出列表
        </button>
        <nz-tag
          [nzColor]="'blue'"
          style="margin-left: 10px;"
          *ngIf="setOfCheckedId.size"
          >已选择 {{ setOfCheckedId.size }} 条数据</nz-tag
        >
      </li>
      <li>
        <nz-checkbox-group
          [(ngModel)]="checkOptionsOne"
          (ngModelChange)="updateSingleChecked()"
        >
        </nz-checkbox-group>
      </li>
    </ul>

    <div *ngIf="showlist" style="margin-top: 15px">
      <nz-table
        #rowSelectionTable
        [nzData]="listOfData"
        [nzShowPagination]="false"
        [(nzPageSize)]="PageSize"
        (nzCurrentPageDataChange)="onCurrentPageDataChange($event)"
        nzBordered
      >
        <thead>
          <tr>
            <th nzWidth="16px">
              <label
                nz-checkbox
                [(ngModel)]="checked"
                (ngModelChange)="onAllChecked($event)"
                [nzIndeterminate]="indeterminate"
              >
              </label>
            </th>
            <th nzWidth="150px">活动</th>
            <th nzWidth="100px">发送对象</th>
            <th nzWidth="100px">发送类型</th>
            <th nzWidth="100px">发送方式</th>
            <th nzWidth="200px">发送内容</th>
            <th nzWidth="120px">发送时间</th>
            <!-- <th nzWidth="100px">发送状态</th> -->
            <th nzWidth="250px">发送状态</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of rowSelectionTable.data">
            <td>
              <label
                nz-checkbox
                [ngModel]="setOfCheckedId.has(data.id)"
                (ngModelChange)="onItemChecked(data.id, $event)"
              >
              </label>
            </td>
            <td>
              <div nz-tooltip [nzTooltipTitle]="titleTemplate">
                <div *ngIf="data.projectCode">
                  <nz-tag
                    [nzColor]="'blue'"
                    class="tips"
                    style="max-width: 150px;"
                    >ID:{{ data.projectCode }}</nz-tag
                  >
                </div>
                <div class="tips" style="width: 150px;">
                  <span>{{ data.projectName.zh_CN }} </span>
                </div>
              </div>
              <ng-template #titleTemplate>
                <span>
                  <span *ngIf="data.projectCode"
                    >ID:{{ data.projectCode || "--" }} <br
                  /></span>
                  {{ data.projectName.zh_CN }}</span
                >
              </ng-template>
            </td>

            <td>
              <div class="tips" style="width: 100px;">
                <span nz-tooltip [nzTooltipTitle]="data.personName">{{
                  data.personName
                }}</span>
              </div>
            </td>
            <td>
              <span *ngIf="data.messagePurpose == 'ANSWER'">作答邀请</span>
              <span *ngIf="data.messagePurpose == 'INVITE_ANSWER'">自邀请</span>
              <span *ngIf="data.messagePurpose == 'ANSWER_WARN'">作答提醒</span>
              <span *ngIf="data.messagePurpose == 'REPORT'">作答报告</span>
              <span *ngIf="data.messagePurpose == 'AUDIT_INVITE_ANSWER'"
                >审核自邀请</span
              >
            </td>
            <td>
              <span *ngIf="data.messageType == 'SMS'">短信</span>
              <span *ngIf="data.messageType == 'MAIL'">邮件</span>
              <span *ngIf="data.messageType == 'ENTERPRISE_WECHAT'"
                >企业微信</span
              >
              <span *ngIf="data.messageType == 'DING_DING'">钉钉</span>
              <span *ngIf="data.messageType == 'THIRD_PARTY_LARK_TEXT_CARD'"
                >飞书</span
              >
            </td>
            <td>
              <div>
                <span
                  class="split_span"
                  nz-tooltip
                  [nzTooltipTitle]="tooltipTemplate"
                >
                  {{ data.content }}
                </span>
                <ng-template #tooltipTemplate>
                  <div *ngIf="data.messageType != 'SMS'">
                    主题：{{ data.subject }}<br />
                    内容：{{ data.content }}
                  </div>
                  <div *ngIf="data.messageType == 'SMS'">
                    {{ data.content }}
                  </div>
                </ng-template>
              </div>
            </td>
            <td>
              <span *ngIf="data.sentTime">
                <!-- {{ data.sentTime.slice(0, 19).replace("T", " ") }} -->
                {{ data.sentTimeFormat }}
              </span>
            </td>
            <td>
              <span *ngIf="data.status == 'SENDING'">发送中</span>
              <span
                *ngIf="data.status == 'SENT' && !data.failReason"
                style="color: #60CB7F;"
                >发送成功</span
              >
              <div
                class="tips"
                *ngIf="data.status == 'SENT' && data.failReason"
                style="color: #60CB7F;width: 250px;"
              >
                <span
                  nz-tooltip
                  [nzTooltipTitle]="'发送成功：' + data.failReason"
                  >发送成功：{{ data.failReason }}</span
                >
              </div>
              <div
                class="tips"
                *ngIf="data.status == 'FAIL'"
                style="color: #F84444;width: 250px;"
              >
                <span nz-tooltip [nzTooltipTitle]="failReasonTemplate">
                  【发送失败】<span [innerHTML]="data.failReason"></span>
                </span>
                <ng-template #failReasonTemplate>
                  <span>
                    【发送失败】<span [innerHTML]="data.failReason"></span>
                  </span>
                </ng-template>
              </div>
            </td>
          </tr>
        </tbody>
      </nz-table>

      <div style="display: flex;justify-content: flex-end;margin-top: 15px;">
        <nz-pagination
          [(nzPageIndex)]="PageIndex"
          [(nzTotal)]="listTotal"
          nzShowSizeChanger
          [(nzPageSize)]="PageSize"
          (nzPageIndexChange)="nzPageIndexChange()"
          (nzPageSizeChange)="nzPageSizeChange()"
          style="margin-right: -10px;"
        ></nz-pagination>
      </div>
    </div>
  </ul>
</div>
