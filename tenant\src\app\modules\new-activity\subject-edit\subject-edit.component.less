.title {
  font-size: 24px;
  font-family: PingFangSC-Light, PingFang SC;
  font-weight: 300;
  color: #17314C;
  line-height: 33px;
  margin-bottom: 10px;
}

.footer {
  border-top: solid 1px #E6E6E6;
  padding-top: 20px;

  .iptBtn {
    width: 128px;
    height: 38px;
    background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
    box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
    border-radius: 19px;
    font-weight: 500;
    color: #FFFFFF;
  }
}

.tip {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #495970;
  line-height: 20px;
}

.preview {
  margin-bottom: 27px;

  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    margin-right: 40px;
    padding-top: 15px;

    p {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #17314C;
      line-height: 20px;
    }

    >div {
      display: flex;
      align-items: center;

      .divider {
        margin: 0 25px;
      }
    }
  }

  .preview-content {
    display: flex;

    ul {
      flex: 1;
      border-radius: 8px;

      // border: 1px solid #EFEFEF;
      li {
        border: 1px solid #E6E6E6;
        min-height: 60px;
        display: flex;

        >div {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 55px;
          background: #F4F4F4;
        }

        p {
          flex: 1;
          padding: 10px 8px;
        }
      }

      li:first-child {
        border-bottom: none;
        border-top-right-radius: 8px;
        border-top-left-radius: 8px;
      }

      li:last-child {
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
      }
    }

    ol {
      margin-left: 10px;
      width: 50%;
      max-height: 400px;
      overflow-y: auto;
      overflow-x: hidden;
      border: 1px solid #E6E6E6;
      border-radius: 8px;

      .open-que-title {
        background: #F4F4F4;
        height: 46px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #E6E6E6;

        span {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #17314C;
        }
      }

      li {
        padding: 10px;
        border-bottom: 1px solid #E6E6E6;
        display: flex;

        >div:first-child {
          // 选项
          flex: 1;
        }
      }

      li:last-child {
        border: none;
      }
    }

    textarea {
      border: none;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    .page-arrow {
      display: flex;
      flex-direction: column;

      >div {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 20px;

        >img {
          cursor: pointer;
        }
      }
    }
  }
}