<div class="renderSetting">
    <div class="header">
        <h2>呈现设置</h2>
    </div>
    <div class="content">
        <div *ngIf="currentStep == 0">
            <div nz-row [nzGutter]="16" class="row title">
                <div nz-col nzSpan="2">呈现</div>
                <div nz-col nzSpan="6">呈现名称</div>
                <div nz-col nzSpan="6">工具类型</div>
                <div nz-col nzSpan="10">数据规则</div>
            </div>
            <div nz-row [nzGutter]="16" class="row" *ngFor="let item of axisList">
                <div nz-col nzSpan="2">
                    <span class="required">{{item.axis == 'Z'?'&nbsp;':'*'}}</span> {{item.axis}}
                </div>
                <div nz-col nzSpan="6">
                    <nz-select nzShowSearch nzAllowClear [nzDropdownRender]="permission?renderOptionsTemplate:null" nzPlaceHolder="请选择" style="width: 100%;" [(ngModel)]="item.nameTypeId">
                        <nz-option *ngFor="let option of zRenderOptions(renderOptions, item.axis)" [nzValue]="option.id" [nzLabel]="option.name.zh_CN" [nzHide]="!isNotSelectedOptions(option, 'nameTypeId')"></nz-option>
                    </nz-select>
                    <ng-template #renderOptionsTemplate>
                        <nz-divider></nz-divider>
                        <div class="selectAdd">
                            <!-- <input type="text" nz-input #inputElement nzSize="small" style="width: 65%" [disabled]="customDisabled('DISPLAY_NAME')" />
                            <a [ngClass]="{'addItem': true, 'addItemDisabled': customDisabled('DISPLAY_NAME')}" (click)="addOption(inputElement, 'DISPLAY_NAME')">
                                <span nz-icon nzType="plus"></span> 添加
                            </a> -->
                            <input type="text" nz-input #inputElement nzSize="small" style="width: 65%" maxlength="2" />
                            <a [ngClass]="{'addItem': true}" (click)="addOption(inputElement, 'DISPLAY_NAME')">
                                <span nz-icon nzType="plus"></span> 添加
                            </a>
                        </div>
                    </ng-template>
                </div>
                <div nz-col nzSpan="6">
                    <nz-select nzShowSearch nzAllowClear [nzDropdownRender]="permission?typeOptionsTemplate:null" nzPlaceHolder="请选择" style="width: 100%;" [(ngModel)]="item.questionnaireTypeId">
                        <nz-option *ngFor="let option of typeOptions" [nzValue]="option.id" [nzLabel]="option.name.zh_CN" [nzHide]="!isNotSelectedOptions(option, 'questionnaireTypeId')"></nz-option>
                    </nz-select>
                    <ng-template #typeOptionsTemplate>
                        <nz-divider></nz-divider>
                        <div class="selectAdd">
                            <!-- <input type="text" nz-input #inputElement nzSize="small" style="width: 65%" [disabled]="customDisabled('QUESTIONNAIRE_TYPE')" />
                            <a [ngClass]="{'addItem': true, 'addItemDisabled': customDisabled('QUESTIONNAIRE_TYPE')}" (click)="addOption(inputElement, 'QUESTIONNAIRE_TYPE')">
                                <span nz-icon nzType="plus"></span> 添加
                            </a> -->
                            <input type="text" nz-input #inputElement nzSize="small" style="width: 65%" />
                            <a [ngClass]="{'addItem': true}" (click)="addOption(inputElement, 'QUESTIONNAIRE_TYPE')">
                                <span nz-icon nzType="plus"></span> 添加
                            </a>
                        </div>
                    </ng-template>
                </div>
                <div nz-col nzSpan="10" class="rule">
                    <span>高/前</span>
                    <nz-input-number [(ngModel)]="item.highPercent" [nzFormatter]="formatterPercent" [nzParser]="parserPercent" [nzMin]="1" [nzMax]="99" [nzStep]="1" style="width: 85px;" (ngModelChange)="changePercent(item)"></nz-input-number>
                    <span>低/后</span>
                    <nz-input-number [(ngModel)]="item.lowPercent" [nzFormatter]="formatterPercent" [nzParser]="parserPercent" [nzMin]="1" [nzMax]="99" [nzStep]="1" style="width: 85px;" (ngModelChange)="changePercent(item)"></nz-input-number>
                </div>
            </div>
        </div>
        <ng-container *ngIf="currentStep == 1">
            <div *ngIf="areaType == 0">
                <div nz-row [nzGutter]="16" class="row title">
                    <div nz-col nzSpan="1"></div>
                    <div nz-col nzSpan="6">名称</div>
                    <div nz-col nzSpan="17">描述</div>
                </div>
                <div nz-row [nzGutter]="16" class="row" *ngFor="let item of areaList; let i = index">
                    <div nz-col nzSpan="1">
                        <index-tip [index]='i' [x]='item.xAxisLevel' [y]='item.yAxisLevel'></index-tip>
                    </div>
                    <div nz-col nzSpan="6">
                        <input nz-input placeholder="请输入" [(ngModel)]="item.name.zh_CN" [disabled]="!item.isEditable" maxlength="4" />
                    </div>
                    <div nz-col nzSpan="17">
                        <input nz-input placeholder="请输入" [(ngModel)]="item.description.zh_CN" [disabled]="true" />
                    </div>
                </div>
            </div>
            <div *ngIf="areaType == 1">
                <div nz-row [nzGutter]="16" class="row title">
                    <div nz-col nzSpan="1"></div>
                    <div nz-col nzSpan="6">名称</div>
                    <div nz-col nzSpan="7">描述</div>
                    <div nz-col nzSpan="10">特征及发展建议</div>
                </div>
                <div nz-row [nzGutter]="16" class="row" *ngFor="let item of areaList; let i = index">
                    <div nz-col nzSpan="1">
                        <index-tip [index]='i' [x]='item.xAxisLevel' [y]='item.yAxisLevel'></index-tip>
                    </div>
                    <div nz-col nzSpan="6">
                        <input nz-input placeholder="请输入" [(ngModel)]="item.name.zh_CN" [disabled]="!item.isEditable" maxlength="4" />
                    </div>
                    <div nz-col nzSpan="7">
                        <input nz-input placeholder="请输入" [(ngModel)]="item.description.zh_CN" [disabled]="true" />
                    </div>
                    <div nz-col nzSpan="10">
                        <input nz-input placeholder="请输入" [(ngModel)]="item.developmentSuggestions.zh_CN" [disabled]="!item.isEditable" />
                    </div>
                </div>
            </div>

            <div *ngIf="areaType == 2">
                <div nz-row [nzGutter]="16" class="row title">
                    <div nz-col nzSpan="1"></div>
                    <div nz-col nzSpan="6">名称</div>
                    <div nz-col nzSpan="7">描述</div>
                    <div nz-col nzSpan="10">特征及发展建议</div>
                </div>
                <div nz-row [nzGutter]="16" class="row group" *ngFor="let item of areaList; let i = index">
                    <div nz-col nzSpan="1">
                        <index-tip [index]='i' [x]='item.xAxisLevel' [y]='item.yAxisLevel'></index-tip>
                    </div>
                    <div nz-col nzSpan="6">
                        <textarea [nzAutosize]="{ minRows: 5, maxRows: 5 }" nz-input placeholder="请输入" [(ngModel)]="item.name.zh_CN" style="height: 100%;" [disabled]="!item.isEditable" maxlength="4"></textarea>
                    </div>
                    <div nz-col nzSpan="7">
                        <textarea [nzAutosize]="{ minRows: 5, maxRows: 5 }" nz-input placeholder="请输入" [(ngModel)]="item.description.zh_CN" style="height: 100%;" [disabled]="true"></textarea>
                    </div>
                    <div nz-col nzSpan="10">
                        <div style="margin-bottom: 10px;">
                            <nz-input-group [nzPrefix]="prefixTemplateSmile">
                                <input type="text" nz-input placeholder="请输入" [(ngModel)]="item.highDevelopmentSuggestions.zh_CN" [disabled]="!item.isEditable" />
                            </nz-input-group>
                            <ng-template #prefixTemplateSmile>
                                <img src="./assets/images/smile.png" alt="">
                                <nz-divider nzType="vertical"></nz-divider>
                            </ng-template>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <nz-input-group [nzPrefix]="prefixTemplateComplacent">
                                <input type="text" nz-input placeholder="请输入" [(ngModel)]="item.midDevelopmentSuggestions.zh_CN" [disabled]="!item.isEditable" />
                            </nz-input-group>
                            <ng-template #prefixTemplateComplacent>
                                <img src="./assets/images/complacent.png" alt="">
                                <nz-divider nzType="vertical"></nz-divider>
                            </ng-template>
                        </div>
                        <div>
                            <nz-input-group [nzPrefix]="prefixTemplateSad">
                                <input type="text" nz-input placeholder="请输入" [(ngModel)]="item.lowDevelopmentSuggestions.zh_CN" [disabled]="!item.isEditable" />
                            </nz-input-group>
                            <ng-template #prefixTemplateSad>
                                <img src="./assets/images/sad.png" alt="">
                                <nz-divider nzType="vertical"></nz-divider>
                            </ng-template>
                        </div>
                    </div>
                </div>
            </div>
        </ng-container>
    </div>
    <div class="footer">
        <div class="btns">
            <span *ngIf="currentStep == 0" (click)="clear()">清空</span>
            <span *ngIf="currentStep == 0" (click)="cilckStep(1)">下一步</span>
            <span *ngIf="currentStep == 1" (click)="cilckStep(0)">上一步</span>
            <span *ngIf="currentStep == 1" (click)="confirm()">确认</span>
        </div>
    </div>
</div>