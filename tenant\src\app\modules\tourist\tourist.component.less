@text-color: #17314c;
@width: 485px;

.content {
  margin: 0 auto;
}
.tourist-content {
  background-color: #f5f6fa;
  padding-top: 0.1px;
}


.title-sub {
  font-size: 24px;
  margin-top: 73px;
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #17314c;
}
.our_good {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
  li {
    flex: 1;
    display: flex;
    border-bottom: 2px solid #dee3e7;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    margin: 0 5px;
    cursor: pointer;
  }
  .border_bot {
    border-bottom: 2px solid #409eff;
  }
}
.our_list {
  padding: 100px 0;
  .one_list {
    display: flex;
    justify-content: center;
    align-items: center;
    .one_ul_s {
      li {
        padding: 20px 0;
        color: #17314c;
        font-size: 20px;
        display: flex;
        align-items: center;
        div {
          width: 11px;
          height: 11px;
          background: linear-gradient(180deg, #27cff1 0%, #40a0ff 100%);
          border-radius: 50%;
        }
      }
    }
  }
}
.three_div {
  background-color: #fff;
  padding: 30px 0;
  .bot_div {
      padding-right:100px;
    position: absolute;
    right: 0;
    bottom: 30px;
    cursor: pointer;
  }
}
.four_div {
  height: 506px;
  background: url(../../../assets/images/cicle_bg.png) no-repeat center;
  display: flex;
  align-items: center;
  justify-content: center;
  .four_ul {
    width: 850px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    > .left_li {
      display: flex;
      > div {
        cursor: pointer;
      }
    }
  }
  
}
.show_ul{
    margin-top: -110px;
    background-color: #fff;
    display: flex;
    border: 1px solid #DDE8F3;
    li{
        flex: 1;
    }
    .card_left{
        .card_ul{
            .card_small{
                padding: 10px;
                >div{
                    display: flex;
                    justify-content: space-between;
                    >p{
                        font-size: 20px;
                        font-weight: bold;
                        color: #17314C;
                    }
                    >span{
                        cursor: pointer;
                        color: #409EFF;
                    }
                }
                >p{
                    padding: 15px 0;
                }
            }
        }
    }
}
.end_finish {
  width: 100%;
  height: 193px;
  background: url(../../../assets/images/eng_bg.png) no-repeat center;
  display: flex;
  justify-content: center;
  align-items: center;
  .p_word {
    color: #ffffff;
    font-size: 30px;
  }
  .end_d {
    margin-top: 30px;
    width: 181px;
    text-align: center;
    line-height: 44px;
    background: #ffffff;
    border-radius: 22px;
    cursor: pointer;
  }
}

.s1 {
  // min-height: 745px;
  margin-top: 30px;
  width: 100%;
  //   background: url(../../../assets/images/index_bg.png) no-repeat top;
  background-size: cover;
  background-color: #fff;
  padding-top: 0.1px;
  .new_bg_img {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    height: 600px;
    position: relative;
    .open_left{
      position: absolute;
      left: 30px;
      cursor: pointer;
    }
    .open_right{
      position: absolute;
      right: 30px;
      cursor: pointer;
    }
    .new_bg_word {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      top: 0;
      width: 100%;
      height: 100%;
      .end_d {
        margin-top: 30px;
        width: 181px;
        text-align: center;
        line-height: 44px;
        color: #fff;
        background: linear-gradient(90deg, #26D0F1 0%, #409EFF 100%);
        border-radius: 22px;
        cursor: pointer;
      }
    }
  }
  .text {
    position: relative;
    margin: 0 auto;
    padding: 100px 0;
    h3 {
      font-size: 32px;
      letter-spacing: 4px;
      font-weight: 500;
      color: #ffffff;
    }
    h5 {
      font-size: 16px;
      letter-spacing: 2px;
      font-weight: bold;
      color: #ffffff;
      margin: 35px 0 93px;
    }
    button {
      width: 160px;
      border-radius: 19px;
      height: 38px;
      background-color: #ffffff;
      color: #409eff;
      position: absolute;
      top: 45%;
      left: 0;
    }
  }
}
.new_div_map {
  width: 100vw;
  min-height: 745px;
  max-height: 745px;
  display: flex;
  justify-content: center;
  position: relative;
  
  .carousels_div {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    display: flex;
    justify-content: center;
    
    .big_bg {
      width: 50%;
      height: 100%;
      display: flex;
      // justify-content: space-between;
      .star1_new {
        flex: 1;
        display: flex;
        align-items: center;
        .img_d {
          width: 90%;
          margin-top: 40%;
          margin-left: -40%;
          position: relative;
          .img_1 {
            width: 100%;
          }
          &:hover .tips_cards_1 {
            display: block;
            width: 243px;
            height: 277px;
            z-index: 999;
            .line_img {
              margin-left: -93px;
            }
          }
        }
        &:hover .img_1 {
          transform: scale(1.1) rotate(10deg);
        }
      }
      .sun_new {
        .img_d {
          width: 120%;
          margin-top: 48%;
          margin-left: -10%;
          position: relative;
          .img_1 {
            width: 100%;
          }
        }

        &:hover .img_1 {
          transform: scale(1.1) rotate(10deg);
        }
      }
      .star4_new {
        .img_d {
          width: 100%;
          margin-top: -42%;
          position: relative;
          .img_1 {
            width: 100%;
          }
          &:hover .tips_cards_2 {
            display: block;
            width: 243px;

            z-index: 999;
            .line_img {
              margin-left: -92px;
              margin-top: 226px;
            }
          }
        }

        &:hover .img_1 {
          transform: scale(1.1) rotate(10deg);
        }
      }
      .star3_new {
        .img_d {
          width: 100%;
          margin-top: -10%;
          margin-left: 40%;
          position: relative;
          .img_1 {
            width: 100%;
          }
          &:hover .tips_cards_4 {
            display: block;
            width: 243px;
            z-index: 999;
            .line_img {
              margin-left: 243px;
            }
          }
        }
        &:hover .img_1 {
          transform: scale(1.1) rotate(10deg);
        }
      }
      .star2_new {
        .img_d {
          width: 50%;
          margin-top: 45%;
          position: relative;
          .img_1 {
            width: 100%;
          }
          &:hover .tips_cards_3 {
            display: block;
            width: 243px;
            z-index: 999;
            .line_img {
              margin-left: 243px;
            }
          }
        }
        &:hover .img_1 {
          transform: scale(1.1) rotate(10deg);
        }
      }
      // .hoverimg_1{

      //   border: 1px solid red;
      // }
    }

    .pos_word_new {
      color: #fff;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      > span {
        font-size: 16px;
        padding: 4px 5px;
        display: inline-block;
        text-align: center;
      }
      .sun_span {
        background: url(../../../assets/images/new_map_more.png) 100% center
          no-repeat;
      }
      .star4_span {
        background: url(../../../assets/images/new_map_more_2.png) 100% center
          no-repeat;
      }
      .star2_span {
        background: url(../../../assets/images/new_map_more_1.png) 100% center
          no-repeat;
      }
      .star1_span {
        background: url(../../../assets/images/new_map_more_1.png) 100% center
          no-repeat;
      }
      .star3_span {
        background: url(../../../assets/images/new_map_more_3.png) 100% center
          no-repeat;
      }
      .star5_span {
        background: url(../../../assets/images/new_map_more_4.png) 100% center
          no-repeat;
      }
      .cards {
        .word_cards_bg {
          top: 0;
          position: absolute;
          width: 100%;
          height: 100%;
          background: #000;
          opacity: 0.5;
        }
        .word_text {
          top: 0;
          position: absolute;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }
      }
      .tips_cards_1 {
        display: none;
        position: absolute;
        top: 20%;
        left: 95%;
      }
      .tips_cards_2 {
        display: none;
        position: absolute;
        bottom: 20%;
        left: 90%;
        height: 277px;
      }
      .tips_cards_3 {
        display: none;
        position: absolute;
        top: 8%;
        right: 120%;
        height: 277px;
      }
      .tips_cards_4 {
        display: none;
        position: absolute;
        top: 20%;
        right: 90%;
        height: 277px;
      }
    }
  }
}
.menus_map {
  position: absolute;
  bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
  padding: 0 8px;
  width: 80px;
  height: 30px;
  background: #485a5d;
  border-radius: 15px;
  li {
    cursor: pointer;
  }
  .point_1 {
    width: 12px;
    height: 12px;
    background: #000000;
    opacity: 0.3;
    border-radius: 100%;
  }
  .point_white{
    background: #fff;
    opacity: 1;
  }
}
.new_div_map{
  animation-name: example;
  animation-duration:0.5s;
}
.carousels_div{
  animation-name: example2;
  animation-duration:1s;
}
@keyframes example {
  0% {
    transform: scale(0, 0);
  }
  100% {
    transform: scale(1, 1) ;
  }
}
@keyframes example2{
  0% {
    transform: scale(0, 0) rotate(0deg);
  }
  100% {
    transform: scale(1, 1) rotate(360deg);
  }
}
// .recommend-check {
//   display: flex;
//   justify-content: space-between;
//   flex-direction: row;
//   flex-wrap: wrap;
//   transition: all 0.5s ease 0s;
//   li {
//     width: @width;
//     padding-bottom: 30px;
//     background: rgba(255, 255, 255, 1);
//     border-radius: 10px;
//     margin-bottom: 30px;
//     .img1 {
//       margin: 56px auto;
//       display: block;
//     }
//     .img2 {
//       margin: 0 auto 63px auto;
//       display: block;
//       width: 100%;
//     }
//     .p1 {
//       font-size: 20px;
//       font-weight: bold;
//       color: @text-color;
//       padding: 0 40px;
//       margin-bottom: 12px;
//     }
//     .p2 {
//       font-size: 14px;
//       font-weight: 400;
//       color: @text-color;
//       padding: 0 40px;
//     }
//     .bottom {
//       display: flex;
//       justify-content: space-between;
//       padding: 22px 30px 0 40px;
//       .btn {
//         width: 90px;
//         height: 30px;
//         line-height: 30px;
//         background: linear-gradient(
//           90deg,
//           rgba(38, 208, 241, 1) 0%,
//           rgba(64, 158, 255, 1) 100%
//         );
//         cursor: pointer;
//         box-shadow: 0px 4px 10px 0px rgba(55, 175, 250, 0.5);
//         border-radius: 19px;
//         text-align: center;
//         font-size: 14px;
//         font-weight: 500;
//         color: #fff;
//       }
//     }
//     &:hover {
//       transform: translateY(-10px);
//     }
//   }
// }
// @media (min-width: 1024px) {
//   @width: 475px;
//   .recommend-check li {
//     width: @width;
//   }
// }

// @media (min-width: 1280px) {
//   @width: 485px;
//   .recommend-check li {
//     width: @width;
//   }
// }

// @media (min-width: 1440px) {
//   @width: 585px;
//   .recommend-check li {
//     width: @width;
//   }
// }

// .content-btm {
//   text-align: center;
//   margin-top: 22px;
//   p {
//     font-size: 30px;
//     line-height: 42px;
//   }
//   img {
//     cursor: pointer;
//     margin: 27px 0 25px;
//     &:hover {
//       animation: shake 0.5s linear;
//     }
//   }
//   button {
//     background: transparent;
//   }
//   @keyframes shake {
//     0% {
//       transform: rotate(0deg);
//     }
//     25% {
//       transform: scale(1.5) rotate(30deg);
//     }
//     50% {
//       transform: scale(1.5) rotate(-30deg);
//     }
//     75% {
//       transform: scale(1.5) rotate(30deg);
//     }
//     100% {
//       transform: rotate(0deg);
//     }
//   }
// }
