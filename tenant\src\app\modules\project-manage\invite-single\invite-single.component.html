<div class="content client-width ">
    <div class="xy_tab">
        <ul class="xy_tab_ul">
            <li class="tab_ul_li " [ngClass]="changeCode?'xy_ch_li':''" (click)="selectCode('public')">公共码</li>
            <li class="tab_ul_li" [ngClass]="changeCode?'':'xy_ch_li'" (click)="selectCode('myself')">一人一码</li>
        </ul>
    </div>
    <form nz-form [formGroup]="validateForm" style="display: flex;">
        <div class="right">
            <div class="title"><span>邀请码({{changeName}})</span></div>
            <!-- <div class="tip"><a href="javascript:void(0)" (click)="createQrcode()">生成二维码</a></div> -->
            <div>
                <qrcode *ngIf="model.qrCodeContent" [margin]="0" [qrdata]="model.qrCodeContent" [width]="160"
                    [errorCorrectionLevel]="'M'" [elementType]="'img'"></qrcode>
                <div style="margin-top: 10px;">
                    <button nz-button nzType="primary" nzGhost nzShape="round" class="download-btn"
                        (click)="downloadImg()">下载到本地</button>
                </div>


            </div>

        </div>
        <div class="left">
            <div>
                <div class="title"><span>邀请链接({{changeName}})</span></div>
                <input class="text copytext" nz-input formControlName="email" placeholder="链接地址"
                    [(ngModel)]="model.qrCodeContent" />

                <button nz-button nzType="primary" nzGhost nzShape="round" class="copy-btn"
                    (click)="doCopy()">复制</button>
            </div>
            <div class="xy_Refresh">
                <span *ngIf="!changeCode" >
                    <div style="margin-top: 10px;">
                        <a nz-button (click)="createQrcode('PRIVATE')" nzType="link"><img
                                src="./assets/images/qr_refresh.png">
                            <span style="margin-left: 5px;">刷新</span></a>
                    </div>
                </span>
            </div>

        </div>

    </form>


</div>

<!-- <span>{{model | json}}</span>        -->