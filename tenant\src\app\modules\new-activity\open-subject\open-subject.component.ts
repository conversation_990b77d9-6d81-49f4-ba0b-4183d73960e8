import { Component, OnInit } from "@angular/core";
import { NzMessageService, NzModalRef } from "ng-zorro-antd";
import { MessageService } from "@src/shared/custom-message/message-service.service";

@Component({
  selector: "app-open-subject",
  templateUrl: "./open-subject.component.html",
  styleUrls: ["./open-subject.component.less"],
})
export class OpenSubjectComponent implements OnInit {
  name: any = {
    zh_CN: "",
    en_US: "",
  };

  constructor(
    private modalRef: NzModalRef,
    private msg: NzMessageService,
    private customMsg: MessageService
  ) {}

  ngOnInit() {}

  ok() {
    if (!this.name.zh_CN) {
      // this.msg.error("中文不能为空");
      this.customMsg.open("error", "中文不能为空");
      return false;
    }
    if (!this.name.en_US) {
      // this.msg.error("英文不能为空");
      this.customMsg.open("error", "英文不能为空");
      return false;
    }
    this.modalRef.triggerOk();
  }
}
