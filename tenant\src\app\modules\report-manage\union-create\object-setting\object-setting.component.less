.objectSetting{
  width: 710px;
  height: 480px;
  .header{
    height: 55px;
    width: 100%;
    box-shadow: 0px 3px 15px -2px #EBEBEB; 
    display: flex;
    justify-content: space-between;
    align-items: center;
    h2{
      font-size: 18px;
      font-weight: 500;
      color: #17314C;
      padding-left: 24px;
    }
    >div{
      display: flex;
      align-items: center;
      margin: 0 24px;
      img{
        width: 16px;
        height: 16px;
        margin-right: 6px;
        margin-bottom: 4px;
      }
    }
  }
  .content{
    width: 100%;
    height: 365px;
    overflow-y: auto;
    &_top{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 24px 0 24px;
      >div{
        width: 310px;
      }
      >span{
        font-size: 14px;
        font-weight: 400;
        color: #495970;
      }
    }
    .table{
      margin: 10px 24px 16px 24px;
      .end{
        color: #52c41a;
        margin-right: 10px;
      }
      .progress{
        color: #faad14;
        margin-right: 10px;
        
      }
    }
    &_btm{
      display: flex;
      justify-content: flex-end;
      margin: 0 24px;
    }
  }
  .footer{
    height: 60px;
    width: 100%;
    box-shadow: 0px -3px 15px -2px #EBEBEB; 
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    .warnText{
      font-size: 12px;
      font-weight: 400;
      color: #F84444;
    }
    .btns{
      span{
        padding: 5px 20px;
        border-radius: 15px;
        &:nth-child(1){
          color: #409EFF;
          background: #FFFFFF;
          border: 1px solid #409EFF;
        }
        &:nth-child(2){
          background: #409EFF;
          color: #FFFFFF;
          border: 1px solid #409EFF;
          margin-left: 15px;
        }
        cursor: pointer;
      }
    }
  }
}
:host ::ng-deep {
  .ant-table-small {
      border: 1px solid #E6E6E6;
  }
  .ant-table-body{
    margin: 0 !important;
  }
  .ant-btn-link{
    color: #17314C;
  }
  .ant-table-thead{
    background-color: #F6F6F6;
    th{
      span{
        font-weight: 500;
        color: #495970;
      }
    }
  }
  .objectSetting{
    .ant-upload-list{
      width: 300px;
      position: absolute;
      top: 17px;
      right: 182px;
    }
    .ant-upload-list-item{
      margin-top: 0;
      width: 300px;
      overflow: hidden;  //超出隐藏
      white-space: nowrap; //不折行
      text-overflow: ellipsis; //溢出显示省略号
    }
  }
  }